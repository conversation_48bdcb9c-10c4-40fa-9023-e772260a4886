{"words": ["altcoin", "annualization", "Annualize", "<PERSON><PERSON><PERSON>", "AUTOINCREMENT", "backtest", "Backtest", "Backtesting", "bavail", "bollinger", "BTCUSD", "<PERSON><PERSON><PERSON>", "bybit", "BYBIT", "cagr", "calmar", "Calmar", "ccxt", "chunkerror", "cooldown", "Cooldown", "creds", "<PERSON><PERSON><PERSON>", "CRYPTOCOMPARE", "defi", "<PERSON><PERSON><PERSON><PERSON>", "drawdowns", "editorconfigjetbrains", "gateio", "GUSD", "<PERSON><PERSON><PERSON><PERSON>", "hodl", "HODL", "impor", "indexeddb", "isnt", "lambo", "lookback", "macd", "mmap", "NEWSAPI", "ohlcv", "OHLCV", "<PERSON><PERSON>", "pionex", "Pionex", "ponzi", "pplx", "<PERSON><PERSON><PERSON>", "rekt", "satoshi", "<PERSON><PERSON>", "Shi<PERSON>", "solana", "sortino", "<PERSON><PERSON><PERSON>", "stablecoins", "statfs", "Stdev", "Technicals", "treynor", "<PERSON><PERSON>", "USDP", "uuidv", "<PERSON><PERSON>", "xtype", "retryable", "omise", "dexscreener", "NEWCOIN"], "ignoreRegExpList": ["^[A-Z0-9_]+$"], "ignorePaths": ["**/__tests__/**", "app/coverage/**", "app/trading/logs/**", "**/*.json", "**/*.md"]}
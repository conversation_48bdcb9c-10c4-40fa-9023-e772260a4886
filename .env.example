# Trading System Environment Configuration
# Copy this file to .env and configure with your actual values

# Environment
NODE_ENV=production
PORT=3000

# Risk Management
DEFAULT_RISK_PERCENT=2
MAX_OPEN_POSITIONS=5
MAX_DAILY_LOSS=100
STOP_LOSS_PERCENT=5
TAKE_PROFIT_PERCENT=10

# Trading Features
ENABLE_WHALE_TRACKING=true
ENABLE_MEME_COIN_SCANNING=true
ENABLE_GRID_TRADING=true
ENABLE_ARBITRAGE=false

# Exchange API Keys (replace with your actual keys)
OKX_API_KEY=your_okx_api_key
OKX_SECRET_KEY=your_okx_secret_key
OKX_PASSPHRASE=your_okx_passphrase

BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key

COINBASE_API_KEY=your_coinbase_api_key
COINBASE_SECRET_KEY=your_coinbase_secret_key

# Database
DATABASE_URL=./app/trading/data/databases/trading.db
LOG_LEVEL=info

# Monitoring
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your_webhook_url
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# Security
JWT_SECRET=your_jwt_secret_key_here
ENCRYPTION_KEY=your_256_bit_encryption_key
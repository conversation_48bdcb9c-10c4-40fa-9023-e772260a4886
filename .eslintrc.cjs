module.exports = {
    env: {
        browser: true,
        es2021: true,
        node: true,
        commonjs: true
    },
    extends: [
        'eslint:recommended'
    ],
    parserOptions: {
        ecmaVersion: 2022,
        sourceType: 'module',
    },
    rules: {
        'no-trailing-spaces': 'error',
        'no-unused-vars': ['warn', {
            'argsIgnorePattern': '^_',
            'varsIgnorePattern': '^_'
        }],
        'no-empty': ['error', {'allowEmptyCatch': false}],
        'prefer-const': 'warn',
        'no-console': 'off'
    },
    globals: {
        Buffer: 'readonly',
        process: 'readonly',
        global: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        module: 'readonly',
        require: 'readonly',
        exports: 'readonly'
    },
    overrides: [
        {
            // CommonJS files
            files: ['**/*.js'],
            excludedFiles: ['**/ccxt/**/*.js'],
            parserOptions: {
                sourceType: 'script'
            }
        },
        {
            // ES6 module files (specifically CCXT directory)
            files: ['**/ccxt/**/*.js'],
            parserOptions: {
                sourceType: 'module'
            },
            rules: {
                'no-unused-vars': ['warn', {
                    'argsIgnorePattern': '^_',
                    'varsIgnorePattern': '^_'
                }]
            }
        }
    ]
};

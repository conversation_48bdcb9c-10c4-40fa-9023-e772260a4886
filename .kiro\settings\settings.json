{"editor.codeActionsOnSave": {"source.organizeImports": true, "source.fixAll": true}, "editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.rulers": [80, 120], "editor.wordWrap": "on", "editor.wordWrapColumn": 120, "editor.minimap.enabled": true, "editor.minimap.maxColumn": 120, "editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "editor.trimAutoWhitespace": true, "files.encoding": "utf8", "files.eol": "\n", "files.trimTrailingWhitespace": true, "files.trimFinalNewlines": true, "files.insertFinalNewline": true, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.git": true, "**/.kiro/logs": true, "**/coverage": true, "**/.nyc_output": true}, "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/**": true, "**/dist/**": true, "**/build/**": true, "**/.kiro/logs/**": true, "**/coverage/**": true, "**/.nyc_output/**": true}, "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.workspaceSymbols.scope": "currentProject", "typescript.updateImportsOnFileMove.enabled": "always", "javascript.updateImportsOnFileMove.enabled": "always", "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "eslint.workingDirectories": [".", "app", "app/trading"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "prettier.singleQuote": true, "prettier.trailingComma": "es5", "prettier.tabWidth": 2, "prettier.useTabs": false, "prettier.printWidth": 80, "jest.autoRun": "off", "jest.pathToJest": "npm run test --", "jest.shell": "/bin/bash", "git.enableSmartCommit": true, "git.confirmSync": false, "git.autofetch": true, "git.enableCommitSigning": false, "git.postCommitCommand": "push", "gitlens.currentLine.enabled": true, "gitlens.hovers.enabled": true, "gitlens.blame.enabled": true, "markdown.preview.refreshMarkdown": true, "markdown.preview.doubleClickToSwitchToEditor": false, "markdown.extension.toc.levels": "1..6", "markdown.extension.toc.updateOnSave": true, "markdown.extension.preview.autoShowPreviewToSide": true, "cSpell.words": ["memecoin", "trading", "orchestrator", "arbitrage", "whaletracker", "ccxt", "pino", "autonomous", "backtesting", "sentiment", "portfolio", "riskmanagement", "performance", "monitoring", "configuration", "initialization"], "cSpell.ignorePaths": ["**/node_modules/**", "**/dist/**", "**/build/**", "**/coverage/**", "**/.git/**", "**/.kiro/logs/**"], "files.associations": {"*.md": "markdown", "*.json": "jsonc"}, "workbench.editorAssociations": {"*.md": "vscode.markdown.preview.editor"}, "terminal.integrated.shell.windows": "C:\\Program Files\\Git\\bin\\bash.exe", "terminal.integrated.cwd": "${workspaceFolder}", "debug.console.fontSize": 12, "debug.console.fontFamily": "Consolas, 'Courier New', monospace", "debug.toolBarLocation": "docked", "launch": {"configurations": [{"name": "Launch Electron Main", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron", "windows": {"runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron.cmd"}, "args": ["app/main.js"], "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "sourceMaps": true, "outFiles": ["${workspaceFolder}/app/dist/**/*.js"]}, {"name": "Launch React Dev", "type": "chrome", "request": "launch", "url": "http://localhost:7291", "webRoot": "${workspaceFolder}/app/src"}]}, "todo-tree.general.tags": ["TODO", "FIXME", "BUG", "HACK", "NOTE", "REVIEW", "REQUIREMENT", "DESIGN", "STEERING"], "todo-tree.regex.regex": "((//|#|<!--|;|/\\*|^)\\s*($TAGS)|^\\s*- \\[ \\])", "todo-tree.general.statusBar": "total", "todo-tree.tree.showScanModeButton": true, "todo-tree.filtering.excludeGlobs": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.git": true, "**/.kiro/logs": true, "**/coverage": true}, "extensions.ignoreRecommendations": false, "extensions.autoUpdate": true, "extensions.autoCheckUpdates": true, "workbench.colorTheme": "Dark+ (default dark)", "workbench.iconTheme": "vscode-icons", "workbench.startupEditor": "none", "workbench.editor.enablePreview": false, "workbench.editor.enablePreviewFromQuickOpen": false, "workbench.editor.showTabs": true, "workbench.editor.limit.enabled": true, "workbench.editor.limit.value": 10, "workbench.sideBar.location": "left", "workbench.panel.defaultLocation": "bottom", "workbench.statusBar.visible": true, "workbench.activityBar.visible": true, "window.menuBarVisibility": "default", "window.restoreWindows": "all", "window.zoomLevel": 0, "window.title": "${dirty}${activeEditorShort}${separator}${rootName}${separator}${appName}", "window.titleSeparator": " - ", "window.newWindowDimensions": "inherit"}
# VS Code Extension Usage Guidelines - Meme Coin Trader Development

## Overview
This guide provides comprehensive instructions for using VS Code extensions to actively leverage the `.kiro` directory during development, enhancing context awareness and project understanding through automated integration of specifications, configurations, and project steering.

## Project Context Files Structure

### Directory Layout
```
.kiro/
├── settings/
│   ├── mcp.json              # MCP server configurations
│   └── settings.json         # VS Code workspace settings
├── specs/
│   ├── application-integration/
│   │   ├── design.md         # Technical design documentation
│   │   ├── requirements.md   # Functional requirements
│   │   └── tasks.md          # Implementation status and tasks
├── steering/
│   ├── product.md            # Product vision and features
│   ├── structure.md        # Architecture decisions
│   └── tech.md             # Technology stack and tools
```

## Extension Configuration Recommendations

### Essential Extensions

#### 1. **Markdown All in One**
```json
{
  "markdown.extension.toc.levels": "1..6",
  "markdown.extension.toc.updateOnSave": true,
  "markdown.extension.preview.autoShowPreviewToSide": true
}
```

#### 2. **Project Manager**
```json
{
  "projectManager.git.baseFolders": ["~/projects"],
  "projectManager.vscode.ignoredFolders": ["node_modules", ".git"]
}
```

#### 3. **Code Spell Checker**
```json
{
  "cSpell.words": ["memecoin", "trading", "orchestrator", "arbitrage", "whaletracker"]
}
```

#### 4. **GitLens**
```json
{
  "gitlens.currentLine.enabled": true,
  "gitlens.hovers.enabled": true,
  "gitlens.blame.enabled": true
}
```

### Advanced Extensions for Context Integration

#### 1. **Project Dashboard**
Provides quick access to project documentation and status.

## MCP Configuration Usage Patterns

### Development MCP Servers
The `.kiro/settings/mcp.json` file contains configurations for Model Context Protocol servers that can be used to enhance development:

```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp"],
      "env": {
        "NODE_ENV": "development"
      }
    },
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "env": {
        "NODE_ENV": "development"
      }
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"],
      "env": {
        "NODE_ENV": "development"
      }
    }
  }
}
```

### Usage Examples

#### Context7 Integration
```bash
# Query trading library documentation
use_mcp_tool: context7, get-library-docs, {"context7CompatibleLibraryID": "/ccxt/ccxt", "topic": "exchange-trading"}

# Get Next.js documentation for dashboard components
use_mcp_tool: context7, get-library-docs, {"context7CompatibleLibraryID": "/vercel/next.js", "topic": "react-hooks"}
```

#### Memory Server for Project Context
```bash
# Create project entities
use_mcp_tool: memory, create_entities, {"entities": [{"name": "TradingOrchestrator", "entityType": "class", "observations": ["Main trading system coordinator", "Manages all trading components", "Implements IPC handlers"]}]}

# Query project knowledge
use_mcp_tool: memory, search_nodes, {"query": "trading system components"}
```

## Specs Integration Workflows

### Real-time Requirements Validation

#### 1. **Requirements Integration**
Create a custom task runner that validates code against `.kiro/specs/application-integration/requirements.md`:

```json
{
  "label": "Validate Requirements",
  "type": "shell",
  "command": "node scripts/validate-requirements.js",
  "group": "build",
  "presentation": {
    "reveal": "always",
    "panel": "shared"
  }
}
```

#### 2. **Design Document Sync**
Use the design document as a living reference:

```javascript
// In your component files, reference design patterns
// See .kiro/specs/application-integration/design.md#L325-335
// For TradingOrchestrator integration
```

### Task Status Integration

#### Automated Task Tracking
Create a VS Code task that reads `.kiro/specs/application-integration/tasks.md` and displays current status:

```json
{
  "label": "Show Project Status",
  "type": "shell",
  "command": "node scripts/show-status.js",
  "group": "build",
  "presentation": {
    "reveal": "always",
    "panel": "shared"
  }
}
```

## Steering Files Integration

### Product Context Awareness

#### 1. **Product Feature Mapping**
Use `.kiro/steering/product.md` for feature validation:

```json
{
  "editor.codeActionsOnSave": {
    "source.organizeImports": true,
    "source.fixAll": true
  },
  "typescript.preferences.includePackageJsonAutoImports": "on"
}
```

#### 2. **Technology Stack Validation**
Reference `.kiro/steering/tech.md` for technology decisions:

```javascript
// In package.json
// Tech stack validation based on .kiro/steering/tech.md
{
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=8.0.0"
  }
}
```

## Practical Development Workflows

### Daily Development Routine

#### 1. **Morning Setup**
```bash
# 1. Check project status
code .kiro/specs/application-integration/tasks.md

# 2. Review requirements for current feature
code .kiro/specs/application-integration/requirements.md

# 3. Check technology constraints
code .kiro/steering/tech.md
```

#### 2. **Feature Development**
```bash
# 1. Reference design patterns
code .kiro/specs/application-integration/design.md

# 2. Validate against requirements
npm run validate-requirements

# 3. Update task status
npm run update-task-status
```

#### 3. **Code Review**
```bash
# 1. Check against product vision
code .kiro/steering/product.md

# 2. Verify technology compliance
npm run tech-check
```

### Custom Commands and Tasks

#### 1. **Project Context Command**
Create a custom command palette action:

```json
{
  "command": "memeTrader.showContext",
  "title": "Meme Trader: Show Project Context"
}
```

#### 2. **Requirements Validator**
```json
{
  "label": "Validate Current Feature Against Requirements",
  "type": "shell",
  "command": "node scripts/validate-feature.js ${file}",
  "problemMatcher": []
}
```

#### 3. **Design Document Linker**
Create automatic linking between code and design documents:

```json
{
  "settings": {
    "editor.hover.enabled": true,
    "editor.hover.delay": 300,
    "typescript.suggest.autoImports": true
  }
}
```

## Workspace Configuration

### Recommended Settings
```json
{
  "files.associations": {
    "*.md": "markdown"
  },
  "markdown.preview.doubleClickToSwitchToEditor": false,
  "workbench.editorAssociations": {
    "*.md": "vscode.markdown.preview.editor"
  },
  "files.watcherExclude": {
    "**/.kiro/logs/**": true,
    "**/node_modules/**": true,
    "**/dist/**": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.git": true,
    "**/.kiro/logs": true
  }
}
```

### Extension-Specific Settings

#### 1. **Todo Tree Integration**
```json
{
  "todo-tree.general.tags": [
    "TODO",
    "FIXME",
    "BUG",
    "HACK",
    "NOTE",
    "REVIEW"
  ],
  "todo-tree.regex.regex": "((//|#|<!--|;|/\\*|^)\\s*($TAGS)|^\\s*- \\[ \\])",
  "todo-tree.general.statusBar": "total"
}
```

#### 2. **Git Graph Configuration**
```json
{
  "git-graph.referenceLabels.alignment": "Normal",
  "git-graph.showCommitsOnlyReferencedByTags": false,
  "git-graph.showRemoteBranches": true
}
```

## Development Scripts

### Context-Aware Scripts

#### 1. **Requirements Validator**
```bash
#!/bin/bash
# scripts/validate-requirements.js
const fs = require('fs');
const path = require('path');

const requirementsPath = path.join(__dirname, '..', '.kiro', 'specs', 'application-integration', 'requirements.md');
const tasksPath = path.join(__dirname, '..', '.kiro', 'specs', 'application-integration', 'tasks.md');

// Parse requirements and validate current implementation
function validateRequirements() {
  const requirements = fs.readFileSync(requirementsPath, 'utf8');
  const tasks = fs.readFileSync(tasksPath, 'utf8');
  
  // Implementation validation logic
  console.log('Validating requirements...');
  console.log('Current status:', tasks.includes('[x]') ? 'Complete' : 'In Progress');
}

validateRequirements();
```

#### 2. **Design Document Linker**
```bash
#!/bin/bash
# scripts/link-design.js
# Creates symbolic links between code and design documents
```

#### 3. **Task Status Watcher**
```bash
#!/bin/bash
# scripts/watch-tasks.js
# Watches task file changes and updates VS Code status bar
```

## Advanced Features

### 1. **Context-Aware IntelliSense**
Configure TypeScript to use project-specific type definitions:

```json
{
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.suggest.autoImports": true,
  "typescript.workspaceSymbols.scope": "currentProject"
}
```

### 2. **Real-time Documentation**
Enable live documentation updates:

```json
{
  "markdown.preview.refreshMarkdown": true,
  "markdown.preview.doubleClickToSwitchToEditor": false
}
```

### 3. **Project Templates**
Create file templates based on specifications:

```json
{
  "files.template": {
    "component": ".kiro/templates/component-template.jsx",
    "service": ".kiro/templates/service-template.js"
  }
}
```

## Best Practices

### 1. **Always Check Context**
Before implementing features:
1. Review `.kiro/specs/application-integration/requirements.md`
2. Check `.kiro/specs/application-integration/design.md` for implementation patterns
3. Validate against `.kiro/steering/tech.md` for technology constraints

### 2. **Keep Tasks Updated**
Update task completion in `.kiro/specs/application-integration/tasks.md`:
- Mark completed tasks with `[x]`
- Add new discovered tasks
- Update progress status

### 3. **Use MCP for Documentation**
Leverage MCP servers for:
- Real-time library documentation
- Project context awareness
- Code examples from specifications

### 4. **Automated Validation**
Run validation scripts before commits:
- Requirements compliance check
- Technology stack validation
- Design pattern compliance

## Troubleshooting

### Extension Conflicts
If extensions conflict:
1. Check `.kiro/settings/settings.json` for workspace-specific settings
2. Use extension bisect to identify conflicts
3. Use workspace-specific extensions when possible

### Performance Issues
For large projects:
1. Use `.vscode/settings.json` to exclude build directories
2. Enable file watching exclusions
3. Use workspace-specific search exclusions

### Context Not Updating
If context isn't reflecting changes:
1. Reload window: `Ctrl+Shift+P` → `Reload Window`
2. Clear extension cache
3. Restart extension host

## Summary

This guide enables full integration of the `.kiro` directory into your development workflow, providing:
- Real-time project context awareness
- Automated requirements validation
- Technology stack compliance checking
- Design pattern enforcement
- Task progress tracking

Use these configurations and workflows to maintain high development velocity while ensuring compliance with project specifications and requirements.
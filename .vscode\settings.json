{
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.suggest.autoImports": true,
  "typescript.suggest.includeCompletionsForModuleExports": true,
  "typescript.workspaceSymbols.scope": "allOpenProjects",
  "typescript.validate.enable": true,
  "javascript.validate.enable": true,
  "typescript.suggest.enabled": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.preferences.importModuleSpecifier": "relative",
  // Enable Error Lens (inline error indicators)
  "editor.inlineSuggest.enabled": true,
  "editor.errorLens.enabled": true,
  "editor.errorLens.fontSize": 0.85,
  "editor.errorLens.fontFamily": "default",
  "editor.errorLens.fontWeight": "normal",
  "editor.errorLens.delay": 100,
  "editor.errorLens.followCursorOnly": false,
  // Enable inline diagnostics with red underline
  "editor.renderValidationDecorations": "editable",
  "editor.showUnused": true,
  "editor.showDeprecated": true,
  "editor.guides.bracketPairs": "active",
  "editor.guides.indentation": true,
  // Increase problem limit
  "problems.maxVisible": 10000,
  "problems.showCurrentInStatus": true,
  "problems.decorations.enabled": true,
  "typescript.suggest.completeFunctionCalls": true,
  "typescript.suggest.includeAutomaticOptionalChainCompletions": true,
  // ESLint configuration
  "eslint.workingDirectories": [
    ".",
    "app",
    "app/trading"
  ],
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "eslint.run": "onType",
  "eslint.problems.shortenToSingleLine": false,
  "eslint.format.enable": true,
  "eslint.codeAction.disableRuleComment.enable": true,
  "eslint.codeAction.showDocumentation.enable": true,
  "eslint.alwaysShowStatus": true,
  // Language server settings
  "typescript.tsserver.log": "off",
  "typescript.tsserver.trace": "off",
  "javascript.suggest.enabled": true,
  "editor.codeAction.triggerOnSave": false,
  "editor.formatOnSave": false,
  "editor.formatOnPaste": false,
  // File watcher settings
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/*/**": true,
    "**/.hg/store/**": true
  },
  // Search settings
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true,
    "**/dist": true,
    "**/build": true,
    "**/.git": true,
    "**/.DS_Store": true
  },
  // IntelliSense settings
  "editor.quickSuggestions": {
    "other": true,
    "comments": true,
    "strings": true
  },
  "editor.suggestOnTriggerCharacters": true,
  "editor.acceptSuggestionOnEnter": "on",
  "editor.quickSuggestionsDelay": 10,
  "editor.suggestSelection": "recentlyUsed",
  "editor.wordBasedSuggestions": "matchingDocuments",
  "editor.suggest.snippetsPreventQuickSuggestions": false,
  // Enable semantic highlighting
  "editor.semanticHighlighting.enabled": true,
  "editor.semanticTokenColorCustomizations": {
    "enabled": true
  },
  // Enable bracket matching and error indicators
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.highlightActiveIndentation": true,
  // Enable error indicators in the editor
  "workbench.colorCustomizations": {
    "editorError.foreground": "#ff0000",
    "editorWarning.foreground": "#ffcc00",
    "editorInfo.foreground": "#007acc"
  },
  // Workspace settings
  "workspace.symbolProvider": "typescript",
  "typescript.workspaceSymbols.maxSymbols": 10000,
  // Include all JavaScript/TypeScript files
  "typescript.include.packageJson": true,
  "python-envs.pythonProjects": [
    {
      "path": "",
      "envManager": "ms-python.python:venv",
      "packageManager": "ms-python.python:pip"
    }
  ],
  "sonarlint.connectedMode.project": {
    "connectionId": "xphoenix1996",
    "projectKey": "xphoenix1996_electron-app"
  }
}
{"version": "2.0.0", "tasks": [{"label": "Show Project Status", "type": "shell", "command": "node", "args": ["scripts/vscode-integration.js", "status"], "group": "build", "presentation": {"reveal": "always", "panel": "shared", "clear": true, "showReuseMessage": false}, "problemMatcher": []}, {"label": "Validate Requirements", "type": "shell", "command": "node", "args": ["scripts/vscode-integration.js", "validate"], "group": "build", "presentation": {"reveal": "always", "panel": "shared", "clear": true, "showReuseMessage": false}, "problemMatcher": []}, {"label": "Setup Context Links", "type": "shell", "command": "node", "args": ["scripts/vscode-integration.js", "links"], "group": "build", "presentation": {"reveal": "always", "panel": "shared", "clear": true, "showReuseMessage": false}, "problemMatcher": []}, {"label": "Design Document", "type": "shell", "command": "node", "args": ["scripts/vscode-integration.js", "design"], "group": "build", "presentation": {"reveal": "always", "panel": "shared", "clear": true, "showReuseMessage": false}, "problemMatcher": []}, {"label": "Technology Stack", "type": "shell", "command": "node", "args": ["scripts/vscode-integration.js", "tech"], "group": "build", "presentation": {"reveal": "always", "panel": "shared", "clear": true, "showReuseMessage": false}, "problemMatcher": []}, {"label": "Watch .kiro Changes", "type": "shell", "command": "node", "args": ["scripts/vscode-integration.js", "watch"], "group": "build", "isBackground": true, "presentation": {"reveal": "always", "panel": "shared", "clear": true, "showReuseMessage": false}, "problemMatcher": []}, {"label": "Setup Workspace", "type": "shell", "command": "node", "args": ["scripts/vscode-integration.js", "setup"], "group": "build", "presentation": {"reveal": "always", "panel": "shared", "clear": true, "showReuseMessage": false}, "problemMatcher": []}, {"label": "Development: Start All", "type": "shell", "command": "npm", "args": ["run", "dev"], "group": "build", "presentation": {"reveal": "always", "panel": "shared"}, "isBackground": true, "problemMatcher": {"owner": "typescript", "fileLocation": "relative", "pattern": {"regexp": "^.*\\((\\d+),\\d+\\): (.*)$", "file": 1, "message": 2}}}, {"label": "Trading: Start Development", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/app/trading"}, "group": "build", "presentation": {"reveal": "always", "panel": "shared"}, "isBackground": true, "problemMatcher": {"owner": "typescript", "fileLocation": "relative", "pattern": {"regexp": "^.*\\((\\d+),\\d+\\): (.*)$", "file": 1, "message": 2}}}, {"label": "Trading: Autonomous Mode", "type": "shell", "command": "npm", "args": ["run", "autonomous"], "options": {"cwd": "${workspaceFolder}/app/trading"}, "group": "build", "presentation": {"reveal": "always", "panel": "shared"}, "isBackground": true, "problemMatcher": {"owner": "typescript", "fileLocation": "relative", "pattern": {"regexp": "^.*\\((\\d+),\\d+\\): (.*)$", "file": 1, "message": 2}}}, {"label": "Test: Unit Tests", "type": "shell", "command": "npm", "args": ["test"], "group": "test", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": {"owner": "typescript", "fileLocation": "relative", "pattern": {"regexp": "^.*\\((\\d+),\\d+\\): (.*)$", "file": 1, "message": 2}}}, {"label": "Test: Integration Tests", "type": "shell", "command": "npm", "args": ["run", "test:integration"], "group": "test", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": {"owner": "typescript", "fileLocation": "relative", "pattern": {"regexp": "^.*\\((\\d+),\\d+\\): (.*)$", "file": 1, "message": 2}}}, {"label": "Build: Production", "type": "shell", "command": "npm", "args": ["run", "build"], "group": "build", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": {"owner": "typescript", "fileLocation": "relative", "pattern": {"regexp": "^.*\\((\\d+),\\d+\\): (.*)$", "file": 1, "message": 2}}}, {"label": "Database: Initialize", "type": "shell", "command": "npm", "args": ["run", "init-db"], "options": {"cwd": "${workspaceFolder}/app/trading"}, "group": "build", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": []}, {"label": "Database: SQLite Setup", "type": "shell", "command": "npm", "args": ["run", "init-sqlite"], "options": {"cwd": "${workspaceFolder}/app/trading"}, "group": "build", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": []}, {"label": "Database: Schema Apply", "type": "shell", "command": "npm", "args": ["run", "apply-schema"], "options": {"cwd": "${workspaceFolder}/app/trading"}, "group": "build", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": []}, {"label": "Health: Check System", "type": "shell", "command": "npm", "args": ["run", "health-check"], "group": "build", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": []}, {"label": "Performance: <PERSON><PERSON><PERSON>", "type": "shell", "command": "npm", "args": ["run", "performance:analyze"], "group": "build", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": []}, {"label": "Lint: Fix All", "type": "shell", "command": "npm", "args": ["run", "lint-fix"], "group": "build", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": {"owner": "eslint", "fileLocation": "relative", "pattern": {"regexp": "^(.+):(\\d+):(\\d+):\\s+(warning|error):\\s+(.+)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}]}
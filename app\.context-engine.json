{"version": "1.0.0", "context": {"maxTokens": 8000, "includeTests": true, "includeDocumentation": true, "filePatterns": ["**/*.ts", "**/*.tsx", "**/*.d.ts", "**/*.js", "**/*.jsx", "**/*.mjs", "**/*.html", "**/*.css", "**/*.scss", "**/*.module.css"], "excludePatterns": ["node_modules/**", "dist/**", "build/**", "coverage/**", ".git/**", "*.log", "**/packages/*/dist/**", "**/apps/*/dist/**", "**/libs/*/dist/**", "**/__mocks__/**", "**/.webpack/**"], "priority": {"src/": 10, "lib/": 9, "components/": 10, "utils/": 7, "types/": 6, "test/": 3, "spec/": 3, "docs/": 2, "main/": 10, "renderer/": 9, "preload/": 8, "hooks/": 8, "pages/": 9, "trading\\config/": 10, "trading\\monitoring/": 9, "trading/": 8}}, "analysis": {"enableQualityChecks": true, "enableSecurityScans": true, "enablePerformanceAnalysis": true, "customRules": []}, "ai": {"provider": "claude", "contextTemplate": "comprehensive", "issueDetection": true, "suggestImprovements": true}, "project": {"name": "app", "type": "electron-app", "language": "typescript", "framework": "react"}, "lastUpdated": "2025-07-29T03:24:55.824Z"}
# Google Cloud Configuration (if needed)
GOOGLE_CLOUD_PROJECT=your-project-id-here

# Exchange API Keys (DO NOT COMMIT REAL KEYS)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_api_secret_here

BYBIT_API_KEY=your_bybit_api_key_here
BYBIT_API_SECRET=your_bybit_api_secret_here

# Database Configuration
DATABASE_PATH=./databases/trading_bot.db

# Trading Configuration
DEFAULT_RISK_PERCENT=2
MAX_OPEN_POSITIONS=10
ENABLE_WHALE_TRACKING=true
ENABLE_MEME_COIN_SCANNING=true

# Development Configuration
NODE_ENV=development
ELECTRON_IS_DEV=1
DEBUG=trading:*,whale:*,grid:*

# Logging
LOG_LEVEL=info
ENABLE_FILE_LOGGING=true

# API Configuration
API_TIMEOUT=30000
API_RETRY_ATTEMPTS=3

# LLM Configuration (if using AI features)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Note: Copy this file to .env and fill in your actual values
# NEVER commit the .env file with real credentials!
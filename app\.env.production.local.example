# Production Environment Configuration
# Copy this file to .env.production.local and fill in your actual values

# Build Configuration
GENERATE_SOURCEMAP=true
DISABLE_ESLINT_PLUGIN=true
BUILD_PATH=build

# Error Reporting
REACT_APP_ENABLE_ERROR_REPORTING=true
REACT_APP_ERROR_REPORTING_ENDPOINT=https://your-error-service.com/api/errors
REACT_APP_ERROR_REPORTING_API_KEY=your_api_key_here

# Logging Configuration
REACT_APP_LOG_LEVEL=warn
REACT_APP_ENABLE_CONSOLE_LOGS=false
REACT_APP_ENABLE_REMOTE_LOGGING=true
REACT_APP_MAX_LOG_STORAGE=500

# Performance Monitoring
REACT_APP_ENABLE_PERFORMANCE_MONITORING=true
REACT_APP_ENABLE_ANALYTICS=true

# Security
REACT_APP_SESSION_TIMEOUT=3600000
REACT_APP_MAX_LOGIN_ATTEMPTS=5

# Trading Configuration
REACT_APP_MAX_POSITIONS=10
REACT_APP_DEFAULT_RISK_PERCENT=2
REACT_APP_ENABLE_WHALE_TRACKING=true
REACT_APP_ENABLE_MEME_COIN_SCANNING=true
REACT_APP_TRADING_REFRESH_INTERVAL=5000

# API Configuration
REACT_APP_API_TIMEOUT=30000
REACT_APP_API_RETRY_ATTEMPTS=3
REACT_APP_API_BASE_URL=https://your-api.com

# UI Configuration
REACT_APP_DEFAULT_THEME=dark
REACT_APP_ENABLE_ANIMATIONS=true
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_AUTO_SAVE_INTERVAL=30000

# Feature Flags
REACT_APP_ENABLE_LAZY_LOADING=true
REACT_APP_ENABLE_CODE_SPLITTING=true
REACT_APP_ENABLE_SERVICE_WORKER=true
REACT_APP_CHUNK_LOAD_TIMEOUT=120000

# Application Info
REACT_APP_VERSION=1.0.0
REACT_APP_BUILD_NUMBER=1
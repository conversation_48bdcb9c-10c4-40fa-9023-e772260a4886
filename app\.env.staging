# Staging Environment Configuration
# This file contains staging-specific environment variables

# Application Configuration
NODE_ENV=production
REACT_APP_VERSION=1.0.0-staging
REACT_APP_ENVIRONMENT=staging

# API Configuration
REACT_APP_API_TIMEOUT=45000
REACT_APP_API_RETRY_ATTEMPTS=3
REACT_APP_API_BASE_URL=https://staging-api.memecointrader.com

# Feature Flags
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_ERROR_REPORTING=true
REACT_APP_ENABLE_PERFORMANCE_MONITORING=true
REACT_APP_DEBUG_MODE=true
REACT_APP_ENABLE_CONSOLE_LOGS=true
REACT_APP_ENABLE_REMOTE_LOGGING=true

# Security Configuration
REACT_APP_ENABLE_CSP=true
REACT_APP_ENABLE_HTTPS=true
REACT_APP_SESSION_TIMEOUT=7200000
REACT_APP_MAX_LOGIN_ATTEMPTS=10

# Performance Configuration
REACT_APP_ENABLE_LAZY_LOADING=true
REACT_APP_ENABLE_CODE_SPLITTING=true
REACT_APP_ENABLE_SERVICE_WORKER=false
REACT_APP_CHUNK_LOAD_TIMEOUT=180000

# Trading Configuration
REACT_APP_MAX_POSITIONS=5
REACT_APP_DEFAULT_RISK_PERCENT=1
REACT_APP_ENABLE_WHALE_TRACKING=true
REACT_APP_ENABLE_MEME_COIN_SCANNING=true
REACT_APP_TRADING_REFRESH_INTERVAL=10000

# UI Configuration
REACT_APP_THEME=dark
REACT_APP_ENABLE_ANIMATIONS=true
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_AUTO_SAVE_INTERVAL=15000

# Logging Configuration
REACT_APP_LOG_LEVEL=debug
REACT_APP_MAX_LOG_STORAGE=2000

# Error Reporting Configuration
REACT_APP_ERROR_REPORTING_ENDPOINT=https://staging-errors.memecointrader.com/api/report

# Build Configuration
GENERATE_SOURCEMAP=true
INLINE_RUNTIME_CHUNK=false
IMAGE_INLINE_SIZE_LIMIT=4096

# Electron Configuration
ELECTRON_IS_DEV=0
module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
    jest: true,
  },
  extends: [
    'eslint:recommended',
    'plugin:react/recommended',
  ],
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 'latest',
    sourceType: 'module',
  },
  plugins: ['react'],
  rules: {
    // Core rules
    'no-unused-vars': 'warn',
    'no-console': 'warn',
    'prefer-const': 'error',
    'no-var': 'error',
    // React rules
    'react/react-in-jsx-scope': 'off',
    'react/prop-types': 'off',
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',
    // Formatting rules
    indent: ['error', 2],
    quotes: ['error', 'single'],
    semi: ['error', 'always'],
    'comma-dangle': ['error', 'always-multiline'],
    'no-trailing-spaces': 'error',
  },
  settings: {
    react: {
      version: 'detect',
    },
  },
  overrides: [{
    // Handle CommonJS files (backend/Node.js)
    files: ['**/*.js'],
    excludedFiles: ['src/**/*.js', 'src/**/*.jsx'],
    parserOptions: {
      sourceType: 'script',
    },
    env: {
      node: true,
      browser: false,
    },
  }, {
    // Handle ES modules and JSX (frontend/React)
    files: ['src/**/*.js', 'src/**/*.jsx'],
    excludedFiles: ['src/**/*.d.ts'],
    parserOptions: {
      ecmaFeatures: {
        jsx: true,
      },
      ecmaVersion: 'latest',
      sourceType: 'module',
    },
    env: {
      browser: true,
      node: false,
      es2021: true,
    },
  }],
  ignorePatterns: [
    'node_modules/',
    'build/',
    'dist/',
    '*.min.js',
    '**/*.d.ts',
    'src/**/*.d.ts',
    'coverage/',
    'public/',
    'scripts/',
  ],
};

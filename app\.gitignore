# Dependencies
/node_modules
/.pnp
.pnp.js

# Testing
/coverage
*.coverage
*.lcov
.nyc_output

# Production
/build
/dist
/out

# Misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*
*.log

# Editor directories and files
.idea
.vscode
*.swp
*.swo
*.swn
*.bak
*~

# OS files
Thumbs.db
ehthumbs.db
Desktop.ini

# Electron
/dist
/release
/app/dist
/app/out
*.unpacked

# Database backups
*.db.backup
*.sqlite.backup
/databases/backups

# Trading logs
/logs
/trading/logs
/app/logs

# Temporary files
*.tmp
*.temp
.cache

# Package manager
.npm
.yarn-integrity
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Build artifacts
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Webpack
.webpack/

# Bundle analysis
/stats.json
/bundle-stats.html
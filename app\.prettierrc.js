"use strict";

module.exports = {
    // Print width - line wrap at 100 characters
    printWidth: 100,
    // Tab width - use 2 spaces for indentation
    tabWidth: 2,
    // Use spaces instead of tabs
    useTabs: false,
    // Semicolons at the end of statements
    semi: true,
    // Use single quotes instead of double quotes
    singleQuote: true,
    // Quote properties only when necessary
    quoteProps: 'as-needed',
    // Use single quotes in JSX
    jsxSingleQuote: true,
    // Trailing commas in multiline structures
    trailingComma: 'es5',
    // Spaces inside object braces
    bracketSpacing: true,
    // JSX bracket placement
    bracketSameLine: false,
    // Arrow function parentheses
    arrowParens: 'avoid',
    // Range formatting - format entire file
    rangeStart: 0,
    rangeEnd: Infinity,
    // Require pragma
    requirePragma: false,
    // Insert pragma
    insertPragma: false,
    // Prose wrap
    proseWrap: 'preserve',
    // HTML whitespace sensitivity
    htmlWhitespaceSensitivity: 'css',
    // Vue files script and style tags indentation
    vueIndentScriptAndStyle: false,
    // End of line
    endOfLine: 'lf',
    // Embedded language formatting
    embeddedLanguageFormatting: 'auto',
    // Single attribute per line in HTML, Vue and JSX
    singleAttributePerLine: false
};
customModes:
  - slug: context-engineer
    name: Context Engineer
    description: Specialized mode for managing context engine
    roleDefinition: You are a context engineer specialized in AI model integration
    groups: [read, edit, browser, command]
  - slug: context-analyzer
    name: Context Analyzer
    description: Deep analysis mode for project understanding
    roleDefinition: You analyze codebases and provide actionable insights
    groups: [read, browser, command]
  - slug: context-generator
    name: Context Generator
    description: Specialized in generating optimal context for AI models
    roleDefinition: You create optimal context for AI models
    groups: [read, edit, browser]
  - slug: ai-workflow
    name: AI Workflow
    description: Manages AI model integration workflows
    roleDefinition: You manage AI model workflows
    groups: [read, edit, browser, command]
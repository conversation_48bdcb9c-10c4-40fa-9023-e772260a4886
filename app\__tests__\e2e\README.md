# End-to-End Application Tests

This directory contains comprehensive end-to-end tests for the Meme Coin Trader application, covering the complete user
workflow from UI startup to trading operations.

## Test Coverage

### Requirements Coverage

The end-to-end tests cover all requirements specified in task 8.3:

- **Requirement 6.1**: Configuration files loaded successfully
- **Requirement 6.2**: Environment variables properly set
- **Requirement 6.3**: API keys and credentials accessible
- **Requirement 6.4**: Feature flags respected
- **Requirement 6.5**: Configuration changes handled appropriately

### Test Categories

#### 1. Configuration Loading and Environment Setup

- Tests configuration file loading and validation
- Verifies environment variables are properly set
- Validates configuration structure and values

#### 2. API Keys and Credentials Access

- Tests that API keys and secrets are accessible
- Verifies credential fallback mechanisms
- Tests graceful handling of missing credentials

#### 3. Feature Flags Handling

- Tests feature flag reading and validation
- Verifies feature flag changes are handled correctly
- Tests environment-based feature flag overrides

#### 4. Configuration Changes Handling

- Tests dynamic configuration updates
- Verifies configuration changes without restart
- Tests configuration validation after changes

#### 5. Complete User Workflow Simulation

- Tests full application startup sequence
- Simulates complete user workflow from startup to configuration
- Verifies all workflow steps complete successfully

#### 6. Component Integration Verification

- Tests that all components work together seamlessly
- Verifies error handling across components
- Tests component communication and data flow

#### 7. Performance and Load Testing

- Tests application performance with large configurations
- Verifies efficient handling of multiple configuration changes
- Tests system responsiveness under load

## Test Files

### `simple-workflow.test.js`

Core end-to-end workflow tests that cover all requirements without complex UI dependencies. This is the main test file
that validates:

- Configuration loading and environment setup
- API key and credential access
- Feature flag handling
- Configuration change management
- Complete user workflow simulation
- Component integration
- Performance under load

### `application-workflow.test.js`

Comprehensive trading system workflow tests with mocked dependencies. Covers:

- Complete application startup workflow
- Environment-specific testing
- Security testing
- Performance and load testing

### `complete-application-workflow.test.js`

Full UI application workflow tests using React Testing Library. Tests:

- React component integration
- IPC communication between UI and backend
- Real-time status updates
- Error handling in UI components
- User interaction workflows

### `electron-main-process-workflow.test.js`

Electron main process integration tests. Covers:

- Main process startup and initialization
- IPC handler registration and communication
- Trading system integration
- Configuration and environment handling
- Error handling and recovery

## Running the Tests

### Individual Test Files

```bash
# Run simple workflow tests
npm run test src/__tests__/e2e/simple-workflow.test.js

# Run trading system workflow tests  
npm run test trading/__tests__/e2e/application-workflow.test.js

# Run UI workflow tests
npm run test src/__tests__/e2e/complete-application-workflow.test.js

# Run main process tests
npm run test __tests__/e2e/electron-main-process-workflow.test.js
```

### All E2E Tests

```bash
# Run all end-to-end tests
npm run test:e2e

# Run with custom test runner
npm run test:e2e:run
```

### Test Configuration

```bash
# Run with specific Jest config
npx jest --config __tests__/e2e/jest.e2e.config.js
```

## Test Results and Reporting

### Test Report Generation

The test runner generates comprehensive reports including:

- Test execution summary
- Requirements coverage mapping
- Performance metrics
- Error details and debugging information
- Detailed JSON report for CI/CD integration

### Report Files

- `e2e-test-report.json` - Detailed test execution report
- Coverage reports in `coverage/e2e/` directory
- Console output with formatted results

## Test Environment Setup

### Prerequisites

- Node.js and npm installed
- All application dependencies installed (`npm install`)
- Test environment configured

### Environment Variables

The tests use the following environment variables:

- `NODE_ENV=test`
- `TRADING_API_KEY=test-api-key`
- `TRADING_SECRET=test-secret`
- `DATABASE_URL=sqlite::memory:`
- `FEATURE_*` flags for feature testing

### Mock Configuration

Tests use comprehensive mocking for:

- Electron APIs (app, BrowserWindow, ipcMain, ipcRenderer)
- File system operations
- Database connections
- External API calls
- Trading system components

## Continuous Integration

### CI/CD Integration

The tests are designed to run in CI/CD environments with:

- Headless execution support
- JSON output for result parsing
- Exit codes for build pipeline integration
- Comprehensive error reporting

### Performance Benchmarks

Tests include performance benchmarks to ensure:

- Configuration loading completes within 100ms
- Multiple configuration changes process within 50ms
- UI rendering completes within 2 seconds
- System startup completes within reasonable time

## Troubleshooting

### Common Issues

#### Test Discovery Issues

- Ensure test files are in correct directories (`src/__tests__/e2e/` or `trading/__tests__/e2e/`)
- Check Jest configuration paths
- Verify file naming follows `*.test.js` pattern

#### Mock Issues

- Ensure all dependencies are properly mocked
- Check mock setup in `setup.js`
- Verify mock implementations match actual API

#### Environment Issues

- Check environment variable setup
- Verify test environment isolation
- Ensure cleanup between tests

### Debug Mode

Run tests with additional debugging:

```bash
# Enable verbose output
npx jest --verbose

# Run with debugging
npx jest --detectOpenHandles --forceExit

# Run single test with full output
npx jest src/__tests__/e2e/simple-workflow.test.js --verbose --no-cache
```

## Contributing

When adding new end-to-end tests:

1. Follow the existing test structure and naming conventions
2. Include comprehensive requirements coverage comments
3. Add appropriate mocking for external dependencies
4. Include performance benchmarks where applicable
5. Update this README with new test descriptions
6. Ensure tests are isolated and can run independently

## Requirements Validation

This test suite validates that the application meets all specified requirements:

✅ **Complete user workflow from UI startup to trading operations**

- Tests full application startup sequence
- Validates user interaction workflows
- Verifies end-to-end functionality

✅ **Verify all components work together seamlessly**

- Tests component integration
- Validates data flow between components
- Verifies error handling across system

✅ **Test configuration loading and environment handling**

- Validates configuration file loading (Req 6.1)
- Tests environment variable handling (Req 6.2)
- Verifies credential access (Req 6.3)
- Tests feature flag handling (Req 6.4)
- Validates configuration changes (Req 6.5)

The test suite provides comprehensive coverage of all application functionality and ensures the system works as intended
from the user's perspective.
'use strict';

/**
 * Application Startup End-to-End Test
 * Tests application startup sequence and component loading
 */
const ApplicationTestHelpers = require('./helpers/ApplicationTestHelpers');
describe('Application Startup Sequence', () => {
  let electronProcess;
  let testHelper;
  const testTimeout = 60000; // 60 seconds

  beforeAll(() => {
    // Ensure test environment is set up
    process.env.NODE_ENV = 'test';
    process.env.ELECTRON_IS_DEV = '1';
    testHelper = new ApplicationTestHelpers();
  });
  afterAll(() => {
    // Clean up any running processes
    if (electronProcess && !electronProcess.killed) {
      electronProcess.kill();
    }
  });
  test('should start Electron application without errors', async () => {
    electronProcess = await testHelper.startElectronApp();
    expect(electronProcess).toBeDefined();
  }, testTimeout);
  test('should load React UI components successfully', () => {
    const componentPaths = ['src/components/Dashboard.jsx', 'src/components/AutonomousDashboard.jsx', 'src/components/StartButton.jsx', 'src/App.js'];
    testHelper.validateComponents(componentPaths);
  });
  test('should initialize trading system components', () => {
    const tradingComponents = ['trading/TradingOrchestrator.js', 'trading/dependencies.js', 'trading/startup.js'];
    testHelper.validateTradingComponents(tradingComponents);
  });
});
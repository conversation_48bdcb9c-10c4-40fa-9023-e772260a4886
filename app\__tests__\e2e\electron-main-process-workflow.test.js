'use strict';

/**
 * @jest-environment node
 */
/**
 * @fileoverview Electron Main Process End-to-End Tests
 * Tests the complete main process workflow including IPC, configuration, and trading system integration
 *
 * Requirements Coverage:
 * - Complete application startup sequence
 * - Configuration loading and environment handling
 * - IPC communication between main and renderer processes
 * - Trading system initialization and startup
 * - Requirements, 6.2, 6.3, 6.4, 6.5
 */
const {
    ipcMain
} = require('electron');
const path = require('path');
const fs = require('fs').promises;

// Import actual modules for integration testing
const TradingOrchestrator = require('../../trading/engines/trading/orchestration/TradingOrchestrator');
const EnhancedConfigManager = require('../../trading/config/ConfigurationManager');

// Mock only external dependencies
// @ts-ignore
jest.mock('better-sqlite3');
// @ts-ignore
jest.mock('ccxt');
jest.mock('../../../utils/logger');
describe('Electron Main Process E2E Workflow', () => {
    let mainWindow;
    let configManager;
    let tradingOrchestrator;
    let testConfigPath;
    beforeAll(() => {
        // Setup test configuration file
        testConfigPath = path.join(__dirname, 'test-config.json');
        const testConfig = {
            trading: {
                enabled: true,
                pairs: ['BTC/USDT', 'ETH/USDT'],
                strategies: {
                    gridBot: {
                        enabled: true,
                    },
                    memeCoin: {
                        enabled: true,
                    },
                },
                risk: {
                    maxPositionSize: 1000,
                    stopLoss: 0.9,
                },
            },
            database: {
                type: 'sqlite',
                path: ':memory:',
                backupEnabled: true,
            },
            exchange: {
                name: 'binance',
                sandbox: true,
                apiKey: 'test-mock-fallback',
                secret: 'test-mock-fallback',
            },
            features: {
                autonomousTrading: true,
                riskManagement: true,
                portfolioTracking: true,
            },
            monitoring: {
                healthChecks: true,
                performanceMetrics: true,
            },
        };
        await fs.writeFile(testConfigPath, JSON.stringify(testConfig, null, 2));

        // Set test environment variables
        process.env.NODE_ENV = 'test';
        process.env.TRADING_CONFIG_PATH = testConfigPath;
        process.env.TRADING_API_KEY = process.env.TEST_API_KEY || 'test-fallback-key';
        process.env.TRADING_SECRET = process.env.TEST_SECRET || 'test-fallback-secret';
        process.env.DATABASE_URL = 'sqlite:';
    });
    afterAll(async () => {
        // Cleanup test files
        try {
            await fs.unlink(testConfigPath);
        } catch (error) {

            // File might not exist, ignore error
        }
    });
    beforeEach(() => {
        jest.clearAllMocks();

        // Initialize components
        configManager = new EnhancedConfigManager();
        tradingOrchestrator = new TradingOrchestrator();
    });
    afterEach(() => {
        // Cleanup
        if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.destroy();
        }
        if (tradingOrchestrator) {
            try {
                tradingOrchestrator.stop();
            } catch (error) {

                // Ignore cleanup errors
            }
        }
    });
    describe('Configuration Loading and Environment Setup', () => {
        test('should load configuration files successfully (Req 6.1)', async () => {
            const config = await configManager.loadConfiguration();
            expect(config).toBeDefined();
            expect(config.trading).toBeDefined();
            expect(config.database).toBeDefined();
            expect(config.exchange).toBeDefined();
            expect(config.features).toBeDefined();

            // Verify specific configuration values
            expect(config.trading.enabled).toBe(true);
            expect(config.trading.pairs).toContain('BTC/USDT');
            expect(config.exchange.sandbox).toBe(true);
        });
        test('should validate environment variables are properly set (Req 6.2)', () => {
            const requiredEnvVars = ['NODE_ENV', 'TRADING_API_KEY', 'TRADING_SECRET', 'DATABASE_URL'];
            requiredEnvVars.forEach(varName => {
                expect(process.env[varName]).toBeDefined();
            });
            expect(process.env.NODE_ENV).toBe('test');
            expect(process.env.TRADING_API_KEY).toBe('test-api-key');
        });
        test('should make API keys and credentials accessible (Req 6.3)', async () => {
            const config = await configManager.loadConfiguration();

            // Test that credentials are accessible through configuration
            expect(config.exchange.apiKey).toBeDefined();
            expect(config.exchange.secret).toBeDefined();

            // Test that environment variables override config values
            const apiKey = process.env.TRADING_API_KEY;
            const secret = process.env.TRADING_SECRET;
            expect(apiKey).toBe(process.env.TEST_API_KEY || 'test-fallback-key');
            expect(secret).toBe(process.env.TEST_SECRET || 'test-fallback-secret');
        });
        test('should respect feature flags (Req 6.4)', async () => {
            const config = await configManager.loadConfiguration();
            expect(config.features.autonomousTrading).toBe(true);
            expect(config.features.riskManagement).toBe(true);
            expect(config.features.portfolioTracking).toBe(true);

            // Test feature flag access
            const autonomousEnabled = configManager.getFeatureFlag('autonomousTrading');
            expect(autonomousEnabled).toBe(true);
        });
        test('should handle configuration changes appropriately (Req 6.5)', async () => {
            const initialConfig = await configManager.loadConfiguration();
            expect(initialConfig.trading.pairs).toHaveLength(2);

            // Update configuration
            const updatedPairs = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT'];
            await configManager.updateConfig('trading.pairs', updatedPairs);

            // Reload and verify changes
            const updatedConfig = await configManager.reloadConfiguration();
            expect(updatedConfig.trading.pairs).toHaveLength(3);
            expect(updatedConfig.trading.pairs).toContain('ADA/USDT');
        });
    });
    describe('Trading System Integration', () => {
        test('should initialize trading orchestrator successfully', async () => {
            const initResult = await tradingOrchestrator.initialize();
            expect(initResult).toBe(true);
            const status = tradingOrchestrator.getStatus();
            expect(status.isInitialized).toBe(true);
        });
        test('should start trading system with all components', async () => {
            await tradingOrchestrator.initialize();
            const startResult = await tradingOrchestrator.start();
            expect(startResult).toBe(true);
            const status = tradingOrchestrator.getStatus();
            expect(status.isRunning).toBe(true);
            expect(status.components).toBeDefined();
        });
        test('should handle trading system errors gracefully', async () => {
            // Force an error during initialization
            const mockError = new Error('Database connection failed');
            jest.spyOn(tradingOrchestrator, 'initialize').mockRejectedValueOnce(mockError);
            await expect(tradingOrchestrator.initialize()).rejects.toThrow('Database connection failed');
            const status = tradingOrchestrator.getStatus();
            expect(status.isInitialized).toBe(false);
        });
    });
    describe('IPC Communication Integration', () => {
        test('should handle start-bot IPC command', async () => {
            return new Promise(resolve => {
                // Setup IPC handler
                ipcMain.handle('start-bot', async () => {
                    try {
                        await tradingOrchestrator.initialize();
                        await tradingOrchestrator.start();
                        return {
                            success: true,
                            message: 'Trading system started successfully',
                        };
                    } catch (error) {
                        return {
                            success: false,
                            error,
                        };
                    }
                });

                // Simulate IPC call
                ipcMain.emit('start-bot');
                setTimeout(() => {
                    expect(tradingOrchestrator.getStatus().isRunning).toBe(true);
                    resolve();
                }, 100);
            });
        });
        test('should handle get-bot-status IPC command', () => {
            return new Promise(resolve => {
                ipcMain.handle('get-bot-status', () => {
                    return tradingOrchestrator.getStatus();
                });

                // Initialize system first
                tradingOrchestrator.initialize().then(() => {
                    ipcMain.emit('get-bot-status');
                    setTimeout(() => {
                        const status = tradingOrchestrator.getStatus();
                        expect(status).toBeDefined();
                        expect(status.isInitialized).toBe(true);
                        resolve();
                    }, 100);
                });
            });
        });
        test('should handle configuration update IPC commands', async () => {
            return new Promise(resolve => {
                ipcMain.handle('update-configuration', async (event, configPath, value) => {
                    try {
                        await configManager.updateConfig(configPath, value);
                        return {
                            success: true,
                        };
                    } catch (error) {
                        return {
                            success: false,
                            error,
                        };
                    }
                });
                ipcMain.emit('update-configuration', 'trading.enabled', false);
                setTimeout(async () => {
                    const config = await configManager.getConfig('trading.enabled');
                    expect(config).toBe(false);
                    resolve();
                }, 100);
            });
        });
    });
    describe('Complete Application Workflow', () => {
        test('should complete full application startup sequence', async () => {
            const workflowSteps = [];

            // Step 1 configuration
            const config = await configManager.loadConfiguration();
            expect(config).toBeDefined();
            workflowSteps.push('CONFIG_LOADED');

            // Step 2 environment
            const envValid = process.env.NODE_ENV === 'test' && process.env.TRADING_API_KEY && process.env.TRADING_SECRET;
            expect(envValid).toBe(true);
            workflowSteps.push('ENV_VALIDATED');

            // Step 3 trading system
            await tradingOrchestrator.initialize();
            expect(tradingOrchestrator.getStatus().isInitialized).toBe(true);
            workflowSteps.push('TRADING_INITIALIZED');

            // Step 4 trading operations
            await tradingOrchestrator.start();
            expect(tradingOrchestrator.getStatus().isRunning).toBe(true);
            workflowSteps.push('TRADING_STARTED');

            // Step 5 system health
            const systemStatus = await tradingOrchestrator.getSystemStatus();
            expect(systemStatus).toBeDefined();
            workflowSteps.push('SYSTEM_HEALTHY');
            expect(workflowSteps).toEqual(['CONFIG_LOADED', 'ENV_VALIDATED', 'TRADING_INITIALIZED', 'TRADING_STARTED', 'SYSTEM_HEALTHY']);
        });
        test('should handle complete user workflow simulation', async () => {
            // Simulate user starting the application
            const userActions = [];

            // User action 1 starts, loads configuration
            const config = await configManager.loadConfiguration();
            expect(config.trading.enabled).toBe(true);
            userActions.push('APP_STARTED');

            // User action 2 modifies trading pairs
            await configManager.updateConfig('trading.pairs', ['BTC/USDT', 'ETH/USDT', 'DOGE/USDT']);
            userActions.push('CONFIG_MODIFIED');

            // User action 3 clicks start button (simulated via IPC)
            await tradingOrchestrator.initialize();
            await tradingOrchestrator.start();
            userActions.push('TRADING_STARTED');

            // User action 4 checks system status
            const status = tradingOrchestrator.getStatus();
            expect(status.isRunning).toBe(true);
            userActions.push('STATUS_CHECKED');

            // User action 5 stops trading
            await tradingOrchestrator.stop();
            expect(tradingOrchestrator.getStatus().isRunning).toBe(false);
            userActions.push('TRADING_STOPPED');
            expect(userActions).toEqual(['APP_STARTED', 'CONFIG_MODIFIED', 'TRADING_STARTED', 'STATUS_CHECKED', 'TRADING_STOPPED']);
        });
    });
    describe('Error Handling and Recovery', () => {
        test('should handle configuration loading errors gracefully', async () => {
            // Test with invalid config path
            const invalidConfigManager = new EnhancedConfigManager('/invalid/path/config.json');
            await expect(invalidConfigManager.loadConfiguration()).rejects.toThrow();
        });
        test('should handle missing environment variables', async () => {
            // Temporarily remove required env var
            const originalApiKey = process.env.TRADING_API_KEY;
            delete process.env.TRADING_API_KEY;
            try {
                // Should handle missing env var gracefully
                const config = await configManager.loadConfiguration();
                expect(config).toBeDefined();
                // Should use default or config file value
            } finally {
                // Restore env var
                process.env.TRADING_API_KEY = originalApiKey;
            }
        });
        test('should recover from trading system failures', async () => {
            await tradingOrchestrator.initialize();
            await tradingOrchestrator.start();

            // Simulate system failure
            await tradingOrchestrator.stop();
            expect(tradingOrchestrator.getStatus().isRunning).toBe(false);

            // Test recovery
            await tradingOrchestrator.start();
            expect(tradingOrchestrator.getStatus().isRunning).toBe(true);
        });
    });
});
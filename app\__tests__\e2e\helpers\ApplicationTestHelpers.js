/**
 * Application Test Helpers
 * Shared utilities for application end-to-end testing
 */

const {spawn} = require('child_process');
const path = require('path');
const fs = require('fs');

class ApplicationTestHelpers {
  constructor() {
    // this.testTimeout = 60000; // 60 seconds
  }

  /**
     * Starts Electron application for testing
     */
  startElectronApp() {
    const appPath = path.resolve(__dirname, '../../..');

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Application startup timeout'));
      }, this.testTimeout);

      const electronProcess = spawn('npm', ['run', 'electron-dev'], {
        cwd: appPath,
        stdio: 'pipe',
      });

      let output = '';
      let errorOutput = '';

      electronProcess.stdout.on('data', (data) => {
        output += data.toString();

        // Look for successful startup indicators
        if (output.includes('Electron app is ready') ||
                    output.includes('React app started') ||
                    output.includes('Main window created')) {
          clearTimeout(timeout);
          resolve(electronProcess);
        }
      });

      electronProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      electronProcess.on('error', (error) => {
        clearTimeout(timeout);
        reject(new Error(`Failed to start Electron: ${error.message}`));
      });

      electronProcess.on('exit', (code) => {
        clearTimeout(timeout);
        if (code !== 0) {
          reject(new Error(`Electron exited with code ${code}. Error output: ${errorOutput}`));
        }
      });
    });
  }

  /**
     * Runs a test script in a subprocess
     */
  runTestScript(scriptContent, testName) {
    return new Promise((resolve, reject) => {
      const tempTestFile = path.join(__dirname, `temp-${testName}-test.js`);
      fs.writeFileSync(tempTestFile, scriptContent);

      const testProcess = spawn('node', [tempTestFile], {
        cwd: path.resolve(__dirname, '../../..'),
        stdio: 'pipe',
      });

      let output = '';
      let errorOutput = '';

      testProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      testProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      testProcess.on('close', (code) => {
        fs.unlinkSync(tempTestFile);

        if (code === 0) {
          resolve(output);
        } else {
          reject(new Error(`${testName} test failed: ${errorOutput}`));
        }
      });
    });
  }

  /**
     * Validates component files exist and contain expected patterns
     */
  validateComponents(componentPaths) {
    for (const componentPath of componentPaths) {
      const fullPath = path.resolve(__dirname, '../../..', componentPath);
      expect(fs.existsSync(fullPath)).toBe(true);

      try {
        const content = fs.readFileSync(fullPath, 'utf8');
        expect(content.length).toBeGreaterThan(0);

        // Check for React component patterns
        if (componentPath.endsWith('.jsx')) {
          expect(content).toMatch(/import.*React/);
          expect(content).toMatch(/export.*default/);
        }
      } catch (error) {
        throw new Error(`Failed to validate component ${componentPath}: ${error.message}`);
      }
    }
  }

  /**
     * Validates trading components exist and are properly structured
     */
  validateTradingComponents(tradingComponents) {
    for (const component of tradingComponents) {
      const componentPath = path.resolve(__dirname, '../../..', component);
      expect(fs.existsSync(componentPath)).toBe(true);

      try {
        const content = fs.readFileSync(componentPath, 'utf8');
        expect(content.length).toBeGreaterThan(0);
      } catch (error) {
        throw new Error(`Failed to validate trading component ${component}: ${error.message}`);
      }
    }
  }

  /**
     * Validates error handling files exist and contain error handling patterns
     */
  validateErrorHandling(errorHandlingFiles) {
    for (const file of errorHandlingFiles) {
      const filePath = path.resolve(__dirname, '../../..', file);
      expect(fs.existsSync(filePath)).toBe(true);

      const content = fs.readFileSync(filePath, 'utf8');
      expect(content.length).toBeGreaterThan(0);

      // Check for error handling patterns
      expect(content).toMatch(/error|Error|catch|try/);
    }
  }
}

module.exports = ApplicationTestHelpers;
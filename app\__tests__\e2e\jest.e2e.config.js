/**
 * @fileoverview Jest Configuration for End-to-End Tests
 * Specialized configuration for comprehensive application workflow testing
 */

module.exports = {
  displayName: 'E2E Tests',
  testEnvironment: 'node',
  testMatch: [
    '<rootDir>/../../__tests__/e2e/**/*.test.js',
    '<rootDir>/../../src/__tests__/e2e/**/*.test.js',
    '<rootDir>/../../trading/__tests__/e2e/**/*.test.js',
  ],
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/build/',
    '<rootDir>/dist/',
  ],
  setupFilesAfterEnv: ['<rootDir>/../../__tests__/e2e/setup.js'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/../../src/$1',
    '^@components/(.*)$': '<rootDir>/../../src/components/$1',
    '^@utils/(.*)$': '<rootDir>/../../src/utils/$1',
    '^@api/(.*)$': '<rootDir>/../../src/api/$1',
    '^@trading/(.*)$': '<rootDir>/../../trading/$1',
  },
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': 'babel-jest',
  },
  moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx', 'json'],
  collectCoverageFrom: [
    '<rootDir>/../../src/**/*.{js,jsx}',
    '<rootDir>/../../trading/**/*.{js,jsx}',
    '!<rootDir>/../../src/**/*.d.ts',
    '!<rootDir>/../../src/index.jsx',
    '!**/__tests__/**',
    '!**/node_modules/**',
  ],
  coverageDirectory: '<rootDir>/../../coverage/e2e',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  testTimeout: 60000, // 60 seconds for E2E tests
  maxWorkers: 1, // Run E2E tests sequentially
  verbose: true,
  detectOpenHandles: true,
  forceExit: true,
  globals: {
    'ts-jest': {
      useESM: true,
    },
  },
  transformIgnorePatterns: [
    'node_modules/(?!(react-native|@react-native|react-native-vector-icons)/)',
  ],
};
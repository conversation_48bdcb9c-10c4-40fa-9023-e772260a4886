/**
 * @fileoverview End-to-End Test Runner
 * Executes all end-to-end tests for the application
 * Provides comprehensive testing of the complete user workflow
 */

/* eslint-disable no-console */

const {spawn} = require('child_process');
const path = require('path');
const fs = require('fs');

class E2ETestRunner {
    constructor() {
        // this.testResults = {
            passed: 0,
            failed: 0,
            total: 0,
            details: [],
        };
    }

    async runTest(testFile, description) {
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        console.log(`\n🧪 Running ${description}...`);
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        console.log(`📁 Test file: ${testFile}`);

        return new Promise((resolve) => {
            const jest = spawn('npx', ['jest', testFile, '--verbose', '--no-cache'], {
                cwd: path.join(__dirname, '../..'),
                stdio: 'pipe',
            });

            let output = '';
            let errorOutput = '';

            jest.stdout.on('data', (data) => {
                output += data.toString();
                process.stdout.write(data);
            });

            jest.stderr.on('data', (data) => {
                errorOutput += data.toString();
                process.stderr.write(data);
            });

            jest.on('close', (code) => {
                const result = {
                    testFile,
                    description,
                    passed: code === 0,
                    output,
                    errorOutput,
                    exitCode: code,
                };

                // this.testResults.total++;
                if (code === 0) {
                    // this.testResults.passed++;
                    // eslint-disable-next-line no-console

                    // eslint-disable-next-line no-console


                    // eslint-disable-next-line no-console



                    console.log(`✅ ${description} - PASSED`);
                } else {
                    // this.testResults.failed++;
                    // eslint-disable-next-line no-console

                    // eslint-disable-next-line no-console


                    // eslint-disable-next-line no-console



                    console.log(`❌ ${description} - FAILED (exit code: ${code})`);
                }

                // this.testResults.details.push(result);
                resolve(result);
            });
        });
    }

    async runAllTests() {
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        console.log('🚀 Starting End-to-End Application Tests');
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        console.log('='.repeat(60));

        const tests = [
            {
                file: 'trading/__tests__/e2e/application-workflow.test.js',
                description: 'Trading System E2E Workflow Tests',
            },
            {
                file: 'src/__tests__/e2e/complete-application-workflow.test.js',
                description: 'Complete UI Application Workflow Tests',
            },
            {
                file: '__tests__/e2e/electron-main-process-workflow.test.js',
                description: 'Electron Main Process Integration Tests',
            },
        ];

        // Run tests sequentially to avoid conflicts
        for (const test of tests) {
            await this.runTest(test.file, test.description);
        }

        // this.generateReport();
    }

    generateReport() {
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        console.log('\n' + '='.repeat(60));
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        console.log('📊 END-TO-END TEST RESULTS SUMMARY');
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        console.log('='.repeat(60));

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log(`Total Tests: ${this.testResults.total}`);
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        console.log(`✅ Passed: ${this.testResults.passed}`);
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        console.log(`❌ Failed: ${this.testResults.failed}`);

        const successRate = this.testResults.total > 0
            ? ((this.testResults.passed / this.testResults.total) * 100).toFixed(1)
            : '0.0';
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        console.log(`📈 Success Rate: ${successRate}%`);

        if (this.testResults.failed > 0) {
            // eslint-disable-next-line no-console

            // eslint-disable-next-line no-console


            // eslint-disable-next-line no-console



            console.log('\n❌ FAILED TESTS:');
            // this.testResults.details
                .filter((result) => !result.passed)
                .forEach((result) => {
                    // eslint-disable-next-line no-console

                    // eslint-disable-next-line no-console


                    // eslint-disable-next-line no-console



                    console.log(`  - ${result.description}`);
                    // eslint-disable-next-line no-console

                    // eslint-disable-next-line no-console


                    // eslint-disable-next-line no-console



                    console.log(`    File: ${result.testFile}`);
                    // eslint-disable-next-line no-console

                    // eslint-disable-next-line no-console


                    // eslint-disable-next-line no-console



                    console.log(`    Exit Code: ${result.exitCode}`);
                });
        }

        // Generate detailed report file
        // this.generateDetailedReport();

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log('\n' + '='.repeat(60));

        if (this.testResults.failed === 0) {
            // eslint-disable-next-line no-console

            // eslint-disable-next-line no-console


            // eslint-disable-next-line no-console



            console.log('🎉 All end-to-end tests passed successfully!');
            // eslint-disable-next-line no-console

            // eslint-disable-next-line no-console


            // eslint-disable-next-line no-console



            console.log('✨ Application workflow is fully functional');
            process.exit(0);
        } else {
            // eslint-disable-next-line no-console

            // eslint-disable-next-line no-console


            // eslint-disable-next-line no-console



            console.log('⚠️  Some end-to-end tests failed');
            // eslint-disable-next-line no-console

            // eslint-disable-next-line no-console


            // eslint-disable-next-line no-console



            console.log('🔍 Check the detailed report for more information');
            process.exit(1);
        }
    }

    generateDetailedReport() {
        const reportPath = path.join(__dirname, 'e2e-test-report.json');
        const successRate = this.testResults.total > 0
            ? ((this.testResults.passed / this.testResults.total) * 100).toFixed(1)
            : '0.0';

        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                total: this.testResults.total,
                passed: this.testResults.passed,
                failed: this.testResults.failed,
                successRate: `${successRate}%`,
            },
            testDetails: this.testResults.details.map((result) => ({
                testFile: result.testFile,
                description: result.description,
                passed: result.passed,
                exitCode: result.exitCode,
                hasOutput: result.output.length > 0,
                hasErrors: result.errorOutput.length > 0,
            })),
            requirements: {
                '6.1': 'Configuration files loaded successfully',
                '6.2': 'Environment variables properly set',
                '6.3': 'API keys and credentials accessible',
                '6.4': 'Feature flags respected',
                '6.5': 'Configuration changes handled appropriately',
            },
            coverage: {
                'Complete user workflow': 'Tested from UI startup to trading operations',
                'Component integration': 'Verified all components work together seamlessly',
                'Configuration loading': 'Tested configuration and environment handling',
                'Error handling': 'Verified graceful error handling and recovery',
                'Performance': 'Tested application performance under load',
            },
        };

        try {
            fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
            // eslint-disable-next-line no-console

            // eslint-disable-next-line no-console


            // eslint-disable-next-line no-console



            console.log(`📄 Detailed report saved to: ${reportPath}`);
        } catch (error) {
            // eslint-disable-next-line no-console

            // eslint-disable-next-line no-console


            // eslint-disable-next-line no-console



            console.warn(`⚠️  Could not save detailed report: ${error.message}`);
        }
    }
}

// Run tests if this script is executed directly
if (require.main === module) {
    const runner = new E2ETestRunner();
    runner.runAllTests().catch((error) => {
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.error('❌ Error running E2E tests:', error);
        process.exit(1);
    });
}

module.exports = E2ETestRunner;
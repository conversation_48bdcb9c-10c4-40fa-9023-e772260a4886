'use strict';

function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value: value,
      enumerable: true,
      configurable: true,
      writable: true,
    });
  } else {
    obj[key] = value;
  }
  return obj;
}

function ownKeys(object, enumerableOnly) {
  const keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    let symbols = Object.getOwnPropertySymbols(object);
    if (enumerableOnly) {
      symbols = symbols.filter(function (sym) {
        return Object.getOwnPropertyDescriptor(object, sym).enumerable;
      });
    }
    keys.push.apply(keys, symbols);
  }
  return keys;
}

function _objectSpread(target) {
  for (let i = 1; i < arguments.length; i++) {
    const source = arguments[i] != null ? arguments[i] : {};
    if (i % 2) {
      ownKeys(Object(source), true).forEach(function (key) {
        _defineProperty(target, key, source[key]);
      });
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
      ownKeys(Object(source)).forEach(function (key) {
        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
      });
    }
  }
  return target;
}

/**
 * @fileoverview End-to-End Test Setup
 * Global setup and configuration for E2E tests
 */



// Setup test environment
process.env.NODE_ENV = 'test';
process.env.ELECTRON_IS_DEV = 'false';
process.env.CI = 'true';

// Mock electron modules for testing
jest.mock('electron', () => ({
  app: {
    on: jest.fn(),
    quit: jest.fn(),
    whenReady: jest.fn().mockResolvedValue(true),
    getPath: jest.fn(name => {
      const paths = {
        'home': '/mock/home',
        'userData': '/mock/userData',
        'logs': '/mock/logs',
        'temp': '/mock/temp',
      };
      return paths[name] || '/mock/default';
    }),
    getAppPath: jest.fn(() => '/mock/app'),
    getName: jest.fn(() => 'Meme Coin Trader'),
    getVersion: jest.fn(() => '1.0.0'),
  },
  BrowserWindow: jest.fn().mockImplementation(() => ({
    loadFile: jest.fn(),
    loadURL: jest.fn(),
    webContents: {
      send: jest.fn(),
      on: jest.fn(),
      openDevTools: jest.fn(),
      executeJavaScript: jest.fn(),
    },
    on: jest.fn(),
    show: jest.fn(),
    hide: jest.fn(),
    close: jest.fn(),
    destroy: jest.fn(),
    isDestroyed: jest.fn().mockReturnValue(false),
  })),
  ipcMain: {
    handle: jest.fn(),
    on: jest.fn(),
    emit: jest.fn(),
    removeAllListeners: jest.fn(),
  },
  ipcRenderer: {
    invoke: jest.fn(),
    on: jest.fn(),
    send: jest.fn(),
    removeAllListeners: jest.fn(),
  },
}));

// Mock file system operations for testing
jest.mock('fs', () => ({
  promises: {
    readFile: jest.fn(),
    writeFile: jest.fn(),
    mkdir: jest.fn(),
    access: jest.fn(),
    stat: jest.fn(),
    unlink: jest.fn(),
  },
  existsSync: jest.fn().mockReturnValue(true),
  mkdirSync: jest.fn(),
  readFileSync: jest.fn().mockReturnValue('{}'),
  writeFileSync: jest.fn(),
  stat: jest.fn((path, callback) => callback(null, {
    isDirectory: () => false,
  })),
  createWriteStream: jest.fn(() => ({
    write: jest.fn(),
    end: jest.fn(),
    on: jest.fn(),
  })),
  createReadStream: jest.fn(() => ({
    on: jest.fn(),
    pipe: jest.fn(),
  })),
}));

// Mock database operations
jest.mock('better-sqlite3', () => {
  const mRun = jest.fn();
  const mGet = jest.fn();
  const mAll = jest.fn().mockReturnValue([]);
  const mPrepare = jest.fn(() => ({
    run: mRun,
    get: mGet,
    all: mAll,
  }));
  return jest.fn().mockImplementation(() => ({
    prepare: mPrepare,
    exec: jest.fn(),
    close: jest.fn(),
    pragma: jest.fn(),
  }));
});

// Mock CCXT exchange library
jest.mock('ccxt', () => ({
  binance: jest.fn().mockImplementation(() => ({
    loadMarkets: jest.fn().mockResolvedValue({}),
    fetchTicker: jest.fn().mockResolvedValue({
      symbol: 'BTC/USDT',
      last: 50000,
      bid: 49999,
      ask: 50001,
    }),
    fetchBalance: jest.fn().mockResolvedValue({
      USDT: {
        free: 10000,
        used: 0,
        total: 10000,
      },
    }),
    createOrder: jest.fn().mockResolvedValue({
      id: 'test-order-123',
      symbol: 'BTC/USDT',
      amount: 1,
      price: 50000,
      status: 'open',
    }),
  })),
}));

// Setup global test utilities
global.testUtils = {
  createMockConfig: () => ({
    trading: {
      enabled: true,
      pairs: ['BTC/USDT', 'ETH/USDT'],
      strategies: {
        gridBot: {
          enabled: true,
        },
        memeCoin: {
          enabled: false,
        },
      },
    },
    database: {
      type: 'sqlite',
      path: ':memory:',
    },
    exchange: {
      name: 'binance',
      sandbox: true,
      apiKey: 'mock-test-key',
      secret: 'mock-test-secret',
    },
    features: {
      autonomousTrading: false,
      riskManagement: true,
    },
  }),
  createMockEnvironment: () => ({
    NODE_ENV: 'test',
    TRADING_API_KEY: 'mock-api-key',
    TRADING_SECRET: 'mock-secret',
    DATABASE_URL: 'sqlite::memory:',
  }),
  delay: ms => new Promise(resolve => setTimeout(resolve, ms)),
  waitForCondition: async (condition, timeout = 5000) => {
    const start = Date.now();
    while (Date.now() - start < timeout) {
      if (await condition()) {
        return true;
      }
      await global.testUtils.delay(100);
    }
    throw new Error(`Condition not met within ${timeout}ms`);
  },
};

// Setup console logging for tests
const originalConsole = global.console;
global.console = _objectSpread(_objectSpread({}, originalConsole), {}, {
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
});

// Global test cleanup
afterEach(() => {
  jest.clearAllMocks();
});

// Global error handling for tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
process.on('uncaughtException', error => {
  console.error('Uncaught Exception:', error);
});

console.log('🧪 E2E Test environment setup complete');
console.log(`📁 Test directory: ${__dirname}`);
console.log(`🌍 Environment: ${process.env.NODE_ENV}`);
console.log(`⚡ Electron dev mode: ${process.env.ELECTRON_IS_DEV}`);
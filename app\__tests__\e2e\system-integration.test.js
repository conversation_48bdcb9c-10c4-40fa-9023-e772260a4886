'use strict';

/**
 * System Integration End-to-End Test
 * Tests IPC communication, database integration, and configuration loading
 */
const ApplicationTestHelpers = require('./helpers/ApplicationTestHelpers');
describe('System Integration', () => {
    let testHelper;
    beforeAll(() => {
        testHelper = new ApplicationTestHelpers();
    });
    describe('IPC Communication', () => {
        test('should establish IPC channels successfully', () => {
            const ipcTestScript = `
                const { ipcMain, ipcRenderer } = require('electron');
                const path = require('path');
                
                // Test that IPC channels are defined
                const requiredChannels = [
                    'start-bot',
                    'stop-bot',
                    'get-bot-status',
                    'get-portfolio-summary',
                    'get-trading-stats'
                ];
                
                console.log('Testing IPC channels...');
                
                // In a real test, we would check if these channels are registered
                // For now, we'll check if the IPC service files exist
                const ipcServicePath = path.join(__dirname, '../..', 'src/services/ipcService.js');
                const fs = require('fs');
                
                if (fs.existsSync(ipcServicePath)) {
                    const ipcService = fs.readFileSync(ipcServicePath, 'utf8');
                    
                    for (const channel of requiredChannels) {
                        if (!ipcService.includes(channel)) {
                            throw new Error('IPC channel not found: ' + channel);
                        }
                    }
                    
                    console.log('All required IPC channels found');
                } else {
                    throw new Error('IPC service file not found');
                }
            `;
            await testHelper.runTestScript(ipcTestScript, 'ipc');
        });
    });
    describe('Database Integration', () => {
        test('should initialize database connections', () => {
            const dbTestScript = `
                const path = require('path');
                const fs = require('fs');
                
                console.log('Testing database initialization...');
                
                // Check for database initialization files
                const dbFiles = [
                    'trading/engines/database/unified-database-initializer.js',
                    'trading/engines/database/connection-manager.js',
                    'trading/databases/unified_schema.sql'
                ];
                
                for (const dbFile of dbFiles) {
                    const dbPath = path.join(__dirname, '../..', dbFile);
                    if (!fs.existsSync(dbPath)) {
                        throw new Error('Database file missing: ' + dbFile);
                    }
                }
                
                console.log('Database files validated');
                
                // Test database initialization (mock)
                try {
                    const dbInitPath = path.join(__dirname, '../..', 'trading/engines/database/unified-database-initializer.js');
                    const dbInit = fs.readFileSync(dbInitPath, 'utf8');
                    
                    // Check for key database initialization patterns
                    if (!dbInit.includes('createConnection') && !dbInit.includes('initialize')) {
                        throw new Error('Database initialization patterns not found');
                    }
                    
                    console.log('Database initialization validated');
                } catch (error) {
                    throw new Error('Database validation failed: ' + error.message);
                }
            `;
            await testHelper.runTestScript(dbTestScript, 'db');
        });
    });
    describe('Configuration Loading', () => {
        test('should load environment-specific configuration', () => {
            const configTestScript = `
                const path = require('path');
                const fs = require('fs');
                
                console.log('Testing configuration loading...');
                
                // Check environment configuration
                const envConfigPath = path.join(__dirname, '../..', 'src/config/environment.js');
                if (fs.existsSync(envConfigPath)) {
                    console.log('Environment configuration found');
                    
                    // Basic validation that it's a proper config file
                    const envConfig = fs.readFileSync(envConfigPath, 'utf8');
                    if (!envConfig.includes('environment') || !envConfig.includes('config')) {
                        throw new Error('Environment configuration appears invalid');
                    }
                } else {
                    throw new Error('Environment configuration not found');
                }
                
                // Check trading configuration
                const tradingConfigPath = path.join(__dirname, '../..', 'trading/config');
                if (!fs.existsSync(tradingConfigPath)) {
                    throw new Error('Trading configuration directory not found');
                }
                
                const configFiles = fs.readdirSync(tradingConfigPath);
                if (configFiles.length === 0) {
                    throw new Error('No trading configuration files found');
                }
                
                console.log('Configuration loading validated');
            `;
            await testHelper.runTestScript(configTestScript, 'config');
        });
    });
    describe('Error Handling', () => {
        test('should have comprehensive error handling in place', () => {
            const errorHandlingFiles = ['src/components/ErrorBoundary.jsx', 'src/services/ErrorReporter.js', 'trading/engines/shared/error-handling/ErrorHandler.js'];
            testHelper.validateErrorHandling(errorHandlingFiles);
        });
    });
});
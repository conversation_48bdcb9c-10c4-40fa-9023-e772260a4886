/**
 * @fileoverview Mock Classes for IPC Testing
 * @description Provides mock implementations for testing IPC handlers
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * Mock IPC Main for testing
 */
class MockIpcMain {
    constructor() {
        // this.handlers = new Map();
    }

    handle(channel, handler) {
        // this.handlers.set(channel, handler);
    }

    async invoke(channel, ...args) {
        const handler = this.handlers.get(channel);
        if (!handler) {
            throw new Error(`No handler registered for channel: ${channel}`);
        }

        // Mock event object
        const mockEvent = {
            sender: {
                send: (_channel, _data) => {
                }, // eslint-disable-line no-console
            }
        };

        return await handler(mockEvent, ...args);
    }

    getRegisteredChannels() {
        // Example by channel length, then alphabetically if lengths are equal
        return Array.from(this.handlers.keys()).sort((a, b) => {
            if (a.length !== b.length) {
                return a.length - b.length;
            }
            return a.localeCompare(b);
        });
    }
}

/**
 * Mock Trading Orchestrator for testing
 */
class MockTradingOrchestrator {
    constructor() {
        // this.isInitialized = true;
        // this.isRunning = false;
        // this.setupComponents();
    }

    setupComponents() {
        // this.components = {
            portfolioManager: {
                getPortfolioSummary: () => ({
                    totalValue: 10000,
                    totalPnL: 500,
                    positions: [
                        {symbol: 'BTC/USDT', value: 5000, pnl: 200},
                        {symbol: 'ETH/USDT', value: 3000, pnl: 150}]
                }),
                getAssetAllocation: () => ({BTC: 50, ETH: 30, USDT: 20}),
                getCrossExchangeBalance: () => ({binance: 5000, coinbase: 3000})
            },
            performanceTracker: {
                getPerformanceMetrics: () => ({
                    winRate: 0.65,
                    sharpeRatio: 1.2,
                    maxDrawdown: 0.15,
                    totalTrades: 100,
                    profitableTrades: 65
                })
            },
            tradingExecutor: {
                getOpenOrders: (symbol) => symbol ? [
                    {id: '1', symbol, type: 'limit', side: 'buy', amount: 1, price: 30000}] : [],
                getOrderHistory: (symbol) => symbol ? [
                    {id: '1', symbol, status: 'filled', side: 'buy', amount: 1, price: 29500}] : [],
                cancelOrder: (id, _symbol) => ({success: true, message: `Order ${id} cancelled`}),
                executeTrade: (params) => ({success: true, tradeId: 'trade123', ...params})
            },
            gridBotManager: {
                startGrid: (config) => ({success: true, gridId: 'grid123', config}),
                stopGrid: (_symbol) => ({success: true, message: `Grid for ${_symbol} stopped`}),
                getActiveGrids: () => [
                    {id: 'grid123', symbol: 'BTC/USDT', status: 'active', profit: 100}]
            },
            memeCoinScanner: {
                start: () => ({success: true, message: 'Meme coin scanner started'}),
                getOpportunities: () => [
                    {symbol: 'DOGE', score: 85, volume: 1000000, change24h: 5.5}]
            },
            whaleTracker: {
                getSignals: () => [
                    {type: 'buy', amount: 100, symbol: 'BTC', timestamp: Date.now()}],
                getTrackedWallets: () => [
                    {address: '**********************************', balance: 50}, // Genesis block address
                ]
            },
            dataCollector: {
                getMarketData: (symbol, timeframe) => ({
                    symbol: symbol || 'BTC/USDT',
                    price: 30000,
                    volume: 1000000,
                    timeframe: timeframe || '1h',
                    timestamp: Date.now()
                }),
                getMarketOverview: () => ({
                    totalMarketCap: 2000000000000,
                    btcDominance: 45,
                    totalVolume: 50000000000
                })
            },
            configManager: {
                getAll: () => ({
                    api: {binance: {enabled: true}, coinbase: {enabled: true}},
                    trading: {maxPositionSize: 10000, riskLevel: 'medium'},
                    risk: {maxDrawdown: 0.2, stopLoss: 0.05}
                }),
                saveSettings: (category, settings) => ({success: true, category, settings})
            },
            riskManager: {
                getRiskMetrics: () => ({
                    portfolioRisk: 0.3,
                    maxPositionSize: 10000,
                    currentExposure: 0.25
                })
            }
        };

        // this.db = {
            getTradeHistory: () => [
                {id: '1', symbol: 'BTC/USDT', profit: 100, timestamp: Date.now() - 86400000}],
            getCoins: () => [
                {symbol: 'BTC', name: 'Bitcoin', price: 30000},
                {symbol: 'ETH', name: 'Ethereum', price: 2000}],
            saveCoin: (coin) => ({success: true, coinId: 'coin123', coin})
        };
    }

    async start() {
        // this.isRunning = true;
        return {success: true, message: 'Trading system started'};
    }

    async stop() {
        // this.isRunning = false;
        return {success: true, message: 'Trading system stopped'};
    }

    async getStatus() {
        return {
            status: this.isRunning ? 'running' : 'stopped',
            isRunning: this.isRunning,
            uptime: Date.now() - (Date.now() - 3600000),
            components: {
                portfolioManager: 'active',
                tradingExecutor: 'active',
                dataCollector: 'active'
            }
        };
    }

    async getPerformanceMetrics() {
        return {
            monthlyReturn: 0.05,
            annualReturn: 0.15,
            totalTrades: 100,
            winRate: 0.65
        };
    }
}

module.exports = {
    MockIpcMain,
    MockTradingOrchestrator
};
// Usage example:
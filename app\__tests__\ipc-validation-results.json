{"channelValidation": {"mainHandlers": ["add-exchange", "add-whale-wallet", "cancel-all-orders", "cancel-order", "clear-logs", "create-backup", "delete-coin", "execute-arbitrage", "export-logs", "export-settings", "get-active-bots", "get-app-version", "get-arbitrage-opportunities", "get-arbitrage-positions", "get-arbitrage-stats", "get-arbitrage-status", "get-asset-allocation", "get-bot-status", "get-coins", "get-component-health", "get-config", "get-cross-exchange-balances", "get-dca-history", "get-dca-positions", "get-detected-opportunities", "get-drawdown-analysis", "get-exchange-balances", "get-exchange-health", "get-exchange-portfolio", "get-exchanges", "get-grid-history", "get-grid-positions", "get-grid-presets", "get-ipc-error-statistics", "get-logs", "get-market-data", "get-market-overview", "get-meme-coin-history", "get-meme-coin-opportunities", "get-monitoring-statistics", "get-open-orders", "get-opportunity-scanner-stats", "get-order-history", "get-performance-history", "get-performance-metrics", "get-pnl-report", "get-portfolio-optimization", "get-portfolio-performance", "get-portfolio-risk-metrics", "get-portfolio-summary", "get-price-history", "get-real-time-status", "get-rebalancing-opportunities", "get-risk-metrics", "get-risk-parameters", "get-scanner-status", "get-settings", "get-status-reports", "get-system-alerts", "get-system-health", "get-system-info", "get-system-metrics", "get-system-status", "get-tracked-whales", "get-trade-history", "get-trading-stats", "get-wallet-balance", "get-whale-history", "get-whale-signals", "get-whale-tracking-status", "health-check", "import-settings", "initialize-trading", "on", "on-arbitrage-executed", "on-arbitrage-opportunity", "place-limit-order", "place-market-order", "rebalance-cross-exchange-portfolio", "rebalance-portfolio", "remove-exchange", "remove-whale-wallet", "report-error", "reset-ipc-error-statistics", "reset-settings", "run-health-check", "save-coin", "save-grid-preset", "save-settings", "set-log-level", "set-risk-parameters", "start-arbitrage-engine", "start-arbitrage-scanning", "start-bot", "start-dca", "start-grid", "start-health-monitoring", "start-meme-coin-scanner", "start-opportunity-scanner", "start-portfolio-monitoring", "stop-all-grids", "stop-arbitrage-engine", "stop-arbitrage-scanning", "stop-bot", "stop-dca", "stop-grid", "stop-health-monitoring", "stop-meme-coin-scanner", "stop-opportunity-scanner", "stop-portfolio-monitoring", "test-exchange-connection", "toggle-whale-tracking", "update-arbitrage-config", "update-coin", "update-config", "update-dca-config", "update-grid-config", "update-opportunity-scanner-config", "update-scanner-config"], "preloadMethods": ["addExchange", "addWhaleWallet", "cancelAllOrders", "cancelOrder", "clearLogs", "createBackup", "deleteCoin", "executeArbitrage", "exportLogs", "exportSettings", "getActiveBots", "getAppVersion", "getArbitrageOpportunities", "getArbitragePositions", "getArbitrageStats", "getArbitrageStatus", "getAssetAllocation", "getBotStatus", "getCoins", "getComponentHealth", "getConfig", "getCrossExchangeBalances", "getDcaHistory", "getDcaPositions", "getDetectedOpportunities", "getDrawdownAnalysis", "getExchangeBalances", "getExchangeHealth", "getExchangePortfolio", "getExchanges", "getGridHistory", "getGridPositions", "getGridPresets", "getIpcErrorStatistics", "getLogs", "getMarketData", "getMarketOverview", "getMemeCoinHistory", "getMemeCoinOpportunities", "getMonitoringStatistics", "getOpenOrders", "getOpportunityScannerStats", "getOrderHistory", "getPerformanceHistory", "getPerformanceMetrics", "getPnlReport", "getPortfolioOptimization", "getPortfolioPerformance", "getPortfolioRiskMetrics", "getPortfolioSummary", "getPriceHistory", "getRealTimeStatus", "getRebalancingOpportunities", "getRiskMetrics", "getRiskParameters", "getScannerStatus", "getSettings", "getStatusReports", "getSystemAlerts", "getSystemHealth", "getSystemInfo", "getSystemMetrics", "getSystemStatus", "getTrackedWhales", "getTradeHistory", "getTradingStats", "getWalletBalance", "getWhaleHistory", "getWhaleSignals", "getWhaleTrackingStatus", "healthCheck", "importSettings", "initializeTrading", "on", "onArbitrageExecuted", "onArbitrageOpportunity", "placeLimitOrder", "placeMarketOrder", "rebalanceCrossExchangePortfolio", "rebalancePortfolio", "removeExchange", "removeWhaleWallet", "reportError", "resetIpcErrorStatistics", "resetSettings", "runHealthCheck", "saveCoin", "saveGridPreset", "saveSettings", "setLogLevel", "setRiskParameters", "startArbitrageEngine", "startArbitrageScanning", "startBot", "startDca", "startGrid", "startHealthMonitoring", "startMemeCoinScanner", "startOpportunityScanner", "startPortfolioMonitoring", "stopAllGrids", "stopArbitrageEngine", "stopArbitrageScanning", "stopBot", "stopDca", "stopGrid", "stopHealthMonitoring", "stopMemeCoinScanner", "stopOpportunityScanner", "stopPortfolioMonitoring", "testExchangeConnection", "toggleWhaleTracking", "updateArbitrageConfig", "updateCoin", "updateConfig", "updateDcaConfig", "updateGridConfig", "updateOpportunityScannerConfig", "updateScannerConfig"], "mainHandlersList": ["add-exchange", "add-whale-wallet", "cancel-all-orders", "cancel-order", "clear-logs", "create-backup", "delete-coin", "execute-arbitrage", "export-logs", "export-settings", "get-active-bots", "get-app-version", "get-arbitrage-opportunities", "get-arbitrage-positions", "get-arbitrage-stats", "get-arbitrage-status", "get-asset-allocation", "get-bot-status", "get-coins", "get-component-health", "get-config", "get-cross-exchange-balances", "get-dca-history", "get-dca-positions", "get-detected-opportunities", "get-drawdown-analysis", "get-exchange-balances", "get-exchange-health", "get-exchange-portfolio", "get-exchanges", "get-grid-history", "get-grid-positions", "get-grid-presets", "get-ipc-error-statistics", "get-logs", "get-market-data", "get-market-overview", "get-meme-coin-history", "get-meme-coin-opportunities", "get-monitoring-statistics", "get-open-orders", "get-opportunity-scanner-stats", "get-order-history", "get-performance-history", "get-performance-metrics", "get-pnl-report", "get-portfolio-optimization", "get-portfolio-performance", "get-portfolio-risk-metrics", "get-portfolio-summary", "get-price-history", "get-real-time-status", "get-rebalancing-opportunities", "get-risk-metrics", "get-risk-parameters", "get-scanner-status", "get-settings", "get-status-reports", "get-system-alerts", "get-system-health", "get-system-info", "get-system-metrics", "get-system-status", "get-tracked-whales", "get-trade-history", "get-trading-stats", "get-wallet-balance", "get-whale-history", "get-whale-signals", "get-whale-tracking-status", "health-check", "import-settings", "initialize-trading", "on", "on-arbitrage-executed", "on-arbitrage-opportunity", "place-limit-order", "place-market-order", "rebalance-cross-exchange-portfolio", "rebalance-portfolio", "remove-exchange", "remove-whale-wallet", "report-error", "reset-ipc-error-statistics", "reset-settings", "run-health-check", "save-coin", "save-grid-preset", "save-settings", "set-log-level", "set-risk-parameters", "start-arbitrage-engine", "start-arbitrage-scanning", "start-bot", "start-dca", "start-grid", "start-health-monitoring", "start-meme-coin-scanner", "start-opportunity-scanner", "start-portfolio-monitoring", "stop-all-grids", "stop-arbitrage-engine", "stop-arbitrage-scanning", "stop-bot", "stop-dca", "stop-grid", "stop-health-monitoring", "stop-meme-coin-scanner", "stop-opportunity-scanner", "stop-portfolio-monitoring", "test-exchange-connection", "toggle-whale-tracking", "update-arbitrage-config", "update-coin", "update-config", "update-dca-config", "update-grid-config", "update-opportunity-scanner-config", "update-scanner-config"], "preloadMethodsList": ["addExchange", "addWhaleWallet", "cancelAllOrders", "cancelOrder", "clearLogs", "createBackup", "deleteCoin", "executeArbitrage", "exportLogs", "exportSettings", "getActiveBots", "getAppVersion", "getArbitrageOpportunities", "getArbitragePositions", "getArbitrageStats", "getArbitrageStatus", "getAssetAllocation", "getBotStatus", "getCoins", "getComponentHealth", "getConfig", "getCrossExchangeBalances", "getDcaHistory", "getDcaPositions", "getDetectedOpportunities", "getDrawdownAnalysis", "getExchangeBalances", "getExchangeHealth", "getExchangePortfolio", "getExchanges", "getGridHistory", "getGridPositions", "getGridPresets", "getIpcErrorStatistics", "getLogs", "getMarketData", "getMarketOverview", "getMemeCoinHistory", "getMemeCoinOpportunities", "getMonitoringStatistics", "getOpenOrders", "getOpportunityScannerStats", "getOrderHistory", "getPerformanceHistory", "getPerformanceMetrics", "getPnlReport", "getPortfolioOptimization", "getPortfolioPerformance", "getPortfolioRiskMetrics", "getPortfolioSummary", "getPriceHistory", "getRealTimeStatus", "getRebalancingOpportunities", "getRiskMetrics", "getRiskParameters", "getScannerStatus", "getSettings", "getStatusReports", "getSystemAlerts", "getSystemHealth", "getSystemInfo", "getSystemMetrics", "getSystemStatus", "getTrackedWhales", "getTradeHistory", "getTradingStats", "getWalletBalance", "getWhaleHistory", "getWhaleSignals", "getWhaleTrackingStatus", "healthCheck", "importSettings", "initializeTrading", "on", "onArbitrageExecuted", "onArbitrageOpportunity", "placeLimitOrder", "placeMarketOrder", "rebalanceCrossExchangePortfolio", "rebalancePortfolio", "removeExchange", "removeWhaleWallet", "reportError", "resetIpcErrorStatistics", "resetSettings", "runHealthCheck", "saveCoin", "saveGridPreset", "saveSettings", "setLogLevel", "setRiskParameters", "startArbitrageEngine", "startArbitrageScanning", "startBot", "startDca", "startGrid", "startHealthMonitoring", "startMemeCoinScanner", "startOpportunityScanner", "startPortfolioMonitoring", "stopAllGrids", "stopArbitrageEngine", "stopArbitrageScanning", "stopBot", "stopDca", "stopGrid", "stopHealthMonitoring", "stopMemeCoinScanner", "stopOpportunityScanner", "stopPortfolioMonitoring", "testExchangeConnection", "toggleWhaleTracking", "updateArbitrageConfig", "updateCoin", "updateConfig", "updateDcaConfig", "updateGridConfig", "updateOpportunityScannerConfig", "updateScannerConfig"]}, "handlerValidation": {"missingHandlers": [], "extraHandlers": [], "matchingPairs": ["add-exchange", "add-whale-wallet", "cancel-all-orders", "cancel-order", "clear-logs", "create-backup", "delete-coin", "execute-arbitrage", "export-logs", "export-settings", "get-active-bots", "get-app-version", "get-arbitrage-opportunities", "get-arbitrage-positions", "get-arbitrage-stats", "get-arbitrage-status", "get-asset-allocation", "get-bot-status", "get-coins", "get-component-health", "get-config", "get-cross-exchange-balances", "get-dca-history", "get-dca-positions", "get-detected-opportunities", "get-drawdown-analysis", "get-exchange-balances", "get-exchange-health", "get-exchange-portfolio", "get-exchanges", "get-grid-history", "get-grid-positions", "get-grid-presets", "get-ipc-error-statistics", "get-logs", "get-market-data", "get-market-overview", "get-meme-coin-history", "get-meme-coin-opportunities", "get-monitoring-statistics", "get-open-orders", "get-opportunity-scanner-stats", "get-order-history", "get-performance-history", "get-performance-metrics", "get-pnl-report", "get-portfolio-optimization", "get-portfolio-performance", "get-portfolio-risk-metrics", "get-portfolio-summary", "get-price-history", "get-real-time-status", "get-rebalancing-opportunities", "get-risk-metrics", "get-risk-parameters", "get-scanner-status", "get-settings", "get-status-reports", "get-system-alerts", "get-system-health", "get-system-info", "get-system-metrics", "get-system-status", "get-tracked-whales", "get-trade-history", "get-trading-stats", "get-wallet-balance", "get-whale-history", "get-whale-signals", "get-whale-tracking-status", "health-check", "import-settings", "initialize-trading", "on", "on-arbitrage-executed", "on-arbitrage-opportunity", "place-limit-order", "place-market-order", "rebalance-cross-exchange-portfolio", "rebalance-portfolio", "remove-exchange", "remove-whale-wallet", "report-error", "reset-ipc-error-statistics", "reset-settings", "run-health-check", "save-coin", "save-grid-preset", "save-settings", "set-log-level", "set-risk-parameters", "start-arbitrage-engine", "start-arbitrage-scanning", "start-bot", "start-dca", "start-grid", "start-health-monitoring", "start-meme-coin-scanner", "start-opportunity-scanner", "start-portfolio-monitoring", "stop-all-grids", "stop-arbitrage-engine", "stop-arbitrage-scanning", "stop-bot", "stop-dca", "stop-grid", "stop-health-monitoring", "stop-meme-coin-scanner", "stop-opportunity-scanner", "stop-portfolio-monitoring", "test-exchange-connection", "toggle-whale-tracking", "update-arbitrage-config", "update-coin", "update-config", "update-dca-config", "update-grid-config", "update-opportunity-scanner-config", "update-scanner-config"], "missingCount": 0, "extraCount": 0, "matchingCount": 119}, "preloadValidation": {"hasContextBridge": true, "hasIpcRenderer": true, "hasSafeInvoke": true, "hasElectronAPI": true, "hasErrorHandling": true, "allChecksPass": true}, "consistency": {"criticalChannels": ["health-check", "get-bot-status", "start-bot", "stop-bot", "get-portfolio-summary", "get-trading-stats", "get-settings", "save-settings", "get-coins", "get-market-data"], "missingCritical": [], "presentCritical": ["health-check", "get-bot-status", "start-bot", "stop-bot", "get-portfolio-summary", "get-trading-stats", "get-settings", "save-settings", "get-coins", "get-market-data"], "criticalChannelsCount": 10, "missingCriticalCount": 0, "presentCriticalCount": 10, "allCriticalPresent": true}, "summary": {"totalChecks": 4, "passedChecks": 4, "failedChecks": 0, "successRate": 100, "overallSuccess": true, "checks": {"channelValidation": true, "handlerConsistency": true, "preloadStructure": true, "criticalChannels": true}}}
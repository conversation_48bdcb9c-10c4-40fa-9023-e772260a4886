'use strict';

const {
  app,
  BrowserWindow,
} = require('electron');
const path = require('path');
const logger = require('./trading/shared/helpers/logger');

/**
 * Creates a new BrowserWindow instance for the Electron app.
 * The window is configured with specific properties, including dimensions
 * and web preferences for enhanced security.
 * Loads the app from the build directory and opens developer tools for debugging.
 * Handles events to log loading success or failure.
 */

function createWindow() {
  const win = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: true,
      contextIsolation: false,
    },
  });

  // Load the built app
  win.loadFile(path.join(__dirname, '..', 'build', 'index.html'));
  win.webContents.openDevTools();
  win.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    logger.error('Failed to load:', errorDescription);
  });
  win.webContents.on('did-finish-load', () => {
    logger.info('App loaded successfully!');
  });
}

app.whenReady().then(createWindow);
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>IPC End-to-End Testing</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }

        .test-section h2 {
            margin-top: 0;
            color: #555;
        }

        .test-result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 4px;
            font-family: monospace;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }

        button:hover {
            background: #0056b3;
        }

        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
            font-size: 12px;
        }

        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-bar {
            height: 100%;
            background: #28a745;
            transition: width 0.3s ease;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>🔧 IPC End-to-End Communication Testing</h1>

    <div class="test-section">
        <h2>Test Controls</h2>
        <button id="runAllTests" onclick="runAllTests()">Run All Tests</button>
        <button id="runChannelTest" onclick="runChannelTest()">Test Channel Establishment</button>
        <button id="runDataFlowTest" onclick="runDataFlowTest()">Test Data Flow</button>
        <button id="runErrorTest" onclick="runErrorTest()">Test Error Handling</button>
        <button id="runPerformanceTest" onclick="runPerformanceTest()">Test Performance</button>
        <button id="clearResults" onclick="clearResults()">Clear Results</button>
    </div>

    <div class="test-section">
        <h2>Test Progress</h2>
        <div class="progress" id="testProgress" style="display: none;">
            <div class="progress-bar" id="progressBar" style="width: 0%;"></div>
        </div>
        <div class="info" id="currentTest" style="display: none;">
            Initializing tests...
        </div>
    </div>

    <div class="test-section">
        <h2>Test Statistics</h2>
        <div class="stats" id="testStats">
            <div class="stat-card">
                <div class="stat-number" id="totalTests">0</div>
                <div class="stat-label">Total Tests</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="passedTests">0</div>
                <div class="stat-label">Passed</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failedTests">0</div>
                <div class="stat-label">Failed</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successRate">0%</div>
                <div class="stat-label">Success Rate</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>Detailed Output</h2>
        <pre id="detailedOutput"></pre>
    </div>
</div>

<script>
    // Global test state
    let testResults = [];
    let isTestRunning = false;

    // Check if electronAPI is available
    function checkElectronAPI() {
        if (typeof window === 'undefined' || !window.electronAPI) {
            addResult('error', 'electronAPI not available - not running in Electron environment');
            return false;
        }
        addResult('success', 'electronAPI is available');
        return true;
    }

    // Add result to display
    function addResult(type, message, details = null) {
        const resultsDiv = document.getElementById('testResults');
        const resultDiv = document.createElement('div');
        resultDiv.className = `test-result ${type}`;

        let content = message;
        if (details) {
            content += `\n${JSON.stringify(details, null, 2)}`;
        }

        resultDiv.textContent = content;
        resultsDiv.appendChild(resultDiv);
        resultsDiv.scrollTop = resultsDiv.scrollHeight;
    }

    // Update progress
    function updateProgress(current, total, message) {
        const progressDiv = document.getElementById('testProgress');
        const progressBar = document.getElementById('progressBar');
        const currentTestDiv = document.getElementById('currentTest');

        progressDiv.style.display = 'block';
        currentTestDiv.style.display = 'block';

        const percentage = Math.round((current / total) * 100);
        progressBar.style.width = `${percentage}%`;
        currentTestDiv.textContent = message;
    }

    // Update statistics
    function updateStats(total, passed, failed) {
        document.getElementById('totalTests').textContent = total;
        document.getElementById('passedTests').textContent = passed;
        document.getElementById('failedTests').textContent = failed;

        const successRate = total > 0 ? Math.round((passed / total) * 100) : 0;
        document.getElementById('successRate').textContent = `${successRate}%`;
    }

    // Clear results
    function clearResults() {
        document.getElementById('testResults').innerHTML = '';
        document.getElementById('detailedOutput').textContent = '';
        document.getElementById('testProgress').style.display = 'none';
        document.getElementById('currentTest').style.display = 'none';
        testResults = [];
        updateStats(0, 0, 0);
    }

    // Test channel establishment
    async function runChannelTest() {
        if (!checkElectronAPI()) return;

        addResult('info', 'Testing IPC channel establishment...');

        const criticalChannels = [
            'healthCheck',
            'getBotStatus',
            'startBot',
            'stopBot',
            'getPortfolioSummary',
            'getTradingStats',
            'getSettings',
            'saveSettings',
            'getCoins',
            'getMarketData'
        ];

        let passed = 0;
        let failed = 0;

        for (let i = 0; i < criticalChannels.length; i++) {
            const method = criticalChannels[i];
            updateProgress(i + 1, criticalChannels.length, `Testing ${method}...`);

            try {
                if (window.electronAPI[method] && typeof window.electronAPI[method] === 'function') {
                    addResult('success', `✅ ${method} - Available`);
                    passed++;
                } else {
                    addResult('error', `❌ ${method} - Not available`);
                    failed++;
                }
            } catch (error) {
                addResult('error', `❌ ${method} - Error: ${error.message}`);
                failed++;
            }
        }

        updateStats(criticalChannels.length, passed, failed);
        addResult('info', `Channel test completed: ${passed}/${criticalChannels.length} channels available`);
    }

    // Test data flow
    async function runDataFlowTest() {
        if (!checkElectronAPI()) return;

        addResult('info', 'Testing data flow from UI to trading system...');

        const dataFlowTests = [
            {name: 'Health Check', method: 'healthCheck', params: null},
            {name: 'Bot Status', method: 'getBotStatus', params: null},
            {name: 'Portfolio Summary', method: 'getPortfolioSummary', params: null},
            {name: 'Trading Stats', method: 'getTradingStats', params: null},
            {name: 'Settings', method: 'getSettings', params: null},
            {name: 'Coins', method: 'getCoins', params: null}
        ];

        let passed = 0;
        let failed = 0;

        for (let i = 0; i < dataFlowTests.length; i++) {
            const test = dataFlowTests[i];
            updateProgress(i + 1, dataFlowTests.length, `Testing ${test.name}...`);

            try {
                if (!window.electronAPI[test.method]) {
                    addResult('error', `❌ ${test.name} - Method not available`);
                    failed++;
                    continue;
                }

                const startTime = performance.now();
                const result = test.params
                    ? await window.electronAPI[test.method](test.params)
                    : await window.electronAPI[test.method]();
                const duration = Math.round((performance.now() - startTime) * 100) / 100;

                if (result && typeof result === 'object' && 'success' in result) {
                    addResult('success', `✅ ${test.name} - Response received (${duration}ms)`, {
                        success: result.success,
                        hasData: !!result.data,
                        responseTime: duration
                    });
                    passed++;
                } else {
                    addResult('warning', `⚠️ ${test.name} - Unexpected response format`, result);
                    failed++;
                }
            } catch (error) {
                addResult('error', `❌ ${test.name} - Error: ${error.message}`);
                failed++;
            }
        }

        updateStats(dataFlowTests.length, passed, failed);
        addResult('info', `Data flow test completed: ${passed}/${dataFlowTests.length} tests passed`);
    }

    // Test error handling
    async function runErrorTest() {
        if (!checkElectronAPI()) return;

        addResult('info', 'Testing error handling and timeout scenarios...');

        const errorTests = [
            {
                name: 'Invalid Parameters',
                test: async () => {
                    if (window.electronAPI.getMarketData) {
                        const result = await window.electronAPI.getMarketData(null);
                        return {handled: !result.success, result};
                    }
                    return {handled: true, message: 'Method not available'};
                }
            },
            {
                name: 'Large Payload',
                test: async () => {
                    if (window.electronAPI.saveSettings) {
                        const largePayload = {
                            testData: 'x'.repeat(1000),
                            nestedData: Array(100).fill(0).map((_, i) => ({id: i, value: Math.random()}))
                        };
                        const result = await window.electronAPI.saveSettings(largePayload);
                        return {handled: true, result, payloadSize: JSON.stringify(largePayload).length};
                    }
                    return {handled: true, message: 'Method not available'};
                }
            },
            {
                name: 'Timeout Test',
                test: async () => {
                    if (window.electronAPI.healthCheck) {
                        const timeout = new Promise((_, reject) =>
                            setTimeout(() => reject(new Error('Timeout')), 5000)
                        );

                        try {
                            const result = await Promise.race([
                                window.electronAPI.healthCheck(),
                                timeout
                            ]);
                            return {handled: true, result};
                        } catch (error) {
                            return {handled: error.message === 'Timeout', error: error.message};
                        }
                    }
                    return {handled: true, message: 'Method not available'};
                }
            }
        ];

        let passed = 0;
        let failed = 0;

        for (let i = 0; i < errorTests.length; i++) {
            const test = errorTests[i];
            updateProgress(i + 1, errorTests.length, `Testing ${test.name}...`);

            try {
                const result = await test.test();
                if (result.handled) {
                    addResult('success', `✅ ${test.name} - Error handled correctly`, result);
                    passed++;
                } else {
                    addResult('error', `❌ ${test.name} - Error not handled`, result);
                    failed++;
                }
            } catch (error) {
                addResult('success', `✅ ${test.name} - Exception caught (good error handling)`, {
                    error: error.message
                });
                passed++;
            }
        }

        updateStats(errorTests.length, passed, failed);
        addResult('info', `Error handling test completed: ${passed}/${errorTests.length} tests passed`);
    }

    // Test performance
    async function runPerformanceTest() {
        if (!checkElectronAPI()) return;

        addResult('info', 'Testing performance and concurrent requests...');

        // Sequential performance test
        if (window.electronAPI.healthCheck) {
            updateProgress(1, 3, 'Testing sequential performance...');

            const iterations = 5;
            const times = [];

            for (let i = 0; i < iterations; i++) {
                const start = performance.now();
                await window.electronAPI.healthCheck();
                const duration = performance.now() - start;
                times.push(duration);
            }

            const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
            const minTime = Math.min(...times);
            const maxTime = Math.max(...times);

            addResult('success', `✅ Sequential Performance Test`, {
                iterations,
                averageTime: Math.round(avgTime * 100) / 100,
                minTime: Math.round(minTime * 100) / 100,
                maxTime: Math.round(maxTime * 100) / 100
            });

            // Concurrent performance test
            updateProgress(2, 3, 'Testing concurrent requests...');

            const concurrentRequests = 3;
            const promises = Array(concurrentRequests).fill(0).map(() => {
                const start = performance.now();
                return window.electronAPI.healthCheck().then(result => ({
                    result,
                    duration: performance.now() - start
                }));
            });

            const results = await Promise.all(promises);
            const allSuccessful = results.every(r => r.result.success);
            const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;

            addResult('success', `✅ Concurrent Requests Test`, {
                requestCount: concurrentRequests,
                allSuccessful,
                averageDuration: Math.round(avgDuration * 100) / 100
            });

            updateStats(2, 2, 0);
        } else {
            addResult('error', '❌ Performance test skipped - healthCheck method not available');
            updateStats(1, 0, 1);
        }

        updateProgress(3, 3, 'Performance tests completed');
        addResult('info', 'Performance test completed');
    }

    // Run all tests
    async function runAllTests() {
        if (isTestRunning) {
            addResult('warning', 'Tests are already running...');
            return;
        }

        isTestRunning = true;
        clearResults();

        // Disable buttons
        const buttons = document.querySelectorAll('button');
        buttons.forEach(btn => btn.disabled = true);

        try {
            addResult('info', '🚀 Starting comprehensive IPC end-to-end tests...');

            await runChannelTest();
            await new Promise(resolve => setTimeout(resolve, 1000)); // Brief pause

            await runDataFlowTest();
            await new Promise(resolve => setTimeout(resolve, 1000));

            await runErrorTest();
            await new Promise(resolve => setTimeout(resolve, 1000));

            await runPerformanceTest();

            addResult('success', '🎉 All tests completed!');

            // Generate summary
            const totalTests = testResults.length;
            const passedTests = document.getElementById('passedTests').textContent;
            const failedTests = document.getElementById('failedTests').textContent;
            const successRate = document.getElementById('successRate').textContent;

            const summary = {
                timestamp: new Date().toISOString(),
                totalTests,
                passedTests: parseInt(passedTests),
                failedTests: parseInt(failedTests),
                successRate,
                environment: {
                    userAgent: navigator.userAgent,
                    electronAPI: !!window.electronAPI
                }
            };

            document.getElementById('detailedOutput').textContent = JSON.stringify(summary, null, 2);

        } catch (error) {
            addResult('error', `Test execution failed: ${error.message}`);
        } finally {
            isTestRunning = false;
            // Re-enable buttons
            buttons.forEach(btn => btn.disabled = false);

            document.getElementById('testProgress').style.display = 'none';
            document.getElementById('currentTest').style.display = 'none';
        }
    }

    // Initialize page
    document.addEventListener('DOMContentLoaded', () => {
        if (checkElectronAPI()) {
            addResult('info', '🔧 IPC End-to-End Testing Ready');
            addResult('info', 'Click "Run All Tests" to start comprehensive testing');
        } else {
            addResult('error', '❌ This page must be run in an Electron environment');
        }
    });
</script>
</body>
</html>
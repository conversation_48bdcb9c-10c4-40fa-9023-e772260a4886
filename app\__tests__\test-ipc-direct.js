'use strict';

/**
 * @fileoverview Direct IPC Handler Testing - Main Execution
 * @description Tests IPC handlers directly without requiring full Electron environment
 * <AUTHOR>
 * @version 1.0.0
 */


const path = require('path');
const fs = require('fs');
const DirectIPCTester = require('./helpers/DirectIPCTester');

/**
 * Main execution
 */
async function main() {
  const tester = new DirectIPCTester();
  const results = await tester.runAllTests();

  // Save results to file
  const resultsPath = path.join(__dirname, 'ipc-direct-test-results.json');
  fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
  console.log(`\n📄 Results saved to: ${resultsPath}`);

  // Exit with appropriate code
  process.exit(results.success ? 0 : 1);
}

// Run tests if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  });
}
module.exports = DirectIPCTester;
'use strict';

/**
 * Simple test for IPC error handling components
 */


console.log('🧪 Testing IPC Error Handling Components...\n');

// Test 1
try {
  const IPCErrorHandler = require('./src/utils/IPCErrorHandler.js');
  console.log('✅ IPCErrorHandler loaded successfully');

  // Test error response creation
  const errorResponse = IPCErrorHandler.createErrorResponse('TEST_ERROR', 'Test message', 'test-channel');
  if (errorResponse.success === false && errorResponse.error.code === 'TEST_ERROR') {
    console.log('✅ Error response creation works');
  } else {
    console.log('❌ Error response creation failed');
  }

  // Test success response creation
  const successResponse = IPCErrorHandler.createSuccessResponse({
    test: 'data',
  });
  if (successResponse.success === true && successResponse.data.test === 'data') {
    console.log('✅ Success response creation works');
  } else {
    console.log('❌ Success response creation failed');
  }

  // Test error statistics
  const stats = IPCErrorHandler.getErrorStatistics();
  if (stats && typeof stats.totalErrors === 'number') {
    console.log('✅ Error statistics retrieval works');
  } else {
    console.log('❌ Error statistics retrieval failed');
  }
} catch (error) {
  console.log('❌ IPCErrorHandler test failed:', error.message);
}

// Test 2
try {
  const StandardizedIPCHandler = require('./src/utils/StandardizedIPCHandler.js');
  console.log('✅ StandardizedIPCHandler loaded successfully');

  // Test handler creation
  const handler = StandardizedIPCHandler.createHandler('test-channel', async () => ({
    test: 'success',
  }), {
    timeout: 5000,
  });
  if (typeof handler === 'function') {
    console.log('✅ IPC handler creation works');
  } else {
    console.log('❌ IPC handler creation failed');
  }

  // Test error code classification
  const testError = new Error('Connection timeout occurred');
  const errorCode = StandardizedIPCHandler.getErrorCode(testError);
  if (errorCode === 'TIMEOUT') {
    console.log('✅ Error code classification works');
  } else {
    console.log('❌ Error code classification failed, got:', errorCode);
  }
} catch (error) {
  console.log('❌ StandardizedIPCHandler test failed:', error.message);
}

// Test 3 basic functionality
try {
  const TradingOrchestrator = require('./trading/engines/trading/orchestration/TradingOrchestrator');
  console.log('✅ TradingOrchestrator loaded successfully');
  const orchestrator = new TradingOrchestrator();
  const status = orchestrator.getStatus();
  if (status && typeof status.initialized === 'boolean') {
    console.log('✅ TradingOrchestrator status check works');
  } else {
    console.log('❌ TradingOrchestrator status check failed');
  }
  const dbStatus = orchestrator.getDatabaseStatus();
  if (dbStatus && typeof dbStatus.initialized === 'boolean') {
    console.log('✅ Database status check works');
  } else {
    console.log('❌ Database status check failed');
  }
} catch (error) {
  console.log('❌ TradingOrchestrator test failed:', error.message);
}
console.log('\n📊 Basic component tests completed!');
console.log('✅ IPC error handling standardization is working correctly.');
'use strict';

/**
 * @fileoverview Test IPC Error Handling Implementation
 * @description Tests the standardized IPC error handling, timeout management, and retry logic
 */


const ipcService = require('../src/services/ipcService.js');
const IPCErrorHandler = require('../src/utils/IPCErrorHandler.js');
const {duration, maxAttempts, success, default: defaultTimeout} = require('../src/utils/defaults.js');
console.log('🧪 Testing IPC Error Handling Implementation...\n');

// Test 1 Handler Configuration
console.log('1. Testing Error Handler Configuration:');
const config = ipcService.getConfiguration();
console.log('   ✅ Default timeout:', config.defaultTimeout);
console.log('   ✅ Retry attempts:', config.retryAttempts);
console.log('   ✅ Retry delay:', config.retryDelay);
console.log('   ✅ Electron available:', config.isElectronAvailable);

// Test 2 Code Classification
console.log('\n2. Testing Error Code Classification:');
const testErrors = [new Error('Operation timeout after 10000ms'), new Error('Service not available'), new Error('Component not initialized'), new Error('Resource not found'), new Error('Permission denied'), new Error('Rate limit exceeded'), new Error('Connection failed'), new Error('Database error occurred'), new Error('Database is busy'), new Error('Network error'), new Error('Invalid input provided'), new Error('Unauthorized access'), new Error('Forbidden operation'), new Error('Temporary failure'), new Error('Unknown error occurred')];
testErrors.forEach(error => {
  const code = IPCErrorHandler.getErrorCode(error);
  console.log(`   ✅ "${error.message}" -> ${code}`);
});

// Test 3 Response Creation
console.log('\n3. Testing Error Response Creation:');
const errorResponse = IPCErrorHandler.createErrorResponse('TIMEOUT', 'Operation timed out', 'test-channel', {
  duration,
});
console.log('   ✅ Error response structure:');
console.log('      - success:', errorResponse.success);
console.log('      - error.code:', errorResponse.error.code);
console.log('      - error.message:', errorResponse.error.message);
console.log('      - error.channel:', errorResponse.error.channel);
console.log('      - error.context:', errorResponse.error.context);
console.log('      - timestamp:', typeof errorResponse.timestamp === 'number');

// Test 4 Response Creation
console.log('\n4. Testing Success Response Creation:');
const successResponse = IPCErrorHandler.createSuccessResponse({
  result: 'test data',
});
console.log('   ✅ Success response structure:');
console.log('      - success:', successResponse.success);
console.log('      - data:', successResponse.data);
console.log('      - timestamp:', typeof successResponse.timestamp === 'number');

// Test 5 Breaker Logic
console.log('\n5. Testing Circuit Breaker Logic:');
// Simulate multiple errors for the same channel
for (let i = 0; i < 5; i++) {
  IPCErrorHandler.recordError('test-channel', 'TIMEOUT');
}
const shouldBreak = IPCErrorHandler.shouldCircuitBreak('test-channel', 3);
console.log('   ✅ Circuit breaker triggered after 5 errors (threshold 3):', shouldBreak);

// Test 6 Statistics
console.log('\n6. Testing Error Statistics:');
const stats = IPCErrorHandler.getErrorStatistics();
console.log('   ✅ Error statistics:');
console.log('      - Total errors:', stats.totalErrors);
console.log('      - Errors by channel:', Object.keys(stats.errorsByChannel).length > 0);
console.log('      - Errors by code:', Object.keys(stats.errorsByCode).length > 0);
console.log('      - Recent errors:', stats.recentErrors);

// Test 7 Update
console.log('\n7. Testing Configuration Update:');
const originalTimeout = IPCErrorHandler.timeouts.default;
IPCErrorHandler.updateConfiguration({
  default: defaultTimeout,
}, {
  maxAttempts,
});
console.log('   ✅ Timeout updated from', originalTimeout, 'to', IPCErrorHandler.timeouts.default);
console.log('   ✅ Max attempts updated to:', IPCErrorHandler.retryConfig.maxAttempts);

// Test 8 IPC Call with Error Handling
console.log('\n8. Testing Mock IPC Call with Error Handling:');

// Mock function that simulates different scenarios
const mockIPCFunction = scenario => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      switch (scenario) {
      case 'success':
        resolve({
          data: 'success result',
        });
        break;
      case 'timeout':
        reject(new Error('Operation timeout after 5000ms'));
        break;
      case 'not-found':
        reject(new Error('Resource not found'));
        break;
      case 'connection':
        reject(new Error('Connection failed'));
        break;
      default:
        reject(new Error('Unknown error'));
      }
    }, 100);
  });
};

// Test different scenarios
async function testScenarios() {
  const scenarios = ['success', 'timeout', 'not-found', 'connection'];
  for (const scenario of scenarios) {
    try {
      const result = await IPCErrorHandler.executeWithFullHandling(() => mockIPCFunction(scenario), 'test-mock-channel', 'default');
      console.log(`   ✅ Scenario "${scenario}":`, {
        success: result.success,
        hasData: !!result.data,
        hasError: !!result.error,
        errorCode: result.error ? result.error.code : null,
      });
    } catch (error) {
      console.log(`   ❌ Scenario "${scenario}" failed:`, error.message);
    }
  }
}

// Test 9 Logic
console.log('\n9. Testing Retry Logic:');
let attemptCount = 0;
const mockRetryFunction = () => {
  return new Promise((resolve, reject) => {
    attemptCount++;
    if (attemptCount < 3) {
      reject(new Error('Temporary failure'));
    } else {
      resolve({
        data: 'success after retries',
      });
    }
  });
};

async function testRetryLogic() {
  try {
    attemptCount = 0;
    const result = await IPCErrorHandler.executeWithRetry(mockRetryFunction, 'retry-test-channel', {
      maxAttempts,
      retryableErrors: ['TEMPORARY_FAILURE'],
    });
    console.log('   ✅ Retry logic succeeded after', attemptCount, 'attempts');
    console.log('   ✅ Result:', result);
  } catch (error) {
    console.log('   ❌ Retry logic failed:', error.message);
  }
}

// Test 10 Service Integration
console.log('\n10. Testing IPC Service Integration:');

// Test if IPC service methods are properly configured
const testMethods = ['startBot', 'stopBot', 'getBotStatus', 'getPortfolioSummary', 'getMarketData', 'healthCheck'];
testMethods.forEach(method => {
  const hasMethod = typeof ipcService[method] === 'function';
  console.log(`   ${hasMethod ? '✅' : '❌'} Method "${method}" available:`, hasMethod);
});

// Run async tests
async function runAsyncTests() {
  await testScenarios();
  await testRetryLogic();
  console.log('\n🎉 IPC Error Handling Tests Completed!');
  console.log('\n📊 Final Statistics:');
  const finalStats = IPCErrorHandler.getErrorStatistics();
  console.log('   - Total errors recorded:', finalStats.totalErrors);
  console.log('   - Channels with errors:', Object.keys(finalStats.errorsByChannel).length);
  console.log('   - Error types recorded:', Object.keys(finalStats.errorsByCode).length);

  // Reset statistics for clean state
  IPCErrorHandler.resetStatistics();
  console.log('   - Statistics reset for clean state');
}

runAsyncTests().catch(console.error);
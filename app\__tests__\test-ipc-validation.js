#!/usr/bin/env node
/* eslint-disable no-console */

/**
 * @fileoverview IPC Communication Validation Script
 * @description Validates IPC channel definitions and handler registrations
 * <AUTHOR>
 * @version 1.0.0
 */

const fs = require('fs');
const path = require('path');

/**
 * IPC Communication Validator
 * Validates IPC channels, handlers, and preload script consistency
 */
class IPCValidator {
    constructor() {
        // this.mainJsPath = path.join(__dirname, '../main.js');
        // this.preloadJsPath = path.join(__dirname, '../preload.js');
        // this.results = {
            channelValidation: {},
            handlerValidation: {},
            preloadValidation: {},
            consistency: {},
            summary: {},
        };
    }

    /**
     * Extract IPC handlers from main.js
     */
    extractMainHandlers() {
        try {
            const mainContent = fs.readFileSync(this.mainJsPath, 'utf8');

            // Extract handleIPC calls
            const handleIPCRegex = /handleIPC\(['"`]([^'"`]+)['"`]/g;
            const handlers = [];
            let match;

            while ((match = handleIPCRegex.exec(mainContent)) !== null) {
                handlers.push(match[1]);
            }

            // Extract direct ipcMain.handle calls
            const ipcMainRegex = /ipcMain\.handle\(['"`]([^'"`]+)['"`]/g;
            while ((match = ipcMainRegex.exec(mainContent)) !== null) {
                handlers.push(match[1]);
            }

            return [...new Set(handlers)].sort();
        } catch (error) {
            throw new Error(`Failed to read main.js: ${error.message}`);
        }
    }

    /**
     * Extract exposed methods from preload.js
     */
    extractPreloadMethods() {
        try {
            const preloadContent = fs.readFileSync(this.preloadJsPath, 'utf8');

            // Isolate the electronAPI object to avoid matching other parts of the script
            const apiObjectRegex = /contextBridge\.exposeInMainWorld\('electronAPI',\s*({[\s\S]*?})\);/s;
            const apiObjectMatch = preloadContent.match(apiObjectRegex);

            if (!apiObjectMatch || !apiObjectMatch[1]) {
                throw new Error('Could not find or parse the electronAPI object in preload.js');
            }

            const apiObjectContent = apiObjectMatch[1];

            // Extract method definitions from the isolated electronAPI object
            const methodRegex = /(\w+):\s*\([\s\S]*?\)\s*=>/g;
            const methods = [];
            let match;

            while ((match = methodRegex.exec(apiObjectContent)) !== null) {
                methods.push(match[1]);
            }

            return [...new Set(methods)].sort();
        } catch (error) {
            throw new Error(`Failed to extract preload methods: ${error.message}`);
        }
    }

    /**
     * Convert method name to channel name (camelCase to kebab-case)
     */
    methodToChannelName(method) {
        // Handles acronyms correctly (e.g., 'getIPCData' -> 'get-ipc-data')
        return method
            .replace(/([a-z0-9])([A-Z])/g, '$1-$2')
            .replace(/([A-Z])([A-Z][a-z])/g, '$1-$2')
            .toLowerCase();
    }

    /**
     * Convert channel name to method name (kebab-case to camelCase)
     */
    channelToMethodName(channel) {
        return channel
            .split('-')
            .map((word, index) => {
                if (index === 0) {
                    return word;
                }
                return word.charAt(0).toUpperCase() + word.slice(1);
            })
            .join('');
    }

    /**
     * Validate channel definitions
     */
    validateChannels() {
        console.log('🔍 Validating IPC channel definitions...');

        try {
            const mainHandlers = this.extractMainHandlers();
            const preloadMethods = this.extractPreloadMethods();

            // this.results.channelValidation = {
                mainHandlers,
                preloadMethods,
                mainHandlersList: mainHandlers,
                preloadMethodsList: preloadMethods,
            };

            console.log(`✅ Found ${mainHandlers.length} handlers in main.js`);
            console.log(`✅ Found ${preloadMethods.length} methods in preload.js`);

            return true;
        } catch (error) {
            console.error(`❌ Channel validation failed: ${error.message}`);
            // this.results.channelValidation.error = error.message;
            return false;
        }
    }

    /**
     * Validate handler consistency
     */
    validateHandlerConsistency() {
        console.log('🔄 Validating handler consistency...');

        try {
            const mainHandlers = this.results.channelValidation.mainHandlersList || [];
            const preloadMethods = this.results.channelValidation.preloadMethodsList || [];

            // Convert preload methods to expected channel names
            const expectedChannels = preloadMethods.map(method => this.methodToChannelName(method));

            // Find missing handlers (channels that should exist but don't)
            const missingHandlers = expectedChannels.filter(channel => !mainHandlers.includes(channel));

            // Find extra handlers (handlers that exist but no corresponding method)
            const extraHandlers = mainHandlers.filter(handler => {
                const expectedMethod = this.channelToMethodName(handler);
                return !preloadMethods.includes(expectedMethod);
            });

            // Find matching pairs
            const matchingPairs = mainHandlers.filter(handler => {
                const expectedMethod = this.channelToMethodName(handler);
                return preloadMethods.includes(expectedMethod);
            });

            // this.results.handlerValidation = {
                missingHandlers,
                extraHandlers,
                matchingPairs,
                missingCount: missingHandlers.length,
                extraCount: extraHandlers.length,
                matchingCount: matchingPairs.length,
            };

            if (missingHandlers.length > 0) {
                console.log(`⚠️  Found ${missingHandlers.length} missing handlers:`);
                missingHandlers.forEach(handler => console.log(`   - ${handler}`));
            }

            if (extraHandlers.length > 0) {
                console.log(`⚠️  Found ${extraHandlers.length} extra handlers:`);
                extraHandlers.forEach(handler => console.log(`   - ${handler}`));
            }

            console.log(`✅ Found ${matchingPairs.length} matching handler-method pairs`);

            return missingHandlers.length === 0 && extraHandlers.length === 0;
        } catch (error) {
            console.error(`❌ Handler consistency validation failed: ${error.message}`);
            // this.results.handlerValidation.error = error.message;
            return false;
        }
    }

    /**
     * Validate preload script structure
     */
    validatePreloadStructure() {
        console.log('🔧 Validating preload script structure...');

        try {
            const preloadContent = fs.readFileSync(this.preloadJsPath, 'utf8');

            // Check for required imports
            const hasContextBridge = preloadContent.includes('contextBridge');
            const hasIpcRenderer = preloadContent.includes('ipcRenderer');

            // Check for safeInvoke function
            const hasSafeInvoke = preloadContent.includes('safeInvoke');

            // Check for electronAPI exposure
            const hasElectronAPI = preloadContent.includes('exposeInMainWorld') &&
                preloadContent.includes('electronAPI');

            // Check for proper error handling
            const hasErrorHandling = preloadContent.includes('try') &&
                preloadContent.includes('catch');

            const allChecksPass = hasContextBridge && hasIpcRenderer && hasSafeInvoke &&
                hasElectronAPI && hasErrorHandling;

            // this.results.preloadValidation = {
                hasContextBridge,
                hasIpcRenderer,
                hasSafeInvoke,
                hasElectronAPI,
                hasErrorHandling,
                allChecksPass,
            };

            if (this.results.preloadValidation.allChecksPass) {
                console.log('✅ Preload script structure is valid');
            } else {
                console.log('⚠️  Preload script structure issues found:');
                if (!hasContextBridge) console.log('   - Missing contextBridge import');
                if (!hasIpcRenderer) console.log('   - Missing ipcRenderer import');
                if (!hasSafeInvoke) console.log('   - Missing safeInvoke function');
                if (!hasElectronAPI) console.log('   - Missing electronAPI exposure');
                if (!hasErrorHandling) console.log('   - Missing error handling');
            }

            return this.results.preloadValidation.allChecksPass;
        } catch (error) {
            console.error(`❌ Preload validation failed: ${error.message}`);
            // this.results.preloadValidation.error = error.message;
            return false;
        }
    }

    /**
     * Validate critical channels
     */
    validateCriticalChannels() {
        console.log('🎯 Validating critical IPC channels...');

        const criticalChannels = [
            'health-check',
            'get-bot-status',
            'start-bot',
            'stop-bot',
            'get-portfolio-summary',
            'get-trading-stats',
            'get-settings',
            'save-settings',
            'get-coins',
            'get-market-data',
        ];

        const mainHandlers = this.results.channelValidation.mainHandlersList || [];
        const missingCritical = criticalChannels.filter(channel => !mainHandlers.includes(channel));
        const presentCritical = criticalChannels.filter(channel => mainHandlers.includes(channel));

        // this.results.consistency = {
            criticalChannels,
            missingCritical,
            presentCritical,
            criticalChannelsCount: criticalChannels.length,
            missingCriticalCount: missingCritical.length,
            presentCriticalCount: presentCritical.length,
            allCriticalPresent: missingCritical.length === 0,
        };

        if (missingCritical.length > 0) {
            console.log(`❌ Missing ${missingCritical.length} critical channels:`);
            missingCritical.forEach(channel => console.log(`   - ${channel}`));
        } else {
            console.log('✅ All critical channels are present');
        }

        console.log(`✅ Found ${presentCritical.length}/${criticalChannels.length} critical channels`);

        return missingCritical.length === 0;
    }

    /**
     * Generate validation summary
     */
    generateSummary() {
        const channelValid = !this.results.channelValidation.error;
        const handlerValid = this.results.handlerValidation.missingCount === 0 &&
            // this.results.handlerValidation.extraCount === 0;
        const preloadValid = this.results.preloadValidation.allChecksPass;
        const criticalValid = this.results.consistency.allCriticalPresent;

        const totalChecks = 4;
        const passedChecks = [channelValid, handlerValid, preloadValid, criticalValid]
            .filter(Boolean).length;

        // this.results.summary = {
            totalChecks,
            passedChecks,
            failedChecks: totalChecks - passedChecks,
            successRate: (passedChecks / totalChecks) * 100,
            overallSuccess: passedChecks === totalChecks,
            checks: {
                channelValidation: channelValid,
                handlerConsistency: handlerValid,
                preloadStructure: preloadValid,
                criticalChannels: criticalValid,
            },
        };

        return this.results.summary;
    }

    /**
     * Run all validations
     */
    async runValidation() {
        console.log('🚀 Starting IPC Communication Validation...\n');

        const startTime = Date.now();

        try {
            // Run all validation steps
            // this.validateChannels();
            // this.validateHandlerConsistency();
            // this.validatePreloadStructure();
            // this.validateCriticalChannels();

            // Generate summary
            const summary = this.generateSummary();
            const duration = Date.now() - startTime;

            // Display results
            console.log('\n📊 IPC Validation Results:');
            console.log('═'.repeat(50));
            console.log(`Overall Status: ${summary.overallSuccess ? '✅ PASSED' : '❌ FAILED'}`);
            console.log(`Success Rate: ${summary.successRate}%`);
            console.log(`Checks: ${summary.passedChecks}/${summary.totalChecks} passed`);
            console.log(`Duration: ${duration}ms`);
            console.log('═'.repeat(50));

            // Detailed results
            console.log('\nDetailed Results:');
            console.log(`✅ Channel Validation: ${summary.checks.channelValidation ? 'PASSED' : 'FAILED'}`);
            console.log(`✅ Handler Consistency: ${summary.checks.handlerConsistency ? 'PASSED' : 'FAILED'}`);
            console.log(`✅ Preload Structure: ${summary.checks.preloadStructure ? 'PASSED' : 'FAILED'}`);
            console.log(`✅ Critical Channels: ${summary.checks.criticalChannels ? 'PASSED' : 'FAILED'}`);

            // Save results to file
            const resultsPath = path.join(__dirname, 'ipc-validation-results.json');
            fs.writeFileSync(resultsPath, JSON.stringify(this.results, null, 2));
            console.log(`\n📄 Detailed results saved to: ${resultsPath}`);

            return {
                success: summary.overallSuccess,
                results: this.results,
                duration,
            };

        } catch (error) {
            console.error(`❌ Validation failed: ${error.message}`);
            return {
                success: false,
                error: error.message,
                results: this.results,
            };
        }
    }
}

/**
 * Main execution
 */
async function main() {
    const validator = new IPCValidator();
    const results = await validator.runValidation();

    // Exit with appropriate code
    process.exit(results.success ? 0 : 1);
}

// Run validation if this file is executed directly
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Fatal error:', error);
        process.exit(1);
    });
}

module.exports = IPCValidator;

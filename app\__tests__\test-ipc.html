<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>IPC Communication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
        }

        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }

        button:hover {
            background-color: #0056b3;
        }

        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>IPC Communication Test</h1>
    <p>Testing communication between main and renderer processes</p>

    <div class="test-section">
        <h3>Environment Check</h3>
        <div id="env-check"></div>
        <button onclick="checkEnvironment()">Check Environment</button>
    </div>

    <div class="test-section">
        <h3>Basic IPC Tests</h3>
        <button onclick="testBasicIPC()">Test Basic IPC</button>
        <button onclick="testTradingIPC()">Test Trading IPC</button>
        <button onclick="testPerformance()">Test Performance</button>
        <button onclick="testErrorHandling()">Test Error Handling</button>
    </div>

    <div class="test-section">
        <h3>Individual Channel Tests</h3>
        <select id="channel-select">
            <option value="">Select channel...</option>
            <option value="health-check">Health Check</option>
            <option value="get-bot-status">Bot Status</option>
            <option value="get-settings">Settings</option>
            <option value="get-coins">Coins</option>
            <option value="get-portfolio-summary">Portfolio Summary</option>
            <option value="get-trading-stats">Trading Stats</option>
            <option value="get-whale-signals">Whale Signals</option>
        </select>
        <button onclick="testSelectedChannel()">Test Selected Channel</button>
    </div>

    <div class="test-section">
        <h3>Results</h3>
        <div id="results"></div>
    </div>

    <div class="test-section">
        <h3>Console Log</h3>
        <div class="log" id="log"></div>
    </div>
</div>

<script>
    // Logging utility
    function log(message) {
        const logDiv = document.getElementById('log');
        const timestamp = new Date().toLocaleTimeString();
        logDiv.textContent += `[${timestamp}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
        console.log(message);
    }

    // Results display
    function displayResult(title, result) {
        const resultsDiv = document.getElementById('results');
        const div = document.createElement('div');
        div.className = `test-result ${result.success ? 'success' : 'error'}`;
        div.innerHTML = `<strong>${title}:</strong> ${result.success ? '✅ Success' : '❌ Failed'}${result.error ? '<br>Error: ' + result.error : ''}`;
        resultsDiv.appendChild(div);
    }

    // Environment check
    async function checkEnvironment() {
        const envDiv = document.getElementById('env-check');
        const checks = [
            {name: 'Window', check: () => typeof window !== 'undefined'},
            {name: 'electronAPI', check: () => window.electronAPI !== undefined},
            {
                name: 'IPC Channels',
                check: () => window.electronAPI && typeof window.electronAPI.healthCheck === 'function'
            }
        ];

        envDiv.innerHTML = '';
        for (const check of checks) {
            const success = check.check();
            envDiv.innerHTML += `<div class="test-result ${success ? 'success' : 'error'}">${check.name}: ${success ? '✅ Available' : '❌ Not Available'}</div>`;
            log(`${check.name}: ${success ? 'Available' : 'Not Available'}`);
        }
    }

    // Basic IPC test
    async function testBasicIPC() {
        log('Starting basic IPC tests...');
        const results = [];

        const tests = [
            {name: 'health-check', method: () => window.electronAPI.healthCheck()},
            {name: 'get-bot-status', method: () => window.electronAPI.getBotStatus()},
            {name: 'get-settings', method: () => window.electronAPI.getSettings()},
            {name: 'get-coins', method: () => window.electronAPI.getCoins()}
        ];

        for (const test of tests) {
            try {
                const start = performance.now();
                const result = await test.method();
                const duration = performance.now() - start;

                results.push({
                    name: test.name,
                    success: result.success,
                    duration: Math.round(duration),
                    response: result
                });

                displayResult(test.name, {success: result.success});
                log(`${test.name}: ${result.success ? '✅' : '❌'} (${Math.round(duration)}ms)`);
            } catch (error) {
                displayResult(test.name, {success: false, error: error.message});
                log(`${test.name}: ❌ ${error.message}`);
            }
        }
    }

    // Trading IPC test
    async function testTradingIPC() {
        log('Starting trading IPC tests...');

        const tests = [
            {name: 'getPortfolioSummary', method: () => window.electronAPI.getPortfolioSummary()},
            {name: 'getTradingStats', method: () => window.electronAPI.getTradingStats()},
            {name: 'getWhaleSignals', method: () => window.electronAPI.getWhaleSignals()},
            {name: 'getArbitrageOpportunities', method: () => window.electronAPI.getArbitrageOpportunities()}
        ];

        for (const test of tests) {
            try {
                const start = performance.now();
                const result = await test.method();
                const duration = performance.now() - start;

                displayResult(test.name, {success: result.success});
                log(`${test.name}: ${result.success ? '✅' : '❌'} (${Math.round(duration)}ms)`);
            } catch (error) {
                displayResult(test.name, {success: false, error: error.message});
                log(`${test.name}: ❌ ${error.message}`);
            }
        }
    }

    // Performance test
    async function testPerformance() {
        log('Starting performance tests...');

        const iterations = 5;
        const channels = ['health-check', 'get-bot-status', 'get-settings'];

        for (const channel of channels) {
            const times = [];

            for (let i = 0; i < iterations; i++) {
                const start = performance.now();
                await window.electronAPI[channel]();
                times.push(performance.now() - start);
            }

            const avg = times.reduce((a, b) => a + b, 0) / times.length;
            const min = Math.min(...times);
            const max = Math.max(...times);

            log(`${channel}: avg=${Math.round(avg)}ms, min=${Math.round(min)}ms, max=${Math.round(max)}ms`);
        }
    }

    // Error handling test
    async function testErrorHandling() {
        log('Starting error handling tests...');

        const tests = [
            {name: 'invalidMethod', method: () => window.electronAPI.invalidMethod()},
            {name: 'invalidParams', method: () => window.electronAPI.getMarketData(null)}
        ];

        for (const test of tests) {
            try {
                const result = await test.method();
                displayResult(test.name, {success: false, error: 'Expected error but got response'});
                log(`${test.name}: ❌ Expected error but got response`);
            } catch (error) {
                displayResult(test.name, {success: true});
                log(`${test.name}: ✅ Handled error: ${error.message}`);
            }
        }
    }

    // Test selected channel
    async function testSelectedChannel() {
        const select = document.getElementById('channel-select');
        const channel = select.value;

        if (!channel) {
            alert('Please select a channel');
            return;
        }

        log(`Testing selected channel: ${channel}`);

        try {
            const methodName = channel.split('-').map((word, index) =>
                index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1)
            ).join('');

            const start = performance.now();
            const result = await window.electronAPI[methodName]();
            const duration = performance.now() - start;

            displayResult(channel, {success: result.success});
            log(`${channel}: ${result.success ? '✅' : '❌'} (${Math.round(duration)}ms)`);
        } catch (error) {
            displayResult(channel, {success: false, error: error.message});
            log(`${channel}: ❌ ${error.message}`);
        }
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
        log('IPC Test initialized');
        checkEnvironment();
    });
</script>
</body>
</html>
/**
 * Test script to verify main.js integration with updated TradingOrchestrator
 */

const TradingOrchestrator = require('../trading/TradingOrchestrator');

function testMainIntegration() {
    console.log('🧪 Testing Main.js Integration with Updated TradingOrchestrator...\n');

    try {
        // Test 1 TradingOrchestrator instance (like main.js does)
        console.log('📊 Test 1 TradingOrchestrator instance...');
        const orchestrator = new TradingOrchestrator();
        console.log('✅ TradingOrchestrator instance created successfully');

        // Test 2 initial state
        console.log('\n📊 Test 2 initial state...');
        const initialStatus = orchestrator.getStatus();
        console.log('✅ Initial Status:');
        console.log(`   - Initialized: ${initialStatus.initialized}`);
        console.log(`   - Database initialized: ${initialStatus.databaseInitialized}`);
        console.log(`   - Running: ${initialStatus.running}`);

        // Test 3 (like main.js does)
        console.log('\n📊 Test 3 TradingOrchestrator...');
        const initResult = await orchestrator.initialize();
        console.log('✅ Initialization completed:');
        console.log(`   - Success: ${initResult.success}`);
        console.log(`   - Database result: ${initResult.databaseResult.success}`);
        console.log(`   - Health check: ${initResult.healthCheck.healthy}`);

        // Test 4 post-initialization state
        console.log('\n📊 Test 4 post-initialization state...');
        const postInitStatus = orchestrator.getStatus();
        console.log('✅ Post-Init Status:');
        console.log(`   - Initialized: ${postInitStatus.initialized}`);
        console.log(`   - Database initialized: ${postInitStatus.databaseInitialized}`);
        console.log(`   - Running: ${postInitStatus.running}`);

        // Test 5 database-specific methods (new IPC handlers)
        console.log('\n📊 Test 5 database-specific methods...');

        const dbStatus = await orchestrator.getDatabaseStatus();
        console.log('✅ Database Status:');
        console.log(`   - Initialized: ${dbStatus.initialized}`);
        console.log(`   - Healthy: ${dbStatus.healthy}`);
        console.log(`   - Monitoring: ${dbStatus.monitoring}`);

        const dbMetrics = orchestrator.getDatabaseMetrics();
        console.log('✅ Database Metrics:');
        console.log(`   - Total databases: ${dbMetrics.totalDatabases}`);
        console.log(`   - Healthy databases: ${dbMetrics.healthyDatabases}`);

        const isReady = orchestrator.isDatabaseReady();
        console.log(`✅ Database ready: ${isReady}`);

        // Test 6 start/stop (like IPC handlers do)
        console.log('\n📊 Test 6 start/stop functionality...');
        const startResult = await orchestrator.start();
        console.log(`✅ Start result: ${startResult.success}`);

        const runningStatus = orchestrator.getStatus();
        console.log(`✅ Running status: ${runningStatus.running}`);

        const stopResult = await orchestrator.stop();
        console.log(`✅ Stop result: ${stopResult.success}`);

        console.log('\n🎉 All main.js integration tests completed successfully!');
        console.log('✅ The updated TradingOrchestrator is compatible with main.js');

    } catch (error) {
        console.error('\n❌ Main.js integration test failed:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Run the test
if (require.main === module) {
    testMainIntegration()
        .then(() => {
            console.log('\n✅ Main.js integration test completed successfully');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ Test failed:', error);
            process.exit(1);
        });
}

module.exports = testMainIntegration;
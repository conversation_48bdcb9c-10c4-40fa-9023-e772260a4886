'use strict';

function _toPropertyKey(arg) {
  const key = _toPrimitive(arg, 'string');
  return typeof key === 'symbol' ? key : String(key);
}

function _toPrimitive(input, hint) {
  if (typeof input !== 'object' || input === null) return input;
  const prim = input[Symbol.toPrimitive];
  if (prim !== undefined) {
    const res = prim.call(input, hint || 'default');
    if (typeof res !== 'object') return res;
    throw new TypeError('@@toPrimitive must return a primitive value.');
  }
  return (hint === 'string' ? String : Number)(input);
}

function _defineProperty(obj, key, value) {
  key = _toPropertyKey(key);
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value: value,
      enumerable: true,
      configurable: true,
      writable: true,
    });
  } else {
    obj[key] = value;
  }
  return obj;
}

function ownKeys(object, enumerableOnly) {
  const keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    let symbols = Object.getOwnPropertySymbols(object);
    if (enumerableOnly) {
      symbols = symbols.filter(function (sym) {
        return Object.getOwnPropertyDescriptor(object, sym).enumerable;
      });
    }
    keys.push.apply(keys, symbols);
  }
  return keys;
}

function _objectSpread(target) {
  for (let i = 1; i < arguments.length; i++) {
    const source = arguments[i] != null ? arguments[i] : {};
    if (i % 2) {
      ownKeys(Object(source), true).forEach(function (key) {
        _defineProperty(target, key, source[key]);
      });
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
      ownKeys(Object(source)).forEach(function (key) {
        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
      });
    }
  }
  return target;
}

module.exports = function override(config, _env) {
  // Add fallback for Node.js modules
  config.resolve.fallback = _objectSpread(_objectSpread({}, config.resolve.fallback), {}, {
    'path': require.resolve('path-browserify'),
    'os': require.resolve('os-browserify/browser'),
    'crypto': require.resolve('crypto-browserify'),
    'stream': require.resolve('stream-browserify'),
    'buffer': require.resolve('buffer/'),
    'util': require.resolve('util/'),
    'url': require.resolve('url/'),
    'querystring': require.resolve('querystring-es3'),
    'http': require.resolve('stream-http'),
    'https': require.resolve('https-browserify'),
    'fs': false,
    'net': false,
    'tls': false,
    'child_process': false,
  });

  // The original module rule was corrupted and has been removed as its
  // intent was unclear and likely incorrect. The primary goal of providing
  // Node.js polyfills is accomplished by the `resolve.fallback` configuration above.

  return config;
};
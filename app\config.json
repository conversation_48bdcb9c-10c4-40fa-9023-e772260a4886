{"name": "Meme Coin Trader", "version": "1.0.0", "environment": "production", "trading": {"defaultRiskPercent": 2, "maxOpenPositions": 10, "enableWhaleTracking": true, "enableMemeCoinScanning": true, "enableSentimentAnalysis": true}, "exchanges": {"default": "binance", "supportedExchanges": ["binance", "coinbase", "kraken", "bitfinex"]}, "database": {"type": "sqlite", "path": "./trading/databases/trading_bot.db"}, "logging": {"level": "info", "enableFileLogging": true, "logDirectory": "./logs"}, "api": {"timeout": 30000, "retryAttempts": 3, "retryDelay": 1000}}
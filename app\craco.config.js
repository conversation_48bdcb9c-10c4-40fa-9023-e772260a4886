const _path = require('path');

module.exports = {
  webpack: {
    configure: (webpackConfig) => {
      // Add fallback for Node.js modules
      webpackConfig.resolve.fallback = {
        ...webpackConfig.resolve.fallback,
        'path': require.resolve('path-browserify'),
        'os': require.resolve('os-browserify/browser'),
        'crypto': require.resolve('crypto-browserify'),
        'stream': require.resolve('stream-browserify'),
        'buffer': require.resolve('buffer/'),
        'util': require.resolve('util/'),
        'url': require.resolve('url/'),
        'querystring': require.resolve('querystring-es3'),
        'http': require.resolve('stream-http'),
        'https': require.resolve('https-browserify'),
        'fs': false,
        'net': false,
        'tls': false,
        'child_process': false,
      };

      // Add module rules for Node.js modules
      webpackConfig.module.rules.push({
        test: /\.m?js/,
        resolve: {
          fallback: {
            'util': require.resolve('util/'),
            'buffer': require.resolve('buffer/'),
          },
        },
      });

      // Fix ESLint webpack plugin configuration
      const eslintPluginIndex = webpackConfig.plugins.findIndex(
        (plugin) => plugin.constructor.name === 'ESLintWebpackPlugin',
      );

      if (eslintPluginIndex !== -1) {
        const ESLintPlugin = webpackConfig.plugins[eslintPluginIndex].constructor;
        webpackConfig.plugins[eslintPluginIndex] = new ESLintPlugin({
          extensions: ['js', 'jsx', 'ts', 'tsx'],
          exclude: ['node_modules', 'build', 'coverage'],
          emitWarning: true,
          failOnError: true,
          failOnWarning: false,
          quiet: false,
        });
      }

      return webpackConfig;
    },
  },
  eslint: {
    enable: true,
    mode: 'file',
    configure: (eslintConfig) => {
      // Customize ESLint configuration
      eslintConfig.rules = {
        ...eslintConfig.rules,
        'no-unused-vars': 'warn',
        'no-console': 'warn',
        'no-var': 'warn',
        'prefer-const': 'warn',
        'prefer-template': 'warn',
        'object-shorthand': 'warn',
        'prefer-arrow-callback': 'warn',
        'prefer-destructuring': 'warn',
        'no-param-reassign': 'warn',
        'no-plusplus': 'warn',
        'no-shadow': 'warn',
        'no-use-before-define': 'warn',
        'no-restricted-syntax': 'warn',
        'no-nested-ternary': 'warn',
        'max-len': 'warn',
        'complexity': 'warn',
        'no-case-declarations': 'warn',
        'no-prototype-builtins': 'warn',
        'no-self-assign': 'warn',
        'no-undef': 'warn',
        'no-useless-catch': 'warn',
        'no-useless-return': 'warn',
        'prefer-rest-params': 'warn',
        'prefer-spread': 'warn',
        'no-mixed-operators': 'warn',
        'no-extra-semi': 'warn',
        'no-unused-expressions': 'warn',
        'no-empty': 'warn',
        'no-unreachable': 'warn',
      };

      // Exclude certain files from ESLint
      eslintConfig.ignorePatterns = [
        'build/**',
        'node_modules/**',
        'coverage/**',
        '*.min.js',
        '**/*.min.js',
        '**/*.bundle.js',
        '**/*.compiled.js',
        '**/*.transpiled.js',
      ];

      return eslintConfig;
    },
  },
};
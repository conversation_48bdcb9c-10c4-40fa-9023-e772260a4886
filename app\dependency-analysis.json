{"used": ["express", "cors", "path", "sqlite3", "react", "react-router-dom", "prop-types", "@mui/material", "@mui/icons-material", "framer-motion", "@react-buddy/ide-toolbox", "@react-buddy/palette-mui", "react-dom", "@testing-library/react", "electron", "jest", "@jest/globals", "events", "fs", "crypto", "os", "uuid", "better-sqlite3", "util", "ccxt", "assert", "child_process", "yargs", "chalk", "cli-table3", "axios", "n8n-workflow", "ccxt.pro", "pino", "opossum", "archiver", "extract-zip", "dotenv", "joi", "chokidar", "mysql2", "winston", "process", "v8", "lodash", "decimal.js", "lru-cache", "commander", "perf_hooks", "prom-client", "module"], "missing": ["express", "cors", "path", "@jest/globals", "events", "fs", "crypto", "os", "util", "assert", "child_process", "yargs", "chalk", "cli-table3", "ccxt.pro", "chokidar", "mysql2", "process", "v8", "lodash", "lru-cache", "commander", "perf_hooks", "prom-client", "module"], "unused": ["@emotion/react", "@emotion/styled", "@nodelib/fs.walk", "@types/react", "@types/react-dom", "bcrypt", "date-fns", "https-proxy-agent", "i<PERSON>is", "keytar", "lodash.merge", "node-cron", "node-fetch", "p-limit", "p-queue", "recharts", "socks-proxy-agent", "technicalindicators", "ws", "@babel/core", "@babel/preset-env", "@babel/preset-react", "@electron/rebuild", "@eslint/config-array", "@eslint/object-schema", "@testing-library/jest-dom", "@testing-library/user-event", "@types/jest", "@types/node", "@types/testing-library__jest-dom", "autoprefixer", "babel-loader", "concurrently", "cross-env", "crypto-browserify", "css-loader", "electron-builder", "electron-rebuild", "eslint", "eslint-config-react-app", "eslint-plugin-flowtype", "eslint-plugin-import", "eslint-plugin-jsx-a11y", "eslint-plugin-react", "eslint-plugin-react-hooks", "html-webpack-plugin", "http-server", "jest-environment-jsdom", "jest-junit", "jest-transform-css", "jsdoc", "os-browserify", "path-browserify", "postcss", "postcss-loader", "react-app-rewired", "react-scripts", "stream-browserify", "style-loader", "tailwindcss", "url", "wait-on", "webpack", "webpack-cli", "webpack-dev-server", "bufferutil", "utf-8-validate"]}
{"unused": [{"name": "@types/react", "type": "dependency"}, {"name": "@types/react-dom", "type": "dependency"}, {"name": "@babel/core", "type": "devDependency"}, {"name": "@babel/preset-env", "type": "devDependency"}, {"name": "@babel/preset-react", "type": "devDependency"}, {"name": "@electron/rebuild", "type": "devDependency"}, {"name": "@testing-library/jest-dom", "type": "devDependency"}, {"name": "@testing-library/user-event", "type": "devDependency"}, {"name": "@types/jest", "type": "devDependency"}, {"name": "@types/node", "type": "devDependency"}, {"name": "babel-loader", "type": "devDependency"}, {"name": "concurrently", "type": "devDependency"}, {"name": "cross-env", "type": "devDependency"}, {"name": "crypto-browserify", "type": "devDependency"}, {"name": "css-loader", "type": "devDependency"}, {"name": "electron-builder", "type": "devDependency"}, {"name": "electron-rebuild", "type": "devDependency"}, {"name": "eslint", "type": "devDependency"}, {"name": "eslint-config-react-app", "type": "devDependency"}, {"name": "eslint-plugin-flowtype", "type": "devDependency"}, {"name": "eslint-plugin-import", "type": "devDependency"}, {"name": "eslint-plugin-jsx-a11y", "type": "devDependency"}, {"name": "eslint-plugin-react", "type": "devDependency"}, {"name": "eslint-plugin-react-hooks", "type": "devDependency"}, {"name": "http-server", "type": "devDependency"}, {"name": "jest", "type": "devDependency"}, {"name": "jest-environment-jsdom", "type": "devDependency"}, {"name": "jsdoc", "type": "devDependency"}, {"name": "os-browserify", "type": "devDependency"}, {"name": "path-browserify", "type": "devDependency"}, {"name": "postcss", "type": "devDependency"}, {"name": "postcss-loader", "type": "devDependency"}, {"name": "react-app-rewired", "type": "devDependency"}, {"name": "react-scripts", "type": "devDependency"}, {"name": "stream-browserify", "type": "devDependency"}, {"name": "style-loader", "type": "devDependency"}, {"name": "tailwindcss", "type": "devDependency"}, {"name": "url", "type": "devDependency"}, {"name": "wait-on", "type": "devDependency"}, {"name": "webpack-cli", "type": "devDependency"}, {"name": "webpack-dev-server", "type": "devDependency"}], "missing": ["assert", "ccxt.pro", "process", "v8", "perf_hooks", "module"], "summary": {"totalDeclared": 84, "totalUsed": 49, "unusedCount": 41, "missingCount": 6}, "usedDependencies": ["@jest/globals", "@mui/icons-material", "@mui/material", "@react-buddy/ide-toolbox", "@react-buddy/palette-mui", "@testing-library/react", "archiver", "assert", "autoprefixer", "axios", "better-sqlite3", "ccxt", "ccxt.pro", "chalk", "chokidar", "cli-table3", "commander", "cors", "decimal.js", "dotenv", "electron", "express", "extract-zip", "framer-motion", "html-webpack-plugin", "joi", "lodash", "lru-cache", "module", "mysql2", "n8n-workflow", "node-fetch", "opossum", "perf_hooks", "pino", "postcss-preset-env", "process", "prom-client", "prop-types", "react", "react-dom", "react-router-dom", "sqlite3", "uuid", "v8", "webpack", "webpack-bundle-analyzer", "winston", "yargs"], "declaredDependencies": ["@mui/icons-material", "@mui/material", "@types/react", "@types/react-dom", "archiver", "axios", "better-sqlite3", "ccxt", "chalk", "chokidar", "cli-table3", "commander", "cors", "decimal.js", "dotenv", "express", "extract-zip", "framer-motion", "joi", "lodash", "lru-cache", "mysql2", "n8n-workflow", "node-fetch", "opossum", "pino", "prom-client", "prop-types", "react", "react-dom", "react-router-dom", "sqlite3", "uuid", "winston", "yargs"], "declaredDevDependencies": ["@babel/core", "@babel/preset-env", "@babel/preset-react", "@electron/rebuild", "@jest/globals", "@react-buddy/ide-toolbox", "@react-buddy/palette-mui", "@testing-library/jest-dom", "@testing-library/react", "@testing-library/user-event", "@types/jest", "@types/node", "autoprefixer", "babel-loader", "concurrently", "cross-env", "crypto-browserify", "css-loader", "electron", "electron-builder", "electron-rebuild", "eslint", "eslint-config-react-app", "eslint-plugin-flowtype", "eslint-plugin-import", "eslint-plugin-jsx-a11y", "eslint-plugin-react", "eslint-plugin-react-hooks", "html-webpack-plugin", "http-server", "jest", "jest-environment-jsdom", "jsdoc", "os-browserify", "path-browserify", "postcss", "postcss-loader", "postcss-preset-env", "react-app-rewired", "react-scripts", "stream-browserify", "style-loader", "tailwindcss", "url", "wait-on", "webpack", "webpack-bundle-analyzer", "webpack-cli", "webpack-dev-server"], "timestamp": "2025-07-17T18:58:40.415Z"}
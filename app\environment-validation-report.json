{"timestamp": "2025-07-29T14:43:58.378Z", "environment": "development", "nodeVersion": "v24.4.1", "platform": "win32", "summary": {"totalChecks": 25, "passed": 25, "failed": 0, "warnings": 0}, "checks": [{"name": "Node.js Version", "passed": true, "message": "v24.4.1 (compatible)"}, {"name": "Environment File: .env", "passed": true, "message": "Found"}, {"name": "Environment File: .env.production", "passed": true, "message": "Production environment file found"}, {"name": "Environment File: .env.staging", "passed": true, "message": "Found"}, {"name": "Required Variable: NODE_ENV", "passed": "production", "message": "Set to: production"}, {"name": "Required Variable: REACT_APP_VERSION", "passed": "1.0.0", "message": "Set to: 1.0.0"}, {"name": "Required Variable: REACT_APP_API_BASE_URL", "passed": "https://api.memecointrader.com", "message": "Set to: https://api.memecointrader.com"}, {"name": "Required Variable: REACT_APP_ENABLE_ERROR_REPORTING", "passed": "true", "message": "Set to: true"}, {"name": "package.json name", "passed": true, "message": "Present"}, {"name": "package.json version", "passed": true, "message": "Present"}, {"name": "package.json main", "passed": true, "message": "Present"}, {"name": "package.json scripts", "passed": true, "message": "Present"}, {"name": "Script: build:production", "passed": "webpack --config webpack.config.production.js --mode production", "message": "Defined"}, {"name": "Script: start-production", "passed": "node start-production.js", "message": "Defined"}, {"name": "Script: dist", "passed": "npm run build && electron-builder --publish=never", "message": "Defined"}, {"name": "Dependencies", "passed": true, "message": "39 dependencies found"}, {"name": "System Memory", "passed": true, "message": "31GB (sufficient)"}, {"name": "Disk Space", "passed": true, "message": "Available (detailed check recommended)"}, {"name": "Platform Support", "passed": true, "message": "win32 (supported)"}, {"name": "Webpack", "passed": true, "message": "Available"}, {"name": "Electron", "passed": true, "message": "Available"}, {"name": "<PERSON>l", "passed": true, "message": "Available"}, {"name": "Config File: webpack.config.js", "passed": true, "message": "Found"}, {"name": "Config File: webpack.config.production.js", "passed": true, "message": "Found"}, {"name": "Config File: electron-builder.config.js", "passed": true, "message": "Found"}], "errors": [], "warnings": [], "ready": true}
#!/usr/bin/env node

/**
 * @fileoverview Comprehensive Backend Syntax Error Fix Script
 * @description Systematically fixes all syntax errors in the backend trading system
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Common syntax error patterns and their fixes
const SYNTAX_FIXES = [
  // Fix incomplete object literals
  {
    pattern: /{\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s*,/g,
    replacement: '{ $1: $1,',
  },
  {
    pattern: /{\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s*}/g,
    replacement: '{ $1: $1 }',
  },

  // Fix function declarations
  {
    pattern: /^(\s*)async\s*\n\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(\s*([^)]*)\s*\)\s*\n\s*{/gm,
    replacement: '$1async $2($3) {',
  },
  {
    pattern: /^(\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(\s*([^)]*)\s*\)\s*\n\s*{/gm,
    replacement: '$1$2($3) {',
  },

  // Fix object property assignments
  {
    pattern: /(\w+)\s*:\s*\n\s*([^,}]+)/g,
    replacement: '$1: $2',
  },

  // Fix array access syntax
  {
    pattern: /(\w+)\(Math\.random\(\)\s*\*\s*(\w+)\.length\s*\)\]/g,
    replacement: '$2[Math.floor(Math.random() * $2.length)]',
  },

  // Fix timestamp() calls
  {
    pattern: /timestamp\(\)/g,
    replacement: 'Date.now()',
  },

  // Fix incomplete return statements
  {
    pattern: /return\s*{\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s*,\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s*,\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s*}/g,
    replacement: 'return { $1: this.$1, $2: this.$2, $3: this.$3 }',
  },
];

// Files to process (critical backend files)
const BACKEND_FILES = [
  'trading/engines/trading/orchestration/TradingOrchestrator.js',
  'trading/engines/trading/bots/GridBotManager.js',
  'trading/engines/trading/bots/UnifiedGridBotEngine.js',
  'trading/engines/trading/orchestration/component-initializer.js',
  'trading/analysis/MemeCoinAnalyzer.js',
  'trading/engines/analysis/MemeCoinPatternAnalyzer.js',
  'trading/engines/trading/n8n-compatibility-test.js',
  'trading/engines/trading/whaletrader/Elite.WhaleTracker.js',
  'trading/analysis/SentimentAnalyzer.js',
  'trading/analysis/PerformanceTracker.js',
  'trading/engines/trading/MemeCoinScanner.js',
  'trading/ai/AutonomousTrader.js',
];

/**
 * Apply syntax fixes to a file
 */
function fixFileSyntax(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️ File not found: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let fixesApplied = 0;

    // Apply each syntax fix
    for (const fix of SYNTAX_FIXES) {
      const originalContent = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== originalContent) {
        fixesApplied++;
      }
    }

    // Additional specific fixes
    content = fixSpecificPatterns(content, filePath);

    // Write back the fixed content
    if (fixesApplied > 0) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed ${fixesApplied} syntax issues in ${filePath}`);
    } else {
      console.log(`✓ No syntax fixes needed for ${filePath}`);
    }

    return true;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Apply specific pattern fixes based on file content
 */
function fixSpecificPatterns(content, filePath) {
  // Fix incomplete object destructuring
  content = content.replace(/{\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\n\s*:\s*([^,}]+)/g, '{ $1: $2');

  // Fix broken catch blocks
  content = content.replace(/catch\s*\(\s*([^)]+)\s*\)\s*\n\s*{/g, 'catch ($1) {');

  // Fix incomplete for loops
  content = content.replace(/for\s*\(\s*([^)]+)\s*\)\s*\n\s*{/g, 'for ($1) {');

  // Fix incomplete if statements
  content = content.replace(/if\s*\(\s*([^)]+)\s*\)\s*\n\s*{/g, 'if ($1) {');

  // Fix incomplete while loops
  content = content.replace(/while\s*\(\s*([^)]+)\s*\)\s*\n\s*{/g, 'while ($1) {');

  // Fix incomplete try blocks
  content = content.replace(/try\s*\n\s*{/g, 'try {');

  // Fix incomplete else blocks
  content = content.replace(/else\s*\n\s*{/g, 'else {');

  // Fix incomplete class methods
  content = content.replace(/^(\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(\s*([^)]*)\s*\)\s*\n\s*{/gm, '$1$2($3) {');

  // Fix incomplete arrow functions
  content = content.replace(/=>\s*\n\s*{/g, '=> {');

  return content;
}

/**
 * Validate syntax of a file
 */
function validateSyntax(filePath) {
  try {
    execSync(`node -c "${filePath}"`, { stdio: 'pipe' });
    return true;
  } catch (error) {
    console.log(`❌ Syntax error in ${filePath}: ${error.message}`);
    return false;
  }
}

/**
 * Main execution function
 */
function fixAllBackendSyntaxErrors() {
  console.log('🔧 Starting comprehensive backend syntax error fixes...\n');

  let totalFiles = 0;
  let fixedFiles = 0;
  let validFiles = 0;

  for (const relativePath of BACKEND_FILES) {
    const fullPath = path.join(__dirname, relativePath);
    totalFiles++;

    console.log(`\n📁 Processing: ${relativePath}`);

    // Apply syntax fixes
    if (fixFileSyntax(fullPath)) {
      fixedFiles++;

      // Validate syntax after fixes
      if (validateSyntax(fullPath)) {
        validFiles++;
        console.log(`✅ Syntax validation passed for ${relativePath}`);
      } else {
        console.log(`⚠️ Syntax validation failed for ${relativePath} - needs manual review`);
      }
    }
  }

  // Summary
  console.log('\n📊 Backend Syntax Fix Summary:');
  console.log(`📁 Total files processed: ${totalFiles}`);
  console.log(`🔧 Files with fixes applied: ${fixedFiles}`);
  console.log(`✅ Files with valid syntax: ${validFiles}`);
  console.log(`❌ Files needing manual review: ${totalFiles - validFiles}`);

  const successRate = ((validFiles / totalFiles) * 100).toFixed(1);
  console.log(`📈 Success rate: ${successRate}%`);

  if (validFiles === totalFiles) {
    console.log('\n🎉 All backend files have valid syntax!');
    console.log('✅ ElectronTrader backend is ready for operation');
  } else {
    console.log('\n⚠️ Some files still need manual review');
    console.log('🔍 Check the files marked with syntax validation failures');
  }
}

// Run if called directly
if (require.main === module) {
  fixAllBackendSyntaxErrors();
}

module.exports = {
  fixAllBackendSyntaxErrors,
  fixFileSyntax,
  validateSyntax,
};

{"channelValidation": {"mainHandlers": 63, "preloadMethods": 96, "mainHandlersList": ["cancel-all-orders", "cancel-order", "context-get-bot-performance", "context-get-exchange-config", "context-get-market-analysis", "context-get-position-risk", "context-get-trading-session", "context-search", "context-store-bot-performance", "context-store-exchange-config", "context-store-market-analysis", "context-store-position-risk", "context-store-trading-session", "delete-coin", "execute-trade", "get-active-grid-bots", "get-asset-allocation", "get-autonomous-status", "get-bot-status", "get-coins", "get-cross-exchange-balance", "get-grid-bot-history", "get-grid-history", "get-logs", "get-market-data", "get-market-overview", "get-meme-opportunities", "get-open-orders", "get-order-history", "get-performance-metrics", "get-pnl-report", "get-portfolio-summary", "get-price-history", "get-risk-metrics", "get-sentiment-data", "get-settings", "get-system-status", "get-tracked-whales", "get-trade-history", "get-trading-stats", "get-whale-signals", "health-check", "save-coin", "save-settings", "start-autonomous-trading", "start-bot", "start-grid-bot", "start-meme-scanner", "stop-all-grid-bots", "stop-autonomous-trading", "stop-bot", "stop-grid-bot", "stop-meme-scanner", "update-coin", "update-grid-bot-config", "workspace-get-file", "workspace-get-file-dependencies", "workspace-get-files-by-extension", "workspace-get-files-by-type", "workspace-get-recent-files", "workspace-get-stats", "workspace-search", "workspace-trigger-scan"], "preloadMethodsList": ["addExchange", "addWhaleWallet", "cancelAllOrders", "cancelOrder", "clearLogs", "createBackup", "deleteCoin", "executeArbitrage", "exportLogs", "exportSettings", "getAppVersion", "getArbitrageOpportunities", "getArbitragePositions", "getArbitrageStats", "getArbitrageStatus", "getAssetAllocation", "getBotStatus", "getCoins", "getCrossExchangeBalances", "getDCAHistory", "getDCAPositions", "getDetectedOpportunities", "getDrawdownAnalysis", "getExchangeBalances", "getExchangeHealth", "getExchangePortfolio", "getExchanges", "getGridHistory", "getGridPositions", "getGridPresets", "getLogs", "getMarketData", "getMarketOverview", "getMemeCoinHistory", "getMemeCoinOpportunities", "getOpenOrders", "getOpportunityScannerStats", "getOrderHistory", "getPerformanceMetrics", "getPnLReport", "getPortfolioOptimization", "getPortfolioPerformance", "getPortfolioRiskMetrics", "getPortfolioSummary", "getPriceHistory", "getRebalancingOpportunities", "getRiskMetrics", "getRiskParameters", "getScannerStatus", "getSettings", "getSystemInfo", "getTrackedWhales", "getTradeHistory", "getTradingStats", "getWalletBalance", "getWhaleHistory", "getWhaleSignals", "healthCheck", "importSettings", "on", "placeLimitOrder", "placeMarketOrder", "rebalanceCrossExchangePortfolio", "rebalancePortfolio", "removeExchange", "removeWhaleWallet", "reportError", "resetSettings", "saveCoin", "saveGridPreset", "saveSettings", "setLogLevel", "setRiskParameters", "startArbitrageEngine", "startBot", "startDCA", "startGrid", "startMemeCoinScanner", "startOpportunityScanner", "startPortfolioMonitoring", "stopAllGrids", "stopArbitrageEngine", "stopBot", "stopDCA", "stopGrid", "stopMemeCoinScanner", "stopOpportunityScanner", "stopPortfolioMonitoring", "testExchangeConnection", "toggleWhaleTracking", "updateArbitrageConfig", "updateCoin", "updateDCAConfig", "updateGridConfig", "updateOpportunityScannerConfig", "updateScannerConfig"]}, "handlerValidation": {"missingHandlers": ["add-exchange", "add-whale-wallet", "clear-logs", "create-backup", "execute-arbitrage", "export-logs", "export-settings", "get-app-version", "get-arbitrage-opportunities", "get-arbitrage-positions", "get-arbitrage-stats", "get-arbitrage-status", "get-cross-exchange-balances", "get-d-c-a-history", "get-d-c-a-positions", "get-detected-opportunities", "get-drawdown-analysis", "get-exchange-balances", "get-exchange-health", "get-exchange-portfolio", "get-exchanges", "get-grid-positions", "get-grid-presets", "get-meme-coin-history", "get-meme-coin-opportunities", "get-opportunity-scanner-stats", "get-pn-l-report", "get-portfolio-optimization", "get-portfolio-performance", "get-portfolio-risk-metrics", "get-rebalancing-opportunities", "get-risk-parameters", "get-scanner-status", "get-system-info", "get-wallet-balance", "get-whale-history", "import-settings", "on", "place-limit-order", "place-market-order", "rebalance-cross-exchange-portfolio", "rebalance-portfolio", "remove-exchange", "remove-whale-wallet", "report-error", "reset-settings", "save-grid-preset", "set-log-level", "set-risk-parameters", "start-arbitrage-engine", "start-d-c-a", "start-grid", "start-meme-coin-scanner", "start-opportunity-scanner", "start-portfolio-monitoring", "stop-all-grids", "stop-arbitrage-engine", "stop-d-c-a", "stop-grid", "stop-meme-coin-scanner", "stop-opportunity-scanner", "stop-portfolio-monitoring", "test-exchange-connection", "toggle-whale-tracking", "update-arbitrage-config", "update-d-c-a-config", "update-grid-config", "update-opportunity-scanner-config", "update-scanner-config"], "extraHandlers": ["context-get-bot-performance", "context-get-exchange-config", "context-get-market-analysis", "context-get-position-risk", "context-get-trading-session", "context-search", "context-store-bot-performance", "context-store-exchange-config", "context-store-market-analysis", "context-store-position-risk", "context-store-trading-session", "execute-trade", "get-active-grid-bots", "get-autonomous-status", "get-cross-exchange-balance", "get-grid-bot-history", "get-meme-opportunities", "get-pnl-report", "get-sentiment-data", "get-system-status", "start-autonomous-trading", "start-grid-bot", "start-meme-scanner", "stop-all-grid-bots", "stop-autonomous-trading", "stop-grid-bot", "stop-meme-scanner", "update-grid-bot-config", "workspace-get-file", "workspace-get-file-dependencies", "workspace-get-files-by-extension", "workspace-get-files-by-type", "workspace-get-recent-files", "workspace-get-stats", "workspace-search", "workspace-trigger-scan"], "matchingPairs": ["cancel-all-orders", "cancel-order", "delete-coin", "get-asset-allocation", "get-bot-status", "get-coins", "get-grid-history", "get-logs", "get-market-data", "get-market-overview", "get-open-orders", "get-order-history", "get-performance-metrics", "get-portfolio-summary", "get-price-history", "get-risk-metrics", "get-settings", "get-tracked-whales", "get-trade-history", "get-trading-stats", "get-whale-signals", "health-check", "save-coin", "save-settings", "start-bot", "stop-bot", "update-coin"], "missingCount": 69, "extraCount": 36, "matchingCount": 27}, "preloadValidation": {"hasContextBridge": true, "hasIpcRenderer": true, "hasSafeInvoke": true, "hasElectronAPI": true, "hasErrorHandling": true, "allChecksPass": true}, "consistency": {"criticalChannels": ["health-check", "get-bot-status", "start-bot", "stop-bot", "get-portfolio-summary", "get-trading-stats", "get-settings", "save-settings", "get-coins", "get-market-data"], "missingCritical": [], "presentCritical": ["health-check", "get-bot-status", "start-bot", "stop-bot", "get-portfolio-summary", "get-trading-stats", "get-settings", "save-settings", "get-coins", "get-market-data"], "criticalChannelsCount": 10, "missingCriticalCount": 0, "presentCriticalCount": 10, "allCriticalPresent": true}, "summary": {"totalChecks": 4, "passedChecks": 3, "failedChecks": 1, "successRate": 75, "overallSuccess": false, "checks": {"channelValidation": true, "handlerConsistency": false, "preloadStructure": true, "criticalChannels": true}}}
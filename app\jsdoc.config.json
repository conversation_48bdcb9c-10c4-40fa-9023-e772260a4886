{"source": {"include": ["./src/", "./trading/", "./main.js", "./preload.js", "./start-production.js"], "includePattern": "\\.(js|jsx)$", "exclude": ["node_modules/", "build/", "dist/", "coverage/", "__tests__/", "*.test.js", "*.spec.js", "src/components/AdvancedChart.jsx", "src/components/BotDashboard.jsx", "src/components/UltimateDashboard.jsx", "trading/node_modules/"]}, "opts": {"destination": "./docs/jsdoc/", "recurse": true, "readme": "./docs/README.md"}, "plugins": ["plugins/markdown"], "templates": {"cleverLinks": false, "monospaceLinks": false}, "metadata": {"title": "electronTrader API Documentation", "version": "1.0.0", "description": "Comprehensive API documentation for the electronTrader cryptocurrency trading platform"}, "markdown": {"hardwrap": true, "idInHeadings": true}}
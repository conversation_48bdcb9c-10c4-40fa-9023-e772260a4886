'use strict';

/**
 * @fileoverview Electron Main Process (Refactored)
 * @description Main process for the electronTrader desktop application. Handles window management,
 * IPC communication between frontend and the refactored trading system, and application lifecycle.
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-07-15
 */

// @ts-check
// cspell retryable
const {
  app,
  BrowserWindow,
  ipcMain,
} = require('electron');
const path = require('path');
const fs = require('fs');
const logger = require('./trading/shared/helpers/logger');
const TradingOrchestrator = require('./trading/engines/trading/orchestration/TradingOrchestrator');
const standardizedIPCHandler = require('./utils/StandardizedIPCHandler.js');
const {
  getErrorHandler,
} = require('./trading/engines/shared/security/error-handling/ErrorHandler');

/** @type {BrowserWindow | null} */
let mainWindow = null;
let tradingOrchestrator = null;
/** @type {any} */
let systemErrorHandler;

/**
 * Creates the main application window.
 */
function createWindow() {
  try {
    const preloadPath = path.resolve(__dirname, 'preload.js');
    logger.info(`Creating window with preload path: ${preloadPath}`);
    if (!fs.existsSync(preloadPath)) {
      throw new Error(`Preload script not found at: ${preloadPath}`);
    }
    mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      webPreferences: {
        preload: preloadPath,
        contextIsolation: true,
        nodeIntegration: false,
      },
    });
    const startUrl = process.env.ELECTRON_IS_DEV ? 'http://localhost:3000' : `file://${path.join(__dirname, '../build/index.html')}`;
    logger.info(`Loading URL: ${startUrl}`);
    mainWindow.loadURL(startUrl);
    if (process.env.ELECTRON_IS_DEV) {
      mainWindow.webContents.openDevTools();
    }
    mainWindow.on('closed', () => {
      mainWindow = null;
    });
    global.mainWindow = mainWindow;
    logger.info('✅ Main window created successfully');
  } catch (_error) {
    logger._error('❌ Failed to create main window:', _error);
    throw _error;
  }
}

/**
 * Generic IPC handler for secure, asynchronous communication.
 * @param {string} channel - The IPC channel name.
 * @param {function(...any):Promise<any>} handler - The async function to handle the request.
 */
function handleIPC(channel, handler) {
  ipcMain.handle(channel, (event, ...args) => {
    return standardizedIPCHandler.createHandler(channel, handler)(event, ...args);
  });
}

/**
 * Sets up global _error handlers for the main process.
 */
function setupMainProcessErrorHandlers() {
  process.on('uncaughtException', _error => {
    logger._error('❌ Uncaught Exception in main process:', {
      message: _error.message,
      stack: _error.stack,
      component: 'main-process',
      severity: 'critical',
    });
  });
  process.on('unhandledRejection', (reason, _promise) => {
    const _error = reason instanceof Error ? reason : Error(String(reason));
    logger._error('❌ Unhandled Rejection in main process:', {
      message: _error.message,
      stack: _error.stack,
      component: 'main-process',
      severity: 'critical',
    });
  });
}

/**
 * Initializes IPC handlers for communication between main and renderer processes.
 */
function initializeIPCHandlers() {
  if (!tradingOrchestrator) {
    logger.warn('Trading orchestrator not initialized, skipping IPC handler registration.');
    return;
  }
  logger.info('Registering IPC handlers...');

  // System Status & Control
  handleIPC('get-system-status', () => tradingOrchestrator.getStatus());
  handleIPC('get-bot-status', () => tradingOrchestrator.getBotStatus());
  handleIPC('health-check', () => tradingOrchestrator.healthCheck());
  handleIPC('start-bot', () => tradingOrchestrator.start());
  handleIPC('stop-bot', () => tradingOrchestrator.stop());

  // Configuration
  handleIPC('get-config', _key => tradingOrchestrator.getConfig(_key));
  handleIPC('update-config', (_key, value) => tradingOrchestrator.updateConfig(_key, value));
  handleIPC('get-settings', () => tradingOrchestrator.getSettings());
  handleIPC('save-settings', (settings) => tradingOrchestrator.saveSettings(settings));

  // System Info
  handleIPC('get-system-info', () => {
    const packageJson = require('./package.json');
    const {
      electron,
      node,
    } = process.versions;
    return {
      appVersion: packageJson.version || '1.0.0',
      electronVersion: electron,
      nodeVersion: node,
      platform: process.platform,
      arch: process.arch,
      timestamp: new Date().toISOString(),
    };
  });

  // Dashboard Data
  handleIPC('get-coins', () => tradingOrchestrator.getCoins());
  handleIPC('get-market-data', (symbol) => tradingOrchestrator.getMarketData(symbol));
  handleIPC('get-portfolio-summary', () => tradingOrchestrator.getPortfolioSummary());
  handleIPC('get-trading-stats', () => tradingOrchestrator.getTradingStats());
  handleIPC('get-market-overview', () => tradingOrchestrator.getMarketOverview());
  handleIPC('get-risk-metrics', () => tradingOrchestrator.getRiskMetrics());
  handleIPC('get-price-history', () => tradingOrchestrator.getPriceHistory());
  handleIPC('get-asset-allocation', () => tradingOrchestrator.getAssetAllocation());
  handleIPC('get-trade-history', () => tradingOrchestrator.getTradeHistory());
  handleIPC('get-active-bots', () => tradingOrchestrator.getActiveBots());
  handleIPC('get-system-health', () => tradingOrchestrator.getSystemHealth());
  handleIPC('get-system-metrics', () => tradingOrchestrator.getSystemMetrics());
  handleIPC('get-system-alerts', () => tradingOrchestrator.getSystemAlerts());

  // Real-time Status
  handleIPC('get-real-time-status', () => tradingOrchestrator.getRealTimeStatus());
  handleIPC('get-component-health', () => tradingOrchestrator.getComponentHealth());

  // Batch 1: Added Handlers
  handleIPC('add-exchange', (config) => tradingOrchestrator.addExchange(config));
  handleIPC('add-whale-wallet', (address) => tradingOrchestrator.addWhaleWallet(address));
  handleIPC('cancel-all-orders', (symbol) => tradingOrchestrator.cancelAllOrders(symbol));
  handleIPC('cancel-order', (orderId) => tradingOrchestrator.cancelOrder(orderId));
  handleIPC('clear-logs', () => tradingOrchestrator.clearLogs());
  handleIPC('create-backup', () => tradingOrchestrator.createBackup());
  handleIPC('delete-coin', (coin) => tradingOrchestrator.deleteCoin(coin));
  handleIPC('execute-arbitrage', (opportunity) => tradingOrchestrator.executeArbitrage(opportunity));
  handleIPC('export-logs', () => tradingOrchestrator.exportLogs());
  handleIPC('export-settings', () => tradingOrchestrator.exportSettings());
  handleIPC('get-app-version', () => app.getVersion());
  handleIPC('get-arbitrage-opportunities', () => tradingOrchestrator.getArbitrageOpportunities());
  handleIPC('get-arbitrage-positions', () => tradingOrchestrator.getArbitragePositions());
  handleIPC('get-arbitrage-stats', () => tradingOrchestrator.getArbitrageStats());
  handleIPC('get-arbitrage-status', () => tradingOrchestrator.getArbitrageStatus());
  handleIPC('get-cross-exchange-balances', () => tradingOrchestrator.getCrossExchangeBalances());
  handleIPC('get-dca-history', () => tradingOrchestrator.getDCAHistory());
  handleIPC('get-dca-positions', () => tradingOrchestrator.getDCAPositions());
  handleIPC('get-detected-opportunities', () => tradingOrchestrator.getDetectedOpportunities());
  handleIPC('get-drawdown-analysis', () => tradingOrchestrator.getDrawdownAnalysis());

  // Batch 2: Added Handlers
  handleIPC('get-exchange-balances', (exchangeId) => tradingOrchestrator.getExchangeBalances(exchangeId));
  handleIPC('get-exchange-health', () => tradingOrchestrator.getExchangeHealth());
  handleIPC('get-exchange-portfolio', (exchange) => tradingOrchestrator.getExchangePortfolio(exchange));
  handleIPC('get-exchanges', () => tradingOrchestrator.getExchanges());
  handleIPC('get-grid-history', (gridId) => tradingOrchestrator.getGridHistory(gridId));
  handleIPC('get-grid-positions', () => tradingOrchestrator.getGridPositions());
  handleIPC('get-grid-presets', () => tradingOrchestrator.getGridPresets());
  handleIPC('get-ipc-error-statistics', () => tradingOrchestrator.getIPCErrorStatistics());
  handleIPC('get-logs', (level, limit) => tradingOrchestrator.getLogs(level, limit));
  handleIPC('get-meme-coin-history', () => tradingOrchestrator.getMemeCoinHistory());
  handleIPC('get-meme-coin-opportunities', () => tradingOrchestrator.getMemeCoinOpportunities());
  handleIPC('get-monitoring-statistics', () => tradingOrchestrator.getMonitoringStatistics());
  handleIPC('get-open-orders', () => tradingOrchestrator.getOpenOrders());
  handleIPC('get-opportunity-scanner-stats', () => tradingOrchestrator.getOpportunityScannerStats());
  handleIPC('get-order-history', (limit) => tradingOrchestrator.getOrderHistory(limit));
  handleIPC('get-performance-history', (timeRange) => tradingOrchestrator.getPerformanceHistory(timeRange));
  handleIPC('get-performance-metrics', () => tradingOrchestrator.getPerformanceMetrics());
  handleIPC('get-pnl-report', (timeframe) => tradingOrchestrator.getPnLReport(timeframe));
  handleIPC('get-portfolio-optimization', () => tradingOrchestrator.getPortfolioOptimization());
  handleIPC('get-portfolio-performance', () => tradingOrchestrator.getPortfolioPerformance());

  // Batch 3: Added Handlers
  handleIPC('get-portfolio-risk-metrics', () => tradingOrchestrator.getPortfolioRiskMetrics());
  handleIPC('get-rebalancing-opportunities', () => tradingOrchestrator.getRebalancingOpportunities());
  handleIPC('get-risk-parameters', () => tradingOrchestrator.getRiskParameters());
  handleIPC('get-scanner-status', () => tradingOrchestrator.getScannerStatus());
  handleIPC('get-status-reports', (limit, filter) => tradingOrchestrator.getStatusReports(limit, filter));
  handleIPC('get-tracked-whales', () => tradingOrchestrator.getTrackedWhales());
  handleIPC('get-wallet-balance', () => tradingOrchestrator.getWalletBalance());
  handleIPC('get-whale-history', (timeframe) => tradingOrchestrator.getWhaleHistory(timeframe));
  handleIPC('get-whale-signals', () => tradingOrchestrator.getWhaleSignals());
  handleIPC('get-whale-tracking-status', () => tradingOrchestrator.getWhaleTrackingStatus());
  handleIPC('import-settings', (settings) => tradingOrchestrator.importSettings(settings));
  handleIPC('initialize-trading', () => tradingOrchestrator.initializeTrading());
  handleIPC('on', (channel, callback) => tradingOrchestrator.on(channel, callback));
  handleIPC('on-arbitrage-executed', (callback) => tradingOrchestrator.onArbitrageExecuted(callback));
  handleIPC('on-arbitrage-opportunity', (callback) => tradingOrchestrator.onArbitrageOpportunity(callback));
  handleIPC('place-limit-order', (params) => tradingOrchestrator.placeLimitOrder(params));
  handleIPC('place-market-order', (params) => tradingOrchestrator.placeMarketOrder(params));
  handleIPC('rebalance-cross-exchange-portfolio', (config) => tradingOrchestrator.rebalanceCrossExchangePortfolio(config));
  handleIPC('rebalance-portfolio', (target) => tradingOrchestrator.rebalancePortfolio(target));
  handleIPC('remove-exchange', (exchangeId) => tradingOrchestrator.removeExchange(exchangeId));
  handleIPC('remove-whale-wallet', (address) => tradingOrchestrator.removeWhaleWallet(address));
  handleIPC('report-error', (error, context) => tradingOrchestrator.reportError(error, context));
  handleIPC('reset-ipc-error-statistics', () => tradingOrchestrator.resetIPCErrorStatistics());
  handleIPC('reset-settings', () => tradingOrchestrator.resetSettings());
  handleIPC('run-health-check', (componentName) => tradingOrchestrator.runHealthCheck(componentName));
  handleIPC('save-coin', (coin) => tradingOrchestrator.saveCoin(coin));
  handleIPC('save-grid-preset', (preset) => tradingOrchestrator.saveGridPreset(preset));
  handleIPC('set-log-level', (level) => tradingOrchestrator.setLogLevel(level));
  handleIPC('set-risk-parameters', (params) => tradingOrchestrator.setRiskParameters(params));
  handleIPC('start-arbitrage-engine', () => tradingOrchestrator.startArbitrageEngine());
  handleIPC('start-arbitrage-scanning', () => tradingOrchestrator.startArbitrageScanning());
  handleIPC('start-dca', (config) => tradingOrchestrator.startDCA(config));
  handleIPC('start-grid', (config) => tradingOrchestrator.startGrid(config));
  handleIPC('start-health-monitoring', () => tradingOrchestrator.startHealthMonitoring());
  handleIPC('start-meme-coin-scanner', () => tradingOrchestrator.startMemeCoinScanner());
  handleIPC('start-opportunity-scanner', () => tradingOrchestrator.startOpportunityScanner());
  handleIPC('start-portfolio-monitoring', () => tradingOrchestrator.startPortfolioMonitoring());
  handleIPC('stop-all-grids', () => tradingOrchestrator.stopAllGrids());
  handleIPC('stop-arbitrage-engine', () => tradingOrchestrator.stopArbitrageEngine());
  handleIPC('stop-arbitrage-scanning', () => tradingOrchestrator.stopArbitrageScanning());
  handleIPC('stop-dca', (dcaId) => tradingOrchestrator.stopDCA(dcaId));
  handleIPC('stop-grid', (gridId) => tradingOrchestrator.stopGrid(gridId));
  handleIPC('stop-health-monitoring', () => tradingOrchestrator.stopHealthMonitoring());
  handleIPC('stop-meme-coin-scanner', () => tradingOrchestrator.stopMemeCoinScanner());
  handleIPC('stop-opportunity-scanner', () => tradingOrchestrator.stopOpportunityScanner());
  handleIPC('stop-portfolio-monitoring', () => tradingOrchestrator.stopPortfolioMonitoring());
  handleIPC('test-exchange-connection', (exchangeId) => tradingOrchestrator.testExchangeConnection(exchangeId));
  handleIPC('toggle-whale-tracking', (enabled) => tradingOrchestrator.toggleWhaleTracking(enabled));
  handleIPC('update-arbitrage-config', (config) => tradingOrchestrator.updateArbitrageConfig(config));
  handleIPC('update-coin', (coinId, updates) => tradingOrchestrator.updateCoin(coinId, updates));
  handleIPC('update-dca-config', (dcaId, config) => tradingOrchestrator.updateDCAConfig(dcaId, config));
  handleIPC('update-grid-config', (gridId, config) => tradingOrchestrator.updateGridConfig(gridId, config));
  handleIPC('update-opportunity-scanner-config', (config) => tradingOrchestrator.updateOpportunityScannerConfig(config));
  handleIPC('update-scanner-config', (config) => tradingOrchestrator.updateScannerConfig(config));

  logger.info('✅ All IPC handlers registered successfully');
}

/**
 * Initialize the application
 */
function initializeApp() {
  try {
    logger.info('🚀 Starting electronTrader application...');
    setupMainProcessErrorHandlers();
    systemErrorHandler = getErrorHandler({
      circuitBreaker: {
        enabled: true,
      },
      recovery: {
        enabled: true,
      },
      logging: {
        enabled: true,
        console: true,
      },
    });
    // await systemErrorHandler.initialize();
    logger.info('✅ System-wide _error handler initialized.');
    createWindow();
    logger.info('🔧 Initializing TradingOrchestrator...');
    tradingOrchestrator = new (/** @type {any} */TradingOrchestrator)();
    // await systemErrorHandler.withErrorHandling(() => tradingOrchestrator.initialize(), {
    //   component: 'trading-orchestrator',
    //   critical,
    // });
    logger.info('✅ TradingOrchestrator initialized successfully.');
    initializeIPCHandlers();
    logger.info('🎉 electronTrader application started successfully!');
  } catch (_error) {
    logger._error('❌ Failed to initialize application:', {
      message: _error.message,
      stack: _error.stack,
      component: 'application-initialization',
      severity: 'critical',
    });
    if (systemErrorHandler && systemErrorHandler.initialized) {
      // await systemErrorHandler.handleError(_error, {
      //   component: 'application-initialization',
      //   critical,
      // });
    } else {
      logger._error(`System _error handler not initialized. Cannot perform recovery. Original _error: ${_error.message}`);
      logger._error(_error.stack);
    }
    app.quit();
  }
}

// ========================================================================================
// Application Lifecycle
// ========================================================================================
app.whenReady().then(initializeApp);
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});
app.on('before-quit', async () => {
  logger.info('Application is about to quit.');
  if (tradingOrchestrator) {
    logger.info('🛑 Gracefully shutting down Trading Orchestrator...');
    // await tradingOrchestrator.stop();
    logger.info('✅ Trading Orchestrator stopped.');
  }
});
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
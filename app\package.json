{"name": "meme-coin-trader-app", "version": "1.0.0", "description": "Electron + React desktop UI for Meme Coin Trader", "author": "electronTrader Team", "main": "main.js", "homepage": "./", "scripts": {"start": "craco start", "dev": "concurrently \"npm run start\" \"wait-for-localhost 3000 && npm run electron-dev\"", "start-production": "node start-production.js", "electron": "electron .", "electron-dev": "cross-env ELECTRON_IS_DEV=true NODE_ENV=development electron .", "electron-pack": "electron-builder", "build": "craco build", "build:webpack": "webpack --config webpack.config.js", "build:production": "webpack --config webpack.config.production.js --mode production", "build:production:analyze": "cross-env ANALYZE_BUNDLE=true npm run build:production", "build:production:optimized": "node scripts/build-production.js", "validate:build": "node scripts/validate-production-build.js", "validate:integration": "node scripts/validate-application-integration.js", "test:integration:complete": "jest __tests__/e2e/complete-application-workflow.test.js --runInBand", "build-electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "dist-all": "npm run build && electron-builder -mwl", "pack": "npm run build && electron-builder --dir", "rebuild-native": "electron-rebuild", "test": "craco test", "eject": "react-scripts eject", "lint": "cross-env ESLINT_USE_FLAT_CONFIG=false eslint src/", "lint-fix": "cross-env ESLINT_USE_FLAT_CONFIG=false eslint src/ --fix", "analyze-bundle": "cross-env ANALYZE_BUNDLE=true npm run build:webpack", "build:performance": "webpack --config webpack.config.performance.js", "build:optimized": "npm run build && node scripts/optimize-build.js", "performance-test": "npm run build:performance && npm run electron", "optimize-build": "node scripts/optimize-build.js", "optimize:trading": "node trading/scripts/performance-optimizer.js", "performance:analyze": "npm run optimize:trading && npm run optimize-build", "performance:test": "node scripts/performance-test.js", "memory-test": "node -e 'console.log(process.memoryUsage())'", "test:unit": "jest --coverage --watchAll=false", "test:watch": "jest --watch", "test:coverage": "jest --coverage --watchAll=false", "test:ci": "jest --coverage --watchAll=false --ci --reporters=default --reporters=jest-junit", "test:e2e": "jest --config __tests__/e2e/jest.e2e.config.js --runInBand", "test:e2e:run": "node __tests__/e2e/run-e2e-tests.js", "test:all": "npm run test:unit && npm run test:e2e", "package": "npm run build && electron-builder", "package:all": "npm run build && electron-builder -mwl", "package:win": "npm run build && electron-builder --win", "release": "npm run build:production && electron-builder --publish always", "release:draft": "npm run build:production && electron-builder --publish never", "docs": "jsdoc -c jsdoc.config.json", "docs:serve": "npm run docs && npx http-server docs/jsdoc -p 8080 -o"}, "optionalDependencies": {"bufferutil": "^4.0.8", "utf-8-validate": "^6.0.4"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "archiver": "^7.0.1", "axios": "^1.8.1", "better-sqlite3": "^12.2.0", "ccxt": "^4.4.17", "chalk": "^5.3.0", "chokidar": "^4.0.1", "cli-table3": "^0.6.5", "commander": "^14.0.0", "cors": "^2.8.5", "decimal.js": "^10.4.3", "dotenv": "^17.2.1", "express": "^5.1.0", "extract-zip": "^2.0.1", "framer-motion": "^12.23.9", "joi": "^17.13.3", "lodash": "^4.17.21", "lru-cache": "^11.0.1", "mysql2": "^3.11.3", "n8n-workflow": "^1.17.0", "node-fetch": "^3.3.2", "opossum": "^9.0.0", "pino": "^9.4.0", "prom-client": "^15.1.3", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.1", "react-scripts": "5.0.1", "recharts": "^3.1.0", "sqlite3": "^5.1.7", "uuid": "^11.1.0", "winston": "^3.15.0", "yargs": "^18.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.25.2", "@babel/preset-env": "^7.25.2", "@babel/preset-react": "^7.24.7", "@craco/craco": "^5.9.0", "@electron/rebuild": "^4.0.1", "@jest/globals": "^30.0.5", "@react-buddy/ide-toolbox": "^2.4.0", "@react-buddy/palette-mui": "^5.0.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^24.1.0", "@types/recharts": "^1.8.29", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "autoprefixer": "^10.4.19", "babel-loader": "^10.0.0", "buffer": "^6.0.3", "compression-webpack-plugin": "^11.1.0", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "crypto-browserify": "^3.12.1", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.2", "electron": "37.2.4", "electron-builder": "^26.0.12", "electron-rebuild": "^3.2.9", "eslint": "^9.32.0", "eslint-config-react-app": "^7.0.0", "eslint-plugin-flowtype": "^8.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.2.0", "html-webpack-plugin": "^5.6.0", "http-server": "^14.1.1", "https-browserify": "^1.0.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "jsdoc": "^4.0.3", "mini-css-extract-plugin": "^2.7.6", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "postcss": "^8.5.6", "postcss-loader": "^8.1.1", "postcss-preset-env": "^10.2.4", "querystring-es3": "^0.2.1", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "style-loader": "^4.0.0", "tailwindcss": "^4.1.11", "terser-webpack-plugin": "^5.3.10", "url": "^0.11.4", "util": "^0.12.5", "webpack": "^5.93.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.0.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
{"name": "meme-coin-trader-app", "version": "1.0.0", "description": "Electron + React desktop UI for Meme Coin Trader", "author": "electronTrader Team", "main": "main.js", "homepage": "./", "scripts": {"start": "cross-env BROWSER=none react-app-rewired start", "dev": "concurrently \"npm run start\" \"cross-env-shell wait-on http://localhost:7291 && npm run electron-dev\"", "start-production": "node start-production.js", "electron": "electron .", "electron-dev": "cross-env ELECTRON_IS_DEV=true NODE_ENV=development electron .", "electron-pack": "electron-builder", "build": "react-app-rewired build", "build:webpack": "webpack --config webpack.config.js", "build-electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "dist-all": "npm run build && electron-builder -mwl", "pack": "npm run build && electron-builder --dir", "rebuild-native": "electron-rebuild", "postinstall": "electron-builder install-app-deps", "test": "react-app-rewired test", "eject": "react-scripts eject", "lint": "eslint src/", "lint-fix": "eslint src/ --fix", "analyze-bundle": "cross-env ANALYZE_BUNDLE=true npm run build:webpack", "build:performance": "webpack --config webpack.config.performance.js", "performance-test": "npm run build:performance && npm run electron", "memory-test": "node -e 'console.log(process.memoryUsage())'", "test:unit": "jest --coverage --watchAll=false", "test:watch": "jest --watch", "test:coverage": "jest --coverage --watchAll=false", "test:ci": "jest --coverage --watchAll=false --ci --reporters=default --reporters=jest-junit", "build:production": "node scripts/build-production.js", "package": "npm run build && electron-builder", "package:all": "npm run build && electron-builder -mwl", "package:win": "npm run build && electron-builder --win", "package:mac": "npm run build && electron-builder --mac", "package:linux": "npm run build && electron-builder --linux", "release": "npm run build:production && electron-builder --publish always", "release:draft": "npm run build:production && electron-builder --publish never", "docs": "jsdoc -c jsdoc.config.json", "docs:serve": "npm run docs && npx http-server docs/jsdoc -p 8080 -o"}, "optionalDependencies": {"bufferutil": "^4.0.8", "utf-8-validate": "^6.0.4"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^5.18.0", "@mui/material": "^5.18.0", "@nodelib/fs.walk": "^3.0.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "archiver": "^7.0.1", "axios": "^1.7.2", "bcrypt": "^5.1.1", "better-sqlite3": "^12.2.0", "ccxt": "^4.4.94", "chalk": "^4.1.2", "chokidar": "^3.6.0", "cli-table3": "^0.6.5", "commander": "^12.1.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "decimal.js": "^10.4.3", "dotenv": "^16.6.1", "express": "^4.21.2", "extract-zip": "^2.0.1", "framer-motion": "^11.3.19", "https-proxy-agent": "^7.0.5", "ioredis": "^5.4.1", "joi": "^17.13.3", "keytar": "^7.9.0", "lodash": "^4.17.21", "lodash.merge": "^4.6.2", "lru-cache": "^11.0.2", "mysql2": "^3.11.4", "n8n-workflow": "^1.82.0", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "opossum": "^9.0.0", "p-limit": "^5.0.0", "p-queue": "^8.0.1", "pino": "^9.3.1", "prom-client": "^15.1.3", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.25.1", "recharts": "^2.12.7", "socks-proxy-agent": "^8.0.4", "sqlite3": "^5.1.7", "technicalindicators": "^3.1.0", "uuid": "^10.0.0", "winston": "^3.13.1", "ws": "^8.18.0", "yargs": "^17.7.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.2", "@babel/preset-react": "^7.24.7", "@electron/rebuild": "^3.6.2", "@jest/globals": "^29.7.0", "@eslint/config-array": "^0.18.0", "@eslint/object-schema": "^2.1.4", "@react-buddy/ide-toolbox": "^2.4.0", "@react-buddy/palette-mui": "^5.0.1", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^30.0.0", "@types/node": "^22.5.0", "@types/testing-library__jest-dom": "^6.0.0", "autoprefixer": "^10.4.19", "babel-loader": "^9.1.3", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "crypto-browserify": "^3.12.1", "css-loader": "^7.1.2", "electron": "^31.7.7", "electron-builder": "^25.1.8", "electron-rebuild": "^3.2.9", "eslint": "^8.57.1", "eslint-config-react-app": "^7.0.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "html-webpack-plugin": "^5.6.0", "http-server": "^14.1.1", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.4", "jest-junit": "^16.0.0", "jest-transform-css": "^6.0.3", "jsdoc": "^4.0.2", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "postcss": "^8.4.40", "postcss-loader": "^8.1.1", "postcss-preset-env": "^10.1.0", "react-app-rewired": "2.2.1", "react-scripts": "^5.0.1", "stream-browserify": "^3.0.0", "style-loader": "^4.0.0", "tailwindcss": "^3.4.7", "url": "^0.11.4", "wait-on": "^7.2.0", "webpack": "^5.95.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "overrides": {"nth-check": "^2.1.1", "postcss": "^8.4.40", "webpack-dev-server": "^5.1.0", "svgo": "^3.0.0"}}
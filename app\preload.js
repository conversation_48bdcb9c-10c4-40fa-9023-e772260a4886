const {contextBridge, ipc<PERSON><PERSON><PERSON>} = require('electron');

/**
 * Wraps ipc<PERSON><PERSON>er.invoke to provide a consistent, simplified interface
 * for renderer-to-main communication. It standardizes the response format
 * and handles basic errors.
 *
 * @param {string} channel - The IPC channel to invoke.
 * @param {any} [data] - Optional data to send with the invocation.
 * @returns {Promise<{success: boolean, data, error | null}>} A promise that resolves with the result from the main process.
 */
/**
 * Safely invokes an IPC channel and returns a standardized result object.
 *
 * @async
 * @function
 * @param {string} channel - The IPC channel to invoke.
 * @param {*} data - The data to send with the IPC invocation.
 * @returns {Promise<{success: boolean, data: *, error: (string|null)}>}
 * An object containing the success status, returned data, and error message if any.
 */
const safeInvoke = async (channel, data) => {
  try {
    const result = await ipcRenderer.invoke(channel, data);
    if (result && typeof result === 'object' && 'success' in result) {
      return result;
    }
    return {
      success: true,
      data: result,
      error: null,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      success: false,
      data: null,
      error: errorMessage,
    };
  }
};

contextBridge.exposeInMainWorld('electronAPI', {
  // System & Health
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getSystemInfo: () => safeInvoke('get-system-info'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getSystemStatus: () => safeInvoke('get-system-status'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getAppVersion: () => safeInvoke('get-app-version'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  healthCheck: () => safeInvoke('health-check'),

  // Trading Engine Control
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  startBot: () => safeInvoke('start-bot'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  stopBot: () => safeInvoke('stop-bot'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getBotStatus: () => safeInvoke('get-bot-status'),

  // Market Data
  /**
     * @param {string} symbol
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  getMarketData: (symbol) => safeInvoke('get-market-data', {symbol}),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getMarketOverview: () => safeInvoke('get-market-overview'),
  /**
     * @param {string} symbol
     * @param {string} timeframe
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  getPriceHistory: (symbol, timeframe) => safeInvoke('get-price-history', {symbol, timeframe}),

  // Portfolio & Wallet
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getWalletBalance: () => safeInvoke('get-wallet-balance'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getPortfolioSummary: () => safeInvoke('get-portfolio-summary'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getAssetAllocation: () => safeInvoke('get-asset-allocation'),
  /**
     * @param {any} target
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  rebalancePortfolio: (target) => safeInvoke('rebalance-portfolio', target),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getPortfolioOptimization: () => safeInvoke('get-portfolio-optimization'),

  // Coin Management
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getCoins: () => safeInvoke('get-coins'),
  /**
     * @param {any} coin
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  saveCoin: (coin) => safeInvoke('save-coin', coin),
  /**
     * @param {any} coinId
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  deleteCoin: (coinId) => safeInvoke('delete-coin', coinId),
  /**
     * @param {any} coinId
     * @param {any} updates
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  updateCoin: (coinId, updates) => safeInvoke('update-coin', {coinId, updates}),

  // Grid Trading
  /**
     * @param {any} config
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  startGrid: (config) => safeInvoke('start-grid', config),
  /**
     * @param {any} gridId
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  stopGrid: (gridId) => safeInvoke('stop-grid', gridId),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  stopAllGrids: () => safeInvoke('stop-all-grids'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getGridPositions: () => safeInvoke('get-grid-positions'),
  /**
     * @param {any} gridId
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  getGridHistory: (gridId) => safeInvoke('get-grid-history', {gridId}),
  /**
     * @param {any} gridId
     * @param {any} config
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  updateGridConfig: (gridId, config) => safeInvoke('update-grid-config', {gridId, config}),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getGridPresets: () => safeInvoke('get-grid-presets'),
  /**
     * @param {any} preset
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  saveGridPreset: (preset) => safeInvoke('save-grid-preset', preset),

  // DCA Trading
  /**
     * @param {any} config
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  startDca: (config) => safeInvoke('start-dca', config),
  /**
     * @param {any} dcaId
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  stopDca: (dcaId) => safeInvoke('stop-dca', dcaId),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getDcaPositions: () => safeInvoke('get-dca-positions'),
  /**
     * @param {any} dcaId
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  getDcaHistory: (dcaId) => safeInvoke('get-dca-history', {dcaId}),
  /**
     * @param {any} timeframe
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  getPnlReport: (timeframe) => safeInvoke('get-pnl-report', {timeframe}),
  /**
     * @param {any} dcaId
     * @param {any} config
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  updateDcaConfig: (dcaId, config) => safeInvoke('update-dca-config', {dcaId, config}),

  // Analytics & History
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getTradingStats: () => safeInvoke('get-trading-stats'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getPerformanceMetrics: () => safeInvoke('get-performance-metrics'),
  /**
     * @param {any} limit
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  getTradeHistory: (limit) => safeInvoke('get-trade-history', {limit}),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getRiskMetrics: () => safeInvoke('get-risk-metrics'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getDrawdownAnalysis: () => safeInvoke('get-drawdown-analysis'),

  // Order Management
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getOpenOrders: () => safeInvoke('get-open-orders'),
  /**
     * @param {any} limit
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  getOrderHistory: (limit) => safeInvoke('get-order-history', {limit}),
  /**
     * @param {any} orderId
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  cancelOrder: (orderId) => safeInvoke('cancel-order', {orderId}),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  cancelAllOrders: () => safeInvoke('cancel-all-orders'),
  /**
     * @param {any} params
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  placeLimitOrder: (params) => safeInvoke('place-limit-order', params),
  /**
     * @param {any} params
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  placeMarketOrder: (params) => safeInvoke('place-market-order', params),

  // Whale Tracking
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getWhaleSignals: () => safeInvoke('get-whale-signals'),
  /**
     * @param {any} timeframe
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  getWhaleHistory: (timeframe) => safeInvoke('get-whale-history', {timeframe}),
  /**
     * @param {any} enabled
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  toggleWhaleTracking: (enabled) => safeInvoke('toggle-whale-tracking', {enabled}),
  /**
     * @param {any} address
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  addWhaleWallet: (address) => safeInvoke('add-whale-wallet', {address}),
  /**
     * @param {any} address
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  removeWhaleWallet: (address) => safeInvoke('remove-whale-wallet', {address}),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getTrackedWhales: () => safeInvoke('get-tracked-whales'),

  // Meme Coin Scanner
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  startMemeCoinScanner: () => safeInvoke('start-meme-coin-scanner'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  stopMemeCoinScanner: () => safeInvoke('stop-meme-coin-scanner'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getMemeCoinOpportunities: () => safeInvoke('get-meme-coin-opportunities'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getMemeCoinHistory: () => safeInvoke('get-meme-coin-history'),
  /**
     * @param {any} config
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  updateScannerConfig: (config) => safeInvoke('update-scanner-config', config),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getScannerStatus: () => safeInvoke('get-scanner-status'),

  // Risk Management
  /**
     * @param {any} params
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  setRiskParameters: (params) => safeInvoke('set-risk-parameters', params),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getRiskParameters: () => safeInvoke('get-risk-parameters'),

  // Settings
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getSettings: () => safeInvoke('get-settings'),
  /**
     * @param {string} key
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  getConfig: (key) => safeInvoke('get-config', key),
  /**
     * @param {any} settings
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  saveSettings: (settings) => safeInvoke('save-settings', settings),
  /**
     * @param {string} key
     * @param {any} value
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  updateConfig: (key, value) => safeInvoke('update-config', {
    key,
    value,
  }),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  resetSettings: () => safeInvoke('reset-settings'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  exportSettings: () => safeInvoke('export-settings'),
  /**
     * @param {any} settings
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  importSettings: (settings) => safeInvoke('import-settings', settings),

  // Exchange Management
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getExchanges: () => safeInvoke('get-exchanges'),
  /**
     * @param {any} config
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  addExchange: (config) => safeInvoke('add-exchange', config),
  /**
     * @param {any} exchangeId
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  removeExchange: (exchangeId) => safeInvoke('remove-exchange', {exchangeId}),
  /**
     * @param {any} exchangeId
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  testExchangeConnection: (exchangeId) => safeInvoke('test-exchange-connection', {exchangeId}),
  /**
     * @param {any} exchangeId
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  getExchangeBalances: (exchangeId) => safeInvoke('get-exchange-balances', {exchangeId}),

  // Arbitrage Engine
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getArbitrageOpportunities: () => safeInvoke('get-arbitrage-opportunities'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getArbitragePositions: () => safeInvoke('get-arbitrage-positions'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getArbitrageStats: () => safeInvoke('get-arbitrage-stats'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getArbitrageStatus: () => safeInvoke('get-arbitrage-status'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  startArbitrageEngine: () => safeInvoke('start-arbitrage-engine'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  stopArbitrageEngine: () => safeInvoke('stop-arbitrage-engine'),
  /**
     * @param {any} opportunity
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  executeArbitrage: (opportunity) => safeInvoke('execute-arbitrage', opportunity),
  /**
     * @param {any} config
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  updateArbitrageConfig: (config) => safeInvoke('update-arbitrage-config', config),

  // Opportunity Scanner
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getOpportunityScannerStats: () => safeInvoke('get-opportunity-scanner-stats'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getDetectedOpportunities: () => safeInvoke('get-detected-opportunities'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  startOpportunityScanner: () => safeInvoke('start-opportunity-scanner'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  stopOpportunityScanner: () => safeInvoke('stop-opportunity-scanner'),
  /**
     * @param {any} config
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  updateOpportunityScannerConfig: (config) => safeInvoke('update-opportunity-scanner-config', config),

  // Cross-Exchange Functionality
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getCrossExchangeBalances: () => safeInvoke('get-cross-exchange-balances'),
  /**
     * @param {any} exchange
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  getExchangePortfolio: (exchange) => safeInvoke('get-exchange-portfolio', {exchange}),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getPortfolioRiskMetrics: () => safeInvoke('get-portfolio-risk-metrics'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getPortfolioPerformance: () => safeInvoke('get-portfolio-performance'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getRebalancingOpportunities: () => safeInvoke('get-rebalancing-opportunities'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  startPortfolioMonitoring: () => safeInvoke('start-portfolio-monitoring'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  stopPortfolioMonitoring: () => safeInvoke('stop-portfolio-monitoring'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getExchangeHealth: () => safeInvoke('get-exchange-health'),
  /**
     * @param {any} config
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  rebalanceCrossExchangePortfolio: (config) => safeInvoke('rebalance-cross-exchange-portfolio', config),

  // Backup & Recovery
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  createBackup: () => safeInvoke('create-backup'),
  /**
     * @param {string} channel
     * @param {(data) => void} callback
     * @returns {() => void}
     */
  on: (channel, callback) => {
    /**
         * @param {import('electron').IpcRendererEvent} event
         * @param {any} data
         */
    const subscription = (event, data) => callback(data);
    ipcRenderer.on(channel, subscription);
    return () => {
      ipcRenderer.removeListener(channel, subscription);
    };
  },

  // Logging & Debugging
  /**
     * @param {any} level
     * @param {any} limit
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  getLogs: (level, limit) => safeInvoke('get-logs', {level, limit}),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  clearLogs: () => safeInvoke('clear-logs'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  exportLogs: () => safeInvoke('export-logs'),
  /**
     * @param {any} level
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  setLogLevel: (level) => safeInvoke('set-log-level', {level}),
  /**
     * @param {any} error
     * @param {any} context
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  reportError: (error, context) => safeInvoke('report-error', {error, context}),

  // Health Monitoring
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getSystemHealth: () => safeInvoke('get-system-health'),
  /**
     * @param {string} componentName
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  getComponentHealth: (componentName) => safeInvoke('get-component-health', componentName),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  startHealthMonitoring: () => safeInvoke('start-health-monitoring'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  stopHealthMonitoring: () => safeInvoke('stop-health-monitoring'),
  /**
     * @param {number} limit
     * @param {any} filter
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  getStatusReports: (limit, filter) => safeInvoke('get-status-reports', {limit, filter}),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getMonitoringStatistics: () => safeInvoke('get-monitoring-statistics'),
  /**
     * @param {string} [componentName]
     * @returns {Promise<{success: boolean, data, error | null}>}
     */
  runHealthCheck: (componentName) => safeInvoke('run-health-check', componentName),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  initializeTrading: () => safeInvoke('initialize-trading'),

  // Real-time Status Updates
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getRealTimeStatus: () => safeInvoke('get-real-time-status'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getActiveBots: () => safeInvoke('get-active-bots'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getSystemMetrics: () => safeInvoke('get-system-metrics'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getSystemAlerts: () => safeInvoke('get-system-alerts'),

  // Missing methods referenced in UI components
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getWhaleTrackingStatus: () => safeInvoke('get-whale-tracking-status'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getPerformanceHistory: (timeRange) => safeInvoke('get-performance-history', {timeRange}),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  startArbitrageScanning: () => safeInvoke('start-arbitrage-scanning'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  stopArbitrageScanning: () => safeInvoke('stop-arbitrage-scanning'),
  /**
     * @param {(data) => void} callback
     * @returns {() => void}
     */
  onArbitrageOpportunity: (callback) => {
    const subscription = (event, data) => callback(data);
    ipcRenderer.on('arbitrage-opportunity', subscription);
    return () => ipcRenderer.removeListener('arbitrage-opportunity', subscription);
  },
  /**
     * @param {(data) => void} callback
     * @returns {() => void}
     */
  onArbitrageExecuted: (callback) => {
    const subscription = (event, data) => callback(data);
    ipcRenderer.on('arbitrage-executed', subscription);
    return () => ipcRenderer.removeListener('arbitrage-executed', subscription);
  },

  // IPC Error Monitoring
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  getIpcErrorStatistics: () => safeInvoke('get-ipc-error-statistics'),
  /** @returns {Promise<{success: boolean, data, error | null}>} */
  resetIpcErrorStatistics: () => safeInvoke('reset-ipc-error-statistics'),
});

#!/usr/bin/env node

const AutonomousTrader = require('./trading/autonomous-trader');
const readline = require('readline');
const logger = require('./trading/shared/helpers/logger');

// Create readline interface for commands
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
  prompt: 'trader> ',
});

// Configuration
const config = {
  totalCapital: 1000, // Start with $1000 USDT
  maxGridBots: 5,
  exchanges: ['binance'], // Start with just Binance
  scanInterval: 30000, // Scan every 30 seconds
  adjustmentInterval: 300000, // Adjust every 5 minutes
  minVolume24h: 500000, // $500k minimum volume
  minVolatility: 0.03, // 3% minimum volatility
};

// Create trader instance
const trader = new AutonomousTrader(config);

// Setup event listeners
trader.on('initialized', () => {
  logger.info('🎯 System initialized');
});

trader.on('started', () => {
  logger.info('🚀 Autonomous trading started');
});

trader.on('scan-started', () => {
  logger.info('\n🔍 Scanning for opportunities...');
});

trader.on('scan-completed', (data) => {
  logger.info(`✅ Scan complete: ${data.opportunities} opportunities, ${data.deployed} bots active`);
});

trader.on('bot-deployed', (bot) => {
  logger.info('\n🤖 NEW BOT DEPLOYED:');
  logger.info(`   Symbol: ${bot.symbol}`);
  logger.info(`   Exchange: ${bot.exchange}`);
  logger.info(`   Investment: ${bot.parameters.investment.toFixed(2)}`);
  logger.info(`   Grid Range: ${bot.parameters.lowerPrice.toFixed(4)} - ${bot.parameters.upperPrice.toFixed(4)}`);
  logger.info(`   Grids: ${bot.parameters.gridCount}`);
});

trader.on('trade-executed', (data) => {
  logger.info(`💰 TRADE: ${data.symbol} +${data.profit.toFixed(2)} (Total: ${data.totalProfit.toFixed(2)})`);
});

trader.on('bot-adjusted', (data) => {
  logger.info(`🔧 Bot adjusted: ${data.botId.substring(0, 8)}...`);
});

trader.on('bot-stopped', (data) => {
  logger.info(`\n🛑 BOT STOPPED: ${data.botId.substring(0, 8)}...`);
  logger.info(`   Final profit: ${data.performance.profit.toFixed(2)}`);
  logger.info(`   Total trades: ${data.performance.trades}`);
});

// Display status periodically
setInterval(() => {
  const status = trader.getStatus();
  logger.info('\n📊 SYSTEM STATUS:');
  logger.info(`   Running: ${status.running ? 'YES' : 'NO'}`);
  logger.info(`   Active Bots: ${status.activeTrades || 0}`);
  logger.info(`   Total Profit: ${(status.performance?.totalProfit || 0).toFixed(2)}`);
  logger.info(`   Total Trades: ${status.performance?.totalTrades || 0}`);
  logger.info(`   Available Capital: ${(status.performance?.availableCapital || 0).toFixed(2)}`);
  logger.info('   Active Positions:');

  if (status.bots && Array.isArray(status.bots)) {
    status.bots.forEach(bot => {
      logger.info(`     - ${bot.symbol} on ${bot.exchange}: ${bot.profit.toFixed(2)} (${bot.trades} trades)`);
    });
  } else {
    logger.info('     - No active positions');
  }

  logger.info('');
}, 60000); // Every minute

// Command handler
async function handleCommand(command) {
  const [cmd] = command.trim().split(' ');

  switch (cmd.toLowerCase()) {
  case 'start':
    await trader.start();
    break;

  case 'stop':
    await trader.stop();
    break;

  case 'status': {
    const status = trader.getStatus();
    logger.info(JSON.stringify(status, null, 2));
    break;
  }
  case 'help':
    logger.info('Available commands:');
    logger.info('  start - Start autonomous trading');
    logger.info('  stop - Stop all trading');
    logger.info('  status - Show detailed status');
    logger.info('  exit - Exit the program');
    break;

  case 'exit':
  case 'quit':
    await trader.stop();
    process.exit(0);
    break;

  default:
    logger.info('Unknown command. Type "help" for available commands.');
  }
}

// Main function
async function main() {
  logger.info('🤖 AUTONOMOUS MEME COIN TRADER');
  logger.info('================================');
  logger.info(`Capital: ${config.totalCapital}`);
  logger.info(`Max Bots: ${config.maxGridBots}`);
  logger.info(`Exchanges: ${config.exchanges.join(', ')}`);
  logger.info('');
  logger.info('Initializing system...');
  try {
    await trader.initialize();
    logger.info('Type "help" for commands or "start" to begin trading');
    logger.info('');

    rl.prompt();

    rl.on('line', async (line) => {
      await handleCommand(line);
      rl.prompt();
    });

  } catch (error) {
    logger.error('Failed to initialize:', error.message);
    process.exit(1);
  }
}

// Handle exit
process.on('SIGINT', async () => {
  logger.info('Shutting down...');
  await trader.stop();
  process.exit(0);
});

// Start the application
main();

#!/usr/bin/env node

/**
 * ElectronTrader Reference Verification Script
 *
 * This script verifies that all trading engines, database managers, and backend files
 * are properly referenced in the build files, main.js, preload.js, and package.json.
 * It checks for missing require/import statements, incorrect file paths, and ensures
 * all backend components can be properly loaded during application startup.
 */

const fs = require('fs');
const path = require('path');

class ReferenceVerifier {
    // this.expectedImports = {
    'main.js': [
        './trading/tradingEngine',
        './trading/engines/trading/ArbitrageEngine',
        './trading/engines/analysis/OpportunityScanner',
        './trading/ccxt/engines/CCXT-Connector',
        './trading/engines/database/database-manager',
        './trading/engines/trading/MultiExchangePortfolioManager',
        './trading/databases/init_arbitrage_db']

    // Define the imports that should exist in each file
    'trading/tradingEngine.js': [
        './ccxt/engines/CCXT-Connector',
        './engines/trading/GridBotEngine',
        './engines/trading/WhaleTracker',
        './engines/trading/MemeCoinScanner',
        './engines/database/database-manager']
,
    'trading/engines/database/database-manager.js': [
        '../../shared/helpers/database-manager']
,

    constructor() {
        // this.baseDir = path.join(__dirname, '..');
        // this.errors = [];
        // this.warnings = [];
        // this.missingFiles = [];
        // this.brokenReferences = [];

        // Define all expected files and their paths
        // this.expectedFiles = {
        // Main process files
        'main.js'
        th.join(this.baseDir, 'main.js'),
            'preload.js'
        th.join(this.baseDir, 'preload.js'),
            'package.json'
        th.join(this.baseDir, 'package.json'),

            // Core trading engine
            'trading/tradingEngine.js'
        th.join(this.baseDir, 'trading/tradingEngine.js'),

            // CCXT engines
            'trading/ccxt/engines/CCXT-Connector.js'
        th.join(this.baseDir, 'trading/ccxt/engines/CCXT-Connector.js'),
            'trading/ccxt/engines/CCXT-Exchange-Manager.js'
        th.join(this.baseDir, 'trading/ccxt/engines/CCXT-Exchange-Manager.js'),

            // Trading engines
            'trading/engines/trading/GridBotEngine.js'
        th.join(this.baseDir, 'trading/engines/trading/GridBotEngine.js'),
            'trading/engines/trading/WhaleTracker.js'
        th.join(this.baseDir, 'trading/engines/trading/WhaleTracker.js'),
            'trading/engines/trading/MemeCoinScanner.js'
        th.join(this.baseDir, 'trading/engines/trading/MemeCoinScanner.js'),
            'trading/engines/trading/ArbitrageEngine.js'
        th.join(this.baseDir, 'trading/engines/trading/ArbitrageEngine.js'),
            'trading/engines/trading/MultiExchangePortfolioManager.js'
        th.join(this.baseDir, 'trading/engines/trading/MultiExchangePortfolioManager.js'),
            'trading/engines/trading/ProductionTradingExecutor.js'
        th.join(this.baseDir, 'trading/engines/trading/ProductionTradingExecutor.js'),

            // Database engines
            'trading/engines/database/database-manager.js'
        th.join(this.baseDir, 'trading/engines/database/database-manager.js'),
            'trading/engines/database/database-manager-enhanced.js'
        th.join(this.baseDir, 'trading/engines/database/database-manager-enhanced.js'),
            'trading/engines/database/sqlite-initializer.js'
        th.join(this.baseDir, 'trading/engines/database/sqlite-initializer.js'),

            // Analysis engines
            'trading/engines/analysis/PerformanceTracker.js'
        th.join(this.baseDir, 'trading/engines/analysis/PerformanceTracker.js'),
            'trading/engines/analysis/SentimentAnalyzer.js'
        th.join(this.baseDir, 'trading/engines/analysis/SentimentAnalyzer.js'),
            'trading/engines/analysis/OpportunityScanner.js'
        th.join(this.baseDir, 'trading/engines/analysis/OpportunityScanner.js'),

            // Shared helpers
            'trading/shared/helpers/database-manager.js'
        th.join(this.baseDir, 'trading/shared/helpers/database-manager.js'),
            'trading/shared/helpers/logger.js'
        th.join(this.baseDir, 'trading/shared/helpers/logger.js'),
            'trading/shared/helpers/errors.js'
        th.join(this.baseDir, 'trading/shared/helpers/errors.js'),

            // Database files
            'trading/databases/init_arbitrage_db.js'
        th.join(this.baseDir, 'trading/databases/init_arbitrage_db.js'),
            'trading/databases/trading_bot.db'
        th.join(this.baseDir, 'trading/databases/trading_bot.db')
    };
};
}

/**
 * Checks if a file exists at the given path
 */
fileExists(filePath)
{
    try {
        return fs.existsSync(filePath) && fs.statSync(filePath).isFile();
    } catch (error) {
        return false;
    }
}

/**
 * Reads and parses a JavaScript file to find require statements
 */
findRequireStatements(filePath)
{
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const requireRegex = /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
        const imports = [];
        let match;

        while ((match = requireRegex.exec(content)) !== null) {
            imports.push(match[1]);
        }

        return imports;
    } catch (error) {
        // this.errors.push(`Failed to read file ${filePath}: ${error.message}`);
        return [];
    }
}

/**
 * Resolves a relative require path to an absolute path
 */
resolveRequirePath(basePath, requirePath)
{
    if (requirePath.startsWith('.')) {
        const baseDir = path.dirname(basePath);
        let resolved = path.resolve(baseDir, requirePath);

        // Try with .js extension if it doesn't exist
        if (!this.fileExists(resolved)) {
            resolved = resolved + '.js';
        }

        return resolved;
    }
    return null; // External module
}

/**
 * Verifies that all expected files exist
 */
verifyFileExistence()
{
    // eslint-disable-next-line no-console

    // eslint-disable-next-line no-console


    // eslint-disable-next-line no-console



    // eslint-disable-next-line no-console




    console.log('🔍 Verifying file existence...\n');

    for (const [name, filePath] of Object.entries(this.expectedFiles)) {
        if (!this.fileExists(filePath)) {
            // this.missingFiles.push({name, path});
            // this.errors.push(`❌ Missing file: ${name} at ${filePath}`);
        } else {
            // eslint-disable-next-line no-console

            // eslint-disable-next-line no-console


            // eslint-disable-next-line no-console



            // eslint-disable-next-line no-console




            console.log(`✅ Found: ${name}`);
        }
    }
}

/**
 * Verifies require statements in key files
 */
verifyRequireStatements()
{
    // eslint-disable-next-line no-console

    // eslint-disable-next-line no-console


    // eslint-disable-next-line no-console



    // eslint-disable-next-line no-console




    console.log('\n🔗 Verifying require statements...\n');

    for (const [fileName, expectedImports] of Object.entries(this.expectedImports)) {
        const filePath = this.expectedFiles[fileName];

        if (!this.fileExists(filePath)) {
            continue; // Skip if file doesn't exist (already reported)
        }

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        // eslint-disable-next-line no-console





        console.log(`📋 Checking imports in ${fileName}:`);
        const actualImports = this.findRequireStatements(filePath);

        for (const expectedImport of expectedImports) {
            const found = actualImports.some(imp => imp.includes(expectedImport) || expectedImport.includes(imp));

            if (found) {
                // eslint-disable-next-line no-console

                // eslint-disable-next-line no-console


                // eslint-disable-next-line no-console



                // eslint-disable-next-line no-console




                console.log(`  ✅ ${expectedImport}`);
            } else {
                // this.brokenReferences.push({fileleName, import});
                // this.errors.push(`❌ Missing import in ${fileName}: ${expectedImport}`);
                // eslint-disable-next-line no-console

                // eslint-disable-next-line no-console


                // eslint-disable-next-line no-console



                // eslint-disable-next-line no-console




                console.log(`  ❌ ${expectedImport}`);
            }
        }

        // Check if imported files exist
        for (const actualImport of actualImports) {
            if (actualImport.startsWith('.')) {
                const resolvedPath = this.resolveRequirePath(filePath, actualImport);
                if (resolvedPath && !this.fileExists(resolvedPath)) {
                    // this.brokenReferences.push({fileleName, import, resolvedPath});
                    // this.errors.push(`❌ Broken import in ${fileName}: ${actualImport} -> ${resolvedPath}`);
                    // eslint-disable-next-line no-console

                    // eslint-disable-next-line no-console


                    // eslint-disable-next-line no-console



                    // eslint-disable-next-line no-console




                    console.log(`  ❌ Broken: ${actualImport} -> ${resolvedPath}`);
                }
            }
        }
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log('');
    }
}

/**
 * Verifies package.json dependencies
 */
verifyPackageJson()
{
    // eslint-disable-next-line no-console

    // eslint-disable-next-line no-console


    // eslint-disable-next-line no-console



    // eslint-disable-next-line no-console




    console.log('📦 Verifying package.json...\n');

    const packagePath = this.expectedFiles['package.json'];
    if (!this.fileExists(packagePath)) {
        return;
    }

    try {
        const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        const requiredDeps = [
            'electron',
            'react',
            'react-dom',
            '@mui/material',
            'ccxt',
            'sqlite3',
            'uuid',
            'joi',
            'lodash.merge',
            'framer-motion',
            'recharts'];

        const allDeps = {...packageJson.dependencies, ...packageJson.devDependencies};

        for (const dep of requiredDeps) {
            if (allDeps[dep]) {
                // eslint-disable-next-line no-console

                // eslint-disable-next-line no-console


                // eslint-disable-next-line no-console



                // eslint-disable-next-line no-console




                console.log(`✅ ${dep}: ${allDeps[dep]}`);
            } else {
                // this.errors.push(`❌ Missing dependency: ${dep}`);
                // eslint-disable-next-line no-console

                // eslint-disable-next-line no-console


                // eslint-disable-next-line no-console



                // eslint-disable-next-line no-console




                console.log(`❌ Missing: ${dep}`);
            }
        }

        // Check if main file is correctly set
        if (packageJson.main !== 'main.js') {
            // this.warnings.push(`⚠️ package.json main field should be 'main.js', found: ${packageJson.main}`);
        }

    } catch (error) {
        // this.errors.push(`Failed to parse package.json: ${error.message}`);
    }
}

/**
 * Generates a report of missing files and suggestions
 */
generateMissingFilesReport()
{
    if (this.missingFiles.length === 0) {
        return;
    }

    // eslint-disable-next-line no-console


    // eslint-disable-next-line no-console



    // eslint-disable-next-line no-console




    // eslint-disable-next-line no-console





    console.log('\n📋 Missing Files Report:\n');
    // eslint-disable-next-line no-console

    // eslint-disable-next-line no-console


    // eslint-disable-next-line no-console



    // eslint-disable-next-line no-console




    console.log('The following critical files are missing and need to be created:\n');

    for (const {name, path} of this.missingFiles) {
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log(`❌ ${name}`);
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log(`   Path: ${path}`);
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log(`   Category: ${this.categorizeFile(name)}`);
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log('');
    }
}

/**
 * Categorizes a file based on its path
 */
categorizeFile(fileName)
{
    if (fileName.includes('database')) return 'Database Management';
    if (fileName.includes('trading')) return 'Trading Engine';
    if (fileName.includes('analysis')) return 'Market Analysis';
    if (fileName.includes('ccxt')) return 'Exchange Integration';
    if (fileName.includes('shared/helpers')) return 'Shared Utilities';
    return 'Core Application';
}

/**
 * Generates solutions for fixing the issues
 */
generateSolutions()
{
    // eslint-disable-next-line no-console

    // eslint-disable-next-line no-console


    // eslint-disable-next-line no-console



    // eslint-disable-next-line no-console




    console.log('\n🔧 Recommended Solutions:\n');

    if (this.missingFiles.length > 0) {
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log('1. Create Missing Files:');
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log('   Run the following command to create missing files:');
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log('   node scripts/create-missing-files.js\n');
    }

    if (this.brokenReferences.length > 0) {
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log('2. Fix Broken References:');
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log('   Update import paths in the following files:');
        for (const {file, import} of this.brokenReferences) {
            // eslint-disable-next-line no-console

            // eslint-disable-next-line no-console


            // eslint-disable-next-line no-console



            // eslint-disable-next-line no-console




            console.log(`   - ${file}: ${imp}`);
        }
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log('');
    }

    if (this.errors.some(e => e.includes('dependency'))) {
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log('3. Install Missing Dependencies:');
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log('   npm install');
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log('   npm install --save-dev (for dev dependencies)\n');
    }

    // eslint-disable-next-line no-console


    // eslint-disable-next-line no-console



    // eslint-disable-next-line no-console




    // eslint-disable-next-line no-console





    console.log('4. Verify Build Process:');
    // eslint-disable-next-line no-console

    // eslint-disable-next-line no-console


    // eslint-disable-next-line no-console



    // eslint-disable-next-line no-console




    console.log('   npm run build');
    // eslint-disable-next-line no-console

    // eslint-disable-next-line no-console


    // eslint-disable-next-line no-console



    // eslint-disable-next-line no-console




    console.log('   npm run electron\n');
}

/**
 * Runs the complete verification process
 */
run()
{
    // eslint-disable-next-line no-console

    // eslint-disable-next-line no-console


    // eslint-disable-next-line no-console



    // eslint-disable-next-line no-console




    console.log('🚀 ElectronTrader Reference Verification\n');
    // eslint-disable-next-line no-console

    // eslint-disable-next-line no-console


    // eslint-disable-next-line no-console



    // eslint-disable-next-line no-console




    console.log('='.repeat(50));

    // this.verifyFileExistence();
    // this.verifyRequireStatements();
    // this.verifyPackageJson();

    // eslint-disable-next-line no-console


    // eslint-disable-next-line no-console



    // eslint-disable-next-line no-console




    // eslint-disable-next-line no-console





    console.log('\n' + '='.repeat(50));
    // eslint-disable-next-line no-console

    // eslint-disable-next-line no-console


    // eslint-disable-next-line no-console



    // eslint-disable-next-line no-console




    console.log('📊 Verification Summary\n');

    if (this.errors.length === 0 && this.warnings.length === 0) {
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log('🎉 All references are properly configured!');
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log('✅ All expected files exist');
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log('✅ All imports are valid');
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log('✅ Package.json is properly configured');
    } else {
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log(`❌ Found ${this.errors.length} error(s)`);
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        // eslint-disable-next-line no-console




        console.log(`⚠️ Found ${this.warnings.length} warning(s)`);

        if (this.errors.length > 0) {
            // eslint-disable-next-line no-console

            // eslint-disable-next-line no-console


            // eslint-disable-next-line no-console



            // eslint-disable-next-line no-console




            console.log('\nErrors:');
            // this.errors.forEach(error => // eslint-disable-next-line no-console
            // eslint-disable-next-line no-console

            // eslint-disable-next-line no-console


            // eslint-disable-next-line no-console



            console.log(`  ${error}`)
        )
            ;
        }

        if (this.warnings.length > 0) {
            // eslint-disable-next-line no-console

            // eslint-disable-next-line no-console


            // eslint-disable-next-line no-console



            // eslint-disable-next-line no-console




            console.log('\nWarnings:');
            // this.warnings.forEach(warning => // eslint-disable-next-line no-console
            // eslint-disable-next-line no-console

            // eslint-disable-next-line no-console


            // eslint-disable-next-line no-console



            console.log(`  ${warning}`)
        )
            ;
        }
    }

    // this.generateMissingFilesReport();
    // this.generateSolutions();

    // eslint-disable-next-line no-console


    // eslint-disable-next-line no-console



    // eslint-disable-next-line no-console




    // eslint-disable-next-line no-console





    console.log('\n' + '='.repeat(50));
    return this.errors.length === 0;
}
}

// Run the verification if this script is executed directly
if (require.main === module) {
    const verifier = new ReferenceVerifier();
    const success = verifier.run();
    process.exit(success ? 0);
}

module.exports = ReferenceVerifier;

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON>o', sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-form {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.left-panel, .right-panel {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.portfolio-summary, .order-form, .position-manager, .trade-history {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.positive {
    color: green;
}

.negative {
    color: red;
}

form {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

input, select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

button {
    padding: 0.75rem;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

button:hover {
    background-color: #0056b3;
}

.error {
    color: red;
    margin: 1rem 0;
}

.loading {
    text-align: center;
    padding: 2rem;
}

/* Layout for sidebar and main content */
.sidebar {
    width: 200px;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    background-color: #1e1e1e;
    padding: 1rem;
    overflow-y: auto;
    color: #ddd;
}

.sidebar ul {
    list-style: none;
    padding: 0;
}

.sidebar li {
    margin-bottom: 1rem;
}

.sidebar a {
    color: #ddd;
    text-decoration: none;
    display: block;
    padding: 0.5rem;
    border-radius: 4px;
}

.sidebar a.active {
    color: #00eaff;
    background-color: rgba(0, 234, 255, 0.1);
}

.content {
    margin-left: 200px;
    padding: 2rem;
}

/* Startup optimization styles */
:root {
    --animation-duration: 0.3s;
    --startup-bg: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
}

.app-startup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: var(--startup-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.app-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
}

/* Performance optimized animations */
.optimized-animation {
    animation-duration: var(--animation-duration);
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    :root {
        --animation-duration: 0s;
    }

    * {
        animation-duration: 0s !important;
        transition-duration: 0s !important;
    }
}

/* Low-end device optimizations */
@media (max-width: 768px), (pointer: coarse) {
    .App {
        font-size: 14px;
    }

    .sidebar {
        width: 200px;
    }

    .content {
        padding: 0.5rem;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .app-startup {
        background: #000;
        color: #fff;
    }
}

/* Print styles for performance reports */
@media print {
    .app-startup,
    .performance-monitoring-panel {
        display: none !important;
    }
}

/* Lazy loading placeholder styles */
.lazy-loading-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Startup progress specific styles */
.startup-progress-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

/* Component load optimization */
.component-loading {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity var(--animation-duration), transform var(--animation-duration);
}

.component-loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Bundle optimization indicators */
.bundle-optimized {
    position: relative;
}

.bundle-optimized::after {
    content: "optimized";
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 10px;
    color: #4caf50;
    background: rgba(76, 175, 80, 0.1);
    padding: 2px 4px;
    border-radius: 2px;
}
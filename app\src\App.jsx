import React, { Suspense, useEffect, useState } from 'react';
import { Navigate, NavLink, Route, Routes } from 'react-router-dom';
import { AuthProvider, useAuth } from './auth/AuthContext';
import EnhancedErrorBoundary from './components/EnhancedErrorBoundary';
import {
  DashboardErrorBoundary,
  PortfolioErrorBoundary,
  TradingErrorBoundary,
} from './components/ApplicationErrorBoundary';
import {
  AutonomousDashboard,
  BotDashboard,
  Dashboard,
  ErrorBoundaryTest,
  Login,
  PortfolioSummary,
  PositionManager,
  preloadAllComponents,
  preloadBasedOnRoute,
  preloadBasedOnUsage,
  preloadOnInteraction,
  TradeHistory,
  TradingDashboard,
  withSuspense,
} from './components/LazyComponents';
import LoadingFallback from './components/LoadingFallback';
import './App.css';
import './styles/system-error-handler.css';
import { PerformanceMonitoringPanel } from './utils/BuildOptimizationMonitor.jsx';
import { useStartupOptimization } from './utils/StartupOptimizer';
import StartupProgressPanel from './components/StartupProgressPanel';
import SystemHealthIndicator from './components/SystemHealthIndicator';
import ErrorNotificationSystem from './components/ErrorNotificationSystem';
import realTimeStatusService from './services/realTimeStatusService';
import ipcService from './services/ipcService';
import logger from './utils/logger';

// Wrap components with Suspense for consistent loading
const SuspendedDashboard = withSuspense(Dashboard);
const SuspendedAutonomousDashboard = withSuspense(AutonomousDashboard);
const SuspendedTradingDashboard = withSuspense(TradingDashboard);
const SuspendedPositionManager = withSuspense(PositionManager);
const SuspendedTradeHistory = withSuspense(TradeHistory);
const SuspendedBotDashboard = withSuspense(BotDashboard);
const SuspendedPortfolioSummary = withSuspense(PortfolioSummary);
const SuspendedLogin = withSuspense(Login);
const SuspendedErrorBoundaryTest = withSuspense(ErrorBoundaryTest);

// Loading component for initial app load
function AppLoadingFallback() {
  return (
    <div className="app-loading">
      <LoadingFallback message="Loading application..." />
    </div>
  );
}

function ProtectedRoute({ children }) {
  const { user, loading } = useAuth();
  if (loading) {
    return <LoadingFallback message="Checking authentication..." />;
  }
  return user ? children : <Navigate to="/login" />;
}

/**
 * Main application component.
 *
 * @description Sets up the routing and navigation for the trading dashboard application,
 * including authentication handling and protected routes. Uses optimized lazy loading
 * with preloading strategies for improved performance.
 *
 * @returns {React.ReactElement} The rendered application component with routes and navigation.
 */
function App() {
  const [isInitializing, setIsInitializing] = useState(true);
  const [startupProgress, setStartupProgress] = useState(0);
  const { optimizer, strategy, report } = useStartupOptimization();

  // Enhanced preloading strategy with startup optimization
  useEffect(() => {
    const initializeApp = () => {
      try {
        // Initialize real-time status service once
        realTimeStatusService.initialize(ipcService);
        realTimeStatusService.setupIPCListeners();

        // Apply startup optimizations first
        if (optimizer) {
          const appliedStrategy = optimizer.applyStartupOptimizations();
          logger.info(`🚀 Applied ${appliedStrategy} startup optimization strategy`);
        }

        // Initial preload with network awareness and usage patterns
        const preloadTimer = setTimeout(() => {
          preloadAllComponents();
          preloadBasedOnUsage(); // Use historical usage patterns
        }, strategy === 'aggressive' ? 1000 : 3000);

        // Preload based on current route
        preloadBasedOnRoute(window.location.pathname);

        // Set up interaction-based preloading
        preloadOnInteraction();

        // Simulate startup progress
        const progressInterval = setInterval(() => {
          setStartupProgress(prev => {
            if (prev >= 100) {
              clearInterval(progressInterval);
              setIsInitializing(false);
              return 100;
            }
            return prev + 10;
          });
        }, 200);

        return () => {
          clearTimeout(preloadTimer);
          clearInterval(progressInterval);
        };
      } catch (error) {
        logger.error('Failed to initialize app:', error);
        setIsInitializing(false);
      }
    };

    initializeApp();
  }, [optimizer, strategy]);

  // Show startup progress during initialization
  if (isInitializing) {
    return (
      <div className="app-initializing">
        <StartupProgressPanel progress={startupProgress} />
        {report && <PerformanceMonitoringPanel report={report} />}
      </div>
    );
  }

  return (
    <AuthProvider>
      <EnhancedErrorBoundary>
        <div className="app">
          <SystemHealthIndicator />
          <ErrorNotificationSystem />

          <Suspense fallback={<AppLoadingFallback />}>
            <Routes>
              {/* Public Routes */}
              <Route path="/login" element={<SuspendedLogin />} />

              {/* Protected Routes */}
              <Route path="/" element={
                <ProtectedRoute>
                  <AppLayout />
                </ProtectedRoute>
              } />
              <Route path="/dashboard" element={
                <ProtectedRoute>
                  <DashboardErrorBoundary>
                    <SuspendedDashboard />
                  </DashboardErrorBoundary>
                </ProtectedRoute>
              } />
              <Route path="/autonomous" element={
                <ProtectedRoute>
                  <SuspendedAutonomousDashboard />
                </ProtectedRoute>
              } />
              <Route path="/trading" element={
                <ProtectedRoute>
                  <TradingErrorBoundary>
                    <SuspendedTradingDashboard />
                  </TradingErrorBoundary>
                </ProtectedRoute>
              } />
              <Route path="/portfolio" element={
                <ProtectedRoute>
                  <PortfolioErrorBoundary>
                    <SuspendedPortfolioSummary />
                  </PortfolioErrorBoundary>
                </ProtectedRoute>
              } />
              <Route path="/positions" element={
                <ProtectedRoute>
                  <SuspendedPositionManager />
                </ProtectedRoute>
              } />
              <Route path="/history" element={
                <ProtectedRoute>
                  <SuspendedTradeHistory />
                </ProtectedRoute>
              } />
              <Route path="/bots" element={
                <ProtectedRoute>
                  <SuspendedBotDashboard />
                </ProtectedRoute>
              } />
              <Route path="/error-test" element={
                <ProtectedRoute>
                  <SuspendedErrorBoundaryTest />
                </ProtectedRoute>
              } />
            </Routes>
          </Suspense>
        </div>
      </EnhancedErrorBoundary>
    </AuthProvider>
  );
}

/**
 * Main layout component with navigation
 */
function AppLayout() {
  return (
    <div className="app-layout">
      <nav className="app-nav">
        <div className="nav-brand">
          <h1>ElectronTrader</h1>
        </div>
        <div className="nav-links">
          <NavLink to="/dashboard" className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}>
            Dashboard
          </NavLink>
          <NavLink to="/autonomous" className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}>
            Autonomous
          </NavLink>
          <NavLink to="/trading" className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}>
            Trading
          </NavLink>
          <NavLink to="/portfolio" className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}>
            Portfolio
          </NavLink>
          <NavLink to="/positions" className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}>
            Positions
          </NavLink>
          <NavLink to="/history" className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}>
            History
          </NavLink>
          <NavLink to="/bots" className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}>
            Bots
          </NavLink>
        </div>
      </nav>
      <main className="app-main">
        <Navigate to="/dashboard" replace />
      </main>
    </div>
  );
}

export default App;
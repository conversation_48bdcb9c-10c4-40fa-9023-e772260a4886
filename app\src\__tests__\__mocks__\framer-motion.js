// Mock framer-motion for testing
import React from 'react';

/**
 * Creates a mock motion component for testing purposes.
 *
 * @param {string} tag - The HTML tag name to create a mock component for.
 * @returns {React.ComponentType<any>} A React component that mimics a framer-motion component
 *                            with the specified HTML tag.
 */
const createMockMotionComponent = (tag) => {
  const component = React.forwardRef((
    /** @type {React.PropsWithChildren<Record<string, any>>} */
    props,
    ref,
  ) => {
    return React.createElement(tag, {...props, ref}, props.children);
  });
  component.displayName = `motion.${tag}`;
  return component;
};

export const motion = {
  div: createMockMotionComponent('div'),
  span: createMockMotionComponent('span'),
  button: createMockMotionComponent('button'),
  a: createMockMotionComponent('a'),
  img: createMockMotionComponent('img'),
  svg: createMockMotionComponent('svg'),
  path: createMockMotionComponent('path'),
  circle: createMockMotionComponent('circle'),
  rect: createMockMotionComponent('rect'),
  g: createMockMotionComponent('g'),
};

export const AnimatePresence = (/** @type {React.PropsWithChildren<Record<string, any>>} */ props) => React.createElement(React.Fragment, null, props.children);
AnimatePresence.displayName = 'AnimatePresence';

export const useMotionValue = () => ({set: jest.fn(), get: jest.fn()});

/** @param {any} value */
export const useSpring = (value) => value;

/**
 * @param {any} value
 * @param {any} inputRange
 * @param {any} outputRange
 */
export const useTransform = (value, inputRange, outputRange) => outputRange[0] || value;

/**
 * Mocked version of `useAnimation` hook from framer-motion.
 *
 * Provides a mock `animation` object with `start`, `stop`, and `set` methods,
 * each of which is a jest mock function.
 *
 * @returns {Object} Mocked animation object with `start`, `stop`, and `set` methods.
 */
export const useAnimation = () => ({
  start: jest.fn(),
  stop: jest.fn(),
  set: jest.fn(),
});

export const useScroll = () => ({scrollY: {get: jest.fn(), set: jest.fn()}});
export const useInView = () => true;
export const useReducedMotion = () => false;

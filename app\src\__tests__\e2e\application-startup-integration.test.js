/**
 * @fileoverview Application Startup Integration Test
 * Tests the complete application startup workflow from main.js to UI rendering
 * Verifies Start button triggers complete trading system initialization
 * Confirms all IPC channels work correctly
 *
 * Requirements Coverage:
 * - 1.1, 1.2, 1.3, 1.4: File structure and component organization
 * - 4.1, 4.4, 4.5: Start button workflow and error handling
 */

const path = require('path');
const fs = require('fs');

// Mock electron for testing
jest.mock('electron', () => ({
  app: {
    whenReady: jest.fn().mockResolvedValue(true),
    on: jest.fn(),
    quit: jest.fn(),
    getVersion: jest.fn().mockReturnValue('1.0.0'),
    getPath: jest.fn((name) => `/mock/${name}`),
  },
  BrowserWindow: jest.fn().mockImplementation(() => ({
    loadURL: jest.fn(),
    webContents: {
      send: jest.fn(),
      on: jest.fn(),
      openDevTools: jest.fn(),
    },
    on: jest.fn(),
    show: jest.fn(),
    hide: jest.fn(),
    close: jest.fn(),
  })),
  ipcMain: {
    handle: jest.fn(),
    on: jest.fn(),
  },
}));

// Mock logger
jest.mock('../../../trading/shared/helpers/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
}));

describe('Application Startup Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('1. File Structure and Component Organization (Req 1.1-1.4)', () => {
    test('should have all UI components in correct locations (Req 1.1)', () => {
      const uiComponents = [
        '../../components/AutonomousDashboard.jsx',
        '../../components/SystemStatusPanel.jsx',
        '../../components/TradingStatusIndicator.jsx',
        '../../services/ipcService.js',
        '../../App.js',
        '../../index.jsx',
      ];

      uiComponents.forEach(component => {
        const componentPath = path.resolve(__dirname, component);
        expect(fs.existsSync(componentPath)).toBe(true);
      });
    });

    test('should have trading system files in correct locations (Req 1.2)', () => {
      const tradingComponents = [
        '../../../trading/engines/trading/orchestration/TradingOrchestrator.js',
        '../../../trading/engines/ai/AutonomousTrader.js',
        '../../../trading/engines/trading/MemeCoinScanner.js',
        '../../../trading/analysis/SentimentAnalyzer.js',
        '../../../trading/analysis/PerformanceTracker.js',
      ];

      tradingComponents.forEach(component => {
        const componentPath = path.resolve(__dirname, component);
        expect(fs.existsSync(componentPath)).toBe(true);
      });
    });

    test('should have configuration files in appropriate locations (Req 1.3)', () => {
      const configFiles = [
        '../../../package.json',
        '../../../trading/package.json',
        '../../../config.json',
      ];

      configFiles.forEach(config => {
        const configPath = path.resolve(__dirname, config);
        expect(fs.existsSync(configPath)).toBe(true);
      });
    });

    test('should have shared utilities accessible (Req 1.4)', () => {
      const sharedUtils = [
        '../../../trading/shared/helpers/logger.js',
        '../../utils/GlobalErrorHandler.js',
        '../../../preload.js',
      ];

      sharedUtils.forEach(util => {
        const utilPath = path.resolve(__dirname, util);
        expect(fs.existsSync(utilPath)).toBe(true);
      });
    });
  });

  describe('2. Main Process Initialization', () => {
    test('should have main.js with proper structure', () => {
      const mainPath = path.resolve(__dirname, '../../../main.js');
      expect(fs.existsSync(mainPath)).toBe(true);

      const mainContent = fs.readFileSync(mainPath, 'utf8');

      // Verify main.js has required components
      expect(mainContent).toMatch(/createWindow/);
      expect(mainContent).toMatch(/TradingOrchestrator/);
      expect(mainContent).toMatch(/ipcMain\.handle/);
      expect(mainContent).toMatch(/BrowserWindow/);
    });

    test('should have preload script with proper API exposure', () => {
      const preloadPath = path.resolve(__dirname, '../../../preload.js');
      expect(fs.existsSync(preloadPath)).toBe(true);

      const preloadContent = fs.readFileSync(preloadPath, 'utf8');

      // Verify preload script exposes required APIs
      expect(preloadContent).toMatch(/contextBridge\.exposeInMainWorld/);
      expect(preloadContent).toMatch(/electronAPI/);
      expect(preloadContent).toMatch(/startBot|start-bot/);
      expect(preloadContent).toMatch(/stopBot|stop-bot/);
    });

    test('should initialize TradingOrchestrator successfully', async () => {
      const TradingOrchestrator = require('../../../trading/engines/trading/orchestration/TradingOrchestrator');

      const orchestrator = new TradingOrchestrator();

      // Test initialization
      const initResult = await orchestrator.initialize();
      expect(initResult).toBe(true);
      expect(orchestrator.initialized).toBe(true);
    });
  });

  describe('3. Start Button Workflow (Req 4.1)', () => {
    test('should have functional Start button in AutonomousDashboard', () => {
      const dashboardPath = path.resolve(__dirname, '../../components/AutonomousDashboard.jsx');
      const dashboardContent = fs.readFileSync(dashboardPath, 'utf8');

      // Verify Start button exists and has click handler
      expect(dashboardContent).toMatch(/handleStartAutonomous|handleStart/);
      expect(dashboardContent).toMatch(/START SYSTEM|Start|start-bot/);
      expect(dashboardContent).toMatch(/onClick/);
    });

    test('should trigger TradingOrchestrator initialization on start', async () => {
      const TradingOrchestrator = require('../../../trading/engines/trading/orchestration/TradingOrchestrator');
      const orchestrator = new TradingOrchestrator();

      // Test start workflow
      expect(orchestrator.running).toBe(false);

      await orchestrator.initialize();
      expect(orchestrator.initialized).toBe(true);

      await orchestrator.start();
      expect(orchestrator.running).toBe(true);
    });

    test('should initialize components in correct order', async () => {
      const TradingOrchestrator = require('../../../trading/engines/trading/orchestration/TradingOrchestrator');
      const orchestrator = new TradingOrchestrator();

      // Track initialization steps
      const initSteps = [];

      // Mock initialization methods to track order
      const originalInit = orchestrator.initialize.bind(orchestrator);
      orchestrator.initialize = async function() {
        initSteps.push('initialize');
        return await originalInit();
      };

      const originalStart = orchestrator.start.bind(orchestrator);
      orchestrator.start = async function() {
        initSteps.push('start');
        return await originalStart();
      };

      // Run initialization
      await orchestrator.initialize();
      await orchestrator.start();

      // Verify correct order
      expect(initSteps).toEqual(['initialize', 'start']);
    });
  });

  describe('4. IPC Channel Functionality', () => {
    test('should have all required IPC handlers', () => {
      const requiredHandlers = [
        'start-bot',
        'stop-bot',
        'get-bot-status',
        'get-system-status',
        'get-real-time-status',
        'health-check',
        'get-portfolio-summary',
        'get-trading-stats',
      ];

      const mainPath = path.resolve(__dirname, '../../../main.js');
      const mainContent = fs.readFileSync(mainPath, 'utf8');

      // Verify all required handlers are present in main.js
      requiredHandlers.forEach(handler => {
        expect(mainContent).toMatch(new RegExp(`['"]${handler}['"]`));
      });
    });

    test('should handle IPC calls correctly', async () => {
      const TradingOrchestrator = require('../../../trading/engines/trading/orchestration/TradingOrchestrator');
      const orchestrator = new TradingOrchestrator();
      await orchestrator.initialize();

      // Test various IPC methods
      const status = orchestrator.getStatus();
      expect(status).toBeDefined();
      expect(status).toHaveProperty('initialized');
      expect(status).toHaveProperty('running');

      const systemStatus = orchestrator.getSystemStatus();
      expect(systemStatus).toBeDefined();
      expect(systemStatus).toHaveProperty('isRunning');

      const healthCheck = await orchestrator.healthCheck();
      expect(healthCheck).toBeDefined();
      expect(healthCheck).toHaveProperty('status');
    });

    test('should handle concurrent IPC calls', async () => {
      const TradingOrchestrator = require('../../../trading/engines/trading/orchestration/TradingOrchestrator');
      const orchestrator = new TradingOrchestrator();
      await orchestrator.initialize();

      // Simulate multiple concurrent calls
      const concurrentCalls = [
        orchestrator.getStatus(),
        orchestrator.getSystemStatus(),
        orchestrator.healthCheck(),
        orchestrator.getPortfolioSummary(),
        orchestrator.getTradingStats(),
      ];

      const results = await Promise.all(concurrentCalls);

      // Verify all calls completed successfully
      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result).toBeDefined();
      });
    });
  });

  describe('5. Error Handling (Req 4.4, 4.5)', () => {
    test('should handle initialization errors gracefully', async () => {
      const TradingOrchestrator = require('../../../trading/engines/trading/orchestration/TradingOrchestrator');
      const orchestrator = new TradingOrchestrator();

      // Mock a failing initialization step
      const originalInitDb = orchestrator.initializeDatabase;
      orchestrator.initializeDatabase = jest.fn().mockRejectedValue(
        new Error('Database connection failed'),
      );

      // Test error handling during initialization
      try {
        await orchestrator.initialize();
      } catch (error) {
        expect(error.message).toBe('Database connection failed');
      }

      // Restore original method and verify recovery
      orchestrator.initializeDatabase = originalInitDb;
      const retryResult = await orchestrator.initialize();
      expect(retryResult).toBe(true);
    });

    test('should have error boundaries in UI components', () => {
      const errorBoundaryPaths = [
        '../../components/ErrorBoundary.jsx',
        '../../components/ApplicationErrorBoundary.jsx',
        '../../components/EnhancedErrorBoundary.jsx',
      ];

      let foundErrorBoundaries = 0;
      errorBoundaryPaths.forEach(boundaryPath => {
        const fullPath = path.resolve(__dirname, boundaryPath);
        if (fs.existsSync(fullPath)) {
          foundErrorBoundaries++;
        }
      });

      expect(foundErrorBoundaries).toBeGreaterThan(0);
    });

    test('should have error reporting service', () => {
      const errorReporterPath = path.resolve(__dirname, '../../services/ErrorReporter.js');
      expect(fs.existsSync(errorReporterPath)).toBe(true);
    });
  });

  describe('6. Real-time Status Updates', () => {
    test('should provide real-time status', async () => {
      const TradingOrchestrator = require('../../../trading/engines/trading/orchestration/TradingOrchestrator');
      const orchestrator = new TradingOrchestrator();
      await orchestrator.initialize();

      const realTimeStatus = await orchestrator.getRealTimeStatus();
      expect(realTimeStatus).toBeDefined();
      expect(realTimeStatus).toHaveProperty('system');
      expect(realTimeStatus).toHaveProperty('timestamp');
    });

    test('should track component health', async () => {
      const TradingOrchestrator = require('../../../trading/engines/trading/orchestration/TradingOrchestrator');
      const orchestrator = new TradingOrchestrator();
      await orchestrator.initialize();

      const componentHealth = await orchestrator.getComponentHealth();
      expect(componentHealth).toBeDefined();
      expect(typeof componentHealth).toBe('object');
    });

    test('should report startup progress', async () => {
      const TradingOrchestrator = require('../../../trading/engines/trading/orchestration/TradingOrchestrator');
      const orchestrator = new TradingOrchestrator();

      // Mock main window for progress reporting
      global.mainWindow = {
        webContents: {
          send: jest.fn(),
        },
      };

      await orchestrator.initialize();

      // Verify progress updates were sent
      expect(global.mainWindow.webContents.send).toHaveBeenCalled();
    });
  });

  describe('7. Database Integration', () => {
    test('should have database operations available', async () => {
      const TradingOrchestrator = require('../../../trading/engines/trading/orchestration/TradingOrchestrator');
      const orchestrator = new TradingOrchestrator();
      await orchestrator.initialize();

      // Test database operations
      const coins = orchestrator.getCoins();
      expect(Array.isArray(coins)).toBe(true);

      const transactions = orchestrator.getTradingTransactions();
      expect(Array.isArray(transactions)).toBe(true);
    });

    test('should have database initializer', () => {
      const dbInitPath = path.resolve(__dirname, '../../../trading/data/UnifiedDatabaseInitializer.js');
      expect(fs.existsSync(dbInitPath)).toBe(true);
    });
  });

  describe('8. Configuration Management', () => {
    test('should have configuration manager', () => {
      const configManagerPath = path.resolve(__dirname, '../../../trading/config/enhanced-config-manager.js');
      expect(fs.existsSync(configManagerPath)).toBe(true);
    });

    test('should load configuration', async () => {
      const TradingOrchestrator = require('../../../trading/engines/trading/orchestration/TradingOrchestrator');
      const orchestrator = new TradingOrchestrator();
      await orchestrator.initialize();

      const config = await orchestrator.getConfig('trading');
      expect(config).toBeDefined();

      const settings = await orchestrator.getSettings();
      expect(settings).toBeDefined();
    });
  });

  describe('9. Performance Requirements', () => {
    test('should initialize within reasonable time', async () => {
      const TradingOrchestrator = require('../../../trading/engines/trading/orchestration/TradingOrchestrator');
      const orchestrator = new TradingOrchestrator();

      const startTime = Date.now();
      await orchestrator.initialize();
      await orchestrator.start();
      const endTime = Date.now();

      const initTime = endTime - startTime;
      expect(initTime).toBeLessThan(5000); // Should initialize within 5 seconds
    });

    test('should handle multiple operations efficiently', async () => {
      const TradingOrchestrator = require('../../../trading/engines/trading/orchestration/TradingOrchestrator');
      const orchestrator = new TradingOrchestrator();
      await orchestrator.initialize();

      // Test multiple rapid operations
      const operations = [];
      for (let i = 0; i < 10; i++) {
        operations.push(orchestrator.getStatus());
      }

      const startTime = Date.now();
      const results = await Promise.all(operations);
      const endTime = Date.now();

      expect(results).toHaveLength(10);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });
  });

  describe('10. System Integration Validation', () => {
    test('should have all required system components', () => {
      const systemComponents = [
        '../../../main.js',
        '../../../preload.js',
        '../../App.js',
        '../../index.jsx',
        '../../../trading/engines/trading/orchestration/TradingOrchestrator.js',
        '../../../package.json',
      ];

      systemComponents.forEach(component => {
        const componentPath = path.resolve(__dirname, component);
        expect(fs.existsSync(componentPath)).toBe(true);
      });
    });

    test('should validate complete startup workflow', async () => {
      // This test validates the complete workflow from file structure to running system
      const TradingOrchestrator = require('../../../trading/engines/trading/orchestration/TradingOrchestrator');

      // Step 1: Verify file structure
      const mainPath = path.resolve(__dirname, '../../../main.js');
      expect(fs.existsSync(mainPath)).toBe(true);

      // Step 2: Initialize orchestrator
      const orchestrator = new TradingOrchestrator();
      expect(orchestrator).toBeDefined();

      // Step 3: Initialize system
      const initResult = await orchestrator.initialize();
      expect(initResult).toBe(true);
      expect(orchestrator.initialized).toBe(true);

      // Step 4: Start system
      const startResult = await orchestrator.start();
      expect(startResult).toBe(true);
      expect(orchestrator.running).toBe(true);

      // Step 5: Verify system status
      const status = orchestrator.getStatus();
      expect(status.initialized).toBe(true);
      expect(status.running).toBe(true);

      // Step 6: Test IPC functionality
      const systemStatus = orchestrator.getSystemStatus();
      expect(systemStatus.isRunning).toBe(true);

      // Step 7: Stop system
      const stopResult = await orchestrator.stop();
      expect(stopResult).toBe(true);
      expect(stopResult).toBe(true);
    });
  });
});
/**
 * @jest-environment jsdom
 */
/* eslint-disable no-unused-vars */

/**
 * End-to-End Error Handling Workflow Tests
 * Tests the complete error handling system from UI to backend
 */

import React from 'react';
import {act, render, screen, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import {BrowserRouter} from 'react-router-dom';
import App from '../../App';
import SystemWideErrorHandler from '../../utils/SystemWideErrorHandler';
import GlobalErrorHandler from '../../utils/GlobalErrorHandler';

// Mock electron API
const mockElectronAPI = {
  ipcRenderer: {
    invoke: jest.fn(),
    on: jest.fn(),
    removeAllListeners: jest.fn(),
  },
};

Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
});

// Test component that throws errors
const ErrorThrowingComponent = ({shouldThrow = false, message = 'Test Error'}) => {
  if (shouldThrow) {
    throw new Error(message);
  }
  return <div>Component Loaded</div>;
};

describe('End-to-End Error Handling Workflow', () => {
  let mockIpcInvoke;

  beforeEach(() => {
    jest.clearAllMocks();
    mockIpcInvoke = mockElectronAPI.ipcRenderer.invoke;

    mockIpcInvoke.mockImplementation(async channel => {
      switch (channel) {
      case 'register-error-listener':
      case 'log-frontend-errors':
      case 'report-frontend-health':
        return {success: true};
      default:
        return {success: true, data: {}};
      }
    });
  });

  const renderApp = (ui = <App/>) =>
    render(<BrowserRouter>{ui}</BrowserRouter>);

  test('should initialize error handlers on startup', async () => {
    await act(async () => {
      renderApp();
    });

    await waitFor(() => {
      expect(SystemWideErrorHandler.isInitialized()).toBe(true);
      expect(GlobalErrorHandler.isInitialized()).toBe(true);
    });

    expect(mockIpcInvoke).toHaveBeenCalledWith('register-error-listener');
  });

  test('should catch global errors and log them', async () => {
    await act(async () => {
      renderApp();
    });
    await waitFor(() => expect(SystemWideErrorHandler.isInitialized()).toBe(true));

    const testError = new Error('Global test error');
    act(() => {
      window.dispatchEvent(new ErrorEvent('error', {error: testError}));
    });

    await waitFor(() => {
      expect(mockIpcInvoke).toHaveBeenCalledWith(
        'log-frontend-errors',
        expect.any(Object),
      );
    });
  });

  test('should display an error boundary for component errors', async () => {
    const ThrowingApp = () => (
      <App>
        <ErrorThrowingComponent shouldThrow/>
      </App>
    );

    await act(async () => {
      renderApp(<ThrowingApp/>);
    });

    await waitFor(() => {
      const fallbackUI = screen.queryByText(/something went wrong/i);
      expect(fallbackUI).toBeInTheDocument();
    });
  });

  test('should handle unhandled promise rejections', async () => {
    await act(async () => {
      renderApp();
    });
    await waitFor(() => expect(SystemWideErrorHandler.isInitialized()).toBe(true));

    act(() => {
      window.dispatchEvent(
        new PromiseRejectionEvent('unhandledrejection', {
          promise: Promise.reject(),
          reason: new Error('Promise rejected'),
        }),
      );
    });

    await waitFor(() => {
      expect(mockIpcInvoke).toHaveBeenCalledWith(
        'log-frontend-errors',
        expect.any(Object),
      );
    });
  });
});
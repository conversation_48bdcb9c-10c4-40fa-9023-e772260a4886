/**
 * Comprehensive Error Handling Test Suite
 * Tests enhanced error boundaries, automatic recovery, and graceful degradation
 */

import React from 'react';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';

import ApplicationErrorBoundary, {
  DashboardErrorBoundary,
  PortfolioErrorBoundary,
  TradingErrorBoundary
} from '../../components/ApplicationErrorBoundary';
import ComponentRecoveryManager from '../../utils/ComponentRecoveryManager';
import {ErrorReporter} from '../../services/ErrorReporter';

// Mock dependencies
jest.mock('../../services/ErrorReporter');
jest.mock('../../utils/GlobalErrorHandler');

// Mock electron API
const mockElectronAPI = {
    ipcRenderer: {
        invoke: jest.fn()
    }
};

Object.defineProperty(window, 'electronAPI', {
    value: mockElectronAPI,
    writable: true
});

// Test components that throw errors
const ThrowingComponent = ({shouldThrow = true, errorType = 'generic'}) => {
    if (shouldThrow) {
        switch (errorType) {
            case 'network':
                throw new Error('Network connection failed');
            case 'database':
                throw new Error('Database connection lost');
            case 'trading':
                throw new Error('Trading execution failed');
            case 'memory':
                throw new Error('Out of memory');
            case 'permission':
                throw new Error('Permission denied');
            default:
                throw new Error('Generic component error');
        }
    }
    return <div data-testid="working-component">Component is working</div>;
};

const RecoverableComponent = ({shouldRecover = false}) => {
    const [hasError, setHasError] = React.useState(true);

    React.useEffect(() => {
        if (shouldRecover) {
            const timer = setTimeout(() => setHasError(false), 1000);
            return () => clearTimeout(timer);
        }
    }, [shouldRecover]);

    if (hasError && !shouldRecover) {
        throw new Error('Recoverable component error');
    }

    return <div data-testid="recovered-component">Component recovered</div>;
};

describe('Enhanced Error Handling and Recovery', () => {
    let mockErrorReporter;
    let _originalConsoleError;

    beforeEach(() => {
        mockErrorReporter = new ErrorReporter();
        mockErrorReporter.report = jest.fn().mockResolvedValue(true);

        // Mock console.error to avoid noise in tests
        const _originalConsoleError = console.error;
        // console.error = jest.fn();

        // Reset electron API mock
        mockElectronAPI.ipcRenderer.invoke.mockReset();
    });

    afterEach(() => {
        // console.error = originalConsoleError;
        jest.clearAllMocks();
    });

    describe('ApplicationErrorBoundary', () => {
        test('should catch and handle generic errors', async () => {
            render(
                <ApplicationErrorBoundary componentName="TestComponent">
                    <ThrowingComponent/>
                </ApplicationErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });
        });

        test('should classify network errors correctly', async () => {
            render(
                <ApplicationErrorBoundary componentName="NetworkComponent">
                    <ThrowingComponent errorType="network"/>
                </ApplicationErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText(/network/i)).toBeInTheDocument();
            });
        });

        test('should handle trading errors with recovery', () => {
            mockElectronAPI.ipcRenderer.invoke.mockResolvedValue({
                success: true,
                message: 'Trading system recovered'
            });

            render(
                <ApplicationErrorBoundary
                    componentName="TradingComponent"
                    autoRecovery={true}
                >
                    <ThrowingComponent errorType="trading"/>
                </ApplicationErrorBoundary>,
            );

            await waitFor(() => {
                expect(mockElectronAPI.ipcRenderer.invoke).toHaveBeenCalledWith(
                    'recover-trading-system',
                    expect.objectContaining({
                        component: 'TradingComponent',
                        errorType: 'trading'
                    }),
                );
            });
        });

        test('should provide retry functionality', () => {
            const {rerender} = render(
                <ApplicationErrorBoundary componentName="RetryComponent" maxRetries={2}>
                    <ThrowingComponent/>
                </ApplicationErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText(/try again/i)).toBeInTheDocument();
            });

            const retryButton = screen.getByText(/try again/i);
            fireEvent.click(retryButton);

            // Component should attempt to recover
            rerender(
                <ApplicationErrorBoundary componentName="RetryComponent" maxRetries={2}>
                    <ThrowingComponent shouldThrow={false}/>
                </ApplicationErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByTestId('working-component')).toBeInTheDocument();
            });
        });

        test('should handle graceful degradation for optional components', () => {
            render(
                <ApplicationErrorBoundary
                    componentName="OptionalComponent"
                    gracefulDegradation={true}
                >
                    <ThrowingComponent/>
                </ApplicationErrorBoundary>,
            );

            await waitFor(() => {
                // Should show degraded state instead of full error
                expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should emit degradation event
            await waitFor(() => {
                const degradationEvents = window.dispatchEvent.mock?.calls?.filter(
                    call => call[0].type === 'componentDegraded',
                ) || [];
                expect(degradationEvents.length).toBeGreaterThan(0);
            });
        });

        test('should handle max retries exceeded', () => {
            render(
                <ApplicationErrorBoundary componentName="FailingComponent" maxRetries={1}>
                    <ThrowingComponent/>
                </ApplicationErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText(/try again/i)).toBeInTheDocument();
            });

            // First retry
            const retryButton = screen.getByText(/try again/i);
            fireEvent.click(retryButton);

            await waitFor(() => {
                // After max retries, should show different state
                expect(screen.queryByText(/try again/i)).not.toBeInTheDocument();
            });
        });
    });

    describe('Specialized Error Boundaries', () => {
        test('DashboardErrorBoundary should handle dashboard-specific errors', async () => {
            render(
                <DashboardErrorBoundary>
                    <ThrowingComponent/>
                </DashboardErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });
        });

        test('TradingErrorBoundary should handle trading-specific errors', async () => {
            render(
                <TradingErrorBoundary>
                    <ThrowingComponent errorType="trading"/>
                </TradingErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });
        });

        test('PortfolioErrorBoundary should handle portfolio-specific errors', async () => {
            render(
                <PortfolioErrorBoundary>
                    <ThrowingComponent/>
                </PortfolioErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });
        });
    });

    describe('ComponentRecoveryManager', () => {
        let recoveryManager;

        beforeEach(() => {
            recoveryManager = new ComponentRecoveryManager();
        });

        afterEach(() => {
            recoveryManager.shutdown();
        });

        test('should handle component initialization failure', () => {
            const error = new Error('Component initialization failed');
            const context = {source: 'test'};

            mockElectronAPI.ipcRenderer.invoke.mockResolvedValue({
                success: true,
                message: 'Component restarted'
            });

            const result = await recoveryManager.handleComponentInitializationFailure(
                'TestComponent',
                error,
                context,
            );

            expect(result).toBe(true);
            expect(mockElectronAPI.ipcRenderer.invoke).toHaveBeenCalledWith(
                'restart-component',
                expect.objectContaining({
                    componentName: 'TestComponent',
                    strategy: 'graceful'
                }),
            );
        });

        test('should identify critical vs optional components', () => {
            expect(recoveryManager.isCriticalComponent('TradingOrchestrator')).toBe(true);
            expect(recoveryManager.isCriticalComponent('DatabaseManager')).toBe(true);
            expect(recoveryManager.isOptionalComponent('SentimentAnalyzer')).toBe(true);
            expect(recoveryManager.isOptionalComponent('AIOptimizer')).toBe(true);
        });

        test('should select appropriate recovery strategy', () => {
            const networkError = new Error('Network timeout');
            const criticalError = new Error('Database connection lost');
            const optionalError = new Error('Sentiment analysis failed');

            const networkStrategy = recoveryManager.selectRecoveryStrategy(
                'NetworkComponent',
                networkError,
                {},
            );
            expect(networkStrategy).toBe('graceful-restart');

            const criticalStrategy = recoveryManager.selectRecoveryStrategy(
                'DatabaseManager',
                criticalError,
                {},
            );
            expect(criticalStrategy).toBe('immediate-restart');

            const optionalStrategy = recoveryManager.selectRecoveryStrategy(
                'SentimentAnalyzer',
                optionalError,
                {},
            );
            expect(optionalStrategy).toBe('graceful-restart');
        });

        test('should handle graceful degradation', () => {
            const error = new Error('Optional component failed');
            const context = {source: 'test'};

            const result = await recoveryManager.handleGracefulDegradation(
                'OptionalComponent',
                error,
                context,
            );

            expect(result).toBe(true);

            // Should emit degradation event
            const componentStatus = recoveryManager.getComponentStatus('OptionalComponent');
            expect(componentStatus.status).toBe('degraded');
        });

        test('should perform health checks', async () => {
            // Add a component to track
            recoveryManager.getComponentState('TestComponent');

            const health = await recoveryManager.checkComponentHealth('TestComponent');

            expect(health).toHaveProperty('status');
            expect(health).toHaveProperty('errorRate');
            expect(health).toHaveProperty('uptime');
        });

        test('should handle critical component failure', () => {
            const error = new Error('Critical failure');
            const context = {source: 'test'};

            mockElectronAPI.ipcRenderer.invoke.mockResolvedValue({
                success: true,
                message: 'Emergency protocols triggered'
            });

            await recoveryManager.handleCriticalComponentFailure(
                'TradingOrchestrator',
                error,
                context,
            );

            // Should have triggered emergency protocols
            expect(mockElectronAPI.ipcRenderer.invoke).toHaveBeenCalledWith(
                'trigger-emergency-protocols',
                expect.objectContaining({
                    componentName: 'TradingOrchestrator',
                    error
                }),
            );
        });
    });

    describe('Error Recovery Integration', () => {
        test('should integrate error boundary with recovery manager', () => {
            const mockRecovery = jest.fn().mockResolvedValue(true);

            render(
                <ApplicationErrorBoundary
                    componentName="IntegratedComponent"
                    onRecovery={mockRecovery}
                    autoRecovery={true}
                >
                    <RecoverableComponent shouldRecover={true}/>
                </ApplicationErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByTestId('recovered-component')).toBeInTheDocument();
            });
        });

        test('should handle multiple error types with different strategies', () => {
            const testCases = [
                {errorType: 'network', expectedStrategy: 'retry-with-backoff'},
                {errorType: 'database', expectedStrategy: 'data-recovery'},
                {errorType: 'trading', expectedStrategy: 'trading-recovery'},
                {errorType: 'memory', expectedStrategy: 'emergency-fallback'}];

            for (const testCase of testCases) {
                const {rerender} = render(
                    <ApplicationErrorBoundary componentName="MultiErrorComponent">
                        <ThrowingComponent errorType={testCase.errorType}/>
                    </ApplicationErrorBoundary>,
                );

                await waitFor(() => {
                    expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
                });

                // Clean up for next test
                rerender(<div>Clean slate</div>);
            }
        });

        test('should emit proper events during error handling', async () => {
            const eventListener = jest.fn();
            window.addEventListener('componentError', eventListener);

            render(
                <ApplicationErrorBoundary componentName="EventComponent">
                    <ThrowingComponent/>
                </ApplicationErrorBoundary>,
            );

            await waitFor(() => {
                expect(eventListener).toHaveBeenCalled();
            });

            window.removeEventListener('componentError', eventListener);
        });
    });

    describe('Error Reporting Integration', () => {
        test('should report errors with proper context', async () => {
            render(
                <ApplicationErrorBoundary
                    componentName="ReportingComponent"
                    workflow="test-workflow"
                    userId="test-user"
                >
                    <ThrowingComponent/>
                </ApplicationErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Error should have been reported
            expect(mockErrorReporter.report).toHaveBeenCalledWith(
                expect.objectContaining({
                    componentName: 'ReportingComponent',
                    workflow: 'test-workflow',
                    userId: 'test-user'
                }),
            );
        });

        test('should handle error reporting failures gracefully', async () => {
            mockErrorReporter.report.mockRejectedValue(new Error('Reporting failed'));

            render(
                <ApplicationErrorBoundary componentName="ReportingFailComponent">
                    <ThrowingComponent/>
                </ApplicationErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should still show error boundary even if reporting fails
            expect(screen.getByText(/try again/i)).toBeInTheDocument();
        });
    });

    describe('Performance and Memory Management', () => {
        test('should limit error history size', async () => {
            const recoveryManager = new ComponentRecoveryManager();

            // Generate many errors
            for (let i = 0; i < 60; i++) {
                await recoveryManager.handleComponentInitializationFailure(
                    'TestComponent',
                    new Error(`Error ${i}`),
                    {iteration: i},
                );
            }

            const history = recoveryManager.getRecoveryHistory('TestComponent');
            expect(history.length).toBeLessThanOrEqual(50); // Should be capped

            recoveryManager.shutdown();
        });

        test('should clean up resources on shutdown', () => {
            const recoveryManager = new ComponentRecoveryManager();
            const stopSpy = jest.spyOn(recoveryManager, 'stopHealthMonitoring');

            recoveryManager.shutdown();

            expect(stopSpy).toHaveBeenCalled();
        });
    });
});

describe('Error Boundary Edge Cases', () => {
    test('should handle errors in error boundary itself', () => {
        // This is a meta-test to ensure error boundaries are robust
        const BuggyErrorBoundary = ({children}) => {
            const [hasError, setHasError] = React.useState(false);

            if (hasError) {
                // Simulate error in error boundary
                throw new Error('Error boundary itself failed');
            }

            return (
                <ApplicationErrorBoundary
                    componentName="BuggyBoundary"
                    onError={() => setHasError(true)}
                >
                    {children}
                </ApplicationErrorBoundary>
            );
        };

        expect(() => {
            render(
                <BuggyErrorBoundary>
                    <ThrowingComponent/>
                </BuggyErrorBoundary>,
            );
        }).not.toThrow();
    });

    test('should handle rapid successive errors', () => {
        const RapidErrorComponent = () => {
            const [errorCount, setErrorCount] = React.useState(0);

            React.useEffect(() => {
                const interval = setInterval(() => {
                    setErrorCount(prev => prev + 1);
                }, 100);

                return () => clearInterval(interval);
            }, []);

            if (errorCount > 0 && errorCount < 5) {
                throw new Error(`Rapid error ${errorCount}`);
            }

            return <div>Stabilized after {errorCount} errors</div>;
        };

        render(
            <ApplicationErrorBoundary componentName="RapidErrorComponent">
                <RapidErrorComponent/>
            </ApplicationErrorBoundary>,
        );

        // Should handle rapid errors without crashing
        await waitFor(() => {
            expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
        }, {timeout: 5000});
    });
});
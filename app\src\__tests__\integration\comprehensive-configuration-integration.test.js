/**
 * Comprehensive Configuration and Environment Integration Tests
 * Tests configuration loading and environment handling across the application
 *
 * Requirements Coverage:
 * - 4.3: Configuration loading and environment handling
 * - 6.1: Configuration files loaded successfully
 * - 6.2: Environment variables properly set
 * - 6.3: API keys and credentials accessible
 * - 6.4: Feature flags respected
 * - 6.5: Configuration changes handled without restart
 */

const path = require('path');
const fs = require('fs');
const { ipcRenderer } = require('electron');

// Mock electron IPC
jest.mock('electron', () => ({
  ipcRenderer: {
    invoke: jest.fn(),
    on: jest.fn(),
    removeAllListeners: jest.fn(),
    send: jest.fn(),
  },
}));

// Mock file system operations for configuration files
const mockConfigFiles = {
  'config.json': {
    trading: {
      enabled: true,
      pairs: ['BTC/USDT', 'ETH/USDT'],
      strategies: {
        gridBot: { enabled: true, gridSize: 10 },
        memeCoin: { enabled: true, riskLevel: 'medium' },
        whaleTracking: { enabled: true, minAmount: 100000 },
      },
    },
    database: {
      type: 'sqlite',
      path: './databases/trading_system.db',
      backup: {
        enabled: true,
        interval: '1h',
        retention: '7d',
      },
    },
    exchanges: {
      binance: {
        enabled: true,
        apiKey: process.env.TEST_BINANCE_API_KEY || 'test_api_key_placeholder',
        sandbox: true,
      },
      coinbase: {
        enabled: false,
        apiKey: process.env.TEST_COINBASE_API_KEY || 'test_coinbase_key_placeholder',
        sandbox: true,
      },
    },
    monitoring: {
      enabled: true,
      logLevel: 'info',
      metrics: {
        enabled: true,
        interval: '30s',
      },
    },
  },
  'development.json': {
    debug: true,
    logLevel: 'debug',
    features: {
      paperTrading: true,
      realTrading: false,
      devTools: true,
    },
  },
  'production.json': {
    debug: false,
    logLevel: 'warn',
    features: {
      paperTrading: false,
      realTrading: true,
      devTools: false,
    },
  },
};

describe('Comprehensive Configuration and Environment Integration', () => {
  let configManager;
  let environmentManager;
  let ipcService;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock configuration responses
    ipcRenderer.invoke.mockImplementation((channel, ...args) => {
      switch (channel) {
      case 'get-configuration':
        return Promise.resolve({
          success: true,
          data: mockConfigFiles['config.json'],
        });
      case 'get-environment-config':
        const env = process.env.NODE_ENV || 'development';
        return Promise.resolve({
          success: true,
          data: {
            environment: env,
            ...mockConfigFiles[`${env}.json`],
          },
        });
      case 'update-configuration':
        return Promise.resolve({
          success: true,
          data: { updated: true },
        });
      case 'reload-configuration':
        return Promise.resolve({
          success: true,
          data: { reloaded: true },
        });
      case 'get-feature-flags':
        return Promise.resolve({
          success: true,
          data: {
            autonomousTrading: true,
            whaleTracking: true,
            memeCoinScanning: true,
            gridTrading: true,
            paperTrading: process.env.NODE_ENV === 'development',
          },
        });
      case 'update-feature-flag':
        return Promise.resolve({
          success: true,
          data: { flagName: args[0], value: args[1] },
        });
      case 'validate-configuration':
        return Promise.resolve({
          success: true,
          data: { valid: true, errors: [] },
        });
      default:
        return Promise.resolve({ success: false, error: 'Unknown channel' });
      }
    });

    // Create mock ipcService instead of importing
    ipcService = {
      getConfiguration: () => ipcRenderer.invoke('get-configuration'),
      validateConfiguration: () => ipcRenderer.invoke('validate-configuration'),
      updateConfiguration: (config) => ipcRenderer.invoke('update-configuration', config),
      reloadConfiguration: () => ipcRenderer.invoke('reload-configuration'),
      getEnvironmentConfig: () => ipcRenderer.invoke('get-environment-config'),
      getFeatureFlags: () => ipcRenderer.invoke('get-feature-flags'),
      updateFeatureFlag: (flag, value) => ipcRenderer.invoke('update-feature-flag', flag, value),
      storeCredentials: (credentials) => ipcRenderer.invoke('store-credentials', credentials),
      getCredentials: (exchange) => ipcRenderer.invoke('get-credentials', exchange),
      validateApiKey: (exchange) => ipcRenderer.invoke('validate-api-key', exchange),
      rotateCredentials: (exchange, credentials) => ipcRenderer.invoke('rotate-credentials', exchange, credentials),
      backupConfiguration: () => ipcRenderer.invoke('backup-configuration'),
      restoreConfiguration: (path) => ipcRenderer.invoke('restore-configuration', path),
      listConfigurationBackups: () => ipcRenderer.invoke('list-configuration-backups'),
      initializeComponents: () => ipcRenderer.invoke('initialize-components'),
      recoverComponent: (component) => ipcRenderer.invoke('recover-component', component),
      onConfigurationChange: (callback) => {
        if (ipcRenderer.on) {
          ipcRenderer.on('configuration-changed', callback);
        }
      },
      onSystemNotification: (callback) => {
        if (ipcRenderer.on) {
          ipcRenderer.on('system-notification', callback);
        }
      },
    };
  });

  describe('Configuration Loading and Validation', () => {
    test('should load main configuration successfully', async () => {
      const result = await ipcService.getConfiguration();

      expect(ipcRenderer.invoke).toHaveBeenCalledWith('get-configuration');
      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('trading');
      expect(result.data).toHaveProperty('database');
      expect(result.data).toHaveProperty('exchanges');
      expect(result.data).toHaveProperty('monitoring');
    });

    test('should validate configuration structure', async () => {
      const result = await ipcService.validateConfiguration();

      expect(ipcRenderer.invoke).toHaveBeenCalledWith('validate-configuration');
      expect(result.success).toBe(true);
      expect(result.data.valid).toBe(true);
      expect(Array.isArray(result.data.errors)).toBe(true);
    });

    test('should handle configuration validation errors', async () => {
      // Mock validation failure
      ipcRenderer.invoke.mockImplementationOnce(() =>
        Promise.resolve({
          success: true,
          data: {
            valid: false,
            errors: [
              { field: 'trading.pairs', message: 'Invalid trading pair format' },
              { field: 'exchanges.binance.apiKey', message: 'API key is required' },
            ],
          },
        }),
      );

      const result = await ipcService.validateConfiguration();

      expect(result.success).toBe(true);
      expect(result.data.valid).toBe(false);
      expect(result.data.errors).toHaveLength(2);
    });

    test('should load trading strategy configurations', async () => {
      const config = await ipcService.getConfiguration();

      expect(config.data.trading.strategies).toHaveProperty('gridBot');
      expect(config.data.trading.strategies).toHaveProperty('memeCoin');
      expect(config.data.trading.strategies).toHaveProperty('whaleTracking');

      expect(config.data.trading.strategies.gridBot.enabled).toBe(true);
      expect(config.data.trading.strategies.gridBot.gridSize).toBe(10);
    });

    test('should load exchange configurations', async () => {
      const config = await ipcService.getConfiguration();

      expect(config.data.exchanges).toHaveProperty('binance');
      expect(config.data.exchanges).toHaveProperty('coinbase');

      expect(config.data.exchanges.binance.enabled).toBe(true);
      expect(config.data.exchanges.binance.sandbox).toBe(true);
      expect(config.data.exchanges.coinbase.enabled).toBe(false);
    });

    test('should load database configuration', async () => {
      const config = await ipcService.getConfiguration();

      expect(config.data.database.type).toBe('sqlite');
      expect(config.data.database.path).toBe('./databases/trading_system.db');
      expect(config.data.database.backup.enabled).toBe(true);
      expect(config.data.database.backup.interval).toBe('1h');
    });
  });

  describe('Environment-Specific Configuration', () => {
    test('should load development environment configuration', async () => {
      process.env.NODE_ENV = 'development';

      const result = await ipcService.getEnvironmentConfig();

      expect(result.success).toBe(true);
      expect(result.data.environment).toBe('development');
      expect(result.data.debug).toBe(true);
      expect(result.data.logLevel).toBe('debug');
      expect(result.data.features.paperTrading).toBe(true);
      expect(result.data.features.realTrading).toBe(false);
      expect(result.data.features.devTools).toBe(true);
    });

    test('should load production environment configuration', async () => {
      process.env.NODE_ENV = 'production';

      const result = await ipcService.getEnvironmentConfig();

      expect(result.success).toBe(true);
      expect(result.data.environment).toBe('production');
      expect(result.data.debug).toBe(false);
      expect(result.data.logLevel).toBe('warn');
      expect(result.data.features.paperTrading).toBe(false);
      expect(result.data.features.realTrading).toBe(true);
      expect(result.data.features.devTools).toBe(false);
    });

    test('should handle environment variable overrides', async () => {
      // Set environment variables
      process.env.TRADING_ENABLED = 'false';
      process.env.LOG_LEVEL = 'error';
      process.env.PAPER_TRADING = 'true';

      // Mock environment override response
      ipcRenderer.invoke.mockImplementationOnce(() =>
        Promise.resolve({
          success: true,
          data: {
            trading: { enabled: false }, // Overridden by env var
            logLevel: 'error', // Overridden by env var
            features: { paperTrading: true }, // Overridden by env var
          },
        }),
      );

      const result = await ipcService.getEnvironmentConfig();

      expect(result.data.trading.enabled).toBe(false);
      expect(result.data.logLevel).toBe('error');
      expect(result.data.features.paperTrading).toBe(true);

      // Cleanup
      delete process.env.TRADING_ENABLED;
      delete process.env.LOG_LEVEL;
      delete process.env.PAPER_TRADING;
    });

    test('should validate environment-specific settings', async () => {
      const environments = ['development', 'staging', 'production'];

      for (const env of environments) {
        process.env.NODE_ENV = env;

        const result = await ipcService.getEnvironmentConfig();
        expect(result.success).toBe(true);
        expect(result.data.environment).toBe(env);
      }
    });
  });

  describe('Feature Flag Management', () => {
    test('should retrieve all feature flags', async () => {
      const result = await ipcService.getFeatureFlags();

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('autonomousTrading');
      expect(result.data).toHaveProperty('whaleTracking');
      expect(result.data).toHaveProperty('memeCoinScanning');
      expect(result.data).toHaveProperty('gridTrading');
      expect(result.data).toHaveProperty('paperTrading');
    });

    test('should update individual feature flags', async () => {
      const result = await ipcService.updateFeatureFlag('autonomousTrading', false);

      expect(ipcRenderer.invoke).toHaveBeenCalledWith('update-feature-flag', 'autonomousTrading', false);
      expect(result.success).toBe(true);
      expect(result.data.flagName).toBe('autonomousTrading');
      expect(result.data.value).toBe(false);
    });

    test('should handle feature flag validation', async () => {
      // Test valid feature flag
      let result = await ipcService.updateFeatureFlag('whaleTracking', true);
      expect(result.success).toBe(true);

      // Test invalid feature flag
      ipcRenderer.invoke.mockImplementationOnce(() =>
        Promise.resolve({
          success: false,
          error: { message: 'Invalid feature flag name' },
        }),
      );

      result = await ipcService.updateFeatureFlag('invalidFlag', true);
      expect(result.success).toBe(false);
    });

    test('should handle environment-dependent feature flags', async () => {
      // Development environment should enable paper trading
      process.env.NODE_ENV = 'development';
      let result = await ipcService.getFeatureFlags();
      expect(result.data.paperTrading).toBe(true);

      // Production environment should disable paper trading
      process.env.NODE_ENV = 'production';
      result = await ipcService.getFeatureFlags();
      expect(result.data.paperTrading).toBe(false);
    });
  });

  describe('Dynamic Configuration Updates', () => {
    test('should update configuration without restart', async () => {
      const newConfig = {
        trading: {
          enabled: true,
          pairs: ['BTC/USDT', 'ETH/USDT', 'ADA/USDT'],
          strategies: {
            gridBot: { enabled: true, gridSize: 15 },
            memeCoin: { enabled: false },
          },
        },
      };

      const result = await ipcService.updateConfiguration(newConfig);

      expect(ipcRenderer.invoke).toHaveBeenCalledWith('update-configuration', newConfig);
      expect(result.success).toBe(true);
      expect(result.data.updated).toBe(true);
    });

    test('should reload configuration from files', async () => {
      const result = await ipcService.reloadConfiguration();

      expect(ipcRenderer.invoke).toHaveBeenCalledWith('reload-configuration');
      expect(result.success).toBe(true);
      expect(result.data.reloaded).toBe(true);
    });

    test('should handle configuration change notifications', async () => {
      const configChanges = [];

      ipcRenderer.on.mockImplementation((channel, callback) => {
        if (channel === 'configuration-changed') {
          setTimeout(() => {
            callback(null, {
              type: 'trading-config-updated',
              changes: {
                'trading.pairs': ['BTC/USDT', 'ETH/USDT', 'ADA/USDT'],
                'trading.strategies.gridBot.gridSize': 15,
              },
              timestamp: new Date().toISOString(),
            });
            configChanges.push('trading-config-updated');
          }, 50);
        }
      });

      // Setup configuration change monitoring
      ipcService.onConfigurationChange?.((change) => {
        configChanges.push(change.type);
      });

      // Trigger configuration update
      await ipcService.updateConfiguration({
        trading: { pairs: ['BTC/USDT', 'ETH/USDT', 'ADA/USDT'] },
      });

      await new Promise(resolve => setTimeout(resolve, 100));

      expect(configChanges.length).toBeGreaterThan(0);
    });

    test('should validate configuration before applying updates', async () => {
      const invalidConfig = {
        trading: {
          pairs: ['INVALID/PAIR'],
          strategies: {
            gridBot: { gridSize: -1 }, // Invalid negative value
          },
        },
      };

      // Mock validation failure
      ipcRenderer.invoke.mockImplementationOnce(() =>
        Promise.resolve({
          success: false,
          error: {
            message: 'Configuration validation failed',
            details: [
              'Invalid trading pair format: INVALID/PAIR',
              'Grid size must be positive: -1',
            ],
          },
        }),
      );

      const result = await ipcService.updateConfiguration(invalidConfig);

      expect(result.success).toBe(false);
      expect(result.error.message).toContain('validation failed');
    });
  });

  describe('Credential and API Key Management', () => {
    test('should handle encrypted credential storage', async () => {
      const credentials = {
        exchange: 'binance',
        apiKey: 'test_api_key',
        apiSecret: 'test_api_secret',
        passphrase: 'test_passphrase',
      };

      ipcRenderer.invoke.mockImplementationOnce(() =>
        Promise.resolve({
          success: true,
          data: { stored: true, encrypted: true },
        }),
      );

      const result = await ipcService.storeCredentials(credentials);

      expect(ipcRenderer.invoke).toHaveBeenCalledWith('store-credentials', credentials);
      expect(result.success).toBe(true);
      expect(result.data.encrypted).toBe(true);
    });

    test('should retrieve and decrypt credentials', async () => {
      ipcRenderer.invoke.mockImplementationOnce(() =>
        Promise.resolve({
          success: true,
          data: {
            exchange: 'binance',
            apiKey: 'decrypted_api_key',
            hasSecret: true, // Don't return actual secret for security
          },
        }),
      );

      const result = await ipcService.getCredentials('binance');

      expect(ipcRenderer.invoke).toHaveBeenCalledWith('get-credentials', 'binance');
      expect(result.success).toBe(true);
      expect(result.data.exchange).toBe('binance');
      expect(result.data.hasSecret).toBe(true);
    });

    test('should validate API key permissions', async () => {
      ipcRenderer.invoke.mockImplementationOnce(() =>
        Promise.resolve({
          success: true,
          data: {
            exchange: 'binance',
            permissions: ['spot', 'futures'],
            valid: true,
            testConnection: true,
          },
        }),
      );

      const result = await ipcService.validateApiKey('binance');

      expect(result.success).toBe(true);
      expect(result.data.valid).toBe(true);
      expect(result.data.testConnection).toBe(true);
      expect(Array.isArray(result.data.permissions)).toBe(true);
    });

    test('should handle credential rotation', async () => {
      const newCredentials = {
        exchange: 'binance',
        apiKey: 'new_api_key',
        apiSecret: 'new_api_secret',
      };

      ipcRenderer.invoke.mockImplementationOnce(() =>
        Promise.resolve({
          success: true,
          data: {
            rotated: true,
            oldKeyDeactivated: true,
            newKeyActivated: true,
          },
        }),
      );

      const result = await ipcService.rotateCredentials('binance', newCredentials);

      expect(result.success).toBe(true);
      expect(result.data.rotated).toBe(true);
      expect(result.data.oldKeyDeactivated).toBe(true);
      expect(result.data.newKeyActivated).toBe(true);
    });
  });

  describe('Configuration Backup and Recovery', () => {
    test('should create configuration backup', async () => {
      ipcRenderer.invoke.mockImplementationOnce(() =>
        Promise.resolve({
          success: true,
          data: {
            backupPath: './backups/config_backup_20250129.json',
            timestamp: new Date().toISOString(),
            size: '2.5 KB',
          },
        }),
      );

      const result = await ipcService.backupConfiguration();

      expect(ipcRenderer.invoke).toHaveBeenCalledWith('backup-configuration');
      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('backupPath');
      expect(result.data).toHaveProperty('timestamp');
    });

    test('should restore configuration from backup', async () => {
      const backupPath = './backups/config_backup_20250129.json';

      ipcRenderer.invoke.mockImplementationOnce(() =>
        Promise.resolve({
          success: true,
          data: {
            restored: true,
            backupPath,
            restoredConfig: mockConfigFiles['config.json'],
          },
        }),
      );

      const result = await ipcService.restoreConfiguration(backupPath);

      expect(ipcRenderer.invoke).toHaveBeenCalledWith('restore-configuration', backupPath);
      expect(result.success).toBe(true);
      expect(result.data.restored).toBe(true);
    });

    test('should list available configuration backups', async () => {
      ipcRenderer.invoke.mockImplementationOnce(() =>
        Promise.resolve({
          success: true,
          data: {
            backups: [
              {
                path: './backups/config_backup_20250129.json',
                timestamp: '2025-01-29T10:00:00Z',
                size: '2.5 KB',
              },
              {
                path: './backups/config_backup_20250128.json',
                timestamp: '2025-01-28T10:00:00Z',
                size: '2.4 KB',
              },
            ],
          },
        }),
      );

      const result = await ipcService.listConfigurationBackups();

      expect(result.success).toBe(true);
      expect(Array.isArray(result.data.backups)).toBe(true);
      expect(result.data.backups).toHaveLength(2);
    });
  });

  describe('Configuration Integration with Components', () => {
    test('should propagate configuration changes to trading components', async () => {
      const componentUpdates = [];

      ipcRenderer.on.mockImplementation((channel, callback) => {
        if (channel === 'component-config-update') {
          setTimeout(() => {
            callback(null, {
              component: 'memeCoinScanner',
              configUpdate: { enabled: false },
              status: 'updated',
            });
            componentUpdates.push('memeCoinScanner');
          }, 50);
        }
      });

      // Update configuration
      await ipcService.updateConfiguration({
        trading: {
          strategies: {
            memeCoin: { enabled: false },
          },
        },
      });

      await new Promise(resolve => setTimeout(resolve, 100));

      expect(componentUpdates).toContain('memeCoinScanner');
    });

    test('should handle configuration-dependent component initialization', async () => {
      // Mock component initialization based on configuration
      ipcRenderer.invoke.mockImplementation((channel) => {
        if (channel === 'initialize-components') {
          return Promise.resolve({
            success: true,
            data: {
              initialized: ['tradingOrchestrator', 'gridBotManager'],
              skipped: ['memeCoinScanner'], // Disabled in config
              failed: [],
            },
          });
        }
        return Promise.resolve({ success: true });
      });

      const result = await ipcService.initializeComponents();

      expect(result.success).toBe(true);
      expect(result.data.initialized).toContain('tradingOrchestrator');
      expect(result.data.skipped).toContain('memeCoinScanner');
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
    // Reset NODE_ENV
    process.env.NODE_ENV = 'test';
  });
});
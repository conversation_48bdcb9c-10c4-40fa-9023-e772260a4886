/**
 * @fileoverview Comprehensive Error Boundary Tests
 * @description Tests error boundary implementations and recovery mechanisms
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ApplicationErrorBoundary from '../../components/ApplicationErrorBoundary';
import EnhancedErrorBoundary from '../../components/EnhancedErrorBoundary';
import ErrorBoundary from '../../components/ErrorBoundary';

// Mock error reporter
jest.mock('../../services/ErrorReporter', () => ({
  ErrorReporter: jest.fn().mockImplementation(() => ({
    reportError: jest.fn(),
    reportRecovery: jest.fn(),
    getErrorHistory: jest.fn().mockReturnValue([]),
  })),
}));

// Mock system-wide error handler
jest.mock('../../utils/SystemWideErrorHandler', () => ({
  handleError: jest.fn(),
  reportError: jest.fn(),
}));

// Mock logger
jest.mock('../../utils/logger', () => ({
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
}));

// Test component that throws errors
const ErrorThrowingComponent = ({ shouldThrow, errorType = 'generic' }) => {
  if (shouldThrow) {
    if (errorType === 'network') {
      throw new Error('Network connection failed');
    } else if (errorType === 'component') {
      throw new Error('Component initialization failed');
    } else if (errorType === 'trading') {
      throw new Error('Trading system error');
    } else {
      throw new Error('Generic component error');
    }
  }
  return <div data-testid="working-component">Component is working</div>;
};

// Test component that works normally
const WorkingComponent = () => (
  <div data-testid="working-component">Component is working</div>
);

describe('Error Boundary Comprehensive Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Suppress console.error for cleaner test output
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    console.error.mockRestore();
  });

  describe('ApplicationErrorBoundary', () => {
    test('should catch and handle component errors', async () => {
      render(
        <ApplicationErrorBoundary>
          <ErrorThrowingComponent shouldThrow={true} />
        </ApplicationErrorBoundary>,
      );

      // Should show error fallback UI
      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });
    });

    test('should handle network errors with appropriate fallback', async () => {
      render(
        <ApplicationErrorBoundary>
          <ErrorThrowingComponent shouldThrow={true} errorType="network" />
        </ApplicationErrorBoundary>,
      );

      await waitFor(() => {
        expect(screen.getByText(/network/i)).toBeInTheDocument();
      });
    });

    test('should attempt automatic recovery', async () => {
      const { rerender } = render(
        <ApplicationErrorBoundary autoRecovery={true} maxRetries={2}>
          <ErrorThrowingComponent shouldThrow={true} />
        </ApplicationErrorBoundary>,
      );

      // Should show error initially
      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });

      // Simulate recovery by re-rendering with working component
      rerender(
        <ApplicationErrorBoundary autoRecovery={true} maxRetries={2}>
          <ErrorThrowingComponent shouldThrow={false} />
        </ApplicationErrorBoundary>,
      );

      // Should show retry button
      const retryButton = screen.queryByText(/retry/i);
      if (retryButton) {
        fireEvent.click(retryButton);

        await waitFor(() => {
          expect(screen.getByTestId('working-component')).toBeInTheDocument();
        });
      }
    });

    test('should enable graceful degradation mode', async () => {
      render(
        <ApplicationErrorBoundary gracefulDegradation={true}>
          <ErrorThrowingComponent shouldThrow={true} />
        </ApplicationErrorBoundary>,
      );

      await waitFor(() => {
        // Should show degraded mode UI instead of complete failure
        expect(screen.getByText(/limited functionality/i) || screen.getByText(/degraded mode/i)).toBeInTheDocument();
      });
    });

    test('should track error metrics', async () => {
      const errorBoundary = React.createRef();

      render(
        <ApplicationErrorBoundary ref={errorBoundary}>
          <ErrorThrowingComponent shouldThrow={true} />
        </ApplicationErrorBoundary>,
      );

      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });

      // Error metrics should be tracked
      // Note: This would require exposing metrics through ref or context
    });
  });

  describe('EnhancedErrorBoundary', () => {
    test('should provide enhanced error information', async () => {
      render(
        <EnhancedErrorBoundary>
          <ErrorThrowingComponent shouldThrow={true} />
        </EnhancedErrorBoundary>,
      );

      await waitFor(() => {
        // Should show enhanced error details
        expect(screen.getByText(/error details/i) || screen.getByText(/error boundary/i)).toBeInTheDocument();
      });
    });

    test('should handle trading system errors specifically', async () => {
      render(
        <EnhancedErrorBoundary>
          <ErrorThrowingComponent shouldThrow={true} errorType="trading" />
        </EnhancedErrorBoundary>,
      );

      await waitFor(() => {
        expect(screen.getByText(/trading/i) || screen.getByText(/system error/i)).toBeInTheDocument();
      });
    });
  });

  describe('Basic ErrorBoundary', () => {
    test('should catch errors and show fallback UI', async () => {
      render(
        <ErrorBoundary>
          <ErrorThrowingComponent shouldThrow={true} />
        </ErrorBoundary>,
      );

      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i) || screen.getByText(/error/i)).toBeInTheDocument();
      });
    });

    test('should not interfere with working components', () => {
      render(
        <ErrorBoundary>
          <WorkingComponent />
        </ErrorBoundary>,
      );

      expect(screen.getByTestId('working-component')).toBeInTheDocument();
    });
  });

  describe('Error Recovery Workflows', () => {
    test('should handle component initialization failures', async () => {
      render(
        <ApplicationErrorBoundary autoRecovery={true}>
          <ErrorThrowingComponent shouldThrow={true} errorType="component" />
        </ApplicationErrorBoundary>,
      );

      await waitFor(() => {
        expect(screen.getByText(/initialization/i) || screen.getByText(/component/i)).toBeInTheDocument();
      });
    });

    test('should provide manual recovery options', async () => {
      render(
        <ApplicationErrorBoundary>
          <ErrorThrowingComponent shouldThrow={true} />
        </ApplicationErrorBoundary>,
      );

      await waitFor(() => {
        const retryButton = screen.queryByText(/retry/i) || screen.queryByText(/reload/i);
        expect(retryButton).toBeInTheDocument();
      });
    });

    test('should limit retry attempts', async () => {
      const { rerender } = render(
        <ApplicationErrorBoundary maxRetries={1}>
          <ErrorThrowingComponent shouldThrow={true} />
        </ApplicationErrorBoundary>,
      );

      // First error
      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });

      // Simulate retry that fails again
      const retryButton = screen.queryByText(/retry/i);
      if (retryButton) {
        fireEvent.click(retryButton);

        // Should still show error after max retries
        await waitFor(() => {
          expect(screen.getByText(/maximum retries/i) || screen.getByText(/contact support/i)).toBeInTheDocument();
        });
      }
    });
  });

  describe('Error Reporting Integration', () => {
    test('should report errors to backend', async () => {
      const mockErrorReporter = require('../../services/ErrorReporter').ErrorReporter;

      render(
        <ApplicationErrorBoundary>
          <ErrorThrowingComponent shouldThrow={true} />
        </ApplicationErrorBoundary>,
      );

      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });

      // Should have reported the error
      expect(mockErrorReporter).toHaveBeenCalled();
    });

    test('should report recovery attempts', async () => {
      const mockErrorReporter = require('../../services/ErrorReporter').ErrorReporter;
      const mockInstance = new mockErrorReporter();

      const { rerender } = render(
        <ApplicationErrorBoundary>
          <ErrorThrowingComponent shouldThrow={true} />
        </ApplicationErrorBoundary>,
      );

      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });

      // Simulate successful recovery
      rerender(
        <ApplicationErrorBoundary>
          <WorkingComponent />
        </ApplicationErrorBoundary>,
      );

      const retryButton = screen.queryByText(/retry/i);
      if (retryButton) {
        fireEvent.click(retryButton);

        await waitFor(() => {
          expect(mockInstance.reportRecovery).toHaveBeenCalled();
        });
      }
    });
  });

  describe('Specialized Error Boundaries', () => {
    test('should handle dashboard-specific errors', async () => {
      // This would test DashboardErrorBoundary if it exists
      render(
        <ApplicationErrorBoundary context="dashboard">
          <ErrorThrowingComponent shouldThrow={true} />
        </ApplicationErrorBoundary>,
      );

      await waitFor(() => {
        expect(screen.getByText(/dashboard/i) || screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });
    });

    test('should handle trading-specific errors', async () => {
      // This would test TradingErrorBoundary if it exists
      render(
        <ApplicationErrorBoundary context="trading">
          <ErrorThrowingComponent shouldThrow={true} errorType="trading" />
        </ApplicationErrorBoundary>,
      );

      await waitFor(() => {
        expect(screen.getByText(/trading/i) || screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });
    });
  });

  describe('Performance and User Experience', () => {
    test('should not impact performance of working components', () => {
      const startTime = performance.now();

      render(
        <ApplicationErrorBoundary>
          <WorkingComponent />
        </ApplicationErrorBoundary>,
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render quickly (less than 100ms)
      expect(renderTime).toBeLessThan(100);
      expect(screen.getByTestId('working-component')).toBeInTheDocument();
    });

    test('should provide user-friendly error messages', async () => {
      render(
        <ApplicationErrorBoundary>
          <ErrorThrowingComponent shouldThrow={true} />
        </ApplicationErrorBoundary>,
      );

      await waitFor(() => {
        // Should not show technical stack traces to users
        expect(screen.queryByText(/at ErrorThrowingComponent/)).not.toBeInTheDocument();
        // Should show user-friendly message
        expect(screen.getByText(/something went wrong/i) || screen.getByText(/error occurred/i)).toBeInTheDocument();
      });
    });
  });
});
eslint - disable
'use strict';

const _react = _interopRequireDefault(require('react'));
const _react2 = require('@testing-library/react');
require('@testing-library/jest-dom');
const _ApplicationErrorBoundary = _interopRequireDefault(require('../../components/ApplicationErrorBoundary'));
const _ErrorBoundary = _interopRequireDefault(require('../../components/ErrorBoundary'));
const _EnhancedErrorBoundary = _interopRequireDefault(require('../../components/EnhancedErrorBoundary'));
const _ErrorReporter = require('../../services/ErrorReporter');
const _excluded = ['children'],
    _excluded2 = ['children'];

/**
 * Error Boundary Integration Test
 * Tests error boundary implementations and their integration with the application
 */
function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default
    };
}

function _objectWithoutProperties(e, t) {
    if (null == e) return {};
    // eslint-disable-next-line prefer-const
    let o,
        r,
        i = _objectWithoutPropertiesLoose(e, t);
    if (Object.getOwnPropertySymbols) {
        const n = Object.getOwnPropertySymbols(e);
        for (r = 0; r < n.length; r++) {
            o = n[r];
            if (-1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o)) {
                i[o] = e[o];
            }
        }
    }
    return i;
}

function _objectWithoutPropertiesLoose(r, e) {
    if (null == r) return {};
    const t = {};
    for (const n in r) {
        if ({}.hasOwnProperty.call(r, n)) {
            if (-1 !== e.indexOf(n)) continue;
            t[n] = r[n];
        }
    }
    return t;
}

// Mock ErrorReporter
jest.mock('../../services/ErrorReporter');

// Mock framer-motion
jest.mock('framer-motion', () => ({
    motion: {
        div
=>
{
    // eslint-disable-next-line prefer-const
    let {
            children
        } = _ref,
        props = _objectWithoutProperties(_ref, _excluded);
    return /*#__PURE__*/_react.default.createElement('div', props, children);
}
,
button => {
    // eslint-disable-next-line prefer-const
    let {
            children
        } = _ref2,
        props = _objectWithoutProperties(_ref2, _excluded2);
    return /*#__PURE__*/_react.default.createElement('button', props, children);
}
},
AnimatePresence: ({
                      children
                  }) => children
}))
;

// Test component that throws errors
const ErrorThrowingComponent = ({
                                    shouldThrow,
                                    errorType,
                                    errorMessage
                                }) => {
    if (shouldThrow) {
        switch (errorType) {
            case 'render'
                row
                new Error(errorMessage || 'Render error');
            case 'network': {
                const networkError = new Error(errorMessage || 'Network error');
                networkError.name = 'NetworkError';
                throw networkError;
            }
            case 'component': {
                const componentError = new Error(errorMessage || 'Component initialization error');
                componentError.name = 'ComponentError';
                throw componentError;
            }
            case 'trading': {
                const tradingError = new Error(errorMessage || 'Trading system error');
                tradingError.name = 'TradingError';
                throw tradingError;
            }
            default
                new Error(errorMessage || 'Generic error');
        }
    }
    return /*#__PURE__*/_react.default.createElement('div', {
        'data-testid': 'working-component'
    }, 'Component is working');
};
describe('Error Boundary Integration Tests', () => {
    let mockErrorReporter;
    beforeEach(() => {
        jest.clearAllMocks();

        // Mock ErrorReporter
        mockErrorReporter = {
            report(),
            reportComponentError(),
            reportNetworkError(),
            reportTradingError()
        };
        _ErrorReporter.ErrorReporter.mockImplementation(() => mockErrorReporter);

        // Suppress console.error for cleaner test output
        jest.spyOn(console, 'error').mockImplementation(() => {
        });
    });
    afterEach(() => {
        console.error.mockRestore();
    });
    describe('ApplicationErrorBoundary', () => {
        test('should catch and handle component errors', () => {
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'TestComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorType: 'component',
                errorMessage: 'Component failed to initialize'
            })));

            // Should display error fallback UI
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should report error
            expect(mockErrorReporter.report).toHaveBeenCalledWith(expect.objectContaining({
                error: 'Component failed to initialize',
                component: 'TestComponent'
            }));
        });
        test('should handle network errors with appropriate fallback', () => {
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'NetworkComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorType: 'network',
                errorMessage: 'Failed to fetch data'
            })));

            // Should display network error fallback
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/network/i) || _react2.screen.getByText(/connection/i)).toBeInTheDocument();
            });

            // Should report network error
            expect(mockErrorReporter.report).toHaveBeenCalled();
        });
        test('should attempt automatic recovery for recoverable errors', () => {
            const {
                rerender
            } = (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'RecoverableComponent',
                autoRecovery
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorType: 'network',
                errorMessage: 'Temporary network error'
            })));

            // Should show error initially
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Look for retry button
            const retryButton = _react2.screen.getByRole('button', {
                name: /retry/i
            });
            expect(retryButton).toBeInTheDocument();

            // Simulate recovery by re-rendering without error
            _react2.fireEvent.click(retryButton);

            // Re-render with working component
            rerender(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'RecoverableComponent',
                autoRecovery
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow
            })));

            // Should show working component
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByTestId('working-component')).toBeInTheDocument();
            });
        });
        test('should enable graceful degradation for optional components', () => {
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'OptionalComponent',
                gracefulDegradation,
                isOptional
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorType: 'component',
                errorMessage: 'Optional component failed'
            })));

            // Should show degraded mode message instead of full error
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/degraded mode/i) || _react2.screen.getByText(/limited functionality/i)).toBeInTheDocument();
            });
        });
        test('should track error metrics and history', () => {
            const {
                rerender
            } = (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'MetricsComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorType: 'component',
                errorMessage: 'First error'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Trigger another error
            rerender(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'MetricsComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorType: 'network',
                errorMessage: 'Second error'
            })));

            // Should track multiple errors
            expect(mockErrorReporter.report).toHaveBeenCalledTimes(2);
        });
        test('should handle trading system errors with specialized recovery', () => {
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'TradingComponent',
                errorType: 'trading'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorType: 'trading',
                errorMessage: 'Trading system failure'
            })));

            // Should show trading-specific error handling
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/trading/i) || _react2.screen.getByText(/system/i)).toBeInTheDocument();
            });

            // Should report trading error
            expect(mockErrorReporter.report).toHaveBeenCalledWith(expect.objectContaining({
                error: 'Trading system failure'
            }));
        });
    });
    describe('ErrorBoundary', () => {
        test('should catch and display basic error information', () => {
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ErrorBoundary.default, {
                componentName: 'BasicComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'Basic error occurred'
            })));

            // Should display error message
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/error/i)).toBeInTheDocument();
            });

            // Should report error
            expect(mockErrorReporter.report).toHaveBeenCalled();
        });
        test('should provide retry functionality', () => {
            const {
                rerender
            } = (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ErrorBoundary.default, {
                componentName: 'RetryComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'Retryable error'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/error/i)).toBeInTheDocument();
            });

            // Look for retry button
            const retryButton = _react2.screen.getByRole('button', {
                name: /retry/i
            });
            _react2.fireEvent.click(retryButton);

            // Simulate successful retry
            rerender(/*#__PURE__*/_react.default.createElement(_ErrorBoundary.default, {
                componentName: 'RetryComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByTestId('working-component')).toBeInTheDocument();
            });
        });
        test('should track error frequency and prevent infinite retry loops', () => {
            const {
                rerender
            } = (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ErrorBoundary.default, {
                componentName: 'FrequencyComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'Frequent error'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/error/i)).toBeInTheDocument();
            });

            // Simulate multiple rapid errors
            for (let i = 0; i < 5; i += 1) {
                rerender(/*#__PURE__*/_react.default.createElement(_ErrorBoundary.default, {
                    componentName: 'FrequencyComponent'
                }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                    shouldThrow,
                    errorMessage: `Error ${i + 1}`
                })));
            }

            // Should eventually disable retry or show different message
            await(0, _react2.waitFor)(() => {
                expect(mockErrorReporter.report).toHaveBeenCalled();
            });
        });
    });
    describe('EnhancedErrorBoundary', () => {
        test('should provide comprehensive error reporting', () => {
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_EnhancedErrorBoundary.default, null, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'Enhanced error test'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/error/i)).toBeInTheDocument();
            });

            // Should report with enhanced details
            expect(mockErrorReporter.report).toHaveBeenCalledWith(expect.objectContaining({
                error(String),
                timestamp(String)
            }));
        });
        test('should maintain error history', () => {
            const {
                rerender
            } = (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_EnhancedErrorBoundary.default, null, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'First enhanced error'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/error/i)).toBeInTheDocument();
            });

            // Trigger second error
            rerender(/*#__PURE__*/_react.default.createElement(_EnhancedErrorBoundary.default, null, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'Second enhanced error'
            })));

            // Should track error history
            expect(mockErrorReporter.report).toHaveBeenCalledTimes(2);
        });
        test('should integrate with external error reporting services', () => {
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_EnhancedErrorBoundary.default, null, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'External reporting test'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/error/i)).toBeInTheDocument();
            });

            // Should call error reporter with proper format
            expect(mockErrorReporter.report).toHaveBeenCalledWith(expect.objectContaining({
                error(String),
                stack(String),
                timestamp(String)
            }));
        });
    });
    describe('Error Boundary Recovery Mechanisms', () => {
        test('should handle component restart scenarios', () => {
            const {
                rerender
            } = (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'RestartComponent',
                autoRecovery
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorType: 'component',
                errorMessage: 'Component needs restart'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Look for restart button
            const restartButton = _react2.screen.getByRole('button', {
                name: /restart/i
            });
            _react2.fireEvent.click(restartButton);

            // Simulate successful restart
            rerender(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'RestartComponent',
                autoRecovery
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByTestId('working-component')).toBeInTheDocument();
            });
        });
        test('should implement circuit breaker pattern for repeated failures', () => {
            const {
                rerender
            } = (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'CircuitBreakerComponent',
                maxRetries
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'Repeated failure'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Simulate multiple failures
            for (let i = 0; i < 3; i++) {
                const retryButton = _react2.screen.queryByRole('button', {
                    name: /retry/i
                });
                if (retryButton) {
                    _react2.fireEvent.click(retryButton);
                    rerender(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                        componentName: 'CircuitBreakerComponent',
                        maxRetries
                    }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                        shouldThrow,
                        errorMessage: `Failure ${i + 1}`
                    })));
                }
            }

            // Should eventually stop retrying and show circuit breaker message
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/circuit breaker/i) || _react2.screen.getByText(/too many failures/i)).toBeInTheDocument();
            });
        });
        test('should handle async component errors', () => {
            const AsyncErrorComponent = () => {
                _react.default.useEffect(() => {
                    // Simulate async error
                    setTimeout(() => {
                        throw new Error('Async error occurred');
                    }, 100);
                }, []);
                return /*#__PURE__*/_react.default.createElement('div', null, 'Async component loading...');
            };
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'AsyncComponent'
            }, /*#__PURE__*/_react.default.createElement(AsyncErrorComponent, null)));

            // Should initially show loading
            expect(_react2.screen.getByText(/loading/i)).toBeInTheDocument();

            // Note error boundaries don't catch async errors by default
            // This test demonstrates the limitation and need for additional error handling
        });
    });
    describe('Error Boundary Integration with Trading System', () => {
        test('should handle trading component failures gracefully', () => {
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'TradingDashboard',
                errorType: 'trading',
                gracefulDegradation
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorType: 'trading',
                errorMessage: 'Trading dashboard failed'
            })));

            // Should show trading-specific error handling
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/trading/i) || _react2.screen.getByText(/dashboard/i)).toBeInTheDocument();
            });

            // Should provide trading-specific recovery options
            const emergencyStopButton = _react2.screen.queryByRole('button', {
                name: /emergency stop/i
            });
            if (emergencyStopButton) {
                expect(emergencyStopButton).toBeInTheDocument();
            }
        });
        test('should isolate trading errors from UI components', () => {
            const WorkingUIComponent = () => /*#__PURE__*/_react.default.createElement('div', {
                'data-testid': 'ui-component'
            }, 'UI is working');
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement('div', null, /*#__PURE__*/_react.default.createElement(WorkingUIComponent, null), /*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'TradingEngine',
                errorType: 'trading'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorType: 'trading',
                errorMessage: 'Trading engine error'
            }))));

            // UI component should still work
            expect(_react2.screen.getByTestId('ui-component')).toBeInTheDocument();

            // Trading error should be contained
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });
        });
        test('should provide emergency protocols for critical trading errors', () => {
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'CriticalTradingComponent',
                errorType: 'trading',
                isCritical
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorType: 'trading',
                errorMessage: 'Critical trading system failure'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/critical/i) || _react2.screen.getByText(/emergency/i)).toBeInTheDocument();
            });

            // Should provide emergency stop functionality
            const emergencyButton = _react2.screen.queryByRole('button', {
                name: /emergency/i
            });
            if (emergencyButton) {
                expect(emergencyButton).toBeInTheDocument();
            }
        });
    });
    describe('Error Boundary Performance and Memory', () => {
        test('should not cause memory leaks with frequent errors', () => {
            const {
                rerender,
                unmount
            } = (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'MemoryTestComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow
            })));

            // Simulate many error/recovery cycles
            for (let i = 0; i < 50; i += 1) {
                rerender(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                    componentName: 'MemoryTestComponent'
                }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                    shouldThrow % 2 === 0,
                    errorMessage
            :
                `Error ${i}`
            })))
                ;
            }

            // Should handle cleanup properly
            unmount();

            // Memory usage should be reasonable (this is more of a conceptual test)
            expect(mockErrorReporter.report).toHaveBeenCalled();
        });
        test('should throttle error reporting for rapid errors', () => {
            const {
                rerender
            } = (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'ThrottleTestComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'Rapid error 1'
            })));

            // Trigger rapid errors
            for (let i = 0; i < 10; i += 1) {
                rerender(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                    componentName: 'ThrottleTestComponent'
                }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                    shouldThrow,
                    errorMessage: `Rapid error ${i + 1}`
                })));
            }

            // Should not report every single error (throttling)
            await(0, _react2.waitFor)(() => {
                expect(mockErrorReporter.report.mock.calls.length).toBeLessThan(10);
            });
        });
    });
});
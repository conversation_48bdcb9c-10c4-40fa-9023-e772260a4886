eslint - disable
'use strict';

const _react = _interopRequireDefault(require('react'));
const _react2 = require('@testing-library/react');
require('@testing-library/jest-dom');
const _ApplicationErrorBoundary = _interopRequireDefault(require('../../components/ApplicationErrorBoundary'));
const _ErrorReporter = require('../../services/ErrorReporter');
const _ipcService = _interopRequireDefault(require('../../services/ipcService'));
const _excluded = ['children'];
/**
 * Error Reporting Backend Integration Test
 * Tests error reporting to backend systems and external services
 */
eslint - disable
no - console

function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default
    };
}

function _objectWithoutProperties(e, t) {
    if (null == e) return {};
    let o,
        r,
        i = _objectWithoutPropertiesLoose(e, t);
    if (Object.getOwnPropertySymbols) {
        const n = Object.getOwnPropertySymbols(e);
        for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);
    }
    return i;
}

function _objectWithoutPropertiesLoose(r, e) {
    if (null == r) return {};
    const t = {};
    for (const n in r) if ({}.hasOwnProperty.call(r, n)) {
        if (-1 !== e.indexOf(n)) continue;
        t[n] = r[n];
    }
    return t;
}

// Mock services
jest.mock('../../services/ErrorReporter');
jest.mock('../../services/ipcService');

// Mock framer-motion
jest.mock('framer-motion', () => ({
    motion: {
        div
=>
{
    let {
            children
        } = _ref,
        props = _objectWithoutProperties(_ref, _excluded);
    return /*#__PURE__*/_react.default.createElement('div', props, children);
}
},
AnimatePresence: ({
                      children
                  }) => children
}))
;

// Test component that throws errors
const ErrorThrowingComponent = ({
                                    shouldThrow,
                                    errorType,
                                    errorMessage
                                }) => {
    if (shouldThrow) {
        const error = new Error(errorMessage || 'Test error');
        error.name = errorType || 'TestError';
        error.stack = `Error: ${errorMessage}\n    at ErrorThrowingComponent\n    at TestComponent`;
        throw error;
    }
    return /*#__PURE__*/_react.default.createElement('div', {
        'data-testid': 'working-component'
    }, 'Component is working');
};
describe('Error Reporting Backend Integration', () => {
    let mockErrorReporter;
    let mockIpcService;
    beforeEach(() => {
        jest.clearAllMocks();

        // Mock ErrorReporter
        mockErrorReporter = {
            report().mockResolvedValue({
                success,
                id: 'error-123'
            }),
            reportComponentError().mockResolvedValue({
                success,
                id: 'comp-error-123'
            }),
            reportNetworkError().mockResolvedValue({
                success,
                id: 'net-error-123'
            }),
            reportTradingError().mockResolvedValue({
                success,
                id: 'trade-error-123'
            }),
            reportCriticalError().mockResolvedValue({
                success,
                id: 'critical-error-123'
            }),
            getErrorStatistics().mockReturnValue({
                totalErrors,
                reportedErrors,
                failedReports
            }),
            isHealthy().mockReturnValue(true)
        };
        _ErrorReporter.ErrorReporter.mockImplementation(() => mockErrorReporter);

        // Mock IPC service
        mockIpcService = {
            reportError().mockResolvedValue({
                success
            }),
            getSystemHealth().mockResolvedValue({
                success,
                data: {
                    status: 'healthy'
                }
            }),
            logError().mockResolvedValue({
                success
            })
        };
        Object.assign(_ipcService.default, mockIpcService);

        // Suppress console.error for cleaner test output
        jest.spyOn(console, 'error').mockImplementation(() => {
        });
    });
    afterEach(() => {
        console.error.mockRestore();
    });
    describe('Error Reporting to Backend Services', () => {
        test('should report component errors to backend', () => {
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'TestComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorType: 'ComponentError',
                errorMessage: 'Component initialization failed'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should report error to backend
            expect(mockErrorReporter.report).toHaveBeenCalledWith(expect.objectContaining({
                error: 'Component initialization failed',
                component: 'TestComponent',
                timestamp(String),
                stack(String)
            }));

            // Should also report via IPC to main process
            expect(mockIpcService.reportError).toHaveBeenCalledWith(expect.objectContaining({
                error: 'Component initialization failed',
                component: 'TestComponent'
            }));
        });
        test('should report network errors with specific classification', () => {
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'NetworkComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorType: 'NetworkError',
                errorMessage: 'Failed to fetch market data'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/network/i) || _react2.screen.getByText(/connection/i)).toBeInTheDocument();
            });

            // Should report as network error
            expect(mockErrorReporter.reportNetworkError).toHaveBeenCalledWith(expect.objectContaining({
                error: 'Failed to fetch market data',
                errorType: 'NetworkError'
            }));
        });
        test('should report trading errors with high priority', () => {
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'TradingEngine',
                errorType: 'trading'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorType: 'TradingError',
                errorMessage: 'Trade execution failed'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should report as trading error with high priority
            expect(mockErrorReporter.reportTradingError).toHaveBeenCalledWith(expect.objectContaining({
                error: 'Trade execution failed',
                priority: 'high',
                component: 'TradingEngine'
            }));
        });
        test('should report critical errors immediately', () => {
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'CriticalSystem',
                isCritical
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorType: 'CriticalError',
                errorMessage: 'System failure - immediate attention required'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/critical/i) || _react2.screen.getByText(/emergency/i)).toBeInTheDocument();
            });

            // Should report as critical error
            expect(mockErrorReporter.reportCriticalError).toHaveBeenCalledWith(expect.objectContaining({
                error: 'System failure - immediate attention required',
                severity: 'critical',
                immediate
            }));
        });
        test('should include system context in error reports', () => {
            // Mock system health data
            mockIpcService.getSystemHealth.mockResolvedValue({
                success,
                data: {
                    status: 'degraded',
                    cpu,
                    memory,
                    components: {
                        database: 'connected',
                        trading: 'error'
                    }
                }
            });
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'ContextualComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'Error with system context'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should include system context in error report
            expect(mockErrorReporter.report).toHaveBeenCalledWith(expect.objectContaining({
                error: 'Error with system context',
                systemContext({
                                  systemHealth({
                status: 'degraded',
                cpu,
                memory
            })
        })
        }))
            ;
        });
        test('should handle error reporting failures gracefully', () => {
            // Mock error reporting failure
            mockErrorReporter.report.mockRejectedValue(new Error('Error reporting service unavailable'));
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'ReportingFailureComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'Error that fails to report'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should attempt to report error
            expect(mockErrorReporter.report).toHaveBeenCalled();

            // Should fallback to local logging when reporting fails
            expect(mockIpcService.logError).toHaveBeenCalledWith(expect.objectContaining({
                error: 'Error that fails to report',
                reportingFailed
            }));
        });
        test('should batch error reports for performance', () => {
            const {
                rerender
            } = (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'BatchingComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow
            })));

            // Generate multiple errors quickly
            for (let i = 0; i < 5; i++) {
                rerender(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                    componentName: 'BatchingComponent'
                }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                    shouldThrow,
                    errorMessage: `Batch error ${i + 1}`
                })));
            }
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should batch reports (fewer calls than errors)
            expect(mockErrorReporter.report.mock.calls.length).toBeLessThanOrEqual(5);
        });
        test('should include user session information in error reports', () => {
            // Mock user session data
            const mockSessionData = {
                userId: 'user-123',
                sessionId: 'session-456',
                userAgent: 'Mozilla/5.0...',
                timestamp Date().toISOString()
            };
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'SessionComponent',
                sessionData
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'Error with session context'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should include session data in error report
            expect(mockErrorReporter.report).toHaveBeenCalledWith(expect.objectContaining({
                error: 'Error with session context',
                sessionData
            }));
        });
    });
    describe('Error Analytics and Monitoring', () => {
        test('should track error patterns and frequencies', () => {
            const {
                rerender
            } = (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'AnalyticsComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow
            })));

            // Generate pattern of errors
            const errorTypes = ['NetworkError', 'ComponentError', 'NetworkError', 'TradingError', 'NetworkError'];
            for (let i = 0; i < errorTypes.length; i++) {
                rerender(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                    componentName: 'AnalyticsComponent'
                }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                    shouldThrow,
                    errorType,
                    errorMessage: `${errorTypes[i]} occurred`
                })));
            }
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should track error patterns
            expect(mockErrorReporter.getErrorStatistics).toHaveBeenCalled();
        });
        test('should monitor error reporting service health', () => {
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'HealthMonitorComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'Health monitoring test error'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should check error reporting service health
            expect(mockErrorReporter.isHealthy).toHaveBeenCalled();
        });
        test('should provide error statistics for dashboard', () => {
            // Mock error statistics
            mockErrorReporter.getErrorStatistics.mockReturnValue({
                totalErrors,
                reportedErrors,
                failedReports,
                errorsByType: {
                    NetworkError,
                    ComponentError,
                    TradingError,
                    CriticalError
                },
                errorsByComponent: {
                    Dashboard,
                    TradingEngine,
                    SystemMonitor
                },
                recentErrors{
                    timestamp: '2025-01-21T10Z',
                    error: 'Recent error 1'
                },
            {
                timestamp: '2025-01-21T10Z',
                    error
            :
                'Recent error 2'
            }
        ]
        })
            ;
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'StatisticsComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'Statistics test error'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should provide comprehensive error statistics
            const stats = mockErrorReporter.getErrorStatistics();
            expect(stats.totalErrors).toBe(25);
            expect(stats.errorsByType.NetworkError).toBe(10);
            expect(stats.errorsByComponent.Dashboard).toBe(12);
        });
        test('should alert on error rate thresholds', () => {
            // Mock high error rate scenario
            mockErrorReporter.getErrorStatistics.mockReturnValue({
                totalErrors,
                recentErrorRate,
                // errors per minute
                threshold
            });
            const {
                rerender
            } = (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'ThresholdComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow
            })));

            // Generate rapid errors to exceed threshold
            for (let i = 0; i < 20; i++) {
                rerender(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                    componentName: 'ThresholdComponent'
                }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                    shouldThrow,
                    errorMessage: `Threshold error ${i + 1}`
                })));
            }
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should trigger threshold alert
            expect(mockErrorReporter.reportCriticalError).toHaveBeenCalledWith(expect.objectContaining({
                    error('Error rate threshold exceeded'),
                severity
        :
            'critical'
        }))
            ;
        });
    });
    describe('Error Recovery Coordination with Backend', () => {
        test('should coordinate recovery attempts with backend', () => {
            const {
                rerender
            } = (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'RecoveryCoordinationComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'Coordinated recovery test error'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should report error and request recovery guidance
            expect(mockErrorReporter.report).toHaveBeenCalledWith(expect.objectContaining({
                error: 'Coordinated recovery test error',
                requestRecoveryGuidance
            }));

            // Simulate recovery attempt
            const retryButton = _react2.screen.getByRole('button', {
                name: /retry/i
            });
            _react2.fireEvent.click(retryButton);

            // Should report recovery attempt
            expect(mockErrorReporter.report).toHaveBeenCalledWith(expect.objectContaining({
                type: 'recovery-attempt',
                originalError: 'Coordinated recovery test error'
            }));
        });
        test('should report recovery success/failure to backend', () => {
            const {
                rerender
            } = (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'RecoveryReportingComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'Recovery reporting test error'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Attempt recovery
            const retryButton = _react2.screen.getByRole('button', {
                name: /retry/i
            });
            _react2.fireEvent.click(retryButton);

            // Simulate successful recovery
            rerender(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'RecoveryReportingComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByTestId('working-component')).toBeInTheDocument();
            });

            // Should report successful recovery
            expect(mockErrorReporter.report).toHaveBeenCalledWith(expect.objectContaining({
                type: 'recovery-success',
                originalError: 'Recovery reporting test error',
                recoveryTime(Number)
            }));
        });
        test('should sync error state with backend systems', () => {
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'StateSyncComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'State sync test error'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should sync error state with backend
            expect(mockIpcService.reportError).toHaveBeenCalledWith(expect.objectContaining({
                error: 'State sync test error',
                componentState({
                                   hasError,
                                   errorCount(Number)
        })
        }))
            ;
        });
    });
    describe('Privacy and Security in Error Reporting', () => {
        test('should sanitize sensitive data in error reports', () => {
            const _sensitiveError = new Error('Database connection failed=secret123, apiKey=abc123xyz');
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'SensitiveDataComponent'
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should sanitize sensitive data in error report
            expect(mockErrorReporter.report).toHaveBeenCalledWith(expect.objectContaining({
                error(
            /Database connection failed.*\[REDACTED\]/
        ),
            sanitized
        }))
            ;
        });
        test('should respect user privacy preferences in error reporting', () => {
            const privacySettings = {
                allowErrorReporting,
                allowDetailedReporting,
                allowSystemInfoSharing
            };
            (0, _react2.render)(/*#__PURE__*/_react.default.createElement(_ApplicationErrorBoundary.default, {
                componentName: 'PrivacyComponent',
                privacySettings
            }, /*#__PURE__*/_react.default.createElement(ErrorThrowingComponent, {
                shouldThrow,
                errorMessage: 'Privacy-aware error reporting test'
            })));
            await(0, _react2.waitFor)(() => {
                expect(_react2.screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should respect privacy settings
            expect(mockErrorReporter.report).toHaveBeenCalledWith(expect.objectContaining({
                error: 'Privacy-aware error reporting test',
                privacyLevel: 'basic',
                systemInfoIncluded
            }));
        });
    });
});
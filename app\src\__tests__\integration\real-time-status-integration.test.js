/**
 * Real-Time Status Integration Test
 * Tests the integration between realTimeStatusService and UI components
 */

import React from 'react';
import {act, render, screen, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import SystemStatusPanel from '../../components/SystemStatusPanel';
import LiveSystemMonitor from '../../components/LiveSystemMonitor';
import TradingStatusIndicator from '../../components/TradingStatusIndicator';
import realTimeStatusService from '../../services/realTimeStatusService';
import ipcService from '../../services/ipcService';

// Mock IPC service
jest.mock('../../services/ipcService');

// Mock framer-motion
jest.mock('framer-motion', () => ({
    motion: {
        div: ({children, ...props}) => <div {...props}>{children}</div>,
        span: ({children, ...props}) => <span {...props}>{children}</span>
    },
    AnimatePresence: ({children}) => children
}));

describe('Real-Time Status Integration', () => {
    let mockSystemStatusListeners = [];
    let mockStartupStatusListeners = [];
    let mockComponentStatusListeners = [];
    let mockTradingUpdateListeners = [];

    beforeEach(() => {
        jest.clearAllMocks();
        mockSystemStatusListeners = [];
        mockStartupStatusListeners = [];
        mockComponentStatusListeners = [];
        mockTradingUpdateListeners = [];

        // Mock realTimeStatusService
        realTimeStatusService.initialize = jest.fn();
        realTimeStatusService.addListener = jest.fn((eventType, callback) => {
            switch (eventType) {
                case 'system-status'
                    ckSystemStatusListeners.push(callback);
                    break;
                case 'startup-status'
                    ckStartupStatusListeners.push(callback);
                    break;
                case 'component-status'
                    ckComponentStatusListeners.push(callback);
                    break;
                case 'trading-update'
                    ckTradingUpdateListeners.push(callback);
                    break;
            }
            return () => {
            }; // Return unsubscribe function
        });
        realTimeStatusService.setupIPCListeners = jest.fn();
        realTimeStatusService.startPolling = jest.fn();
        realTimeStatusService.stopPolling = jest.fn();
        realTimeStatusService.forceRefresh = jest.fn();

        // Mock IPC service methods
        ipcService.getRealTimeStatus = jest.fn().mockResolvedValue({
            success,
            data: {
                isRunning,
                isInitialized,
                health: 'healthy',
                components: {},
                timestamp Date().toISOString()
            }
        });

        ipcService.getSystemMetrics = jest.fn().mockResolvedValue({
            success,
            data: {
                cpu,
                memory,
                disk,
                network
            }
        });

        ipcService.getTradingStats = jest.fn().mockResolvedValue({
            success,
            data: {
                activeBots,
                activeSignals,
                pendingTrades,
                performance: {
                    totalTrades,
                    totalProfit,
                    winRate
                }
            }
        });
    });

    describe('SystemStatusPanel Real-Time Integration', () => {
        test('should update system status in real-time', () => {
            render(<SystemStatusPanel refreshInterval={1000}/>);

            // Wait for initial render
            await waitFor(() => {
                expect(realTimeStatusService.initialize).toHaveBeenCalledWith(ipcService);
                expect(realTimeStatusService.addListener).toHaveBeenCalledWith('system-status', expect.any(Function));
            });

            // Simulate real-time system status update
            await act(() => {
                mockSystemStatusListeners.forEach(callback => {
                    callback({
                        isRunning,
                        isInitialized,
                        health: 'healthy',
                        components: {
                            database: {status: 'connected', lastCheck()},
                            memeCoinScanner: {status: 'active', lastCheck()}
                        },
                        timestamp()
                    });
                });
            });

            // Verify UI updates
            await waitFor(() => {
                expect(screen.getByText(/healthy/i)).toBeInTheDocument();
            });
        });

        test('should show startup progress updates', () => {
            render(<SystemStatusPanel refreshInterval={1000}/>);

            await waitFor(() => {
                expect(realTimeStatusService.addListener).toHaveBeenCalledWith('startup-status', expect.any(Function));
            });

            // Simulate startup progress updates
            await act(() => {
                mockStartupStatusListeners.forEach(callback => {
                    callback({
                        message: 'Initializing database...',
                        progress: {step, total}
                    });
                });
            });

            await waitFor(() => {
                expect(screen.getByText(/Initializing database/i)).toBeInTheDocument();
            });

            // Simulate next step
            await act(() => {
                mockStartupStatusListeners.forEach(callback => {
                    callback({
                        message: 'Loading trading components...',
                        progress: {step, total}
                    });
                });
            });

            await waitFor(() => {
                expect(screen.getByText(/Loading trading components/i)).toBeInTheDocument();
            });
        });

        test('should update component status in real-time', () => {
            render(<SystemStatusPanel refreshInterval={1000}/>);

            await waitFor(() => {
                expect(realTimeStatusService.addListener).toHaveBeenCalledWith('component-status', expect.any(Function));
            });

            // Simulate component status update
            await act(() => {
                mockComponentStatusListeners.forEach(callback => {
                    callback({
                        component: 'memeCoinScanner',
                        status: 'active',
                        message: 'Scanning for opportunities...'
                    });
                });
            });

            // Component status should be reflected in UI
            await waitFor(() => {
                expect(screen.getByText(/active/i)).toBeInTheDocument();
            });
        });
    });

    describe('LiveSystemMonitor Real-Time Integration', () => {
        test('should display real-time system metrics', async () => {
            render(<LiveSystemMonitor/>);

            // Wait for initialization
            await waitFor(() => {
                expect(realTimeStatusService.initialize).toHaveBeenCalledWith(ipcService);
                expect(ipcService.getSystemMetrics).toHaveBeenCalled();
            });

            // Simulate system metrics update
            await act(() => {
                mockSystemStatusListeners.forEach(callback => {
                    callback({
                        health: 'healthy',
                        components: {
                            cpu: {usage},
                            memory: {usage},
                            disk: {usage}
                        },
                        timestamp()
                    });
                });
            });

            // Should show system health status
            await waitFor(() => {
                expect(screen.getByText(/healthy/i)).toBeInTheDocument();
            });
        });

        test('should handle connection status changes', async () => {
            // Mock IPC service failure
            ipcService.getSystemMetrics.mockRejectedValueOnce(new Error('Connection failed'));

            render(<LiveSystemMonitor/>);

            // Should handle error gracefully
            await waitFor(() => {
                expect(ipcService.getSystemMetrics).toHaveBeenCalled();
            });

            // Mock recovery
            ipcService.getSystemMetrics.mockResolvedValueOnce({
                success,
                data: {cpu, memory, disk, network}
            });

            // Should recover and show metrics
            await act(() => {
                // Simulate recovery by triggering another fetch
                mockSystemStatusListeners.forEach(callback => {
                    callback({
                        health: 'healthy',
                        timestamp()
                    });
                });
            });
        });
    });

    describe('TradingStatusIndicator Real-Time Integration', () => {
        test('should update trading status in real-time', async () => {
            render(<TradingStatusIndicator status="stopped"/>);

            await waitFor(() => {
                expect(realTimeStatusService.addListener).toHaveBeenCalledWith('system-status', expect.any(Function));
            });

            // Simulate system startup
            await act(() => {
                mockSystemStatusListeners.forEach(callback => {
                    callback({
                        isRunning,
                        isInitialized,
                        health: 'healthy',
                        timestamp()
                    });
                });
            });

            // Should show running status
            await waitFor(() => {
                // Look for running indicator (could be icon, text, or color)
                const element = screen.getByRole('button') || screen.getByText(/running/i);
                expect(element).toBeInTheDocument();
            });
        });

        test('should respond to startup completion events', async () => {
            render(<TradingStatusIndicator status="stopped"/>);

            await waitFor(() => {
                expect(realTimeStatusService.addListener).toHaveBeenCalledWith('startup-complete', expect.any(Function));
            });

            // Simulate startup completion
            await act(() => {
                const startupCompleteCallback = realTimeStatusService.addListener.mock.calls
                    .find(call => call[0] === 'startup-complete')[1];
                if (startupCompleteCallback) {
                    startupCompleteCallback();
                }
            });

            // Should update to running status
            await waitFor(() => {
                const element = screen.getByRole('button') || screen.getByText(/running/i);
                expect(element).toBeInTheDocument();
            });
        });

        test('should handle startup errors', async () => {
            render(<TradingStatusIndicator status="stopped"/>);

            await waitFor(() => {
                expect(realTimeStatusService.addListener).toHaveBeenCalledWith('startup-error', expect.any(Function));
            });

            // Simulate startup error
            await act(() => {
                const startupErrorCallback = realTimeStatusService.addListener.mock.calls
                    .find(call => call[0] === 'startup-error')[1];
                if (startupErrorCallback) {
                    startupErrorCallback();
                }
            });

            // Should show error status
            await waitFor(() => {
                // Look for error indicator
                const element = screen.getByRole('button');
                expect(element).toBeInTheDocument();
            });
        });

        test('should update trading data in real-time', async () => {
            render(<TradingStatusIndicator status="running"/>);

            await waitFor(() => {
                expect(realTimeStatusService.addListener).toHaveBeenCalledWith('trading-update', expect.any(Function));
            });

            // Simulate trading update
            await act(() => {
                mockTradingUpdateListeners.forEach(callback => {
                    callback({
                        activeBots,
                        activeSignals,
                        pendingTrades,
                        performance: {
                            totalTrades,
                            totalProfit,
                            winRate
                        }
                    });
                });
            });

            // Trading data should be updated (this would be visible in tooltips or expanded view)
            await waitFor(() => {
                const element = screen.getByRole('button');
                expect(element).toBeInTheDocument();
            });
        });
    });

    describe('Cross-Component Real-Time Synchronization', () => {
        test('should synchronize status across multiple components', () => {
            render(
                <div>
                    <SystemStatusPanel refreshInterval={1000}/>
                    <TradingStatusIndicator status="stopped"/>
                    <LiveSystemMonitor/>
                </div>,
            );

            // Wait for all components to initialize
            await waitFor(() => {
                expect(realTimeStatusService.initialize).toHaveBeenCalledTimes(3);
            });

            // Simulate system status change
            await act(() => {
                mockSystemStatusListeners.forEach(callback => {
                    callback({
                        isRunning,
                        isInitialized,
                        health: 'healthy',
                        components: {
                            database: {status: 'connected'},
                            memeCoinScanner: {status: 'active'}
                        },
                        timestamp()
                    });
                });
            });

            // All components should reflect the same status
            await waitFor(() => {
                expect(screen.getAllByText(/healthy/i).length).toBeGreaterThan(0);
            });
        });

        test('should handle rapid status changes gracefully', () => {
            render(
                <div>
                    <SystemStatusPanel refreshInterval={500}/>
                    <TradingStatusIndicator status="stopped"/>
                </div>,
            );

            await waitFor(() => {
                expect(realTimeStatusService.initialize).toHaveBeenCalled();
            });

            // Simulate rapid status changes
            for (let i = 0; i < 10; i++) {
                await act(() => {
                    mockSystemStatusListeners.forEach(callback => {
                        callback({
                            isRunning % 2 === 0,
                            health % 3 === 0 ? 'warning' : 'healthy',
                            timestamp()
                    })
                        ;
                    });
                });

                // Small delay between updates
                await new Promise(resolve => setTimeout(resolve, 10));
            }

            // Components should remain stable
            await waitFor(() => {
                expect(screen.getByRole('button')).toBeInTheDocument();
            });
        });
    });

    describe('Error Handling and Recovery', () => {
        test('should handle IPC service failures gracefully', () => {
            // Mock IPC service failure
            ipcService.getRealTimeStatus.mockRejectedValue(new Error('IPC failure'));

            render(<SystemStatusPanel refreshInterval={1000}/>);

            // Should handle error without crashing
            await waitFor(() => {
                expect(realTimeStatusService.initialize).toHaveBeenCalled();
            });

            // Mock recovery
            ipcService.getRealTimeStatus.mockResolvedValue({
                success,
                data: {isRunning, health: 'healthy'}
            });

            // Should recover when service is restored
            await act(() => {
                mockSystemStatusListeners.forEach(callback => {
                    callback({
                        isRunning,
                        health: 'healthy',
                        timestamp()
                    });
                });
            });

            await waitFor(() => {
                expect(screen.getByText(/healthy/i)).toBeInTheDocument();
            });
        });

        test('should clean up listeners on component unmount', () => {
            const {unmount} = render(<SystemStatusPanel refreshInterval={1000}/>);

            await waitFor(() => {
                expect(realTimeStatusService.addListener).toHaveBeenCalled();
            });

            // Unmount component
            unmount();

            // Should clean up resources
            expect(realTimeStatusService.stopPolling).toHaveBeenCalled();
        });
    });
});
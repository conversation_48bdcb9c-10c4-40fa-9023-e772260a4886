/**
 * Comprehensive Integration Test Runner
 * Executes all comprehensive integration tests and generates a summary report
 *
 * Requirements Coverage:
 * - 4.1: UI-to-trading system communication for all major operations
 * - 4.2: Database integration with trading operations
 * - 4.3: Configuration loading and environment handling
 * - 4.4: Component lifecycle management and error recovery
 * - 4.5: Real-time status updates and monitoring
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class ComprehensiveIntegrationTestRunner {
  constructor() {
    this.testSuites = [
      {
        name: 'UI-to-Trading System Communication',
        path: './comprehensive-ui-trading-integration.test.js',
        description: 'Tests all major operations between UI and trading system',
        requirements: ['4.1', '4.5'],
      },
      {
        name: 'Database Integration',
        path: '../../../trading/__tests__/integration/comprehensive-database-integration.test.js',
        description: 'Tests database integration with trading operations',
        requirements: ['4.2', '3.5', '6.1'],
      },
      {
        name: 'Configuration and Environment',
        path: './comprehensive-configuration-integration.test.js',
        description: 'Tests configuration loading and environment handling',
        requirements: ['4.3', '6.1', '6.2', '6.3', '6.4', '6.5'],
      },
      {
        name: 'Component Lifecycle and Error Recovery',
        path: '../../../trading/__tests__/integration/comprehensive-lifecycle-error-recovery.test.js',
        description: 'Tests component lifecycle management and error recovery',
        requirements: ['4.4', '3.1', '3.2', '5.3'],
      },
    ];

    this.results = {
      totalSuites: this.testSuites.length,
      passedSuites: 0,
      failedSuites: 0,
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      skippedTests: 0,
      suiteResults: [],
      startTime: null,
      endTime: null,
      duration: 0,
    };
  }

  async runAllTests() {
    console.log('🚀 Starting Comprehensive Integration Tests...\n');
    this.results.startTime = new Date();

    for (const suite of this.testSuites) {
      await this.runTestSuite(suite);
    }

    this.results.endTime = new Date();
    this.results.duration = this.results.endTime - this.results.startTime;

    this.generateReport();
    this.saveReport();

    return this.results;
  }

  async runTestSuite(suite) {
    console.log(`📋 Running: ${suite.name}`);
    console.log(`   Description: ${suite.description}`);
    console.log(`   Requirements: ${suite.requirements.join(', ')}`);

    const suiteResult = {
      name: suite.name,
      path: suite.path,
      description: suite.description,
      requirements: suite.requirements,
      status: 'unknown',
      tests: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
      },
      duration: 0,
      errors: [],
      startTime: new Date(),
    };

    try {
      const testPath = path.resolve(__dirname, suite.path);

      if (!fs.existsSync(testPath)) {
        throw new Error(`Test file not found: ${testPath}`);
      }

      // Run Jest for this specific test file
      const jestCommand = `npx jest "${testPath}" --json --testTimeout=30000`;
      let output;
      try {
        output = execSync(jestCommand, {
          encoding: 'utf8',
          cwd: path.resolve(__dirname, '../../../..'),
          stdio: 'pipe',
        });
      } catch (error) {
        // Jest returns non-zero exit code for failed tests, but we still want the output
        output = error.stdout || error.output?.join('') || '';
      }

      let jestResult;
      try {
        jestResult = JSON.parse(output);
      } catch (parseError) {
        throw new Error(`Failed to parse Jest output: ${parseError.message}\nOutput: ${output.substring(0, 500)}...`);
      }

      // Parse Jest results
      if (jestResult.testResults && jestResult.testResults.length > 0) {
        const testResult = jestResult.testResults[0];

        suiteResult.tests.total = testResult.numPassingTests + testResult.numFailingTests + testResult.numPendingTests;
        suiteResult.tests.passed = testResult.numPassingTests;
        suiteResult.tests.failed = testResult.numFailingTests;
        suiteResult.tests.skipped = testResult.numPendingTests;
        suiteResult.duration = testResult.perfStats ? (testResult.perfStats.end - testResult.perfStats.start) : 0;

        if (testResult.numFailingTests === 0) {
          suiteResult.status = 'passed';
          this.results.passedSuites++;
        } else {
          suiteResult.status = 'failed';
          this.results.failedSuites++;
          suiteResult.errors = testResult.assertionResults
            .filter(assertion => assertion.status === 'failed')
            .map(assertion => ({
              title: assertion.title,
              message: assertion.failureMessages.join('\n'),
            }));
        }
      } else {
        suiteResult.status = 'no-tests';
        console.log(`   ⚠️  No tests found in ${suite.name}`);
      }

      // Update totals
      this.results.totalTests += suiteResult.tests.total;
      this.results.passedTests += suiteResult.tests.passed;
      this.results.failedTests += suiteResult.tests.failed;
      this.results.skippedTests += suiteResult.tests.skipped;

    } catch (error) {
      suiteResult.status = 'error';
      suiteResult.errors.push({
        title: 'Test Suite Execution Error',
        message: error.message,
      });
      this.results.failedSuites++;
      console.log(`   ❌ Error running ${suite.name}: ${error.message}`);
    }

    suiteResult.endTime = new Date();
    if (suiteResult.duration === 0) {
      suiteResult.duration = suiteResult.endTime - suiteResult.startTime;
    }

    this.results.suiteResults.push(suiteResult);

    // Print suite result
    const statusIcon = suiteResult.status === 'passed' ? '✅' :
      suiteResult.status === 'failed' ? '❌' :
        suiteResult.status === 'error' ? '💥' : '⚠️';

    console.log(`   ${statusIcon} ${suiteResult.status.toUpperCase()}: ${suiteResult.tests.passed}/${suiteResult.tests.total} tests passed (${Math.round(suiteResult.duration)}ms)\n`);
  }

  generateReport() {
    console.log('📊 Comprehensive Integration Test Results');
    console.log('=' .repeat(50));

    // Overall summary
    console.log(`Total Test Suites: ${this.results.totalSuites}`);
    console.log(`Passed Suites: ${this.results.passedSuites}`);
    console.log(`Failed Suites: ${this.results.failedSuites}`);
    console.log(`Total Tests: ${this.results.totalTests}`);
    console.log(`Passed Tests: ${this.results.passedTests}`);
    console.log(`Failed Tests: ${this.results.failedTests}`);
    console.log(`Skipped Tests: ${this.results.skippedTests}`);
    console.log(`Total Duration: ${Math.round(this.results.duration)}ms`);

    const successRate = this.results.totalTests > 0 ?
      (this.results.passedTests / this.results.totalTests * 100).toFixed(1) : 0;
    console.log(`Success Rate: ${successRate}%`);

    console.log('\n📋 Suite Details:');
    console.log('-'.repeat(50));

    this.results.suiteResults.forEach(suite => {
      const statusIcon = suite.status === 'passed' ? '✅' :
        suite.status === 'failed' ? '❌' :
          suite.status === 'error' ? '💥' : '⚠️';

      console.log(`${statusIcon} ${suite.name}`);
      console.log(`   Requirements: ${suite.requirements.join(', ')}`);
      console.log(`   Tests: ${suite.tests.passed}/${suite.tests.total} passed`);
      console.log(`   Duration: ${Math.round(suite.duration)}ms`);

      if (suite.errors.length > 0) {
        console.log('   Errors:');
        suite.errors.forEach(error => {
          console.log(`     - ${error.title}`);
        });
      }
      console.log('');
    });

    // Requirements coverage
    console.log('📋 Requirements Coverage:');
    console.log('-'.repeat(50));

    const allRequirements = [...new Set(this.testSuites.flatMap(suite => suite.requirements))];
    const coveredRequirements = this.results.suiteResults
      .filter(suite => suite.status === 'passed')
      .flatMap(suite => suite.requirements);

    allRequirements.forEach(req => {
      const covered = coveredRequirements.includes(req);
      const icon = covered ? '✅' : '❌';
      console.log(`${icon} Requirement ${req}: ${covered ? 'COVERED' : 'NOT COVERED'}`);
    });

    // Final status
    console.log('\n🎯 Final Status:');
    console.log('=' .repeat(50));

    if (this.results.failedSuites === 0 && this.results.failedTests === 0) {
      console.log('🎉 ALL COMPREHENSIVE INTEGRATION TESTS PASSED!');
      console.log('✅ All requirements are properly tested and validated.');
    } else {
      console.log('⚠️  SOME TESTS FAILED OR HAD ERRORS');
      console.log(`❌ ${this.results.failedSuites} test suite(s) failed`);
      console.log(`❌ ${this.results.failedTests} individual test(s) failed`);
      console.log('🔧 Please review and fix the failing tests.');
    }
  }

  saveReport() {
    const reportData = {
      ...this.results,
      generatedAt: new Date().toISOString(),
      testEnvironment: {
        nodeVersion: process.version,
        platform: process.platform,
        cwd: process.cwd(),
      },
    };

    const reportPath = path.resolve(__dirname, 'comprehensive-integration-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));

    console.log(`\n📄 Detailed report saved to: ${reportPath}`);

    // Also save a markdown report
    this.saveMarkdownReport(reportData);
  }

  saveMarkdownReport(reportData) {
    const markdownReport = this.generateMarkdownReport(reportData);
    const reportPath = path.resolve(__dirname, 'comprehensive-integration-test-report.md');
    fs.writeFileSync(reportPath, markdownReport);

    console.log(`📄 Markdown report saved to: ${reportPath}`);
  }

  generateMarkdownReport(reportData) {
    const successRate = reportData.totalTests > 0 ?
      (reportData.passedTests / reportData.totalTests * 100).toFixed(1) : 0;

    return `# Comprehensive Integration Test Report

Generated: ${reportData.generatedAt}

## Summary

| Metric | Value |
|--------|-------|
| Total Test Suites | ${reportData.totalSuites} |
| Passed Suites | ${reportData.passedSuites} |
| Failed Suites | ${reportData.failedSuites} |
| Total Tests | ${reportData.totalTests} |
| Passed Tests | ${reportData.passedTests} |
| Failed Tests | ${reportData.failedTests} |
| Skipped Tests | ${reportData.skippedTests} |
| Success Rate | ${successRate}% |
| Total Duration | ${Math.round(reportData.duration)}ms |

## Test Suite Results

${reportData.suiteResults.map(suite => `
### ${suite.status === 'passed' ? '✅' : suite.status === 'failed' ? '❌' : '💥'} ${suite.name}

**Description:** ${suite.description}
**Requirements:** ${suite.requirements.join(', ')}
**Status:** ${suite.status.toUpperCase()}
**Tests:** ${suite.tests.passed}/${suite.tests.total} passed
**Duration:** ${Math.round(suite.duration)}ms

${suite.errors.length > 0 ? `
**Errors:**
${suite.errors.map(error => `- **${error.title}**\n  \`\`\`\n  ${error.message}\n  \`\`\``).join('\n')}
` : ''}
`).join('\n')}

## Requirements Coverage

${[...new Set(reportData.suiteResults.flatMap(suite => suite.requirements))].map(req => {
    const covered = reportData.suiteResults
      .filter(suite => suite.status === 'passed')
      .some(suite => suite.requirements.includes(req));
    return `- ${covered ? '✅' : '❌'} **Requirement ${req}:** ${covered ? 'COVERED' : 'NOT COVERED'}`;
  }).join('\n')}

## Environment

- **Node Version:** ${reportData.testEnvironment.nodeVersion}
- **Platform:** ${reportData.testEnvironment.platform}
- **Working Directory:** ${reportData.testEnvironment.cwd}

## Conclusion

${reportData.failedSuites === 0 && reportData.failedTests === 0 ?
    '🎉 **ALL COMPREHENSIVE INTEGRATION TESTS PASSED!** All requirements are properly tested and validated.' :
    `⚠️ **SOME TESTS FAILED:** ${reportData.failedSuites} test suite(s) and ${reportData.failedTests} individual test(s) failed. Please review and fix the failing tests.`
}
`;
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const runner = new ComprehensiveIntegrationTestRunner();
  runner.runAllTests()
    .then(results => {
      process.exit(results.failedSuites === 0 && results.failedTests === 0 ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Test runner failed:', error);
      process.exit(1);
    });
}

module.exports = ComprehensiveIntegrationTestRunner;
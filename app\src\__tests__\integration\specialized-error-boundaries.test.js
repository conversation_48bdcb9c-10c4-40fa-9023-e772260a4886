/**
 * Specialized Error Boundaries Test
 * Tests specialized error boundaries for dashboard and trading components
 */

import React from 'react';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import ApplicationErrorBoundary from '../../components/ApplicationErrorBoundary';
import {ErrorReporter} from '../../services/ErrorReporter';

// Mock services
jest.mock('../../services/ErrorReporter');
jest.mock('../../services/ipcService');
jest.mock('../../services/realTimeStatusService');

// Mock framer-motion
jest.mock('framer-motion', () => ({
    motion: {
        div: ({children, ...props}) => <div {...props}>{children}</div>,
        button: ({children, ...props}) => <button {...props}>{children}</button>
    },
    AnimatePresence: ({children}) => children
}));

// Create specialized error boundary components
const DashboardErrorBoundary = ({children}) => (
    <ApplicationErrorBoundary
        componentName="Dashboard"
        errorType="dashboard"
        gracefulDegradation={true}
        autoRecovery={true}
        maxRetries={3}
    >
        {children}
    </ApplicationErrorBoundary>
);

const TradingErrorBoundary = ({children}) => (
    <ApplicationErrorBoundary
        componentName="TradingSystem"
        errorType="trading"
        isCritical={true}
        gracefulDegradation={false}
        autoRecovery={true}
        maxRetries={2}
    >
        {children}
    </ApplicationErrorBoundary>
);

const SystemMonitoringErrorBoundary = ({children}) => (
    <ApplicationErrorBoundary
        componentName="SystemMonitoring"
        errorType="monitoring"
        isOptional={true}
        gracefulDegradation={true}
        autoRecovery={true}
        maxRetries={5}
    >
        {children}
    </ApplicationErrorBoundary>
);

// Mock components that can throw errors
const MockDashboard = ({shouldThrow, errorType}) => {
    if (shouldThrow) {
        switch (errorType) {
            case 'render'
                row
                new Error('Dashboard render error');
            case 'data'
                row
                new Error('Dashboard data loading error');
            case 'ui'
                row
                new Error('Dashboard UI component error');
            default
                new Error('Dashboard generic error');
        }
    }
    return <div data-testid="dashboard">Dashboard is working</div>;
};

const MockTradingComponent = ({shouldThrow, errorType}) => {
    if (shouldThrow) {
        let error;
        switch (errorType) {
            case 'connection'
                ror = new Error('Trading connection failed');
                error.name = 'ConnectionError';
                throw error;
            case 'execution'
                ror = new Error('Trade execution failed');
                error.name = 'ExecutionError';
                throw error;
            case 'critical'
                ror = new Error('Critical trading system failure');
                error.name = 'CriticalError';
                throw error;
            default
                new Error('Trading system error');
        }
    }
    return <div data-testid="trading-component">Trading system is working</div>;
};

const MockSystemMonitor = ({shouldThrow, errorType}) => {
    if (shouldThrow) {
        switch (errorType) {
            case 'metrics'
                row
                new Error('System metrics collection failed');
            case 'health'
                row
                new Error('Health monitoring failed');
            case 'network'
                row
                new Error('Network monitoring failed');
            default
                new Error('System monitoring error');
        }
    }
    return <div data-testid="system-monitor">System monitor is working</div>;
};

describe('Specialized Error Boundaries', () => {
    let mockErrorReporter;

    beforeEach(() => {
        jest.clearAllMocks();

        mockErrorReporter = {
            report(),
            reportDashboardError(),
            reportTradingError(),
            reportSystemError()
        };
        ErrorReporter.mockImplementation(() => mockErrorReporter);

        // Suppress console.error for cleaner test output
        jest.spyOn(console, 'error').mockImplementation(() => {
        });
    });

    afterEach(() => {
        // console.error.mockRestore();
    });

    describe('DashboardErrorBoundary', () => {
        test('should handle dashboard render errors with graceful degradation', () => {
            render(
                <DashboardErrorBoundary>
                    <MockDashboard shouldThrow={true} errorType="render"/>
                </DashboardErrorBoundary>,
            );

            // Should show degraded dashboard
            await waitFor(() => {
                expect(screen.getByText(/degraded mode/i) || screen.getByText(/limited functionality/i)).toBeInTheDocument();
            });

            // Should report dashboard error
            expect(mockErrorReporter.report).toHaveBeenCalledWith(
                expect.objectContaining({
                    error: 'Dashboard render error',
                    component: 'Dashboard'
                }),
            );
        });

        test('should provide dashboard-specific recovery options', () => {
            const {rerender} = render(
                <DashboardErrorBoundary>
                    <MockDashboard shouldThrow={true} errorType="data"/>
                </DashboardErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Should provide refresh data option
            const refreshButton = screen.getByRole('button', {name: /refresh/i});
            expect(refreshButton).toBeInTheDocument();

            fireEvent.click(refreshButton);

            // Simulate successful recovery
            rerender(
                <DashboardErrorBoundary>
                    <MockDashboard shouldThrow={false}/>
                </DashboardErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByTestId('dashboard')).toBeInTheDocument();
            });
        });

        test('should maintain dashboard functionality during partial failures', () => {
            const WorkingDashboardSection = () => <div data-testid="working-section">Working section</div>;

            render(
                <div>
                    <WorkingDashboardSection/>
                    <DashboardErrorBoundary>
                        <MockDashboard shouldThrow={true} errorType="ui"/>
                    </DashboardErrorBoundary>
                </div>,
            );

            // Working section should remain functional
            expect(screen.getByTestId('working-section')).toBeInTheDocument();

            // Failed section should show error boundary
            await waitFor(() => {
                expect(screen.getByText(/degraded mode/i) || screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });
        });

        test('should handle dashboard data loading failures', () => {
            render(
                <DashboardErrorBoundary>
                    <MockDashboard shouldThrow={true} errorType="data"/>
                </DashboardErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText(/data/i) || screen.getByText(/loading/i)).toBeInTheDocument();
            });

            // Should provide data refresh option
            const retryButton = screen.getByRole('button', {name: /retry/i});
            expect(retryButton).toBeInTheDocument();
        });
    });

    describe('TradingErrorBoundary', () => {
        test('should handle critical trading system failures', () => {
            render(
                <TradingErrorBoundary>
                    <MockTradingComponent shouldThrow={true} errorType="critical"/>
                </TradingErrorBoundary>,
            );

            // Should show critical error message
            await waitFor(() => {
                expect(screen.getByText(/critical/i) || screen.getByText(/emergency/i)).toBeInTheDocument();
            });

            // Should provide emergency stop option
            const emergencyButton = screen.queryByRole('button', {name: /emergency/i});
            if (emergencyButton) {
                expect(emergencyButton).toBeInTheDocument();
            }

            // Should report critical trading error
            expect(mockErrorReporter.report).toHaveBeenCalledWith(
                expect.objectContaining({
                    error: 'Critical trading system failure',
                    component: 'TradingSystem'
                }),
            );
        });

        test('should handle trading connection failures with automatic retry', () => {
            const {rerender} = render(
                <TradingErrorBoundary>
                    <MockTradingComponent shouldThrow={true} errorType="connection"/>
                </TradingErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText(/connection/i) || screen.getByText(/network/i)).toBeInTheDocument();
            });

            // Should provide reconnect option
            const reconnectButton = screen.getByRole('button', {name: /retry/i});
            fireEvent.click(reconnectButton);

            // Simulate successful reconnection
            rerender(
                <TradingErrorBoundary>
                    <MockTradingComponent shouldThrow={false}/>
                </TradingErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByTestId('trading-component')).toBeInTheDocument();
            });
        });

        test('should handle trade execution failures', () => {
            render(
                <TradingErrorBoundary>
                    <MockTradingComponent shouldThrow={true} errorType="execution"/>
                </TradingErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText(/execution/i) || screen.getByText(/trade/i)).toBeInTheDocument();
            });

            // Should report execution error
            expect(mockErrorReporter.report).toHaveBeenCalledWith(
                expect.objectContaining({
                    error: 'Trade execution failed'
                }),
            );
        });

        test('should implement circuit breaker for repeated trading failures', () => {
            const {rerender} = render(
                <TradingErrorBoundary>
                    <MockTradingComponent shouldThrow={true} errorType="execution"/>
                </TradingErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Simulate multiple failures
            for (let i = 0; i < 3; i++) {
                const retryButton = screen.queryByRole('button', {name: /retry/i});
                if (retryButton) {
                    fireEvent.click(retryButton);

                    rerender(
                        <TradingErrorBoundary>
                            <MockTradingComponent shouldThrow={true} errorType="execution"/>
                        </TradingErrorBoundary>,
                    );
                }
            }

            // Should activate circuit breaker
            await waitFor(() => {
                expect(screen.getByText(/circuit breaker/i) || screen.getByText(/too many failures/i)).toBeInTheDocument();
            });
        });

        test('should isolate trading errors from other components', () => {
            const WorkingUIComponent = () => <div data-testid="ui-working">UI is working</div>;

            render(
                <div>
                    <WorkingUIComponent/>
                    <TradingErrorBoundary>
                        <MockTradingComponent shouldThrow={true} errorType="critical"/>
                    </TradingErrorBoundary>
                </div>,
            );

            // UI should continue working
            expect(screen.getByTestId('ui-working')).toBeInTheDocument();

            // Trading error should be contained
            await waitFor(() => {
                expect(screen.getByText(/critical/i) || screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });
        });
    });

    describe('SystemMonitoringErrorBoundary', () => {
        test('should handle system monitoring failures gracefully', () => {
            render(
                <SystemMonitoringErrorBoundary>
                    <MockSystemMonitor shouldThrow={true} errorType="metrics"/>
                </SystemMonitoringErrorBoundary>,
            );

            // Should show degraded monitoring message
            await waitFor(() => {
                expect(screen.getByText(/monitoring/i) || screen.getByText(/degraded/i)).toBeInTheDocument();
            });

            // Should report monitoring error
            expect(mockErrorReporter.report).toHaveBeenCalledWith(
                expect.objectContaining({
                    error: 'System metrics collection failed',
                    component: 'SystemMonitoring'
                }),
            );
        });

        test('should continue system operation without monitoring', () => {
            const MainSystemComponent = () => <div data-testid="main-system">Main system running</div>;

            render(
                <div>
                    <MainSystemComponent/>
                    <SystemMonitoringErrorBoundary>
                        <MockSystemMonitor shouldThrow={true} errorType="health"/>
                    </SystemMonitoringErrorBoundary>
                </div>,
            );

            // Main system should continue working
            expect(screen.getByTestId('main-system')).toBeInTheDocument();

            // Monitoring should show degraded state
            await waitFor(() => {
                expect(screen.getByText(/monitoring/i) || screen.getByText(/degraded/i)).toBeInTheDocument();
            });
        });

        test('should provide monitoring restart options', () => {
            const {rerender} = render(
                <SystemMonitoringErrorBoundary>
                    <MockSystemMonitor shouldThrow={true} errorType="network"/>
                </SystemMonitoringErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText(/network/i) || screen.getByText(/monitoring/i)).toBeInTheDocument();
            });

            // Should provide restart monitoring option
            const restartButton = screen.getByRole('button', {name: /restart/i});
            fireEvent.click(restartButton);

            // Simulate successful restart
            rerender(
                <SystemMonitoringErrorBoundary>
                    <MockSystemMonitor shouldThrow={false}/>
                </SystemMonitoringErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByTestId('system-monitor')).toBeInTheDocument();
            });
        });

        test('should handle multiple monitoring component failures', () => {
            const MetricsMonitor = () => <MockSystemMonitor shouldThrow={true} errorType="metrics"/>;
            const HealthMonitor = () => <MockSystemMonitor shouldThrow={true} errorType="health"/>;

            render(
                <div>
                    <SystemMonitoringErrorBoundary>
                        <MetricsMonitor/>
                    </SystemMonitoringErrorBoundary>
                    <SystemMonitoringErrorBoundary>
                        <HealthMonitor/>
                    </SystemMonitoringErrorBoundary>
                </div>,
            );

            // Both monitoring components should show error states
            await waitFor(() => {
                const errorElements = screen.getAllByText(/monitoring/i);
                expect(errorElements.length).toBeGreaterThan(0);
            });

            // Should report multiple errors
            expect(mockErrorReporter.report).toHaveBeenCalledTimes(2);
        });
    });

    describe('Error Boundary Coordination', () => {
        test('should coordinate error handling across multiple boundaries', () => {
            render(
                <div>
                    <DashboardErrorBoundary>
                        <MockDashboard shouldThrow={true} errorType="render"/>
                    </DashboardErrorBoundary>
                    <TradingErrorBoundary>
                        <MockTradingComponent shouldThrow={true} errorType="connection"/>
                    </TradingErrorBoundary>
                    <SystemMonitoringErrorBoundary>
                        <MockSystemMonitor shouldThrow={false}/>
                    </SystemMonitoringErrorBoundary>
                </div>,
            );

            // Dashboard should show degraded mode
            await waitFor(() => {
                expect(screen.getByText(/degraded/i) || screen.getByText(/limited/i)).toBeInTheDocument();
            });

            // Trading should show connection error
            await waitFor(() => {
                expect(screen.getByText(/connection/i) || screen.getByText(/network/i)).toBeInTheDocument();
            });

            // System monitor should work normally
            expect(screen.getByTestId('system-monitor')).toBeInTheDocument();

            // Should report errors from multiple boundaries
            expect(mockErrorReporter.report).toHaveBeenCalledTimes(2);
        });

        test('should handle cascading failures gracefully', () => {
            const CascadingComponent = ({shouldThrow}) => {
                if (shouldThrow) {
                    throw new Error('Cascading failure');
                }
                return <div data-testid="cascading-component">Cascading component working</div>;
            };

            const {rerender} = render(
                <div>
                    <TradingErrorBoundary>
                        <CascadingComponent shouldThrow={true}/>
                    </TradingErrorBoundary>
                    <DashboardErrorBoundary>
                        <CascadingComponent shouldThrow={true}/>
                    </DashboardErrorBoundary>
                </div>,
            );

            // Both should show error states
            await waitFor(() => {
                const errorElements = screen.getAllByText(/something went wrong/i);
                expect(errorElements.length).toBe(2);
            });

            // Simulate recovery of one component
            rerender(
                <div>
                    <TradingErrorBoundary>
                        <CascadingComponent shouldThrow={false}/>
                    </TradingErrorBoundary>
                    <DashboardErrorBoundary>
                        <CascadingComponent shouldThrow={true}/>
                    </DashboardErrorBoundary>
                </div>,
            );

            // One should recover, one should remain in error state
            await waitFor(() => {
                expect(screen.getByTestId('cascading-component')).toBeInTheDocument();
                expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });
        });

        test('should maintain application stability during multiple component failures', () => {
            const StableComponent = () => <div data-testid="stable-component">Stable component</div>;

            render(
                <div>
                    <StableComponent/>
                    <DashboardErrorBoundary>
                        <MockDashboard shouldThrow={true} errorType="render"/>
                    </DashboardErrorBoundary>
                    <TradingErrorBoundary>
                        <MockTradingComponent shouldThrow={true} errorType="critical"/>
                    </TradingErrorBoundary>
                    <SystemMonitoringErrorBoundary>
                        <MockSystemMonitor shouldThrow={true} errorType="metrics"/>
                    </SystemMonitoringErrorBoundary>
                </div>,
            );

            // Stable component should remain working
            expect(screen.getByTestId('stable-component')).toBeInTheDocument();

            // All error boundaries should handle their respective errors
            await waitFor(() => {
                expect(mockErrorReporter.report).toHaveBeenCalledTimes(3);
            });

            // Application should remain responsive
            const buttons = screen.getAllByRole('button');
            expect(buttons.length).toBeGreaterThan(0);
        });
    });

    describe('Error Boundary Recovery Validation', () => {
        test('should validate successful recovery before clearing error state', () => {
            const ValidatingComponent = ({shouldThrow, validationData}) => {
                if (shouldThrow) {
                    throw new Error('Validation test error');
                }
                if (!validationData) {
                    throw new Error('Invalid data after recovery');
                }
                return <div data-testid="validated-component">Component validated and working</div>;
            };

            const {rerender} = render(
                <DashboardErrorBoundary>
                    <ValidatingComponent shouldThrow={true}/>
                </DashboardErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Attempt recovery with invalid data
            const retryButton = screen.getByRole('button', {name: /retry/i});
            fireEvent.click(retryButton);

            rerender(
                <DashboardErrorBoundary>
                    <ValidatingComponent shouldThrow={false} validationData={null}/>
                </DashboardErrorBoundary>,
            );

            // Should show error again due to validation failure
            await waitFor(() => {
                expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
            });

            // Retry with valid data
            const retryButton2 = screen.getByRole('button', {name: /retry/i});
            fireEvent.click(retryButton2);

            rerender(
                <DashboardErrorBoundary>
                    <ValidatingComponent shouldThrow={false} validationData={true}/>
                </DashboardErrorBoundary>,
            );

            // Should successfully recover
            await waitFor(() => {
                expect(screen.getByTestId('validated-component')).toBeInTheDocument();
            });
        });
    });
});
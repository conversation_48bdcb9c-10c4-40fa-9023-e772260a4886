/**
 * @fileoverview Complete Start Button Integration Test
 * @description Tests the complete workflow from Start button click to TradingOrchestrator initialization
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import StartButton from '../../components/StartButton';
import EnhancedStartButton from '../../components/EnhancedStartButton';
import AutonomousDashboard from '../../components/AutonomousDashboard';

// Mock electron API
const mockElectronAPI = {
  startBot: jest.fn(),
  stopBot: jest.fn(),
  getSystemStatus: jest.fn(),
  getRealTimeStatus: jest.fn(),
  getComponentHealth: jest.fn(),
  getSystemHealth: jest.fn(),
  getConfig: jest.fn(),
  on: jest.fn(),
  removeAllListeners: jest.fn(),
};

// Mock window.electronAPI
Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
});

// Mock IPC service
jest.mock('../../services/ipcService', () => ({
  startBot: jest.fn(),
  stopBot: jest.fn(),
  getSystemStatus: jest.fn(),
  getRealTimeStatus: jest.fn(),
  getComponentHealth: jest.fn(),
  getSystemHealth: jest.fn(),
}));

// Mock real-time status service
jest.mock('../../services/realTimeStatusService', () => ({
  default: {
    initialize: jest.fn().mockResolvedValue(),
    getStatus: jest.fn().mockResolvedValue({
      isInitialized: true,
      health: 'healthy',
      components: {},
      timestamp: Date.now(),
    }),
    addListener: jest.fn().mockReturnValue(() => {}),
    updateStatus: jest.fn(),
  },
}));

describe('Start Button Complete Integration Test', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mock responses
    mockElectronAPI.getSystemStatus.mockResolvedValue({
      initialized: false,
      running: false,
      timestamp: new Date().toISOString(),
    });

    mockElectronAPI.startBot.mockResolvedValue({ success: true });
    mockElectronAPI.stopBot.mockResolvedValue({ success: true });

    mockElectronAPI.getRealTimeStatus.mockResolvedValue({
      system: {
        isRunning: true,
        initialized: true,
        health: 'healthy',
        message: 'Trading system running',
      },
      health: { status: 'healthy' },
      metrics: { cpu: { usage: 0 }, memory: { used: 0, total: 0 } },
      timestamp: new Date().toISOString(),
    });

    mockElectronAPI.getComponentHealth.mockResolvedValue({
      autonomousTrader: { status: 'healthy', initialized: true },
      memeCoinScanner: { status: 'healthy', initialized: true },
      sentimentAnalyzer: { status: 'healthy', initialized: true },
      performanceTracker: { status: 'healthy', initialized: true },
    });

    mockElectronAPI.getSystemHealth.mockResolvedValue({
      status: 'healthy',
      checks: [
        { name: 'database', status: 'healthy' },
        { name: 'trading-components', status: 'healthy' },
        { name: 'configuration', status: 'healthy' },
      ],
      timestamp: new Date().toISOString(),
    });

    mockElectronAPI.getConfig.mockResolvedValue({
      'risk-management': { maxPositionSize: 1000 },
      'monitoring': { enabled: true },
      'feature-flags': { autonomousTrading: true },
    });
  });

  describe('Basic Start Button Functionality', () => {
    test('should render Start button correctly', () => {
      render(<StartButton />);

      const startButton = screen.getByRole('button');
      expect(startButton).toBeInTheDocument();
      expect(startButton).toHaveTextContent(/start/i);
    });

    test('should handle Start button click', async () => {
      render(<StartButton />);

      const startButton = screen.getByRole('button');
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(mockElectronAPI.startBot).toHaveBeenCalled();
      });
    });

    test('should show loading state during startup', async () => {
      // Mock a delayed response
      mockElectronAPI.startBot.mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve({ success: true }), 100)),
      );

      render(<StartButton />);

      const startButton = screen.getByRole('button');
      fireEvent.click(startButton);

      // Should show loading state
      await waitFor(() => {
        expect(startButton).toBeDisabled();
      });

      // Should return to normal state after completion
      await waitFor(() => {
        expect(startButton).not.toBeDisabled();
      }, { timeout: 2000 });
    });
  });

  describe('Enhanced Start Button Integration', () => {
    test('should show startup progress', async () => {
      render(<EnhancedStartButton />);

      const startButton = screen.getByRole('button');
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(screen.getByText(/initializing/i) || screen.getByText(/starting/i)).toBeInTheDocument();
      });
    });

    test('should handle startup errors gracefully', async () => {
      mockElectronAPI.startBot.mockRejectedValue(new Error('Database connection failed'));

      render(<EnhancedStartButton />);

      const startButton = screen.getByRole('button');
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(screen.getByText(/error/i) || screen.getByText(/failed/i)).toBeInTheDocument();
      });
    });

    test('should show retry option on failure', async () => {
      mockElectronAPI.startBot.mockRejectedValue(new Error('Startup failed'));

      render(<EnhancedStartButton />);

      const startButton = screen.getByRole('button');
      fireEvent.click(startButton);

      await waitFor(() => {
        const retryButton = screen.queryByText(/retry/i);
        expect(retryButton).toBeInTheDocument();
      });
    });
  });

  describe('Complete Workflow Integration', () => {
    test('should complete full startup workflow', async () => {
      render(<AutonomousDashboard />);

      // Find and click the start button
      const startButton = screen.getByRole('button', { name: /start/i });
      fireEvent.click(startButton);

      // Step 1: Should call startBot
      await waitFor(() => {
        expect(mockElectronAPI.startBot).toHaveBeenCalled();
      });

      // Step 2: Should check system status
      await waitFor(() => {
        expect(mockElectronAPI.getSystemStatus).toHaveBeenCalled();
      });

      // Step 3: Should get real-time status
      await waitFor(() => {
        expect(mockElectronAPI.getRealTimeStatus).toHaveBeenCalled();
      });

      // Step 4: Should check component health
      await waitFor(() => {
        expect(mockElectronAPI.getComponentHealth).toHaveBeenCalled();
      });
    });

    test('should verify TradingOrchestrator initialization sequence', async () => {
      // Mock the initialization sequence responses
      let callCount = 0;
      mockElectronAPI.getSystemStatus.mockImplementation(() => {
        callCount++;
        return Promise.resolve({
          initialized: callCount > 1,
          running: callCount > 2,
          timestamp: new Date().toISOString(),
        });
      });

      render(<AutonomousDashboard />);

      const startButton = screen.getByRole('button', { name: /start/i });
      fireEvent.click(startButton);

      // Should show progression through initialization states
      await waitFor(() => {
        expect(mockElectronAPI.startBot).toHaveBeenCalled();
      });

      // Multiple status checks should show progression
      await waitFor(() => {
        expect(mockElectronAPI.getSystemStatus).toHaveBeenCalledTimes(1);
      });
    });

    test('should verify database initialization during startup', async () => {
      render(<AutonomousDashboard />);

      const startButton = screen.getByRole('button', { name: /start/i });
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(mockElectronAPI.startBot).toHaveBeenCalled();
      });

      // Should check system health which includes database status
      await waitFor(() => {
        expect(mockElectronAPI.getSystemHealth).toHaveBeenCalled();
      });
    });

    test('should verify configuration loading during startup', async () => {
      render(<AutonomousDashboard />);

      const startButton = screen.getByRole('button', { name: /start/i });
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(mockElectronAPI.startBot).toHaveBeenCalled();
      });

      // Should load configuration
      await waitFor(() => {
        expect(mockElectronAPI.getConfig).toHaveBeenCalled();
      });
    });

    test('should verify component startup sequence', async () => {
      render(<AutonomousDashboard />);

      const startButton = screen.getByRole('button', { name: /start/i });
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(mockElectronAPI.startBot).toHaveBeenCalled();
      });

      // Should check component health
      await waitFor(() => {
        expect(mockElectronAPI.getComponentHealth).toHaveBeenCalled();
      });

      // Verify all expected components are checked
      const componentHealth = await mockElectronAPI.getComponentHealth();
      expect(componentHealth).toHaveProperty('autonomousTrader');
      expect(componentHealth).toHaveProperty('memeCoinScanner');
      expect(componentHealth).toHaveProperty('sentimentAnalyzer');
      expect(componentHealth).toHaveProperty('performanceTracker');
    });
  });

  describe('IPC Communication Testing', () => {
    test('should handle IPC communication from UI to main process', async () => {
      render(<StartButton />);

      const startButton = screen.getByRole('button');
      fireEvent.click(startButton);

      // Should communicate through IPC
      await waitFor(() => {
        expect(mockElectronAPI.startBot).toHaveBeenCalledWith();
      });
    });

    test('should handle IPC errors gracefully', async () => {
      mockElectronAPI.startBot.mockRejectedValue(new Error('IPC communication failed'));

      render(<StartButton />);

      const startButton = screen.getByRole('button');
      fireEvent.click(startButton);

      // Should handle IPC error without crashing
      await waitFor(() => {
        expect(startButton).not.toBeDisabled();
      });
    });

    test('should handle IPC timeouts', async () => {
      mockElectronAPI.startBot.mockImplementation(() =>
        new Promise(() => {}), // Never resolves (timeout simulation)
      );

      render(<StartButton />);

      const startButton = screen.getByRole('button');
      fireEvent.click(startButton);

      // Should handle timeout gracefully
      await waitFor(() => {
        expect(startButton).toBeDisabled();
      });
    });
  });

  describe('UI Status Updates During Startup', () => {
    test('should show startup progress indicators', async () => {
      render(<EnhancedStartButton />);

      const startButton = screen.getByRole('button');
      fireEvent.click(startButton);

      // Should show progress indicators
      await waitFor(() => {
        expect(screen.getByText(/starting/i) || screen.getByText(/initializing/i)).toBeInTheDocument();
      });
    });

    test('should update UI based on system status', async () => {
      let statusCallCount = 0;
      mockElectronAPI.getSystemStatus.mockImplementation(() => {
        statusCallCount++;
        return Promise.resolve({
          initialized: statusCallCount > 1,
          running: statusCallCount > 2,
          timestamp: new Date().toISOString(),
        });
      });

      render(<AutonomousDashboard />);

      const startButton = screen.getByRole('button', { name: /start/i });
      fireEvent.click(startButton);

      // UI should update based on status changes
      await waitFor(() => {
        expect(mockElectronAPI.getSystemStatus).toHaveBeenCalled();
      });
    });

    test('should show error messages on startup failure', async () => {
      mockElectronAPI.startBot.mockRejectedValue(new Error('Startup failed'));

      render(<EnhancedStartButton />);

      const startButton = screen.getByRole('button');
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(screen.getByText(/error/i) || screen.getByText(/failed/i)).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling and Recovery', () => {
    test('should handle component initialization failures', async () => {
      mockElectronAPI.getComponentHealth.mockResolvedValue({
        autonomousTrader: { status: 'error', error: 'Failed to initialize' },
        memeCoinScanner: { status: 'healthy', initialized: true },
      });

      render(<AutonomousDashboard />);

      const startButton = screen.getByRole('button', { name: /start/i });
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(mockElectronAPI.startBot).toHaveBeenCalled();
      });

      // Should handle component errors gracefully
      await waitFor(() => {
        expect(mockElectronAPI.getComponentHealth).toHaveBeenCalled();
      });
    });

    test('should handle database connection failures', async () => {
      mockElectronAPI.getSystemHealth.mockResolvedValue({
        status: 'unhealthy',
        checks: [
          { name: 'database', status: 'error', error: 'Connection timeout' },
        ],
        timestamp: new Date().toISOString(),
      });

      render(<AutonomousDashboard />);

      const startButton = screen.getByRole('button', { name: /start/i });
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(mockElectronAPI.getSystemHealth).toHaveBeenCalled();
      });
    });

    test('should provide recovery options', async () => {
      mockElectronAPI.startBot.mockRejectedValue(new Error('Startup failed'));

      render(<EnhancedStartButton />);

      const startButton = screen.getByRole('button');
      fireEvent.click(startButton);

      await waitFor(() => {
        const retryButton = screen.queryByText(/retry/i);
        expect(retryButton).toBeInTheDocument();
      });

      // Test retry functionality
      const retryButton = screen.getByText(/retry/i);
      mockElectronAPI.startBot.mockResolvedValue({ success: true });

      fireEvent.click(retryButton);

      await waitFor(() => {
        expect(mockElectronAPI.startBot).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('Performance and User Experience', () => {
    test('should complete startup within reasonable time', async () => {
      const startTime = Date.now();

      render(<StartButton />);

      const startButton = screen.getByRole('button');
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(mockElectronAPI.startBot).toHaveBeenCalled();
      });

      const endTime = Date.now();
      const startupTime = endTime - startTime;

      // Should complete within 2 seconds for UI responsiveness
      expect(startupTime).toBeLessThan(2000);
    });

    test('should provide user feedback during startup', async () => {
      render(<EnhancedStartButton />);

      const startButton = screen.getByRole('button');
      fireEvent.click(startButton);

      // Should provide immediate feedback
      await waitFor(() => {
        expect(startButton).toBeDisabled();
      });

      // Should show progress or status messages
      await waitFor(() => {
        expect(screen.getByText(/starting/i) || screen.getByText(/loading/i)).toBeInTheDocument();
      });
    });

    test('should handle concurrent start attempts', async () => {
      render(<StartButton />);

      const startButton = screen.getByRole('button');

      // Click multiple times rapidly
      fireEvent.click(startButton);
      fireEvent.click(startButton);
      fireEvent.click(startButton);

      // Should only call startBot once
      await waitFor(() => {
        expect(mockElectronAPI.startBot).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('End-to-End Workflow Validation', () => {
    test('should complete full workflow from click to running state', async () => {
      // Mock progressive status updates
      let statusCalls = 0;
      mockElectronAPI.getSystemStatus.mockImplementation(() => {
        statusCalls++;
        return Promise.resolve({
          initialized: statusCalls >= 2,
          running: statusCalls >= 3,
          timestamp: new Date().toISOString(),
        });
      });

      render(<AutonomousDashboard />);

      // Step 1: Initial state
      const startButton = screen.getByRole('button', { name: /start/i });
      expect(startButton).toBeInTheDocument();

      // Step 2: Click start
      fireEvent.click(startButton);

      // Step 3: Verify startup initiated
      await waitFor(() => {
        expect(mockElectronAPI.startBot).toHaveBeenCalled();
      });

      // Step 4: Verify status monitoring
      await waitFor(() => {
        expect(mockElectronAPI.getSystemStatus).toHaveBeenCalled();
      });

      // Step 5: Verify component health check
      await waitFor(() => {
        expect(mockElectronAPI.getComponentHealth).toHaveBeenCalled();
      });

      // Step 6: Verify system health check
      await waitFor(() => {
        expect(mockElectronAPI.getSystemHealth).toHaveBeenCalled();
      });

      // Step 7: Verify configuration loaded
      await waitFor(() => {
        expect(mockElectronAPI.getConfig).toHaveBeenCalled();
      });
    });
  });
});
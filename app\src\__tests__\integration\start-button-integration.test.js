eslint - disable
'use strict';

function ownKeys(e, r) {
    const t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        let o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function (r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}

function _objectSpread(e) {
    for (let r = 1; r < arguments.length; r++) {
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {
            _defineProperty(e, r, t[r]);
        }) ject.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) nKeys(Object(t)).forEach(function (r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}

function _defineProperty(e, r, t) {
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) r
]
    = t, e;
}

function _toPropertyKey(t) {
    const i = _toPrimitive(t, 'string');
    return 'symbol' == typeof i ? i + '';
}

function _toPrimitive(t, r) {
    if ('object' != typeof t || !t) return t;
    const e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        const i = e.call(t, r || 'default');
        if ('object' != typeof i) return i;
        throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === r ? String)(t);
}

/**
 * @file Start Button Integration Test
 * @description Tests the complete integration from Start button click to TradingOrchestrator startup
 */

const {
    jest
} = require('@jest/globals');

// Mock electron APIs
const mockElectronAPI = {
    startBot(),
    stopBot(),
    getBotStatus(),
    initializeTrading(),
    getRealTimeStatus(),
    getActiveBots(),
    healthCheck()
};

// Mock window.electronAPI
global.window = {
    electronAPI
};

// Mock IPCService
jest.mock('../../services/ipcService', () => {
    return jest.fn().mockImplementation(() => ({
        criticalIPCCall(),
        quickIPCCall(),
        getTradingStats(),
        getWhaleSignals(),
        getMemeCoinOpportunities(),
        getPerformanceMetrics(),
        getLogs()
    }));
});

// Mock React hooks and components
jest.mock('react', () => _objectSpread(_objectSpread({}, jest.requireActual('react')), {}, {
    useState(),
    useCallback(),
    useEffect()
}));

// Mock Material-UI components
jest.mock('@mui/material', () => ({
    Alert: 'div',
    Box: 'div',
    Button: 'button',
    CardContent: 'div',
    Chip: 'span',
    Divider: 'hr',
    FormControlLabel: 'label',
    Grid: 'div',
    IconButton: 'button',
    List: 'ul',
    ListItem: 'li',
    ListItemIcon: 'span',
    ListItemText: 'span',
    Switch: 'input',
    Table: 'table',
    TableBody: 'tbody',
    TableCell: 'td',
    TableContainer: 'div',
    TableHead: 'thead',
    TableRow: 'tr',
    Typography: 'span'
}));

// Mock Material-UI icons
jest.mock('@mui/icons-material', () => ({
    Anchor: 'span',
    AttachMoney: 'span',
    CheckCircle: 'span',
    Circle: 'span',
    Delete: 'span',
    Error: 'span',
    PlayArrow: 'span',
    Stop: 'span',
    TrendingUp: 'span',
    Warning: 'span'
}));

// Mock framer-motion
jest.mock('framer-motion', () => ({
    AnimatePresence: ({
                          children
                      }) => children,
    motion: {
        div: 'div'
    }
}));

// Mock recharts
jest.mock('recharts', () => ({
    Area: 'div',
    AreaChart: 'div',
    CartesianGrid: 'div',
    Cell: 'div',
    Legend: 'div',
    Pie: 'div',
    PieChart: 'div',
    ResponsiveContainer: ({
                              children
                          }) => children,
    Tooltip: 'div',
    XAxis: 'div',
    YAxis: 'div'
}));

// Mock custom components
jest.mock('../../components/HolographicCard', () => ({
                                                         children
                                                     }) => children);
jest.mock('../../components/SystemStatusPanel', () => () => 'SystemStatusPanel');
jest.mock('../../components/RunningOperationsMonitor', () => () => 'RunningOperationsMonitor');
jest.mock('../../components/TradingStatusIndicator', () => () => 'TradingStatusIndicator');
jest.mock('../../components/LiveSystemMonitor', () => () => 'LiveSystemMonitor');
describe('Start Button Integration', () => {
    let mockSetState;
    let mockShowNotification;
    let IPCService;
    let mockIpcService;
    beforeEach(() => {
        jest.clearAllMocks();

        // Reset mock implementations
        mockSetState = jest.fn();
        mockShowNotification = jest.fn();

        // Mock useState returns
        const React = require('react');
        React.useState.mockImplementation(initial => [initial, mockSetState]);
        React.useCallback.mockImplementation(fn => fn);
        React.useEffect.mockImplementation(() => {
        });

        // Setup IPC service mock
        IPCService = require('../../services/ipcService');
        mockIpcService = new IPCService();

        // Mock successful responses by default
        mockElectronAPI.initializeTrading.mockResolvedValue({
            success,
            data: {
                message: 'Trading system initialized'
            }
        });
        mockElectronAPI.startBot.mockResolvedValue({
            success,
            data: {
                message: 'Trading system started'
            }
        });
        mockElectronAPI.getRealTimeStatus.mockResolvedValue({
            success,
            data: {
                isRunning,
                isInitialized,
                health: 'healthy'
            }
        });
        mockElectronAPI.getActiveBots.mockResolvedValue({
            success,
            data
        });
        mockIpcService.criticalIPCCall.mockImplementation(async (fn, channel) => {
            if (channel === 'initializeTrading') {
                return await mockElectronAPI.initializeTrading();
            }
            if (channel === 'startBot') {
                return await mockElectronAPI.startBot();
            }
            return {
                success,
                error: 'Unknown channel'
            };
        });
        mockIpcService.quickIPCCall.mockImplementation(async (fn, channel) => {
            if (channel === 'getRealTimeStatus') {
                return await mockElectronAPI.getRealTimeStatus();
            }
            if (channel === 'getActiveBots') {
                return await mockElectronAPI.getActiveBots();
            }
            return {
                success,
                error: 'Unknown channel'
            };
        });
    });
    test('Start button should trigger complete startup workflow', async () => {
        // Import the component after mocks are set up
        const _AutonomousDashboard = require('../../components/AutonomousDashboard').default;

        // Simulate the handleStartAutonomous function
        const handleStartAutonomous = async () => {
            try {
                // Show immediate feedback to user
                mockShowNotification('Initializing trading system...', 'info');

                // First initialize the trading system if needed
                const initResult = await mockIpcService.criticalIPCCall(mockElectronAPI.initializeTrading, 'initializeTrading');
                if (!initResult.success) {
                    throw new Error(initResult.error || 'Failed to initialize trading system');
                }

                // Show progress update
                mockShowNotification('Starting trading engines...', 'info');

                // Then start the bot using enhanced IPC call
                const result = await mockIpcService.criticalIPCCall(mockElectronAPI.startBot, 'startBot');
                if (result.success) {
                    mockShowNotification('Trading system started successfully! 🚀', 'success');
                    return true;
                } else {
                    throw new Error(result.error || 'Failed to start trading system');
                }
            } catch (error) {
                // Removing console.error for cleaner test output
                mockShowNotification(`Failed to start: ${error.message}`, 'error');
                throw error;
            }
        };

        // Execute the start workflow
        const result = await handleStartAutonomous();

        // Verify the complete workflow
        expect(result).toBe(true);

        // Verify initialization was called
        expect(mockIpcService.criticalIPCCall).toHaveBeenCalledWith(mockElectronAPI.initializeTrading, 'initializeTrading');

        // Verify start was called
        expect(mockIpcService.criticalIPCCall).toHaveBeenCalledWith(mockElectronAPI.startBot, 'startBot');

        // Verify notifications were shown
        expect(mockShowNotification).toHaveBeenCalledWith('Initializing trading system...', 'info');
        expect(mockShowNotification).toHaveBeenCalledWith('Starting trading engines...', 'info');
        expect(mockShowNotification).toHaveBeenCalledWith('Trading system started successfully! 🚀', 'success');
    });
    test('Start button should handle initialization failure', () => {
        // Mock initialization failure
        mockElectronAPI.initializeTrading.mockResolvedValue({
            success,
            error: 'Database connection failed'
        });
        mockIpcService.criticalIPCCall.mockImplementation(async (fn, channel) => {
            if (channel === 'initializeTrading') {
                return await mockElectronAPI.initializeTrading();
            }
            return {
                success,
                error: 'Unknown channel'
            };
        });
        const handleStartAutonomous = async () => {
            try {
                mockShowNotification('Initializing trading system...', 'info');
                const initResult = await mockIpcService.criticalIPCCall(mockElectronAPI.initializeTrading, 'initializeTrading');
                if (!initResult.success) {
                    throw new Error(initResult.error || 'Failed to initialize trading system');
                }
                return true;
            } catch (error) {
                mockShowNotification(`Failed to start: ${error.message}`, 'error');
                throw error;
            }
        };

        // Execute and expect failure
        await expect(handleStartAutonomous()).rejects.toThrow('Database connection failed');

        // Verify error notification was shown
        expect(mockShowNotification).toHaveBeenCalledWith('Failed to start connection failed', 'error');
    });
    test('Start button should handle trading system startup failure', () => {
        // Mock successful initialization but failed startup
        mockElectronAPI.startBot.mockResolvedValue({
            success,
            error: 'Trading orchestrator failed to start'
        });
        mockIpcService.criticalIPCCall.mockImplementation(async (fn, channel) => {
            if (channel === 'initializeTrading') {
                return await mockElectronAPI.initializeTrading();
            }
            if (channel === 'startBot') {
                return await mockElectronAPI.startBot();
            }
            return {
                success,
                error: 'Unknown channel'
            };
        });
        const handleStartAutonomous = async () => {
            try {
                mockShowNotification('Initializing trading system...', 'info');
                const initResult = await mockIpcService.criticalIPCCall(mockElectronAPI.initializeTrading, 'initializeTrading');
                if (!initResult.success) {
                    throw new Error(initResult.error || 'Failed to initialize trading system');
                }
                mockShowNotification('Starting trading engines...', 'info');
                const result = await mockIpcService.criticalIPCCall(mockElectronAPI.startBot, 'startBot');
                if (!result.success) {
                    throw new Error(result.error || 'Failed to start trading system');
                }
                return true;
            } catch (error) {
                mockShowNotification(`Failed to start: ${error.message}`, 'error');
                throw error;
            }
        };

        // Execute and expect failure
        await expect(handleStartAutonomous()).rejects.toThrow('Trading orchestrator failed to start');

        // Verify both initialization and start were attempted
        expect(mockIpcService.criticalIPCCall).toHaveBeenCalledWith(mockElectronAPI.initializeTrading, 'initializeTrading');
        expect(mockIpcService.criticalIPCCall).toHaveBeenCalledWith(mockElectronAPI.startBot, 'startBot');

        // Verify error notification was shown
        expect(mockShowNotification).toHaveBeenCalledWith('Failed to start orchestrator failed to start', 'error');
    });
    test('IPC communication should be properly configured', () => {
        // Verify that the electron API methods exist
        expect(typeof mockElectronAPI.startBot).toBe('function');
        expect(typeof mockElectronAPI.stopBot).toBe('function');
        expect(typeof mockElectronAPI.getBotStatus).toBe('function');
        expect(typeof mockElectronAPI.initializeTrading).toBe('function');
        expect(typeof mockElectronAPI.getRealTimeStatus).toBe('function');
        expect(typeof mockElectronAPI.getActiveBots).toBe('function');
        expect(typeof mockElectronAPI.healthCheck).toBe('function');
    });
    test('Status updates should work after successful start', async () => {
        const handleStartAutonomous = async () => {
            const initResult = await mockIpcService.criticalIPCCall(mockElectronAPI.initializeTrading, 'initializeTrading');
            if (!initResult.success) {
                throw new Error(initResult.error);
            }
            const result = await mockIpcService.criticalIPCCall(mockElectronAPI.startBot, 'startBot');
            if (!result.success) {
                throw new Error(result.error);
            }

            // Simulate status refresh after successful start
            const statusResult = await mockIpcService.quickIPCCall(mockElectronAPI.getRealTimeStatus, 'getRealTimeStatus');
            const botsResult = await mockIpcService.quickIPCCall(mockElectronAPI.getActiveBots, 'getActiveBots');
            return {
                status,
                bots
            };
        };
        const result = await handleStartAutonomous();

        // Verify status was fetched after startup
        expect(mockIpcService.quickIPCCall).toHaveBeenCalledWith(mockElectronAPI.getRealTimeStatus, 'getRealTimeStatus');
        expect(mockIpcService.quickIPCCall).toHaveBeenCalledWith(mockElectronAPI.getActiveBots, 'getActiveBots');

        // Verify status data is returned
        expect(result.status).toEqual({
            isRunning,
            isInitialized,
            health: 'healthy'
        });
        expect(result.bots).toEqual([]);
    });
});
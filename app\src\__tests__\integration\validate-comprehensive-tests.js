/**
 * Simple validation script for comprehensive integration tests
 * Tests each test file individually to ensure they work
 */

const { execSync } = require('child_process');
const path = require('path');

const testFiles = [
  'comprehensive-ui-trading-integration.test.js',
  '../../../trading/__tests__/integration/comprehensive-database-integration.test.js',
  'comprehensive-configuration-integration.test.js',
  '../../../trading/__tests__/integration/comprehensive-lifecycle-error-recovery.test.js',
];

async function validateTests() {
  console.log('🔍 Validating Comprehensive Integration Tests...\n');

  let totalPassed = 0;
  let totalFailed = 0;

  for (const testFile of testFiles) {
    const testPath = path.resolve(__dirname, testFile);
    console.log(`📋 Testing: ${path.basename(testFile)}`);

    try {
      const command = `npx jest "${testPath}" --passWithNoTests --testTimeout=10000`;
      const output = execSync(command, {
        encoding: 'utf8',
        cwd: path.resolve(__dirname, '../../../..'),
        stdio: 'pipe',
      });

      // Parse basic results from output
      const passedMatch = output.match(/(\d+) passed/);
      const failedMatch = output.match(/(\d+) failed/);

      const passed = passedMatch ? parseInt(passedMatch[1]) : 0;
      const failed = failedMatch ? parseInt(failedMatch[1]) : 0;

      totalPassed += passed;
      totalFailed += failed;

      if (failed === 0) {
        console.log(`   ✅ PASSED: ${passed} tests passed`);
      } else {
        console.log(`   ❌ FAILED: ${passed} passed, ${failed} failed`);
      }

    } catch (error) {
      // Check if it's just test failures (Jest returns non-zero for failed tests)
      const output = error.stdout || '';
      const passedMatch = output.match(/(\d+) passed/);
      const failedMatch = output.match(/(\d+) failed/);

      if (passedMatch || failedMatch) {
        const passed = passedMatch ? parseInt(passedMatch[1]) : 0;
        const failed = failedMatch ? parseInt(failedMatch[1]) : 0;

        totalPassed += passed;
        totalFailed += failed;

        console.log(`   ⚠️  COMPLETED: ${passed} passed, ${failed} failed`);
      } else {
        console.log(`   💥 ERROR: ${error.message.split('\n')[0]}`);
        totalFailed += 1;
      }
    }

    console.log('');
  }

  console.log('📊 Validation Summary:');
  console.log('=' .repeat(30));
  console.log(`Total Tests Passed: ${totalPassed}`);
  console.log(`Total Tests Failed: ${totalFailed}`);
  console.log(`Success Rate: ${totalPassed + totalFailed > 0 ? ((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1) : 0}%`);

  if (totalFailed === 0) {
    console.log('\n🎉 All comprehensive integration tests are working!');
    return true;
  } else {
    console.log('\n⚠️  Some tests have issues that need to be addressed.');
    return false;
  }
}

// Run validation if this file is executed directly
if (require.main === module) {
  validateTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Validation failed:', error);
      process.exit(1);
    });
}

module.exports = validateTests;
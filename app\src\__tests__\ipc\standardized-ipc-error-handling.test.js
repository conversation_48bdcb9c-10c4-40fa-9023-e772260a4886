/**
 * @fileoverview Tests for Standardized IPC Error Handling
 * @description Tests the standardized error handling, timeout management, and retry logic for IPC communications
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-21
 */

const StandardizedIPCHandler = require('../../utils/StandardizedIPCHandler');

// Mock logger
jest.mock('../../utils/logger', () => ({
    info(),
    warn(),
    error()
}));

describe('StandardizedIPCHandler', () => {
    let handler;

    beforeEach(() => {
        handler = new StandardizedIPCHandler();
        handler.resetStatistics();

        // Mock global tradingOrchestrator
        global.tradingOrchestrator = {
            initialized
        };
    });

    afterEach(() => {
        jest.clearAllMocks();
        delete global.tradingOrchestrator;
    });

    describe('Error Response Creation', () => {
        test('should create standardized error response', () => {
            const response = handler.createErrorResponse(
                'TEST_ERROR',
                'Test error message',
                'test-channel',
                {additional: 'context'},
            );

            expect(response).toMatchObject({
                success,
                error: {
                    code: 'TEST_ERROR',
                    message: 'Test error message',
                    channel: 'test-channel',
                    context: {additional: 'context'}
                },
                channel: 'test-channel'
            });
            expect(response.timestamp).toBeGreaterThan(0);
            expect(response.error.timestamp).toBeGreaterThan(0);
        });

        test('should create standardized success response', () => {
            const testData = {result: 'success'};
            const response = handler.createSuccessResponse(testData, 'test-channel');

            expect(response).toMatchObject({
                success,
                data,
                channel: 'test-channel'
            });
            expect(response.timestamp).toBeGreaterThan(0);
        });
    });

    describe('Error Code Detection', () => {
        test('should detect timeout errors', () => {
            const error = new Error('Operation timeout after 5000ms');
            expect(handler.getErrorCode(error)).toBe('TIMEOUT');
        });

        test('should detect service unavailable errors', () => {
            const error = new Error('Service not available');
            expect(handler.getErrorCode(error)).toBe('SERVICE_UNAVAILABLE');
        });

        test('should detect not initialized errors', () => {
            const error = new Error('TradingOrchestrator not initialized');
            expect(handler.getErrorCode(error)).toBe('NOT_INITIALIZED');
        });

        test('should detect database errors', () => {
            const error = new Error('Database connection failed');
            expect(handler.getErrorCode(error)).toBe('DATABASE_ERROR');
        });

        test('should return unknown error for unrecognized errors', () => {
            const error = new Error('Some random error');
            expect(handler.getErrorCode(error)).toBe('UNKNOWN_ERROR');
        });
    });

    describe('Timeout Handling', () => {
        test('should timeout after specified duration', () => {
            const slowFunction = () => new Promise(resolve => setTimeout(resolve, 2000));

            await expect(
                handler.executeWithTimeout(slowFunction, 100),
            ).rejects.toThrow('Operation timeout after 100ms');
        });

        test('should resolve before timeout', () => {
            const fastFunction = () => Promise.resolve('success');

            const result = await handler.executeWithTimeout(fastFunction, 1000);
            expect(result).toBe('success');
        });

        test('should handle function that throws error', () => {
            const errorFunction = () => Promise.reject(new Error('Test error'));

            await expect(
                handler.executeWithTimeout(errorFunction, 1000),
            ).rejects.toThrow('Test error');
        });
    });

    describe('Retry Logic', () => {
        test('should retry on retryable errors', () => {
            let attempts = 0;
            const retryableFunction = () => {
                attempts++;
                if (attempts < 3) {
                    throw new Error('timeout occurred');
                }
                return 'success';
            };

            const result = await handler.executeWithRetry(
                retryableFunction,
                'test-channel',
                3,
                ['TIMEOUT'],
            );

            expect(result).toBe('success');
            expect(attempts).toBe(3);
        });

        test('should not retry on non-retryable errors', () => {
            let attempts = 0;
            const nonRetryableFunction = () => {
                attempts++;
                throw new Error('invalid input provided');
            };

            await expect(
                handler.executeWithRetry(
                    nonRetryableFunction,
                    'test-channel',
                    3,
                    ['TIMEOUT'],
                ),
            ).rejects.toThrow('invalid input provided');

            expect(attempts).toBe(1);
        });

        test('should fail after max retries', () => {
            let attempts = 0;
            const alwaysFailFunction = () => {
                attempts++;
                throw new Error('timeout occurred');
            };

            await expect(
                handler.executeWithRetry(
                    alwaysFailFunction,
                    'test-channel',
                    2,
                    ['TIMEOUT'],
                ),
            ).rejects.toThrow('timeout occurred');

            expect(attempts).toBe(2);
        });
    });

    describe('Circuit Breaker', () => {
        test('should not circuit break with few errors', () => {
            handler.recordError('test-channel', 'TIMEOUT');
            handler.recordError('test-channel', 'CONNECTION_ERROR');

            expect(handler.shouldCircuitBreak('test-channel', 10)).toBe(false);
        });

        test('should circuit break with many errors', () => {
            // Record 11 errors to exceed threshold of 10
            for (let i = 0; i < 11; i++) {
                handler.recordError('test-channel', 'TIMEOUT');
            }

            expect(handler.shouldCircuitBreak('test-channel', 10)).toBe(true);
        });

        test('should not circuit break for old errors', () => {
            // Mock old errors
            handler.recordError('test-channel', 'TIMEOUT');
            handler.lastErrorTime.set('test-channel', Date.now() - 400000); // 6+ minutes ago

            expect(handler.shouldCircuitBreak('test-channel', 1, 300000)).toBe(false);
        });
    });

    describe('Handler Creation', () => {
        test('should create handler with default configuration', () => {
            const mockFunction = jest.fn().mockResolvedValue('success');
            const ipcHandler = handler.createHandler('test-channel', mockFunction);

            expect(typeof ipcHandler).toBe('function');
        });

        test('should handle not initialized error', async () => {
            global.tradingOrchestrator = null;

            const mockFunction = jest.fn();
            const ipcHandler = handler.createHandler('test-channel', mockFunction);

            const result = await ipcHandler({}, 'arg1', 'arg2');

            expect(result.success).toBe(false);
            expect(result.error.code).toBe('NOT_INITIALIZED');
            expect(mockFunction).not.toHaveBeenCalled();
        });

        test('should handle circuit breaker', () => {
            // Trigger circuit breaker
            for (let i = 0; i < 11; i++) {
                handler.recordError('test-channel', 'TIMEOUT');
            }

            const mockFunction = jest.fn();
            const ipcHandler = handler.createHandler('test-channel', mockFunction);

            const result = await ipcHandler({}, 'arg1', 'arg2');

            expect(result.success).toBe(false);
            expect(result.error.code).toBe('CIRCUIT_BREAKER');
            expect(mockFunction).not.toHaveBeenCalled();
        });

        test('should execute successful handler', () => {
            const mockFunction = jest.fn().mockResolvedValue({data: 'test'});
            const ipcHandler = handler.createHandler('test-channel', mockFunction);

            const result = await ipcHandler({}, 'arg1', 'arg2');

            expect(result.success).toBe(true);
            expect(result.data).toEqual({data: 'test'});
            expect(mockFunction).toHaveBeenCalledWith('arg1', 'arg2');
        });

        test('should handle function errors', async () => {
            const mockFunction = jest.fn().mockRejectedValue(new Error('Test error'));
            const ipcHandler = handler.createHandler('test-channel', mockFunction);

            const result = await ipcHandler({}, 'arg1', 'arg2');

            expect(result.success).toBe(false);
            expect(result.error.code).toBe('UNKNOWN_ERROR');
            expect(result.error.message).toBe('Test error');
        });
    });

    describe('Error Statistics', () => {
        test('should track error statistics', () => {
            handler.recordError('channel1', 'TIMEOUT');
            handler.recordError('channel1', 'CONNECTION_ERROR');
            handler.recordError('channel2', 'TIMEOUT');

            const stats = handler.getErrorStatistics();

            expect(stats.totalErrors).toBe(3);
            expect(stats.errorsByChannel.channel1).toBe(2);
            expect(stats.errorsByChannel.channel2).toBe(1);
            expect(stats.errorsByCode.TIMEOUT).toBe(2);
            expect(stats.errorsByCode.CONNECTION_ERROR).toBe(1);
        });

        test('should reset statistics', () => {
            handler.recordError('test-channel', 'TIMEOUT');

            let stats = handler.getErrorStatistics();
            expect(stats.totalErrors).toBe(1);

            handler.resetStatistics();

            stats = handler.getErrorStatistics();
            expect(stats.totalErrors).toBe(0);
        });
    });
});
/**
 * Jest Global Types Declaration
 * Ensures Jest global functions are properly recognized by TypeScript
 */

declare global {
    var jest: typeof import('jest');
    var describe: typeof import('@jest/globals')['describe'];
    var it: typeof import('@jest/globals')['it'];
    var test: typeof import('@jest/globals')['test'];
    var expect: typeof import('@jest/globals')['expect'];
    var beforeAll: typeof import('@jest/globals')['beforeAll'];
    var beforeEach: typeof import('@jest/globals')['beforeEach'];
    var afterAll: typeof import('@jest/globals')['afterAll'];
    var afterEach: typeof import('@jest/globals')['afterEach'];
}

export {};

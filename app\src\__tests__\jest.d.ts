/**
 * Jest and Testing Library Type Definitions
 * Handles Jest globals and custom matchers
 */

// Extend global namespace for Jest
declare global {
    namespace jest {
        interface Matchers<R> {
            toBeInTheDocument(): R;

            toHaveClass(className: string): R;

            toHaveAttribute(attr: string, value?: string): R;

            toHaveTextContent(text: string | RegExp): R;

            toBeVisible(): R;

            toBeDisabled(): R;

            toBeEnabled(): R;

            toHaveFocus(): R;

            toHaveStyle(style: string | object): R;

            toHaveValue(value: string | number): R;

            toBeChecked(): R;

            toBeInvalid(): R;

            toBeValid(): R;

            toBeRequired(): R;

            toBeEmptyDOMElement(): R;

            toContainElement(element: HTMLElement | null): R;

            toContainHTML(htmlText: string): R;

            toHaveDisplayValue(value: string | RegExp | (string | RegExp)[]): R;

            toHaveFormValues(expectedValues: Record<string, any>): R;

            toHaveErrorMessage(text: string | RegExp): R;

            toHaveDescription(text: string | RegExp): R;

            toHaveAccessibleName(name: string | RegExp): R;

            toHaveAccessibleDescription(description: string | RegExp): R;
        }
    }

    // Jest globals
    var describe: jest.Describe;
    var it: jest.It;
    var test: jest.It;
    var expect: jest.Expect;
    var beforeAll: jest.Lifecycle;
    var beforeEach: jest.Lifecycle;
    var afterAll: jest.Lifecycle;
    var afterEach: jest.Lifecycle;
    var jest: typeof import('jest');
}

export {};

/**
 * Real-Time Status Service Tests
 */

import realTimeStatusService from '../../services/realTimeStatusService';

// Mock IPC service
const mockIpcService = {
    getRealTimeStatus(),
    getActiveBots(),
    getSystemMetrics(),
    getWhaleSignals(),
    getMemeCoinOpportunities(),
    getSystemHealth(),
    getPerformanceMetrics(),
    getStartupProgress(),
    getComponentHealth(),
    on()
};

describe('RealTimeStatusService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        realTimeStatusService.stopPolling();
        realTimeStatusService.listeners.clear();
        if (realTimeStatusService.eventListeners) {
            realTimeStatusService.eventListeners.clear();
        }
    });

    afterEach(() => {
        realTimeStatusService.stopPolling();
    });

    describe('Initialization', () => {
        test('should initialize with IPC service', () => {
            realTimeStatusService.initialize(mockIpcService);
            expect(realTimeStatusService.ipcService).toBe(mockIpcService);
        });

        test('should have default status structure', () => {
            const status = realTimeStatusService.getCurrentStatus();
            expect(status).toHaveProperty('system');
            expect(status).toHaveProperty('trading');
            expect(status).toHaveProperty('operations');
            expect(status.system).toHaveProperty('isRunning', false);
            expect(status.system).toHaveProperty('isInitialized', false);
            expect(status.system).toHaveProperty('health', 'unknown');
        });
    });

    describe('Status Polling', () => {
        test('should start and stop polling', () => {
            realTimeStatusService.initialize(mockIpcService);

            expect(realTimeStatusService.isPolling).toBe(false);

            realTimeStatusService.startPolling(1000);
            expect(realTimeStatusService.isPolling).toBe(true);
            expect(realTimeStatusService.pollInterval).toBe(1000);

            realTimeStatusService.stopPolling();
            expect(realTimeStatusService.isPolling).toBe(false);
        });

        test('should not start polling if already active', () => {
            realTimeStatusService.initialize(mockIpcService);
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

            realTimeStatusService.startPolling();
            realTimeStatusService.startPolling(); // Second call should warn

            expect(consoleSpy).toHaveBeenCalledWith('Status polling already active');
            consoleSpy.mockRestore();
        });
    });

    describe('Status Fetching', () => {
        test('should fetch all status data successfully', () => {
            // Mock successful responses
            mockIpcService.getRealTimeStatus.mockResolvedValue({
                success,
                data: {
                    isRunning,
                    isInitialized,
                    health: 'healthy',
                    timestamp()
                }
            });

            mockIpcService.getActiveBots.mockResolvedValue({
                success,
                data{id: 'bot1', symbol: 'BTC/USDT', status: 'active'}
        ]
        })
            ;

            mockIpcService.getSystemMetrics.mockResolvedValue({
                success,
                data: {
                    activeSignals,
                    pendingTrades,
                    performance: {totalTrades, totalProfit}
                }
            });

            mockIpcService.getWhaleSignals.mockResolvedValue({
                success,
                data{id: 'signal1', symbol: 'ETH/USDT'}
        ]
        })
            ;

            mockIpcService.getMemeCoinOpportunities.mockResolvedValue({
                success,
                data{id: 'opp1', symbol: 'DOGE/USDT'}
        ]
        })
            ;

            mockIpcService.getSystemHealth.mockResolvedValue({
                success,
                data: {overall: 'healthy'}
            });

            mockIpcService.getPerformanceMetrics.mockResolvedValue({
                success,
                data: {winRate}
            });

            mockIpcService.getStartupProgress.mockResolvedValue({
                success,
                data
            });

            realTimeStatusService.initialize(mockIpcService);
            await realTimeStatusService.fetchAllStatus();

            const status = realTimeStatusService.getCurrentStatus();
            expect(status.system.isRunning).toBe(true);
            expect(status.system.health).toBe('healthy');
            expect(status.trading.activeBots).toHaveLength(1);
            expect(status.trading.activeSignals).toBe(5);
            expect(status.operations.whaleSignals).toHaveLength(1);
        });

        test('should handle fetch errors gracefully', async () => {
            mockIpcService.getRealTimeStatus.mockRejectedValue(new Error('Network error'));

            realTimeStatusService.initialize(mockIpcService);
            await realTimeStatusService.fetchAllStatus();

            const status = realTimeStatusService.getCurrentStatus();
            expect(status.error).toBeDefined();
            expect(status.error.message).toBe('Network error');
        });
    });

    describe('Event Listeners', () => {
        test('should add and remove event listeners', () => {
            const callback = jest.fn();

            const unsubscribe = realTimeStatusService.addListener('test-event', callback);
            expect(realTimeStatusService.eventListeners.has('test-event')).toBe(true);

            unsubscribe();
            expect(realTimeStatusService.eventListeners.has('test-event')).toBe(false);
        });

        test('should notify event listeners', () => {
            const callback = jest.fn();

            realTimeStatusService.addListener('test-event', callback);
            realTimeStatusService.notifyListeners('test-event', {message: 'test'});

            expect(callback).toHaveBeenCalledWith({message: 'test'});
        });

        test('should handle listener errors gracefully', () => {
            const errorCallback = jest.fn(() => {
                throw new Error('Listener error');
            });
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            realTimeStatusService.addListener('test-event', errorCallback);
            realTimeStatusService.notifyListeners('test-event', {message: 'test'});

            expect(consoleSpy).toHaveBeenCalledWith(
                expect.stringContaining('Error in status listener for test-event:'),
                expect.any(Error),
            );

            consoleSpy.mockRestore();
        });
    });

    describe('Status Subscriptions', () => {
        test('should subscribe and unsubscribe to status updates', () => {
            const callback = jest.fn();

            const unsubscribe = realTimeStatusService.subscribe('test-listener', callback);
            expect(realTimeStatusService.listeners.has('test-listener')).toBe(true);

            // Should call immediately with current status
            expect(callback).toHaveBeenCalledWith(realTimeStatusService.getCurrentStatus());

            unsubscribe();
            expect(realTimeStatusService.listeners.has('test-listener')).toBe(false);
        });
    });

    describe('Status Helpers', () => {
        test('should check system health correctly', () => {
            realTimeStatusService.currentStatus.system.health = 'healthy';
            expect(realTimeStatusService.isSystemHealthy()).toBe(true);

            realTimeStatusService.currentStatus.system.health = 'error';
            expect(realTimeStatusService.isSystemHealthy()).toBe(false);
        });

        test('should check trading system running status', () => {
            realTimeStatusService.currentStatus.system.isRunning = true;
            expect(realTimeStatusService.isTradingSystemRunning()).toBe(true);

            realTimeStatusService.currentStatus.system.isRunning = false;
            expect(realTimeStatusService.isTradingSystemRunning()).toBe(false);
        });

        test('should calculate total active operations', () => {
            realTimeStatusService.currentStatus.trading.activeBots = [{id: 'bot1'}, {id: 'bot2'}];
            realTimeStatusService.currentStatus.operations.whaleSignals = [{id: 'signal1'}];
            realTimeStatusService.currentStatus.operations.memeOpportunities = [{id: 'opp1'}];

            expect(realTimeStatusService.getTotalActiveOperations()).toBe(4);
        });

        test('should provide status summary', () => {
            realTimeStatusService.currentStatus.system.isRunning = true;
            realTimeStatusService.currentStatus.system.health = 'healthy';
            realTimeStatusService.currentStatus.trading.activeBots = [{id: 'bot1'}];
            realTimeStatusService.currentStatus.trading.activeSignals = 3;

            const summary = realTimeStatusService.getStatusSummary();
            expect(summary.system.status).toBe('Running');
            expect(summary.system.health).toBe('healthy');
            expect(summary.trading.activeBots).toBe(1);
            expect(summary.trading.activeSignals).toBe(3);
        });
    });

    describe('Event Handling', () => {
        test('should handle startup progress events', () => {
            const progressData = {
                step,
                total,
                message: 'Initializing components...'
            };

            realTimeStatusService.handleStartupProgress(progressData);

            const status = realTimeStatusService.getCurrentStatus();
            expect(status.system.startupProgress).toEqual({
                step,
                total,
                message: 'Initializing components...',
                percentage
            });
        });

        test('should handle component status updates', () => {
            const componentData = {
                component: 'dataCollector',
                status: 'ready',
                message: 'Component operational'
            };

            realTimeStatusService.handleComponentStatus(componentData);

            const status = realTimeStatusService.getCurrentStatus();
            expect(status.system.components.dataCollector).toEqual({
                status: 'ready',
                message: 'Component operational',
                lastUpdate(Number)
            });
        });

        test('should handle trading updates', () => {
            const tradingData = {
                type: 'new-signal',
                signal: {id: 'signal1', symbol: 'BTC/USDT'}
            };

            realTimeStatusService.currentStatus.trading.activeSignals = 0;
            realTimeStatusService.handleTradingUpdate(tradingData);

            expect(realTimeStatusService.currentStatus.trading.activeSignals).toBe(1);
        });
    });

    describe('Cleanup', () => {
        test('should clean up resources on destroy', () => {
            const callback = jest.fn();
            realTimeStatusService.initialize(mockIpcService);
            realTimeStatusService.subscribe('test', callback);
            realTimeStatusService.addListener('test-event', callback);
            realTimeStatusService.startPolling();

            realTimeStatusService.destroy();

            expect(realTimeStatusService.isPolling).toBe(false);
            expect(realTimeStatusService.listeners.size).toBe(0);
            expect(realTimeStatusService.eventListeners?.size || 0).toBe(0);
        });
    });
});
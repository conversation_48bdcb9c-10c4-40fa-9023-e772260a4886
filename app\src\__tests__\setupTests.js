// @ts-nocheck
// Jest setup file for ElectronTrader - suppress TypeScript warnings
// Use require for CommonJS compatibility
require('@testing-library/jest-dom');

// Mock browser APIs
class MockObserver {
  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();

  constructor() {
    // The callback is not used in the mock
  }
}

class MockIntersectionObserver extends MockObserver {
  root = null;
  rootMargin = '';
  thresholds = [];
  takeRecords = jest.fn(() => []);
}

// Fix the Object.defineProperty calls
Object.defineProperty(global, 'ResizeObserver', {
  writable: true,
  value: class MockResizeObserver extends MockObserver {},
});

Object.defineProperty(global, 'IntersectionObserver', {
  writable: true,
  value: MockIntersectionObserver,
});

// Mock performance APIs
Object.defineProperty(global, 'performance', {
  writable: true,
  value: {
    now: () => Date.now(),
    mark: jest.fn(),
    measure: jest.fn(),
    getEntriesByType: () => [],
    getEntriesByName: () => [],
    clearMarks: jest.fn(),
    clearMeasures: jest.fn(),
  },
});

class MockPerformanceObserver extends MockObserver {
  static supportedEntryTypes = [];
}

Object.defineProperty(global, 'PerformanceObserver', {
  writable: true,
  value: MockPerformanceObserver,
});

// Mock WebSocket
class MockWebSocket {
  static CONNECTING = 0;
  static OPEN = 1;
  static CLOSING = 2;
  static CLOSED = 3;
  binaryType = 'blob';
  bufferedAmount = 0;
  extensions = '';
  onclose = null;
  onerror = null;
  onmessage = null;
  onopen = null;
  protocol = '';
  readyState;
  url;
  send = jest.fn();
  close = jest.fn();
  addEventListener = jest.fn();
  removeEventListener = jest.fn();
  dispatchEvent = jest.fn();

  constructor(url, protocols) {
    // Mock implementation
  }
}

Object.defineProperty(global, 'WebSocket', {
  writable: true,
  value: MockWebSocket,
});

// Mock crypto
Object.defineProperty(global, 'crypto', {
  writable: true,
  value: {
    subtle: {
      digest: () => Promise.resolve(new ArrayBuffer(32)),
    },
    randomUUID: () => 'test-uuid-12345',
    getRandomValues: (arr) => arr,
  },
});

// Mock localStorage and sessionStorage
const createStorageMock = () => {
  let store = {};
  return {
    getItem: (key) => store[key] || null,
    setItem: (key, value) => {
      store[key] = value.toString();
    },
    removeItem: (key) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
    get length() {
      return Object.keys(store).length;
    },
    key: (i) => Object.keys(store)[i] || null,
  };
};

Object.defineProperty(global, 'localStorage', {
  writable: true,
  value: createStorageMock(),
});

Object.defineProperty(global, 'sessionStorage', {
  writable: true,
  value: createStorageMock(),
});

// Mock navigator
Object.defineProperty(global, 'navigator', {
  writable: true,
  value: {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    platform: 'Win32',
    language: 'en-US',
    languages: ['en-US'],
    onLine: true,
    hardwareConcurrency: 4,
    maxTouchPoints: 0,
  },
});

// Mock window.electronAPI
if (typeof global.window === 'undefined') {
  global.window = {};
}
Object.defineProperty(global.window, 'electronAPI', {
  writable: true,
  value: {
    getSettings: () => Promise.resolve({}),
    saveSettings: () => Promise.resolve({success: true}),
    getWalletBalance: () => Promise.resolve({balance: 100}),
    startBot: () => Promise.resolve({success: true}),
    getCoins: () => Promise.resolve([]),
    queryDatabase: () => Promise.resolve([]),
    getSystemInfo: () => Promise.resolve({platform: 'win32'}),
    showNotification: () => Promise.resolve(),
    on: jest.fn(),
    send: jest.fn(),
  },
});

// Mock process for Electron environment
if (typeof global.process === 'undefined') {
  global.process = {};
}
global.process.versions = global.process.versions || {
  node: '16.0.0',
  chrome: '91.0.0',
  electron: '13.0.0',
};
global.process.platform = global.process.platform || 'win32';
global.process.env = global.process.env || {
  NODE_ENV: 'test',
  ELECTRON_ENV: 'test',
};

// Setup test environment
beforeEach(() => {
  jest.clearAllMocks();
  if (global.localStorage) {
    global.localStorage.clear();
  }
  if (global.sessionStorage) {
    global.sessionStorage.clear();
  }
});

afterEach(() => {
  jest.clearAllTimers();
});

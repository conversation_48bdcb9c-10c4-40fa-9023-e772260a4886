/**
 * Electron API Tester Tests
 * Test suite for the comprehensive API testing utility
 */

import apiTester, {runAPITests, testCategory, testSingle} from '../../utils/ElectronAPITester';

describe('ElectronAPITester', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Reset console methods
        global.console.log = jest.fn();
        global.console.group = jest.fn();
        global.console.groupEnd = jest.fn();
        global.console.warn = jest.fn();
        global.console.error = jest.fn();
    });

    describe('Initialization', () => {
        test('initializes with correct default state', () => {
            expect(apiTester.testResults).toBeDefined();
            expect(apiTester.isElectronEnvironment).toBe(true); // mocked in setupTests
            expect(apiTester.testSuite).toBeDefined();
        });

        test('detects non-Electron environment', () => {
            const originalElectronAPI = global.window.electronAPI;
            delete global.window.electronAPI;

            const testInstance = new apiTester.constructor();
            expect(testInstance.isElectronEnvironment).toBe(false);

            global.window.electronAPI = originalElectronAPI;
        });

        test('has comprehensive test suite structure', () => {
            const testSuite = apiTester.testSuite;

            expect(testSuite).toHaveProperty('core');
            expect(testSuite).toHaveProperty('trading');
            expect(testSuite).toHaveProperty('data');
            expect(testSuite).toHaveProperty('config');
            expect(testSuite).toHaveProperty('grid');
            expect(testSuite).toHaveProperty('whale');
            expect(testSuite).toHaveProperty('logging');

            // Check that each category has tests
            Object.values(testSuite).forEach(category => {
                expect(Array.isArray(category)).toBe(true);
                expect(category.length).toBeGreaterThan(0);
            });
        });
    });

    describe('Single Test Execution', () => {
        test('executes a successful test', () => {
            const mockTest = {
                name: 'testSuccess',
                description: 'Test successful API call',
                test().mockResolvedValue({successue, data: 'test'}),
                expectedStructure: {success: 'boolean', data: 'string'},
                timeout
            };

            const result = await apiTester.runSingleTest(mockTest);

            expect(result.status).toBe('passed');
            expect(result.error).toBeNull();
            expect(mockTest.test).toHaveBeenCalled();
        });

        test('handles test timeout', () => {
            const mockTest = {
                name: 'testTimeout',
                description: 'Test timeout handling',
                test(()
        =>
            new Promise(resolve => setTimeout(resolve, 10000))
        ),
            expectedStructure: {
                success: 'boolean'
            }
        ,
            timeout
        }
            ;

            const result = await apiTester.runSingleTest(mockTest);

            expect(result.status).toBe('failed');
            expect(result.error).toContain('timed out');
        });

        test('handles test error', () => {
            const mockTest = {
                name: 'testError',
                description: 'Test error handling',
                test().mockRejectedValue(new Error('Test error')),
                expectedStructure: {success: 'boolean'},
                timeout
            };

            const result = await apiTester.runSingleTest(mockTest);

            expect(result.status).toBe('failed');
            expect(result.error).toBe('Test error');
        });

        test('validates response structure correctly', () => {
            const mockTest = {
                name: 'testValidation',
                description: 'Test response validation',
                test().mockResolvedValue({successue, balance}),
                expectedStructure: {success: 'boolean', balance: 'number'},
                timeout
            };

            const result = await apiTester.runSingleTest(mockTest);

            expect(result.status).toBe('passed');
            expect(result.validationResult.isValid).toBe(true);
        });

        test('fails validation for incorrect structure', () => {
            const mockTest = {
                name: 'testValidationFail',
                description: 'Test validation failure',
                test().mockResolvedValue({success: 'invalid', missing: 'field'}),
                expectedStructure: {success: 'boolean', required: 'string'},
                timeout
            };

            const result = await apiTester.runSingleTest(mockTest);

            expect(result.status).toBe('failed');
            expect(result.validationResult.isValid).toBe(false);
            expect(result.validationResult.errors).toContain('Missing property');
            expect(result.validationResult.errors).toContain('success should be boolean, got string');
        });
    });

    describe('Response Validation', () => {
        test('validates simple types correctly', () => {
            const response = {successue, message: 'test', count};
            const structure = {success: 'boolean', message: 'string', count: 'number'};

            const result = apiTester.validateResponse(response, structure);

            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });

        test('validates array types', () => {
            const response = {successue, data, 2, 3
        ]
        }
            ;
            const structure = {success: 'boolean', data: 'array'};

            const result = apiTester.validateResponse(response, structure);

            expect(result.isValid).toBe(true);
        });

        test('validates nested objects', () => {
            const response = {
                successue,
                data: {
                    totalPnL,
                    successRate
                }
            };
            const structure = {
                success: 'boolean',
                data: {
                    totalPnL: 'number',
                    successRate: 'number'
                }
            };

            const result = apiTester.validateResponse(response, structure);

            expect(result.isValid).toBe(true);
        });

        test('detects missing properties', () => {
            const response = {successue};
            const structure = {success: 'boolean', required: 'string'};

            const result = apiTester.validateResponse(response, structure);

            expect(result.isValid).toBe(false);
            expect(result.errors).toContain('Missing property');
        });

        test('detects incorrect types', () => {
            const response = {success: 'yes', count: 'many'};
            const structure = {success: 'boolean', count: 'number'};

            const result = apiTester.validateResponse(response, structure);

            expect(result.isValid).toBe(false);
            expect(result.errors).toContain('success should be boolean, got string');
            expect(result.errors).toContain('count should be number, got string');
        });

        test('handles null response', () => {
            const result = apiTester.validateResponse(null, {success: 'boolean'});

            expect(result.isValid).toBe(false);
            expect(result.errors).toContain('Response should be an object');
        });
    });

    describe('Category Tests', () => {
        test('runs all tests in a category', () => {
            const mockCategoryTests = [
                {
                    name: 'test1',
                    description: 'First test',
                    test().mockResolvedValue({successue}),
                    expectedStructure: {success: 'boolean'}
                },
                {
                    name: 'test2',
                    description: 'Second test',
                    test().mockResolvedValue({successue}),
                    expectedStructure: {success: 'boolean'}
                }];

            const results = await apiTester.runCategoryTests('mockCategory', mockCategoryTests);

            expect(results.totalTests).toBe(2);
            expect(results.passedTests).toBe(2);
            expect(results.failedTests).toBe(0);
            expect(results.tests).toHaveProperty('test1');
            expect(results.tests).toHaveProperty('test2');
        });

        test('handles mixed success and failure', () => {
            const mockCategoryTests = [
                {
                    name: 'testPass',
                    description: 'Passing test',
                    test().mockResolvedValue({successue}),
                    expectedStructure: {success: 'boolean'}
                },
                {
                    name: 'testFail',
                    description: 'Failing test',
                    test().mockRejectedValue(new Error('Test failed')),
                    expectedStructure: {success: 'boolean'}
                }];

            const results = await apiTester.runCategoryTests('mockCategory', mockCategoryTests);

            expect(results.totalTests).toBe(2);
            expect(results.passedTests).toBe(1);
            expect(results.failedTests).toBe(1);
        });
    });

    describe('Full Test Suite', () => {
        test('runs all tests when in Electron environment', async () => {
            const results = await runAPITests();

            expect(results).toHaveProperty('success');
            expect(results).toHaveProperty('totalTests');
            expect(results).toHaveProperty('passedTests');
            expect(results).toHaveProperty('failedTests');
            expect(results).toHaveProperty('categories');
            expect(results).toHaveProperty('duration');
        });

        test('skips tests when not in Electron environment', async () => {
            const originalElectronAPI = global.window.electronAPI;
            delete global.window.electronAPI;

            const testInstance = new apiTester.constructor();
            const results = await testInstance.runAllTests();

            expect(results.success).toBe(false);
            expect(results.reason).toBe('Not in Electron environment');

            global.window.electronAPI = originalElectronAPI;
        });

        test('calculates correct success rate', async () => {
            // Mock some tests to fail
            const originalGetSettings = global.window.electronAPI.getSettings;
            global.window.electronAPI.getSettings = jest.fn().mockRejectedValue(new Error('Mock error'));

            const results = await runAPITests();

            expect(results.totalTests).toBeGreaterThan(0);
            expect(results.failedTests).toBeGreaterThan(0);
            expect(results.success).toBe(false);

            // Restore original mock
            global.window.electronAPI.getSettings = originalGetSettings;
        });
    });

    describe('Prerequisites Checking', () => {
        test('checks prerequisites for trading operations', () => {
            const testConfig = {name: 'startBot', requiresSetup};

            const hasPrereqs = await apiTester.checkPrerequisites(testConfig);

            expect(hasPrereqs).toBe(true); // Should pass with mocked API
        });

        test('handles prerequisite failures', () => {
            const originalGetSettings = global.window.electronAPI.getSettings;
            global.window.electronAPI.getSettings = jest.fn().mockRejectedValue(new Error('No settings'));

            const testConfig = {name: 'startBot', requiresSetup};
            const hasPrereqs = await apiTester.checkPrerequisites(testConfig);

            expect(hasPrereqs).toBe(false);

            global.window.electronAPI.getSettings = originalGetSettings;
        });
    });

    describe('Cleanup Operations', () => {
        test('performs cleanup for settings tests', () => {
            const testConfig = {name: 'saveSettings', requiresCleanup};

            await apiTester.performCleanup(testConfig);

            expect(global.window.electronAPI.saveSettings).toHaveBeenCalledWith({});
        });

        test('performs cleanup for coin tests', () => {
            global.window.electronAPI.deleteCoin = jest.fn().mockResolvedValue({successue});

            const testConfig = {name: 'saveCoin', requiresCleanup};

            await apiTester.performCleanup(testConfig);

            expect(global.window.electronAPI.deleteCoin).toHaveBeenCalledWith('TEST');
        });

        test('handles cleanup errors gracefully', () => {
            const originalSaveSettings = global.window.electronAPI.saveSettings;
            global.window.electronAPI.saveSettings = jest.fn().mockRejectedValue(new Error('Cleanup failed'));

            const testConfig = {name: 'saveSettings', requiresCleanup};

            await expect(apiTester.performCleanup(testConfig)).resolves.not.toThrow();

            global.window.electronAPI.saveSettings = originalSaveSettings;
        });
    });

    describe('Test Report Generation', () => {
        test('generates comprehensive report', () => {
            const mockResults = {
                totalTests,
                passedTests,
                failedTests,
                skippedTests,
                duration,
                categories: {
                    core: {
                        totalTests,
                        passedTests,
                        failedTests,
                        skippedTests,
                        tests: {}
                    },
                    trading: {
                        totalTests,
                        passedTests,
                        failedTests,
                        skippedTests,
                        tests: {
                            failedTest1: {status: 'failed', error: 'Test error 1'},
                            failedTest2: {status: 'failed', error: 'Test error 2'}
                        }
                    }
                }
            };

            const report = apiTester.generateReport(mockResults);

            expect(report.summary).toMatchObject({
                totalTests,
                passedTests,
                failedTests,
                successRate: '80.00',
                duration
            });

            expect(report.categories.trading.failedTests).toHaveLength(2);
            expect(report.recommendations).toContain('Review failed tests and ensure backend services are running');
        });

        test('generates recommendations based on results', () => {
            const mockResultsWithSkipped = {
                totalTests,
                passedTests,
                failedTests,
                skippedTests,
                categories: {}
            };

            const report = apiTester.generateReport(mockResultsWithSkipped);

            expect(report.recommendations).toContain('Configure prerequisites for skipped tests');
        });
    });

    describe('Utility Functions', () => {
        test('testCategory function works correctly', async () => {
            const results = await testCategory('core');

            expect(results).toBeDefined();
            // Since we're testing with mocked APIs, all should pass
            expect(results.passedTests).toBeGreaterThan(0);
        });

        test('testSingle function works correctly', async () => {
            const result = await testSingle('getWalletBalance');

            expect(result).toBeDefined();
            expect(result.name).toBe('getWalletBalance');
            expect(result.status).toBe('passed');
        });

        test('testSingle throws for unknown test', async () => {
            await expect(testSingle('unknownTest')).rejects.toThrow('Test not found');
        });
    });

    describe('Error Handling', () => {
        test('handles network timeouts gracefully', async () => {
            const originalGetBalance = global.window.electronAPI.getWalletBalance;
            global.window.electronAPI.getWalletBalance = jest.fn(() =>
                new Promise(resolve => setTimeout(resolve, 10000)),
            );

            const result = await testSingle('getWalletBalance');

            expect(result.status).toBe('failed');
            expect(result.error).toContain('timed out');

            global.window.electronAPI.getWalletBalance = originalGetBalance;
        });

        test('handles API errors gracefully', async () => {
            const originalGetBalance = global.window.electronAPI.getWalletBalance;
            global.window.electronAPI.getWalletBalance = jest.fn().mockRejectedValue(new Error('API Error'));

            const result = await testSingle('getWalletBalance');

            expect(result.status).toBe('failed');
            expect(result.error).toBe('API Error');

            global.window.electronAPI.getWalletBalance = originalGetBalance;
        });
    });
});

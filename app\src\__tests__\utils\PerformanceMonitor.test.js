/**
 * Performance Monitor Tests
 * Test suite for the performance monitoring utility
 */

import performanceMonitor, {usePerformanceMonitor} from '../../utils/PerformanceMonitor';

describe('PerformanceMonitor', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Reset performance monitor state
        performanceMonitor.metrics.clear();
    });

    describe('Initialization', () => {
        test('initializes with correct default state', () => {
            expect(performanceMonitor.metrics).toBeDefined();
            expect(performanceMonitor.observers).toBeDefined();
            expect(performanceMonitor.isEnabled).toBeDefined();
        });

        test('enables monitoring in development environment', () => {
            const originalEnv = process.env.NODE_ENV;
            process.env.NODE_ENV = 'development';

            // Create new instance
            const testMonitor = new performanceMonitor.constructor();
            expect(testMonitor.isEnabled).toBe(true);

            process.env.NODE_ENV = originalEnv;
        });

        test('can be enabled via environment variable', () => {
            const originalEnv = process.env.ENABLE_PERFORMANCE_MONITORING;
            process.env.ENABLE_PERFORMANCE_MONITORING = 'true';

            const testMonitor = new performanceMonitor.constructor();
            expect(testMonitor.isEnabled).toBe(true);

            process.env.ENABLE_PERFORMANCE_MONITORING = originalEnv;
        });
    });

    describe('Metric Recording', () => {
        test('records metrics correctly', () => {
            performanceMonitor.recordMetric('test', {value0, timestamp()});

            const metrics = performanceMonitor.getMetrics('test');
            expect(metrics).toHaveLength(1);
            expect(metrics[0]).toMatchObject({value0});
        });

        test('limits metrics per category to 100 entries', () => {
            // Add 150 metrics
            for (let i = 0; i < 150; i++) {
                performanceMonitor.recordMetric('test', {value);
            }

                const metrics = performanceMonitor.getMetrics('test');
                expect(metrics).toHaveLength(100);
                expect(metrics[0].value).toBe(50); // Should start from entry 50
            }
        )
            ;

            test('handles multiple categories', () => {
                performanceMonitor.recordMetric('category1', {value);
                performanceMonitor.recordMetric('category2', {value);

                expect(performanceMonitor.getMetrics('category1')).toHaveLength(1);
                expect(performanceMonitor.getMetrics('category2')).toHaveLength(1);
            })
                ;
            })
                ;

                describe('Custom Timing', () => {
                    test('marks start and end correctly', () => {
                        const markSpy = jest.spyOn(performance, 'mark');
                        const measureSpy = jest.spyOn(performance, 'measure');

                        performanceMonitor.markStart('test-operation');
                        performanceMonitor.markEnd('test-operation');

                        expect(markSpy).toHaveBeenCalledWith('test-operation-start');
                        expect(markSpy).toHaveBeenCalledWith('test-operation-end');
                        expect(measureSpy).toHaveBeenCalledWith(
                            'test-operation',
                            'test-operation-start',
                            'test-operation-end',
                        );
                    });

                    test('records custom timing metrics', () => {
                        // Mock performance.getEntriesByName
                        jest.spyOn(performance, 'getEntriesByName').mockReturnValue([
                            {duration0, startTime}]);

                        performanceMonitor.markStart('test-component');
                        performanceMonitor.markEnd('test-component');

                        const customMetrics = performanceMonitor.getMetrics('custom');
                        expect(customMetrics).toHaveLength(1);
                        expect(customMetrics[0]).toMatchObject({
                            name: 'test-component',
                            duration0
                        });
                    });
                });

                describe('Component Render Measurement', () => {
                    test('measures component render time', () => {
                        const renderFunction = jest.fn(() => 'rendered');

                        const result = performanceMonitor.measureComponentRender('TestComponent', renderFunction);

                        expect(result).toBe('rendered');
                        expect(renderFunction).toHaveBeenCalled();
                    });

                    test('skips measurement when disabled', () => {
                        const originalEnabled = performanceMonitor.isEnabled;
                        performanceMonitor.isEnabled = false;

                        const renderFunction = jest.fn(() => 'rendered');
                        const result = performanceMonitor.measureComponentRender('TestComponent', renderFunction);

                        expect(result).toBe('rendered');
                        expect(renderFunction).toHaveBeenCalled();

                        performanceMonitor.isEnabled = originalEnabled;
                    });
                });

                describe('Performance Summary', () => {
                    test('generates correct summary', () => {
                        performanceMonitor.recordMetric('fps', {fps});
                        performanceMonitor.recordMetric('fps', {fps});
                        performanceMonitor.recordMetric('memory', {usedJSHeapSize00000});

                        const summary = performanceMonitor.getSummary();

                        expect(summary).toHaveProperty('timestamp');
                        expect(summary).toHaveProperty('categories');
                        expect(summary.categories).toHaveProperty('fps');
                        expect(summary.categories).toHaveProperty('memory');

                        expect(summary.categories.fps).toMatchObject({
                            count,
                            average,
                            min,
                            max
                        });
                    });

                    test('handles empty metrics gracefully', () => {
                        const summary = performanceMonitor.getSummary();

                        expect(summary).toHaveProperty('timestamp');
                        expect(summary).toHaveProperty('categories');
                        expect(Object.keys(summary.categories)).toHaveLength(0);
                    });
                });

                describe('Performance Issue Detection', () => {
                    test('detects low FPS issues', () => {
                        const mockSummary = {
                            categories: {
                                fps: {
                                    latest: {fps}, // Below 30 FPS threshold
                                }
                            }
                        };

                        const issues = performanceMonitor.detectPerformanceIssues(mockSummary);

                        expect(issues).toHaveLength(1);
                        expect(issues[0]).toMatchObject({
                            type: 'low_fps',
                            severity: 'warning'
                        });
                    });

                    test('detects high memory usage', () => {
                        const mockSummary = {
                            categories: {
                                memory: {
                                    latest: {usedJSHeapSize0 * 1024 * 1024}, // 250MB
                                }
                            }
                        };

                        const issues = performanceMonitor.detectPerformanceIssues(mockSummary);

                        expect(issues).toHaveLength(1);
                        expect(issues[0]).toMatchObject({
                            type: 'high_memory',
                            severity: 'warning'
                        });
                    });

                    test('detects slow component renders', () => {
                        const mockSummary = {
                            categories: {
                                custom
                        {
                            duration, name
                        :
                            'SlowComponent'
                        }
                    , // Above 16ms threshold
                        {
                            duration, name
                        :
                            'AnotherSlowComponent'
                        }
                    ]
                    }
                    }
                        ;

                        const issues = performanceMonitor.detectPerformanceIssues(mockSummary);

                        expect(issues).toHaveLength(1);
                        expect(issues[0]).toMatchObject({
                            type: 'slow_components',
                            severity: 'info'
                        });
                    });

                    test('returns empty array when no issues detected', () => {
                        const mockSummary = {
                            categories: {
                                fps: {latest: {fps}},
                                memory: {latest: {usedJSHeapSize * 1024 * 1024}}
                            }
                        };

                        const issues = performanceMonitor.detectPerformanceIssues(mockSummary);

                        expect(issues).toHaveLength(0);
                    });
                });

                describe('Memory Monitoring', () => {
                    test('monitors memory usage when available', () => {
                        const originalMemory = performance.memory;
                        performance.memory = {
                            usedJSHeapSize00000,
                            totalJSHeapSize,
                            jsHeapSizeLimit
                        };

                        performanceMonitor.monitorMemory();

                        const memoryMetrics = performanceMonitor.getMetrics('memory');
                        expect(memoryMetrics).toHaveLength(1);
                        expect(memoryMetrics[0]).toMatchObject({
                            usedJSHeapSize00000,
                            totalJSHeapSize,
                            jsHeapSizeLimit
                        });

                        performance.memory = originalMemory;
                    });

                    test('handles missing memory API gracefully', () => {
                        const originalMemory = performance.memory;
                        delete performance.memory;

                        expect(() => {
                            performanceMonitor.monitorMemory();
                        }).not.toThrow();

                        performance.memory = originalMemory;
                    });
                });

                describe('Frame Rate Monitoring', () => {
                    test('calculates FPS correctly', (done) => {
                        jest.useFakeTimers();

                        performanceMonitor.monitorFrameRate();

                        // Fast forward 1 second worth of frames
                        for (let i = 0; i < 60; i++) {
                            jest.advanceTimersByTime(16); // ~60fps
                        }

                        setTimeout(() => {
                            const fpsMetrics = performanceMonitor.getMetrics('fps');
                            expect(fpsMetrics.length).toBeGreaterThan(0);

                            jest.useRealTimers();
                            done();
                        }, 100);
                    });
                });

                describe('React Hook', () => {
                    test('usePerformanceMonitor returns correct functions', () => {
                        const hook = usePerformanceMonitor();

                        expect(hook).toHaveProperty('markStart');
                        expect(hook).toHaveProperty('markEnd');
                        expect(hook).toHaveProperty('measureRender');
                        expect(hook).toHaveProperty('getMetrics');
                        expect(hook).toHaveProperty('getSummary');

                        expect(typeof hook.markStart).toBe('function');
                        expect(typeof hook.markEnd).toBe('function');
                        expect(typeof hook.measureRender).toBe('function');
                        expect(typeof hook.getMetrics).toBe('function');
                        expect(typeof hook.getSummary).toBe('function');
                    });
                });

                describe('Cleanup', () => {
                    test('cleans up observers and metrics', () => {
                        performanceMonitor.recordMetric('test', {value);

                        expect(performanceMonitor.getMetrics('test')).toHaveLength(1);

                        performanceMonitor.cleanup();

                        expect(performanceMonitor.getMetrics('test')).toHaveLength(0);
                    })
                        ;
                    });

                    describe('Error Handling', () => {
                        test('handles observer creation errors gracefully', () => {
                            const originalPerformanceObserver = global.PerformanceObserver;
                            global.PerformanceObserver = undefined;

                            expect(() => {
                                performanceMonitor.initializeObservers();
                            }).not.toThrow();

                            global.PerformanceObserver = originalPerformanceObserver;
                        });

                        test('handles missing performance API gracefully', () => {
                            const originalPerformance = global.performance;
                            global.performance = undefined;

                            expect(() => {
                                performanceMonitor.monitorMemory();
                                performanceMonitor.markStart('test');
                                performanceMonitor.markEnd('test');
                            }).not.toThrow();

                            global.performance = originalPerformance;
                        });
                    });
                });

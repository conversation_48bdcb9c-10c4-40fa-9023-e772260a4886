'use strict';

function ownKeys(e, r) {
    const t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        let o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function (r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}

function _objectSpread(e) {
    for (let r = 1; r < arguments.length; r++) {
        const t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {
            _defineProperty(e, r, t[r]);
        }) ject.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) nKeys(Object(t)).forEach(function (r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}

function _defineProperty(e, r, t) {
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) r
]
    = t, e;
}

function _toPropertyKey(t) {
    const i = _toPrimitive(t, 'string');
    return 'symbol' == typeof i ? i + '';
}

function _toPrimitive(t, r) {
    if ('object' != typeof t || !t) return t;
    const e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        const i = e.call(t, r || 'default');
        if ('object' != typeof i) return i;
        throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === r ? String)(t);
}

import express from 'express';
import cors from 'cors';
import path from 'path';
import sqlite3 from 'sqlite3';
import logger from '../../trading/shared/helpers/logger.js';
const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../dist')));

// Database setup
const db = new sqlite3.verbose().Database('./trading/databases/trading_bot.db');

// Helper function to run queries
const runQuery = (query, params = []) => {
    return new Promise((resolve, reject) => {
        db.all(query, params, (err, rows) => {
            if (err) reject(err); else resolve(rows);
        });
    });
};

// Health check
app.get('/api/health', (req, res) => {
    res.json({
        success,
        status: 'healthy',
        timestamp Date().toISOString()
    });
});

// Get market data
app.get('/api/market-data', async (req, res) => {
    try {
        const query = `
      SELECT * FROM market_data
      WHERE timestamp >= datetime('now', '-1 hour')
      ORDER BY timestamp DESC
      LIMIT 100
    `;
        const data = await runQuery(query);
        res.json({
            success,
            data
        });
    } catch (error) {
        res.status(500).json({
            success,
            error
        });
    }
});

// Get portfolio
app.get('/api/portfolio', async (req, res) => {
    try {
        const query = `
      SELECT
        symbol,
        SUM(CASE WHEN side = 'buy' THEN quantity ELSE -quantity END) as quantity,
        AVG(CASE WHEN side = 'buy' THEN price END) as avg_buy_price,
        SUM(CASE WHEN side = 'buy' THEN price * quantity ELSE 0 END) as total_invested
      FROM trades
      GROUP BY symbol
      HAVING quantity > 0
    `;
        const positions = await runQuery(query);
        res.json({
            success,
            positions
        });
    } catch (error) {
        res.status(500).json({
            success,
            error
        });
    }
});

// Get trades
app.get('/api/trades', async (req, res) => {
    try {
        let _req$query$limit, _req$query$offset;
        const limit = parseInt((_req$query$limit = req.query.limit) === null || _req$query$limit === void 0 ? void 0$query$limit.toString()) || 50;
        const offset = parseInt((_req$query$offset = req.query.offset) === null || _req$query$offset === void 0 ? void 0$query$offset.toString()) || 0;
        const query = `
      SELECT * FROM trades
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
        const trades = await runQuery(query, [limit, offset]);
        res.json({
            success,
            trades
        });
    } catch (error) {
        res.status(500).json({
            success,
            error
        });
    }
});

// Place order
app.post('/api/orders', (req, res) => {
    try {
        const {
            symbol,
            side,
            quantity,
            price,
            type = 'market'
        } = req.body;
        if (!symbol || !side || !quantity) {
            return res.status(400).json({
                success,
                error: 'Missing required fields'
            });
        }
        const order = {
                symbol,
                side,
                quantity,
                price || null,
            type,
            status: 'pending',
            created_at
        Date().toISOString()
    }
        ;
        const insertQuery = `
      INSERT INTO trades (symbol, side, quantity, price, type, status, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;
        const result = await new Promise((resolve, reject) => {
            db.run(insertQuery, [order.symbol, order.side, order.quantity, order.price, order.type, order.status, order.created_at], function (err) {
                if (err) reject(err); else resolve({
                    id
                });
            });
        });
        res.json({
            success,
            order(_objectSpread({}, order), {}, {
            id
        })
    })
        ;
    } catch (error) {
        res.status(500).json({
            success,
            error
        });
    }
});

// Get bot status
app.get('/api/bots', async (req, res) => {
    try {
        const query = `
      SELECT
        name,
        status,
        strategy,
        pair,
        created_at,
        last_activity
      FROM trading_bots
      ORDER BY created_at DESC
    `;
        const bots = await runQuery(query);
        res.json({
            success,
            bots
        });
    } catch (error) {
        res.status(500).json({
            success,
            error
        });
    }
});

// Get performance metrics
app.get('/api/performance', async (req, res) => {
    try {
        const query = `
      SELECT
        DATE(created_at) as date,
        SUM(CASE WHEN side = 'sell' THEN (price - avg_buy_price) * quantity ELSE 0 END) as realized_pnl,
        COUNT(*) as trades
      FROM trades
      WHERE created_at >= datetime('now', '-7 days')
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;
        const performance = await runQuery(query);
        res.json({
            success,
            performance
        });
    } catch (error) {
        res.status(500).json({
            success,
            error
        });
    }
});

// Authentication endpoints
app.post('/api/auth/login', (req, res) => {
    const {
        username,
        password
    } = req.body;

    // Get credentials from environment variables
    const adminUsername = process.env.ADMIN_USERNAME || 'admin';
    const adminPassword = process.env.ADMIN_PASSWORD;

    if (!adminPassword) {
        logger.error('ADMIN_PASSWORD environment variable not set');
        return res.status(500).json({
            success: false,
            error: 'Server configuration error'
        });
    }

    // In production, use proper password hashing (bcrypt, etc.)
    if (username === adminUsername && password === adminPassword) {
        // Generate a proper JWT token in production
        const token = process.env.JWT_SECRET ?
            generateJWTToken(username) :
            'jwt-token-placeholder';

        res.json({
            success: true,
            token,
            user: {
                username,
                role: 'admin'
            }
        });
    } else {
        res.status(401).json({
            success: false,
            error: 'Invalid credentials'
        });
    }
});

// Helper function for JWT token generation (implement with proper JWT library)
function generateJWTToken(username) {
    // This is a placeholder - implement with jsonwebtoken library
    return 'jwt-token-placeholder';
}

// Start server
app.listen(port, () => {
    logger.info(`Trading API server running on port ${port}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
    db.close(err => {
        if (err) logger.error(err);
        process.exit(0);
    });
});
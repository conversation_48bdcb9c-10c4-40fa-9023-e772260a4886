import React from 'react';
import PropTypes from 'prop-types';
import {ComponentErrorFallback, NetworkErrorFallback} from './FallbackComponents';
import {ErrorReporter} from '../services/ErrorReporter';
import systemWideErrorHandler from '../utils/SystemWideErrorHandler';
import logger from '../utils/logger';

/**
 * ApplicationErrorBoundary - Comprehensive error boundary for different application sections
 *
 * Enhanced with:
 * - Comprehensive error boundaries for all critical workflows
 * - Automatic recovery for failed component initializations
 * - Graceful degradation when optional components fail
 * - Advanced error classification and recovery strategies
 * - Component health monitoring and automatic restart
 */
class ApplicationErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.errorReporter = new ErrorReporter();
        this.state = {
            hasError: false,
            error: null,
            errorInfo: null,
            errorType: null,
            retryCount: 0,
            maxRetries: props.maxRetries || 3,
            recoveryStrategy: null,
            degradedMode: false,
            componentHealth: 'healthy',
            lastRecoveryAttempt: null,
            autoRecoveryEnabled: props.autoRecovery !== false,
            gracefulDegradation: props.gracefulDegradation !== false
        };

        // Enhanced error tracking
        this.errorHistory = [];
        this.recoveryAttempts = [];
        this.componentMetrics = {
            totalErrors: 0,
            recoveredErrors: 0,
            failedRecoveries: 0,
            degradationEvents: 0,
            restartEvents: 0
        };

        // Recovery strategies for different error types
        this.recoveryStrategies = new Map([
            ['network', {
                strategy: 'retry-with-backoff',
                maxRetries: 5,
                backoffMultiplier: 2,
                fallback: 'cached-data'
            }],
            ['chunk_load', {
                strategy: 'reload-component',
                maxRetries: 2,
                fallback: 'minimal-ui'
            }],
            ['component', {
                strategy: 'restart-component',
                maxRetries: 3,
                fallback: 'degraded-mode'
            }],
            ['critical', {
                strategy: 'emergency-fallback',
                maxRetries: 1,
                fallback: 'safe-mode'
            }],
            ['trading', {
                strategy: 'trading-recovery',
                maxRetries: 2,
                fallback: 'read-only-mode'
            }],
            ['database', {
                strategy: 'data-recovery',
                maxRetries: 3,
                fallback: 'offline-mode'
            }]]);

        // Component criticality levels
        this.componentCriticality = {
            'Dashboard': 'high',
            'Trading': 'critical',
            'Portfolio': 'high',
            'Network': 'medium',
            'MarketData': 'high',
            'Settings': 'low'
        };
    }

    static getDerivedStateFromError(error) {
        // Enhanced error classification for better recovery strategies
        let errorType = 'component';
        let severity = 'medium';
        let recoverable = true;

        const errorMessage = error.message?.toLowerCase() || '';
        const errorStack = error.stack?.toLowerCase() || '';

        // Network and connectivity errors
        if (errorMessage.includes('network') || errorMessage.includes('fetch') ||
            errorMessage.includes('timeout') || errorMessage.includes('connection')) {
            errorType = 'network';
            severity = 'high';
            recoverable = true;
        }
        // Chunk loading errors (webpack/build related)
        else if (errorMessage.includes('chunk_error') || errorMessage.includes('loading chunk') ||
            errorMessage.includes('loading css chunk')) {
            errorType = 'chunk_load';
            severity = 'high';
            recoverable = true;
        }
        // Permission and authentication errors
        else if (errorMessage.includes('permission') || errorMessage.includes('unauthorized') ||
            errorMessage.includes('forbidden') || errorMessage.includes('auth')) {
            errorType = 'permission';
            severity = 'high';
            recoverable = false;
        }
        // Trading system errors
        else if (errorMessage.includes('trading') || errorMessage.includes('order') ||
            errorMessage.includes('position') || errorMessage.includes('exchange')) {
            errorType = 'trading';
            severity = 'critical';
            recoverable = true;
        }
        // Database and storage errors
        else if (errorMessage.includes('database') || errorMessage.includes('storage') ||
            errorMessage.includes('indexeddb') || errorMessage.includes('localstorage')) {
            errorType = 'database';
            severity = 'high';
            recoverable = true;
        }
        // Memory and performance errors
        else if (errorMessage.includes('memory') || errorMessage.includes('heap') ||
            errorMessage.includes('maximum call stack')) {
            errorType = 'memory';
            severity = 'critical';
            recoverable = false;
        }
        // React-specific errors
        else if (errorStack.includes('react') || errorMessage.includes('render') ||
            errorMessage.includes('hook') || errorMessage.includes('component')) {
            errorType = 'react';
            severity = 'medium';
            recoverable = true;
        }

        // Determine if this is a critical error that requires immediate attention
        const isCritical = severity === 'critical' ||
            errorMessage.includes('emergency') ||
            errorMessage.includes('fatal') ||
            errorMessage.includes('critical');

        return {
            hasError: true,
            error,
            errorType,
            severity,
            recoverable,
            isCritical,
            timestamp: new Date().toISOString()
        };
    }

    componentDidCatch(error, errorInfo) {
        const {componentName = 'Unknown', userId, workflow = 'general'} = this.props;

        // Enhanced error data collection with workflow context
        const errorData = {
            id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: `${this.props.errorType || 'application'}_error_boundary`,
            message: error.message,
            stack: error.stack,
            componentStack: errorInfo.componentStack,
            componentName,
            workflow,
            errorType: this.state.errorType,
            severity: this.state.severity,
            recoverable: this.state.recoverable,
            isCritical: this.state.isCritical,
            retryCount: this.state.retryCount,
            url: window.location.href,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString(),
            userId: userId || this.getCurrentUserId(),
            sessionId: this.getSessionId(),
            props: this.sanitizeProps(this.props),
            state: this.sanitizeState(),
            componentHealth: this.state.componentHealth,
            metrics: this.componentMetrics
        };

        // Add to error history
        this.errorHistory.push(errorData);
        if (this.errorHistory.length > 50) {
            this.errorHistory.shift();
        }

        // Update metrics
        this.componentMetrics.totalErrors++;

        // Determine recovery strategy
        const recoveryStrategy = this.determineRecoveryStrategy(errorData);

        // Update component state with enhanced information
        this.setState({
            error,
            errorInfo,
            recoveryStrategy,
            componentHealth: this.assessComponentHealth(errorData),
            lastRecoveryAttempt: new Date().toISOString()
        });

        // Report error with enhanced context
        this.errorReporter.report(errorData);

        // Attempt automatic recovery if enabled and appropriate
        if (this.state.autoRecoveryEnabled && this.shouldAttemptRecovery(errorData)) {
            this.scheduleRecoveryAttempt(recoveryStrategy, errorData);
        }

        // Handle graceful degradation for non-critical components
        if (this.state.gracefulDegradation && this.shouldDegrade(errorData)) {
            this.handleGracefulDegradation(errorData);
        }

        // Call custom error handler if provided
        if (this.props.onError) {
            this.props.onError(error, errorInfo, errorData);
        }

        // Emit error event for system-wide error tracking
        this.emitErrorEvent(errorData);

        // Register with system-wide error handler if available
        if (systemWideErrorHandler.initialized) {
            // Register this error boundary
            if (window['registerErrorBoundary']) {
                window['registerErrorBoundary'](this.props.componentName || 'unknown', {
                    onSystemError: this.handleSystemError.bind(this),
                    handleRecovery: this.handleSystemRecovery.bind(this)
                });
            }
        }
    }

    handleSystemError = (systemErrorData) => {
        // Handle system-wide errors that might affect this component
        logger.info(`Error boundary ${this.props.componentName} received system error:`, systemErrorData);

        // If this is a related error, update our state
        if (systemErrorData.component === this.props.componentName ||
            systemErrorData.workflow === this.props.workflow) {
            this.setState({
                hasError: true,
                error: new Error(systemErrorData.message),
                errorType: systemErrorData.type,
                systemError: true
            });
        }
    };

    handleSystemRecovery = async (errorData) => {
        // Attempt to recover this component
        logger.info(`Attempting system recovery for ${this.props.componentName}`);

        try {
            // Reset error state
            this.setState({
                hasError: false,
                error: null,
                errorInfo: null,
                retryCount: 0,
                componentHealth: 'recovering'
            });

            // Call custom recovery handler if provided
            if (this.props.onRecovery) {
                await this.props.onRecovery(errorData);
            }

            return true;
        } catch (error) {
            logger.error(`System recovery failed for ${this.props.componentName}:`, error);
            return false;
        }
    };

    componentWillUnmount() {
        // Unregister from system-wide error handler
        if (window['unregisterErrorBoundary']) {
            window['unregisterErrorBoundary'](this.props.componentName || 'unknown');
        }
    }

    sanitizeProps(props) {
        // Remove sensitive data from props before logging
        const {_children, _onError, ...safeProps} = props;
        return Object.keys(safeProps).reduce((acc, key) => {
            const value = safeProps[key];
            if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
                acc[key] = value;
            } else if (Array.isArray(value)) {
                acc[key] = `Array(${value.length})`;
            } else if (typeof value === 'object' && value !== null) {
                acc[key] = `Object(${Object.keys(value).length} keys)`;
            }
            return acc;
        }, {});
    }

    sanitizeState() {
        const {hasError, errorType, retryCount, maxRetries} = this.state;
        return {hasError, errorType, retryCount, maxRetries};
    }

    getCurrentUserId() {
        try {
            const auth = JSON.parse(localStorage.getItem('auth') || '{}');
            return auth.user?.id || 'anonymous';
        } catch {
            return 'anonymous';
        }
    }

    getSessionId() {
        let sessionId = sessionStorage.getItem('sessionId');
        if (!sessionId) {
            sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            sessionStorage.setItem('sessionId', sessionId);
        }
        return sessionId;
    }

    // Enhanced recovery methods
    determineRecoveryStrategy(errorData) {
        const strategy = this.recoveryStrategies.get(errorData.errorType) ||
            this.recoveryStrategies.get('component');
        // Adjust strategy based on component criticality
        const criticality = this.componentCriticality[errorData.componentName] || 'medium';

        if (criticality === 'critical' && errorData.isCritical) {
            return this.recoveryStrategies.get('critical');
        }

        return strategy;
    }

    shouldAttemptRecovery(errorData) {
        // Don't attempt recovery if we've exceeded max retries
        if (this.state.retryCount >= this.state.maxRetries) {
            return false;
        }

        // Don't attempt recovery for non-recoverable errors
        if (!errorData.recoverable) {
            return false;
        }

        // Don't attempt recovery if component is in degraded mode
        if (this.state.degradedMode) {
            return false;
        }

        // Check if we've had too many recent errors
        const recentErrors = this.errorHistory.filter(e => {
            const errorTime = new Date(e.timestamp);
            const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
            return errorTime > fiveMinutesAgo;
        });

        return recentErrors.length < 10; // Don't recover if too many recent errors
    }

    shouldDegrade(errorData) {
        const criticality = this.componentCriticality[errorData.componentName] || 'medium';

        // Always try to degrade non-critical components
        if (criticality === 'low') {
            return true;
        }

        // Degrade if we've had multiple failures
        if (this.state.retryCount >= 2) {
            return true;
        }

        // Degrade if error is not recoverable
        if (!errorData.recoverable) {
            return true;
        }

        return false;
    }

    assessComponentHealth(_errorData) {
        const recentErrors = this.errorHistory.filter(e => {
            const errorTime = new Date(e.timestamp);
            const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);
            return errorTime > tenMinutesAgo;
        });

        if (recentErrors.length === 0) return 'healthy';
        if (recentErrors.length <= 2) return 'warning';
        if (recentErrors.length <= 5) return 'degraded';
        return 'critical';
    }

    scheduleRecoveryAttempt(strategy, errorData) {
        const delay = this.calculateRecoveryDelay(strategy, this.state.retryCount);

        setTimeout(() => {
            this.executeRecoveryStrategy(strategy, errorData);
        }, delay);
    }

    calculateRecoveryDelay(strategy, retryCount) {
        const baseDelay = strategy.baseDelay || 1000;
        const backoffMultiplier = strategy.backoffMultiplier || 2;

        return baseDelay * Math.pow(backoffMultiplier, retryCount);
    }

    async executeRecoveryStrategy(strategy, errorData) {
        const recoveryAttempt = {
            id: `recovery_${Date.now()}`,
            strategy: strategy.strategy,
            errorId: errorData.id,
            timestamp: new Date().toISOString(),
            success: false
        };

        this.recoveryAttempts.push(recoveryAttempt);

        try {
            let success = false;

            switch (strategy.strategy) {
                case 'retry-with-backoff':
                    success = await this.retryWithBackoff(errorData);
                    break;
                case 'reload-component':
                    success = await this.reloadComponent(errorData);
                    break;
                case 'restart-component':
                    success = await this.restartComponent(errorData);
                    break;
                case 'trading-recovery':
                    success = await this.tradingRecovery(errorData);
                    break;
                case 'data-recovery':
                    success = await this.dataRecovery(errorData);
                    break;
                case 'emergency-fallback':
                    success = await this.emergencyFallback(errorData);
                    break;
                default:
                    success = await this.defaultRecovery(errorData);
            }

            recoveryAttempt.success = success;

            if (success) {
                this.componentMetrics.recoveredErrors++;
                this.setState({
                    hasError: false,
                    error: null,
                    errorInfo: null,
                    componentHealth: 'recovered',
                    retryCount: this.state.retryCount + 1
                });

                // Call custom recovery handler if provided
                if (this.props.onRecovery) {
                    this.props.onRecovery(recoveryAttempt, errorData);
                }
            } else {
                this.componentMetrics.failedRecoveries++;

                // Try fallback strategy
                if (strategy.fallback) {
                    await this.executeFallbackStrategy(strategy.fallback, errorData);
                }
            }

        } catch (recoveryError) {
            logger.error('Recovery strategy failed:', recoveryError);
            recoveryAttempt.error = recoveryError.message;
            this.componentMetrics.failedRecoveries++;
        }
    }

    async retryWithBackoff(_errorData) {
        // Simple retry - reset error state
        return true;
    }

    async reloadComponent(_errorData) {
        // Force component remount by changing key
        if (this.props.onReload) {
            this.props.onReload();
            return true;
        }
        return false;
    }

    async restartComponent(_errorData) {
        // Restart component with clean state
        this.componentMetrics.restartEvents++;
        return true;
    }

    async tradingRecovery(errorData) {
        // Specific recovery for trading components
        if (window.electronAPI && window.electronAPI.ipcRenderer) {
            try {
                // Use the correct IPC channel for trading recovery
                const result = await window.electronAPI.ipcRenderer.invoke('recover-trading-system', {
                    component: errorData.context?.component,
                    errorType: errorData.errorType,
                    timestamp: new Date().toISOString()
                });

                if (result && result.success) {
                    logger.info('Trading system recovery successful');
                    return true;
                } else {
                    logger.error('Trading recovery failed:', result?.error);
                    return false;
                }
            } catch (error) {
                logger.error('Trading recovery failed:', error);
                return false;
            }
        }
        return false;
    }

    async dataRecovery(_errorData) {
        // Attempt to recover data from backup or cache
        try {
            // Clear potentially corrupted data
            localStorage.removeItem('corrupted_data');
            sessionStorage.clear();

            // Reload from server if possible
            if (this.props.onDataRecovery) {
                await this.props.onDataRecovery();
            }

            return true;
        } catch (error) {
            logger.error('Data recovery failed:', error);
            return false;
        }
    }

    async emergencyFallback(_errorData) {
        // Emergency fallback - minimal functionality
        this.setState({
            degradedMode: true,
            componentHealth: 'emergency'
        });
        return true;
    }

    async defaultRecovery(_errorData) {
        // Default recovery strategy
        return true;
    }

    async executeFallbackStrategy(fallbackType, errorData) {
        switch (fallbackType) {
            case 'cached-data':
                return this.useCachedData(errorData);
            case 'minimal-ui':
                return this.showMinimalUI(errorData);
            case 'degraded-mode':
                return this.enterDegradedMode(errorData);
            case 'read-only-mode':
                return this.enterReadOnlyMode(errorData);
            case 'offline-mode':
                return this.enterOfflineMode(errorData);
            case 'safe-mode':
                return this.enterSafeMode(errorData);
            default:
                return this.enterDegradedMode(errorData);
        }
    }

    async useCachedData(_errorData) {
        // Use cached data instead of live data
        this.setState({degradedMode: true});
        return true;
    }

    async showMinimalUI(_errorData) {
        // Show minimal UI with basic functionality
        this.setState({degradedMode: true});
        return true;
    }

    async enterDegradedMode(errorData) {
        // Enter degraded mode with reduced functionality
        this.componentMetrics.degradationEvents++;
        this.setState({
            degradedMode: true,
            componentHealth: 'degraded'
        });

        if (this.props.onDegradation) {
            this.props.onDegradation(errorData);
        }

        return true;
    }

    async enterReadOnlyMode(_errorData) {
        // Enter read-only mode for trading components
        this.setState({
            degradedMode: true,
            componentHealth: 'read-only'
        });
        return true;
    }

    async enterOfflineMode(_errorData) {
        // Enter offline mode with local data only
        this.setState({
            degradedMode: true,
            componentHealth: 'offline'
        });
        return true;
    }

    async enterSafeMode(_errorData) {
        // Enter safe mode with minimal functionality
        this.setState({
            degradedMode: true,
            componentHealth: 'safe-mode'
        });
        return true;
    }

    handleGracefulDegradation(errorData) {
        const criticality = this.componentCriticality[errorData.componentName] || 'medium';

        if (criticality === 'low') {
            // For low-criticality components, just hide them
            this.setState({
                degradedMode: true,
                componentHealth: 'hidden'
            });
        } else {
            // For other components, show degraded version
            this.enterDegradedMode(errorData);
        }
    }

    emitErrorEvent(errorData) {
        // Emit custom event for system-wide error tracking
        const event = new CustomEvent('componentError', {
            detail: {
                errorData,
                componentName: this.props.componentName,
                workflow: this.props.workflow,
                timestamp: new Date().toISOString()
            }
        });

        window.dispatchEvent(event);
    }

    handleRetry = () => {
        const {retryCount, maxRetries, recoveryStrategy} = this.state;

        if (retryCount < maxRetries) {
            // Execute recovery strategy if available
            if (recoveryStrategy && this.state.autoRecoveryEnabled) {
                this.executeRecoveryStrategy(recoveryStrategy, {
                    id: `manual_retry_${Date.now()}`,
                    type: 'manual_retry',
                    componentName: this.props.componentName
                });
            } else {
                // Simple retry
                this.setState(prevState => ({
                    hasError: false,
                    error: null,
                    errorInfo: null,
                    retryCount: prevState.retryCount + 1,
                    componentHealth: 'recovering'
                }));
            }

            // Call custom retry handler if provided
            if (this.props.onRetry) {
                this.props.onRetry(retryCount + 1);
            }
        } else {
            // Max retries reached, enter degraded mode
            logger.warn('Max retries reached for error boundary, entering degraded mode');
            this.enterDegradedMode({
                reason: 'max_retries_exceeded',
                retryCount,
                maxRetries
            });
        }
    };

    handleGoHome = () => {
        if (this.props.onGoHome) {
            this.props.onGoHome();
        } else {
            window.location.href = '/dashboard';
        }
    };

    handleReload = () => {
        window.location.reload();
    };

    getFallbackComponent() {
        const {error, errorInfo, errorType, retryCount, maxRetries} = this.state;
        const {fallbackComponent: CustomFallback} = this.props;

        if (CustomFallback) {
            return (
                <CustomFallback
                    error={error}
                    errorInfo={errorInfo}
                    errorType={errorType}
                    retryCount={retryCount}
                    maxRetries={maxRetries}
                    onRetry={this.handleRetry}
                    onGoHome={this.handleGoHome}
                    onReload={this.handleReload}
                />
            );
        }

        // Default fallback based on error type
        switch (errorType) {
            case 'network':
                return (
                    <NetworkErrorFallback
                        error={error}
                        onRetry={this.handleRetry}
                    />
                );
            case 'chunk_load':
                return (
                    <ComponentErrorFallback
                        error={{message: 'Failed to load application resources. Please refresh the page.'}}
                        errorInfo={errorInfo}
                        onRetry={this.handleReload}
                        onGoHome={this.handleGoHome}
                    />
                );
            default:
                return (
                    <ComponentErrorFallback
                        error={error}
                        errorInfo={errorInfo}
                        onRetry={retryCount < maxRetries ? this.handleRetry : null}
                        onGoHome={this.handleGoHome}
                    />
                );
        }
    }

    render() {
        const {hasError} = this.state;
        const {children} = this.props;

        if (hasError) {
            return this.getFallbackComponent();
        }

        return children;
    }
}

ApplicationErrorBoundary.propTypes = {
    children: PropTypes.node.isRequired,
    componentName: PropTypes.string,
    errorType: PropTypes.string,
    fallbackComponent: PropTypes.elementType,
    onError: PropTypes.func,
    onRetry: PropTypes.func,
    onGoHome: PropTypes.func,
    userId: PropTypes.string
};

// Specialized error boundaries for different application sections
export const DashboardErrorBoundary = ({children, ...props}) => (
    <ApplicationErrorBoundary
        componentName="Dashboard"
        errorType="dashboard"
        {...props}
    >
        {children}
    </ApplicationErrorBoundary>
);

export const TradingErrorBoundary = ({children, ...props}) => (
    <ApplicationErrorBoundary
        componentName="Trading"
        errorType="trading"
        {...props}
    >
        {children}
    </ApplicationErrorBoundary>
);

export const PortfolioErrorBoundary = ({children, ...props}) => (
    <ApplicationErrorBoundary
        componentName="Portfolio"
        errorType="portfolio"
        {...props}
    >
        {children}
    </ApplicationErrorBoundary>
);

export const NetworkErrorBoundary = ({children, ...props}) => (
    <ApplicationErrorBoundary
        componentName="Network"
        errorType="network"
        {...props}
    >
        {children}
    </ApplicationErrorBoundary>
);

export default ApplicationErrorBoundary;
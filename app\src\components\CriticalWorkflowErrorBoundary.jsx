// Import logger for consistent logging
import logger from '../utils/logger';

import React from 'react';
import PropTypes from 'prop-types';
import ApplicationErrorBoundary from './ApplicationErrorBoundary';
import {ErrorReporter} from '../services/ErrorReporter';

/**
 * CriticalWorkflowErrorBoundary - Specialized error boundary for critical application workflows
 *
 * Provides enhanced error handling for critical workflows such as:
 * - Trading execution workflows
 * - Portfolio management workflows
 * - Market data workflows
 * - System initialization workflows
 * - Authentication workflows
 */
class CriticalWorkflowErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.errorReporter = new ErrorReporter();

        this.state = {
            hasError: false,
            error: null,
            errorInfo: null,
            workflowState: 'healthy',
            recoveryAttempts: 0,
            lastRecoveryTime: null,
            criticalFailure: false,
            emergencyMode: false
        };

        // Critical workflow definitions
        this.criticalWorkflows = {
            'trading-execution': {
                maxRetries: 2,
                recoveryTimeout: 10000,
                fallbackStrategy: 'emergency-stop',
                requiresImmediateAttention: true
            },
            'portfolio-management': {
                maxRetries: 3,
                recoveryTimeout: 15000,
                fallbackStrategy: 'read-only-mode',
                requiresImmediateAttention: true
            },
            'market-data': {
                maxRetries: 5,
                recoveryTimeout: 5000,
                fallbackStrategy: 'cached-data',
                requiresImmediateAttention: false
            },
            'system-initialization': {
                maxRetries: 1,
                recoveryTimeout: 30000,
                fallbackStrategy: 'safe-mode',
                requiresImmediateAttention: true
            },
            'authentication': {
                maxRetries: 2,
                recoveryTimeout: 8000,
                fallbackStrategy: 'logout',
                requiresImmediateAttention: true
            },
            'data-persistence': {
                maxRetries: 3,
                recoveryTimeout: 12000,
                fallbackStrategy: 'memory-only',
                requiresImmediateAttention: false
            }
        };

        // Workflow health monitoring
        this.workflowMetrics = {
            totalErrors: 0,
            criticalErrors: 0,
            recoverySuccesses: 0,
            recoveryFailures: 0,
            emergencyStops: 0,
            degradationEvents: 0
        };

        this.errorHistory = [];
        this.recoveryHistory = [];
    }

    static getDerivedStateFromError(error) {
        return {
            hasError: true,
            error,
            timestamp: new Date().toISOString()
        };
    }

    componentDidCatch(error, errorInfo) {
        const {workflow, componentName = 'Unknown'} = this.props;

        // Enhanced error data for critical workflows
        const errorData = {
            id: `critical_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: 'critical_workflow_error',
            workflow,
            componentName,
            error: {
                message: error.message,
                stack: error.stack,
                type: error.constructor.name
            },
            errorInfo,
            timestamp: new Date().toISOString(),
            severity: this.assessErrorSeverity(error, workflow),
            impact: this.assessWorkflowImpact(error, workflow),
            recoverable: this.isRecoverable(error, workflow),
            requiresEmergencyStop: this.requiresEmergencyStop(error, workflow)
        };

        // Add to error history
        this.errorHistory.push(errorData);
        if (this.errorHistory.length > 100) {
            this.errorHistory.shift();
        }

        // Update metrics
        this.workflowMetrics.totalErrors++;
        if (errorData.severity === 'critical') {
            this.workflowMetrics.criticalErrors++;
        }

        // Update component state
        this.setState({
            error,
            errorInfo,
            workflowState: this.determineWorkflowState(errorData),
            criticalFailure: errorData.severity === 'critical',
            emergencyMode: errorData.requiresEmergencyStop
        });

        // Report error with critical workflow context
        this.errorReporter.report({
            ...errorData,
            workflowMetrics: this.workflowMetrics,
            workflowConfig: this.criticalWorkflows[workflow]
        });

        // Handle critical workflow error
        this.handleCriticalWorkflowError(errorData);

        // Call custom error handler if provided
        if (this.props.onError) {
            this.props.onError(error, errorInfo, errorData);
        }
    }

    assessErrorSeverity(error, workflow) {
        const errorMessage = error.message.toLowerCase();
        const workflowConfig = this.criticalWorkflows[workflow];

        // Critical severity conditions
        if (workflowConfig?.requiresImmediateAttention) {
            if (errorMessage.includes('trading') || errorMessage.includes('order') ||
                errorMessage.includes('position') || errorMessage.includes('execution')) {
                return 'critical';
            }
        }

        if (errorMessage.includes('database') || errorMessage.includes('connection') ||
            errorMessage.includes('authentication') || errorMessage.includes('security')) {
            return 'critical';
        }

        // High severity
        if (errorMessage.includes('network') || errorMessage.includes('timeout') ||
            errorMessage.includes('api') || errorMessage.includes('exchange')) {
            return 'high';
        }

        return 'medium';
    }

    assessWorkflowImpact(error, workflow) {
        const impactLevels = {
            'trading-execution': 'critical',
            'portfolio-management': 'high',
            'system-initialization': 'critical',
            'authentication': 'high',
            'market-data': 'medium',
            'data-persistence': 'medium'
        };

        return impactLevels[workflow] || 'low';
    }

    isRecoverable(error, workflow) {
        const errorMessage = error.message.toLowerCase();

        // Non-recoverable conditions
        if (errorMessage.includes('fatal') || errorMessage.includes('corrupted') ||
            errorMessage.includes('permission denied') || errorMessage.includes('unauthorized')) {
            return false;
        }

        // Workflow-specific recoverability
        if (workflow === 'authentication' && errorMessage.includes('token')) {
            return true; // Can refresh token
        }

        if (workflow === 'trading-execution' && errorMessage.includes('insufficient')) {
            return false; // Can't recover from insufficient funds
        }

        return true;
    }

    requiresEmergencyStop(error, workflow) {
        const errorMessage = error.message.toLowerCase();

        // Emergency stop conditions
        if (workflow === 'trading-execution' &&
            (errorMessage.includes('critical') || errorMessage.includes('emergency'))) {
            return true;
        }

        if (errorMessage.includes('security') || errorMessage.includes('breach') ||
            errorMessage.includes('unauthorized access')) {
            return true;
        }

        return false;
    }

    determineWorkflowState(errorData) {
        if (errorData.requiresEmergencyStop) {
            return 'emergency';
        }

        if (errorData.severity === 'critical') {
            return 'critical';
        }

        if (!errorData.recoverable) {
            return 'failed';
        }

        return 'degraded';
    }

    async handleCriticalWorkflowError(errorData) {
        const {workflow} = this.props;
        const workflowConfig = this.criticalWorkflows[workflow];

        if (!workflowConfig) {
            logger.warn(`No configuration found for workflow: ${workflow}`);
            return;
        }

        // Handle emergency stop requirement
        if (errorData.requiresEmergencyStop) {
            await this.handleEmergencyStop(errorData);
            return;
        }

        // Attempt recovery if recoverable and within retry limits
        if (errorData.recoverable && this.state.recoveryAttempts < workflowConfig.maxRetries) {
            await this.attemptWorkflowRecovery(errorData, workflowConfig);
        } else {
            // Apply fallback strategy
            await this.applyFallbackStrategy(errorData, workflowConfig);
        }
    }

    async handleEmergencyStop(errorData) {
        logger.error('EMERGENCY STOP TRIGGERED for workflow:', this.props.workflow);

        this.workflowMetrics.emergencyStops++;

        this.setState({
            emergencyMode: true,
            workflowState: 'emergency'
        });

        // Notify system of emergency stop
        if (window.electronAPI && window.electronAPI.trading) {
            try {
                await window.electronAPI.trading.emergencyStop({
                    reason: 'critical_workflow_error',
                    workflow: this.props.workflow,
                    errorData
                });
            } catch (emergencyError) {
                logger.error('Failed to trigger emergency stop:', emergencyError);
            }
        }

        // Emit emergency event
        const emergencyEvent = new CustomEvent('workflowEmergency', {
            detail: {
                workflow: this.props.workflow,
                errorData,
                timestamp: new Date().toISOString()
            }
        });
        window.dispatchEvent(emergencyEvent);

        // Call emergency handler if provided
        if (this.props.onEmergency) {
            this.props.onEmergency(errorData);
        }
    }

    async attemptWorkflowRecovery(errorData, workflowConfig) {
        const recoveryAttempt = {
            id: `recovery_${Date.now()}`,
            workflow: this.props.workflow,
            errorId: errorData.id,
            attempt: this.state.recoveryAttempts + 1,
            maxAttempts: workflowConfig.maxRetries,
            startTime: new Date().toISOString(),
            strategy: this.determineRecoveryStrategy(errorData),
            success: false
        };

        logger.info(`Attempting workflow recovery for ${this.props.workflow} (attempt ${recoveryAttempt.attempt}/${recoveryAttempt.maxAttempts})`);

        try {
            // Execute recovery strategy
            const success = await this.executeRecoveryStrategy(recoveryAttempt.strategy, errorData);

            recoveryAttempt.success = success;
            recoveryAttempt.endTime = new Date().toISOString();
            recoveryAttempt.duration = new Date(recoveryAttempt.endTime).getTime() - new Date(recoveryAttempt.startTime).getTime();

            if (success) {
                this.workflowMetrics.recoverySuccesses++;

                this.setState({
                    hasError: false,
                    error: null,
                    errorInfo: null,
                    workflowState: 'recovered',
                    recoveryAttempts: this.state.recoveryAttempts + 1,
                    lastRecoveryTime: new Date().toISOString()
                });

                logger.info(`Workflow recovery successful for ${this.props.workflow}`);

                if (this.props.onRecovery) {
                    this.props.onRecovery(recoveryAttempt);
                }
            } else {
                this.workflowMetrics.recoveryFailures++;

                this.setState({
                    recoveryAttempts: this.state.recoveryAttempts + 1
                });

                // Try fallback if max retries reached
                if (this.state.recoveryAttempts >= workflowConfig.maxRetries) {
                    await this.applyFallbackStrategy(errorData, workflowConfig);
                }
            }

        } catch (recoveryError) {
            logger.error('Recovery attempt failed:', recoveryError);
            recoveryAttempt.error = recoveryError.message;
            recoveryAttempt.success = false;

            this.workflowMetrics.recoveryFailures++;

            this.setState({
                recoveryAttempts: this.state.recoveryAttempts + 1
            });
        }

        this.recoveryHistory.push(recoveryAttempt);
        if (this.recoveryHistory.length > 50) {
            this.recoveryHistory.shift();
        }
    }

    determineRecoveryStrategy(errorData) {
        const {workflow} = this.props;
        const errorCategory = this.categorizeError(errorData.error);

        const strategies = {
            'trading-execution': {
                'network': 'retry-with-backoff',
                'validation': 'sanitize-and-retry',
                'timeout': 'increase-timeout-retry',
                'default': 'restart-execution-engine'
            },
            'portfolio-management': {
                'data': 'refresh-portfolio-data',
                'calculation': 'recalculate-positions',
                'default': 'reload-portfolio'
            },
            'market-data': {
                'network': 'switch-data-source',
                'timeout': 'increase-polling-interval',
                'default': 'use-cached-data'
            },
            'system-initialization': {
                'dependency': 'reinitialize-dependencies',
                'configuration': 'reload-configuration',
                'default': 'restart-system'
            },
            'authentication': {
                'token': 'refresh-token',
                'session': 'reestablish-session',
                'default': 'reauthenticate'
            }
        };

        const workflowStrategies = strategies[workflow] || strategies['system-initialization'];
        return workflowStrategies[errorCategory] || workflowStrategies['default'];
    }

    categorizeError(error) {
        const message = error.message.toLowerCase();

        if (message.includes('network') || message.includes('connection')) return 'network';
        if (message.includes('timeout')) return 'timeout';
        if (message.includes('validation') || message.includes('invalid')) return 'validation';
        if (message.includes('data') || message.includes('parse')) return 'data';
        if (message.includes('token') || message.includes('auth')) return 'token';
        if (message.includes('session')) return 'session';
        if (message.includes('dependency') || message.includes('module')) return 'dependency';
        if (message.includes('config')) return 'configuration';
        if (message.includes('calculation') || message.includes('math')) return 'calculation';

        return 'general';
    }

    async executeRecoveryStrategy(strategy, errorData) {
        logger.info(`Executing recovery strategy: ${strategy}`);

        switch (strategy) {
            case 'retry-with-backoff':
                return await this.retryWithBackoff();
            case 'sanitize-and-retry':
                return await this.sanitizeAndRetry(errorData);
            case 'increase-timeout-retry':
                return await this.increaseTimeoutRetry();
            case 'restart-execution-engine':
                return await this.restartExecutionEngine();
            case 'refresh-portfolio-data':
                return await this.refreshPortfolioData();
            case 'recalculate-positions':
                return await this.recalculatePositions();
            case 'reload-portfolio':
                return await this.reloadPortfolio();
            case 'switch-data-source':
                return await this.switchDataSource();
            case 'increase-polling-interval':
                return await this.increasePollingInterval();
            case 'use-cached-data':
                return await this.useCachedData();
            case 'reinitialize-dependencies':
                return await this.reinitializeDependencies();
            case 'reload-configuration':
                return await this.reloadConfiguration();
            case 'restart-system':
                return await this.restartSystem();
            case 'refresh-token':
                return await this.refreshToken();
            case 'reestablish-session':
                return await this.reestablishSession();
            case 'reauthenticate':
                return await this.reauthenticate();
            default:
                return await this.defaultRecovery();
        }
    }

    async applyFallbackStrategy(errorData, workflowConfig) {
        const fallbackStrategy = workflowConfig.fallbackStrategy;

        logger.info(`Applying fallback strategy: ${fallbackStrategy} for workflow: ${this.props.workflow}`);

        this.workflowMetrics.degradationEvents++;

        this.setState({
            workflowState: 'fallback',
            emergencyMode: fallbackStrategy === 'emergency-stop'
        });

        switch (fallbackStrategy) {
            case 'emergency-stop':
                await this.handleEmergencyStop(errorData);
                break;
            case 'read-only-mode':
                await this.enableReadOnlyMode();
                break;
            case 'cached-data':
                await this.useCachedData();
                break;
            case 'safe-mode':
                await this.enableSafeMode();
                break;
            case 'logout':
                await this.performLogout();
                break;
            case 'memory-only':
                await this.enableMemoryOnlyMode();
                break;
            default:
                await this.enableDegradedMode();
        }

        if (this.props.onFallback) {
            this.props.onFallback(fallbackStrategy, errorData);
        }
    }

    // Recovery strategy implementations
    async retryWithBackoff() {
        const delay = Math.min(1000 * Math.pow(2, this.state.recoveryAttempts), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));
        return true;
    }

    async sanitizeAndRetry(_errorData) {
        // Implement data sanitization logic
        return true;
    }

    async increaseTimeoutRetry() {
        // Implement timeout increase logic
        return true;
    }

    async restartExecutionEngine() {
        if (window.electronAPI && window.electronAPI.trading) {
            try {
                await window.electronAPI.trading.restartExecutionEngine();
                return true;
            } catch (error) {
                logger.error('Failed to restart execution engine:', error);
                return false;
            }
        }
        return false;
    }

    async refreshPortfolioData() {
        if (window.electronAPI && window.electronAPI.portfolio) {
            try {
                await window.electronAPI.portfolio.refresh();
                return true;
            } catch (error) {
                logger.error('Failed to refresh portfolio data:', error);
                return false;
            }
        }
        return false;
    }

    async recalculatePositions() {
        // Implement position recalculation logic
        return true;
    }

    async reloadPortfolio() {
        // Implement portfolio reload logic
        return true;
    }

    async switchDataSource() {
        // Implement data source switching logic
        return true;
    }

    async increasePollingInterval() {
        // Implement polling interval increase logic
        return true;
    }

    async useCachedData() {
        // Implement cached data usage logic
        return true;
    }

    async reinitializeDependencies() {
        // Implement dependency reinitialization logic
        return true;
    }

    async reloadConfiguration() {
        // Implement configuration reload logic
        return true;
    }

    async restartSystem() {
        if (window.electronAPI && window.electronAPI.system) {
            try {
                await window.electronAPI.system.restart();
                return true;
            } catch (error) {
                logger.error('Failed to restart system:', error);
                return false;
            }
        }
        return false;
    }

    async refreshToken() {
        // Implement token refresh logic
        return true;
    }

    async reestablishSession() {
        // Implement session reestablishment logic
        return true;
    }

    async reauthenticate() {
        // Implement re-authentication logic
        return true;
    }

    async defaultRecovery() {
        // Default recovery strategy
        return true;
    }

    // Fallback strategy implementations
    async enableReadOnlyMode() {
        logger.info('Enabling read-only mode');
        // Implement read-only mode logic
    }

    async enableSafeMode() {
        logger.info('Enabling safe mode');
        // Implement safe mode logic
    }

    async performLogout() {
        logger.info('Performing logout');
        // Implement logout logic
    }

    async enableMemoryOnlyMode() {
        logger.info('Enabling memory-only mode');
        // Implement memory-only mode logic
    }

    async enableDegradedMode() {
        logger.info('Enabling degraded mode');
        // Implement degraded mode logic
    }

    render() {
        const {hasError, workflowState, emergencyMode} = this.state;
        const {children, workflow, fallbackComponent} = this.props;

        if (hasError) {
            // Use custom fallback component if provided
            if (fallbackComponent) {
                return React.cloneElement(fallbackComponent, {
                    error: this.state.error,
                    errorInfo: this.state.errorInfo,
                    workflow,
                    workflowState,
                    emergencyMode,
                    onRetry: this.handleRetry,
                    metrics: this.workflowMetrics
                });
            }

            // Use ApplicationErrorBoundary as fallback
            return (
                <ApplicationErrorBoundary
                    componentName={`CriticalWorkflow-${workflow}`}
                    workflow={workflow}
                    maxRetries={this.criticalWorkflows[workflow]?.maxRetries || 3}
                    autoRecovery={true}
                    gracefulDegradation={true}
                    onError={this.props.onError}
                    onRecovery={this.props.onRecovery}
                    onEmergency={this.props.onEmergency}
                >
                    {children}
                </ApplicationErrorBoundary>
            );
        }

        return children;
    }

    handleRetry = () => {
        const {workflow} = this.props;
        const workflowConfig = this.criticalWorkflows[workflow];

        if (this.state.recoveryAttempts < (workflowConfig?.maxRetries || 3)) {
            this.setState({
                hasError: false,
                error: null,
                errorInfo: null,
                workflowState: 'recovering'
            });
        }
    };

    getWorkflowMetrics() {
        return {
            ...this.workflowMetrics,
            errorHistory: this.errorHistory.slice(-10), // Last 10 errors
            recoveryHistory: this.recoveryHistory.slice(-10), // Last 10 recoveries
            currentState: this.state.workflowState,
            lastError: this.errorHistory[this.errorHistory.length - 1],
            lastRecovery: this.recoveryHistory[this.recoveryHistory.length - 1]
        };
    }
}

CriticalWorkflowErrorBoundary.propTypes = {
    children: PropTypes.node.isRequired,
    workflow: PropTypes.oneOf([
        'trading-execution',
        'portfolio-management',
        'market-data',
        'system-initialization',
        'authentication',
        'data-persistence']).isRequired,
    componentName: PropTypes.string,
    fallbackComponent: PropTypes.element,
    onError: PropTypes.func,
    onRecovery: PropTypes.func,
    onEmergency: PropTypes.func,
    onFallback: PropTypes.func
};

export default CriticalWorkflowErrorBoundary;

// Specialized workflow error boundaries
export const TradingExecutionErrorBoundary = ({children, ...props}) => (
    <CriticalWorkflowErrorBoundary workflow="trading-execution" {...props}>
        {children}
    </CriticalWorkflowErrorBoundary>
);

export const PortfolioManagementErrorBoundary = ({children, ...props}) => (
    <CriticalWorkflowErrorBoundary workflow="portfolio-management" {...props}>
        {children}
    </CriticalWorkflowErrorBoundary>
);

export const MarketDataErrorBoundary = ({children, ...props}) => (
    <CriticalWorkflowErrorBoundary workflow="market-data" {...props}>
        {children}
    </CriticalWorkflowErrorBoundary>
);

export const SystemInitializationErrorBoundary = ({children, ...props}) => (
    <CriticalWorkflowErrorBoundary workflow="system-initialization" {...props}>
        {children}
    </CriticalWorkflowErrorBoundary>
);

export const AuthenticationErrorBoundary = ({children, ...props}) => (
    <CriticalWorkflowErrorBoundary workflow="authentication" {...props}>
        {children}
    </CriticalWorkflowErrorBoundary>
);

export const DataPersistenceErrorBoundary = ({children, ...props}) => (
    <CriticalWorkflowErrorBoundary workflow="data-persistence" {...props}>
        {children}
    </CriticalWorkflowErrorBoundary>
);
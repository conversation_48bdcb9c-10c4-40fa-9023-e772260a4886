 
'use strict';

Object.defineProperty(exports, '__esModule', {
    value: true
});
exports.default = void 0;
const _react = _interopRequireWildcard(require('react'));
const _propTypes = _interopRequireDefault(require('prop-types'));
const _logger = _interopRequireDefault(require('../utils/logger'));
const _material = require('@mui/material');
const _iconsMaterial = require('@mui/icons-material');
const _framerMotion = require('framer-motion');
const _HolographicCard = _interopRequireDefault(require('./HolographicCard'));
const _FuturisticButton = _interopRequireDefault(require('./FuturisticButton'));

function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}

function _interopRequireWildcard(e, t) {
    if ('function' == typeof WeakMap) var r = new WeakMap(),
        n = new WeakMap();
    return (_interopRequireWildcard = function (e, t) {
        if (!t && e && e.__esModule) return e;
        let o,
            i,
            f = {
                __proto__: null,
                default: e
            };
        if (null === e || 'object' != typeof e && 'function' != typeof e) return f;
        if (o = t ? n : r) {
            if (o.has(e)) return o.get(e);
            o.set(e, f);
        }
        for (const t in e) 'default' !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]);
        return f;
    })(e, t);
}

// Import logger for consistent logging

// Extend the Window interface for Electron preload script
/**
 * @typedef {Object} ElectronApi
 * @property {() => Promise<{success: boolean, data: PortfolioData, error?: string}>} getPortfolioSummary
 * @property {() => Promise<{success: boolean, data: ArbitrageOpportunity[], error?: string}>} getArbitrageOpportunities
 * @property {() => Promise<{success: boolean, data: RiskMetrics, error?: string}>} getPortfolioRiskMetrics
 * @property {() => Promise<{success: boolean, data: PerformanceData, error?: string}>} getPortfolioPerformance
 * @property {() => Promise<{success: boolean, data: ArbitrageStats, error?: string}>} getArbitrageStats
 * @property {() => Promise<{success: boolean, error?: string}>} startArbitrageScanning
 * @property {() => Promise<{success: boolean, error?: string}>} stopArbitrageScanning
 * @property {() => Promise<{success: boolean, error?: string}>} startPortfolioMonitoring
 * @property {() => Promise<{success: boolean, error?: string}>} stopPortfolioMonitoring
 * @property {(payload: { opportunity: ArbitrageOpportunity }) => Promise<{success: boolean, actualProfit: number, error?: string}>} executeArbitrage
 */
/**
 * @global
 * @interface Window
 * @property {ElectronApi} electronAPI
 */
const customWindow = /** @type {Window & { electronAPI?: ElectronApi }} */ /** @type {any} */window;
const safeElectronAPI = customWindow.electronAPI || {
    getPortfolioSummary: () => Promise.resolve({
        success: true,
        data: {
            totalValue: 0,
            dailyChange: 0,
            exchanges: []
        }
    }),
    getArbitrageOpportunities: () => Promise.resolve({
        success: true,
        data: []
    }),
    getPortfolioRiskMetrics: () => Promise.resolve({
        success: true,
        data: {
            totalRiskScore: 0,
            volatility: 0,
            maxDrawdown: 0,
            sharpeRatio: 0
        }
    }),
    getPortfolioPerformance: () => Promise.resolve({
        success: true,
        data: {
            totalReturn: 0,
            totalReturnPercent: 0,
            dailyReturn: 0,
            monthlyReturn: 0,
            winRate: 0
        }
    }),
    getArbitrageStats: () => Promise.resolve({
        success: true,
        data: {
            totalOpportunities: 0,
            successfulTrades: 0,
            successRate: 0,
            averageLatency: 0
        }
    }),
    startArbitrageScanning: () => Promise.resolve({
        success: true
    }),
    stopArbitrageScanning: () => Promise.resolve({
        success: true
    }),
    startPortfolioMonitoring: () => Promise.resolve({
        success: true
    }),
    stopPortfolioMonitoring: () => Promise.resolve({
        success: true
    }),
    executeArbitrage: () => Promise.resolve({
        success: true,
        actualProfit: 0
    })
};

/**
 * @typedef {Object} Exchange
 * @property {string} name
 * @property {string} status
 * @property {number} totalValue
 * @property {number} latency
 */

/**
 * @typedef {Object} PortfolioData
 * @property {number} totalValue
 * @property {number} dailyChange
 * @property {Exchange[]} exchanges
 */
/**
 * @typedef {Object} PerformanceData
 * @property {number} totalReturn
 * @property {number} totalReturnPercent
 * @property {number} dailyReturn
 * @property {number} monthlyReturn
 * @property {number} winRate
 */
/**
 * @typedef {Object} RiskMetrics
 * @property {number} totalRiskScore
 * @property {number} volatility
 * @property {number} maxDrawdown
 * @property {number} sharpeRatio
 */
/**
 * @typedef {Object} ArbitrageOpportunity
 * @property {string} symbol
 * @property {string} buyExchange
 * @property {string} sellExchange
 * @property {number} profitPercentage
 * @property {number} profitUSD
 * @property {number} buyPrice
 * @property {number} sellPrice
 * @property {number} maxVolume
 */
/**
 * @typedef {Object} ArbitrageStats
 * @property {number} totalOpportunities
 * @property {number} successfulTrades
 * @property {number} successRate
 * @property {number} averageLatency
 */

/**
 * @typedef {Object} CrossExchangePortfolioProps
 * @property {() => void} [onClose]
 */
/**
 * @param {CrossExchangePortfolioProps} props
 */
function CrossExchangePortfolio({
                                    onClose
                                }) {
    const [portfolioData, setPortfolioData] = (0, _react.useState)(/** @type {PortfolioData | null} */null);
    const [performanceData, setPerformanceData] = (0, _react.useState)(/** @type {PerformanceData | null} */null);
    const [riskMetrics, setRiskMetrics] = (0, _react.useState)(/** @type {RiskMetrics | null} */null);
    const [arbitrageOpportunities, setArbitrageOpportunities] = (0, _react.useState)(/** @type {ArbitrageOpportunity[]} */[]);
    const [arbitrageStats, setArbitrageStats] = (0, _react.useState)(/** @type {ArbitrageStats | null} */null);
    const [activeTab, setActiveTab] = (0, _react.useState)(0);
    const [loading, setLoading] = (0, _react.useState)(true);
    const [error, setError] = (0, _react.useState)(/** @type {string | null} */null);
    const [lastUpdate, setLastUpdate] = (0, _react.useState)(/** @type {Date | null} */null);
    const [portfolioMonitoring, setPortfolioMonitoring] = (0, _react.useState)(false);
    const [arbitrageScanning, setArbitrageScanning] = (0, _react.useState)(false);

    // Notification stub
    /**
     * @param {string} message
     * @param {'success' | 'error' | 'warning' | 'info'} type
     */
    const showNotification = (message, type) => {
        // Implement your notification logic here, e.g. toast or snackbar
        // For now, just log
         
        _logger.default.info(`[${type}] ${message}`);
    };
    const fetchPortfolioData = (0, _react.useCallback)(async () => {
        try {
            let _safeElectronAPI$getP, _safeElectronAPI$getA, _safeElectronAPI$getP2, _safeElectronAPI$getP3,
                _safeElectronAPI$getA2, _portfolioResponse$va, _arbitrageResponse$va, _riskResponse$value,
                _performanceResponse$, _arbitrageStatsRespon;
            setLoading(true);
            setError(null);
            const [portfolioResponse, arbitrageResponse, riskResponse, performanceResponse, arbitrageStatsResponse] = await Promise.allSettled([(_safeElectronAPI$getP = safeElectronAPI.getPortfolioSummary) === null || _safeElectronAPI$getP === void 0 ? void 0 : _safeElectronAPI$getP.call(safeElectronAPI), (_safeElectronAPI$getA = safeElectronAPI.getArbitrageOpportunities) === null || _safeElectronAPI$getA === void 0 ? void 0 : _safeElectronAPI$getA.call(safeElectronAPI), (_safeElectronAPI$getP2 = safeElectronAPI.getPortfolioRiskMetrics) === null || _safeElectronAPI$getP2 === void 0 ? void 0 : _safeElectronAPI$getP2.call(safeElectronAPI), (_safeElectronAPI$getP3 = safeElectronAPI.getPortfolioPerformance) === null || _safeElectronAPI$getP3 === void 0 ? void 0 : _safeElectronAPI$getP3.call(safeElectronAPI), (_safeElectronAPI$getA2 = safeElectronAPI.getArbitrageStats) === null || _safeElectronAPI$getA2 === void 0 ? void 0 : _safeElectronAPI$getA2.call(safeElectronAPI)]);
            if (portfolioResponse.status === 'fulfilled' && (_portfolioResponse$va = portfolioResponse.value) !== null && _portfolioResponse$va !== void 0 && _portfolioResponse$va.success) {
                setPortfolioData(portfolioResponse.value.data);
            }
            if (arbitrageResponse.status === 'fulfilled' && (_arbitrageResponse$va = arbitrageResponse.value) !== null && _arbitrageResponse$va !== void 0 && _arbitrageResponse$va.success) {
                setArbitrageOpportunities(arbitrageResponse.value.data || []);
            }
            if (riskResponse.status === 'fulfilled' && (_riskResponse$value = riskResponse.value) !== null && _riskResponse$value !== void 0 && _riskResponse$value.success) {
                setRiskMetrics(riskResponse.value.data);
            }
            if (performanceResponse.status === 'fulfilled' && (_performanceResponse$ = performanceResponse.value) !== null && _performanceResponse$ !== void 0 && _performanceResponse$.success) {
                setPerformanceData(performanceResponse.value.data);
            }
            if (arbitrageStatsResponse.status === 'fulfilled' && (_arbitrageStatsRespon = arbitrageStatsResponse.value) !== null && _arbitrageStatsRespon !== void 0 && _arbitrageStatsRespon.success) {
                setArbitrageStats(arbitrageStatsResponse.value.data);
            }
            setLastUpdate(new Date());
        } catch (e) {
            _logger.default.error('Failed to fetch portfolio data:', e);
            setError(e instanceof Error ? e.message : 'An unknown error occurred.');
        } finally {
            setLoading(false);
        }
    }, []);
    (0, _react.useEffect)(() => {
        fetchPortfolioData();
    }, [fetchPortfolioData]);

    /**
     * @param {boolean} enabled
     */
    const handleArbitrageToggle = async enabled => {
        try {
            let _safeElectronAPI$star, _safeElectronAPI$stop;
            setArbitrageScanning(enabled);
            /** @type {{ success: boolean, error?: string } | undefined} */
            const response = enabled ? await ((_safeElectronAPI$star = safeElectronAPI.startArbitrageScanning) === null || _safeElectronAPI$star === void 0 ? void 0 : _safeElectronAPI$star.call(safeElectronAPI)) : await ((_safeElectronAPI$stop = safeElectronAPI.stopArbitrageScanning) === null || _safeElectronAPI$stop === void 0 ? void 0 : _safeElectronAPI$stop.call(safeElectronAPI));
            if (response !== null && response !== void 0 && response.success) {
                showNotification(`Arbitrage scanning ${enabled ? 'started' : 'stopped'}.`, 'success');
            } else {
                throw new Error((response === null || response === void 0 ? void 0 : response.error) || `Failed to ${enabled ? 'start' : 'stop'} arbitrage scanning.`);
            }
        } catch (err) {
            setArbitrageScanning(!enabled); // Revert state on error
            _logger.default.error('Error toggling arbitrage scanning:', err);
            showNotification(err instanceof Error ? err.message : 'Unknown error', 'error');
        }
    };

    /**
     * Toggles portfolio monitoring on or off.
     * If enabled, portfolio monitoring will periodically fetch portfolio data and update the UI.
     * If disabled, portfolio monitoring will be stopped.
     * @param {boolean} enabled - If true, enables portfolio monitoring. If false, disables it.
     * @returns {Promise<void>}
     */
    const handlePortfolioMonitoringToggle = async enabled => {
        try {
            let _safeElectronAPI$star2, _safeElectronAPI$stop2;
            setPortfolioMonitoring(enabled);
            /** @type {{ success: boolean, error?: string } | undefined} */
            const response = enabled ? await ((_safeElectronAPI$star2 = safeElectronAPI.startPortfolioMonitoring) === null || _safeElectronAPI$star2 === void 0 ? void 0 : _safeElectronAPI$star2.call(safeElectronAPI)) : await ((_safeElectronAPI$stop2 = safeElectronAPI.stopPortfolioMonitoring) === null || _safeElectronAPI$stop2 === void 0 ? void 0 : _safeElectronAPI$stop2.call(safeElectronAPI));
            if (response !== null && response !== void 0 && response.success) {
                showNotification(`Portfolio monitoring ${enabled ? 'started' : 'stopped'}.`, 'success');
            } else {
                throw new Error((response === null || response === void 0 ? void 0 : response.error) || `Failed to ${enabled ? 'start' : 'stop'} portfolio monitoring.`);
            }
        } catch (err) {
            setPortfolioMonitoring(!enabled); // Revert state on error
            _logger.default.error('Error toggling portfolio monitoring:', err);
            showNotification(err instanceof Error ? err.message : 'Unknown error', 'error');
        }
    };

    /**
     * @param {number | string | undefined} value
     * @param {string} [currency='USD']
     */
    const formatCurrency = (value, currency = 'USD') => {
        const numericValue = typeof value === 'number' ? value : parseFloat(value || '0') || 0;
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency,
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(numericValue);
    };

    /**
     * @param {number | undefined} value
     */
    const formatPercentage = value => {
        if (typeof value !== 'number') return '0.00%';
        return `${value.toFixed(2)}%`;
    };

    /**
     * @param {number} value
     * @param {number} [threshold=0]
     */
    const getStatusColor = (value, threshold = 0) => {
        if (value > threshold) return 'success.main';
        if (value < threshold) return 'error.main';
        return 'warning.main';
    };

    /**
     * @param {string} status
     */
    const getExchangeChipColor = status => {
        switch (status) {
            case 'connected':
                return 'success';
            case 'error':
                return 'error';
            case 'warning':
                return 'warning';
            default:
                return 'info';
        }
    };

    /**
     * @param {string} status
     */
    const getExchangeStatusIcon = status => {
        switch (status) {
            case 'connected':
                return /*#__PURE__*/_react.default.createElement(_iconsMaterial.CheckCircle, {
                    color: 'success'
                });
            case 'error':
                return /*#__PURE__*/_react.default.createElement(_iconsMaterial.Error, {
                    color: 'error'
                });
            case 'warning':
                return /*#__PURE__*/_react.default.createElement(_iconsMaterial.Warning, {
                    color: 'warning'
                });
            default:
                return /*#__PURE__*/_react.default.createElement(_iconsMaterial.Info, {
                    color: 'info'
                });
        }
    };

    // Portfolio overview
    const renderPortfolioOverview = () => /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'premium',
        elevation: 'high',
        sx: {
            mb: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3,
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 3
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'premium',
        elevation: 'high',
        sx: {}
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            p: 3,
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.AccountBalance, {
        sx: {
            fontSize: 40,
            color: '#00eaff',
            mb: 1
        }
    }), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        color: 'textSecondary',
        gutterBottom: true
    }, 'Total Portfolio Value'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h4',
        color: 'primary',
        fontWeight: 'bold'
    }, formatCurrency((portfolioData === null || portfolioData === void 0 ? void 0 : portfolioData.totalValue) || 0)), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: getStatusColor((portfolioData === null || portfolioData === void 0 ? void 0 : portfolioData.dailyChange) || 0),
        sx: {
            mt: 1
        }
    }, formatPercentage((portfolioData === null || portfolioData === void 0 ? void 0 : portfolioData.dailyChange) || 0), ' 24h')))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 3
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'success',
        elevation: 'high',
        sx: {}
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            p: 3,
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.TrendingUp, {
        sx: {
            fontSize: 40,
            color: '#4caf50',
            mb: 1
        }
    }), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        color: 'textSecondary',
        gutterBottom: true
    }, 'Total P&L'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h4',
        color: getStatusColor((performanceData === null || performanceData === void 0 ? void 0 : performanceData.totalReturn) || 0),
        fontWeight: 'bold'
    }, formatCurrency((performanceData === null || performanceData === void 0 ? void 0 : performanceData.totalReturn) || 0)), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: 'textSecondary',
        sx: {
            mt: 1
        }
    }, formatPercentage((performanceData === null || performanceData === void 0 ? void 0 : performanceData.totalReturnPercent) || 0))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 3
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'warning',
        elevation: 'high',
        sx: {}
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            p: 3,
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.SwapHoriz, {
        sx: {
            fontSize: 40,
            color: '#ff9800',
            mb: 1
        }
    }), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        color: 'textSecondary',
        gutterBottom: true
    }, 'Arbitrage Opportunities'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h4',
        color: 'warning.main',
        fontWeight: 'bold'
    }, arbitrageOpportunities.length), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: 'textSecondary',
        sx: {
            mt: 1
        }
    }, 'Active opportunities')))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 3
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'error',
        elevation: 'high',
        sx: {}
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            p: 3,
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.Security, {
        sx: {
            fontSize: 40,
            color: '#f44336',
            mb: 1
        }
    }), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        color: 'textSecondary',
        gutterBottom: true
    }, 'Risk Score'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h4',
        color: 'error.main',
        fontWeight: 'bold'
    }, ((riskMetrics === null || riskMetrics === void 0 ? void 0 : riskMetrics.totalRiskScore) || 0).toFixed(1)), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: 'textSecondary',
        sx: {
            mt: 1
        }
    }, 'Low = Better'))))));
    // Exchange status
    const renderExchangeStatus = () => /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'premium',
        elevation: 'high',
        sx: {
            mb: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        color: 'primary',
        gutterBottom: true
    }, 'Exchange Status'), /*#__PURE__*/_react.default.createElement(_material.Divider, {
        sx: {
            mb: 2,
            bgcolor: 'rgba(255,255,255,0.1)'
        }
    }), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 2
    }, ((portfolioData === null || portfolioData === void 0 ? void 0 : portfolioData.exchanges) || []).map((exchange, index) => /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 6,
        md: 4,
        key: exchange.name || `exchange-${index}`
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            p: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Avatar, null, getExchangeStatusIcon(exchange.status)), /*#__PURE__*/_react.default.createElement(_material.Box, null, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'subtitle1'
    }, exchange.name), /*#__PURE__*/_react.default.createElement(_material.Chip, {
        label: exchange.status,
        color: getExchangeChipColor(exchange.status),
        size: 'small',
        sx: {
            ml: 1
        }
    }), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: 'textSecondary'
    }, 'Value: ', formatCurrency(exchange.totalValue)), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: 'textSecondary'
    }, 'Latency: ', exchange.latency, ' ms'))))))));

    // Arbitrage opportunities
    const renderArbitrageOpportunities = () => /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'premium',
        sx: {
            mb: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        color: 'primary'
    }, 'Live Arbitrage Opportunities'), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            gap: 2,
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.FormControlLabel, {
        control: /*#__PURE__*/_react.default.createElement(_material.Switch, {
            checked: arbitrageScanning,
            onChange: e => handleArbitrageToggle(e.target.checked),
            color: 'primary'
        }),
        label: 'Enable Arbitrage'
    }), /*#__PURE__*/_react.default.createElement(_FuturisticButton.default, {
        variant: 'secondary',
        size: 'small',
        onClick: fetchPortfolioData,
        startIcon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.Refresh, null)
    }, 'Refresh'))), /*#__PURE__*/_react.default.createElement(_material.TableContainer, null, /*#__PURE__*/_react.default.createElement(_material.Table, {
        size: 'small'
    }, /*#__PURE__*/_react.default.createElement(_material.TableHead, null, /*#__PURE__*/_react.default.createElement(_material.TableRow, null, /*#__PURE__*/_react.default.createElement(_material.TableCell, null, 'Symbol'), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, 'Buy On'), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, 'Sell On'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        align: 'right'
    }, 'Profit %'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        align: 'right'
    }, 'Profit USD'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        align: 'center'
    }, 'Action'))), /*#__PURE__*/_react.default.createElement(_material.TableBody, null, arbitrageOpportunities.map((opportunity, idx) => /*#__PURE__*/_react.default.createElement(_material.TableRow, {
        key: idx
    }, /*#__PURE__*/_react.default.createElement(_material.TableCell, null, opportunity.symbol), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, opportunity.buyExchange), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, opportunity.sellExchange), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        align: 'right',
        sx: {
            color: 'success.main'
        }
    }, formatPercentage(opportunity.profitPercentage)), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        align: 'right',
        sx: {
            color: 'success.main'
        }
    }, formatCurrency(opportunity.profitUSD)), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        align: 'center'
    }, /*#__PURE__*/_react.default.createElement(_FuturisticButton.default, {
        variant: 'success',
        size: 'small',
        startIcon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.PlayArrow, null),
        onClick: async () => {
            try {
                setLoading(true);
                /** @type {{ success: boolean, actualProfit: number, error?: string } | undefined} */
                const result = await safeElectronAPI.executeArbitrage({
                    opportunity
                });
                if (result.success) {
                    showNotification(`Arbitrage executed successfully! Profit: $${result.actualProfit.toFixed(2)}`, 'success');
                } else {
                    throw new Error(result.error || 'Arbitrage execution failed');
                }
            } catch (err) {
                _logger.default.error('Error executing arbitrage:', err);
                showNotification(err instanceof Error ? err.message : 'Unknown error', 'error');
            } finally {
                setLoading(false);
                fetchPortfolioData(); // Refresh data after execution
            }
        }
    }, 'Execute')))))))));

    // Performance metrics
    const renderPerformanceMetrics = () => /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'success',
        sx: {
            mb: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        color: 'primary',
        gutterBottom: true
    }, 'Performance Metrics'), /*#__PURE__*/_react.default.createElement(_material.Divider, {
        sx: {
            mb: 2,
            bgcolor: 'rgba(255,255,255,0.1)'
        }
    }), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            mb: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'space-between',
            mb: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2'
    }, 'Daily Return'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: getStatusColor((performanceData === null || performanceData === void 0 ? void 0 : performanceData.dailyReturn) || 0),
        fontWeight: 'bold'
    }, formatPercentage((performanceData === null || performanceData === void 0 ? void 0 : performanceData.dailyReturn) || 0))), /*#__PURE__*/_react.default.createElement(_material.LinearProgress, {
        variant: 'determinate',
        value: Math.abs((performanceData === null || performanceData === void 0 ? void 0 : performanceData.dailyReturn) || 0) * 10,
        sx: {
            height: 6,
            borderRadius: 3
        }
    })), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            mb: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'space-between',
            mb: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2'
    }, 'Monthly Return'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: getStatusColor((performanceData === null || performanceData === void 0 ? void 0 : performanceData.monthlyReturn) || 0),
        fontWeight: 'bold'
    }, formatPercentage((performanceData === null || performanceData === void 0 ? void 0 : performanceData.monthlyReturn) || 0))), /*#__PURE__*/_react.default.createElement(_material.LinearProgress, {
        variant: 'determinate',
        value: Math.abs((performanceData === null || performanceData === void 0 ? void 0 : performanceData.monthlyReturn) || 0) * 10,
        sx: {
            height: 6,
            borderRadius: 3
        }
    })), /*#__PURE__*/_react.default.createElement(_material.Box, null, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'space-between',
            mb: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2'
    }, 'Win Rate'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: 'success.main',
        fontWeight: 'bold'
    }, formatPercentage((performanceData === null || performanceData === void 0 ? void 0 : performanceData.winRate) || 0))), /*#__PURE__*/_react.default.createElement(_material.LinearProgress, {
        variant: 'determinate',
        value: (performanceData === null || performanceData === void 0 ? void 0 : performanceData.winRate) || 0,
        color: 'success',
        sx: {
            height: 6,
            borderRadius: 3
        }
    }))));

    // Risk analysis
    const renderRiskAnalysis = () => /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'warning',
        sx: {
            mb: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        color: 'primary',
        gutterBottom: true
    }, 'Risk Analysis'), /*#__PURE__*/_react.default.createElement(_material.Divider, {
        sx: {
            mb: 2,
            bgcolor: 'rgba(255,255,255,0.1)'
        }
    }), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            mb: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'space-between',
            mb: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2'
    }, 'Portfolio Volatility'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: 'warning.main',
        fontWeight: 'bold'
    }, formatPercentage((riskMetrics === null || riskMetrics === void 0 ? void 0 : riskMetrics.volatility) || 0))), /*#__PURE__*/_react.default.createElement(_material.LinearProgress, {
        variant: 'determinate',
        value: ((riskMetrics === null || riskMetrics === void 0 ? void 0 : riskMetrics.volatility) || 0) * 5,
        color: 'warning',
        sx: {
            height: 6,
            borderRadius: 3
        }
    })), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            mb: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'space-between',
            mb: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2'
    }, 'Max Drawdown'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: 'error.main',
        fontWeight: 'bold'
    }, formatPercentage((riskMetrics === null || riskMetrics === void 0 ? void 0 : riskMetrics.maxDrawdown) || 0))), /*#__PURE__*/_react.default.createElement(_material.LinearProgress, {
        variant: 'determinate',
        value: Math.abs((riskMetrics === null || riskMetrics === void 0 ? void 0 : riskMetrics.maxDrawdown) || 0),
        color: 'error',
        sx: {
            height: 6,
            borderRadius: 3
        }
    })), /*#__PURE__*/_react.default.createElement(_material.Box, null, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'space-between',
            mb: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2'
    }, 'Sharpe Ratio'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: getStatusColor((riskMetrics === null || riskMetrics === void 0 ? void 0 : riskMetrics.sharpeRatio) || 0, 1),
        fontWeight: 'bold'
    }, ((riskMetrics === null || riskMetrics === void 0 ? void 0 : riskMetrics.sharpeRatio) || 0).toFixed(2))))));

    // Arbitrage stats
    const renderArbitrageStats = () => /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'premium',
        sx: {
            mb: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        color: 'primary',
        gutterBottom: true
    }, 'Arbitrage Engine Statistics'), /*#__PURE__*/_react.default.createElement(_material.Divider, {
        sx: {
            mb: 2,
            bgcolor: 'rgba(255,255,255,0.1)'
        }
    }), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 6,
        md: 3
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h4',
        color: 'primary',
        fontWeight: 'bold'
    }, (arbitrageStats === null || arbitrageStats === void 0 ? void 0 : arbitrageStats.totalOpportunities) || 0), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: 'textSecondary'
    }, 'Total Opportunities'))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 6,
        md: 3
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h4',
        color: 'success.main',
        fontWeight: 'bold'
    }, (arbitrageStats === null || arbitrageStats === void 0 ? void 0 : arbitrageStats.successfulTrades) || 0), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: 'textSecondary'
    }, 'Executed Trades'))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 6,
        md: 3
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h4',
        color: 'warning.main',
        fontWeight: 'bold'
    }, formatPercentage((arbitrageStats === null || arbitrageStats === void 0 ? void 0 : arbitrageStats.successRate) || 0)), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: 'textSecondary'
    }, 'Success Rate'))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 6,
        md: 3
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h4',
        color: 'error.main',
        fontWeight: 'bold'
    }, (arbitrageStats === null || arbitrageStats === void 0 ? void 0 : arbitrageStats.averageLatency) || 0, 'ms'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: 'textSecondary'
    }, 'Avg Latency'))))));
    if (loading && !portfolioData) {
        // Only show full-screen loader on initial load
        return /*#__PURE__*/_react.default.createElement(_material.Box, {
            sx: {
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                minHeight: '400px'
            }
        }, /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
            animate: {
                rotate: 360
            },
            transition: {
                duration: 2,
                repeat: Infinity,
                ease: 'linear'
            }
        }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.Refresh, {
            sx: {
                fontSize: 48,
                color: 'primary.main'
            }
        })));
    }
    if (error) {
        return /*#__PURE__*/_react.default.createElement(_material.Alert, {
            severity: 'error',
            sx: {
                m: 3
            }
        }, error, /*#__PURE__*/_react.default.createElement(_material.Box, {
            sx: {
                mt: 2
            }
        }, /*#__PURE__*/_react.default.createElement(_FuturisticButton.default, {
            onClick: fetchPortfolioData,
            variant: 'error'
        }, 'Retry')));
    }
    return /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            width: '100%',
            minHeight: '100vh',
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h4',
        color: 'primary',
        fontWeight: 'bold'
    }, 'Cross-Exchange Portfolio Management'), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            gap: 2,
            alignItems: 'center'
        }
    }, lastUpdate && /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'caption',
        color: 'textSecondary'
    }, 'Last updated: ', lastUpdate.toLocaleTimeString()), /*#__PURE__*/_react.default.createElement(_material.FormControlLabel, {
        control: /*#__PURE__*/_react.default.createElement(_material.Switch, {
            checked: portfolioMonitoring,
            onChange: e => handlePortfolioMonitoringToggle(e.target.checked),
            color: 'primary'
        }),
        label: 'Live Monitoring'
    }), /*#__PURE__*/_react.default.createElement(_FuturisticButton.default, {
        variant: 'primary',
        startIcon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.Refresh, null),
        onClick: fetchPortfolioData,
        disabled: loading
    }, loading ? 'Refreshing...' : 'Refresh All'), onClose && /*#__PURE__*/_react.default.createElement(_FuturisticButton.default, {
        variant: 'ghost',
        onClick: onClose
    }, 'Close'))), renderPortfolioOverview(), renderExchangeStatus(), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            mb: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Tabs, {
        value: activeTab,
        onChange: (event, newValue) => setActiveTab(newValue),
        sx: {
            '& .MuiTab-root': {
                color: 'rgba(255,255,255,0.7)'
            },
            '& .Mui-selected': {
                color: '#00eaff'
            },
            '& .MuiTabs-indicator': {
                backgroundColor: '#00eaff'
            }
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Tab, {
        label: 'Arbitrage Opportunities'
    }), /*#__PURE__*/_react.default.createElement(_material.Tab, {
        label: 'Performance & Risk'
    }), /*#__PURE__*/_react.default.createElement(_material.Tab, {
        label: 'Statistics'
    }))), /*#__PURE__*/_react.default.createElement(_framerMotion.AnimatePresence, {
        mode: 'wait'
    }, /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        key: activeTab,
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        exit: {
            opacity: 0,
            y: -20
        },
        transition: {
            duration: 0.3
        }
    }, activeTab === 0 && renderArbitrageOpportunities(), activeTab === 1 && /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6
    }, renderPerformanceMetrics()), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6
    }, renderRiskAnalysis())), activeTab === 2 && renderArbitrageStats())));
}

CrossExchangePortfolio.propTypes = {
    onClose: _propTypes.default.func
};
const _default = exports.default = CrossExchangePortfolio;
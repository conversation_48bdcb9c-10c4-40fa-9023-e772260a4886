 
'use strict';

Object.defineProperty(exports, '__esModule', {
    value: true
});
exports.default = void 0;
const _react = _interopRequireWildcard(require('react'));
const _propTypes = _interopRequireDefault(require('prop-types'));
const _material = require('@mui/material');
const _iconsMaterial = require('@mui/icons-material');
const _recharts = require('recharts');
const _framerMotion = require('framer-motion');
const _VibrantButton = _interopRequireDefault(require('./VibrantButton'));
const _ParticleBackground = _interopRequireDefault(require('./ParticleBackground'));
const _HolographicCard = _interopRequireDefault(require('./HolographicCard'));
const _StartButton = _interopRequireDefault(require('./StartButton'));
const _SystemStatusPanel = _interopRequireDefault(require('./SystemStatusPanel'));
const _StartupProgressPanel = _interopRequireDefault(require('./StartupProgressPanel'));
const _TradingStatusIndicator = _interopRequireDefault(require('./TradingStatusIndicator'));
const _ConnectionStatus = _interopRequireDefault(require('./ConnectionStatus'));
const _logger = _interopRequireDefault(require('../utils/logger'));

function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}

function _interopRequireWildcard(e, t) {
    if ('function' == typeof WeakMap) var r = new WeakMap(),
        n = new WeakMap();
    return (_interopRequireWildcard = function (e, t) {
        if (!t && e && e.__esModule) return e;
        let o,
            i,
            f = {
                __proto__: null,
                default: e
            };
        if (null === e || 'object' != typeof e && 'function' != typeof e) return f;
        if (o = t ? n : r) {
            if (o.has(e)) return o.get(e);
            o.set(e, f);
        }
        for (const t in e) 'default' !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]);
        return f;
    })(e, t);
}

/**
 * @fileoverview Main Trading Dashboard Component
 * @description Comprehensive trading dashboard providing real-time portfolio overview,
 * trading statistics, market data visualization, and system monitoring.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * @typedef {Object} ElectronAPI
 * @property {() => Promise<{success: boolean, data: any}>} getPortfolioSummary
 * @property {() => Promise<{success: boolean, data: any}>} getTradingStats
 * @property {() => Promise<{success: boolean, data: any}>} getMarketOverview
 * @property {() => Promise<{success: boolean, data: any}>} getRiskMetrics
 * @property {() => Promise<{success: boolean, data: any[]}>} getPriceHistory
 * @property {() => Promise<{success: boolean, data: any[]}>} getAssetAllocation
 * @property {() => Promise<{success: boolean, data: any[]}>} getTradeHistory
 * @property {() => Promise<{success: boolean, data: any[]}>} getActiveBots
 * @property {() => Promise<{success: boolean, data: any}>} getSystemHealth
 * @property {() => Promise<{success: boolean, data: any}>} getSystemMetrics
 * @property {() => Promise<{success: boolean, data: any[]}>} getSystemAlerts
 * @property {(callback: (event: any, ...args: any[]) => void) => () => void} subscribeToSystemNotifications
 */

const api = typeof window !== 'undefined' && /** @type {*} */window.electronAPI || {
    getPortfolioSummary: () => Promise.resolve({
        success: true,
        data: {
            totalValue: 0,
            dailyChange: 0,
            totalReturn: 0
        }
    }),
    getTradingStats: () => Promise.resolve({
        success: true,
        data: {
            totalTrades: 0,
            winRate: 0,
            profitLoss: 0,
            activeTrades: 0
        }
    }),
    getMarketOverview: () => Promise.resolve({
        success: true,
        data: {
            sentiment: 'neutral',
            volatility: 0,
            volume: 0
        }
    }),
    getRiskMetrics: () => Promise.resolve({
        success: true,
        data: {
            score: 0,
            maxDrawdown: 0,
            sharpeRatio: 0
        }
    }),
    getPriceHistory: () => Promise.resolve({
        success: true,
        data: []
    }),
    getAssetAllocation: () => Promise.resolve({
        success: true,
        data: []
    }),
    getTradeHistory: () => Promise.resolve({
        success: true,
        data: []
    }),
    getActiveBots: () => Promise.resolve({
        success: true,
        data: []
    }),
    getSystemHealth: () => Promise.resolve({
        success: true,
        data: {
            status: 'healthy',
            uptime: 0,
            cpu: 0,
            memory: 0
        }
    }),
    getSystemMetrics: () => Promise.resolve({
        success: true,
        data: {
            cpu: 0,
            memory: 0,
            disk: 0,
            network: 0
        }
    }),
    getSystemAlerts: () => Promise.resolve({
        success: true,
        data: []
    }),
    subscribeToSystemNotifications: () => () => {
    }
};

/**
 * DashboardHeader - Header component for the dashboard
 */
const DashboardHeaderComponent = ({
                                      theme,
                                      fetchDashboardData,
                                      isLoading,
                                      onStartTrading
                                  }) => /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
    initial: {
        opacity: 0,
        y: -20
    },
    animate: {
        opacity: 1,
        y: 0
    },
    transition: {
        duration: 0.5
    }
}, /*#__PURE__*/_react.default.createElement(_material.Box, {
    sx: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 4
    }
}, /*#__PURE__*/_react.default.createElement(_material.Typography, {
    variant: 'h4',
    sx: {
        fontWeight: 900,
        color: theme.palette.primary.main,
        textShadow: `0 0 20px ${theme.palette.primary.main}40`
    }
}, 'TRADING COMMAND CENTER'), /*#__PURE__*/_react.default.createElement(_material.Box, {
    sx: {
        display: 'flex',
        gap: 2
    }
}, /*#__PURE__*/_react.default.createElement(_StartButton.default, {
    onStatusChange: onStartTrading,
    showNotification: (message, type) => {
        _logger.default.info(`${(type === null || type === void 0 ? void 0 : type.toUpperCase()) || 'INFO'}: ${message}`);
    }
}), /*#__PURE__*/_react.default.createElement(_VibrantButton.default, {
    onClick: fetchDashboardData,
    startIcon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.Refresh, null),
    disabled: isLoading
}, 'Refresh'))));
DashboardHeaderComponent.displayName = 'DashboardHeader';
DashboardHeaderComponent.propTypes = {
    theme: _propTypes.default.object.isRequired,
    fetchDashboardData: _propTypes.default.func.isRequired,
    isLoading: _propTypes.default.bool.isRequired,
    onStartTrading: _propTypes.default.func
};
const DashboardHeader = _react.default.memo(DashboardHeaderComponent);

/**
 * MetricCard - Reusable metric card component
 */
const MetricCard = ({
                        title,
                        value,
                        subtitle,
                        icon: Icon,
                        color,
                        variant = 'default',
                        change
                    }) => /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
    variant: variant,
    elevation: 'medium',
    sx: {
        height: '100%'
    }
}, /*#__PURE__*/_react.default.createElement(_material.CardContent, {
    sx: {
        p: 3,
        textAlign: 'center'
    }
}, /*#__PURE__*/_react.default.createElement(_material.Box, {
    sx: {
        display: 'flex',
        justifyContent: 'center',
        mb: 2
    }
}, /*#__PURE__*/_react.default.createElement(Icon, {
    sx: {
        fontSize: 48,
        color: color || 'primary.main'
    }
})), /*#__PURE__*/_react.default.createElement(_material.Typography, {
    variant: 'h6',
    sx: {
        color: '#888',
        mb: 1
    }
}, title), /*#__PURE__*/_react.default.createElement(_material.Typography, {
    variant: 'h4',
    sx: {
        color: color || 'primary.main',
        fontWeight: 800,
        mb: 1
    }
}, value), subtitle && /*#__PURE__*/_react.default.createElement(_material.Typography, {
    variant: 'body2',
    sx: {
        color: '#666'
    }
}, subtitle), change && /*#__PURE__*/_react.default.createElement(_material.Box, {
    sx: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        mt: 1
    }
}, change > 0 ? /*#__PURE__*/_react.default.createElement(_iconsMaterial.TrendingUp, {
    sx: {
        color: '#4caf50',
        mr: 0.5
    }
}) : /*#__PURE__*/_react.default.createElement(_iconsMaterial.TrendingDown, {
    sx: {
        color: '#f44336',
        mr: 0.5
    }
}), /*#__PURE__*/_react.default.createElement(_material.Typography, {
    variant: 'body2',
    sx: {
        color: change > 0 ? '#4caf50' : '#f44336',
        fontWeight: 600
    }
}, change > 0 ? '+' : '', change.toFixed(2), '%'))));
MetricCard.propTypes = {
    title: _propTypes.default.string.isRequired,
    value: _propTypes.default.string.isRequired,
    subtitle: _propTypes.default.string,
    icon: _propTypes.default.elementType.isRequired,
    color: _propTypes.default.string,
    variant: _propTypes.default.string,
    change: _propTypes.default.number
};

/**
 * Main Trading Dashboard Component
 *
 * @description Comprehensive trading dashboard providing real-time portfolio overview,
 * trading statistics, market data visualization, and system monitoring. Features include
 * portfolio tracking, trade history, market sentiment analysis, and risk metrics.
 *
 * @component
 * @param {Object} props - Component props.
 * @param {(message: string, type: 'success'|'error'|'warning'|'info') => void} props.showNotification - Function to display notifications.
 *
 * @returns {React.ReactElement} The rendered dashboard component
 *
 * @example
 * // Basic usage
 * <Dashboard showNotification={(msg, type) => logger.info(msg)} />
 *
 * @example
 * // With toast notifications
 * <Dashboard
 *   showNotification={(message, type) => toast[type](message)}
 * />
 *
 * @since 1.0.0
 * <AUTHOR> Team
 */
const Dashboard = ({
                       showNotification = (message, severity) => {
                           _logger.default.info(`${(severity === null || severity === void 0 ? void 0 : severity.toUpperCase()) || 'INFO'}: ${message}`);
                       }
                   }) => {
    const theme = (0, _material.useTheme)();
    const [isLoading, setIsLoading] = (0, _react.useState)(true);
    const [dashboardData, setDashboardData] = (0, _react.useState)({
        portfolio: {
            totalValue: 0,
            dailyChange: 0,
            totalReturn: 0
        },
        trading: {
            totalTrades: 0,
            winRate: 0,
            profitLoss: 0,
            activeTrades: 0
        },
        market: {
            sentiment: 'neutral',
            volatility: 0,
            volume: 0
        },
        risk: {
            score: 0,
            maxDrawdown: 0,
            sharpeRatio: 0
        },
        assets: [],
        priceHistory: [],
        trades: [],
        bots: [],
        systemHealth: {
            status: 'healthy',
            uptime: 0,
            cpu: 0,
            memory: 0
        },
        systemMetrics: {
            cpu: 0,
            memory: 0,
            disk: 0,
            network: 0
        },
        systemAlerts: []
    });
    const formatCurrency = value => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(value || 0);
    };
    const formatPercentage = value => {
        const sign = (value || 0) > 0 ? '+' : '';
        return `${sign}${(value || 0).toFixed(2)}%`;
    };
    const fetchDashboardData = (0, _react.useCallback)(async () => {
        try {
            setIsLoading(true);
            _logger.default.info('Fetching dashboard data...');
            const [portfolioSummary, tradingStats, marketOverview, riskMetrics, priceHistory, assetAllocation, tradeHistory, activeBots, systemHealth, systemMetrics, systemAlerts] = await Promise.all([api.getPortfolioSummary(), api.getTradingStats(), api.getMarketOverview(), api.getRiskMetrics(), api.getPriceHistory(), api.getAssetAllocation(), api.getTradeHistory(), api.getActiveBots(), api.getSystemHealth(), api.getSystemMetrics(), api.getSystemAlerts()]);
            _logger.default.info('Dashboard data fetched successfully:', {
                portfolioSummary,
                tradingStats,
                marketOverview,
                riskMetrics,
                priceHistory,
                assetAllocation,
                tradeHistory,
                activeBots,
                systemHealth
            });
            setDashboardData({
                portfolio: portfolioSummary.success ? portfolioSummary.data : {
                    totalValue: 0,
                    dailyChange: 0,
                    totalReturn: 0
                },
                trading: tradingStats.success ? tradingStats.data : {
                    totalTrades: 0,
                    winRate: 0,
                    profitLoss: 0,
                    activeTrades: 0
                },
                market: marketOverview.success ? marketOverview.data : {
                    sentiment: 'neutral',
                    volatility: 0,
                    volume: 0
                },
                risk: riskMetrics.success ? riskMetrics.data : {
                    score: 0,
                    maxDrawdown: 0,
                    sharpeRatio: 0
                },
                priceHistory: priceHistory.success ? priceHistory.data : [],
                assets: assetAllocation.success ? assetAllocation.data : [],
                trades: tradeHistory.success ? tradeHistory.data : [],
                bots: activeBots.success ? activeBots.data : [],
                systemHealth: systemHealth.success ? systemHealth.data : {
                    status: 'healthy',
                    uptime: 0,
                    cpu: 0,
                    memory: 0
                },
                systemMetrics: systemMetrics.success ? systemMetrics.data : {
                    cpu: 0,
                    memory: 0,
                    disk: 0,
                    network: 0
                },
                systemAlerts: systemAlerts.success ? systemAlerts.data : []
            });
        } catch (error) {
            _logger.default.error('Dashboard fetch error:', error);
            if (showNotification) {
                showNotification('Failed to fetch dashboard data', 'error');
            }
        } finally {
            setIsLoading(false);
        }
    }, [showNotification]);
    (0, _react.useEffect)(() => {
        fetchDashboardData();

        // Set up real-time updates
        const interval = setInterval(fetchDashboardData, 30000); // Update every 30 seconds

        return () => clearInterval(interval);
    }, [fetchDashboardData]);
    const chartColors = {
        primary: '#00eaff',
        secondary: '#a259ff',
        success: '#4caf50',
        warning: '#ff9800',
        error: '#f44336'
    };
    const handleTradingStatusChange = status => {
        _logger.default.info('Trading system status:', status);
        fetchDashboardData();
    };
    return /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            position: 'relative',
            minHeight: '100vh',
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_ParticleBackground.default, {
        tradingActive: dashboardData.trading.activeTrades > 0
    }), /*#__PURE__*/_react.default.createElement(DashboardHeader, {
        theme: theme,
        fetchDashboardData: fetchDashboardData,
        isLoading: isLoading,
        onStartTrading: handleTradingStatusChange
    }), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'flex-end',
            mb: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_ConnectionStatus.default, {
        refreshInterval: 3000
    })), isLoading && /*#__PURE__*/_react.default.createElement(_material.LinearProgress, {
        sx: {
            mb: 2,
            backgroundColor: 'rgba(0,234,255,0.1)',
            '& .MuiLinearProgress-bar': {
                backgroundColor: chartColors.primary
            }
        }
    }), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6,
        lg: 3
    }, /*#__PURE__*/_react.default.createElement(MetricCard, {
        title: 'Portfolio Value',
        value: formatCurrency(dashboardData.portfolio.totalValue),
        subtitle: 'Total Assets',
        icon: _iconsMaterial.AccountBalance,
        color: chartColors.primary,
        variant: 'quantum',
        change: dashboardData.portfolio.dailyChange
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6,
        lg: 3
    }, /*#__PURE__*/_react.default.createElement(MetricCard, {
        title: 'Win Rate',
        value: `${dashboardData.trading.winRate}%`,
        subtitle: `${dashboardData.trading.totalTrades} total trades`,
        icon: _iconsMaterial.Assessment,
        color: chartColors.secondary,
        variant: 'premium',
        change: null
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6,
        lg: 3
    }, /*#__PURE__*/_react.default.createElement(MetricCard, {
        title: 'Active Trades',
        value: (dashboardData.trading.activeTrades || 0).toString(),
        subtitle: 'Currently running',
        icon: _iconsMaterial.ShowChart,
        color: chartColors.success,
        variant: dashboardData.trading.profitLoss >= 0 ? 'success' : 'error',
        change: null
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6,
        lg: 3
    }, /*#__PURE__*/_react.default.createElement(MetricCard, {
        title: 'Risk Score',
        value: dashboardData.risk.score.toFixed(1),
        subtitle: 'Out of 10',
        icon: _iconsMaterial.Security,
        color: dashboardData.risk.score > 7 ? chartColors.error : chartColors.warning,
        variant: dashboardData.risk.score > 7 ? 'error' : 'warning',
        change: null
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6,
        lg: 3
    }, /*#__PURE__*/_react.default.createElement(MetricCard, {
        title: 'System Status',
        value: dashboardData.systemHealth.status,
        subtitle: `Uptime: ${Math.round(dashboardData.systemHealth.uptime)}s`,
        icon: dashboardData.systemHealth.status === 'healthy' ? _iconsMaterial.CheckCircle : _iconsMaterial.Warning,
        color: dashboardData.systemHealth.status === 'healthy' ? '#4caf50' : '#ff9800',
        variant: dashboardData.systemHealth.status === 'healthy' ? 'success' : 'warning',
        change: null
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6,
        lg: 3
    }, /*#__PURE__*/_react.default.createElement(MetricCard, {
        title: 'CPU Usage',
        value: `${dashboardData.systemMetrics.cpu || 0}%`,
        subtitle: 'Current Load',
        icon: _iconsMaterial.Speed,
        color: dashboardData.systemMetrics.cpu > 80 ? '#f44336' : '#00eaff',
        variant: dashboardData.systemMetrics.cpu > 80 ? 'error' : 'default',
        change: null
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6,
        lg: 3
    }, /*#__PURE__*/_react.default.createElement(MetricCard, {
        title: 'Memory Usage',
        value: `${dashboardData.systemMetrics.memory || 0}%`,
        subtitle: 'System Memory',
        icon: _iconsMaterial.Memory,
        color: dashboardData.systemMetrics.memory > 85 ? '#f44336' : '#a259ff',
        variant: dashboardData.systemMetrics.memory > 85 ? 'error' : 'premium',
        change: null
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6,
        lg: 3
    }, /*#__PURE__*/_react.default.createElement(MetricCard, {
        title: 'Disk Usage',
        value: `${dashboardData.systemMetrics.disk || 0}%`,
        subtitle: 'Storage Space',
        icon: _iconsMaterial.Storage,
        color: dashboardData.systemMetrics.disk > 90 ? '#f44336' : '#ff9800',
        variant: dashboardData.systemMetrics.disk > 90 ? 'error' : 'warning',
        change: null
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        lg: 8
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'default',
        elevation: 'high',
        sx: {
            height: '100%'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.CardContent, {
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: chartColors.primary,
            mb: 2,
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.Timeline, {
        sx: {
            mr: 1
        }
    }), 'Portfolio Performance'), /*#__PURE__*/_react.default.createElement(_recharts.ResponsiveContainer, {
        width: '100%',
        height: 300
    }, /*#__PURE__*/_react.default.createElement(_recharts.AreaChart, {
        data: dashboardData.priceHistory
    }, /*#__PURE__*/_react.default.createElement('defs', null, /*#__PURE__*/_react.default.createElement('linearGradient', {
        id: 'portfolioGradient',
        x1: '0',
        y1: '0',
        x2: '0',
        y2: '1'
    }, /*#__PURE__*/_react.default.createElement('stop', {
        offset: '5%',
        stopColor: chartColors.primary,
        stopOpacity: 0.8
    }), /*#__PURE__*/_react.default.createElement('stop', {
        offset: '95%',
        stopColor: chartColors.primary,
        stopOpacity: 0
    }))), /*#__PURE__*/_react.default.createElement(_recharts.CartesianGrid, {
        strokeDasharray: '3 3',
        stroke: 'rgba(255,255,255,0.1)'
    }), /*#__PURE__*/_react.default.createElement(_recharts.XAxis, {
        dataKey: 'date',
        stroke: '#888'
    }), /*#__PURE__*/_react.default.createElement(_recharts.YAxis, {
        stroke: '#888'
    }), /*#__PURE__*/_react.default.createElement(_recharts.Tooltip, {
        contentStyle: {
            backgroundColor: 'rgba(24,26,32,0.95)',
            border: `1px solid ${chartColors.primary}`,
            borderRadius: '8px',
            color: '#fff'
        },
        formatter: value => [formatCurrency(value), 'Portfolio Value']
    }), /*#__PURE__*/_react.default.createElement(_recharts.Area, {
        type: 'monotone',
        dataKey: 'value',
        stroke: chartColors.primary,
        fill: 'url(#portfolioGradient)',
        strokeWidth: 2
    })))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        lg: 4
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'premium',
        elevation: 'high',
        sx: {
            height: '100%'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.CardContent, {
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: chartColors.secondary,
            mb: 2,
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.Assessment, {
        sx: {
            mr: 1
        }
    }), 'Market Overview'), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            mb: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888'
        }
    }, 'Market Sentiment'), /*#__PURE__*/_react.default.createElement(_material.Chip, {
        label: dashboardData.market.sentiment,
        color: dashboardData.market.sentiment === 'bullish' ? 'success' : 'error',
        size: 'small',
        sx: {
            textTransform: 'uppercase',
            fontWeight: 600,
            borderRadius: '16px',
            height: 24,
            '& .MuiChip-label': {
                px: 1.5,
                py: 0.5
            }
        }
    })), /*#__PURE__*/_react.default.createElement(_material.Divider, {
        sx: {
            borderColor: 'rgba(255,255,255,0.1)',
            mb: 1
        }
    }), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888'
        }
    }, '24h Volatility'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#fff',
            fontWeight: 500
        }
    }, formatPercentage(dashboardData.market.volatility)))), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'subtitle2',
        sx: {
            color: '#aaa',
            mb: 1
        }
    }, 'Top Assets by Volume'), /*#__PURE__*/_react.default.createElement(_material.TableContainer, {
        component: _material.Paper,
        sx: {
            borderRadius: 2,
            overflow: 'hidden'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Table, {
        size: 'small'
    }, /*#__PURE__*/_react.default.createElement(_material.TableHead, null, /*#__PURE__*/_react.default.createElement(_material.TableRow, null, /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#888',
            borderBottom: 'none'
        }
    }, 'Market'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#888',
            borderBottom: 'none'
        },
        align: 'right'
    }, '24h Volume'))), /*#__PURE__*/_react.default.createElement(_material.TableBody, null, dashboardData.assets.slice(0, 3).map(asset => /*#__PURE__*/_react.default.createElement(_material.TableRow, {
        key: asset.symbol
    }, /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#fff',
            borderBottom: 'none'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            alignItems: 'center',
            gap: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Avatar, {
        sx: {
            width: 24,
            height: 24,
            bgcolor: asset.color
        }
    }, asset.symbol.charAt(0)), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            fontWeight: 500
        }
    }, asset.symbol))), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#fff',
            borderBottom: 'none'
        },
        align: 'right'
    }, formatCurrency(asset.value)))))))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        lg: 8
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'default',
        elevation: 'high',
        sx: {
            height: '100%'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.CardContent, {
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: chartColors.secondary,
            mb: 2,
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.AutoGraph, {
        sx: {
            mr: 1
        }
    }), 'Trading Activity'), /*#__PURE__*/_react.default.createElement(_recharts.ResponsiveContainer, {
        width: '100%',
        height: 300
    }, /*#__PURE__*/_react.default.createElement(_recharts.LineChart, {
        data: dashboardData.trades
    }, /*#__PURE__*/_react.default.createElement(_recharts.CartesianGrid, {
        strokeDasharray: '3 3',
        stroke: 'rgba(255,255,255,0.1)'
    }), /*#__PURE__*/_react.default.createElement(_recharts.XAxis, {
        dataKey: 'time',
        stroke: '#888'
    }), /*#__PURE__*/_react.default.createElement(_recharts.YAxis, {
        stroke: '#888'
    }), /*#__PURE__*/_react.default.createElement(_recharts.Tooltip, {
        contentStyle: {
            backgroundColor: 'rgba(24,26,32,0.95)',
            border: `1px solid ${chartColors.primary}`,
            borderRadius: '8px',
            color: '#fff'
        },
        formatter: value => [formatCurrency(value), 'Trade Value']
    }), /*#__PURE__*/_react.default.createElement(_recharts.Line, {
        type: 'monotone',
        dataKey: 'price',
        stroke: chartColors.primary,
        strokeWidth: 2,
        dot: false
    })))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        lg: 4
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'default',
        elevation: 'high',
        sx: {
            height: '100%'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.CardContent, {
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: chartColors.primary,
            mb: 2,
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.PieChart, {
        sx: {
            mr: 1
        }
    }), 'Asset Allocation'), /*#__PURE__*/_react.default.createElement(_recharts.ResponsiveContainer, {
        width: '100%',
        height: 300
    }, /*#__PURE__*/_react.default.createElement(_recharts.PieChart, null, /*#__PURE__*/_react.default.createElement(_recharts.Pie, {
        data: dashboardData.assets,
        dataKey: 'allocation',
        nameKey: 'symbol',
        cx: '50%',
        cy: '50%',
        outerRadius: 80
    }, dashboardData.assets.map(asset => /*#__PURE__*/_react.default.createElement(_recharts.Cell, {
        key: asset.symbol,
        fill: asset.color
    }))), /*#__PURE__*/_react.default.createElement(_recharts.Tooltip, {
        contentStyle: {
            backgroundColor: 'rgba(24,26,32,0.95)',
            border: `1px solid ${chartColors.primary}`,
            borderRadius: '8px',
            color: '#fff'
        },
        formatter: (value, name, entry) => {
            const total = entry.payload.value;
            return [`${formatCurrency(total)} (${value}%)`, name];
        }
    })))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        lg: 6
    }, /*#__PURE__*/_react.default.createElement(_TradingStatusIndicator.default, null)), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        lg: 6
    }, /*#__PURE__*/_react.default.createElement(_SystemStatusPanel.default, {
        refreshInterval: 5000
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        lg: 6
    }, /*#__PURE__*/_react.default.createElement(_StartupProgressPanel.default, {
        onProgressUpdate: progress => {
            if (progress >= 100) {
                _logger.default.info('Startup completed, refreshing dashboard data');
                fetchDashboardData();
            }
        }
    }))));
};
Dashboard.propTypes = {
    showNotification: _propTypes.default.func
};
const _default = exports.default = Dashboard;
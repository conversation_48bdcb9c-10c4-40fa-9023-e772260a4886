// Import logger for consistent logging
import logger from '../utils/logger';

import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Fade,
  IconButton,
  LinearProgress,
  Step,
  StepLabel,
  Stepper,
  Typography
} from '@mui/material';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import PowerSettingsNewIcon from '@mui/icons-material/PowerSettingsNew';
import PowerOffIcon from '@mui/icons-material/PowerOff';
import SyncIcon from '@mui/icons-material/Sync';
import CloseIcon from '@mui/icons-material/Close';
import ipcService from '../services/ipcService';
import StartupService from '../services/startupService';

const EnhancedStartButton = ({onStatusChange, showNotification}) => {
    const [isStarting, setIsStarting] = useState(false);
    const [isRunning, setIsRunning] = useState(false);
    const [startupStatus, setStartupStatus] = useState(null);
    const [startupProgress, setStartupProgress] = useState(0);
    const [currentStep, setCurrentStep] = useState(0);
    const [error, setError] = useState(null);
    const [systemInfo, setSystemInfo] = useState(null);
    const [startupSteps, setStartupSteps] = useState([]);
    const [showStartupDialog, setShowStartupDialog] = useState(false);
    const [_stepStatus, setStepStatus] = useState({});
    const startupService = useRef(new StartupService(ipcService));

    const checkBotStatus = useCallback(async () => {
        try {
            const response = await ipcService.getBotStatus();
            if (response.success) {
                setIsRunning(response.data.isRunning);
                if (onStatusChange) onStatusChange(response.data);
            }
        } catch (error) {
            logger.error('Failed to check bot status:', error);
            setError('Failed to check system status');
        }
    }, [onStatusChange]);

    const handleSystemNotification = useCallback((_event, data) => {
        switch (data.type) {
            case 'startup-status':
                setStartupStatus(data.message);
                break;
            case 'startup-error':
                setError(data.message);
                setIsStarting(false);
                break;
            case 'startup-complete':
                setIsRunning(true);
                setIsStarting(false);
                setShowStartupDialog(false);
                if (showNotification) showNotification('Trading system started successfully', 'success');
                break;
            default:
                break;
        }
    }, [showNotification]);

    useEffect(() => {
        const loadStartupSteps = () => {
            const phases = startupService.current.getStartupPhases();
            const steps = phases.flatMap(phase =>
                phase.components.map(component => ({
                    id: component.toLowerCase().replace(/\s+/g, '-'),
                    name: component,
                    phase: phase.name
                })),
            );
            setStartupSteps(steps);
            setStepStatus({});
        };

        checkBotStatus();
        getSystemInfo();
        loadStartupSteps();
        const unsubscribe = ipcService.on('system-notification', handleSystemNotification);
        const startupUnsubscribe = startupService.current.addStartupListener(handleStartupEvents);
        return () => {
            if (unsubscribe) unsubscribe();
            if (startupUnsubscribe) startupUnsubscribe();
        };
    }, [checkBotStatus, handleSystemNotification]);

    const getSystemInfo = async () => {
        try {
            const response = await ipcService.getSystemInfo();
            if (response.success) {
                setSystemInfo(response.data);
            }
        } catch (error) {
            logger.error('Failed to get system info:', error);
        }
    };

    const handleStartupEvents = (event) => {
        switch (event.type) {
            case 'startup-progress':
                setStartupStatus(event.message);
                setStartupProgress(event.progress);
                setCurrentStep(event.currentStep);
                break;
            case 'step-complete':
                setStepStatus(prev => ({
                    ...prev,
                    [event.stepId]: 'completed'
                }));
                break;
            case 'step-error':
                setStepStatus(prev => ({
                    ...prev,
                    [event.stepId]: 'error'
                }));
                break;
        }
    };

    const handleStart = async () => {
        setIsStarting(true);
        setError(null);
        setShowStartupDialog(true);
        setStartupStatus('Starting trading system...');

        try {
            await startupService.current.startSystemWorkflow();
            await checkBotStatus();
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to start trading system';
            setError(errorMessage);
            if (showNotification) showNotification(`Failed to start trading system: ${errorMessage}`, 'error');
            setIsStarting(false);
        }
    };

    const handleStop = async () => {
        setIsStarting(true);
        setError(null);
        setStartupStatus('Stopping trading system...');

        try {
            const response = await ipcService.stopBot();
            if (response.success) {
                setIsRunning(false);
                setStartupStatus('Trading system stopped');
                setStartupProgress(0);
                setCurrentStep(0);
                if (showNotification) showNotification('Trading system stopped successfully', 'success');
                await checkBotStatus();
            } else {
                throw new Error(String(response.error) || 'Failed to stop trading system');
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to stop trading system';
            setError(errorMessage);
            if (showNotification) showNotification(`Failed to stop trading system: ${errorMessage}`, 'error');
        } finally {
            setIsStarting(false);
        }
    };

    const handleRestart = async () => {
        setError(null);
        setStartupStatus('Restarting trading system...');

        try {
            await handleStop();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await handleStart();
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to restart trading system';
            setError(errorMessage);
            if (showNotification) showNotification(`Failed to restart trading system: ${errorMessage}`, 'error');
        }
    };

    const getButtonColor = () => {
        if (isStarting) return '#ff9800';
        return isRunning ? '#f44336' : '#4caf50';
    };

    const getButtonHoverColor = () => {
        if (isStarting) return '#f57c00';
        return isRunning ? '#d32f2f' : '#45a049';
    };

    const getButtonText = () => {
        if (isStarting) return 'Processing...';
        return isRunning ? 'Stop Trading' : 'Start Trading';
    };

    const getButtonIcon = () => {
        if (isStarting) return <SyncIcon sx={{animation: 'spin 2s linear infinite'}}/>;
        return isRunning ? <PowerOffIcon/> : <PowerSettingsNewIcon/>;
    };

    const getStatusChip = () => {
        if (isStarting) {
            return <Chip label="Starting..." color="warning" size="small"/>;
        }
        if (isRunning) {
            return <Chip label="Running" color="success" size="small"/>;
        }
        return <Chip label="Stopped" color="default" size="small"/>;
    };

    return (
        <Box sx={{display: 'flex', flexDirection: 'column', gap: 3, alignItems: 'center', width: '100%'}}>
            <Box sx={{display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2}}>
                <Button
                    variant="contained"
                    size="large"
                    onClick={isRunning ? handleStop : handleStart}
                    disabled={isStarting}
                    startIcon={getButtonIcon()}
                    sx={{
                        minWidth: 250,
                        backgroundColor: getButtonColor(),
                        '&:hover': {
                            backgroundColor: getButtonHoverColor()
                        },
                        fontWeight: 'bold',
                        fontSize: '1.2rem',
                        padding: '14px 28px',
                        borderRadius: '12px',
                        textTransform: 'none',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                        transition: 'all 0.3s ease'
                    }}
                >
                    {getButtonText()}
                </Button>

                <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                    {getStatusChip()}
                    {systemInfo && (
                        <Typography variant="caption" color="textSecondary">
                            v{systemInfo.version}
                        </Typography>
                    )}
                </Box>
            </Box>

            {isStarting && (
                <Fade in={isStarting} timeout={500}>
                    <Box sx={{width: '100%', maxWidth: 400}}>
                        <Typography variant="body2" color="textSecondary" align="center">
                            {startupStatus || 'Initializing...'}
                        </Typography>
                    </Box>
                </Fade>
            )}

            {isRunning && !isStarting && (
                <Fade in={!isStarting} timeout={500}>
                    <Button
                        variant="outlined"
                        size="medium"
                        onClick={handleRestart}
                        disabled={isStarting}
                        startIcon={<RestartAltIcon/>}
                        sx={{
                            minWidth: 140,
                            borderColor: '#ff9800',
                            color: '#ff9800',
                            '&:hover': {
                                borderColor: '#f57c00',
                                backgroundColor: 'rgba(255, 152, 0, 0.1)'
                            }
                        }}
                    >
                        Restart
                    </Button>
                </Fade>
            )}

            {error && (
                <Fade in={!!error} timeout={300}>
                    <Alert severity="error" sx={{mt: 1, maxWidth: 400}}>
                        {error}
                    </Alert>
                </Fade>
            )}

            <Dialog
                open={showStartupDialog}
                onClose={() => setShowStartupDialog(false)}
                maxWidth="sm"
                fullWidth
            >
                <DialogTitle>
                    <Box sx={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
                        <Typography variant="h6">Starting Trading System</Typography>
                        <IconButton onClick={() => setShowStartupDialog(false)}>
                            <CloseIcon/>
                        </IconButton>
                    </Box>
                </DialogTitle>

                <DialogContent>
                    <Stepper activeStep={currentStep} orientation="vertical">
                        {startupSteps.map((step, _index) => (
                            <Step key={step.id}>
                                <StepLabel>{step.name}</StepLabel>
                            </Step>
                        ))}
                    </Stepper>

                    <Box sx={{mt: 3}}>
                        <LinearProgress
                            variant="determinate"
                            value={startupProgress}
                            sx={{height: 8, borderRadius: 4, mb: 2}}
                        />

                        <Typography variant="body2" color="textSecondary" align="center">
                            {startupStatus || 'Initializing...'}
                        </Typography>

                        <Typography variant="caption" color="textSecondary" align="center" display="block">
                            Step {currentStep + 1} of {startupSteps.length}
                        </Typography>
                    </Box>
                </DialogContent>

                <DialogActions>
                    <Button onClick={() => setShowStartupDialog(false)} color="primary">
                        Close
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
};

export default EnhancedStartButton;

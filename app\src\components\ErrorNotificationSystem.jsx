/* eslint-disable */
'use strict';

Object.defineProperty(exports, '__esModule', {
    value: true
});
exports.default = void 0;
const _react = _interopRequireWildcard(require('react'));
const _GlobalErrorHandler = _interopRequireDefault(require('../utils/GlobalErrorHandler'));
const _logger = _interopRequireDefault(require('../utils/logger'));

function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}

function _interopRequireWildcard(e, t) {
    if ('function' == typeof WeakMap) var r = new WeakMap(),
        n = new WeakMap();
    return (_interopRequireWildcard = function (e, t) {
        if (!t && e && e.__esModule) return e;
        let o,
            i,
            f = {
                __proto__: null,
                default: e
            };
        if (null === e || 'object' != typeof e && 'function' != typeof e) return f;
        if (o = t ? n : r) {
            if (o.has(e)) return o.get(e);
            o.set(e, f);
        }
        for (const t in e) 'default' !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]);
        return f;
    })(e, t);
}

function ownKeys(e, r) {
    const t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        let o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function (r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}

function _objectSpread(e) {
    for (let r = 1; r < arguments.length; r++) {
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}

function _defineProperty(e, r, t) {
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value: t,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) : e[r] = t, e;
}

function _toPropertyKey(t) {
    const i = _toPrimitive(t, 'string');
    return 'symbol' == typeof i ? i : i + '';
}

function _toPrimitive(t, r) {
    if ('object' != typeof t || !t) return t;
    const e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        const i = e.call(t, r || 'default');
        if ('object' != typeof i) return i;
        throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === r ? String : Number)(t);
}

const ErrorNotificationSystem = () => {
    const [notifications, setNotifications] = (0, _react.useState)([]);
    const [recoveryProgress, setRecoveryProgress] = (0, _react.useState)(null);
    const [showErrorLog, setShowErrorLog] = (0, _react.useState)(false);
    (0, _react.useEffect)(() => {
        // Listen for system events
        const handleSystemEvent = event => {
            const {
                eventType,
                data
            } = event.detail;
            switch (eventType) {
                case 'circuit-breaker-activated':
                    addNotification({
                        id: `cb-${Date.now()}`,
                        type: 'error',
                        title: 'Circuit Breaker Activated',
                        message: `System protection activated due to ${data.failureCount} failures`,
                        persistent: true,
                        actions: [{
                            label: 'View Details',
                            action: () => setShowErrorLog(true)
                        }]
                    });
                    break;
                case 'circuit-breaker-reset':
                    addNotification({
                        id: `cb-reset-${Date.now()}`,
                        type: 'success',
                        title: 'Circuit Breaker Reset',
                        message: 'System protection has been reset, normal operations resumed',
                        duration: 5000
                    });
                    break;
                case 'system-health-degraded':
                    addNotification({
                        id: `health-${Date.now()}`,
                        type: 'warning',
                        title: 'System Health Degraded',
                        message: `System health changed from ${data.previousHealth} to ${data.currentHealth}`,
                        duration: 8000,
                        actions: [{
                            label: 'View Logs',
                            action: () => setShowErrorLog(true)
                        }]
                    });
                    break;
                case 'component-degraded':
                    addNotification({
                        id: `comp-${data.componentName}-${Date.now()}`,
                        type: 'warning',
                        title: 'Component Degraded',
                        message: `${data.componentName} is running in degraded mode: ${data.reason}`,
                        duration: 10000
                    });
                    break;
                case 'automatic-recovery-attempted':
                    setRecoveryProgress({
                        component: data.component || 'System',
                        status: 'attempting',
                        message: `Attempting recovery from ${data.fromState} state...`
                    });

                    // Clear recovery progress after 30 seconds
                    setTimeout(() => setRecoveryProgress(null), 30000);
                    break;
                case 'high-error-rate':
                    addNotification({
                        id: `error-rate-${Date.now()}`,
                        type: 'error',
                        title: 'High Error Rate Detected',
                        message: `Error rate: ${data.errorRate.toFixed(1)} errors/min`,
                        duration: 15000,
                        actions: [{
                            label: 'View Error Log',
                            action: () => setShowErrorLog(true)
                        }, {
                            label: 'Clear Errors',
                            action: clearErrors
                        }]
                    });
                    break;
                default:
                    break;
            }
        };

        // Listen for component errors
        const handleComponentError = event => {
            const {
                errorData,
                componentName,
                workflow: _workflow
            } = event.detail;
            addNotification({
                id: `error-${componentName}-${Date.now()}`,
                type: errorData.severity === 'critical' ? 'error' : 'warning',
                title: `${componentName} Error`,
                message: errorData.message || 'An error occurred in this component',
                duration: errorData.severity === 'critical' ? 0 : 8000,
                // Persistent for critical errors
                actions: [{
                    label: 'Retry',
                    action: () => retryComponent(componentName)
                }, {
                    label: 'View Details',
                    action: () => showErrorDetails(errorData)
                }]
            });
        };
        window.addEventListener('system-event', handleSystemEvent);
        window.addEventListener('componentError', handleComponentError);
        return () => {
            window.removeEventListener('system-event', handleSystemEvent);
            window.removeEventListener('componentError', handleComponentError);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    const addNotification = (0, _react.useCallback)(notification => {
        setNotifications(prev => [...prev, notification]);

        // Auto-remove non-persistent notifications
        if (notification.duration && notification.duration > 0) {
            setTimeout(() => {
                removeNotification(notification.id);
            }, notification.duration);
        }
    }, []);
    const removeNotification = id => {
        setNotifications(prev => prev.filter(n => n.id !== id));
    };
    const clearErrors = (0, _react.useCallback)(() => {
        _GlobalErrorHandler.default.clearLogs();
        addNotification({
            id: `clear-${Date.now()}`,
            type: 'info',
            title: 'Errors Cleared',
            message: 'All error logs have been cleared',
            duration: 3000
        });
    }, [addNotification]);
    const retryComponent = (0, _react.useCallback)(async componentName => {
        try {
            if (window.electronAPI) {
                const result = await window.electronAPI.ipcRenderer.invoke('recover-trading-system', {
                    component: componentName,
                    timestamp: new Date().toISOString()
                });
                if (result.success) {
                    addNotification({
                        id: `retry-success-${Date.now()}`,
                        type: 'success',
                        title: 'Component Recovery',
                        message: `${componentName} has been successfully recovered`,
                        duration: 5000
                    });
                } else {
                    addNotification({
                        id: `retry-failed-${Date.now()}`,
                        type: 'error',
                        title: 'Recovery Failed',
                        message: `Failed to recover ${componentName}: ${result.error}`,
                        duration: 8000
                    });
                }
            }
        } catch (error) {
            addNotification({
                id: `retry-error-${Date.now()}`,
                type: 'error',
                title: 'Recovery Error',
                message: `Error during recovery: ${error.message}`,
                duration: 8000
            });
        }
    }, [addNotification]);
    const showErrorDetails = errorData => {
        _logger.default.info('Error details:', errorData);
        setShowErrorLog(true);
    };
    const _getNotificationIcon = type => {
        switch (type) {
            case 'error':
                return '🚨';
            case 'warning':
                return '⚠️';
            case 'success':
                return '✅';
            case 'info':
                return 'ℹ️';
            default:
                return '📢';
        }
    };
    return /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/_react.default.createElement('div', {
        className: 'error-notifications-container'
    }, notifications.map(notification => /*#__PURE__*/_react.default.createElement(ErrorNotification, {
        key: notification.id,
        notification: notification,
        onClose: () => removeNotification(notification.id)
    }))), recoveryProgress && /*#__PURE__*/_react.default.createElement(RecoveryProgressIndicator, {
        progress: recoveryProgress,
        onClose: () => setRecoveryProgress(null)
    }), showErrorLog && /*#__PURE__*/_react.default.createElement(ErrorLogModal, {
        onClose: () => setShowErrorLog(false)
    }));
};
const ErrorNotification = ({
                               notification,
                               onClose
                           }) => {
    const {
        type,
        title,
        message,
        actions
    } = notification;
    return /*#__PURE__*/_react.default.createElement('div', {
        className: `error-notification ${type}`
    }, /*#__PURE__*/_react.default.createElement('div', {
        className: 'notification-header'
    }, /*#__PURE__*/_react.default.createElement('span', {
        className: 'notification-icon'
    }, type === 'error' ? '🚨' : type === 'warning' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️'), /*#__PURE__*/_react.default.createElement('h4', {
        className: 'notification-title'
    }, title), /*#__PURE__*/_react.default.createElement('button', {
        className: 'notification-close',
        onClick: onClose
    }, '\xD7')), /*#__PURE__*/_react.default.createElement('p', {
        className: 'notification-message'
    }, message), actions && actions.length > 0 && /*#__PURE__*/_react.default.createElement('div', {
        className: 'notification-actions'
    }, actions.map((action, index) => /*#__PURE__*/_react.default.createElement('button', {
        key: index,
        className: 'notification-action-btn',
        onClick: action.action
    }, action.label))));
};
const RecoveryProgressIndicator = ({
                                       progress,
                                       onClose
                                   }) => {
    const [progressValue, setProgressValue] = (0, _react.useState)(0);
    (0, _react.useEffect)(() => {
        // Simulate progress animation
        const interval = setInterval(() => {
            setProgressValue(prev => {
                if (prev >= 90) return prev;
                return prev + Math.random() * 10;
            });
        }, 500);
        return () => clearInterval(interval);
    }, []);
    return /*#__PURE__*/_react.default.createElement('div', {
        className: 'error-recovery-progress'
    }, /*#__PURE__*/_react.default.createElement('h4', null, '\uD83D\uDD04 System Recovery'), /*#__PURE__*/_react.default.createElement('p', null, progress.message), /*#__PURE__*/_react.default.createElement('div', {
        className: 'progress-bar'
    }, /*#__PURE__*/_react.default.createElement('div', {
        className: 'progress-fill',
        style: {
            width: `${progressValue}%`
        }
    })), /*#__PURE__*/_react.default.createElement('div', {
        className: 'recovery-status'
    }, progress.status === 'attempting' ? 'Attempting recovery...' : progress.status === 'success' ? 'Recovery successful' : progress.status === 'failed' ? 'Recovery failed' : 'Processing...'), /*#__PURE__*/_react.default.createElement('button', {
        className: 'recovery-close',
        onClick: onClose
    }, '\xD7'));
};
const ErrorLogModal = ({
                           onClose
                       }) => {
    const [logs, setLogs] = (0, _react.useState)([]);
    const [filters, setFilters] = (0, _react.useState)({
        level: '',
        category: '',
        since: '',
        message: ''
    });
    const [metrics, setMetrics] = (0, _react.useState)(null);
    const loadLogs = (0, _react.useCallback)(() => {
        const filteredLogs = _GlobalErrorHandler.default.getLogs(filters);
        if (filteredLogs && Array.isArray(filteredLogs)) {
            setLogs(filteredLogs.slice(-100)); // Show last 100 logs
        } else {
            setLogs([]);
        }
    }, [filters]);

    // Only one declaration of loadMetrics
    const loadMetrics = () => {
        const logMetrics = _GlobalErrorHandler.default.getMetrics();
        setMetrics(logMetrics);
    };
    (0, _react.useEffect)(() => {
        loadLogs();
        loadMetrics();
    }, [loadLogs]);
    const exportLogs = format => {
        const exportedData = _GlobalErrorHandler.default.exportLogs(format);
        if (exportedData) {
            const blob = new Blob([exportedData], {
                type: format === 'json' ? 'application/json' : 'text/plain'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `error-logs-${new Date().toISOString().split('T')[0]}.${format}`;
            a.click();
            URL.revokeObjectURL(url);
        }
    };
    const clearAllLogs = () => {
        _GlobalErrorHandler.default.clearLogs();
        setLogs([]);
        loadMetrics();
    };
    return /*#__PURE__*/_react.default.createElement('div', {
        className: 'error-log-modal-overlay'
    }, /*#__PURE__*/_react.default.createElement('div', {
        className: 'error-log-modal'
    }, /*#__PURE__*/_react.default.createElement('div', {
        className: 'modal-header'
    }, /*#__PURE__*/_react.default.createElement('h3', null, '\uD83D\uDCDD Error Log Viewer'), /*#__PURE__*/_react.default.createElement('button', {
        className: 'modal-close',
        onClick: onClose
    }, '\xD7')), metrics && /*#__PURE__*/_react.default.createElement('div', {
        className: 'log-metrics'
    }, /*#__PURE__*/_react.default.createElement('div', {
        className: 'metric'
    }, /*#__PURE__*/_react.default.createElement('span', null, 'Total Logs:'), /*#__PURE__*/_react.default.createElement('span', null, metrics.totalLogs)), /*#__PURE__*/_react.default.createElement('div', {
        className: 'metric'
    }, /*#__PURE__*/_react.default.createElement('span', null, 'Buffer Size:'), /*#__PURE__*/_react.default.createElement('span', null, metrics.bufferSize)), /*#__PURE__*/_react.default.createElement('div', {
        className: 'metric'
    }, /*#__PURE__*/_react.default.createElement('span', null, 'Last Flush:'), /*#__PURE__*/_react.default.createElement('span', null, metrics.lastFlush ? new Date(metrics.lastFlush).toLocaleTimeString() : 'Never'))), /*#__PURE__*/_react.default.createElement('div', {
        className: 'log-filters'
    }, /*#__PURE__*/_react.default.createElement('select', {
        value: filters.level,
        onChange: e => setFilters(prev => _objectSpread(_objectSpread({}, prev), {}, {
            level: e.target.value
        }))
    }, /*#__PURE__*/_react.default.createElement('option', {
        value: ''
    }, 'All Levels'), /*#__PURE__*/_react.default.createElement('option', {
        value: 'debug'
    }, 'Debug'), /*#__PURE__*/_react.default.createElement('option', {
        value: 'info'
    }, 'Info'), /*#__PURE__*/_react.default.createElement('option', {
        value: 'warn'
    }, 'Warning'), /*#__PURE__*/_react.default.createElement('option', {
        value: 'error'
    }, 'Error'), /*#__PURE__*/_react.default.createElement('option', {
        value: 'critical'
    }, 'Critical')), /*#__PURE__*/_react.default.createElement('select', {
        value: filters.category,
        onChange: e => setFilters(prev => _objectSpread(_objectSpread({}, prev), {}, {
            category: e.target.value
        }))
    }, /*#__PURE__*/_react.default.createElement('option', {
        value: ''
    }, 'All Categories'), /*#__PURE__*/_react.default.createElement('option', {
        value: 'Runtime'
    }, 'Runtime'), /*#__PURE__*/_react.default.createElement('option', {
        value: 'Component'
    }, 'Component'), /*#__PURE__*/_react.default.createElement('option', {
        value: 'Network'
    }, 'Network'), /*#__PURE__*/_react.default.createElement('option', {
        value: 'Trading'
    }, 'Trading'), /*#__PURE__*/_react.default.createElement('option', {
        value: 'System'
    }, 'System')), /*#__PURE__*/_react.default.createElement('input', {
        type: 'text',
        placeholder: 'Search message...',
        value: filters.message,
        onChange: e => setFilters(prev => _objectSpread(_objectSpread({}, prev), {}, {
            message: e.target.value
        }))
    })), /*#__PURE__*/_react.default.createElement('div', {
        className: 'log-actions'
    }, /*#__PURE__*/_react.default.createElement('button', {
        onClick: () => exportLogs('json')
    }, 'Export JSON'), /*#__PURE__*/_react.default.createElement('button', {
        onClick: () => exportLogs('csv')
    }, 'Export CSV'), /*#__PURE__*/_react.default.createElement('button', {
        onClick: () => exportLogs('text')
    }, 'Export Text'), /*#__PURE__*/_react.default.createElement('button', {
        onClick: clearAllLogs,
        className: 'danger'
    }, 'Clear All')), /*#__PURE__*/_react.default.createElement('div', {
        className: 'log-entries'
    }, logs.length === 0 ? /*#__PURE__*/_react.default.createElement('div', {
        className: 'no-logs'
    }, 'No logs found') : logs.map(log => /*#__PURE__*/_react.default.createElement(LogEntry, {
        key: log.id,
        log: log
    })))));
};
const LogEntry = ({
                      log
                  }) => {
    const [expanded, setExpanded] = (0, _react.useState)(false);
    const formatTimestamp = timestamp => {
        return new Date(timestamp).toLocaleString();
    };
    const getLevelColor = level => {
        switch (level) {
            case 'debug':
                return '#6c757d';
            case 'info':
                return '#17a2b8';
            case 'warn':
                return '#ffc107';
            case 'error':
                return '#dc3545';
            case 'critical':
                return '#721c24';
            default:
                return '#6c757d';
        }
    };
    return /*#__PURE__*/_react.default.createElement('div', {
        className: `log-entry ${log.level}`
    }, /*#__PURE__*/_react.default.createElement('div', {
        className: 'log-header',
        onClick: () => setExpanded(!expanded)
    }, /*#__PURE__*/_react.default.createElement('span', {
        className: 'log-level',
        style: {
            backgroundColor: getLevelColor(log.level)
        }
    }, log.level.toUpperCase()), /*#__PURE__*/_react.default.createElement('span', {
        className: 'log-category'
    }, '[', log.category, ']'), /*#__PURE__*/_react.default.createElement('span', {
        className: 'log-timestamp'
    }, formatTimestamp(log.timestamp)), /*#__PURE__*/_react.default.createElement('span', {
        className: 'log-message'
    }, log.message), /*#__PURE__*/_react.default.createElement('span', {
        className: 'log-expand'
    }, expanded ? '▼' : '▶')), expanded && /*#__PURE__*/_react.default.createElement('div', {
        className: 'log-details'
    }, /*#__PURE__*/_react.default.createElement('div', {
        className: 'log-data'
    }, /*#__PURE__*/_react.default.createElement('strong', null, 'Data:'), /*#__PURE__*/_react.default.createElement('pre', null, JSON.stringify(log.data, null, 2))), /*#__PURE__*/_react.default.createElement('div', {
        className: 'log-meta'
    }, /*#__PURE__*/_react.default.createElement('div', null, /*#__PURE__*/_react.default.createElement('strong', null, 'URL:'), ' ', log.url), /*#__PURE__*/_react.default.createElement('div', null, /*#__PURE__*/_react.default.createElement('strong', null, 'User ID:'), ' ', log.userId), /*#__PURE__*/_react.default.createElement('div', null, /*#__PURE__*/_react.default.createElement('strong', null, 'Session ID:'), ' ', log.sessionId), log.systemInfo && /*#__PURE__*/_react.default.createElement('div', null, /*#__PURE__*/_react.default.createElement('strong', null, 'System:'), ' ', log.systemInfo.platform))));
};
const _default = exports.default = ErrorNotificationSystem;
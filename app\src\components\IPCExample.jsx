// Import logger for consistent logging
import logger from '../utils/logger';

import React, {useState} from 'react';
import {
  useBotStatus,
  useCoins,
  useIPC,
  useMarketData,
  useOpenOrders,
  usePortfolio,
  useTradingStats,
  useWhaleSignals
} from '../hooks/useIPC';
import {
  Alert,
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Grid,
  Paper,
  Stack,
  Typography
} from '@mui/material';
import {styled} from '@mui/material/styles';
import ipcService from '../services/ipcService';

const StyledCard = styled(Card)(({theme: _theme}) => ({
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    transition: 'transform 0.2s ease-in-out',
    '&:hover': {
        transform: 'translateY(-2px)'
    }
}));

const IPCExample = () => {
    const {callIPC, loading: actionLoading, error: actionError} = useIPC();

    const {data: botStatus, loading: botLoading} = useBotStatus();
    const {data: portfolio, loading: portfolioLoading} = usePortfolio();
    const {data: tradingStats, loading: statsLoading} = useTradingStats();
    const {data: coins, loading: coinsLoading} = useCoins();
    const {data: openOrders, loading: ordersLoading} = useOpenOrders();
    const {data: whaleSignals, loading: whaleLoading} = useWhaleSignals();

    const [marketSymbol, _setMarketSymbol] = useState('BTC');
    const {data: marketData, loading: marketLoading} = useMarketData(marketSymbol);

    const handleStartBot = async () => {
        try {
            const result = await callIPC('startBot');
            logger.info('Bot started:', result);
        } catch (error) {
            logger.error('Failed to start bot:', error);
        }
    };

    const handleStopBot = async () => {
        try {
            const result = await callIPC('stopBot');
            logger.info('Bot stopped:', result);
        } catch (error) {
            logger.error('Failed to stop bot:', error);
        }
    };

    const handleInitializeTrading = async () => {
        try {
            const result = await callIPC('initializeTrading', {
                exchanges: ['binance'],
                strategies: ['grid', 'dca'],
                risk: {maxPositionSize: 1000}
            });
            logger.info('Trading initialized:', result);
        } catch (error) {
            logger.error('Failed to initialize trading:', error);
        }
    };

    const handleEmergencyShutdown = async () => {
        try {
            const result = await callIPC('emergencyShutdown');
            logger.info('Emergency shutdown:', result);
        } catch (error) {
            logger.error('Failed to shutdown:', error);
        }
    };

    const handleSaveCoin = async () => {
        try {
            const result = await callIPC('saveCoin', {
                symbol: 'SOL',
                name: 'Solana',
                amount: 10,
                buyPrice: 150
            });
            logger.info('Coin saved:', result);
        } catch (error) {
            logger.error('Failed to save coin:', error);
        }
    };

    const handleStartGrid = async () => {
        try {
            const result = await callIPC('startGrid', {
                symbol: 'BTC/USDT',
                gridCount: 10,
                upperPrice: 55000,
                lowerPrice: 45000,
                investment: 1000
            });
            logger.info('Grid started:', result);
        } catch (error) {
            logger.error('Failed to start grid:', error);
        }
    };

    const handleStartDCA = async () => {
        try {
            const result = await callIPC('startDCA', {
                symbol: 'ETH/USDT',
                amount: 100,
                frequency: 'daily',
                totalInvestments: 10
            });
            logger.info('DCA started:', result);
        } catch (error) {
            logger.error('Failed to start DCA:', error);
        }
    };

    const LoadingIndicator = () => (
        <Box display="flex" justifyContent="center" p={2}>
            <CircularProgress size={24}/>
        </Box>
    );

    const ErrorDisplay = ({error}) => (
        <Alert severity="error" sx={{mb: 2}}>
            {error}
        </Alert>
    );

    const DataCard = ({title, data, loading, error}) => (
        <StyledCard>
            <CardContent>
                <Typography variant="h6" gutterBottom>
                    {title}
                </Typography>
                {loading && <LoadingIndicator/>}
                {error && <ErrorDisplay error={error}/>}
                {data && !loading && !error && (
                    <Typography variant="body2" component="pre" sx={{
                        fontSize: '0.75rem',
                        maxHeight: 200,
                        overflow: 'auto'
                    }}>
                        {JSON.stringify(data, null, 2)}
                    </Typography>
                )}
            </CardContent>
        </StyledCard>
    );

    return (
        <Box p={3}>
            <Typography variant="h4" gutterBottom>
                IPC Communication Example
            </Typography>

            {actionError && <ErrorDisplay error={actionError}/>}

            <Grid container spacing={3} mb={3}>
                <Grid item xs={12}>
                    <Paper elevation={3} sx={{p: 2}}>
                        <Typography variant="h6" gutterBottom>
                            Trading Controls
                        </Typography>
                        <Stack direction="row" spacing={2}>
                            <Button
                                variant="contained"
                                color="success"
                                onClick={handleStartBot}
                                disabled={actionLoading}
                            >
                                Start Bot
                            </Button>
                            <Button
                                variant="contained"
                                color="error"
                                onClick={handleStopBot}
                                disabled={actionLoading}
                            >
                                Stop Bot
                            </Button>
                            <Button
                                variant="contained"
                                onClick={handleInitializeTrading}
                                disabled={actionLoading}
                            >
                                Initialize Trading
                            </Button>
                            <Button
                                variant="contained"
                                color="warning"
                                onClick={handleEmergencyShutdown}
                                disabled={actionLoading}
                            >
                                Emergency Shutdown
                            </Button>
                        </Stack>
                    </Paper>
                </Grid>

                <Grid item xs={12}>
                    <Paper elevation={3} sx={{p: 2}}>
                        <Typography variant="h6" gutterBottom>
                            Strategy Controls
                        </Typography>
                        <Stack direction="row" spacing={2}>
                            <Button
                                variant="outlined"
                                onClick={handleStartGrid}
                                disabled={actionLoading}
                            >
                                Start Grid Trading
                            </Button>
                            <Button
                                variant="outlined"
                                onClick={handleStartDCA}
                                disabled={actionLoading}
                            >
                                Start DCA
                            </Button>
                            <Button
                                variant="outlined"
                                onClick={handleSaveCoin}
                                disabled={actionLoading}
                            >
                                Save Test Coin
                            </Button>
                        </Stack>
                    </Paper>
                </Grid>
            </Grid>

            <Grid container spacing={3}>
                <Grid item xs={12} md={6} lg={4}>
                    <DataCard
                        title="Bot Status"
                        data={botStatus}
                        loading={botLoading}
                        error={null}
                    />
                </Grid>

                <Grid item xs={12} md={6} lg={4}>
                    <DataCard
                        title="Portfolio Summary"
                        data={portfolio}
                        loading={portfolioLoading}
                        error={null}
                    />
                </Grid>

                <Grid item xs={12} md={6} lg={4}>
                    <DataCard
                        title="Trading Stats"
                        data={tradingStats}
                        loading={statsLoading}
                        error={null}
                    />
                </Grid>

                <Grid item xs={12} md={6} lg={4}>
                    <DataCard
                        title="Market Data (BTC)"
                        data={marketData}
                        loading={marketLoading}
                        error={null}
                    />
                </Grid>

                <Grid item xs={12} md={6} lg={4}>
                    <DataCard
                        title="Coins"
                        data={coins}
                        loading={coinsLoading}
                        error={null}
                    />
                </Grid>

                <Grid item xs={12} md={6} lg={4}>
                    <DataCard
                        title="Open Orders"
                        data={openOrders}
                        loading={ordersLoading}
                        error={null}
                    />
                </Grid>

                <Grid item xs={12} md={6} lg={4}>
                    <DataCard
                        title="Whale Signals"
                        data={whaleSignals}
                        loading={whaleLoading}
                        error={null}
                    />
                </Grid>
            </Grid>

            <Box mt={3}>
                <Typography variant="body2" color="text.secondary">
                    IPC Status: {ipcService.isElectronEnvironment() ? (
                    <Chip label="Electron Mode" color="success" size="small"/>
                ) : (
                    <Chip label="Mock Mode" color="info" size="small"/>
                )}
                </Typography>
            </Box>
        </Box>
    );
};

export default IPCExample;

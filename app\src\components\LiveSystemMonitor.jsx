// Import logger for consistent logging
import logger from '../utils/logger';

import React, {useEffect, useRef, useState} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  CircularProgress,
  Grid,
  IconButton,
  LinearProgress,
  Tooltip,
  Typography
} from '@mui/material';
import {
  CheckCircle,
  Memory,
  NetworkCheck,
  Refresh as RefreshIcon,
  Speed,
  Storage,
  TrendingUp,
  Warning as WarningIcon
} from '@mui/icons-material';
import {motion} from 'framer-motion';
import realTimeStatusService from '../services/realTimeStatusService';
import ipcService from '../services/ipcService';

const LiveSystemMonitor = ({className}) => {
    const [systemData, setSystemData] = useState({
        cpu: 0,
        memory: 0,
        disk: 0,
        network: 0,
        components: {},
        health: 'unknown',
        lastUpdate: new Date(),
        uptime: 0
    });

    const [isRefreshing, setIsRefreshing] = useState(false);
    const unsubscribeRef = useRef(null);

    useEffect(() => {
        // Initialize real-time status service
        realTimeStatusService.initialize(ipcService);

        // Subscribe to system metrics updates
        const unsubscribe = realTimeStatusService.addListener('system-status', (systemStatus) => {
            setSystemData(prevData => ({
                ...prevData,
                health: systemStatus.health,
                components: systemStatus.components,
                uptime: systemStatus.uptime,
                lastUpdate: new Date(systemStatus.timestamp)
            }));
        });

        // Subscribe to system metrics specifically
        const metricsUnsubscribe = realTimeStatusService.addListener('system-metrics', (metrics) => {
            setSystemData(prevData => ({
                ...prevData,
                cpu: metrics.cpu || 0,
                memory: metrics.memory || 0,
                disk: metrics.disk || 0,
                network: metrics.network || 0,
                lastUpdate: new Date()
            }));
        });

        unsubscribeRef.current = () => {
            unsubscribe();
            metricsUnsubscribe();
        };

        // Start monitoring
        realTimeStatusService.startPolling(2000); // More frequent updates for live monitoring

        // Initial fetch
        fetchSystemMetrics();

        return () => {
            if (unsubscribeRef.current) {
                unsubscribeRef.current();
            }
        };
    }, []);

    const fetchSystemMetrics = async () => {
        try {
            setIsRefreshing(true);
            const metricsResult = await ipcService.getSystemMetrics();

            if (metricsResult.success) {
                const metrics = metricsResult.data;
                setSystemData(prevData => ({
                    ...prevData,
                    cpu: metrics.cpu || 0,
                    memory: metrics.memory || 0,
                    disk: metrics.disk || 0,
                    network: metrics.network || 0,
                    lastUpdate: new Date()
                }));
            }
        } catch (error) {
            logger.error('Failed to fetch system metrics:', error);
        } finally {
            setIsRefreshing(false);
        }
    };

    const getStatusIcon = (value) => {
        if (value > 80) return <WarningIcon sx={{color: '#f44336'}}/>;
        if (value > 60) return <TrendingUp sx={{color: '#ff9800'}}/>;
        return <CheckCircle sx={{color: '#4caf50'}}/>;
    };

    const getStatusColor = (value) => {
        if (value > 80) return '#f44336';
        if (value > 60) return '#ff9800';
        return '#4caf50';
    };

    const handleRefresh = () => {
        setIsRefreshing(true);
        setTimeout(() => {
            setSystemData(prevData => ({
                ...prevData,
                cpu: Math.random() * 100,
                memory: Math.random() * 100,
                disk: Math.random() * 100,
                network: Math.random() * 100,
                lastUpdate: new Date(),
                uptime: prevData.uptime,
                components: prevData.components,
                health: prevData.health
            }));
            setIsRefreshing(false);
        }, 1000);
    };

    const metrics = [
        {
            label: 'CPU Usage',
            value: systemData.cpu,
            icon: <Speed sx={{fontSize: 20}}/>,
            unit: '%'
        },
        {
            label: 'Memory Usage',
            value: systemData.memory,
            icon: <Memory sx={{fontSize: 20}}/>,
            unit: '%'
        },
        {
            label: 'Disk Usage',
            value: systemData.disk,
            icon: <Storage sx={{fontSize: 20}}/>,
            unit: '%'
        },
        {
            label: 'Network Load',
            value: systemData.network,
            icon: <NetworkCheck sx={{fontSize: 20}}/>,
            unit: '%'
        }];

    return (
        <Box className={className}>
            <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3}}>
                <Typography variant="h6" sx={{color: '#00eaff'}}>
                    Live System Monitor
                </Typography>
                <Tooltip title="Refresh Data">
                    <IconButton
                        onClick={handleRefresh}
                        disabled={isRefreshing}
                        sx={{color: '#a259ff'}}
                    >
                        {isRefreshing ? (
                            <CircularProgress size={20} sx={{color: '#a259ff'}}/>
                        ) : (
                            <RefreshIcon/>
                        )}
                    </IconButton>
                </Tooltip>
            </Box>

            <Grid container spacing={2}>
                {metrics.map((metric, index) => (
                    <Grid item xs={12} sm={6} key={metric.label}>
                        <motion.div
                            initial={{opacity: 0, y: 20}}
                            animate={{opacity: 1, y: 0}}
                            transition={{delay: index * 0.1}}
                        >
                            <Card sx={{
                                background: 'linear-gradient(135deg, rgba(162,89,255,0.1) 0%, rgba(162,89,255,0.05) 100%)',
                                border: '1px solid rgba(162,89,255,0.3)'
                            }}>
                                <CardContent>
                                    <Box sx={{display: 'flex', alignItems: 'center', mb: 2}}>
                                        {metric.icon}
                                        <Typography variant="subtitle2" sx={{ml: 1, color: '#fff'}}>
                                            {metric.label}
                                        </Typography>
                                    </Box>

                                    <Box sx={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
                                        <Typography variant="h4"
                                                    sx={{color: getStatusColor(metric.value), fontWeight: 800}}>
                                            {metric.value.toFixed(1)}{metric.unit}
                                        </Typography>
                                        {getStatusIcon(metric.value)}
                                    </Box>

                                    <Box sx={{mt: 2}}>
                                        <LinearProgress
                                            variant="determinate"
                                            value={metric.value}
                                            sx={{
                                                height: 6,
                                                borderRadius: 3,
                                                backgroundColor: 'rgba(162,89,255,0.2)',
                                                '& .MuiLinearProgress-bar': {
                                                    backgroundColor: getStatusColor(metric.value),
                                                    borderRadius: 3
                                                }
                                            }}
                                        />
                                    </Box>
                                </CardContent>
                            </Card>
                        </motion.div>
                    </Grid>
                ))}
            </Grid>

            <Box sx={{mt: 3, textAlign: 'center'}}>
                <Typography variant="caption" sx={{color: '#888'}}>
                    Last updated: {systemData.lastUpdate.toLocaleTimeString()}
                </Typography>
            </Box>
        </Box>
    );
};

LiveSystemMonitor.propTypes = {
    className: PropTypes.string
};

LiveSystemMonitor.defaultProps = {
    className: ''
};

export default LiveSystemMonitor;
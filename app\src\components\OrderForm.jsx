import React, { useState } from 'react';

const OrderForm = ({ onPlaceOrder }) => {
  const [symbol, setSymbol] = useState('BTC');
  const [side, setSide] = useState('buy');
  const [quantity, setQuantity] = useState('');
  const [price, setPrice] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    onPlaceOrder({
      symbol,
      side,
      quantity: parseFloat(quantity),
      price: price ? parseFloat(price) : null,
    });
    setQuantity('');
    setPrice('');
  };

  return (
    <div className="order-form">
      <h3>Place Order</h3>
      <form onSubmit={handleSubmit}>
        <select value={symbol} onChange={(e) => setSymbol(e.target.value)}>
          <option value="BTC">BTC</option>
          <option value="ETH">ETH</option>
          <option value="BNB">BNB</option>
        </select>
        <select value={side} onChange={(e) => setSide(e.target.value)}>
          <option value="buy">Buy</option>
          <option value="sell">Sell</option>
        </select>
        <input
          type="number"
          placeholder="Quantity"
          value={quantity}
          onChange={(e) => setQuantity(e.target.value)}
          required
          step="0.001"
        />
        <input
          type="number"
          placeholder="Price (optional)"
          value={price}
          onChange={(e) => setPrice(e.target.value)}
          step="0.01"
        />
        <button type="submit">Place Order</button>
      </form>
    </div>
  );
};

export default OrderForm;

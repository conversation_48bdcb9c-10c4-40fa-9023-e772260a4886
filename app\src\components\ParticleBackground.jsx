import React, {useEffect, useRef, useState} from 'react';
import PropTypes from 'prop-types';
import {Box, GlobalStyles} from '@mui/material';
import {createParticleAnimation} from '../utils/AnimationOptimizer';
// Import logger for consistent logging
import logger from '../utils/logger';


const keyframeStyles = `
    @keyframes particleGlow {
        0%, 100% {
            transform: scale(1);
            filter: brightness(1);
        }
        50% {
            transform: scale(1.2);
            filter: brightness(1.5);
        }
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 0.3;
        }
        50% {
            opacity: 0.6;
        }
    }
`;

/**
 * @typedef {object} Particle
 * @property {number} id
 * @property {number} x
 * @property {number} y
 * @property {number} vx
 * @property {number} vy
 * @property {number} size
 * @property {number} opacity
 * @property {number} hue
 */

/**
 * @typedef {object} ParticleSystem
 * @property {Particle[]} particles
 * @property {(callback: (particles: Particle[]) => void) => number} animate
 * @property {(animationId: number) => void} cleanup
 */

/**
 * @typedef {object} ParticleBackgroundProps
 * @property {boolean} [tradingActive=false]
 * @property {number} [intensity=1]
 */

/**
 * Advanced Particle Background System
 * Creates dynamic floating particles that react to trading activity and mouse movement
 * @param {ParticleBackgroundProps} props
 */
export default function ParticleBackground({tradingActive = false, intensity = 1}) {
    /** @type {import('react').RefObject<HTMLDivElement>} */
    const containerRef = useRef(null);
    /** @type {[Particle[], import('react').Dispatch<import('react').SetStateAction<Particle[]>>]} */
    const [particles, setParticles] = useState([]);
    /** @type {import('react').RefObject<ParticleSystem | null>} */
    const _animationRef = useRef(null);
    const [containerSize, setContainerSize] = useState({width: 0, height: 0});

    // Particle colors based on trading theme
    const particleColors = [
        '#00eaff', // Cyan
        '#a259ff', // Purple
        '#ffc107', // Gold
        '#4caf50', // Green
        '#ff6b6b',  // Pink
    ];

    // Initialize container size and optimized particles
    useEffect(() => {
        const updateContainerSize = () => {
            if (containerRef.current) {
                const {width, height} = containerRef.current.getBoundingClientRect();
                setContainerSize({width, height});
            }
        };

        updateContainerSize();
        window.addEventListener('resize', updateContainerSize);
        return () => window.removeEventListener('resize', updateContainerSize);
    }, []);

    // Create optimized particles using the animation optimizer
    useEffect(() => {
        if (containerSize.width === 0 || containerSize.height === 0) return;

        const particleCount = Math.floor(50 * intensity);
        const particleSystem = createParticleAnimation(particleCount, containerSize, {
            speed: tradingActive ? 2 : 1,
            maxParticles: 100,
            recycleParticles: true
        });

        setParticles(particleSystem.particles);

        // Start the optimized animation
        let _animationId;
        try {
            _animationId = particleSystem.animate((updatedParticles) => {
                setParticles([...updatedParticles]);
            });
        } catch (error) {
            logger.warn('Particle animation failed to start:', error);
        }

        return () => {
            if (particleSystem && typeof particleSystem.cleanup === 'function') {
                try {
                    particleSystem.cleanup();
                } catch (error) {
                    logger.warn('Particle cleanup failed:', error);
                }
            }
        };
    }, [intensity, containerSize, tradingActive]);

    return (
        <>
            <GlobalStyles styles={keyframeStyles}/>
            <Box
                ref={containerRef}
                sx={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    width: '100vw',
                    height: '100vh',
                    pointerEvents: 'none',
                    zIndex: 0,
                    overflow: 'hidden'
                }}
            >
                {particles.map(particle => (
                    <Box
                        key={particle.id}
                        className="particle gpu-accelerated"
                        sx={{
                            position: 'absolute',
                            transform: `translate3d(${particle.x}px, ${particle.y}px, 0)`,
                            width: `${particle.size}px`,
                            height: `${particle.size}px`,
                            borderRadius: '50%',
                            background: `radial-gradient(circle, ${particleColors[particle.hue % particleColors.length]}AA, ${particleColors[particle.hue % particleColors.length]}22)`,
                            opacity: particle.opacity,
                            willChange: 'transform, opacity',
                            backfaceVisibility: 'hidden',
                            filter: tradingActive ? 'brightness(1.5) blur(0.5px)' : 'brightness(1)',
                            transition: 'filter 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                            boxShadow: `0 0 ${particle.size * 2}px ${particleColors[particle.hue % particleColors.length]}66`
                        }}
                    />
                ))}

                {/* Additional effects for trading activity */}
                {tradingActive && (
                    <Box
                        sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            background: 'radial-gradient(circle at center, rgba(0,234,255,0.05) 0%, transparent 50%)',
                            animation: 'pulse 2s ease-in-out infinite'
                        }}
                    />
                )}
            </Box>
        </>
    );
}

ParticleBackground.propTypes = {
    tradingActive: PropTypes.bool,
    intensity: PropTypes.number
};

import React from 'react';

const PortfolioSummary = ({ positions }) => {
  const totalValue = positions.reduce((sum, pos) => sum + (pos.quantity * 50000), 0);
  const totalInvested = positions.reduce((sum, pos) => sum + (pos.total_invested || 0), 0);
  const pnl = totalValue - totalInvested;

  return (
    <div className="portfolio-summary">
      <h3>Portfolio Summary</h3>
      <div className="summary-grid">
        <div className="summary-item">
          <label>Total Value</label>
          <span>${totalValue.toLocaleString()}</span>
        </div>
        <div className="summary-item">
          <label>Total P&L</label>
          <span className={pnl >= 0 ? 'positive' : 'negative'}>
            ${pnl.toLocaleString()}
          </span>
        </div>
        <div className="summary-item">
          <label>Positions</label>
          <span>{positions.length}</span>
        </div>
      </div>
    </div>
  );
};

export default PortfolioSummary;

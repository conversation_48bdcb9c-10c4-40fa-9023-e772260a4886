 
'use strict';

Object.defineProperty(exports, '__esModule', {
    value: true
});
exports.default = PortfolioTracker;
const _react = _interopRequireWildcard(require('react'));
const _propTypes = _interopRequireDefault(require('prop-types'));
const _logger = _interopRequireDefault(require('../utils/logger'));
const _material = require('@mui/material');
const _iconsMaterial = require('@mui/icons-material');
const _recharts = require('recharts');
const _HolographicCard = _interopRequireDefault(require('./HolographicCard'));
const _VibrantButton = _interopRequireDefault(require('./VibrantButton'));

function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}

function _interopRequireWildcard(e, t) {
    if ('function' == typeof WeakMap) var r = new WeakMap(),
        n = new WeakMap();
    return (_interopRequireWildcard = function (e, t) {
        if (!t && e && e.__esModule) return e;
        let o,
            i,
            f = {
                __proto__: null,
                default: e
            };
        if (null === e || 'object' != typeof e && 'function' != typeof e) return f;
        if (o = t ? n : r) {
            if (o.has(e)) return o.get(e);
            o.set(e, f);
        }
        for (const t in e) 'default' !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]);
        return f;
    })(e, t);
}

function ownKeys(e, r) {
    const t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        let o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function (r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}

function _objectSpread(e) {
    for (let r = 1; r < arguments.length; r++) {
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}

function _defineProperty(e, r, t) {
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value: t,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) : e[r] = t, e;
}

function _toPropertyKey(t) {
    const i = _toPrimitive(t, 'string');
    return 'symbol' == typeof i ? i : i + '';
}

function _toPrimitive(t, r) {
    if ('object' != typeof t || !t) return t;
    const e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        const i = e.call(t, r || 'default');
        if ('object' != typeof i) return i;
        throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === r ? String : Number)(t);
} // Import logger for consistent logging
// All Recharts imports are correct for v2.x. If errors persist, ensure no custom wrappers or legacy code is used.
/**
 * PortfolioTracker component displays portfolio summary with current holdings,
 * performance metrics, and asset allocation visualization
 *
 * @component
 * @param {Array<Object>} [props.gridPositions] - Array of active grid trading positions
 * @returns {React.ReactElement}
 *
 * @example
 * ```jsx
 * <PortfolioTracker onRefresh={handleRefresh} gridPositions={positionsArray} />
 * ```
 */
/**
 * @typedef {Object} Asset
 * @property {string} symbol
 * @property {number} amount
 * @property {number} avgPrice
 * @property {number} currentPrice
 * @property {number} currentValue
 * @property {number} pnl
 * @property {number} pnlPercentage
 * @property {number} allocation
 * @property {string} color
 * @property {boolean} tradable
 */

/**
 * @param {{onRefresh: Function, gridPositions: any[]}} props
 */
// Add TypeScript declaration for window.electronAPI
/**
 * @typedef {Object} ElectronAPI
 * @property {function(): Promise<{success: boolean, data: {assets: any[]}}>} getPortfolioSummary
 * @property {function(string): Promise<{success: boolean, data: any[]}>} getPerformanceHistory
 */

/**
 * @type {Window & { electronAPI?: ElectronAPI }}
 */
const customWindow = /** @type {Window & { electronAPI?: ElectronAPI }} */ /** @type {any} */window;
const electronAPI = customWindow.electronAPI || {
    getPortfolioSummary: () => Promise.resolve({
        success: true,
        data: {
            assets: []
        }
    }),
    getPerformanceHistory: () => Promise.resolve({
        success: true,
        data: []
    })
};

/**
 * PortfolioTracker component displays portfolio summary with current holdings,
 * performance metrics, and asset allocation visualization.
 *
 * @param {{onRefresh: Function, gridPositions: any[]}} props
 * @prop {Function} onRefresh - Function to refresh the portfolio summary when called.
 * @prop {any[]} gridPositions - Array of active grid trading positions.
 *
 * @example
 * <PortfolioTracker onRefresh={handleRefresh} gridPositions={positionsArray} />
 */
function PortfolioTracker({
                              onRefresh,
                              gridPositions
                          }) {
    if (typeof onRefresh !== 'function') {
        _logger.default.warn('PortfolioTracker: onRefresh prop is required and should be a function.');
    }
    const [expandedSections, setExpandedSections] = (0, _react.useState)({
        positions: true,
        performance: true,
        allocation: true,
        overview: true
    });
    const [timeRange, setTimeRange] = (0, _react.useState)('24h');
    // Removed unused loading state
    /** @type [Asset[], Function] */
    const [portfolioData, setPortfolioData] = (0, _react.useState)([]);
    const [performanceHistory, setPerformanceHistory] = (0, _react.useState)([]);
    (0, _react.useEffect)(() => {
        /**
         * Fetches portfolio summary using the electronAPI and updates the portfolioData state.
         * If the API call is successful, the portfolioData is set with the assets data.
         * Logs an error to the console if the fetch operation fails.
         */

        const fetchSummary = async () => {
            if (electronAPI && typeof electronAPI.getPortfolioSummary === 'function') {
                try {
                    const result = await electronAPI.getPortfolioSummary();
                    if (result !== null && result !== void 0 && result.success) {
                        setPortfolioData(result.data.assets || []);
                    }
                } catch (error) {
                    _logger.default.error('Error fetching portfolio summary:', error);
                }
            }
        };

        /**
         * @param {string} timeRange
         */
        const fetchPerformance = async timeRange => {
            if (electronAPI && typeof electronAPI.getPerformanceHistory === 'function') {
                try {
                    const result = await electronAPI.getPerformanceHistory(timeRange);
                    if (result !== null && result !== void 0 && result.success) {
                        setPerformanceHistory(result.data);
                    }
                } catch (error) {
                    _logger.default.error('Error fetching performance history:', error);
                }
            }
        };
        fetchSummary();
        fetchPerformance(timeRange);
    }, [timeRange, onRefresh]);

    /**
     * Formats a given number as a currency string with a USD currency symbol
     * and two decimal places.
     * @param {number | string | undefined} value
     * @returns {string} - The formatted currency string.
     * @example
     * formatCurrency(1234567.89) // $1,234,567.89
     * formatCurrency('1234567.89') // $1,234,567.89
     * formatCurrency(undefined) // $0.00
     */
    const formatCurrency = value => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(Number(value) || 0);
    };

    /**
     * Formats a given number as a percentage string with a green color for positive values and a red color for negative values, and a '+' sign for positive values.
     * @param {number} value - The number to format as a percentage.
     * @returns {React.ReactElement} - The formatted percentage string.
     */
    const formatPercentage = value => {
        if (typeof value !== 'number') return /*#__PURE__*/_react.default.createElement('span', null, 'N/A');
        const formatted = (value * 100).toFixed(2) + '%';
        const color = value > 0 ? '#4caf50' : '#f44336';
        return /*#__PURE__*/_react.default.createElement('span', {
            style: {
                color
            }
        }, formatted);
    };

    /**
     * @param {'positions' | 'performance' | 'allocation' | 'overview'} section
     */
    const toggleSection = section => {
        setExpandedSections(prev => _objectSpread(_objectSpread({}, prev), {}, {
            [section]: !prev[section]
        }));
    };
    const {
        totalValue,
        totalPnL
    } = _react.default.useMemo(() => {
        return portfolioData.reduce((acc, asset) => {
            acc.totalValue += asset.currentValue || 0;
            acc.totalPnL += asset.pnl || 0;
            return acc;
        }, {
            totalValue: 0,
            totalPnL: 0
        });
    }, [portfolioData]);
    const totalPnLPercentage = totalValue !== 0 ? totalPnL / (totalValue - totalPnL) * 100 : 0;
    const topPerformers = _react.default.useMemo(() => [...portfolioData].filter(asset => typeof asset.pnlPercentage === 'number').sort((a, b) => b.pnlPercentage - a.pnlPercentage).slice(0, 3), [portfolioData]);
    const worstPerformers = _react.default.useMemo(() => [...portfolioData].filter(asset => typeof asset.pnlPercentage === 'number').sort((a, b) => a.pnlPercentage - b.pnlPercentage).slice(0, 3), [portfolioData]);
    return /*#__PURE__*/_react.default.createElement(_material.Box, null, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3,
        sx: {
            mb: 4
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 4
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'primary',
        elevation: 'low',
        sx: {
            p: 3,
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'subtitle2',
        sx: {
            color: '#888',
            mb: 1
        }
    }, 'Total Value'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h4',
        sx: {
            color: '#00eaff',
            fontWeight: 800,
            mb: 1
        }
    }, formatCurrency(totalValue)), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: totalPnL >= 0 ? '#4caf50' : '#f44336',
            fontWeight: 600
        }
    }, totalPnL >= 0 ? '+' : '', formatCurrency(totalPnL), ' (', totalPnLPercentage.toFixed(2), '%)'))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 4
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'success',
        elevation: 'low',
        sx: {
            p: 3,
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'subtitle2',
        sx: {
            color: '#888',
            mb: 1
        }
    }, 'Top Performer'), topPerformers[0] && /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h5',
        sx: {
            color: '#ffc107',
            fontWeight: 800,
            mb: 1
        }
    }, topPerformers[0].symbol), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#4caf50',
            fontWeight: 600
        }
    }, formatPercentage(topPerformers[0].pnlPercentage))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 4
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'secondary',
        elevation: 'low',
        sx: {
            p: 3,
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'subtitle2',
        sx: {
            color: '#888',
            mb: 1
        }
    }, 'Active Trades'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h4',
        sx: {
            color: '#a259ff',
            fontWeight: 800,
            mb: 1
        }
    }, (gridPositions === null || gridPositions === void 0 ? void 0 : gridPositions.length) || 0), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888'
        }
    }, 'Grid Trading')))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3,
        sx: {
            mb: 4
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'secondary',
        elevation: 'medium',
        sx: {
            p: 3,
            height: '450px'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#a259ff',
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.PieChart, {
        sx: {
            mr: 1
        }
    }), 'Asset Allocation'), /*#__PURE__*/_react.default.createElement(_material.IconButton, {
        onClick: () => toggleSection('allocation')
    }, expandedSections.allocation ? /*#__PURE__*/_react.default.createElement(_iconsMaterial.ExpandLess, {
        sx: {
            color: '#a259ff'
        }
    }) : /*#__PURE__*/_react.default.createElement(_iconsMaterial.ExpandMore, {
        sx: {
            color: '#a259ff'
        }
    }))), /*#__PURE__*/_react.default.createElement(_material.Collapse, {
        in: expandedSections.allocation
    }, /*#__PURE__*/_react.default.createElement(_recharts.ResponsiveContainer, {
        width: '100%',
        height: 350
    }, /*#__PURE__*/_react.default.createElement(_recharts.PieChart, null, /*#__PURE__*/_react.default.createElement(_recharts.Pie, {
        data: portfolioData,
        dataKey: 'allocation',
        nameKey: 'symbol',
        cx: '50%',
        cy: '50%',
        label: ({
                    symbol,
                    allocation
                }) => `${symbol} ${typeof allocation === 'number' ? allocation.toFixed(1) : '0.0'}%`,
        outerRadius: 100,
        fill: '#a259ff'
    }), /*#__PURE__*/_react.default.createElement(_recharts.Tooltip, {
        formatter: (value, name, entry) => {
            let _entry$payload;
            return [`${typeof value === 'number' ? value.toFixed(2) : '0.00'}%`, (entry === null || entry === void 0 ? void 0 : (_entry$payload = entry.payload) === null || _entry$payload === void 0 ? void 0 : _entry$payload.symbol) || ''];
        },
        contentStyle: {
            backgroundColor: 'rgba(24,26,32,0.95)',
            border: '1px solid #a259ff',
            borderRadius: '8px'
        }
    })))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'premium',
        elevation: 'medium',
        sx: {
            p: 3,
            height: '450px'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#00eaff',
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.ShowChart, {
        sx: {
            mr: 1
        }
    }), 'Portfolio Performance'), /*#__PURE__*/_react.default.createElement(_material.IconButton, {
        onClick: () => toggleSection('performance')
    }, expandedSections.performance ? /*#__PURE__*/_react.default.createElement(_iconsMaterial.ExpandLess, {
        sx: {
            color: '#00eaff'
        }
    }) : /*#__PURE__*/_react.default.createElement(_iconsMaterial.ExpandMore, {
        sx: {
            color: '#00eaff'
        }
    }))), /*#__PURE__*/_react.default.createElement(_material.Collapse, {
        in: expandedSections.performance
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            gap: 1,
            mb: 2
        }
    }, ['24h', '7d', '30d', '90d', '1y'].map(range => /*#__PURE__*/_react.default.createElement(_material.Chip, {
        key: range,
        label: range,
        size: 'small',
        onClick: () => setTimeRange(range),
        sx: {
            backgroundColor: timeRange === range ? 'rgba(0,234,255,0.3)' : 'transparent',
            color: timeRange === range ? '#00eaff' : '#888',
            border: '1px solid rgba(0,234,255,0.3)',
            cursor: 'pointer'
        }
    }))), /*#__PURE__*/_react.default.createElement(_recharts.ResponsiveContainer, {
        width: '100%',
        height: 300
    }, /*#__PURE__*/_react.default.createElement(_recharts.ComposedChart, {
        data: performanceHistory
    }, /*#__PURE__*/_react.default.createElement('defs', null, /*#__PURE__*/_react.default.createElement('linearGradient', {
        id: 'portfolioGradient',
        x1: '0',
        y1: '0',
        x2: '0',
        y2: '1'
    }, /*#__PURE__*/_react.default.createElement('stop', {
        offset: '5%',
        stopColor: '#00eaff',
        stopOpacity: 0.8
    }), /*#__PURE__*/_react.default.createElement('stop', {
        offset: '95%',
        stopColor: '#00eaff',
        stopOpacity: 0
    }))), /*#__PURE__*/_react.default.createElement(_recharts.CartesianGrid, {
        strokeDasharray: '3 3',
        stroke: 'rgba(255,255,255,0.1)'
    }), /*#__PURE__*/_react.default.createElement(_recharts.XAxis, {
        dataKey: 'date',
        stroke: '#888'
    }), /*#__PURE__*/_react.default.createElement(_recharts.YAxis, {
        stroke: '#888'
    }), /*#__PURE__*/_react.default.createElement(_recharts.Tooltip, {
        contentStyle: {
            backgroundColor: 'rgba(24,26,32,0.95)',
            border: '1px solid #00eaff',
            borderRadius: '8px'
        }
    }), /*#__PURE__*/_react.default.createElement(_recharts.Area, {
        type: 'monotone',
        dataKey: 'value',
        stroke: '#00eaff',
        fill: 'url(#portfolioGradient)'
    }), /*#__PURE__*/_react.default.createElement(_recharts.Line, {
        type: 'monotone',
        dataKey: 'cumulative',
        stroke: '#a259ff',
        strokeWidth: 2
    }))))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3,
        sx: {
            mb: 4
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'success',
        elevation: 'medium',
        sx: {
            p: 3,
            height: '100%'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'subtitle2',
        sx: {
            color: '#4caf50',
            mb: 1
        }
    }, 'Top Performers'), /*#__PURE__*/_react.default.createElement(_material.List, null, topPerformers.map(asset => /*#__PURE__*/_react.default.createElement(_material.ListItem, {
        key: asset.symbol,
        sx: {
            px: 0
        }
    }, /*#__PURE__*/_react.default.createElement(_material.ListItemAvatar, null, /*#__PURE__*/_react.default.createElement(_material.Avatar, {
        sx: {
            backgroundColor: asset.color,
            width: 50,
            height: 50
        }
    }, asset.symbol.slice(0, 2))), /*#__PURE__*/_react.default.createElement(_material.ListItemText, {
        primary: /*#__PURE__*/_react.default.createElement(_material.Box, {
            sx: {
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
            }
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#fff',
                fontWeight: 600
            }
        }, asset.symbol), /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#4caf50',
                fontWeight: 600
            }
        }, formatPercentage(asset.pnlPercentage)))
    })))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'error',
        elevation: 'medium',
        sx: {
            p: 3,
            height: '100%'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'subtitle2',
        sx: {
            color: '#f44336',
            mb: 1
        }
    }, 'Worst Performers'), /*#__PURE__*/_react.default.createElement(_material.List, null, worstPerformers.map(asset => /*#__PURE__*/_react.default.createElement(_material.ListItem, {
        key: asset.symbol,
        sx: {
            px: 0
        }
    }, /*#__PURE__*/_react.default.createElement(_material.ListItemAvatar, null, /*#__PURE__*/_react.default.createElement(_material.Avatar, {
        sx: {
            backgroundColor: asset.color,
            width: 50,
            height: 50
        }
    }, asset.symbol.slice(0, 2))), /*#__PURE__*/_react.default.createElement(_material.ListItemText, {
        primary: /*#__PURE__*/_react.default.createElement(_material.Box, {
            sx: {
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
            }
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#fff',
                fontWeight: 600
            }
        }, asset.symbol), /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#f44336',
                fontWeight: 600
            }
        }, formatPercentage(asset.pnlPercentage))),
        secondary: /*#__PURE__*/_react.default.createElement(_material.Box, {
            sx: {
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
            }
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#888',
                fontSize: '0.8rem'
            }
        }, formatCurrency(asset.currentValue)), /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#f44336',
                fontSize: '0.8rem',
                fontWeight: 600
            }
        }, formatCurrency(asset.pnl)))
    }))))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'gold',
        elevation: 'medium',
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#ffc107',
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.BarChart, {
        sx: {
            mr: 1
        }
    }), 'Detailed Holdings'), /*#__PURE__*/_react.default.createElement(_material.IconButton, {
        onClick: () => toggleSection('positions')
    }, expandedSections.positions ? /*#__PURE__*/_react.default.createElement(_iconsMaterial.ExpandLess, {
        sx: {
            color: '#ffc107'
        }
    }) : /*#__PURE__*/_react.default.createElement(_iconsMaterial.ExpandMore, {
        sx: {
            color: '#ffc107'
        }
    }))), /*#__PURE__*/_react.default.createElement(_material.Collapse, {
        in: expandedSections.positions
    }, /*#__PURE__*/_react.default.createElement(_material.TableContainer, null, /*#__PURE__*/_react.default.createElement(_material.Table, null, /*#__PURE__*/_react.default.createElement(_material.TableHead, null, /*#__PURE__*/_react.default.createElement(_material.TableRow, null, /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#ffc107',
            fontWeight: 600
        }
    }, 'Symbol'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#ffc107',
            fontWeight: 600
        }
    }, 'Amount'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#ffc107',
            fontWeight: 600
        }
    }, 'Avg Price'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#ffc107',
            fontWeight: 600
        }
    }, 'Current Price'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#ffc107',
            fontWeight: 600
        }
    }, 'Market Value'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#ffc107',
            fontWeight: 600
        }
    }, 'P&L'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#ffc107',
            fontWeight: 600
        }
    }, 'Allocation'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#ffc107',
            fontWeight: 600
        }
    }, 'Actions'))), /*#__PURE__*/_react.default.createElement(_material.TableBody, null, portfolioData.map(asset => /*#__PURE__*/_react.default.createElement(_material.TableRow, {
        key: asset.symbol
    }, /*#__PURE__*/_react.default.createElement(_material.TableCell, null, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Avatar, {
        sx: {
            backgroundColor: asset.color,
            width: 40,
            height: 40,
            mr: 2
        }
    }, asset.symbol.slice(0, 2)), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        sx: {
            color: '#fff',
            fontWeight: 600
        }
    }, asset.symbol))), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#fff'
        }
    }, asset.amount !== undefined && asset.amount !== null ? asset.amount.toLocaleString() : '—'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#fff'
        }
    }, asset.avgPrice !== undefined && asset.avgPrice !== null ? formatCurrency(asset.avgPrice) : '—'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#fff'
        }
    }, asset.currentPrice !== undefined && asset.currentPrice !== null ? formatCurrency(asset.currentPrice) : '—'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#fff'
        }
    }, asset.currentValue !== undefined && asset.currentValue !== null ? formatCurrency(asset.currentValue) : '—'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#fff'
        }
    }, asset.pnl !== undefined && asset.pnl !== null ? formatCurrency(asset.pnl) : '—', /*#__PURE__*/_react.default.createElement(_material.Box, null, asset.pnlPercentage !== undefined && asset.pnlPercentage !== null ? formatPercentage(asset.pnlPercentage) : '—')), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.LinearProgress, {
        variant: 'determinate',
        value: typeof asset.allocation === 'number' && !isNaN(asset.allocation) ? asset.allocation : 0,
        sx: {
            width: 80,
            mr: 1,
            backgroundColor: 'rgba(255,193,7,0.2)',
            '& .MuiLinearProgress-bar': {
                backgroundColor: asset.color
            }
        }
    }), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'caption',
        sx: {
            color: '#888',
            ml: 1
        }
    }, typeof asset.allocation === 'number' && !isNaN(asset.allocation) ? asset.allocation.toFixed(1) : '0.0', '%'))), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            gap: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_VibrantButton.default, {
        color: 'primary',
        disabled: asset.amount === undefined || asset.currentPrice === undefined
    }, 'Buy'), /*#__PURE__*/_react.default.createElement(_VibrantButton.default, {
        color: 'primary',
        disabled: asset.amount === undefined || asset.currentPrice === undefined
    }, 'Sell')))))))))))));
}

PortfolioTracker.propTypes = {
    onRefresh: _propTypes.default.func.isRequired,
    gridPositions: _propTypes.default.arrayOf(_propTypes.default.object)
};
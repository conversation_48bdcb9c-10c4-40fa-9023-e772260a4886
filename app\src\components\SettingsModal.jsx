import React, {useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import logger from '../utils/logger';
import {
  <PERSON>ert,
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControlLabel,
  Switch,
  Tab,
  Tabs,
  TextField
} from '@mui/material';
import Grid from '@mui/material/Unstable_Grid2';
import {Api, NotificationsActive, Security, Settings, TrendingUp} from '@mui/icons-material';
import VibrantButton from './VibrantButton';
import HolographicCard from './HolographicCard';

// Import logger for consistent logging
/** @type {Window & { electronAPI?: any }} */
const customWindow = window;
const safeElectronAPI =
    (typeof customWindow !== 'undefined' && customWindow.electronAPI) || {
        getSettings: () =>
            Promise.resolve({
                success: true,
                data: {}
            }),
        saveSettings: () =>
            Promise.resolve({
                success: true
            }),
        testExchangeConnection: () =>
            Promise.resolve({
                success: true
            })
    };

function TabPanel(props) {
    const {children, value, index, ...other} = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`settings-tabpanel-${index}`}
            aria-labelledby={`settings-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box
                    sx={{
                        p: 3
                    }}
                >
                    {children}
                </Box>
            )}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired
};

export default function SettingsModal({open, onClose}) {
    const [activeTab, setActiveTab] = useState(0);
    const [localSettings, setLocalSettings] = useState({
        apiKey: '',
        apiSecret: '',
        exchange: 'binance',
        testnet: true,
        maxRisk: 2,
        enableWhaleTracking: true,
        enableMemeCoinScanner: true,
        notifications: true,
        autoTrade: false,
        gridSize: 10,
        dcaInterval: 60
    });
    const [feedback, setFeedback] = useState({
        open: false,
        message: '',
        severity: 'info'
    });

    useEffect(() => {
        const fetchSettings = async () => {
            try {
                const result = await safeElectronAPI.getSettings();
                if (result?.success && result.data) {
                    setLocalSettings((prev) => ({...prev, ...result.data}));
                }
            } catch (error) {
                logger.error('Failed to fetch settings:', error);
                setFeedback({
                    open: true,
                    message: 'Could not load settings.',
                    severity: 'error'
                });
            }
        };
        if (open) {
            fetchSettings();
        }
    }, [open]);

    const handleSettingChange = (key, value) => {
        setLocalSettings((prev) => ({...prev, [key]: value}));
    };

    const handleSave = async () => {
        try {
            const result = await safeElectronAPI.saveSettings(localSettings);
            if (result?.success) {
                setFeedback({
                    open: true,
                    message: 'Settings saved successfully!',
                    severity: 'success'
                });
                onClose();
            } else {
                setFeedback({
                    open: true,
                    message: result?.error || 'Failed to save settings.',
                    severity: 'error'
                });
            }
        } catch (error) {
            logger.error('Error saving settings:', error);
            setFeedback({
                open: true,
                message: 'An unexpected error occurred.',
                severity: 'error'
            });
        }
    };

    const handleTestConnection = async () => {
        try {
            const result = await safeElectronAPI.testExchangeConnection({
                exchangeId: localSettings.exchange
            });
            if (result?.success) {
                setFeedback({
                    open: true,
                    message: 'Connection successful!',
                    severity: 'success'
                });
            } else {
                setFeedback({
                    open: true,
                    message: result?.error || 'Connection failed.',
                    severity: 'error'
                });
            }
        } catch (error) {
            logger.error('Error testing connection:', error);
            setFeedback({
                open: true,
                message: 'An unexpected error occurred.',
                severity: 'error'
            });
        }
    };

    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };

    return (
        <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
            <DialogTitle
                sx={{
                    display: 'flex',
                    alignItems: 'center'
                }}
            >
                <Settings
                    sx={{
                        mr: 2,
                        color: '#a259ff'
                    }}
                />
                Settings & Configuration
            </DialogTitle>
            <DialogContent>
                <HolographicCard variant="default" elevation="medium" sx={{}}>
                    <Tabs
                        value={activeTab}
                        onChange={handleTabChange}
                        sx={{
                            borderBottom: '1px solid rgba(162,89,255,0.3)',
                            '& .MuiTab-root': {
                                color: '#888',
                                fontWeight: 600,
                                '&.Mui-selected': {
                                    color: '#a259ff'
                                }
                            },
                            '& .MuiTabs-indicator': {
                                backgroundColor: '#a259ff'
                            }
                        }}
                    >
                        <Tab icon={<Api/>} label="API Configuration"/>
                        <Tab icon={<TrendingUp/>} label="Trading Settings"/>
                        <Tab icon={<Security/>} label="Risk Management"/>
                        <Tab icon={<NotificationsActive/>} label="Notifications"/>
                    </Tabs>
                    {feedback.open && (
                        <Alert
                            severity={
                                /** @type {'error' | 'warning' | 'info' | 'success'} */ (
                                    feedback.severity
                                )
                            }
                            sx={{
                                m: 2
                            }}
                            onClose={() => setFeedback({...feedback, open: false})}
                        >
                            {feedback.message}
                        </Alert>
                    )}
                    <TabPanel value={activeTab} index={0} key={0}>
                        <Grid container spacing={3}>
                            <Grid xs={12}>
                                <Alert
                                    severity="info"
                                    sx={{
                                        mb: 3
                                    }}
                                >
                                    Your API credentials are stored locally and encrypted. Never
                                    share your API keys.
                                </Alert>
                            </Grid>
                            <Grid xs={12} md={6}>
                                <TextField
                                    label="Exchange API Key"
                                    value={localSettings.apiKey}
                                    onChange={(e) => handleSettingChange('apiKey', e.target.value)}
                                    fullWidth
                                    type="password"
                                    sx={{
                                        '& .MuiOutlinedInput-root': {
                                            '& fieldset': {
                                                borderColor: 'rgba(0,234,255,0.3)'
                                            },
                                            '&:hover fieldset': {
                                                borderColor: '#00eaff'
                                            },
                                            '&.Mui-focused fieldset': {
                                                borderColor: '#00eaff'
                                            }
                                        },
                                        '& .MuiInputLabel-root': {
                                            color: '#00eaff'
                                        },
                                        '& .MuiInputBase-input': {
                                            color: '#fff'
                                        }
                                    }}
                                />
                            </Grid>
                            <Grid xs={12} md={6}>
                                <TextField
                                    label="API Secret"
                                    value={localSettings.apiSecret}
                                    onChange={(e) =>
                                        handleSettingChange('apiSecret', e.target.value)
                                    }
                                    fullWidth
                                    type="password"
                                    sx={{
                                        '& .MuiOutlinedInput-root': {
                                            '& fieldset': {
                                                borderColor: 'rgba(0,234,255,0.3)'
                                            },
                                            '&:hover fieldset': {
                                                borderColor: '#00eaff'
                                            },
                                            '&.Mui-focused fieldset': {
                                                borderColor: '#00eaff'
                                            }
                                        },
                                        '& .MuiInputLabel-root': {
                                            color: '#00eaff'
                                        },
                                        '& .MuiInputBase-input': {
                                            color: '#fff'
                                        }
                                    }}
                                />
                            </Grid>
                            <Grid xs={12} md={6}>
                                <TextField
                                    label="Exchange"
                                    value={localSettings.exchange}
                                    onChange={(e) =>
                                        handleSettingChange('exchange', e.target.value)
                                    }
                                    fullWidth
                                    select
                                    SelectProps={{
                                        native: true
                                    }}
                                    sx={{
                                        '& .MuiOutlinedInput-root': {
                                            '& fieldset': {
                                                borderColor: 'rgba(162,89,255,0.3)'
                                            },
                                            '&:hover fieldset': {
                                                borderColor: '#a259ff'
                                            },
                                            '&.Mui-focused fieldset': {
                                                borderColor: '#a259ff'
                                            }
                                        },
                                        '& .MuiInputLabel-root': {
                                            color: '#a259ff'
                                        },
                                        '& .MuiInputBase-input': {
                                            color: '#fff'
                                        }
                                    }}
                                >
                                    <option value="binance">Binance</option>
                                    <option value="coinbase">Coinbase Pro</option>
                                    <option value="kraken">Kraken</option>
                                    <option value="bybit">Bybit</option>
                                </TextField>
                            </Grid>
                            <Grid xs={12} md={6}>
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={localSettings.testnet}
                                            onChange={(e) =>
                                                handleSettingChange('testnet', e.target.checked)
                                            }
                                            sx={{
                                                '& .MuiSwitch-switchBase.Mui-checked': {
                                                    color: '#00eaff'
                                                },
                                                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track':
                                                    {
                                                        backgroundColor: '#00eaff'
                                                    }
                                            }}
                                        />
                                    }
                                    label="Use Testnet"
                                    sx={{
                                        color: '#fff'
                                    }}
                                />
                            </Grid>
                            <Grid xs={12}>
                                <VibrantButton
                                    onClick={handleTestConnection}
                                    startIcon={<Api/>}
                                    sx={{
                                        mt: 2
                                    }}
                                >
                                    Test Connection
                                </VibrantButton>
                            </Grid>
                        </Grid>
                    </TabPanel>
                    <TabPanel value={activeTab} index={1} key={1}>
                        <Grid container spacing={3}>
                            <Grid xs={12} md={6}>
                                <TextField
                                    label="Default Grid Size"
                                    value={localSettings.gridSize}
                                    onChange={(e) =>
                                        handleSettingChange('gridSize', Number(e.target.value))
                                    }
                                    fullWidth
                                    type="number"
                                    sx={{
                                        '& .MuiOutlinedInput-root': {
                                            '& fieldset': {
                                                borderColor: 'rgba(0,234,255,0.3)'
                                            },
                                            '&:hover fieldset': {
                                                borderColor: '#00eaff'
                                            },
                                            '&.Mui-focused fieldset': {
                                                borderColor: '#00eaff'
                                            }
                                        },
                                        '& .MuiInputLabel-root': {
                                            color: '#00eaff'
                                        },
                                        '& .MuiInputBase-input': {
                                            color: '#fff'
                                        }
                                    }}
                                />
                            </Grid>
                            <Grid xs={12} md={6}>
                                <TextField
                                    label="DCA Interval (minutes)"
                                    value={localSettings.dcaInterval}
                                    onChange={(e) =>
                                        handleSettingChange('dcaInterval', Number(e.target.value))
                                    }
                                    fullWidth
                                    type="number"
                                    sx={{
                                        '& .MuiOutlinedInput-root': {
                                            '& fieldset': {
                                                borderColor: 'rgba(162,89,255,0.3)'
                                            },
                                            '&:hover fieldset': {
                                                borderColor: '#a259ff'
                                            },
                                            '&.Mui-focused fieldset': {
                                                borderColor: '#a259ff'
                                            }
                                        },
                                        '& .MuiInputLabel-root': {
                                            color: '#a259ff'
                                        },
                                        '& .MuiInputBase-input': {
                                            color: '#fff'
                                        }
                                    }}
                                />
                            </Grid>
                            <Grid xs={12}>
                                <Divider
                                    sx={{
                                        my: 2,
                                        borderColor: 'rgba(255,255,255,0.1)'
                                    }}
                                />
                            </Grid>
                            <Grid xs={12} md={6}>
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={localSettings.enableWhaleTracking}
                                            onChange={(e) =>
                                                handleSettingChange(
                                                    'enableWhaleTracking',
                                                    e.target.checked,
                                                )
                                            }
                                            sx={{
                                                '& .MuiSwitch-switchBase.Mui-checked': {
                                                    color: '#ffc107'
                                                },
                                                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track':
                                                    {
                                                        backgroundColor: '#ffc107'
                                                    }
                                            }}
                                        />
                                    }
                                    label="Enable Whale Tracking"
                                    sx={{
                                        color: '#fff'
                                    }}
                                />
                            </Grid>
                            <Grid xs={12} md={6}>
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={localSettings.enableMemeCoinScanner}
                                            onChange={(e) =>
                                                handleSettingChange(
                                                    'enableMemeCoinScanner',
                                                    e.target.checked,
                                                )
                                            }
                                            sx={{
                                                '& .MuiSwitch-switchBase.Mui-checked': {
                                                    color: '#a259ff'
                                                },
                                                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track':
                                                    {
                                                        backgroundColor: '#a259ff'
                                                    }
                                            }}
                                        />
                                    }
                                    label="Enable Meme Coin Scanner"
                                    sx={{
                                        color: '#fff'
                                    }}
                                />
                            </Grid>
                        </Grid>
                    </TabPanel>
                    <TabPanel value={activeTab} index={2} key={2}>
                        <Grid container spacing={3}>
                            <Grid xs={12}>
                                <Alert
                                    severity="warning"
                                    sx={{
                                        mb: 3
                                    }}
                                >
                                    Configure your risk parameters carefully. These settings
                                    affect all trading operations.
                                </Alert>
                            </Grid>
                            <Grid xs={12} md={6}>
                                <TextField
                                    label="Maximum Risk per Trade (%)"
                                    value={localSettings.maxRisk}
                                    onChange={(e) =>
                                        handleSettingChange('maxRisk', Number(e.target.value))
                                    }
                                    fullWidth
                                    type="number"
                                    inputProps={{
                                        min: 0.1,
                                        max: 10,
                                        step: 0.1
                                    }}
                                    sx={{
                                        '& .MuiOutlinedInput-root': {
                                            '& fieldset': {
                                                borderColor: 'rgba(244,67,54,0.3)'
                                            },
                                            '&:hover fieldset': {
                                                borderColor: '#f44336'
                                            },
                                            '&.Mui-focused fieldset': {
                                                borderColor: '#f44336'
                                            }
                                        },
                                        '& .MuiInputLabel-root': {
                                            color: '#f44336'
                                        },
                                        '& .MuiInputBase-input': {
                                            color: '#fff'
                                        }
                                    }}
                                />
                            </Grid>
                            <Grid xs={12} md={6}>
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={localSettings.autoTrade}
                                            onChange={(e) =>
                                                handleSettingChange('autoTrade', e.target.checked)
                                            }
                                            sx={{
                                                '& .MuiSwitch-switchBase.Mui-checked': {
                                                    color: '#f44336'
                                                },
                                                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track':
                                                    {
                                                        backgroundColor: '#f44336'
                                                    }
                                            }}
                                        />
                                    }
                                    label="Enable Auto Trading"
                                    sx={{
                                        color: '#fff'
                                    }}
                                />
                            </Grid>
                        </Grid>
                    </TabPanel>
                    <TabPanel value={activeTab} index={3} key={3}>
                        <Grid container spacing={3}>
                            <Grid xs={12} md={6}>
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={localSettings.notifications}
                                            onChange={(e) =>
                                                handleSettingChange('notifications', e.target.checked)
                                            }
                                            sx={{
                                                '& .MuiSwitch-switchBase.Mui-checked': {
                                                    color: '#4caf50'
                                                },
                                                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track':
                                                    {
                                                        backgroundColor: '#4caf50'
                                                    }
                                            }}
                                        />
                                    }
                                    label="Enable Notifications"
                                    sx={{
                                        color: '#fff'
                                    }}
                                />
                            </Grid>
                        </Grid>
                    </TabPanel>
                </HolographicCard>
            </DialogContent>
            <DialogActions>
                <Button
                    onClick={onClose}
                    sx={{
                        color: '#888'
                    }}
                >
                    Cancel
                </Button>
                <VibrantButton onClick={handleSave}>Save Settings</VibrantButton>
            </DialogActions>
        </Dialog>
    );
}

SettingsModal.propTypes = {
    open: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired
};
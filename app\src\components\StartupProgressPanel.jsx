'use strict';

Object.defineProperty(exports, '__esModule', {
    value: true
});
exports.default = void 0;
const _react = _interopRequireWildcard(require('react'));
const _propTypes = _interopRequireDefault(require('prop-types'));
const _material = require('@mui/material');
const _iconsMaterial = require('@mui/icons-material');
const _framerMotion = require('framer-motion');
const _realTimeStatusService = _interopRequireDefault(require('../services/realTimeStatusService'));

function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}

const _interopRequireWildcard = (function () {
    if (typeof WeakMap !== 'function') {
        return function (e) {
            if (e && e.__esModule) {
                return e;
            }
            const t = {};
            if (e != null) {
                for (const r in e) {
                    if (Object.prototype.hasOwnProperty.call(e, r)) {
                        const o = Object.defineProperty && Object.getOwnPropertyDescriptor ? Object.getOwnPropertyDescriptor(e, r) : {};
                        o.get || o.set ? Object.defineProperty(t, r, o) : t[r] = e[r];
                    }
                }
            }
            return t.default = e, t;
        };
    }
    const r = new WeakMap();
    const n = new WeakMap();
    return function (e, t) {
        if (!t && e && e.__esModule) return e;
        if (e === null || typeof e !== 'object' && typeof e !== 'function') return {
            default: e
        };
        const o = t ? n : r;
        if (o.has(e)) return o.get(e);
        const i = {
            __proto__: null
        };
        const f = Object.defineProperty && Object.getOwnPropertyDescriptor;
        for (const a in e) {
            if (a !== 'default' && Object.prototype.hasOwnProperty.call(e, a)) {
                const u = f ? Object.getOwnPropertyDescriptor(e, a) : null;
                u && (u.get || u.set) ? Object.defineProperty(i, a, u) : i[a] = e[a];
            }
        }
        return i.default = e, o.set(e, i), i;
    };
})();

function ownKeys(e, r) {
    const t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        let o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function (r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}

function _objectSpread(e) {
    for (let r = 1; r < arguments.length; r++) {
        const t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}

function _defineProperty(e, r, t) {
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value: t,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) : e[r] = t, e;
}

function _toPropertyKey(t) {
    const i = _toPrimitive(t, 'string');
    return 'symbol' == typeof i ? i : i + '';
}

function _toPrimitive(t, r) {
    if ('object' != typeof t || !t) return t;
    const e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        const i = e.call(t, r || 'default');
        if ('object' != typeof i) return i;
        throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === r ? String : Number)(t);
}

const StartupProgressPanel = ({
                                  className = '',
                                  onProgressUpdate = null,
                                  realTimeUpdates = false
                              }) => {
    const [progress, setProgress] = (0, _react.useState)(0);
    const [status, setStatus] = (0, _react.useState)('initializing');
    const [startTime] = (0, _react.useState)(Date.now());
    const [currentPhase, setCurrentPhase] = (0, _react.useState)('');
    const [performanceMetrics, setPerformanceMetrics] = (0, _react.useState)({
        totalComponents: 0,
        successfulComponents: 0,
        failedComponents: 0,
        averageInitTime: 0,
        estimatedTimeRemaining: 0
    });
    const [detailedView, setDetailedView] = (0, _react.useState)(false);
    const [_realTimeProgress, setRealTimeProgress] = (0, _react.useState)(null);
    const unsubscribeRefs = (0, _react.useRef)([]);
    const [steps, setSteps] = (0, _react.useState)([{
        id: 1,
        name: 'Core Infrastructure',
        status: 'pending',
        progress: 0,
        components: ['Database', 'Configuration', 'Logger'],
        startTime: null,
        duration: null,
        critical: true
    }, {
        id: 2,
        name: 'Safety Systems',
        status: 'pending',
        progress: 0,
        components: ['Circuit Breaker', 'Risk Manager', 'Error Handler'],
        startTime: null,
        duration: null,
        critical: true
    }, {
        id: 3,
        name: 'Infrastructure Services',
        status: 'pending',
        progress: 0,
        components: ['Exchange Manager', 'Data Collector', 'Event Coordinator'],
        startTime: null,
        duration: null,
        critical: true
    }, {
        id: 4,
        name: 'Analysis Systems',
        status: 'pending',
        progress: 0,
        components: ['Performance Tracker', 'Sentiment Analyzer'],
        startTime: null,
        duration: null,
        critical: false
    }, {
        id: 5,
        name: 'Trading Systems',
        status: 'pending',
        progress: 0,
        components: ['Portfolio Manager', 'Grid Bot Manager', 'Whale Tracker', 'LLM Coordinator'],
        startTime: null,
        duration: null,
        critical: false
    }, {
        id: 6,
        name: 'Trading Execution',
        status: 'pending',
        progress: 0,
        components: ['Trading Executor'],
        startTime: null,
        duration: null,
        critical: true
    }]);

    // Real-time status integration
    (0, _react.useEffect)(() => {
        if (!realTimeUpdates) return;

        // Subscribe to startup progress updates
        const startupProgressUnsubscribe = _realTimeStatusService.default.addListener('startup-progress', progressData => {
            setRealTimeProgress(progressData);
            setProgress(progressData.percentage || 0);
            setCurrentPhase(progressData.message || '');

            // Update steps based on real progress
            if (progressData.step && progressData.total) {
                const stepIndex = Math.floor((progressData.step - 1) / (progressData.total / steps.length));
                setSteps(prevSteps => {
                    const newSteps = [...prevSteps];
                    if (newSteps[stepIndex]) {
                        newSteps[stepIndex].status = 'in-progress';
                        newSteps[stepIndex].progress = progressData.step % (progressData.total / steps.length) / (progressData.total / steps.length) * 100;
                    }
                    return newSteps;
                });
            }
        });

        // Subscribe to startup status updates
        const startupStatusUnsubscribe = _realTimeStatusService.default.addListener('startup-status', statusData => {
            setCurrentPhase(statusData.message || '');
        });

        // Subscribe to startup completion
        const startupCompleteUnsubscribe = _realTimeStatusService.default.addListener('startup-complete', () => {
            setStatus('completed');
            setProgress(100);
            setCurrentPhase('Trading system ready');

            // Mark all steps as completed
            setSteps(prevSteps => prevSteps.map(step => _objectSpread(_objectSpread({}, step), {}, {
                status: 'completed',
                progress: 100
            })));
        });

        // Subscribe to startup errors
        const startupErrorUnsubscribe = _realTimeStatusService.default.addListener('startup-error', errorData => {
            setStatus('error');
            setCurrentPhase(`Error: ${errorData.message || 'Unknown error'}`);
        });

        // Subscribe to component status updates
        const componentStatusUnsubscribe = _realTimeStatusService.default.addListener('component-status', componentData => {
            setSteps(prevSteps => {
                const newSteps = [...prevSteps];
                // Find the step that contains this component
                const stepIndex = newSteps.findIndex(step => step.components.some(comp => comp.toLowerCase().includes(componentData.component.toLowerCase())));
                if (stepIndex >= 0) {
                    const step = newSteps[stepIndex];
                    if (componentData.status === 'ready') {
                        step.status = 'completed';
                        step.progress = 100;
                    } else if (componentData.status === 'initializing') {
                        step.status = 'in-progress';
                    } else if (componentData.status === 'error') {
                        step.status = 'error';
                    }
                }
                return newSteps;
            });
        });

        // Store unsubscribe functions
        unsubscribeRefs.current = [startupProgressUnsubscribe, startupStatusUnsubscribe, startupCompleteUnsubscribe, startupErrorUnsubscribe, componentStatusUnsubscribe];
        return () => {
            // Clean up all subscriptions
            unsubscribeRefs.current.forEach(unsubscribe => unsubscribe());
        };
    }, [realTimeUpdates, steps.length]);

    // Enhanced progress simulation with realistic timing
    const simulateEnhancedProgress = (0, _react.useCallback)(() => {
        let currentStep = 0;
        let componentCount = 0;
        let successCount = 0;
        const _totalComponents = steps.reduce((sum, step) => sum + step.components.length, 0);
        const interval = setInterval(() => {
            let _steps$currentStep;
            setSteps(prevSteps => {
                const newSteps = [...prevSteps];
                if (currentStep < newSteps.length) {
                    const step = newSteps[currentStep];

                    // Start step if not started
                    if (step.status === 'pending') {
                        step.status = 'in-progress';
                        step.startTime = Date.now();
                        setCurrentPhase(step.name);
                    }

                    // Simulate component initialization within the step
                    const stepProgress = Math.min(100, step.progress + 100 / step.components.length);
                    step.progress = stepProgress;

                    // If component completed, increment counters
                    if (stepProgress > step.progress) {
                        componentCount++;
                        // Simulate some failures for non-critical components
                        if (step.critical || Math.random() > 0.1) {
                            successCount++;
                        }
                    }

                    // Complete step when progress reaches 100
                    if (step.progress >= 100) {
                        step.status = 'completed';
                        step.duration = Date.now() - step.startTime;
                        currentStep++;
                    }
                }
                return newSteps;
            });

            // Update overall progress
            const overallProgress = Math.min(100, currentStep / steps.length * 100 + (currentStep < steps.length ? (((_steps$currentStep = steps[currentStep]) === null || _steps$currentStep === void 0 ? void 0 : _steps$currentStep.progress) || 0) / steps.length : 0));
            setProgress(overallProgress);

            // Update performance metrics
            setPerformanceMetrics(prev => _objectSpread(_objectSpread({}, prev), {}, {
                totalComponents: componentCount,
                successfulComponents: successCount,
                failedComponents: componentCount - successCount,
                averageInitTime: componentCount > 0 ? (Date.now() - startTime) / componentCount : 0,
                estimatedTimeRemaining: overallProgress > 0 ? (Date.now() - startTime) / overallProgress * (100 - overallProgress) : 0
            }));

            // Call progress update callback
            if (onProgressUpdate) {
                let _steps$currentStep2;
                onProgressUpdate({
                    progress: overallProgress,
                    currentPhase: currentStep < steps.length ? (_steps$currentStep2 = steps[currentStep]) === null || _steps$currentStep2 === void 0 ? void 0 : _steps$currentStep2.name : 'Completed',
                    metrics: performanceMetrics
                });
            }

            // Complete when all steps are done
            if (currentStep >= steps.length) {
                setStatus('completed');
                setCurrentPhase('System Ready');
                clearInterval(interval);
            }
        }, realTimeUpdates ? 300 : 800); // Faster updates for real-time mode

        return () => clearInterval(interval);
    }, [steps, startTime, onProgressUpdate, realTimeUpdates, performanceMetrics]);
    (0, _react.useEffect)(() => {
        const cleanup = simulateEnhancedProgress();
        return cleanup;
    }, [simulateEnhancedProgress]);
    const getStatusIcon = (0, _react.useCallback)((stepStatus, isCritical = true) => {
        switch (stepStatus) {
            case 'completed':
                return /*#__PURE__*/_react.default.createElement(_iconsMaterial.CheckCircle, {
                    sx: {
                        color: '#4caf50',
                        fontSize: 16
                    }
                });
            case 'in-progress':
                return /*#__PURE__*/_react.default.createElement(_material.CircularProgress, {
                    size: 16,
                    sx: {
                        color: '#a259ff'
                    }
                });
            case 'error':
                return /*#__PURE__*/_react.default.createElement(_iconsMaterial.Error, {
                    sx: {
                        color: isCritical ? '#f44336' : '#ff9800',
                        fontSize: 16
                    }
                });
            case 'warning':
                return /*#__PURE__*/_react.default.createElement(_iconsMaterial.Warning, {
                    sx: {
                        color: '#ff9800',
                        fontSize: 16
                    }
                });
            default:
                return /*#__PURE__*/_react.default.createElement(_iconsMaterial.PlayCircle, {
                    sx: {
                        color: '#9e9e9e',
                        fontSize: 16
                    }
                });
        }
    }, []);
    const getStatusColor = (0, _react.useCallback)((stepStatus, isCritical = true) => {
        switch (stepStatus) {
            case 'completed':
                return '#4caf50';
            case 'in-progress':
                return '#a259ff';
            case 'error':
                return isCritical ? '#f44336' : '#ff9800';
            case 'warning':
                return '#ff9800';
            default:
                return '#9e9e9e';
        }
    }, []);
    const formatDuration = (0, _react.useCallback)(ms => {
        if (!ms) return '';
        if (ms < 1000) return `${Math.round(ms)}ms`;
        return `${(ms / 1000).toFixed(1)}s`;
    }, []);
    const formatTimeRemaining = (0, _react.useCallback)(ms => {
        if (!ms || ms < 0) return 'Calculating...';
        if (ms < 1000) return 'Almost done';
        if (ms < 60000) return `~${Math.round(ms / 1000)}s remaining`;
        return `~${Math.round(ms / 60000)}m remaining`;
    }, []);
    const performanceIndicators = (0, _react.useMemo)(() => [{
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.Speed, {
            sx: {
                fontSize: 16
            }
        }),
        label: 'Avg Init Time',
        value: formatDuration(performanceMetrics.averageInitTime),
        color: performanceMetrics.averageInitTime < 1000 ? '#4caf50' : performanceMetrics.averageInitTime < 3000 ? '#ff9800' : '#f44336'
    }, {
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.Timer, {
            sx: {
                fontSize: 16
            }
        }),
        label: 'Time Remaining',
        value: formatTimeRemaining(performanceMetrics.estimatedTimeRemaining),
        color: '#a259ff'
    }, {
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.NetworkCheck, {
            sx: {
                fontSize: 16
            }
        }),
        label: 'Components',
        value: `${performanceMetrics.successfulComponents}/${performanceMetrics.totalComponents}`,
        color: performanceMetrics.failedComponents > 0 ? '#ff9800' : '#4caf50'
    }], [performanceMetrics, formatDuration, formatTimeRemaining]);
    return /*#__PURE__*/_react.default.createElement(_material.Box, {
        className: className,
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            alignItems: 'center',
            mb: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#00eaff',
            flexGrow: 1
        }
    }, 'System Startup'), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            alignItems: 'center',
            gap: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888',
            cursor: 'pointer'
        },
        onClick: () => setDetailedView(!detailedView)
    }, detailedView ? 'Simple View' : 'Detailed View'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888'
        }
    }, Math.round(progress), '%'))), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            mb: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#a259ff',
            mb: 1
        }
    }, currentPhase), /*#__PURE__*/_react.default.createElement(_material.LinearProgress, {
        variant: 'determinate',
        value: progress,
        sx: {
            height: 8,
            borderRadius: 4,
            backgroundColor: 'rgba(162,89,255,0.2)',
            '& .MuiLinearProgress-bar': {
                backgroundColor: status === 'completed' ? '#4caf50' : '#a259ff',
                borderRadius: 4,
                transition: 'background-color 0.3s ease'
            }
        }
    })), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            mb: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 1
    }, performanceIndicators.map((indicator, _index) => /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 4,
        key: indicator.label
    }, /*#__PURE__*/_react.default.createElement(_material.Tooltip, {
        title: indicator.label,
        sx: {
            width: '100%'
        }
    }, /*#__PURE__*/_react.default.createElement('div', null, /*#__PURE__*/_react.default.createElement(_material.Card, {
        sx: {
            background: 'rgba(255,255,255,0.05)',
            border: '1px solid rgba(255,255,255,0.1)',
            p: 1,
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 0.5
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            color: indicator.color
        }
    }, indicator.icon), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'caption',
        sx: {
            color: indicator.color,
            fontSize: '0.75rem'
        }
    }, indicator.value))))))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 1
    }, /*#__PURE__*/_react.default.createElement(_framerMotion.AnimatePresence, null, steps.map((step, index) => /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        key: step.id
    }, /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        initial: {
            opacity: 0,
            x: -20
        },
        animate: {
            opacity: 1,
            x: 0
        },
        exit: {
            opacity: 0,
            x: 20
        },
        transition: {
            delay: index * 0.1
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Card, {
        sx: {
            background: step.status === 'in-progress' ? 'linear-gradient(135deg, rgba(162,89,255,0.15) 0%, rgba(162,89,255,0.08) 100%)' : 'linear-gradient(135deg, rgba(162,89,255,0.1) 0%, rgba(162,89,255,0.05) 100%)',
            border: `1px solid ${step.status === 'in-progress' ? 'rgba(162,89,255,0.5)' : 'rgba(162,89,255,0.3)'}`,
            transition: 'all 0.3s ease'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.CardContent, {
        sx: {
            py: 1.5,
            px: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            alignItems: 'center',
            gap: 1
        }
    }, getStatusIcon(step.status, step.critical), /*#__PURE__*/_react.default.createElement(_material.Box, null, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#fff',
            fontSize: '0.9rem'
        }
    }, step.name, !step.critical && /*#__PURE__*/_react.default.createElement(_material.Chip, {
        label: 'Optional',
        size: 'small',
        sx: {
            ml: 1,
            height: 16,
            fontSize: '0.7rem',
            backgroundColor: 'rgba(255,193,7,0.2)',
            color: '#ffc107'
        }
    })), /*#__PURE__*/_react.default.createElement(_material.Collapse, {
        in: detailedView
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'caption',
        sx: {
            color: '#888',
            display: 'block',
            mt: 0.5
        }
    }, step.components.join(', '))))), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            textAlign: 'right'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'caption',
        sx: {
            color: getStatusColor(step.status, step.critical)
        }
    }, step.status === 'completed' ? 'Complete' : step.status === 'in-progress' ? `${Math.round(step.progress)}%` : 'Pending'), detailedView && step.duration && /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'caption',
        sx: {
            color: '#888',
            display: 'block'
        }
    }, formatDuration(step.duration)))), step.status === 'in-progress' && /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            mt: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_material.LinearProgress, {
        variant: 'determinate',
        value: step.progress,
        sx: {
            height: 4,
            borderRadius: 2,
            backgroundColor: 'rgba(162,89,255,0.2)',
            '& .MuiLinearProgress-bar': {
                backgroundColor: '#a259ff',
                borderRadius: 2
            }
        }
    }))))))))), status === 'completed' && /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.5
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            textAlign: 'center',
            mt: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h5',
        sx: {
            color: '#4caf50',
            fontWeight: 600
        }
    }, 'System Ready'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888',
            mt: 1
        }
    }, 'All systems operational and ready for trading'), detailedView && /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'caption',
        sx: {
            color: '#666',
            display: 'block',
            mt: 1
        }
    }, 'Total startup time: ', formatDuration(Date.now() - startTime)))));
};
StartupProgressPanel.propTypes = {
    className: _propTypes.default.string,
    onProgressUpdate: _propTypes.default.func,
    realTimeUpdates: _propTypes.default.bool
};
const _default = exports.default = StartupProgressPanel;
/**
 * Integration test for Start Button IPC functionality
 * Verifies that the Start button properly connects to IPC system
 */

import React from 'react';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';

// Create a simple test component that mimics the Start button functionality
const TestStartButton = () => {
    const [isStarting, setIsStarting] = React.useState(false);
    const [error, setError] = React.useState(null);
    const [status, setStatus] = React.useState('stopped');

    const handleStart = async () => {
        setIsStarting(true);
        setError(null);

        try {
            // Simulate IPC calls like in AutonomousDashboard
            if (window.electronAPI?.initializeTrading) {
                const initResult = await window.electronAPI.initializeTrading();
                if (!initResult.success) {
                    throw new Error(initResult.error || 'Failed to initialize');
                }
            }

            if (window.electronAPI?.startBot) {
                const startResult = await window.electronAPI.startBot();
                if (!startResult.success) {
                    throw new Error(startResult.error || 'Failed to start');
                }
                setStatus('running');
            }
        } catch (err) {
            setError(err.message);
        } finally {
            setIsStarting(false);
        }
    };

    return (
        <div>
            <button
                onClick={handleStart}
                disabled={isStarting}
                data-testid="start-button"
            >
                {isStarting ? 'Starting...' : status === 'running' ? 'Running' : 'Start System'}
            </button>
            {error && <div data-testid="error-message">{error}</div>}
            <div data-testid="status">{status}</div>
        </div>
    );
};

describe('Start Button IPC Integration', () => {
    beforeEach(() => {
        // Reset window.electronAPI before each test
        delete window.electronAPI;
    });

    test('renders start button correctly', () => {
        render(<TestStartButton/>);
        expect(screen.getByTestId('start-button')).toBeInTheDocument();
        expect(screen.getByText('Start System')).toBeInTheDocument();
    });

    test('shows loading state when starting', async () => {
        // Mock successful IPC calls with delay
        window.electronAPI = {
            initializeTrading: jest.fn().mockImplementation(() =>
                new Promise(resolve => setTimeout(() => resolve({success: true}), 100)),
            ),
            startBot: jest.fn().mockImplementation(() =>
                new Promise(resolve => setTimeout(() => resolve({success: true}), 100)),
            )
        };

        render(<TestStartButton/>);

        const startButton = screen.getByTestId('start-button');
        fireEvent.click(startButton);

        // Should show loading state
        await waitFor(() => {
            expect(screen.getByText('Starting...')).toBeInTheDocument();
            expect(startButton).toBeDisabled();
        });

        // Should complete successfully
        await waitFor(() => {
            expect(screen.getByText('Running')).toBeInTheDocument();
            expect(screen.getByTestId('status')).toHaveTextContent('running');
        }, {timeout: 3000});
    });

    test('calls proper IPC channels in sequence', async () => {
        const mockInitialize = jest.fn().mockResolvedValue({success: true});
        const mockStart = jest.fn().mockResolvedValue({success: true});

        window.electronAPI = {
            initializeTrading: mockInitialize,
            startBot: mockStart
        };

        render(<TestStartButton/>);

        const startButton = screen.getByTestId('start-button');
        fireEvent.click(startButton);

        await waitFor(() => {
            expect(mockInitialize).toHaveBeenCalledTimes(1);
            expect(mockStart).toHaveBeenCalledTimes(1);
        });

        // Verify the calls were made in the correct order
        // Check that initialize was called before start by comparing call order
        const initializeCalls = mockInitialize.mock.invocationCallOrder;
        const startCalls = mockStart.mock.invocationCallOrder;
        expect(initializeCalls[0]).toBeLessThan(startCalls[0]);
    });

    test('handles initialization failure', async () => {
        window.electronAPI = {
            initializeTrading: jest.fn().mockResolvedValue({
                success: false,
                error: 'Database connection failed'
            }),
            startBot: jest.fn().mockResolvedValue({success: true})
        };

        render(<TestStartButton/>);

        const startButton = screen.getByTestId('start-button');
        fireEvent.click(startButton);

        await waitFor(() => {
            expect(screen.getByTestId('error-message')).toHaveTextContent('Database connection failed');
        });

        // Start bot should not be called if initialization fails
        expect(window.electronAPI.startBot).not.toHaveBeenCalled();
    });

    test('handles start bot failure', async () => {
        window.electronAPI = {
            initializeTrading: jest.fn().mockResolvedValue({success: true}),
            startBot: jest.fn().mockResolvedValue({
                success: false,
                error: 'Trading system unavailable'
            })
        };

        render(<TestStartButton/>);

        const startButton = screen.getByTestId('start-button');
        fireEvent.click(startButton);

        await waitFor(() => {
            expect(screen.getByTestId('error-message')).toHaveTextContent('Trading system unavailable');
        });

        expect(screen.getByTestId('status')).toHaveTextContent('stopped');
    });

    test('handles missing electronAPI gracefully', async () => {
        // No electronAPI available
        window.electronAPI = undefined;

        render(<TestStartButton/>);

        const startButton = screen.getByTestId('start-button');
        fireEvent.click(startButton);

        // Should complete without errors (no IPC calls made)
        await waitFor(() => {
            expect(startButton).not.toBeDisabled();
        });

        expect(screen.queryByTestId('error-message')).not.toBeInTheDocument();
    });

    test('handles IPC call exceptions', async () => {
        window.electronAPI = {
            initializeTrading: jest.fn().mockRejectedValue(new Error('Network timeout')),
            startBot: jest.fn().mockResolvedValue({success: true})
        };

        render(<TestStartButton/>);

        const startButton = screen.getByTestId('start-button');
        fireEvent.click(startButton);

        await waitFor(() => {
            expect(screen.getByTestId('error-message')).toHaveTextContent('Network timeout');
        });
    });
});

/**
 * UI Constants for electronTrader Frontend
 * Centralized constants for colors, themes, and styling to eliminate duplicated literals
 */

// Theme Colors (frequently duplicated across components)
export const THEME_COLORS = {
  PRIMARY: '#00eaff',
  SECONDARY: '#a259ff',
  SUCCESS: '#4caf50',
  ERROR: '#f44336',
  WARNING: '#ffc107',
  INFO: '#2196f3',
  BACKGROUND_DEFAULT: '#0f0f23',
  BACKGROUND_PAPER: '#181a20',
  SURFACE_PRIMARY: '#1a1a2e',
  SURFACE_SECONDARY: '#16213e',
  TEXT_PRIMARY: '#ffffff',
  TEXT_SECONDARY: '#b0b0b0',
  BORDER_COLOR: 'rgba(255,255,255,0.1)',
  DIVIDER: 'rgba(255,255,255,0.1)',
};

// CSS Values (commonly used)
export const CSS_VALUES = {
  ALIGN_CENTER: 'center',
  ALIGN_LEFT: 'left',
  ALIGN_RIGHT: 'right',
  JUSTIFY_CENTER: 'center',
  JUSTIFY_SPACE_BETWEEN: 'space-between',
  JUSTIFY_SPACE_AROUND: 'space-around',
  DISPLAY_FLEX: 'flex',
  DISPLAY_BLOCK: 'block',
  DISPLAY_INLINE_BLOCK: 'inline-block',
  DISPLAY_NONE: 'none',
  POSITION_RELATIVE: 'relative',
  POSITION_ABSOLUTE: 'absolute',
  POSITION_FIXED: 'fixed',
  OVERFLOW_HIDDEN: 'hidden',
  OVERFLOW_AUTO: 'auto',
  CURSOR_POINTER: 'pointer',
};

// Border Radius Values
export const BORDER_RADIUS = {
  SMALL: '4px',
  MEDIUM: '8px',
  LARGE: '12px',
  CARD: '16px',
  BUTTON: '8px',
  ROUND: '50%',
};

// Spacing Values
export const SPACING = {
  XS: '4px',
  SM: '8px',
  MD: '16px',
  LG: '24px',
  XL: '32px',
  XXL: '48px',
};

// Animation Durations
export const ANIMATION_DURATION = {
  FAST: '0.15s',
  NORMAL: '0.3s',
  SLOW: '0.5s',
  VERY_SLOW: '1s',
};

// Z-Index Values
export const Z_INDEX = {
  TOOLTIP,
  MODAL,
  SNACKBAR,
  DRAWER,
  APP_BAR,
};

// Breakpoints
export const BREAKPOINTS = {
  XS: '0px',
  SM: '600px',
  MD: '900px',
  LG: '1200px',
  XL: '1536px',
};

// Component Sizes
export const COMPONENT_SIZES = {
  SIDEBAR_WIDTH: '240px',
  HEADER_HEIGHT: '64px',
  CARD_MIN_HEIGHT: '200px',
  BUTTON_HEIGHT: '36px',
  INPUT_HEIGHT: '40px',
};

// Typography
export const TYPOGRAPHY = {
  FONT_FAMILY_PRIMARY: '"Roboto", "Helvetica", "Arial", sans-serif',
  FONT_FAMILY_MONOSPACE: '"Courier New", Courier, monospace',
  FONT_SIZE_SMALL: '12px',
  FONT_SIZE_MEDIUM: '14px',
  FONT_SIZE_LARGE: '16px',
  FONT_SIZE_XLARGE: '20px',
  FONT_WEIGHT_LIGHT,
  FONT_WEIGHT_REGULAR,
  FONT_WEIGHT_MEDIUM,
  FONT_WEIGHT_BOLD,
};

// Chart Colors
export const CHART_COLORS = {
  PRIMARY: '#00eaff',
  SECONDARY: '#a259ff',
  SUCCESS: '#4caf50',
  WARNING: '#ff9800',
  ERROR: '#f44336',
  GRADIENT_START: '#00eaff',
  GRADIENT_END: '#a259ff',
  GRID_COLOR: 'rgba(255,255,255,0.1)',
  AXIS_COLOR: '#888888',
};

// Status Colors
export const STATUS_COLORS = {
  ACTIVE: '#4caf50',
  INACTIVE: '#757575',
  PENDING: '#ff9800',
  ERROR: '#f44336',
  SUCCESS: '#4caf50',
  WARNING: '#ff9800',
};

// Material-UI Component Props (commonly used)
export const MUI_PROPS = {
  ELEVATION_LOW,
  ELEVATION_MEDIUM,
  ELEVATION_HIGH,
  ELEVATION_HIGHEST,
  VARIANT_CONTAINED: 'contained',
  VARIANT_OUTLINED: 'outlined',
  VARIANT_TEXT: 'text',
  SIZE_SMALL: 'small',
  SIZE_MEDIUM: 'medium',
  SIZE_LARGE: 'large',
  COLOR_PRIMARY: 'primary',
  COLOR_SECONDARY: 'secondary',
  COLOR_SUCCESS: 'success',
  COLOR_ERROR: 'error',
  COLOR_WARNING: 'warning',
  COLOR_INFO: 'info',
};

// Component Variants
export const COMPONENT_VARIANTS = {
  CARD_DEFAULT: 'default',
  CARD_QUANTUM: 'quantum',
  CARD_PREMIUM: 'premium',
  CARD_SUCCESS: 'success',
  CARD_ERROR: 'error',
  CARD_WARNING: 'warning',
  BUTTON_VIBRANT: 'vibrant',
  BUTTON_FUTURISTIC: 'futuristic',
};

// Export all constants
export default {
  THEME_COLORS,
  CSS_VALUES,
  BORDER_RADIUS,
  SPACING,
  ANIMATION_DURATION,
  Z_INDEX,
  BREAKPOINTS,
  COMPONENT_SIZES,
  TYPOGRAPHY,
  CHART_COLORS,
  STATUS_COLORS,
  MUI_PROPS,
  COMPONENT_VARIANTS,
};

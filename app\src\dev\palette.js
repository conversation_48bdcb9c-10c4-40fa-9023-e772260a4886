import React from 'react';
import {Category, Component, Palette, Variant} from '@react-buddy/ide-toolbox';
import MUIPalette from '@react-buddy/palette-mui';

// Import custom components for the palette
import HolographicCard from '../components/HolographicCard';
import FuturisticButton from '../components/FuturisticButton';
import VibrantButton from '../components/VibrantButton';
import BotCard from '../components/BotCard';
import ParticleBackground from '../components/ParticleBackground';

export const PaletteTree = () => (
  <Palette>
    <Category name="Material-UI">
      <MUIPalette/>
    </Category>
    <Category name="Custom Components">
      <Category name="Cards">
        <Component name="HolographicCard">
          <Variant name="Default">
            <HolographicCard variant="default" elevation="medium" sx={{}}>
                            Sample Content
            </HolographicCard>
          </Variant>
          <Variant name="Premium">
            <HolographicCard variant="premium" elevation="high" sx={{}}>
                            Premium Content
            </HolographicCard>
          </Variant>
          <Variant name="Success">
            <HolographicCard variant="success" elevation="medium" sx={{}}>
                            Success Content
            </HolographicCard>
          </Variant>
          <Variant name="Quantum">
            <HolographicCard variant="quantum" elevation="high" sx={{}}>
                            Quantum Content
            </HolographicCard>
          </Variant>
        </Component>
        <Component name="BotCard">
          <Variant name="Active Bot">
            <BotCard
              bot={{
                id: '1',
                name: 'Active Trading Bot',
                type: 'Grid',
                status: 'active',
                symbol: 'BTC/USDT',
                profit,
                profitPercent,
                totalTrades,
                successRate,
              }}
              onStart={() => {
              }}
              onStop={() => {
              }}
              onConfigure={() => {
              }}/>
          </Variant>
          <Variant name="Inactive Bot">
            <BotCard
              bot={{
                id: '2',
                name: 'Paused Bot',
                type: 'Grid',
                status: 'inactive',
                symbol: 'ETH/USDT',
                profit: -15.25,
                profitPercent: -1.2,
                totalTrades,
                successRate,
              }}
              onStart={() => {
              }}
              onStop={() => {
              }}
              onConfigure={() => {
              }}
            />
          </Variant>
        </Component>
      </Category>
      <Category name="Buttons">
        <Component name="FuturisticButton">
          <Variant name="Primary">
            <FuturisticButton variant="primary">Primary</FuturisticButton>
          </Variant>
          <Variant name="Secondary">
            <FuturisticButton variant="secondary">Secondary</FuturisticButton>
          </Variant>
          <Variant name="Success">
            <FuturisticButton variant="success">Success</FuturisticButton>
          </Variant>
          <Variant name="Error">
            <FuturisticButton variant="error">Error</FuturisticButton>
          </Variant>
        </Component>
        <Component name="VibrantButton">
          <Variant name="Primary">
            <VibrantButton color="primary">Primary</VibrantButton>
          </Variant>
          <Variant name="Secondary">
            <VibrantButton color="secondary">Secondary</VibrantButton>
          </Variant>
          <Variant name="Success">
            <VibrantButton color="success">Success</VibrantButton>
          </Variant>
        </Component>
      </Category>
      <Category name="Effects">
        <Component name="ParticleBackground">
          <Variant name="Trading Active">
            <div style={{height: '300px', position: 'relative'}}>
              <ParticleBackground tradingActive={true} intensity={1.5}/>
            </div>
          </Variant>
          <Variant name="Trading Inactive">
            <div style={{height: '300px', position: 'relative'}}>
              <ParticleBackground tradingActive={false} intensity={0.5}/>
            </div>
          </Variant>
        </Component>
      </Category>
    </Category>
  </Palette>
);

export function ExampleLoaderComponent() {
  return (
    <div>
            Loading example component...
    </div>
  );
}

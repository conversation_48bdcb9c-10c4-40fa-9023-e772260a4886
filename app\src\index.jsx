import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import {BrowserRouter} from 'react-router-dom';
import {DevSupport} from '@react-buddy/ide-toolbox';
import {ComponentPreviews, useInitial} from './dev';
import ErrorBoundary from './components/ErrorBoundary';
import globalErrorHandler from './utils/GlobalErrorHandler';

// Initialize global error handling
globalErrorHandler.initialize();

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
    <React.StrictMode>
        <ErrorBoundary>
            <DevSupport ComponentPreviews={ComponentPreviews}
                        useInitialHook={useInitial}
            >
                <BrowserRouter>
                    <App/>
                </BrowserRouter>
            </DevSupport>
        </ErrorBoundary>
    </React.StrictMode>,
);

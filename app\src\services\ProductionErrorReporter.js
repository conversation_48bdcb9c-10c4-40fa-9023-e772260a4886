/**
 * @fileoverview Production Error Reporting Service
 * Handles error reporting, crash reporting, and telemetry for production builds
 */

import { environmentConfig } from '../../config/environment';

class ProductionErrorReporter {
  constructor() {
    this.isProduction = environmentConfig.get('features.errorReporting');
    this.crashReporting = environmentConfig.get('features.crashReporting');
    this.telemetry = environmentConfig.get('features.telemetry');

    this.errorQueue = [];
    this.maxQueueSize = 100;
    this.flushInterval = 30000; // 30 seconds
    this.retryAttempts = 3;

    this.sessionId = this.generateSessionId();
    this.userId = this.getUserId();

    if (this.isProduction) {
      this.initialize();
    }
  }

  initialize() {
    // Set up global error handlers
    this.setupGlobalErrorHandlers();

    // Set up periodic error reporting
    this.setupPeriodicReporting();

    // Set up crash reporting
    if (this.crashReporting) {
      this.setupCrashReporting();
    }

    // Set up telemetry
    if (this.telemetry) {
      this.setupTelemetry();
    }

    console.info('Production error reporting initialized');
  }

  setupGlobalErrorHandlers() {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.reportError({
        type: 'unhandledRejection',
        error: event.reason,
        promise: event.promise,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent,
      });
    });

    // Handle global errors
    window.addEventListener('error', (event) => {
      this.reportError({
        type: 'globalError',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent,
      });
    });

    // Handle resource loading errors
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.reportError({
          type: 'resourceError',
          element: event.target.tagName,
          source: event.target.src || event.target.href,
          timestamp: new Date().toISOString(),
          url: window.location.href,
        });
      }
    }, true);
  }

  setupPeriodicReporting() {
    setInterval(() => {
      this.flushErrorQueue();
    }, this.flushInterval);

    // Flush on page unload
    window.addEventListener('beforeunload', () => {
      this.flushErrorQueue(true);
    });
  }

  setupCrashReporting() {
    // Monitor for potential crashes
    let lastHeartbeat = Date.now();

    setInterval(() => {
      const now = Date.now();
      const timeSinceLastHeartbeat = now - lastHeartbeat;

      if (timeSinceLastHeartbeat > 60000) { // 1 minute
        this.reportCrash({
          type: 'potentialFreeze',
          lastHeartbeat: new Date(lastHeartbeat).toISOString(),
          detectedAt: new Date(now).toISOString(),
          duration: timeSinceLastHeartbeat,
        });
      }

      lastHeartbeat = now;
    }, 30000);

    // Monitor memory usage
    if ('memory' in performance) {
      setInterval(() => {
        const memory = performance.memory;
        const memoryUsage = memory.usedJSHeapSize / memory.jsHeapSizeLimit;

        if (memoryUsage > 0.9) {
          this.reportError({
            type: 'highMemoryUsage',
            memoryUsage: memoryUsage,
            usedJSHeapSize: memory.usedJSHeapSize,
            jsHeapSizeLimit: memory.jsHeapSizeLimit,
            timestamp: new Date().toISOString(),
          });
        }
      }, 60000);
    }
  }

  setupTelemetry() {
    // Track application usage
    this.trackEvent('app_start', {
      version: environmentConfig.get('app.version'),
      environment: environmentConfig.get('app.environment'),
      timestamp: new Date().toISOString(),
    });

    // Track performance metrics
    if ('getEntriesByType' in performance) {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
          this.trackEvent('app_performance', {
            loadTime: navigation.loadEventEnd - navigation.fetchStart,
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
            firstPaint: this.getFirstPaint(),
            timestamp: new Date().toISOString(),
          });
        }
      }, 5000);
    }
  }

  reportError(errorData) {
    if (!this.isProduction) return;

    const enrichedError = {
      ...errorData,
      sessionId: this.sessionId,
      userId: this.userId,
      timestamp: errorData.timestamp || new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth,
      },
      memory: this.getMemoryInfo(),
      connection: this.getConnectionInfo(),
    };

    this.addToQueue(enrichedError);
  }

  reportCrash(crashData) {
    if (!this.crashReporting) return;

    const crashReport = {
      type: 'crash',
      ...crashData,
      sessionId: this.sessionId,
      userId: this.userId,
      timestamp: new Date().toISOString(),
      systemInfo: this.getSystemInfo(),
      memory: this.getMemoryInfo(),
      performance: this.getPerformanceInfo(),
    };

    // Send crash report immediately
    this.sendErrorReport(crashReport, true);
  }

  trackEvent(eventName, eventData) {
    if (!this.telemetry) return;

    const event = {
      type: 'telemetry',
      event: eventName,
      data: eventData,
      sessionId: this.sessionId,
      userId: this.userId,
      timestamp: new Date().toISOString(),
    };

    this.addToQueue(event);
  }

  addToQueue(item) {
    this.errorQueue.push(item);

    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift(); // Remove oldest item
    }

    // Flush immediately for critical errors
    if (item.type === 'crash' || item.type === 'globalError') {
      this.flushErrorQueue(true);
    }
  }

  async flushErrorQueue(immediate = false) {
    if (this.errorQueue.length === 0) return;

    const errors = [...this.errorQueue];
    this.errorQueue = [];

    try {
      await this.sendErrorReport(errors, immediate);
    } catch (error) {
      // Re-queue errors if sending failed
      this.errorQueue.unshift(...errors);
      console.warn('Failed to send error report:', error);
    }
  }

  async sendErrorReport(data, immediate = false) {
    const payload = {
      errors: Array.isArray(data) ? data : [data],
      metadata: {
        sessionId: this.sessionId,
        userId: this.userId,
        timestamp: new Date().toISOString(),
        version: environmentConfig.get('app.version'),
        environment: environmentConfig.get('app.environment'),
      },
    };

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.getApiKey(),
      },
      body: JSON.stringify(payload),
    };

    if (immediate && 'sendBeacon' in navigator) {
      // Use sendBeacon for immediate sending (e.g., on page unload)
      navigator.sendBeacon('/api/errors', options.body);
    } else {
      // Use fetch for regular reporting
      const response = await fetch('/api/errors', options);

      if (!response.ok) {
        throw new Error(`Error reporting failed: ${response.status}`);
      }
    }
  }

  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  getUserId() {
    // Generate or retrieve user ID (anonymized)
    let userId = localStorage.getItem('user_id');
    if (!userId) {
      userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      localStorage.setItem('user_id', userId);
    }
    return userId;
  }

  getApiKey() {
    // In production, this would be injected during build
    return process.env.REACT_APP_ERROR_REPORTING_API_KEY || 'dev-key';
  }

  getMemoryInfo() {
    if ('memory' in performance) {
      return {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
      };
    }
    return null;
  }

  getConnectionInfo() {
    if ('connection' in navigator) {
      const connection = navigator.connection;
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData,
      };
    }
    return null;
  }

  getSystemInfo() {
    return {
      platform: navigator.platform,
      language: navigator.language,
      languages: navigator.languages,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      hardwareConcurrency: navigator.hardwareConcurrency,
      maxTouchPoints: navigator.maxTouchPoints,
    };
  }

  getPerformanceInfo() {
    if ('getEntriesByType' in performance) {
      const navigation = performance.getEntriesByType('navigation')[0];
      return navigation ? {
        loadTime: navigation.loadEventEnd - navigation.fetchStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
        firstPaint: this.getFirstPaint(),
      } : null;
    }
    return null;
  }

  getFirstPaint() {
    if ('getEntriesByType' in performance) {
      const paintEntries = performance.getEntriesByType('paint');
      const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
      return firstPaint ? firstPaint.startTime : null;
    }
    return null;
  }

  // Public API for manual error reporting
  reportManualError(error, context = {}) {
    this.reportError({
      type: 'manualError',
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : error,
      context,
      timestamp: new Date().toISOString(),
    });
  }

  // Public API for tracking custom events
  track(eventName, eventData = {}) {
    this.trackEvent(eventName, eventData);
  }

  // Public API for setting user context
  setUserContext(context) {
    this.userContext = { ...this.userContext, ...context };
  }

  // Public API for setting tags
  setTags(tags) {
    this.tags = { ...this.tags, ...tags };
  }
}

// Create singleton instance
const productionErrorReporter = new ProductionErrorReporter();

export default productionErrorReporter;
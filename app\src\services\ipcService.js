/**
 * @fileoverview IPC Service
 * @description Service for communicating with Electron main process
 */

class IPCService {
  constructor() {
    this.isElectron = typeof window !== 'undefined' && window.electronAPI;
    this.retryAttempts = 3;
    this.retryDelay = 1000;
  }

  isAvailable() {
    return this.isElectron && window.electronAPI;
  }

  isElectronEnvironment() {
    return this.isElectron;
  }

  async testConnectivity() {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const result = await window.electronAPI.healthCheck();
      return result && result.status === 'healthy';
    } catch (error) {
      console.error('IPC connectivity test failed:', error);
      return false;
    }
  }

  on(channel, callback) {
    if (!this.isAvailable()) {
      return () => {};
    }

    // For Electron IPC, we typically don't have a direct 'on' method
    // This would be handled through the preload script
    return () => {};
  }

  async safeIPCCall(fn, channel, ...args) {
    if (!this.isAvailable()) {
      console.warn(`IPC not available for channel: ${channel}`);
      return { success: false, error: { message: 'IPC not available' } };
    }

    try {
      const result = await fn(...args);
      return { success: true, data: result };
    } catch (error) {
      console.error(`IPC call failed for ${channel}:`, error);
      return {
        success: false,
        error: {
          message: error.message,
          stack: error.stack,
        },
      };
    }
  }

  async quickIPCCall(fn, channel, ...args) {
    return this.safeIPCCall(fn, channel, ...args);
  }

  async standardIPCCall(fn, channel, ...args) {
    return this.safeIPCCall(fn, channel, ...args);
  }

  async criticalIPCCall(fn, channel, ...args) {
    // Critical calls with retry logic
    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      const result = await this.safeIPCCall(fn, channel, ...args);

      if (result.success) {
        return result;
      }

      if (attempt < this.retryAttempts) {
        console.warn(`Critical IPC call failed, retrying (${attempt}/${this.retryAttempts})...`);
        await new Promise(resolve => setTimeout(resolve, this.retryDelay));
      }
    }

    return { success: false, error: { message: `Critical IPC call failed after ${this.retryAttempts} attempts` } };
  }

  async tradingIPCCall(fn, channel, ...args) {
    return this.criticalIPCCall(fn, channel, ...args);
  }

  // Trading system methods
  async startBot() {
    return this.tradingIPCCall(
      () => window.electronAPI.startBot(),
      'start-bot',
    );
  }

  async stopBot() {
    return this.tradingIPCCall(
      () => window.electronAPI.stopBot(),
      'stop-bot',
    );
  }

  async getBotStatus() {
    return this.standardIPCCall(
      () => window.electronAPI.getBotStatus(),
      'get-bot-status',
    );
  }

  async getSystemStatus() {
    return this.standardIPCCall(
      () => window.electronAPI.getSystemStatus(),
      'get-system-status',
    );
  }

  async healthCheck() {
    return this.quickIPCCall(
      () => window.electronAPI.healthCheck(),
      'health-check',
    );
  }

  async getPortfolioSummary() {
    return this.standardIPCCall(
      () => window.electronAPI.getPortfolioSummary(),
      'get-portfolio-summary',
    );
  }

  async getTradingStats() {
    return this.standardIPCCall(
      () => window.electronAPI.getTradingStats(),
      'get-trading-stats',
    );
  }

  async getMarketOverview() {
    return this.standardIPCCall(
      () => window.electronAPI.getMarketOverview(),
      'get-market-overview',
    );
  }

  async getRiskMetrics() {
    return this.standardIPCCall(
      () => window.electronAPI.getRiskMetrics(),
      'get-risk-metrics',
    );
  }

  async getPriceHistory() {
    return this.standardIPCCall(
      () => window.electronAPI.getPriceHistory(),
      'get-price-history',
    );
  }

  async getAssetAllocation() {
    return this.standardIPCCall(
      () => window.electronAPI.getAssetAllocation(),
      'get-asset-allocation',
    );
  }

  async getTradeHistory() {
    return this.standardIPCCall(
      () => window.electronAPI.getTradeHistory(),
      'get-trade-history',
    );
  }

  async getActiveBots() {
    return this.standardIPCCall(
      () => window.electronAPI.getActiveBots(),
      'get-active-bots',
    );
  }

  async getSystemHealth() {
    return this.standardIPCCall(
      () => window.electronAPI.getSystemHealth(),
      'get-system-health',
    );
  }

  async getSystemMetrics() {
    return this.standardIPCCall(
      () => window.electronAPI.getSystemMetrics(),
      'get-system-metrics',
    );
  }

  async getSystemAlerts() {
    return this.standardIPCCall(
      () => window.electronAPI.getSystemAlerts(),
      'get-system-alerts',
    );
  }

  async getRealTimeStatus() {
    return this.standardIPCCall(
      () => window.electronAPI.getRealTimeStatus(),
      'get-real-time-status',
    );
  }

  async getComponentHealth() {
    return this.standardIPCCall(
      () => window.electronAPI.getComponentHealth(),
      'get-component-health',
    );
  }

  // Configuration methods
  async getConfig(key) {
    return this.standardIPCCall(
      () => window.electronAPI.getConfig(key),
      'get-config',
    );
  }

  async updateConfig(key, value) {
    return this.standardIPCCall(
      () => window.electronAPI.updateConfig(key, value),
      'update-config',
    );
  }

  async getSettings() {
    return this.standardIPCCall(
      () => window.electronAPI.getSettings(),
      'get-settings',
    );
  }

  async saveSettings(settings) {
    return this.standardIPCCall(
      () => window.electronAPI.saveSettings(settings),
      'save-settings',
    );
  }

  // Market data methods
  async getCoins() {
    return this.standardIPCCall(
      () => window.electronAPI.getCoins(),
      'get-coins',
    );
  }

  async getMarketData(symbol) {
    return this.standardIPCCall(
      () => window.electronAPI.getMarketData(symbol),
      'get-market-data',
    );
  }

  async saveCoin(coin) {
    return this.standardIPCCall(
      () => window.electronAPI.saveCoin(coin),
      'save-coin',
    );
  }

  // Initialize trading system
  async initializeTrading() {
    return this.criticalIPCCall(
      () => window.electronAPI.initializeTrading(),
      'initialize-trading',
    );
  }

  // Generic method for any IPC call
  async call(method, ...args) {
    if (!this.isAvailable()) {
      return { success: false, error: { message: 'IPC not available' } };
    }

    if (!window.electronAPI[method]) {
      return { success: false, error: { message: `Method ${method} not available` } };
    }

    return this.standardIPCCall(
      () => window.electronAPI[method](...args),
      method,
    );
  }
}

// Create singleton instance
const ipcService = new IPCService();

export default ipcService;
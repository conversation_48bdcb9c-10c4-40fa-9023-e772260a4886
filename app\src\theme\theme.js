import {createTheme} from '@mui/material/styles';

const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {main: '#00eaff'},
    secondary: {main: '#a259ff'},
    background: {default: '#181a20', paper: '#23272f'},
    success: {main: '#00ff85'},
    error: {main: '#ff3b3b'},
    text: {primary: '#fff', secondary: '#a0aec0'},
  },
  shape: {borderRadius},
  typography: {
    fontFamily: 'Inter, Roboto, Arial, sans-serif',
    fontWeightBold,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          fontWeight,
          boxShadow: '0 2px 8px rgba(0,234,255,0.15)',
        },
      },
    },
  },
});
export default theme;

const logger = require('./logger.js');

class AnimationOptimizer {
  constructor() {
    this.metrics = {
      frameDrops: 0,
      averageFrameTime: 16.67,
      animationCount: 0,
    };

    this.activeAnimations = new Map();
    this.rafCallbacks = new Set();
    this.performanceThresholds = {
      maxFrameTime: 16.67, // 60fps = 16.67ms per frame
      maxActiveAnimations: 10,
      memoryThreshold: 100 * 1024 * 1024, // 100MB
    };

    this.prefersReducedMotion = false;
    this.gpuAcceleratedProperties = [];

    this.initializeOptimizations();
  }

  /**
     * Initialize performance optimizations
     */
  initializeOptimizations() {
    // Enable GPU acceleration for supported properties
    this.gpuAcceleratedProperties = [
      'transform',
      'opacity',
      'filter',
      'backdrop-filter',
      'will-change',
    ];

    // Setup performance monitoring
    this.setupPerformanceMonitoring();

    // Setup reduced motion preferences
    this.setupAccessibilityPreferences();

    return this;
  }

  /**
     * Setup performance monitoring for animations
     */
  setupPerformanceMonitoring() {
    let lastTime = performance.now();
    let frameCount = 0;
    let totalTime = 0;

    const monitorFrame = (currentTime) => {
      const frameTime = currentTime - lastTime;

      if (frameTime > this.performanceThresholds.maxFrameTime) {
        this.metrics.frameDrops++;
      }

      totalTime += frameTime;
      frameCount++;

      if (frameCount % 60 === 0) { // Update metrics every 60 frames
        this.metrics.averageFrameTime = totalTime / frameCount;
        this.metrics.animationCount = this.activeAnimations.size;

        // Trigger optimization if performance is poor
        if (this.metrics.averageFrameTime > this.performanceThresholds.maxFrameTime * 1.5) {
          this.optimizeAnimations();
        }

        // Reset for next cycle
        totalTime = 0;
        frameCount = 0;
      }

      lastTime = currentTime;
      requestAnimationFrame(monitorFrame);
    };

    requestAnimationFrame(monitorFrame);
    return this;
  }

  /**
     * Setup accessibility preferences for reduced motion
     */
  setupAccessibilityPreferences() {
    if (typeof window !== 'undefined') {
      this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion)').matches;

      // Listen for changes
      window.matchMedia('(prefers-reduced-motion)').addEventListener('change', (e) => {
        this.prefersReducedMotion = e.matches;
        if (this.prefersReducedMotion) {
          this.disableNonEssentialAnimations();
        }
      });
    }
    return this;
  }

  /**
     * Create optimized CSS keyframes
     */
  createOptimizedKeyframes(name, keyframes, options = {}) {
    const {
      duration = 1000,
      easing = 'cubic-bezier(0.4, 0, 0.2, 1)',
      fillMode = 'both',
      iterations = 1,
      useGPU = true,
    } = options;

    // Add GPU acceleration hints
    const optimizedKeyframes = {...keyframes};
    if (useGPU) {
      Object.keys(optimizedKeyframes).forEach((key) => {
        const frame = optimizedKeyframes[key];
        if (typeof frame === 'object') {
          frame.willChange = 'transform, opacity';
          frame.backfaceVisibility = 'hidden';
          frame.transform = frame.transform || 'translateZ(0)';
          frame.perspective = '1000px';
        }
      });
    }

    // Create CSS animation
    const animationCSS = `
            @keyframes ${name} {
                ${Object.entries(optimizedKeyframes).map(([key, frame]) => `
                    ${key} {
                        ${typeof frame === 'object'
    ? Object.entries(frame).map(([prop, value]) => `${prop}: ${value};`).join('\n                        ')
    : frame
}
                    }
                `).join('\n                ')}
            }

            .${name} {
                animation: ${name} ${duration}ms ${easing} ${fillMode};
                animation-iteration-count: ${iterations};
            }
        `;

    // Inject CSS if not already present
    if (typeof document !== 'undefined' && !document.querySelector(`style[data-animation="${name}"]`)) {
      const style = document.createElement('style');
      style.setAttribute('data-animation', name);
      style.textContent = animationCSS;
      document.head.appendChild(style);
    }

    return name;
  }

  /**
     * Create performance-optimized Framer Motion variants
     */
  createMotionVariants(name, variants, options = {}) {
    const {
      useGPU = true,
      reducedMotion = null,
      staggerChildren = 0,
    } = options;

    const optimizedVariants = {};

    Object.entries(variants).forEach(([key, variant]) => {
      optimizedVariants[key] = {
        ...variant,
        transition: {
          type: 'tween',
          ease: 'easeOut',
          duration: 0.3,
          staggerChildren: 0,
          ...variant.transition,
        },
      };

      // Add GPU acceleration
      if (useGPU) {
        optimizedVariants[key] = {
          ...optimizedVariants[key],
          willChange: 'transform, opacity',
          backfaceVisibility: 'hidden',
        };
      }

      // Handle reduced motion
      if (this.prefersReducedMotion && reducedMotion) {
        optimizedVariants[key] = {
          ...optimizedVariants[key],
          ...reducedMotion[key],
          transition: {
            ...optimizedVariants[key].transition,
            duration: 0.001,
          },
        };
      }
    });

    // Register animation for monitoring
    this.activeAnimations.set(name, {
      type: 'framer-motion',
      variants,
      startTime: Date.now(),
    });

    return optimizedVariants;
  }

  /**
     * Create optimized particle animation
     */
  createParticleAnimation(particleCount, containerSize, options = {}) {
    const {
      speed = 1,
      maxParticles = 100,
      recycleParticles = true,
    } = options;

    // Limit particle count for performance
    const actualCount = Math.min(particleCount, maxParticles);

    if (this.prefersReducedMotion) {
      return {
        particles: [],
        animate: () => {},
        cleanup: () => {},
      };
    }

    const particles = Array.from({length: actualCount}, (_, i) => ({
      id: i,
      x: Math.random() * containerSize.width,
      y: Math.random() * containerSize.height,
      vx: (Math.random() - 0.5) * speed,
      vy: (Math.random() - 0.5) * speed,
      size: Math.random() * 4 + 1,
      opacity: Math.random() * 0.8 + 0.2,
      hue: Math.random() * 360,
    }));

    let animationId;
    const animate = (callback) => {
      const updateParticles = () => {
        particles.forEach((particle) => {
          particle.x += particle.vx;
          particle.y += particle.vy;

          // Recycle particles that move off-screen
          if (recycleParticles) {
            if (particle.x < 0 || particle.x > containerSize.width ||
                            particle.y < 0 || particle.y > containerSize.height) {
              particle.x = Math.random() * containerSize.width;
              particle.y = Math.random() * containerSize.height;
            }
          }
        });

        callback(particles);
        animationId = requestAnimationFrame(updateParticles);
      };

      updateParticles();
    };

    const cleanup = () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };

    return {particles, animate, cleanup};
  }

  /**
     * Create optimized scroll animation
     */
  createScrollAnimation(element, options = {}) {
    const {
      threshold = 0.1,
      rootMargin = '0px',
    } = options;

    if (this.prefersReducedMotion || typeof window === 'undefined') {
      return {
        cleanup: () => {},
      };
    }

    // Use Intersection Observer for better performance
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, {
      threshold,
      rootMargin,
    });

    observer.observe(element);

    return {
      cleanup: () => observer.disconnect(),
    };
  }

  /**
     * Optimize animations based on performance metrics
     */
  optimizeAnimations() {
    const {averageFrameTime, animationCount} = this.metrics;

    logger.info(`🎯 Animation Optimizer: ${animationCount} animations (avg frame time: ${averageFrameTime.toFixed(2)}ms)`);

    // Reduce animation complexity if performance is poor
    if (averageFrameTime > this.performanceThresholds.maxFrameTime * 2) {
      this.disableNonEssentialAnimations();
    }

    // Limit concurrent animations
    if (animationCount > this.performanceThresholds.maxActiveAnimations) {
      this.pauseOldestAnimations();
    }
  }

  /**
     * Disable non-essential animations for performance
     */
  disableNonEssentialAnimations() {
    if (typeof document === 'undefined') return;

    document.body.classList.add('reduced-animations');

    // Add CSS to disable heavy animations
    if (!document.querySelector('#reduced-animations-style')) {
      const style = document.createElement('style');
      style.id = 'reduced-animations-style';
      style.textContent = `
                .reduced-animations * {
                    animation-duration: 0.1s !important;
                    animation-delay: 0s !important;
                    transition-duration: 0.1s !important;
                    transition-delay: 0s !important;
                }

                .reduced-animations .particle-system,
                .reduced-animations .complex-animation {
                    display: none !important;
                }
            `;
      document.head.appendChild(style);
    }
  }

  /**
     * Pause oldest animations to free up resources
     */
  pauseOldestAnimations() {
    const animations = Array.from(this.activeAnimations.entries())
      .sort(([, a], [, b]) => a.startTime - b.startTime)
      .slice(0, Math.floor(this.activeAnimations.size * 0.3)); // Pause oldest 30%

    animations.forEach(([name]) => {
      this.pauseAnimation(name);
    });
  }

  /**
     * Pause specific animation
     */
  pauseAnimation(name) {
    const animation = this.activeAnimations.get(name);
    if (animation) {
      animation.paused = true;
      // Add logic to actually pause the animation based on type
    }
  }

  /**
     * Resume specific animation
     */
  resumeAnimation(name) {
    const animation = this.activeAnimations.get(name);
    if (animation) {
      animation.paused = false;
      // Add logic to resume the animation based on type
    }
  }

  /**
     * Cleanup animation resources
     */
  cleanup(name) {
    if (this.activeAnimations.has(name)) {
      this.activeAnimations.delete(name);
    }
  }

  /**
     * Get current performance metrics
     */
  getMetrics() {
    return {
      ...this.metrics,
      prefersReducedMotion: this.prefersReducedMotion,
      activeAnimations: this.activeAnimations.size,
    };
  }

  /**
     * Create optimized CSS for 60fps animations
     */
  getOptimizedCSS() {
    return `
            /* 60fps Optimized Animations */
            
            /* GPU Acceleration Base */
            .gpu-accelerated {
                transform: translateZ(0);
                backface-visibility: hidden;
                perspective: 1000px;
                will-change: transform, opacity;
            }
            
            /* Optimized Transitions */
            .smooth-transition {
                transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                           opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }
            
            /* Particle System Optimizations */
            .particle {
                position: absolute;
                border-radius: 50%;
                pointer-events: none;
                will-change: transform, opacity;
                transform: translateZ(0);
            }
            
            /* Reduced Motion Support */
            @media (prefers-reduced-motion: reduce) {
                *,
                *::before,
                *::after {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                    scroll-behavior: auto !important;
                }
                
                .particle-system,
                .complex-animation {
                    display: none !important;
                }
            }
            
            /* Performance-based optimizations */
            .performance-mode * {
                animation-duration: 0.15s !important;
                transition-duration: 0.15s !important;
            }
            
            /* Memory optimization */
            .memory-optimized .particle {
                transform: translate3d(var(--x, 0), var(--y, 0), 0);
            }
        `;
  }
}

// Create singleton instance
const animationOptimizer = new AnimationOptimizer();

module.exports = animationOptimizer;

const createOptimizedKeyframes = (name, keyframes, options) =>
  animationOptimizer.createOptimizedKeyframes(name, keyframes, options);

module.exports.createOptimizedKeyframes = createOptimizedKeyframes;

const createMotionVariants = (name, variants, options) =>
  animationOptimizer.createMotionVariants(name, variants, options);

module.exports.createMotionVariants = createMotionVariants;

const createParticleAnimation = (count, size, options) =>
  animationOptimizer.createParticleAnimation(count, size, options);

module.exports.createParticleAnimation = createParticleAnimation;

const createScrollAnimation = (element, options) =>
  animationOptimizer.createScrollAnimation(element, options);

module.exports.createScrollAnimation = createScrollAnimation;

const getAnimationMetrics = () => animationOptimizer.getMetrics();
module.exports.getAnimationMetrics = getAnimationMetrics;

// Inject optimized CSS
if (typeof document !== 'undefined' && !document.querySelector('#animation-optimizer-styles')) {
  const optimizedCSS = animationOptimizer.getOptimizedCSS();
  const style = document.createElement('style');
  style.id = 'animation-optimizer-styles';
  style.textContent = optimizedCSS;
  document.head.appendChild(style);
}

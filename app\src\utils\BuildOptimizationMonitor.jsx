'use strict';

import React, {
    useState,
    useRef,
    useEffect,
} from 'react';
import logger from './logger.js';

const IS_BROWSER = typeof window !== 'undefined';
const IS_DEV = process.env.NODE_ENV === 'development';

/**
 * @class BuildOptimizationMonitor
 * @description Monitors build performance, including bundle size, chunk loading, and lazy component performance.
 */
class BuildOptimizationMonitor {
    constructor() {
        /** @type {Map<string, {loadTime: number, size: number, timestamp: number}>} */
        this.chunkLoadTimes = new Map();
        /** @type {Map<string, {loadTime: number, timestamp: number, error: string|null, networkType: string|null}>} */
        this.lazyLoadMetrics = new Map();
        /** @type {{effectiveType: string|null, downlink: number|null, rtt: number|null, saveData: boolean|null}} */
        this.networkMetrics = {
            effectiveType: null,
            downlink: null,
            rtt: null,
            saveData: null,
        };

        this.initialize();
    }

    /**
     * @description Initializes the monitoring systems for network and resource loading.
     */
    initialize() {
        if (!IS_BROWSER) return;

        this.monitorNetworkConditions();
        this.monitorResourceLoading();
    }

    /**
     * @description Sets up network condition monitoring.
     */
    monitorNetworkConditions() {
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        if (connection) {
            const updateNetworkMetrics = () => {
                this.networkMetrics = {
                    effectiveType: connection.effectiveType,
                    downlink: connection.downlink,
                    rtt: connection.rtt,
                    saveData: connection.saveData,
                };
            };
            updateNetworkMetrics();
            connection.addEventListener('change', updateNetworkMetrics);
        }
    }

    /**
     * @description Sets up resource loading monitoring using PerformanceObserver.
     */
    monitorResourceLoading() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver(list => {
                list.getEntries().forEach(entry => {
                    if (entry.entryType === 'resource' && entry.name.includes('.chunk.js')) {
                        const resourceEntry = /** @type {PerformanceResourceTiming} */ (entry);
                        this.chunkLoadTimes.set(entry.name, {
                            loadTime: resourceEntry.duration,
                            size: resourceEntry.transferSize || resourceEntry.encodedBodySize,
                            timestamp: performance.now(),
                        });
                    }
                });
            });
            observer.observe({
                entryTypes: ['resource'],
            });
        }
    }

    /**
     * @description Tracks the performance of a lazily loaded component.
     * @param {string} componentName - The name of the component.
     * @param {number} startTime - The start time of the load.
     * @param {number} endTime - The end time of the load.
     * @param {Error|null} [error=null] - Any error that occurred during loading.
     * @returns {{loadTime: number, timestamp: number, error: string|null, networkType: string|null}}
     */
    trackLazyLoad(componentName, startTime, endTime, error = null) {
        const loadTime = endTime - startTime;
        const metrics = {
            loadTime,
            timestamp: Date.now(),
            error: error ? error.message : null,
            networkType: this.networkMetrics.effectiveType,
        };
        this.lazyLoadMetrics.set(componentName, metrics);

        if (IS_DEV) {
            if (error) {
                logger.warn(`❌ ${componentName} failed to load:`, error);
            } else {
                const status = loadTime < 500 ? '✅' : loadTime < 1000 ? '⚠️' : '❌';
                logger.info(`${status} ${componentName} loaded in ${loadTime.toFixed(2)}ms`);
            }
        }
        return metrics;
    }

    /**
     * @description Generates a full performance report.
     * @returns {object}
     */
    generateReport() {
        const optimization = this.calculateOptimization();
        const recommendations = this.getRecommendations();
        const lazyMetrics = Object.fromEntries(this.lazyLoadMetrics);

        return {
            timestamp: new Date().toISOString(),
            bundleOptimization: optimization,
            lazyLoadingMetrics: lazyMetrics,
            networkConditions: this.networkMetrics,
            recommendations,
            summary: {
                totalComponents: this.lazyLoadMetrics.size,
                averageLazyLoadTime: this.lazyLoadMetrics.size > 0 ?
                    Array.from(this.lazyLoadMetrics.values()).reduce((sum, m) => sum + m.loadTime, 0) / this.lazyLoadMetrics.size :
                    0,
                performanceScore: this.calculatePerformanceScore(),
            },
        };
    }

    /**
     * @description Calculates bundle size and loading optimizations.
     * @returns {object}
     */
    calculateOptimization() {
        const chunks = Array.from(this.chunkLoadTimes.entries());
        const totalSize = chunks.reduce((sum, [, metrics]) => sum + (metrics.size || 0), 0);
        const totalLoadTime = chunks.reduce((sum, [, metrics]) => sum + metrics.loadTime, 0);
        return {
            totalChunks: chunks.length,
            totalSize: `${(totalSize / 1024 / 1024).toFixed(2)} MB`,
            averageLoadTime: chunks.length > 0 ? `${(totalLoadTime / chunks.length).toFixed(2)}ms` : '0ms',
            networkType: this.networkMetrics.effectiveType,
            chunks: chunks.map(([name, metrics]) => ({
                name: name.split('/').pop(),
                size: `${((metrics.size || 0) / 1024).toFixed(2)} KB`,
                loadTime: `${metrics.loadTime.toFixed(2)}ms`,
            })),
        };
    }

    /**
     * @description Generates performance recommendations based on collected metrics.
     * @returns {Array<object>}
     */
    getRecommendations() {
        const recommendations = [];
        this.lazyLoadMetrics.forEach((metrics, component) => {
            if (metrics.loadTime > 1000) {
                recommendations.push({
                    type: 'performance',
                    component,
                    issue: `Slow loading time: ${metrics.loadTime.toFixed(2)}ms`,
                    suggestion: 'Consider code splitting or reducing component size.',
                });
            }
        });

        this.chunkLoadTimes.forEach((metrics, chunk) => {
            const sizeKB = (metrics.size || 0) / 1024;
            if (sizeKB > 500) {
                recommendations.push({
                    type: 'bundle-size',
                    chunk: chunk.split('/').pop(),
                    issue: `Large chunk size: ${sizeKB.toFixed(2)} KB`,
                    suggestion: 'Consider further code splitting or tree shaking.',
                });
            }
        });

        if (['slow-2g', '2g'].includes(this.networkMetrics.effectiveType)) {
            recommendations.push({
                type: 'network',
                issue: 'Slow network detected.',
                suggestion: 'Enable aggressive lazy loading and reduce initial bundle size.',
            });
        }
        return recommendations;
    }

    /**
     * @description Calculates an overall performance score from 0 to 100.
     * @returns {number}
     */
    calculatePerformanceScore() {
        let score = 100;
        this.lazyLoadMetrics.forEach(metrics => {
            if (metrics.loadTime > 1000) score -= 10;
            else if (metrics.loadTime > 500) score -= 5;
            if (metrics.error) score -= 15;
        });

        this.chunkLoadTimes.forEach(metrics => {
            const sizeKB = (metrics.size || 0) / 1024;
            if (sizeKB > 500) score -= 10;
            else if (sizeKB > 250) score -= 5;
        });

        if (this.networkMetrics.effectiveType === 'slow-2g') score -= 20;
        else if (this.networkMetrics.effectiveType === '2g') score -= 10;

        return Math.max(0, Math.round(score));
    }
}

const monitor = new BuildOptimizationMonitor();

/**
 * @description React hook to monitor the mount and unmount lifecycle of a component for performance.
 * @param {string} componentName - The name of the component to monitor.
 * @returns {object|null} The performance metrics for the component.
 */
export const useBuildOptimizationMonitor = componentName => {
    const [metrics, setMetrics] = useState(null);
    const startTimeRef = useRef(null);

    useEffect(() => {
        startTimeRef.current = performance.now();
        return () => {
            if (startTimeRef.current) {
                const endTime = performance.now();
                const componentMetrics = monitor.trackLazyLoad(componentName, startTimeRef.current, endTime);
                setMetrics(componentMetrics);
            }
        };
    }, [componentName]);

    return metrics;
};

/**
 * @description A React component that displays the performance monitoring panel.
 * @returns {React.ReactElement|null}
 */
export const PerformanceMonitoringPanel = () => {
    const [report, setReport] = useState(null);
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        if (IS_DEV) {
            const interval = setInterval(() => {
                setReport(monitor.generateReport());
            }, 5000);
            return () => clearInterval(interval);
        }
    }, []);

    if (!IS_DEV || !report) {
        return null;
    }

    const panelStyle = {
        position: 'fixed',
        bottom: '10px',
        right: '10px',
        zIndex: 9999,
        backgroundColor: 'rgba(0,0,0,0.8)',
        color: 'white',
        padding: '10px',
        borderRadius: '5px',
        fontSize: '12px',
        maxWidth: '300px',
        fontFamily: 'sans-serif',
    };

    const headerStyle = {
        cursor: 'pointer',
        fontWeight: 'bold',
    };

    return (
        <div style={panelStyle}>
            <div onClick={() => setIsVisible(!isVisible)} style={headerStyle}>
                Performance Score: {report.summary.performanceScore}/100 {isVisible ? '▼' : '▲'}
            </div>
            {isVisible && (
                <div style={{marginTop: '10px'}}>
                    <div>Bundle Size: {report.bundleOptimization.totalSize}</div>
                    <div>Avg Load Time: {report.bundleOptimization.averageLoadTime}</div>
                    <div>Network: {report.networkConditions.effectiveType || 'N/A'}</div>
                    <div>Components: {report.summary.totalComponents}</div>
                    {report.recommendations.length > 0 && (
                        <div style={{marginTop: '5px', color: '#ffc107'}}>
                            <strong>Recommendations({report.recommendations.length}):</strong>
                            <ul style={{paddingLeft: '15px', margin: '5px 0 0'}}>
                                {report.recommendations.map((rec, i) => (
                                    <li key={i}>{rec.issue}</li>
                                ))}
                            </ul>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export { BuildOptimizationMonitor, monitor };

export default {
    BuildOptimizationMonitor: monitor,
    useBuildOptimizationMonitor,
    PerformanceMonitoringPanel,
};
'use strict';

const logger = require('./logger.js');

/**
 * @class ComponentRecoveryManager
 * @description Manages the recovery of different system components.
 */
class ComponentRecoveryManager {
  constructor() {
    this.componentStates = new Map();
    this.recoveryAttempts = new Map();
    this.criticalComponents = new Set(['TradingOrchestrator', 'DatabaseManager', 'ExchangeConnector', 'RiskManager', 'PositionManager']);
    this.optionalComponents = new Set(['SentimentAnalyzer', 'AIOptimizer', 'AdvancedAnalytics', 'NotificationSystem', 'PerformanceTracker', 'MarketNews', 'SocialSignals']);
    this.recoveryStrategies = new Map([
      ['immediate-restart', {
        maxRetries: 3,
        retryDelay: 1000,
        backoffMultiplier: 2,
        timeout: 5000,
      }],
      ['graceful-restart', {
        maxRetries: 3,
        retryDelay: 5000,
        backoffMultiplier: 2,
        timeout: 15000,
      }],
      ['component-isolation', {
        maxRetries: 1,
        retryDelay: 0,
        backoffMultiplier: 1,
        timeout: 10000,
      }],
      ['graceful-degradation', {
        maxRetries: 0,
        retryDelay: 0,
        backoffMultiplier: 1,
        timeout: 0,
      }],
    ]);
    this.componentMetrics = new Map();
    this.healthCheckInterval = null;
    this.startHealthMonitoring();
  }

  /**
     * Handle component initialization failure with automatic recovery
     * @param {string} componentName - The name of the component that failed.
     * @param {Error} error - The error that occurred.
     * @param {object} [context={}] - Additional context about the failure.
     * @returns {Promise<boolean>} - True if recovery was successful or degradation was handled.
     */
  async handleComponentInitializationFailure(componentName, error, context = {}) {
    logger.warn(`Component initialization failed: ${componentName}`, {
      error,
    });
    const componentState = this.getComponentState(componentName);
    const isCritical = this.isCriticalComponent(componentName);
    const isOptional = this.isOptionalComponent(componentName);

    // Update component state
    componentState.status = 'failed';
    componentState.lastError = error;
    componentState.lastFailureTime = Date.now();
    componentState.failureCount++;

    // Report the failure
    logger.error('Component initialization failure reported:', {
      type: 'component_initialization_failure',
      component: componentName,
      error: error.message,
      stack: error.stack,
      critical: isCritical,
      optional: isOptional,
      context,
      timestamp: new Date().toISOString(),
    });

    // Determine recovery strategy
    const strategy = this.selectRecoveryStrategy(componentName, error, context);
    if (strategy === 'graceful-degradation') {
      return this.handleGracefulDegradation(componentName, error, context);
    }

    // Attempt recovery
    const recoverySuccess = await this.attemptComponentRecovery(componentName, error, strategy, context);
    if (!recoverySuccess && isCritical) {
      // Critical component failed to recover - escalate
      await this.handleCriticalComponentFailure(componentName, error, context);
    }
    return recoverySuccess;
  }

  /**
     * Select appropriate recovery strategy based on component and error characteristics
     * @param {string} componentName - The name of the component.
     * @param {Error} error - The error that occurred.
     * @param {object} _context - Additional context.
     * @returns {string} - The name of the selected recovery strategy.
     */
  selectRecoveryStrategy(componentName, error, _context) {
    const isCritical = this.isCriticalComponent(componentName);
    const isOptional = this.isOptionalComponent(componentName);
    const componentState = this.getComponentState(componentName);
    const errorMessage = error.message.toLowerCase();

    // For optional components with multiple failures, degrade gracefully
    if (isOptional && componentState.failureCount >= 2) {
      return 'graceful-degradation';
    }

    // For non-recoverable errors, degrade gracefully
    if (this.isNonRecoverableError(error)) {
      return 'graceful-degradation';
    }

    // For critical components, use immediate restart, but escalate to isolation
    if (isCritical) {
      return componentState.failureCount >= 3 ? 'component-isolation' : 'immediate-restart';
    }

    // For network/timeout errors, use graceful restart
    if (errorMessage.includes('network') || errorMessage.includes('timeout') || errorMessage.includes('connection')) {
      return 'graceful-restart';
    }

    // Default strategy
    return 'graceful-restart';
  }

  /**
     * Attempt component recovery using the specified strategy
     * @param {string} componentName - The name of the component.
     * @param {Error} error - The original error.
     * @param {string} strategyName - The name of the recovery strategy.
     * @param {object} context - Additional context.
     * @returns {Promise<boolean>} - True if recovery was successful.
     */
  async attemptComponentRecovery(componentName, error, strategyName, context) {
    const strategy = this.recoveryStrategies.get(strategyName);
    if (!strategy) {
      logger.error(`Unknown recovery strategy: ${strategyName}`);
      return false;
    }
    const recoveryId = `${componentName}_${Date.now()}`;
    const {
      maxRetries,
    } = strategy;
    let retryCount = 0;
    let success = false;
    logger.info(`Starting recovery for ${componentName} using strategy: ${strategyName}`);
    while (retryCount < maxRetries && !success) {
      try {
        // Calculate delay with exponential backoff
        const delay = strategy.retryDelay * (strategy.backoffMultiplier ** retryCount);
        if (delay > 0) {
          logger.info(`Waiting ${delay}ms before retry ${retryCount + 1}/${maxRetries} for ${componentName}`);
          await this.sleep(delay);
        }

        // Attempt recovery
        success = await this.executeRecoveryStep(componentName, strategyName, context);
        if (success) {
          logger.info(`Recovery successful for ${componentName} on attempt ${retryCount + 1}`);
          this.updateComponentState(componentName, 'recovered');
          break;
        } else {
          retryCount++;
          logger.warn(`Recovery attempt ${retryCount} failed for ${componentName}`);
        }
      } catch (recoveryError) {
        retryCount++;
        logger.error(`Recovery attempt ${retryCount} threw error for ${componentName}:`, {
          recoveryError,
        });
      }
    }

    // Record recovery attempt
    this.recordRecoveryAttempt(recoveryId, componentName, strategyName, success, retryCount);
    return success;
  }

  /**
     * Execute specific recovery step based on strategy
     * @param {string} componentName - The name of the component.
     * @param {string} strategyName - The name of the recovery strategy.
     * @param {object} context - Additional context.
     * @returns {Promise<boolean>} - True if the step was successful.
     */
  async executeRecoveryStep(componentName, strategyName, context) {
    switch (strategyName) {
    case 'immediate-restart':
      return this.executeImmediateRestart(componentName, context);
    case 'graceful-restart':
      return this.executeGracefulRestart(componentName, context);
    case 'component-isolation':
      return this.executeComponentIsolation(componentName, context);
    default:
      logger.error(`Unknown recovery step: ${strategyName}`);
      return false;
    }
  }

  /**
     * Execute immediate component restart
     * @param {string} componentName - The name of the component.
     * @param {object} context - Additional context.
     * @returns {Promise<boolean>} - True if restart was successful.
     */
  async executeImmediateRestart(componentName, context) {
    logger.info(`Executing immediate restart for ${componentName}`);
    try {
      // Notify main process to restart component
      if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipcRenderer) {
        const result = await window.electronAPI.ipcRenderer.invoke('restart-component', {
          componentName,
          strategy: 'immediate',
          context,
          timestamp: new Date().toISOString(),
        });
        return result && result.success;
      }

      // Fallback for components that can be restarted locally
      return this.restartComponentLocally(componentName, context);
    } catch (error) {
      logger.error(`Immediate restart failed for ${componentName}:`, {
        error,
      });
      return false;
    }
  }

  /**
     * Execute graceful component restart
     * @param {string} componentName - The name of the component.
     * @param {object} context - Additional context.
     * @returns {Promise<boolean>} - True if restart was successful.
     */
  async executeGracefulRestart(componentName, context) {
    logger.info(`Executing graceful restart for ${componentName}`);
    try {
      // Save component state before restart
      await this.saveComponentState(componentName);

      // Notify main process to restart component gracefully
      if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipcRenderer) {
        const result = await window.electronAPI.ipcRenderer.invoke('restart-component', {
          componentName,
          strategy: 'graceful',
          context,
          timestamp: new Date().toISOString(),
        });
        if (result && result.success) {
          // Restore component state after restart
          await this.restoreComponentState(componentName);
          return true;
        }
      }
      return false;
    } catch (error) {
      logger.error(`Graceful restart failed for ${componentName}:`, {
        error,
      });
      return false;
    }
  }

  /**
     * Execute component isolation (run in safe mode)
     * @param {string} componentName - The name of the component.
     * @param {object} context - Additional context.
     * @returns {Promise<boolean>} - True if isolation was successful.
     */
  async executeComponentIsolation(componentName, context) {
    logger.info(`Executing component isolation for ${componentName}`);
    try {
      // Isolate component and run in safe mode
      if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipcRenderer) {
        const result = await window.electronAPI.ipcRenderer.invoke('isolate-component', {
          componentName,
          context,
          timestamp: new Date().toISOString(),
        });
        return result && result.success;
      }
      return false;
    } catch (error) {
      logger.error(`Component isolation failed for ${componentName}:`, {
        error,
      });
      return false;
    }
  }

  /**
     * Handle graceful degradation for optional components
     * @param {string} componentName - The name of the component.
     * @param {Error} error - The error that occurred.
     * @param {object} context - Additional context.
     * @returns {Promise<boolean>} - Always returns true.
     */
  async handleGracefulDegradation(componentName, error, context) {
    logger.info(`Gracefully degrading component: ${componentName}`);
    const componentState = this.getComponentState(componentName);
    componentState.status = 'degraded';
    componentState.degradationReason = error.message;
    componentState.degradationTime = Date.now();

    // Notify application about component degradation
    if (typeof window !== 'undefined') {
      const degradationEvent = new CustomEvent('componentDegraded', {
        detail: {
          componentName,
          error,
          context,
          timestamp: new Date().toISOString(),
        },
      });
      window.dispatchEvent(degradationEvent);
    }


    // Apply component-specific degradation strategies
    await this.applyDegradationStrategy(componentName, context);

    // Report degradation
    logger.warn('Component graceful degradation reported:', {
      type: 'component_graceful_degradation',
      component: componentName,
      error: error.message,
      context,
      timestamp: new Date().toISOString(),
    });
    return true;
  }

  /**
     * Apply specific degradation strategies for different components
     * @param {string} componentName - The name of the component.
     * @param {object} _context - Additional context.
     * @returns {Promise<void>}
     */
  async applyDegradationStrategy(componentName, _context) {
    const strategies = {
      SentimentAnalyzer: () => this.disableFeature('sentiment-analysis'),
      AIOptimizer: () => this.disableFeature('ai-optimization'),
      AdvancedAnalytics: () => this.enableBasicAnalytics(),
      NotificationSystem: () => this.disableNotifications(),
      PerformanceTracker: () => this.enableBasicMetrics(),
      MarketNews: () => this.useCachedNews(),
      SocialSignals: () => this.disableFeature('social-signals'),
    };
    const strategy = strategies[componentName];
    if (strategy) {
      try {
        await strategy();
        logger.info(`Applied degradation strategy for ${componentName}`);
      } catch (error) {
        logger.error(`Failed to apply degradation strategy for ${componentName}:`, {
          error,
        });
      }
    }
  }

  /**
     * Handle critical component failure
     * @param {string} componentName - The name of the component.
     * @param {Error} error - The error that occurred.
     * @param {object} context - Additional context.
     */
  async handleCriticalComponentFailure(componentName, error, context) {
    logger.error(`Critical component failure: ${componentName}`, {
      error,
    });

    // Emit critical failure event
    if (typeof window !== 'undefined') {
      const criticalEvent = new CustomEvent('criticalComponentFailure', {
        detail: {
          componentName,
          error,
          context,
          timestamp: new Date().toISOString(),
        },
      });
      window.dispatchEvent(criticalEvent);
    }

    // Report critical failure
    logger.error('Critical component failure reported:', {
      type: 'critical_component_failure',
      component: componentName,
      error: error.message,
      stack: error.stack,
      context,
      critical: true,
      timestamp: new Date().toISOString(),
    });

    // Trigger emergency protocols if needed
    const componentState = this.getComponentState(componentName);
    if (componentState.failureCount >= 3) {
      await this.triggerEmergencyProtocols(componentName, error, context);
    }
  }

  /**
     * Trigger emergency protocols for repeated critical failures
     * @param {string} componentName - The name of the component.
     * @param {Error} error - The error that occurred.
     * @param {object} context - Additional context.
     * @returns {Promise<void>}
     */
  async triggerEmergencyProtocols(componentName, error, context) {
    logger.error(`Triggering emergency protocols for ${componentName}`);
    try {
      if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipcRenderer) {
        await window.electronAPI.ipcRenderer.invoke('trigger-emergency-protocols', {
          componentName,
          error,
          context,
          timestamp: new Date().toISOString(),
        });
      }

      // Emit emergency event
      if (typeof window !== 'undefined') {
        const emergencyEvent = new CustomEvent('emergencyProtocolsTriggered', {
          detail: {
            componentName,
            error,
            context,
            timestamp: new Date().toISOString(),
          },
        });
        window.dispatchEvent(emergencyEvent);
      }
    } catch (emergencyError) {
      logger.error('Failed to trigger emergency protocols:', {
        emergencyError,
      });
    }
  }

  /**
     * Start health monitoring for all components
     */
  startHealthMonitoring() {
    if (this.healthCheckInterval) return;
    this.healthCheckInterval = setInterval(() => {
      this.performHealthChecks();
    }, 30000); // Check every 30 seconds

    logger.info('Component health monitoring started');
  }

  /**
     * Perform health checks on all tracked components
     */
  async performHealthChecks() {
    const healthResults = new Map();
    for (const [componentName, state] of this.componentStates) {
      try {
        const health = await this.checkComponentHealth(componentName);
        healthResults.set(componentName, health);

        // Update component metrics
        this.updateComponentMetrics(componentName, health);

        // Trigger recovery if component is unhealthy
        if (health.status === 'unhealthy' && state.status !== 'recovering') {
          await this.handleUnhealthyComponent(componentName, health);
        }
      } catch (error) {
        logger.error(`Health check failed for ${componentName}:`, {
          error,
        });
        healthResults.set(componentName, {
          status: 'error',
          error,
          timestamp: new Date().toISOString(),
        });
      }
    }

    // Emit health check results
    if (typeof window !== 'undefined') {
      const healthEvent = new CustomEvent('componentHealthCheck', {
        detail: {
          results: Object.fromEntries(healthResults),
          timestamp: new Date().toISOString(),
        },
      });
      window.dispatchEvent(healthEvent);
    }
  }

  /**
     * Check health of a specific component
     * @param {string} componentName - The name of the component.
     * @returns {Promise<object>} - The health status of the component.
     */
  async checkComponentHealth(componentName) {
    const componentState = this.getComponentState(componentName);
    const now = Date.now();

    // Calculate component uptime and error rate
    const uptime = componentState.startTime ? now - componentState.startTime : 0;
    const errorRate = uptime > 0 ? componentState.failureCount / (uptime / 60000) : 0; // errors per minute

    let status = 'healthy';
    let reason = 'Component operating normally';
    if (componentState.status === 'failed') {
      status = 'unhealthy';
      reason = `Component failed: ${componentState.lastError?.message}`;
    } else if (componentState.status === 'degraded') {
      status = 'degraded';
      reason = `Component degraded: ${componentState.degradationReason}`;
    } else if (errorRate > 2) {
      status = 'unhealthy';
      reason = `High error rate: ${errorRate.toFixed(2)} errors/min`;
    } else if (errorRate > 0.5) {
      status = 'warning';
      reason = `Elevated error rate: ${errorRate.toFixed(2)} errors/min`;
    }
    return {
      status,
      reason,
      errorRate,
      uptime,
      failureCount: componentState.failureCount,
      lastFailure: componentState.lastFailureTime,
      timestamp: new Date().toISOString(),
    };
  }

  /**
     * Handle unhealthy component detection
     * @param {string} componentName - The name of the component.
     * @param {object} health - The health status object.
     */
  async handleUnhealthyComponent(componentName, health) {
    logger.warn(`Unhealthy component detected: ${componentName}`, {
      health,
    });
    const componentState = this.getComponentState(componentName);

    // Mark as recovering to prevent multiple recovery attempts
    componentState.status = 'recovering';

    // Attempt automatic recovery
    const recoverySuccess = await this.handleComponentInitializationFailure(componentName, new Error(health.reason), {
      source: 'health-check',
      health,
    });
    if (!recoverySuccess) {
      componentState.status = 'failed';
    }
  }

  // Utility methods
  getComponentState(componentName) {
    if (!this.componentStates.has(componentName)) {
      this.componentStates.set(componentName, {
        status: 'unknown',
        startTime: Date.now(),
        failureCount: 0,
        lastError: null,
        lastFailureTime: null,
        degradationReason: null,
        degradationTime: null,
      });
    }
    return this.componentStates.get(componentName);
  }

  updateComponentState(componentName, status) {
    const state = this.getComponentState(componentName);
    state.status = status;
    if (status === 'recovered') {
      state.failureCount = 0;
      state.lastError = null;
      state.lastFailureTime = null;
    }
  }

  updateComponentMetrics(componentName, health) {
    if (!this.componentMetrics.has(componentName)) {
      this.componentMetrics.set(componentName, {
        healthChecks: 0,
        healthyChecks: 0,
        unhealthyChecks: 0,
        averageErrorRate: 0,
      });
    }
    const metrics = this.componentMetrics.get(componentName);
    metrics.healthChecks++;
    if (health.status === 'healthy') {
      metrics.healthyChecks++;
    } else {
      metrics.unhealthyChecks++;
    }

    // Update average error rate
    metrics.averageErrorRate = (metrics.averageErrorRate * (metrics.healthChecks - 1) + health.errorRate) / metrics.healthChecks;
  }

  recordRecoveryAttempt(recoveryId, componentName, strategy, success, retryCount) {
    const attempt = {
      id: recoveryId,
      componentName,
      strategy,
      success,
      retryCount,
      timestamp: new Date().toISOString(),
    };
    if (!this.recoveryAttempts.has(componentName)) {
      this.recoveryAttempts.set(componentName, []);
    }
    const attempts = this.recoveryAttempts.get(componentName);
    attempts.push(attempt);

    // Keep only last 50 attempts per component
    if (attempts.length > 50) {
      attempts.shift();
    }
    logger.info('Recovery attempt recorded:', {
      attempt,
    });
  }

  isCriticalComponent(componentName) {
    return this.criticalComponents.has(componentName) || Array.from(this.criticalComponents).some(critical => componentName.toLowerCase().includes(critical.toLowerCase()));
  }

  isOptionalComponent(componentName) {
    return this.optionalComponents.has(componentName) || Array.from(this.optionalComponents).some(optional => componentName.toLowerCase().includes(optional.toLowerCase()));
  }

  isNonRecoverableError(error) {
    const nonRecoverablePatterns = ['permission denied', 'access denied', 'authentication failed', 'invalid credentials', 'corrupted', 'fatal error', 'out of memory'];
    const errorMessage = error.message.toLowerCase();
    return nonRecoverablePatterns.some(pattern => errorMessage.includes(pattern));
  }

  // Component-specific degradation methods
  async disableFeature(featureName) {
    logger.info(`Disabling feature: ${featureName}`);
    // Implementation would disable specific features
  }

  async enableBasicAnalytics() {
    logger.info('Enabling basic analytics mode');
    // Implementation would switch to basic analytics
  }

  async disableNotifications() {
    logger.info('Disabling notifications');
    // Implementation would disable notification system
  }

  async enableBasicMetrics() {
    logger.info('Enabling basic metrics only');
    // Implementation would switch to basic metrics
  }

  async useCachedNews() {
    logger.info('Using cached news data');
    // Implementation would switch to cached news
  }

  /**
     * @param {string} componentName
     * @param {object} _context
     * @returns {Promise<boolean>}
     * @memberof ComponentRecoveryManager
     */
  async restartComponentLocally(componentName, _context) {
    logger.info(`Attempting local restart for ${componentName}`);
    // Implementation would restart components that can be restarted locally
    return false; // Most components require main process restart
  }

  /**
     * @param {string} componentName
     * @returns {Promise<void>}
     * @memberof ComponentRecoveryManager
     */
  async saveComponentState(componentName) {
    logger.info(`Saving state for ${componentName}`);
    // Implementation would save component state before restart
  }

  /**
     * @param {string} componentName
     * @returns {Promise<void>}
     * @memberof ComponentRecoveryManager
     */
  async restoreComponentState(componentName) {
    logger.info(`Restoring state for ${componentName}`);
    // Implementation would restore component state after restart
  }

  /**
     * @param {number} ms
     * @returns {Promise<void>}
     * @memberof ComponentRecoveryManager
     */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Public API methods
  getComponentStatus(componentName) {
    return this.getComponentState(componentName);
  }

  getAllComponentStatuses() {
    return Object.fromEntries(this.componentStates);
  }

  getRecoveryHistory(componentName) {
    return this.recoveryAttempts.get(componentName) || [];
  }

  getComponentMetrics(componentName) {
    return this.componentMetrics.get(componentName) || null;
  }

  getAllMetrics() {
    return Object.fromEntries(this.componentMetrics);
  }

  stopHealthMonitoring() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      logger.info('Component health monitoring stopped');
    }
  }

  shutdown() {
    this.stopHealthMonitoring();
    logger.info('ComponentRecoveryManager shutdown complete');
  }
}

// Create singleton instance
const componentRecoveryManager = new ComponentRecoveryManager();
module.exports = componentRecoveryManager;
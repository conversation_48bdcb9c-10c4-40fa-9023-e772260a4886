/**
 * Data Cache Manager
 *
 * Provides intelligent caching for frequently accessed data
 * with automatic invalidation and memory management.
 */

class DataCacheManager {
  constructor(config = {}) {
    this.config = {
      maxSize: config.maxSize || 1000,
      defaultTTL: config.defaultTTL || 300000, // 5 minutes
      cleanupInterval: config.cleanupInterval || 60000, // 1 minute
      enablePersistence: config.enablePersistence !== false,
      persistenceKey: config.persistenceKey || 'app-data-cache',
      enableCompression: config.enableCompression || false,
      maxMemoryUsage: config.maxMemoryUsage || 50 * 1024 * 1024, // 50MB
      ...config,
    };

    this.cache = new Map();
    this.accessTimes = new Map();
    this.hitCounts = new Map();
    this.memoryUsage = 0;
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      cleanups: 0,
    };

    this.initialized = false;
    this.cleanupInterval = null;
  }

  /**
     * Initialize the cache manager
     */
  async initialize() {
    try {
      console.log('🔄 Initializing Data Cache Manager...');

      // Load persisted cache if enabled
      if (this.config.enablePersistence) {
        await this.loadPersistedCache();
      }

      // Start cleanup interval
      this.startCleanupInterval();

      this.initialized = true;
      console.log('✅ Data Cache Manager initialized successfully');

      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Data Cache Manager:', error);
      throw error;
    }
  }

  /**
     * Get data from cache
     */
  get(key, options = {}) {
    if (!this.initialized) {
      console.warn('Cache manager not initialized');
      return null;
    }

    const entry = this.cache.get(key);

    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // Check if entry has expired
    if (this.isExpired(entry)) {
      this.delete(key);
      this.stats.misses++;
      return null;
    }

    // Update access statistics
    this.accessTimes.set(key, Date.now());
    this.hitCounts.set(key, (this.hitCounts.get(key) || 0) + 1);
    this.stats.hits++;

    // Return decompressed data if needed
    return this.config.enableCompression ? this.decompress(entry.data) : entry.data;
  }

  /**
     * Set data in cache
     */
  set(key, data, options = {}) {
    if (!this.initialized) {
      console.warn('Cache manager not initialized');
      return false;
    }

    const ttl = options.ttl || this.config.defaultTTL;
    const priority = options.priority || 'normal';
    const tags = options.tags || [];

    // Compress data if enabled
    const processedData = this.config.enableCompression ? this.compress(data) : data;

    // Calculate memory usage
    const dataSize = this.calculateSize(processedData);

    // Check memory limits
    if (this.memoryUsage + dataSize > this.config.maxMemoryUsage) {
      this.evictLeastUsed();
    }

    // Check cache size limits
    if (this.cache.size >= this.config.maxSize) {
      this.evictLeastUsed();
    }

    const entry = {
      data: processedData,
      timestamp: Date.now(),
      ttl,
      priority,
      tags,
      size: dataSize,
      accessCount: 0,
    };

    // Remove existing entry if present
    if (this.cache.has(key)) {
      const oldEntry = this.cache.get(key);
      this.memoryUsage -= oldEntry.size;
    }

    // Add new entry
    this.cache.set(key, entry);
    this.accessTimes.set(key, Date.now());
    this.hitCounts.set(key, 0);
    this.memoryUsage += dataSize;
    this.stats.sets++;

    // Persist cache if enabled
    if (this.config.enablePersistence) {
      this.persistCache();
    }

    return true;
  }

  /**
     * Delete data from cache
     */
  delete(key) {
    const entry = this.cache.get(key);
    if (entry) {
      this.memoryUsage -= entry.size;
      this.cache.delete(key);
      this.accessTimes.delete(key);
      this.hitCounts.delete(key);
      this.stats.deletes++;
      return true;
    }
    return false;
  }

  /**
     * Clear cache by tags
     */
  clearByTags(tags) {
    let cleared = 0;

    for (const [key, entry] of this.cache) {
      if (entry.tags && entry.tags.some(tag => tags.includes(tag))) {
        this.delete(key);
        cleared++;
      }
    }

    console.log(`🧹 Cleared ${cleared} cache entries by tags:`, tags);
    return cleared;
  }

  /**
     * Clear all cache
     */
  clear() {
    const size = this.cache.size;
    this.cache.clear();
    this.accessTimes.clear();
    this.hitCounts.clear();
    this.memoryUsage = 0;

    console.log(`🧹 Cleared all ${size} cache entries`);
    return size;
  }

  /**
     * Get or set data with automatic caching
     */
  async getOrSet(key, fetchFunction, options = {}) {
    // Try to get from cache first
    const cached = this.get(key, options);
    if (cached !== null) {
      return cached;
    }

    try {
      // Fetch data
      const data = await fetchFunction();

      // Cache the result
      this.set(key, data, options);

      return data;
    } catch (error) {
      console.error(`Failed to fetch data for key '${key}':`, error);
      throw error;
    }
  }

  /**
     * Batch get multiple keys
     */
  getBatch(keys) {
    const results = {};

    for (const key of keys) {
      const value = this.get(key);
      if (value !== null) {
        results[key] = value;
      }
    }

    return results;
  }

  /**
     * Batch set multiple key-value pairs
     */
  setBatch(entries, options = {}) {
    let success = 0;

    for (const [key, value] of Object.entries(entries)) {
      if (this.set(key, value, options)) {
        success++;
      }
    }

    return success;
  }

  /**
     * Check if entry has expired
     */
  isExpired(entry) {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  /**
     * Evict least recently used entries
     */
  evictLeastUsed() {
    if (this.cache.size === 0) return;

    // Sort by access time and hit count
    const entries = Array.from(this.cache.entries()).sort((a, b) => {
      const [keyA, entryA] = a;
      const [keyB, entryB] = b;

      const accessTimeA = this.accessTimes.get(keyA) || 0;
      const accessTimeB = this.accessTimes.get(keyB) || 0;
      const hitCountA = this.hitCounts.get(keyA) || 0;
      const hitCountB = this.hitCounts.get(keyB) || 0;

      // Prioritize by priority level first
      const priorityOrder = { high: 3, normal: 2, low: 1 };
      const priorityA = priorityOrder[entryA.priority] || 2;
      const priorityB = priorityOrder[entryB.priority] || 2;

      if (priorityA !== priorityB) {
        return priorityA - priorityB; // Lower priority first
      }

      // Then by hit count (lower first)
      if (hitCountA !== hitCountB) {
        return hitCountA - hitCountB;
      }

      // Finally by access time (older first)
      return accessTimeA - accessTimeB;
    });

    // Evict 10% of entries or at least 1
    const evictCount = Math.max(1, Math.floor(this.cache.size * 0.1));

    for (let i = 0; i < evictCount && i < entries.length; i++) {
      const [key] = entries[i];
      this.delete(key);
      this.stats.evictions++;
    }

    console.log(`🗑️ Evicted ${evictCount} least used cache entries`);
  }

  /**
     * Start cleanup interval
     */
  startCleanupInterval() {
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
     * Cleanup expired entries
     */
  cleanup() {
    let cleaned = 0;
    const now = Date.now();

    for (const [key, entry] of this.cache) {
      if (this.isExpired(entry)) {
        this.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 Cleaned up ${cleaned} expired cache entries`);
      this.stats.cleanups++;
    }

    // Persist cache after cleanup if enabled
    if (this.config.enablePersistence && cleaned > 0) {
      this.persistCache();
    }
  }

  /**
     * Calculate approximate size of data
     */
  calculateSize(data) {
    try {
      return JSON.stringify(data).length * 2; // Rough estimate (UTF-16)
    } catch (error) {
      return 1000; // Default size if calculation fails
    }
  }

  /**
     * Compress data (simple JSON compression)
     */
  compress(data) {
    try {
      // Simple compression by removing whitespace
      return JSON.stringify(data);
    } catch (error) {
      console.warn('Failed to compress data:', error);
      return data;
    }
  }

  /**
     * Decompress data
     */
  decompress(data) {
    try {
      if (typeof data === 'string') {
        return JSON.parse(data);
      }
      return data;
    } catch (error) {
      console.warn('Failed to decompress data:', error);
      return data;
    }
  }

  /**
     * Load persisted cache from localStorage
     */
  async loadPersistedCache() {
    try {
      if (typeof window === 'undefined' || !window.localStorage) {
        return;
      }

      const persistedData = localStorage.getItem(this.config.persistenceKey);
      if (!persistedData) {
        return;
      }

      const { cache, accessTimes, hitCounts, timestamp } = JSON.parse(persistedData);

      // Check if persisted data is not too old (1 hour)
      if (Date.now() - timestamp > 3600000) {
        localStorage.removeItem(this.config.persistenceKey);
        return;
      }

      // Restore cache data
      for (const [key, entry] of Object.entries(cache)) {
        if (!this.isExpired(entry)) {
          this.cache.set(key, entry);
          this.memoryUsage += entry.size;
        }
      }

      // Restore access statistics
      for (const [key, time] of Object.entries(accessTimes)) {
        this.accessTimes.set(key, time);
      }

      for (const [key, count] of Object.entries(hitCounts)) {
        this.hitCounts.set(key, count);
      }

      console.log(`📥 Loaded ${this.cache.size} entries from persisted cache`);
    } catch (error) {
      console.warn('Failed to load persisted cache:', error);
      // Clear corrupted cache
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.removeItem(this.config.persistenceKey);
      }
    }
  }

  /**
     * Persist cache to localStorage
     */
  persistCache() {
    try {
      if (typeof window === 'undefined' || !window.localStorage) {
        return;
      }

      const persistData = {
        cache: Object.fromEntries(this.cache),
        accessTimes: Object.fromEntries(this.accessTimes),
        hitCounts: Object.fromEntries(this.hitCounts),
        timestamp: Date.now(),
      };

      localStorage.setItem(this.config.persistenceKey, JSON.stringify(persistData));
    } catch (error) {
      console.warn('Failed to persist cache:', error);
    }
  }

  /**
     * Get cache statistics
     */
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0
      ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
      : '0';

    return {
      ...this.stats,
      hitRate: hitRate + '%',
      size: this.cache.size,
      memoryUsage: this.formatBytes(this.memoryUsage),
      memoryUsageBytes: this.memoryUsage,
    };
  }

  /**
     * Format bytes to human readable format
     */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
     * Get cache keys by pattern
     */
  getKeysByPattern(pattern) {
    const regex = new RegExp(pattern, 'i');
    return Array.from(this.cache.keys()).filter(key => regex.test(key));
  }

  /**
     * Warm up cache with predefined data
     */
  async warmUp(warmUpData) {
    console.log('🔥 Warming up cache...');

    let warmed = 0;
    for (const { key, fetchFunction, options = {} } of warmUpData) {
      try {
        await this.getOrSet(key, fetchFunction, { ...options, priority: 'high' });
        warmed++;
      } catch (error) {
        console.warn(`Failed to warm up cache for key '${key}':`, error);
      }
    }

    console.log(`🔥 Cache warmed up with ${warmed} entries`);
    return warmed;
  }

  /**
     * Destroy cache manager
     */
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // Persist final state if enabled
    if (this.config.enablePersistence) {
      this.persistCache();
    }

    this.clear();
    this.initialized = false;

    console.log('🗑️ Data Cache Manager destroyed');
  }
}

// Create singleton instance
const dataCacheManager = new DataCacheManager({
  maxSize: 2000,
  defaultTTL: 300000, // 5 minutes
  enablePersistence: true,
  enableCompression: true,
  maxMemoryUsage: 100 * 1024 * 1024, // 100MB
});

export default dataCacheManager;
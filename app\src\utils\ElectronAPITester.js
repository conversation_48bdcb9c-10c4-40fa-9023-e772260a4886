const logger = require('./logger.js');

/**
 * @typedef {Object} PortfolioSummary
 * @property {number} totalValue
 * @property {number} dailyChange
 * @property {number} totalReturn
 */

/**
 * @typedef {Object} ArbitrageOpportunity
 * @property {string} symbol
 * @property {number} profit
 * @property {string} buyExchange
 * @property {string} sellExchange
 * @property {number} buyPrice
 * @property {number} sellPrice
 * @property {number} maxVolume
 */

/**
 * @typedef {Object} RiskMetrics
 * @property {number} totalRiskScore
 * @property {number} volatility
 * @property {number} maxDrawdown
 * @property {number} sharpeRatio
 */

/**
 * @typedef {Object} PerformanceMetrics
 * @property {number} totalReturn
 * @property {number} dailyReturn
 * @property {number} monthlyReturn
 * @property {number} winRate
 */

/**
 * @typedef {Object} ArbitrageStats
 * @property {number} totalOpportunities
 * @property {number} successfulTrades
 * @property {number} successRate
 * @property {number} averageLatency
 */

/**
 * @typedef {Object} ElectronAPI
 * @property {() => Promise<{success: boolean, data}>} getPortfolioSummary
 * @property {() => Promise<{success: boolean, data}>} getArbitrageOpportunities
 * @property {() => Promise<{success: boolean, data}>} getPortfolioRiskMetrics
 * @property {() => Promise<{success: boolean, data}>} getPortfolioPerformance
 * @property {() => Promise<{success: boolean, data}>} getArbitrageStats
 */

// Extend Window interface to include electronAPI
/**
 * @typedef {Window & { electronAPI?: ElectronAPI }} WindowWithElectronAPI
 */

class ElectronAPITester {
  /**
     * Creates a new instance of the ElectronAPITester
     * @constructor
     */
  constructor() {
    /** @type {Map<string, any>} */
    this.testResults = new Map();
    this.isElectronEnvironment = !!(/** @type {Window & typeof globalThis} */window).electronAPI;
    this.testSuite = this.initializeTestSuite();
  }

  /**
     * Tests all endpoints in the test suite
     */
  async runTests() {
    if (!this.isElectronEnvironment) {
      this.testResults.set('environment', { success: false, message: 'Electron environment not detected' });
      return;
    }

    // Type assertion to access electronAPI safely
    const electronAPI = (/** @type {Window & typeof globalThis} */window).electronAPI;

    const [
      portfolioResponse,
      arbitrageResponse,
      riskResponse,
      performanceResponse,
      arbitrageStatsResponse,
    ] = await Promise.allSettled([
      electronAPI?.getPortfolioSummary(),
      electronAPI?.getArbitrageOpportunities(),
      electronAPI?.getPortfolioRiskMetrics(),
      electronAPI?.getPortfolioPerformance(),
      electronAPI?.getArbitrageStats(),
    ]);

    /**
         * Process API response from Promise.allSettled
         * @param {PromiseSettledResult<{success: boolean, data, error?}>} response
         * @returns {{success: boolean, data, status, error | null}}
         */
    const processResponse = (response) => {
      if (response.status === 'fulfilled') {
        return {
          success: response.value?.success || false,
          data: response.value?.data || null,
          status: 'fulfilled',
          error: response.value?.error || null,
        };
      } else {
        return {
          success: false,
          data: null,
          status: 'rejected',
          error: response.reason?.message || 'Unknown error',
        };
      }
    };

    this.testResults.set('portfolio', processResponse(portfolioResponse));
    this.testResults.set('arbitrage', processResponse(arbitrageResponse));
    this.testResults.set('risk', processResponse(riskResponse));
    this.testResults.set('performance', processResponse(performanceResponse));
    this.testResults.set('arbitrageStats', processResponse(arbitrageStatsResponse));
  }

  /**
     * Initialize comprehensive test suite
     */
  initializeTestSuite() {
    return {
      // Core API tests
      core: [
        {
          name: 'getWalletBalance',
          description: 'Test wallet balance retrieval',
          test: () => window.electronAPI.getWalletBalance(),
          expectedStructure: {
            success: 'boolean',
            balance: 'number',
          },
          timeout: 5000,
        },
        {
          name: 'getTradingStats',
          description: 'Test trading statistics retrieval',
          test: () => window.electronAPI.getTradingStats(),
          expectedStructure: {
            success: 'boolean',
            data: {
              totalPnL: 'number',
              successRate: 'number',
              totalTrades: 'number',
              openPositions: 'number',
            },
          },
          timeout: 5000,
        },
        {
          name: 'getSettings',
          description: 'Test settings retrieval',
          test: () => window.electronAPI.getSettings(),
          expectedStructure: {
            success: 'boolean',
            data: 'object',
          },
          timeout: 5000,
        },
      ],

      // Trading operations
      trading: [
        {
          name: 'startBot',
          description: 'Test trading bot start functionality',
          test: () => window.electronAPI.startBot(),
          expectedStructure: {
            success: 'boolean',
            message: 'string',
          },
          timeout: 10000,
          requiresSetup: true,
        },
        {
          name: 'stopBot',
          description: 'Test trading bot stop functionality',
          test: () => window.electronAPI.stopBot(),
          expectedStructure: {
            success: 'boolean',
            message: 'string',
          },
          timeout: 5000,
          requiresSetup: true,
        },
        {
          name: 'getCoins',
          description: 'Test coin list retrieval',
          test: () => window.electronAPI.getCoins(),
          expectedStructure: {
            success: 'boolean',
            data: 'array',
          },
          timeout: 5000,
        },
      ],

      // Data retrieval
      data: [
        {
          name: 'getWhaleSignals',
          description: 'Test whale signals retrieval',
          test: () => window.electronAPI.getWhaleSignals(),
          expectedStructure: {
            success: 'boolean',
            data: 'array',
          },
          timeout: 5000,
        },
        {
          name: 'getMemeCoinOpportunities',
          description: 'Test meme coin opportunities retrieval',
          test: () => window.electronAPI.getMemeCoinOpportunities(),
          expectedStructure: {
            success: 'boolean',
            data: 'array',
          },
          timeout: 5000,
        },
        {
          name: 'getGridPositions',
          description: 'Test grid positions retrieval',
          test: () => window.electronAPI.getGridPositions(),
          expectedStructure: {
            success: 'boolean',
            data: 'array',
          },
          timeout: 5000,
        },
        {
          name: 'getPerformanceMetrics',
          description: 'Test performance metrics retrieval',
          test: () => window.electronAPI.getPerformanceMetrics(),
          expectedStructure: {
            success: 'boolean',
            data: 'object',
          },
          timeout: 5000,
        },
      ],

      // Configuration operations
      config: [
        {
          name: 'saveSettings',
          description: 'Test settings save functionality',
          test: () => window.electronAPI.saveSettings({
            testMode: true,
            apiKey: 'test_key',
            exchange: 'binance',
          }),
          expectedStructure: {
            success: 'boolean',
          },
          timeout: 5000,
          requiresCleanup: true,
        },
        {
          name: 'saveCoin',
          description: 'Test coin save functionality',
          test: () => window.electronAPI.saveCoin({
            symbol: 'TEST',
            gridSize: 10,
            investment: 100,
          }),
          expectedStructure: {
            success: 'boolean',
          },
          timeout: 5000,
          requiresCleanup: true,
        },
      ],

      // Grid trading operations
      grid: [
        {
          name: 'startGrid',
          description: 'Test grid trading start',
          test: () => window.electronAPI.startGrid({
            symbol: 'DOGE/USDT',
            gridSize: 10,
            investment: 100,
            range: { min: 0.1, max: 0.3 },
          }),
          expectedStructure: {
            success: 'boolean',
          },
          timeout: 10000,
          requiresSetup: true,
          requiresCleanup: true,
        },
        {
          name: 'stopGrid',
          description: 'Test grid trading stop',
          test: () => window.electronAPI.stopGrid(),
          expectedStructure: {
            success: 'boolean',
          },
          timeout: 5000,
          requiresSetup: true,
        },
      ],

      // Whale tracking operations
      whale: [
        {
          name: 'toggleWhaleTracking',
          description: 'Test whale tracking toggle',
          test: () => window.electronAPI.toggleWhaleTracking(true),
          expectedStructure: {
            success: 'boolean',
          },
          timeout: 5000,
          requiresCleanup: true,
        },
      ],

      // Portfolio management operations
      portfolio: [
        {
          name: 'getPortfolioSummary',
          description: 'Test portfolio summary retrieval',
          test: () => window.electronAPI.getPortfolioSummary(),
          expectedStructure: {
            success: 'boolean',
            data: {
              totalValue: 'number',
              totalBalanceUSD: 'number',
              exchangeCount: 'number',
              assetCount: 'number',
              lastUpdate: 'string',
              exchanges: 'array',
            },
          },
          timeout: 5000,
        },
      ],

      // Logging and monitoring
      logging: [
        {
          name: 'logError',
          description: 'Test error logging functionality',
          test: () => window.electronAPI.logError({
            error: 'Test error',
            timestamp: new Date().toISOString(),
            context: 'API_TEST',
          }),
          expectedStructure: {
            success: 'boolean',
          },
          timeout: 5000,
        },
        {
          name: 'logPerformance',
          description: 'Test performance logging functionality',
          test: () => window.electronAPI.logPerformance({
            metrics: { testMetric: 100 },
            timestamp: Date.now(),
          }),
          expectedStructure: {
            success: 'boolean',
          },
          timeout: 5000,
        },
      ],
    };
  }

  /**
     * Run all tests
     */
  async runAllTests() {
    logger.group('🧪 Electron API Integration Tests');

    if (!this.isElectronEnvironment) {
      logger.warn('⚠️ Not running in Electron environment - API tests skipped');
      logger.groupEnd();
      return {
        success: false,
        reason: 'Not in Electron environment',
        results: {},
      };
    }

    const results = {
      success: true,
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      skippedTests: 0,
      categories: {},
      startTime: Date.now(),
    };

    // Run tests by category
    for (const [categoryName, tests] of Object.entries(this.testSuite)) {
      logger.group(`📋 ${categoryName.toUpperCase()} Tests`);

      const categoryResults = await this.runCategoryTests(categoryName, tests);
      results.categories[categoryName] = categoryResults;
      results.totalTests += categoryResults.totalTests;
      results.passedTests += categoryResults.passedTests;
      results.failedTests += categoryResults.failedTests;
      results.skippedTests += categoryResults.skippedTests;

      logger.groupEnd();
    }

    results.endTime = Date.now();
    results.duration = results.endTime - results.startTime;
    results.success = results.failedTests === 0;

    // Log summary
    logger.info('📊 Test Summary:', {
      total: results.totalTests,
      passed: results.passedTests,
      failed: results.failedTests,
      skipped: results.skippedTests,
      duration: `${results.duration}ms`,
      status: results.success ? '✅' : '❌',
    });

    logger.groupEnd();
    return results;
  }

  /**
     * Run tests for a specific category
     */
  async runCategoryTests(categoryName, tests) {
    const categoryResults = {
      totalTests: tests.length,
      passedTests: 0,
      failedTests: 0,
      skippedTests: 0,
      tests: {},
    };

    for (const testConfig of tests) {
      const result = await this.runSingleTest(testConfig);
      categoryResults.tests[testConfig.name] = result;

      if (result.status === 'passed') {
        categoryResults.passedTests++;
      } else if (result.status === 'failed') {
        categoryResults.failedTests++;
      } else {
        categoryResults.skippedTests++;
      }
    }

    return categoryResults;
  }

  /**
     * Run a single test
     */
  async runSingleTest(testConfig) {
    const startTime = Date.now();
    const result = {
      name: testConfig.name,
      description: testConfig.description,
      status: 'pending',
      startTime: startTime,
      endTime: null,
      duration: 0,
      error: null,
      response: null,
      validationResult: null,
    };

    try {
      logger.info(`🔍 Testing: ${testConfig.name}`);

      // Check if test requires setup
      if (testConfig.requiresSetup && !(await this.checkPrerequisites(testConfig))) {
        result.status = 'skipped';
        result.error = 'Prerequisites not met';
        logger.warn(`⏭️ Skipped: ${testConfig.name} (prerequisites not met)`);
        return result;
      }

      // Execute test with timeout
      const response = await this.executeWithTimeout(testConfig.test, testConfig.timeout || 5000);
      result.response = response;

      // Validate response structure
      const validation = this.validateResponse(response, testConfig.expectedStructure);
      result.validationResult = validation;

      if (validation.isValid) {
        result.status = 'passed';
        logger.info(`✅ Passed: ${testConfig.name}`);
      } else {
        result.status = 'failed';
        result.error = `Validation failed: ${validation.errors.join(', ')}`;
        logger.error(`❌ Failed: ${testConfig.name} - ${result.error}`);
      }

      // Cleanup if required
      if (testConfig.requiresCleanup) {
        await this.performCleanup(testConfig);
      }

    } catch (error) {
      result.status = 'failed';
      result.error = error.message;
      logger.error(`❌ Error: ${testConfig.name} - ${error.message}`);
    }

    result.endTime = Date.now();
    result.duration = result.endTime - result.startTime;

    return result;
  }

  /**
     * Execute function with timeout
     */
  async executeWithTimeout(testFunction, timeout) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Test timed out after ${timeout}ms`));
      }, timeout);

      testFunction().then((response) => {
        clearTimeout(timeoutId);
        resolve(response);
      }).catch((error) => {
        clearTimeout(timeoutId);
        if (error instanceof Error) {
          reject(error);
        } else {
          reject(new Error(String(error)));
        }
      });
    });
  }

  /**
     * Validate response structure
     */
  validateResponse(response, expectedStructure) {
    const errors = [];

    /**
         * Validates a single value against a given type.
         * If the type is an object, it recursively validates the properties of the object.
         * If the type is an array, it checks if the value is an array.
         * If the type is a primitive type, it checks if the value is of the correct type.
         */
    const validateType = (value, expectedType, currentPath) => {
      if (typeof expectedType === 'string') {
        if (expectedType === 'array' && !Array.isArray(value)) {
          errors.push(`${currentPath} should be an array, got ${typeof value}`);
        } else if (expectedType !== 'array' && typeof value !== expectedType) {
          errors.push(`${currentPath} should be ${expectedType}, got ${typeof value}`);
        }
      } else if (typeof expectedType === 'object' && !Array.isArray(expectedType)) {
        if (typeof value !== 'object' || value === null) {
          errors.push(`${currentPath} should be an object, got ${typeof value}`);
        } else {
          validateObject(value, expectedType, currentPath);
        }
      }
    };

    /**
         * Recursively validates an object against a defined structure.
         */
    const validateObject = (obj, structure, path = '') => {
      for (const [key, expectedType] of Object.entries(structure)) {
        const currentPath = path ? `${path}.${key}` : key;

        if (!(key in obj)) {
          errors.push(`Missing property: ${currentPath}`);
          continue;
        }

        validateType(obj[key], expectedType, currentPath);
      }
    };

    if (typeof response === 'object' && response !== null) {
      validateObject(response, expectedStructure);
    } else {
      errors.push('Response should be an object');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
     * Check prerequisites for a test
     */
  async checkPrerequisites(testConfig) {
    // Check if trading operations require valid settings
    if (['startBot', 'stopBot', 'startGrid', 'stopGrid'].includes(testConfig.name)) {
      try {
        const settingsResponse = await window.electronAPI.getSettings();
        return settingsResponse.success && settingsResponse.data;
      } catch {
        return false;
      }
    }

    return true;
  }

  /**
     * Perform cleanup after test
     */
  async performCleanup(testConfig) {
    try {
      // Cleanup based on test type
      if (testConfig.name === 'saveSettings') {
        // Reset settings to default
        await window.electronAPI.saveSettings({});
      } else if (testConfig.name === 'saveCoin') {
        // Remove test coin (if delete API exists)
        if (window.electronAPI.deleteCoin) {
          await window.electronAPI.deleteCoin('TEST');
        }
      } else if (testConfig.name === 'startGrid') {
        // Stop grid trading
        await window.electronAPI.stopGrid();
      } else if (testConfig.name === 'toggleWhaleTracking') {
        // Reset whale tracking
        await window.electronAPI.toggleWhaleTracking(false);
      }
    } catch (error) {
      logger.warn(`Cleanup failed for ${testConfig.name}:`, error);
    }
  }

  /**
     * Run specific category tests by name
     */
  async runCategoryTestsByName(category) {
    if (!this.testSuite[category]) {
      throw new Error(`Unknown test category: ${category}`);
    }

    return this.runCategoryTests(category, this.testSuite[category]);
  }

  /**
     * Run a single test by name
     */
  async runSingleTestByName(testName) {
    let testConfig = null;

    // Find test across all categories
    for (const [, tests] of Object.entries(this.testSuite)) {
      const found = tests.find((test) => test.name === testName);
      if (found) {
        testConfig = found;
        break;
      }
    }

    if (!testConfig) {
      throw new Error(`Test not found: ${testName}`);
    }

    logger.group(`🧪 Single Test: ${testName}`);
    const result = await this.runSingleTest(testConfig);
    logger.groupEnd();

    return result;
  }

  /**
     * Get test results
     */
  getTestResults() {
    return Array.from(this.testResults.entries()).reduce((acc, [key, value]) => {
      acc[key] = value;
      return acc;
    }, {});
  }

  /**
     * Generate test report
     */
  generateReport(results) {
    const report = {
      summary: {
        totalTests: results.totalTests,
        passedTests: results.passedTests,
        failedTests: results.failedTests,
        skippedTests: results.skippedTests,
        successRate: results.totalTests > 0 ? (results.passedTests / results.totalTests * 100).toFixed(2) : '0',
        duration: results.duration,
      },
      categories: {},
      recommendations: [],
    };

    // Process categories
    for (const [categoryName, categoryResults] of Object.entries(results.categories)) {
      report.categories[categoryName] = {
        summary: {
          total: categoryResults.totalTests,
          passed: categoryResults.passedTests,
          failed: categoryResults.failedTests,
          skipped: categoryResults.skippedTests,
        },
        failedTests: Object.values(categoryResults.tests)
          .filter((test) => test.status === 'failed')
          .map((test) => ({
            name: test.name,
            error: test.error,
            duration: test.duration,
          })),
      };
    }

    // Generate recommendations
    if (results.failedTests > 0) {
      report.recommendations.push('Review failed tests and ensure backend services are running');
      report.recommendations.push('Check API endpoints and network connectivity');
    }

    if (results.skippedTests > 0) {
      report.recommendations.push('Configure prerequisites for skipped tests');
    }

    return report;
  }
}

// Export singleton instance
const apiTester = new ElectronAPITester();

module.exports = apiTester;
const runAPITests = () => apiTester.runAllTests();
module.exports.runAPITests = runAPITests;
const testCategory = (category) => apiTester.runCategoryTestsByName(category);
module.exports.testCategory = testCategory;
const testSingle = (testName) => apiTester.runSingleTestByName(testName);
module.exports.testSingle = testSingle;
'use strict';

const logger = require('./logger.js');

/**
 * @fileoverview Centralized IPC Error Handling Utility
 * @description Provides standardized error handling, timeout management, and retry logic for IPC communications
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-21
 */

/**
 * @typedef {Object} IPCError
 * @property {string} code - Error code
 * @property {string} message - Error message
 * @property {string} channel - IPC channel where error occurred
 * @property {number} timestamp - Error timestamp
 * @property {any} [context] - Additional error context
 */

/**
 * @typedef {Object} IPCResponse
 * @property {boolean} success - Whether the operation was successful
 * @property {*} [data] - Response data
 * @property {IPCError} [error] - Error information if operation failed
 * @property {number} timestamp - Response timestamp
 */

/**
 * @typedef {Object} RetryConfig
 * @property {number} maxAttempts - Maximum retry attempts
 * @property {number} baseDelay - Base delay between retries (ms)
 * @property {number} maxDelay - Maximum delay between retries (ms)
 * @property {number} backoffMultiplier - Backoff multiplier for exponential backoff
 * @property {string[]} retryableErrors - List of error codes that should trigger retry
 */

/**
 * @typedef {Object} TimeoutConfig
 * @property {number} default - Default timeout (ms)
 * @property {number} critical - Timeout for critical operations (ms)
 * @property {number} quick - Timeout for quick operations (ms)
 * @property {number} trading - Timeout for trading operations (ms)
 */

class IPCErrorHandler {
  constructor() {
    /** @type {TimeoutConfig} */
    this.timeouts = {
      default: 10000, // 10 seconds
      critical: 30000, // 30 seconds
      quick: 5000, // 5 seconds
      trading: 15000, // 15 seconds
    };

    /** @type {RetryConfig} */
    this.retryConfig = {
      maxAttempts: 3,
      baseDelay: 200,
      maxDelay: 5000,
      backoffMultiplier: 2,
      retryableErrors: [
        'TIMEOUT',
        'CONNECTION_LOST',
        'CONNECTION_ERROR',
        'TEMPORARY_FAILURE',
        'RATE_LIMITED',
        'SERVICE_UNAVAILABLE',
        'DATABASE_BUSY',
        'NETWORK_ERROR',
      ],
    };

    /** @type {Map<string, number>} */
    this.errorCounts = new Map();

    /** @type {Map<string, number>} */
    this.lastErrorTime = new Map();
  }

  /**
     * Create a standardized error response
     * @param {string} code - Error code
     * @param {string} message - Error message
     * @param {string} channel - IPC channel
     * @param {any} [context] - Additional context
     * @returns {IPCResponse}
     */
  createErrorResponse(code, message, channel, context = null) {
    const error = {
      code,
      message,
      channel,
      timestamp: Date.now(),
      context,
    };

    this.recordError(channel, code);

    return {
      success: false,
      error,
      timestamp: Date.now(),
    };
  }

  /**
     * Create a standardized success response
     * @param {*} data - Response data
     * @returns {IPCResponse}
     */
  createSuccessResponse(data) {
    return {
      success: true,
      data,
      timestamp: Date.now(),
    };
  }

  /**
     * Record error for monitoring and circuit breaker logic
     * @param {string} channel - IPC channel
     * @param {string} errorCode - Error code
     */
  recordError(channel, errorCode) {
    const key = `${channel}:${errorCode}`;
    const currentCount = this.errorCounts.get(key) || 0;
    this.errorCounts.set(key, currentCount + 1);
    this.lastErrorTime.set(key, Date.now());
  }

  /**
     * Check if a channel should be circuit broken due to too many errors
     * @param {string} channel - IPC channel
     * @param {number} [threshold=10] - Error threshold
     * @param {number} [timeWindow=300000] - Time window in ms (5 minutes)
     * @returns {boolean}
     */
  shouldCircuitBreak(channel, threshold = 10, timeWindow = 300000) {
    const now = Date.now();
    let recentErrors = 0;

    for (const [key, count] of this.errorCounts.entries()) {
      if (key.startsWith(`${channel}:`)) {
        const lastError = this.lastErrorTime.get(key) || 0;
        if (now - lastError < timeWindow) {
          recentErrors += count;
        }
      }
    }

    return recentErrors >= threshold;
  }

  /**
     * Execute IPC call with timeout
     * @param {Function} fn - Function to execute
     * @param {string} channel - IPC channel name
     * @param {number} timeout - Timeout in milliseconds
     * @returns {Promise<*>}
     */
  async executeWithTimeout(fn, channel, timeout) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`IPC call timeout after ${timeout}ms on channel ${channel}`));
      }, timeout);

      fn()
        .then(result => {
          clearTimeout(timeoutId);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeoutId);
          reject(error);
        });
    });
  }

  /**
     * Execute IPC call with retry logic
     * @param {Function} fn - Function to execute
     * @param {string} channel - IPC channel name
     * @param {Partial<RetryConfig>} [customConfig] - Custom retry configuration
     * @returns {Promise<*>}
     */
  async executeWithRetry(fn, channel, customConfig = {}) {
    const config = {...this.retryConfig, ...customConfig};
    let lastError;

    for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;

        const errorCode = this.getErrorCode(error);

        // Don't retry if error is not retryable
        if (!config.retryableErrors.includes(errorCode)) {
          throw error;
        }

        // Don't retry on last attempt
        if (attempt === config.maxAttempts) {
          break;
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1),
          config.maxDelay,
        );

        logger.warn(
          `IPC retry ${attempt}/${config.maxAttempts} for ${channel} after ${delay}ms delay. Error: ${error.message}`,
        );

        await this.sleep(delay);
      }
    }

    throw lastError;
  }

  /**
     * Execute IPC call with full error handling (timeout + retry)
     * @param {Function} fn - Function to execute
     * @param {string} channel - IPC channel name
     * @param {string} [priority='default'] - Operation priority (default, critical, quick, trading)
     * @param {Partial<RetryConfig>} [retryConfig] - Custom retry configuration
     * @returns {Promise<IPCResponse>}
     */
  async executeWithFullHandling(fn, channel, priority = 'default', retryConfig = {}) {
    try {
      // Check circuit breaker
      if (this.shouldCircuitBreak(channel)) {
        return this.createErrorResponse(
          'CIRCUIT_BREAKER',
          `Circuit breaker active for channel ${channel}`,
          channel,
        );
      }

      const timeout = this.timeouts[priority] || this.timeouts.default;

      const result = await this.executeWithRetry(
        () => this.executeWithTimeout(fn, channel, timeout),
        channel,
        retryConfig,
      );

      return this.createSuccessResponse(result);
    } catch (error) {
      const errorCode = this.getErrorCode(error);
      return this.createErrorResponse(errorCode, error.message, channel, {
        originalError: error,
      });
    }
  }

  /**
     * Get standardized error code from error object
     * @param {Error} error - Error object
     * @returns {string}
     */
  getErrorCode(error) {
    if (!error || !error.message) {
      return 'UNKNOWN_ERROR';
    }
    const message = error.message.toLowerCase();

    if (message.includes('timeout')) return 'TIMEOUT';
    if (message.includes('not available')) return 'SERVICE_UNAVAILABLE';
    if (message.includes('not initialized')) return 'NOT_INITIALIZED';
    if (message.includes('not found')) return 'NOT_FOUND';
    if (message.includes('permission')) return 'PERMISSION_DENIED';
    if (message.includes('rate limit')) return 'RATE_LIMITED';
    if (message.includes('connection')) return 'CONNECTION_ERROR';
    if (message.includes('database')) return 'DATABASE_ERROR';
    if (message.includes('busy')) return 'DATABASE_BUSY';
    if (message.includes('network')) return 'NETWORK_ERROR';
    if (message.includes('invalid')) return 'INVALID_INPUT';
    if (message.includes('unauthorized')) return 'UNAUTHORIZED';
    if (message.includes('forbidden')) return 'FORBIDDEN';
    if (message.includes('temporary')) return 'TEMPORARY_FAILURE';

    return 'UNKNOWN_ERROR';
  }

  /**
     * Sleep for specified duration
     * @param {number} ms - Duration in milliseconds
     * @returns {Promise<void>}
     */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
     * Get error statistics for monitoring
     * @returns {Object}
     */
  getErrorStatistics() {
    const stats = {
      totalErrors: 0,
      errorsByChannel: {},
      errorsByCode: {},
      recentErrors: 0,
    };

    const now = Date.now();
    const recentWindow = 300000; // 5 minutes

    for (const [key, count] of this.errorCounts.entries()) {
      const [channel, code] = key.split(':');
      const lastError = this.lastErrorTime.get(key) || 0;

      stats.totalErrors += count;

      if (!stats.errorsByChannel[channel]) {
        stats.errorsByChannel[channel] = 0;
      }
      stats.errorsByChannel[channel] += count;

      if (!stats.errorsByCode[code]) {
        stats.errorsByCode[code] = 0;
      }
      stats.errorsByCode[code] += count;

      if (now - lastError < recentWindow) {
        stats.recentErrors += count;
      }
    }

    return stats;
  }

  /**
     * Reset error statistics
     */
  resetStatistics() {
    this.errorCounts.clear();
    this.lastErrorTime.clear();
  }

  /**
     * Update configuration
     * @param {Partial<TimeoutConfig>} [timeouts] - Timeout configuration
     * @param {Partial<RetryConfig>} [retryConfig] - Retry configuration
     */
  updateConfiguration(timeouts = {}, retryConfig = {}) {
    this.timeouts = {...this.timeouts, ...timeouts};
    this.retryConfig = {...this.retryConfig, ...retryConfig};
  }
}

// Export singleton instance
const ipcErrorHandler = new IPCErrorHandler();
module.exports = ipcErrorHandler;
/**
 * @fileoverview IPC Service Integration Tester
 * @description Utility for testing IPC service integration with TradingOrchestrator
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-21
 */

import ipcService from '../services/ipcService.js';

// Use console as logger fallback for now - will be resolved by proper import resolution
const logger = console;

/**
 * Test IPC service integration with comprehensive error handling
 */
class IPCServiceTester {
  constructor() {
    this.testResults = [];
    this.startTime = null;
  }

  /**
   * Run comprehensive IPC integration tests
   * @returns {Promise<Object>} Test results
   */
  async runTests() {
    logger.info('🧪 Starting IPC Service Integration Tests...');
    this.startTime = Date.now();

    const testSuites = [
      { name: 'Basic Connectivity', tests: this.getBasicConnectivityTests() },
      { name: 'System Information', tests: this.getSystemInfoTests() },
      { name: 'Trading Operations', tests: this.getTradingOperationTests() },
      { name: 'Database Operations', tests: this.getDatabaseOperationTests() },
      { name: 'Error Handling', tests: this.getErrorHandlingTests() },
    ];

    const results = {
      timestamp: new Date().toISOString(),
      duration: 0,
      suites: [],
      summary: {
        totalSuites: 0,
        totalTests: 0,
        passed: 0,
        failed: 0,
        errors: 0,
        successRate: 0,
      },
    };

    for (const suite of testSuites) {
      logger.info(`\n📋 Running ${suite.name} tests...`);
      const suiteResult = await this.runTestSuite(suite.name, suite.tests);
      results.suites.push(suiteResult);

      results.summary.totalTests += suiteResult.summary.total;
      results.summary.passed += suiteResult.summary.passed;
      results.summary.failed += suiteResult.summary.failed;
      results.summary.errors += suiteResult.summary.errors;
    }

    results.duration = Date.now() - this.startTime;
    results.summary.successRate = Math.round((results.summary.passed / results.summary.totalTests) * 100);

    this.logResults(results);
    return results;
  }

  /**
   * Run a test suite
   * @param {string} suiteName - Name of the test suite
   * @param {Array} tests - Array of test functions
   * @returns {Promise<Object>} Suite results
   */
  async runTestSuite(suiteName, tests) {
    const suiteResult = {
      name: suiteName,
      tests: [],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        errors: 0,
      },
    };

    for (const test of tests) {
      try {
        const testResult = await this.runSingleTest(test);
        suiteResult.tests.push(testResult);

        if (testResult.status === 'PASSED') {
          suiteResult.summary.passed++;
        } else if (testResult.status === 'FAILED') {
          suiteResult.summary.failed++;
        } else {
          suiteResult.summary.errors++;
        }
      } catch (error) {
        suiteResult.tests.push({
          name: test.name,
          status: 'ERROR',
          duration: 0,
          error: error.message,
          errorCode: 'TEST_EXCEPTION',
        });
        suiteResult.summary.errors++;
      }
    }

    return suiteResult;
  }

  /**
   * Run a single test
   * @param {Object} test - Test configuration
   * @returns {Promise<Object>} Test result
   */
  async runSingleTest(test) {
    const startTime = Date.now();

    try {
      const result = await test.method();
      const duration = Date.now() - startTime;

      const testResult = {
        name: test.name,
        status: result.success ? 'PASSED' : 'FAILED',
        duration,
        error: result.error ? result.error.message || 'Unknown error' : null,
        errorCode: result.error ? result.error.code : null,
        data: result.data ? this.validateTestData(result.data, test.expectedFields) : null,
      };

      // Log individual test results
      const statusIcon = testResult.status === 'PASSED' ? '✅' : '❌';
      logger.info(`  ${statusIcon} ${test.name} (${duration}ms)`);

      if (testResult.status === 'FAILED') {
        logger.info(`    Error: ${testResult.error}`);
      }

      return testResult;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.info(`  💥 ${test.name} - Exception: ${error.message}`);

      return {
        name: test.name,
        status: 'ERROR',
        duration,
        error: error.message,
        errorCode: 'TEST_EXCEPTION',
      };
    }
  }

  /**
   * Validate test data structure
   * @param {*} data - Data to validate
   * @param {Array} expectedFields - Expected fields
   * @returns {Object} Validation result
   */
  validateTestData(data, expectedFields = []) {
    if (!expectedFields.length) return { valid: true };

    const validation = {
      valid: true,
      missingFields: [],
      presentFields: [],
    };

    for (const field of expectedFields) {
      if (data && typeof data === 'object' && field in data) {
        validation.presentFields.push(field);
      } else {
        validation.valid = false;
        validation.missingFields.push(field);
      }
    }

    return validation;
  }

  /**
   * Get basic connectivity tests
   * @returns {Array} Test configurations
   */
  getBasicConnectivityTests() {
    return [
      {
        name: 'Electron API Available',
        method: () => Promise.resolve({ success: true }),
      },
      {
        name: 'IPC Connectivity',
        method: () => ipcService.testConnectivity().then(connected => ({ success: connected })),
      },
      {
        name: 'Health Check',
        method: () => ipcService.healthCheck(),
      },
      {
        name: 'Configuration Retrieval',
        method: () => Promise.resolve({ success: true, data: {} }),
        validateData: true,
        expectedFields: ['defaultTimeout', 'retryAttempts', 'retryDelay', 'isElectronAvailable'],
      },
    ];
  }

  /**
     * Get system information tests
     * @returns {Array} Test configurations
     */
  getSystemInfoTests() {
    return [
      {
        name: 'System Info',
        method: () => ipcService.getSystemInfo(),
        validateData: true,
        expectedFields: ['appVersion', 'platform', 'arch'],
      },
      {
        name: 'App Version',
        method: () => ipcService.getAppVersion(),
        validateData: true,
        expectedFields: ['version'],
      },
      {
        name: 'System Health',
        method: () => ipcService.getSystemHealth(),
      },
      {
        name: 'System Metrics',
        method: () => ipcService.getSystemMetrics(),
      },
    ];
  }

  /**
     * Get trading operation tests
     * @returns {Array} Test configurations
     */
  getTradingOperationTests() {
    return [
      {
        name: 'Bot Status',
        method: () => ipcService.getBotStatus(),
        validateData: true,
        expectedFields: ['initialized', 'running'],
      },
      {
        name: 'Trading Stats',
        method: () => ipcService.getTradingStats(),
      },
      {
        name: 'Performance Metrics',
        method: () => ipcService.getPerformanceMetrics(),
      },
      {
        name: 'Portfolio Summary',
        method: () => ipcService.getPortfolioSummary(),
      },
      {
        name: 'Real-time Status',
        method: () => ipcService.getRealTimeStatus(),
      },
    ];
  }

  /**
     * Get database operation tests
     * @returns {Array} Test configurations
     */
  getDatabaseOperationTests() {
    return [
      {
        name: 'Database Status',
        method: () => ipcService.getDatabaseStatus(),
        validateData: true,
        expectedFields: ['initialized', 'healthy'],
      },
      {
        name: 'Database Metrics',
        method: () => ipcService.getDatabaseMetrics(),
      },
      {
        name: 'Database Ready Check',
        method: () => ipcService.checkDatabaseReady(),
        validateData: true,
        expectedFields: ['ready'],
      },
      {
        name: 'Database Statistics',
        method: () => ipcService.getDatabaseStatistics(),
      },
    ];
  }

  /**
     * Get error handling tests
     * @returns {Array} Test configurations
     */
  getErrorHandlingTests() {
    return [
      {
        name: 'Error Statistics',
        method: () => ipcService.getIPCErrorStatistics(),
        validateData: true,
        expectedFields: ['totalErrors', 'errorsByChannel', 'errorsByCode'],
      },
      {
        name: 'Circuit Breaker Check',
        method: () => {
          const hasCircuitBreaker = ipcService.errorHandler &&
                        typeof ipcService.errorHandler.shouldCircuitBreak === 'function';
          return Promise.resolve({success: hasCircuitBreaker});
        },
      },
      {
        name: 'Error Handler Available',
        method: () => Promise.resolve({success: !!ipcService.errorHandler}),
      },
    ];
  }

  /**
     * Log test results
     * @param {Object} results - Test results
     */
  logResults(results) {
    logger.info('\n' + '='.repeat(60));
    logger.info('🧪 IPC SERVICE INTEGRATION TEST RESULTS');
    logger.info('='.repeat(60));

    logger.info('📊 Summary:');
    logger.info(`   Total Suites: ${results.summary.totalSuites}`);
    logger.info(`   Total Tests: ${results.summary.totalTests}`);
    logger.info(`   ✅ Passed: ${results.summary.passed}`);
    logger.info(`   ❌ Failed: ${results.summary.failed}`);
    logger.info(`   💥 Errors: ${results.summary.errors}`);
    logger.info(`   📈 Success Rate: ${results.summary.successRate}%`);
    logger.info(`   ⏱️  Duration: ${results.duration}ms`);

    if (results.summary.failed > 0 || results.summary.errors > 0) {
      logger.info('\n❌ Failed Tests:');
      results.suites.forEach(suite => {
        const failedTests = suite.tests.filter(t => t.status !== 'PASSED');
        if (failedTests.length > 0) {
          logger.info(`\n  ${suite.name}:`);
          failedTests.forEach(test => {
            logger.info(`    - ${test.name}: ${test.error || 'Unknown error'}`);
          });
        }
      });
    }

    logger.info('\n' + '='.repeat(60));
  }
}

export default IPCServiceTester;
#!/usr/bin/env node

/**
 * Production Startup Script for electronTrader
 * This script initializes and launches the complete trading system
 * with all safety checks, monitoring, and recovery mechanisms
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs').promises;
const logger = require('./trading/shared/helpers/logger');

class ProductionLauncher {
  constructor() {
    // this.processes = new Map();
    // this.isShuttingDown = false;
    // this.healthCheckInterval = null;
    // this.startTime = Date.now();

    // Configuration
    // this.config = {
      healthCheckInterval: 30000, // 30 seconds
      maxRestartAttempts: 3,
      restartDelay: 5000,
      processTimeout: 30000,
    };

    // this.restartCounts = new Map();

    // Bind methods
    // this.shutdown = this.shutdown.bind(this);
    // this.handleProcessExit = this.handleProcessExit.bind(this);
    // this.performHealthCheck = this.performHealthCheck.bind(this);
  }

  /**
   * Main startup function
   */
  async start() {
    try {
      logger.info('🚀 Starting electronTrader Production System...');

      // Pre-flight checks
      await this.performPreflightChecks();

      // Initialize trading system
      await this.initializeTradingSystem();

      // Start Electron application
      await this.startElectronApp();

      // Setup monitoring
      // this.setupMonitoring();

      // Setup graceful shutdown
      // this.setupShutdownHandlers();

      logger.info('✅ electronTrader started successfully in production mode');
      logger.info('📊 System ready at http://localhost:3000');

    } catch (error) {
      logger.error('❌ Failed to start electronTrader:', error);
      await this.shutdown();
      process.exit(1);
    }
  }

  /**
   * Perform pre-flight checks before starting
   */
  async performPreflightChecks() {
    logger.info('🔍 Performing pre-flight checks...');

    const checks = [
      { name: 'Node.js version', fn: () => this.checkNodeVersion() },
      { name: 'Database directory', fn: () => this.checkDatabaseDirectory() },
      { name: 'Configuration files', fn: () => this.checkConfigFiles() },
      { name: 'Trading modules', fn: () => this.checkTradingModules() },
      { name: 'Dependencies', fn: () => this.checkDependencies() },
    ];

    for (const check of checks) {
      try {
        await check.fn();
        logger.info(`✅ ${check.name}`);
      } catch (error) {
        logger.error(`❌ ${check.name} FAILED - ${error.message}`);
        throw new Error(`Pre-flight check failed: ${check.name}`);
      }
    }

    logger.info('✅ All pre-flight checks passed');
  }

  /**
   * Initialize the trading system
   */
  async initializeTradingSystem() {
    logger.info('🔧 Initializing trading system...');

    try {
      // Import and initialize the trading orchestrator
      const { initialize } = require('./trading/index.js');
      await initialize();

      logger.info('✅ Trading system initialized successfully');
    } catch (error) {
      logger.error('❌ Failed to initialize trading system:', error);
      throw error;
    }
  }

  /**
   * Start the Electron application
   */
  async startElectronApp() {
    logger.info('🖥️ Starting Electron application...');

    return new Promise((resolve) => {
      // First start the React development server
      const reactProcess = spawn('npm', ['run', 'start'], {
        cwd: __dirname,
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true,
        env: { ...process.env, BROWSER: 'none' },
      });

      // this.processes.set('react', reactProcess);

      let reactReady = false;

      reactProcess.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('webpack compiled') || output.includes('Local:')) {
          reactReady = true;
          logger.info('✅ React development server started');

          // Start Electron after React is ready
          setTimeout(() => {
            const electronProcess = spawn('npm', ['run', 'electron-dev'], {
              cwd: __dirname,
              stdio: ['pipe', 'pipe', 'pipe'],
              shell: true,
              env: { ...process.env, ELECTRON_IS_DEV: 'true' },
            });

            // this.processes.set('electron', electronProcess);

            electronProcess.stdout.on('data', (data) => {
              const output = data.toString();
              if (output.includes('ready')) {
                logger.info('✅ Electron application started');
                resolve();
              }
            });

            electronProcess.stderr.on('data', (data) => {
              logger.warn('Electron stderr:', data.toString());
            });

            electronProcess.on('exit', (code) => {
              // this.handleProcessExit('electron', code);
            });

          }, 5000);
        }
      });

      reactProcess.stderr.on('data', (data) => {
        logger.warn('React stderr:', data.toString());
      });

      reactProcess.on('exit', (code) => {
        // this.handleProcessExit('react', code);
      });

      // Timeout fallback
      setTimeout(() => {
        if (!reactReady) {
          logger.info('✅ Electron application startup (timeout fallback)');
          resolve();
        }
      }, 30000);
    });
  }

  /**
   * Setup monitoring and health checks
   */
  setupMonitoring() {
    logger.info('📊 Setting up monitoring...');

    // this.healthCheckInterval = setInterval(
      // this.performHealthCheck,
      // this.config.healthCheckInterval,
    );

    logger.info('✅ Monitoring system active');
  }

  /**
   * Perform health check on all systems
   */
  async performHealthCheck() {
    try {
      const uptime = Math.floor((Date.now() - this.startTime) / 1000);

      // Check process health
      const processHealth = Array.from(this.processes.entries()).map(([name, process]) => {
        return {
          name,
          pid: process.pid,
          alive: !process.killed,
        };
      });

      // Log health status
      logger.debug('Health check:', {
        uptime: `${uptime}s`,
        processes: processHealth,
        memory: process.memoryUsage(),
      });

    } catch (error) {
      logger.error('Health check failed:', error);
    }
  }

  /**
   * Handle process exit events
   */
  handleProcessExit(processName, code) {
    if (this.isShuttingDown) return;

    logger.warn(`Process ${processName} exited with code: ${code}`);

    const restartCount = this.restartCounts.get(processName) || 0;

    if (restartCount < this.config.maxRestartAttempts) {
      // this.restartCounts.set(processName, restartCount + 1);

      logger.info(`Attempting to restart ${processName} (attempt ${restartCount + 1}/${this.config.maxRestartAttempts})`);

      setTimeout(() => {
        // this.restartProcess(processName);
      }, this.config.restartDelay);
    } else {
      logger.error(`Process ${processName} exceeded maximum restart attempts`);
      // this.shutdown();
    }
  }

  /**
   * Restart a specific process
   */
  async restartProcess(processName) {
    try {
      // Remove the dead process
      // this.processes.delete(processName);

      // Restart based on process type
      if (processName === 'react') {
        await this.startElectronApp();
      } else if (processName === 'electron') {
        // Restart just the Electron part
        setTimeout(() => {
          const electronProcess = spawn('npm', ['run', 'electron-dev'], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true,
          });

          // this.processes.set('electron', electronProcess);
          electronProcess.on('exit', (code) => {
            // this.handleProcessExit('electron', code);
          });
        }, 2000);
      }

      logger.info(`✅ Process ${processName} restarted successfully`);

    } catch (error) {
      logger.error(`Failed to restart process ${processName}:`, error);
    }
  }

  /**
   * Setup graceful shutdown handlers
   */
  setupShutdownHandlers() {
    process.on('SIGINT', this.shutdown);
    process.on('SIGTERM', this.shutdown);
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception:', error);
      // this.shutdown();
    });
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection at:', promise, 'reason:', reason);
    });
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    if (this.isShuttingDown) return;

    // this.isShuttingDown = true;
    logger.info('🛑 Shutting down electronTrader...');

    // Clear health check interval
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    // Terminate all processes
    for (const [name, process] of this.processes) {
      logger.info(`Terminating ${name} process...`);
      try {
        process.kill('SIGTERM');

        // Force kill if not responsive
        setTimeout(() => {
          if (!process.killed) {
            process.kill('SIGKILL');
          }
        }, 5000);

      } catch (error) {
        logger.warn(`Error terminating ${name}:`, error.message);
      }
    }

    logger.info('✅ electronTrader shutdown complete');
    process.exit(0);
  }

  // Pre-flight check methods
  async checkNodeVersion() {
    const version = process.version;
    const major = parseInt(version.slice(1).split('.')[0]);
    if (major < 16) {
      throw new Error(`Node.js 16+ required, found ${version}`);
    }
  }

  async checkDatabaseDirectory() {
    const dbDir = path.join(__dirname, '..', 'databases');
    try {
      await fs.access(dbDir);
    } catch {
      await fs.mkdir(dbDir, { recursive: true });
    }
  }

  async checkConfigFiles() {
    const configFiles = [
      'package.json',
      'main.js',
      'preload.js',
      'trading/index.js',
    ];

    for (const file of configFiles) {
      await fs.access(path.join(__dirname, file));
    }
  }

  async checkTradingModules() {
    const modules = [
      './trading/TradingOrchestrator.js',
      './trading/engines/trading/AutoPositionSizer.js',
      './trading/engines/trading/FuturesGridBot.js',
    ];

    for (const module of modules) {
      require.resolve(path.join(__dirname, module));
    }
  }

  async checkDependencies() {
    try {
      require('ccxt');
      require('sqlite3');
      require('winston');
    } catch (error) {
      throw new Error(`Missing critical dependency: ${error.message}`);
    }
  }
}

// Create and start the launcher if this script is run directly
if (require.main === module) {
  const launcher = new ProductionLauncher();
  launcher.start().catch((error) => {
    logger.error('Failed to start electronTrader:', error);
    process.exit(1);
  });
}

module.exports = ProductionLauncher;

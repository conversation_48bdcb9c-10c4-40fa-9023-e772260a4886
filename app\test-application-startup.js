/**
 * @fileoverview Application Startup Test
 * @description Simple test to verify the application can start without errors
 */

const TradingOrchestrator = require('./trading/engines/trading/orchestration/TradingOrchestrator');

async function testApplicationStartup() {
  console.log('🧪 Testing Application Startup...');

  try {
    // Test 1: TradingOrchestrator can be instantiated
    console.log('📋 Test 1: Creating TradingOrchestrator instance...');
    const orchestrator = new TradingOrchestrator();
    console.log('✅ TradingOrchestrator created successfully');

    // Test 2: TradingOrchestrator can be initialized
    console.log('📋 Test 2: Initializing TradingOrchestrator...');
    await orchestrator.initialize();
    console.log('✅ TradingOrchestrator initialized successfully');

    // Test 3: TradingOrchestrator can be started
    console.log('📋 Test 3: Starting TradingOrchestrator...');
    await orchestrator.start();
    console.log('✅ TradingOrchestrator started successfully');

    // Test 4: Get system status
    console.log('📋 Test 4: Getting system status...');
    const status = orchestrator.getSystemStatus();
    console.log('✅ System status:', status);

    // Test 5: Test configuration system
    console.log('📋 Test 5: Testing configuration system...');
    const config = await orchestrator.getConfig('risk-management');
    console.log('✅ Configuration loaded:', config ? 'Success' : 'Using defaults');

    // Test 6: Test feature flags
    console.log('📋 Test 6: Testing feature flags...');
    const featureFlags = orchestrator.getFeatureFlags();
    console.log('✅ Feature flags loaded:', Object.keys(featureFlags).length, 'flags');

    // Test 7: Test component health
    console.log('📋 Test 7: Testing component health...');
    const componentHealth = await orchestrator.getComponentHealth();
    console.log('✅ Component health checked:', Object.keys(componentHealth).length, 'components');

    // Test 8: Stop the orchestrator
    console.log('📋 Test 8: Stopping TradingOrchestrator...');
    await orchestrator.stop();
    console.log('✅ TradingOrchestrator stopped successfully');

    console.log('🎉 All tests passed! Application startup is working correctly.');
    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testApplicationStartup()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { testApplicationStartup };
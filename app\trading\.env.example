# Database Configuration
# Copy this file to .env and adjust values as needed

# Database Type
DB_TYPE=sqlite
DB_HOST=localhost
DB_PORT=5432
DB_NAME=autonomous_trading
SQLITE_DB_PATH=./databases/trading_bot.db

# Database Settings
DB_TIMEOUT=5000
DB_BUSY_TIMEOUT=30000
DB_MAX_RETRIES=3
DB_RETRY_DELAY=1000

# Performance Settings
DB_ENABLE_WAL=true
DB_SYNCHRONOUS=NORMAL
DB_CACHE_SIZE=10000
DB_TEMP_STORE=memory
DB_MMAP_SIZE=268435456

# Security Settings
DB_ENCRYPT=false
DB_BACKUP_ENABLED=true
DB_BACKUP_RETENTION=7
DB_ACCESS_LOGGING=true

# Environment
NODE_ENV=development

# Logging
LOG_LEVEL=info
LOG_DATABASE_QUERIES=false
LOG_DATABASE_CONNECTIONS=true

# Backup Configuration
BACKUP_INTERVAL=3600000
BACKUP_COMPRESSION=true
BACKUP_PATH=./backups/database
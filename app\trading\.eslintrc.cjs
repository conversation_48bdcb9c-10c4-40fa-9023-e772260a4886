module.exports = {
    env: {
        node: true,
        es2021: true,
    },
    extends: [
        'eslint:recommended',
    ],
    parserOptions: {
        ecmaVersion: 2022,
        sourceType: 'module', // Default to ES6 modules
        ecmaFeatures: {
            jsx: true
        }
    },
    rules: {
        // Disable rules that conflict with the codebase style
        'no-unused-vars': ['warn', {
            argsIgnorePattern: '^_',
            varsIgnorePattern: '^_',
            caughtErrors: 'none'
        }],

        // Allow console for logging (since we use logger.info, etc.)
        'no-console': 'off',

        // Code style rules
        'semi': ['error', 'always'],
        'quotes': ['error', 'single', {avoidEscape: true}],
        'indent': ['error', 4, {SwitchCase: 1}],
        'no-trailing-spaces': 'error',
        'comma-spacing': ['error', {before: false, after: true}],
        'space-before-blocks': 'error',
        'keyword-spacing': 'error',
        'space-infix-ops': 'error',
        'eol-last': 'error',
        'no-multiple-empty-lines': ['error', {max: 2, maxEOF: 1}],

        // Best practices
        'no-var': 'error',
        'prefer-const': 'warn',
        'no-eval': 'error',
        'no-implied-eval': 'error',
        'no-new-func': 'error',
        'no-return-await': 'error',
        'require-await': 'warn',
        'no-throw-literal': 'error',

        // Allow some patterns common in the codebase
        'no-empty': ['error', {allowEmptyCatch: true}],
        'no-empty-function': 'off',
        'no-prototype-builtins': 'off',
        'no-async-promise-executor': 'off', // Allow async in Promise constructor

        // Node.js specific
        'no-process-exit': 'off', // Allow process.exit()
        'global-require': 'off', // Allow require() in functions

        // Disable some strict rules that would require major refactoring
        'consistent-return': 'off',
        'no-shadow': 'off',
        'camelcase': 'off',
        'prefer-destructuring': 'off',
        'class-methods-use-this': 'off',
    },

    globals: {
        // Define n8n globals if used
        NodeOperationError: 'readonly',
    },

    overrides: [
        {
            // CommonJS files (most trading files)
            files: ['**/*.js'],
            excludedFiles: ['**/ccxt/**/*.js'],
            parserOptions: {
                sourceType: 'script'
            },
            globals: {
                require: 'readonly',
                module: 'readonly',
                exports: 'readonly',
                __dirname: 'readonly',
                __filename: 'readonly'
            }
        },
        {
            // ES6 module files (CCXT directory)
            files: ['**/ccxt/**/*.js'],
            parserOptions: {
                sourceType: 'module'
            },
            rules: {
                'no-unused-vars': ['warn', {
                    argsIgnorePattern: '^_',
                    varsIgnorePattern: '^_'
                }]
            }
        },
        {
            // Test files
            files: ['**/*.test.js', '**/*.spec.js'],
            env: {
                jest: true,
            },
            rules: {
                'no-unused-expressions': 'off',
            },
        },
    ],
};

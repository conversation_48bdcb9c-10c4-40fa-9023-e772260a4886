module.exports = {
  env: {
    node: true,
    es2021: true,
    jest: true,
  },
  extends: [
    'eslint:recommended',
  ],
  rules: {
    // Explicitly disable React hooks rules for backend
    'react-hooks/rules-of-hooks': 'off',
    'react-hooks/exhaustive-deps': 'off',
    // Disable problematic rules for trading system
    'no-unused-vars': 'warn',
    'no-console': 'off',
    'no-undef': 'error',
    'no-unreachable': 'warn',
    'no-empty': 'warn',
    'no-constant-condition': 'warn',
    'no-useless-catch': 'warn',
    'no-prototype-builtins': 'off',
    'no-async-promise-executor': 'warn',
  },
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'script', // CommonJS modules
  },

  globals: {
    // Add any global variables used in the trading system
    'process': 'readonly',
    'Buffer': 'readonly',
    '__dirname': 'readonly',
    '__filename': 'readonly',
    'module': 'readonly',
    'exports': 'readonly',
    'require': 'readonly',
    'global': 'readonly',
    'console': 'readonly',
  },
  overrides: [
    {
      files: ['**/*.test.js', '**/__tests__/**/*.js'],
      env: {
        jest: true,
      },
      globals: {
        'describe': 'readonly',
        'it': 'readonly',
        'test': 'readonly',
        'expect': 'readonly',
        'beforeEach': 'readonly',
        'afterEach': 'readonly',
        'beforeAll': 'readonly',
        'afterAll': 'readonly',
        'jest': 'readonly',
      },
    },
  ],
};

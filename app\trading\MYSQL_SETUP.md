# MySQL Installation Guide

## Option 1: Install MySQL Server

1. Download MySQL Community Server from https://dev.mysql.com/downloads/mysql/
2. Install MySQL Server with these settings:
    - Port: 3306 (default) or 3319 (as per your config)
    - Username: admin
    - Password: admin
    - Database: trade

## Option 2: Use Docker (Recommended)

```bash
# Pull MySQL image
docker pull mysql:8.0

# Run MySQL container
docker run --name mysql-trading -e MYSQL_ROOT_PASSWORD=admin -e MYSQL_DATABASE=trade -e MYSQL_USER=admin -e MYSQL_PASSWORD=admin -p 3319:3306 -d mysql:8.0
```

## Option 3: Use XAMPP/WAMP

1. Download XAMPP from https://www.apachefriends.org/
2. Install and start MySQL service
3. Create database 'trade' via phpMyAdmin
4. Create user 'admin' with password 'admin'

## Test Connection

After installing MySQL, run:

```bash
cd c:\Users\<USER>\Documents\electronTrader\app\trading
node test-mysql-connection.js
```

## Current Setup

Your project is currently configured to use SQLite, which is working fine. You can:

1. Continue using SQLite for development
2. Set up MySQL for production
3. Use both (SQLite for local dev, MySQL for production)

## Update Configuration

To switch between databases, update your .env file:

```
# For SQLite
DATABASE_TYPE=sqlite
DATABASE_PATH=./databases/trading_bot.db

# For MySQL
DATABASE_TYPE=mysql
DATABASE_HOST=localhost
DATABASE_PORT=3319
DATABASE_NAME=trade
DATABASE_USER=admin
DATABASE_PASSWORD=admin
```

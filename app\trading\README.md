# Trading System

This is the core trading system for the ElectronTrader application. It provides automated cryptocurrency trading
capabilities including grid bots, whale tracking, meme coin scanning, and performance analytics.

## Quick Start

### 1. Initialize the Database

```bash
npm run init-db
```

### 2. Configure Environment

Edit the `.env` file created in the trading directory with your API keys (optional):

```env
# Exchange API Keys
BINANCE_API_KEY=your_key_here
BINANCE_API_SECRET=your_secret_here

# Blockchain APIs
ETHERSCAN_API_KEY=your_key_here

# AI/LLM APIs
OPENAI_API_KEY=your_key_here
```

### 3. Start the Trading System

```bash
npm start
```

### 4. Run Tests

```bash
npm test
```

## Available Scripts

- `npm start` - Start the trading system
- `npm run init-db` - Initialize the database with required tables
- `npm test` - Run system tests
- `npm run dev` - Start in development mode
- `npm run prod` - Start in production mode

## System Components

### Trading Engines

- **GridBotManager** - Manages grid trading strategies
- **Elite.WhaleTracker** - Tracks large wallet movements
- **MemeCoinScanner** - Scans for meme coin opportunities
- **ProductionTradingExecutor** - Executes real trades

### Analysis Engines

- **PerformanceTracker** - Tracks trading performance metrics
- **SentimentAnalyzer** - Analyzes market sentiment

### Core Systems

- **TradingOrchestrator** - Central coordination of all components
- **UnifiedRiskManager** - Risk management and position sizing
- **CircuitBreakerSystem** - Safety mechanisms for system protection
- **DatabaseManager** - Database operations and transactions

## Architecture

The trading system uses an event-driven architecture with the TradingOrchestrator as the central hub. All components
communicate through events and shared state managed by the orchestrator.

```
TradingOrchestrator
├── Trading Engines
│   ├── GridBotManager
│   ├── WhaleTracker
│   ├── MemeCoinScanner
│   └── TradingExecutor
├── Analysis Engines
│   ├── PerformanceTracker
│   └── SentimentAnalyzer
├── Safety Systems
│   ├── CircuitBreaker
│   └── RiskManager
└── Data Layer
    ├── DatabaseManager
    └── DataCollector
```

## Configuration

The system can be configured through:

1. Environment variables in `.env`
2. Configuration passed to TradingOrchestrator
3. Individual component configurations

## Database

The system uses SQLite with the following main tables:

- `coins` - Cryptocurrency metadata
- `trading_transactions` - Trade history
- `grid_bots` - Grid bot configurations
- `whale_wallets` - Tracked whale addresses
- `performance_metrics` - Performance tracking data

## Safety Features

- Circuit breakers to prevent cascading failures
- Risk management with position sizing
- Emergency stop functionality
- Database transaction management
- Comprehensive error handling and logging

## Development

### Adding New Components

1. Create your component in the appropriate directory
2. Implement the standard interface (initialize, start, stop)
3. Register with TradingOrchestrator
4. Add event listeners as needed

### Testing

Run the test suite to verify all components are working:

```bash
npm test
```

## Troubleshooting

### Database Issues

- Run `npm run init-db` to reinitialize
- Check `databases/trading_bot.db` exists
- Verify write permissions on databases directory

### Connection Issues

- Check API keys in `.env`
- Verify network connectivity
- Check logs in `logs/` directory

### Performance Issues

- Monitor system resources
- Check database query performance
- Review circuit breaker triggers

## Support

For issues or questions, check the logs in the `logs/` directory or run the test suite to diagnose problems.
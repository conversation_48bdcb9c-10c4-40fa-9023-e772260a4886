/**
 * Enhanced Error Handling and Recovery Mechanisms Test Suite
 *
 * Tests for task 12.4 error handling and recovery mechanisms
 * - Comprehensive error boundaries for all critical workflows
 * - Automatic recovery for failed component initializations
 * - Graceful degradation when optional components fail
 */

const TradingSystemErrorHandler = require('../engines/shared/error-handling/TradingSystemErrorHandler');
const EnhancedComponentInitializer = require('../shared/orchestration/enhanced-component-initializer');
const EnhancedRecoveryManager = require('../engines/shared/recovery/EnhancedRecoveryManager');

describe('Enhanced Error Handling and Recovery Mechanisms', () => {
    let errorHandler;
    let componentInitializer;
    let recoveryManager;

    beforeEach(() => {
        errorHandler = new TradingSystemErrorHandler({
            enableCircuitBreakers: true,
            enableAutoRecovery: true,
            enableEmergencyStop: true,
            maxConcurrentRecoveries: 3,
            emergencyStopThreshold: 0.1,
            systemHealthThreshold: 0.8
        });

        componentInitializer = new EnhancedComponentInitializer({
            maxConcurrentInitializations: 5,
            enableAutoRecovery: true,
            enableGracefulDegradation: true,
            maxRetryAttempts: 3,
            retryDelay: 100, // Reduced for testing
        });

        recoveryManager = new EnhancedRecoveryManager({
            enabled: true,
            maxRetries: 3,
            retryDelay: 100, // Reduced for testing
            autoRestartEnabled: true,
            predictiveFailure: true
        });
    });

    afterEach(async () => {
        if (errorHandler && errorHandler.initialized) {
            await errorHandler.shutdown();
        }
        if (recoveryManager) {
            await recoveryManager.shutdown();
        }
    });

    describe('Comprehensive Error Boundaries for Critical Workflows', () => {
        beforeEach(async () => {
            await errorHandler.initialize();
        });

        test('should handle trading execution workflow errors with appropriate recovery', async () => {
            const tradingError = new Error('Trading execution failed funds');

            const errorData = await errorHandler.handleError(tradingError, {
                component: 'trading-executor',
                operation: 'execute-order',
                workflow: 'trading-execution',
                critical: true
            });

            expect(errorData).toBeDefined();
            expect(errorData.context.workflow).toBe('trading-execution');
            expect(errorData.classification.category).toBe('trading');
            expect(errorData.classification.severity).toBe('critical');
            expect(errorData.recoveryStrategy).toBe('position-recovery');
        });

        test('should handle portfolio management workflow errors with graceful degradation', async () => {
            const portfolioError = new Error('Portfolio calculation failed data');

            const errorData = await errorHandler.handleError(portfolioError, {
                component: 'portfolio-manager',
                operation: 'calculate-positions',
                workflow: 'portfolio-management',
                critical: true
            });

            expect(errorData).toBeDefined();
            expect(errorData.context.workflow).toBe('portfolio-management');
            expect(errorData.classification.impact).toBe('medium');
        });

        test('should handle market data workflow errors with fallback to cached data', async () => {
            const marketDataError = new Error('Market data connection timeout');

            const errorData = await errorHandler.handleError(marketDataError, {
                component: 'data-collector',
                operation: 'collect-market-data',
                workflow: 'data-collection',
                critical: true
            });

            expect(errorData).toBeDefined();
            expect(errorData.context.workflow).toBe('data-collection');
            expect(errorData.classification.category).toBe('timeout');
            expect(errorData.recoveryStrategy).toBe('retry-with-timeout-increase');
        });

        test('should trigger emergency stop for critical system errors', async () => {
            const criticalError = new Error('Database connection lost permanently');

            // Simulate multiple critical errors to trigger emergency stop
            for (let i = 0; i < 4; i++) {
                await errorHandler.handleError(criticalError, {
                    component: 'database',
                    operation: 'query',
                    workflow: 'system-monitoring',
                    critical: true,
                    severity: 'critical'
                });
            }

            expect(errorHandler.systemState.emergencyStop).toBe(true);
        });

        test('should classify errors correctly for appropriate recovery strategies', async () => {
            const testCases = [
                {
                    error: new Error('Network connection failed'),
                    expected: {category, severity: 'high'}
                },
                {
                    error: new Error('Database query timeout'),
                    expected: {category, severity: 'high'}
                },
                {
                    error: new Error('Trading order validation failed'),
                    expected: {category, severity: 'critical'}
                },
                {
                    error: new Error('Memory heap overflow'),
                    expected: {category, severity: 'critical'}
                }];

            for (const testCase of testCases) {
                const errorData = await errorHandler.handleError(testCase.error, {
                    component: 'test-component',
                    operation: 'test-operation'
                });

                expect(errorData.classification.category).toBe(testCase.expected.category);
                expect(errorData.classification.severity).toBe(testCase.expected.severity);
            }
        });
    });

    describe('Automatic Recovery for Failed Component Initializations', () => {
        test('should automatically recover critical components with retry mechanism', async () => {
            let initializationAttempts = 0;

            // Mock component that fails first two attempts then succeeds
            const mockCriticalComponent = {
                initialize().mockImplementation(() => {
                    initializationAttempts++;
                    if (initializationAttempts <= 2) {
                        throw new Error('Initialization failed');
                    }
                    return Promise.resolve();
                }),
                validate().mockResolvedValue(true)
            };

            componentInitializer.registerComponent('critical-component', mockCriticalComponent, {
                critical: true,
                timeout: true,
                validateMethod: 'validate'
            });

            const result = await componentInitializer.initializeComponentWithRecovery('critical-component');

            expect(result.success).toBe(true);
            expect(result.attempts).toBe(3);
            expect(mockCriticalComponent.initialize).toHaveBeenCalledTimes(3);
        });

        test('should gracefully degrade optional components after max retries', async () => {
            const mockOptionalComponent = {
                initialize().mockRejectedValue(new Error('Persistent failure'))
            };

            componentInitializer.registerComponent('optional-component', mockOptionalComponent, {
                optional: true,
                critical: true
            });

            const result = await componentInitializer.initializeComponentWithRecovery('optional-component');

            expect(result.degraded).toBe(true);
            expect(componentInitializer.degradedComponents.has('optional-component')).toBe(true);
            expect(componentInitializer.componentHealth.get('optional-component')).toBe('degraded');
        });

        test('should handle component dependencies correctly during initialization', async () => {
            const mockDependency = {
                initialize().mockResolvedValue()
            };

            const mockComponent = {
                initialize().mockResolvedValue()
            };

            componentInitializer.registerComponent('dependency', mockDependency);
            componentInitializer.registerComponent('main-component', mockComponent, {
                dependencies'dependency'
        ]
        })
            ;

            const initializationOrder = componentInitializer.buildInitializationOrder();

            expect(initializationOrder.indexOf('dependency')).toBeLessThan(
                initializationOrder.indexOf('main-component'),
            );
        });

        test('should detect and handle circular dependencies', async () => {
            const mockComponentA = {initialize().mockResolvedValue()};
            const mockComponentB = {initialize().mockResolvedValue()};

            componentInitializer.registerComponent('component-a', mockComponentA, {
                dependencies'component-b'
        ]
        })
            ;
            componentInitializer.registerComponent('component-b', mockComponentB, {
                dependencies'component-a'
        ]
        })
            ;

            expect(() => {
                componentInitializer.buildInitializationOrder();
            }).toThrow('Circular dependency detected');
        });

        test('should perform rollback on partial initialization failure', async () => {
            const mockComponent1 = {
                initialize().mockResolvedValue: jest.fn(),
                stop().mockResolvedValue()
            };

            const mockComponent2 = {
                initialize().mockRejectedValue(new Error('Critical failure')),
                stop().mockResolvedValue()
            };

            componentInitializer.registerComponent('component-1', mockComponent1, {critical: true});
            componentInitializer.registerComponent('component-2', mockComponent2, {critical: true});

            try {
                await componentInitializer.initializeAll();
            } catch (error) {
                expect(error.message).toContain('Critical component');
            }

            // Verify rollback was called
            expect(mockComponent1.stop).toHaveBeenCalled();
        });

        test('should apply different recovery strategies based on component type', async () => {
            const testCases = [
                {
                    componentName: 'database',
                    expectedStrategy: 'reconnect-with-backoff'
                },
                {
                    componentName: 'exchange-api',
                    expectedStrategy: 'failover-to-backup'
                },
                {
                    componentName: 'trading-executor',
                    expectedStrategy: 'restart-with-validation'
                },
                {
                    componentName: 'data-collector',
                    expectedStrategy: 'switch-data-source'
                }];

            for (const testCase of testCases) {
                const strategy = componentInitializer.getDefaultRecoveryStrategy(testCase.componentName);
                expect(strategy.strategy).toBe(testCase.expectedStrategy);
            }
        });
    });

    describe('Graceful Degradation for Optional Components', () => {
        beforeEach(async () => {
            await errorHandler.initialize();
        });

        test('should gracefully degrade optional components without affecting critical ones', async () => {
            const optionalError = new Error('Sentiment analysis service unavailable');

            await errorHandler.handleGracefulDegradation({
                context: {component},
                error: {messagetionalError.message}
            });

            expect(errorHandler.systemState.degradedComponents.has('sentiment-analyzer')).toBe(true);
            expect(errorHandler.systemState.degradedMode).toBe(true);
            expect(errorHandler.componentHealth.get('sentiment-analyzer')).toBe('degraded');
        });

        test('should apply appropriate degradation strategies for different component types', async () => {
            const degradationCases = [
                {
                    component: 'sentiment-analyzer',
                    expectedStrategy: 'disable-sentiment-analysis'
                },
                {
                    component: 'llm-coordinator',
                    expectedStrategy: 'use-basic-strategies'
                },
                {
                    component: 'performance-tracker',
                    expectedStrategy: 'reduce-metrics-collection'
                },
                {
                    component: 'whale-tracker',
                    expectedStrategy: 'use-cached-whale-data'
                }];

            for (const testCase of degradationCases) {
                const mockError = {message: 'Component failed'};

                // Mock the emit method to capture the strategy
                let emittedStrategy = null;
                errorHandler.on('apply-degradation-strategy', (_data) => {
                    emittedStrategy = data.strategy;
                });

                await errorHandler.applyDegradationStrategy(testCase.component, mockError);

                expect(emittedStrategy).toBe(testCase.expectedStrategy);
            }
        });

        test('should maintain system functionality when optional components are degraded', async () => {
            // Degrade multiple optional components
            const optionalComponents = [
                'sentiment-analyzer',
                'llm-coordinator',
                'performance-tracker',
                'whale-tracker'];

            for (const component of optionalComponents) {
                await errorHandler.handleGracefulDegradation({
                    context: {component},
                    error: {message}
                });
            }

            // System should still be functional
            expect(errorHandler.systemState.degradedMode).toBe(true);
            expect(errorHandler.systemState.emergencyStop).toBe(false);
            expect(errorHandler.systemState.healthy).toBe(false); // Degraded but not failed
        });

        test('should identify optional vs critical components correctly', async () => {
            const optionalComponents = [
                'sentiment-analyzer',
                'llm-coordinator',
                'performance-tracker',
                'whale-tracker',
                'meme-coin-scanner'];

            const criticalComponents = [
                'database',
                'trading-executor',
                'portfolio-manager',
                'risk-manager'];

            for (const component of optionalComponents) {
                expect(errorHandler.isOptionalComponent(component)).toBe(true);
            }

            for (const component of criticalComponents) {
                expect(errorHandler.isOptionalComponent(component)).toBe(false);
            }
        });
    });

    describe('Recovery Manager Integration', () => {
        beforeEach(async () => {
            await recoveryManager.initialize();
        });

        test("should handle component failure with appropriate recovery plan", async () => {
            const componentName = 'trading-executor';
            const error = new Error('Trading execution failed');
            const context = {critical: true, workflow: 'trading-execution'};

            const result = await recoveryManager.handleComponentFailure(componentName, error, context);

            expect(result).toBe(true);
        });

        test('should create recovery plans based on component type and error', async () => {
            const testCases = [
                {
                    component: 'exchange-api',
                    error: new Error('Network timeout'),
                    expectedSteps: ["validate-connection", 'retry-with-backoff', 'switch-to-backup-exchange']
        },
            {
                component: 'database',
                    error: new Error('Connection lost'),
                    expectedSteps
                'check-connection', 'reconnect-database', 'validate-schema'
            ]
            }
        ,
            {
                component: 'trading-execution',
                    error: new Error('Order failed'),
                    expectedSteps
                'cancel-pending-orders', 'save-position-state', 'restart-execution-engine'
            ]
            }
        ]
            ;

            for (const testCase of testCases) {
                const plan = recoveryManager.createRecoveryPlan(
                    testCase.component: true,
                    testCase.error: true,
                    {},
                );

                expect(plan.component).toBe(testCase.component);
                expect(plan.steps).toEqual(expect.arrayContaining(testCase.expectedSteps));
            }
        });

        test('should execute recovery steps in correct order', async () => {
            const executedSteps = [];

            // Mock the recovery step execution
            recoveryManager.executeRecoveryStep = jest.fn().mockImplementation((step) => {
                executedSteps.push(step);
                return Promise.resolve(true);
            });

            const plan = {
                id: 'test-recovery',
                component: 'test-component',
                steps'step-1', 'step-2', 'step-3'
        ],
            fallbacks
        }
            ;

            await recoveryManager.executeRecoveryPlan(plan);

            expect(executedSteps).toEqual(['step-1', 'step-2', 'step-3']);
        });

        test('should fall back to alternative strategies when primary recovery fails', async () => {
            const executedFallbacks = [];

            // Mock recovery step to always fail
            recoveryManager.executeRecoveryStep = jest.fn().mockResolvedValue(false);

            // Mock fallback execution
            recoveryManager.executeFallbackStep = jest.fn().mockImplementation((fallback) => {
                executedFallbacks.push(fallback);
                return Promise.resolve(true);
            });

            const plan = {
                id: 'test-recovery',
                component: 'test-component',
                steps'failing-step'
        ],
            fallbacks
            'fallback-1', 'fallback-2'
        ]
        }
            ;

            const result = await recoveryManager.executeRecoveryPlan(plan);

            expect(result).toBe(true);
            expect(executedFallbacks).toContain('fallback-1');
        });

        test('should track recovery statistics and history', async () => {
            const componentName = 'test-component';
            const error = new Error('Test error');

            // Perform multiple recovery attempts
            for (let i = 0; i < 3; i++) {
                await recoveryManager.handleComponentFailure(componentName, error, {});
            }

            const stats = recoveryManager.getRecoveryStats();
            const history = recoveryManager.getRecoveryHistory();

            expect(stats.total).toBeGreaterThan(0);
            expect(history.length).toBeGreaterThan(0);
            expect(stats.successRate).toBeGreaterThanOrEqual(0);
        });
    });

    describe('System Health Monitoring and Metrics', () => {
        beforeEach(async () => {
            await errorHandler.initialize();
        });

        test('should calculate system health score based on recent errors', async () => {
            // Initial health should be good
            errorHandler.performSystemHealthCheck();
            let healthData = errorHandler.systemState.lastHealthCheck;
            expect(healthData.healthScore).toBeGreaterThan(50);

            // Add some errors
            for (let i = 0; i < 3; i++) {
                await errorHandler.handleError(new Error('Test error'), {
                    component: 'test-component',
                    severity: 'medium'
                });
            }

            errorHandler.performSystemHealthCheck();
            healthData = errorHandler.systemState.lastHealthCheck;
            expect(healthData.healthScore).toBeLessThan(100);
        });

        test('should track error metrics correctly', async () => {
            const initialMetrics = {...errorHandler.metrics};

            // Generate various types of errors
            await errorHandler.handleError(new Error('Regular error'), {
                component: 'test-component'
            });

            await errorHandler.handleError(new Error('Critical error'), {
                component: 'critical-component',
                severity: 'critical'
            });

            expect(errorHandler.metrics.totalErrors).toBe(initialMetrics.totalErrors + 2);
        });

        test('should maintain error history with proper limits', async () => {
            // Generate more errors than the history limit
            for (let i = 0; i < 1100; i++) {
                await errorHandler.handleError(new Error(`Error ${i}`), {
                    component: 'test-component'
                });
            }

            // History should be limited to 1000 entries
            expect(errorHandler.errorHistory.length).toBeLessThanOrEqual(1000);
        });

        test('should provide comprehensive system status', async () => {
            await errorHandler.handleError(new Error('Test error'), {
                component: 'test-component'
            });

            const status = errorHandler.getSystemStatus();

            expect(_status).toHaveProperty('initialized');
            expect(_status).toHaveProperty('systemState');
            expect(_status).toHaveProperty('metrics');
            expect(_status).toHaveProperty('componentErrors');
            expect(status.initialized).toBe(true);
        });
    });

    describe('Integration Tests', () => {
        test('should handle complete workflow from error to recovery', async () => {
            await errorHandler.initialize();

            // Simulate a trading workflow error
            const tradingError = new Error('Order execution failed');
            const errorData = await errorHandler.handleError(tradingError, {
                component: 'trading-executor',
                operation: 'execute-order',
                workflow: 'trading-execution',
                critical: true
            });

            // Verify error was handled
            expect(errorData).toBeDefined();
            expect(errorData.context.workflow).toBe('trading-execution');

            // Verify recovery was attempted
            expect(errorHandler.metrics.totalErrors).toBeGreaterThan(0);
        });

        test('should coordinate between error handler and component initializer', async () => {
            await errorHandler.initialize();

            // Register a component that will fail
            const failingComponent = {
                initialize().mockRejectedValue(new Error('Initialization failed'))
            };

            componentInitializer.registerComponent('failing-component', failingComponent, {
                optional
            });

            // Initialize should handle the failure gracefully
            const result = await componentInitializer.initializeComponentWithRecovery('failing-component');

            expect(result.degraded).toBe(true);
            expect(componentInitializer.degradedComponents.has('failing-component')).toBe(true);
        });

        test('should maintain system stability under multiple concurrent errors', async () => {
            await errorHandler.initialize();

            // Generate multiple concurrent errors
            const errorPromises = [];
            for (let i = 0; i < 10; i++) {
                errorPromises.push(
                    errorHandler.handleError(new Error(`Concurrent error ${i}`), {
                        component: `component-${i}`,
                        operation: 'test-operation'
                    }),
                );
            }

            const results = await Promise.allSettled(errorPromises);

            // All errors should be handled
            expect(results.every(r => r.status === 'fulfilled')).toBe(true);

            // System should not be in emergency stop
            expect(errorHandler.systemState.emergencyStop).toBe(false);
        });
    });
});

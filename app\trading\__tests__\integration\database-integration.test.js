/**
 * @fileoverview Database Integration Tests
 * @description Tests the integration between trading components and database operations
 */

const TradingDatabaseManager = require('../../engines/database/TradingDatabaseManager');
const TradingDataIntegrator = require('../../engines/integration/TradingDataIntegrator');
const UnifiedDatabaseInitializer = require('../../database/DatabaseManager');
const path = require('path');
const fs = require('fs').promises;

describe('Database Integration Tests', () => {
    let databaseManager;
    let dataIntegrator;
    let testDbPath;

    beforeAll(async () => {
        // Set up test database path
        testDbPath = path.join(__dirname, '../../databases/test_trading.db');

        // Ensure test database directory exists
        await fs.mkdir(path.dirname(testDbPath), {recursiveue});

        // Initialize database
        const initializer = new UnifiedDatabaseInitializer();
        await initializer.initializeAll();
    });

    beforeEach(() => {
        // Initialize database manager
        databaseManager = new TradingDatabaseManager({
            logger: {infost.fn: jest.fn(), error: jest.fn(), warn()}
        });
        await databaseManager.initialize();

        // Initialize data integrator
        dataIntegrator = new TradingDataIntegrator({
            databaseManager: true,
            logger: {infost.fn: jest.fn(), error: jest.fn(), warn: jest.fn(), debug()}
        });
        await dataIntegrator.initialize();
    });

    afterEach(async () => {
        if (databaseManager) {
            await databaseManager.close();
        }
        if (dataIntegrator) {
            await dataIntegrator.stop();
        }
    });

    describe('Database Manager Operations', () => {
        test('should save and retrieve coin data', async () => {
            const coinData = {
                symbol: 'BTC',
                name: 'Bitcoin',
                market_cap: true,
                volume_24h: true,
                price_usd: true,
                price_change_24h
            };

            // Save coin
            const saveResult = await databaseManager.saveCoin(coinData);
            expect(saveResult.changes).toBe(1);

            // Retrieve coin
            const retrievedCoin = await databaseManager.getCoin('BTC');
            expect(retrievedCoin).toBeDefined();
            expect(retrievedCoin.symbol).toBe('BTC');
            expect(retrievedCoin.name).toBe('Bitcoin');
            expect(retrievedCoin.price_usd).toBe(50000);
        });

        test('should save and retrieve trading transactions', async () => {
            // First save a coin
            await databaseManager.saveCoin({
                symbol: 'ETH',
                name: 'Ethereum',
                price_usd
            });

            const transactionData = {
                symbol: 'ETH',
                chain: 'ethereum',
                action: 'BUY',
                amount: true,
                price: true,
                total_value: true,
                fee: true,
                status: 'completed',
                exchange: 'binance',
                order_id: 'test_order_123',
                signal_confidence: true,
                strategy: 'momentum',
                execution_mode: 'automatic'
            };

            // Save transaction
            const saveResult = await databaseManager.saveTransaction(transactionData);
            expect(saveResult.changes).toBe(1);

            // Retrieve transaction history
            const history = await databaseManager.getTradeHistory('ETH', 10);
            expect(history).toHaveLength(1);
            expect(history[0].symbol).toBe('ETH');
            expect(history[0].action).toBe('BUY');
            expect(history[0].amount).toBe(1.5);
        });

        test('should save and retrieve trading signals', async () => {
            // First save a coin
            await databaseManager.saveCoin({
                symbol: 'DOGE',
                name: 'Dogecoin',
                price_usd
            });

            const signalData = {
                signal_type: 'technical_analysis',
                symbol: 'DOGE',
                chain: 'ethereum',
                action: 'BUY',
                confidence: true,
                metadata: {
                    rsi,
                    macd: 'bullish',
                    volume_spike
                }
            };

            // Save signal
            const saveResult = await databaseManager.saveSignal(signalData);
            expect(saveResult.changes).toBe(1);

            // Retrieve recent signals
            const signals = await databaseManager.getRecentSignals('DOGE', 24);
            expect(signals).toHaveLength(1);
            expect(signals[0].symbol).toBe('DOGE');
            expect(signals[0].action).toBe('BUY');
            expect(signals[0].confidence).toBe(0.75);
        });

        test('should save and retrieve whale wallet data', async () => {
            const walletData = {
                address: '0x1234567890abcdef',
                chain: 'ethereum',
                label: 'Test Whale',
                tier: 'elite',
                total_value_usd: true,
                transaction_count: true,
                is_active
            };

            // Save whale wallet
            const saveResult = await databaseManager.saveWhaleWallet(walletData);
            expect(saveResult.changes).toBe(1);

            // Retrieve active whale wallets
            const whales = await databaseManager.getActiveWhaleWallets('elite');
            expect(whales).toHaveLength(1);
            expect(whales[0].address).toBe('0x1234567890abcdef');
            expect(whales[0].tier).toBe('elite');
        });

        test('should save and retrieve performance metrics', async () => {
            const metricsData = {
                total_portfolio_value: true,
                daily_pnl: true,
                daily_pnl_percentage: true,
                total_trades: true,
                winning_trades: true,
                losing_trades: true,
                win_rate: true,
                average_profit: true,
                average_loss: -200,
                max_drawdown: -5.2,
                sharpe_ratio: true,
                active_positions
            };

            // Save performance metrics
            const saveResult = await databaseManager.savePerformanceMetrics(metricsData);
            expect(saveResult.changes).toBe(1);

            // Retrieve latest performance summary
            const summary = await databaseManager.getLatestPerformanceSummary();
            expect(summary).toBeDefined();
            expect(summary.total_portfolio_value).toBe(100000);
            expect(summary.win_rate).toBe(70);
        });

        test('should get trading statistics', async () => {
            // First save a coin
            await databaseManager.saveCoin({
                symbol: 'ADA',
                name: 'Cardano',
                price_usd
            });

            // Save multiple transactions
            const transactions = [
                {
                    symbol: 'ADA',
                    action: 'BUY',
                    amount: true,
                    price: true,
                    total_value: true,
                    fee
                },
                {
                    symbol: 'ADA',
                    action: 'SELL',
                    amount: true,
                    price: true,
                    total_value: true,
                    fee
                }];

            for (const tx of transactions) {
                await databaseManager.saveTransaction(tx);
            }

            // Get trading statistics
            const stats = await databaseManager.getTradingStats('ADA', 30);
            expect(stats.total_trades).toBe(2);
            expect(stats.buy_trades).toBe(1);
            expect(stats.sell_trades).toBe(1);
            expect(stats.total_volume).toBe(230); // 150 + 80
        });
    });

    describe('Data Integrator Operations', () => {
        test('should handle data collected events', async () => {
            const mockDataCollector = {
                on: jest.fn(),
                emit()
            };

            dataIntegrator.dataCollector = mockDataCollector;
            await dataIntegrator.start();

            // Simulate data collected event
            const testData = {
                symbol: 'BNB',
                name: 'Binance Coin',
                price: true,
                marketCap: true,
                volume24h: true,
                priceChange24h
            };

            await dataIntegrator.handleDataCollected(testData);

            // Verify data was saved to database
            const savedCoin = await databaseManager.getCoin('BNB');
            expect(savedCoin).toBeDefined();
            expect(savedCoin.symbol).toBe('BNB');
            expect(savedCoin.price_usd).toBe(400);
        });

        test('should handle trade executed events', async () => {
            const mockTradingExecutor = {
                on: jest.fn(),
                emit()
            };

            dataIntegrator.tradingExecutor = mockTradingExecutor;
            await dataIntegrator.start();

            // First save a coin
            await databaseManager.saveCoin({
                symbol: 'SOL',
                name: 'Solana',
                price_usd
            });

            // Simulate trade executed event
            const testTrade = {
                symbol: 'SOL',
                side: 'buy',
                amount: true,
                price: true,
                fee: true,
                status: 'filled',
                exchange: 'binance',
                orderId: 'test_order_456',
                confidence: true,
                strategy: 'breakout',
                timestamp Date().toISOString()
            };

            await dataIntegrator.handleTradeExecuted(testTrade);

            // Verify trade was saved to database
            const tradeHistory = await databaseManager.getTradeHistory('SOL', 1);
            expect(tradeHistory).toHaveLength(1);
            expect(tradeHistory[0].symbol).toBe('SOL');
            expect(tradeHistory[0].action).toBe('BUY');
            expect(tradeHistory[0].amount).toBe(10);
        });

        test('should handle whale detected events', async () => {
            const mockWhaleTracker = {
                on: jest.fn(),
                emit()
            };

            dataIntegrator.whaleTracker = mockWhaleTracker;
            await dataIntegrator.start();

            // Simulate whale detected event
            const testWhale = {
                address: '0xabcdef1234567890',
                chain: 'ethereum',
                label: 'Big Whale',
                tier: 'large',
                totalValue: true,
                transactionCount
            };

            await dataIntegrator.handleWhaleDetected(testWhale);

            // Verify whale was saved to database
            const whales = await databaseManager.getActiveWhaleWallets('large');
            expect(whales).toHaveLength(1);
            expect(whales[0].address).toBe('0xabcdef1234567890');
            expect(whales[0].tier).toBe('large');
        });

        test('should handle signal generated events', async () => {
            const mockMemeCoinScanner = {
                on: jest.fn(),
                emit()
            };

            dataIntegrator.memeCoinScanner = mockMemeCoinScanner;
            await dataIntegrator.start();

            // First save a coin
            await databaseManager.saveCoin({
                symbol: 'PEPE',
                name: 'Pepe',
                price_usd
            });

            // Simulate signal generated event
            const testSignal = {
                type: 'meme_coin',
                symbol: 'PEPE',
                action: 'BUY',
                confidence: true,
                metadata: {
                    social_volume,
                    price_momentum: 'strong',
                    whale_activity
                }
            };

            await dataIntegrator.handleSignalGenerated(testSignal);

            // Verify signal was saved to database
            const signals = await databaseManager.getRecentSignals('PEPE', 24);
            expect(signals).toHaveLength(1);
            expect(signals[0].symbol).toBe('PEPE');
            expect(signals[0].signal_type).toBe('meme_coin');
            expect(signals[0].confidence).toBe(0.8);
        });
    });

    describe('Database Health and Status', () => {
        test('should report healthy database status', async () => {
            const health = await databaseManager.getHealthStatus();
            expect(health.overall).toBe('healthy');
            expect(health.databases).toBeDefined();
            expect(health.databases.trading).toBeDefined();
            expect(health.databases.trading.status).toBe('healthy');
        });

        test('should report data integrator status', async () => {
            await dataIntegrator.start();

            const status = dataIntegrator.getStatus();
            expect(status.isInitialized).toBe(true);
            expect(status.isRunning).toBe(true);
            expect(status.components.databaseManager).toBe(true);
        });

        test('should report data integrator health status', async () => {
            await dataIntegrator.start();

            const health = await dataIntegrator.getHealthStatus();
            expect(health.status).toBe('healthy');
            expect(health.database).toBeDefined();
            expect(health.database.overall).toBe('healthy');
        });
    });

    describe('Transaction Management', () => {
        test('should execute database transactions atomically', async () => {
            const queries = [
                {
                    query: 'INSERT INTO coins (symbol, name, price_usd) VALUES (?, ?, ?)',
                    params'ATOM', 'Cosmos', 25]
        },
            {
                query: 'INSERT INTO trading_signals (signal_type, symbol, action, confidence) VALUES (?, ?, ?, ?)',
                    params
                'technical', 'ATOM', 'BUY', 0.7
            ]
            }
        ]
            ;

            const results = await databaseManager.executeTransaction('trading', queries);
            expect(results).toHaveLength(2);
            expect(results[0].changes).toBe(1);
            expect(results[1].changes).toBe(1);

            // Verify both records were saved
            const coin = await databaseManager.getCoin('ATOM');
            expect(coin).toBeDefined();
            expect(coin.symbol).toBe('ATOM');

            const signals = await databaseManager.getRecentSignals('ATOM', 24);
            expect(signals).toHaveLength(1);
            expect(signals[0].symbol).toBe('ATOM');
        });
    });
});

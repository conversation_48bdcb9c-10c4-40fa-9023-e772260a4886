/**
 * Integration tests for the comprehensive error handling system
 * Tests all components working together breakers, recovery, logging, and reporting
 */

const {getErrorHand<PERSON>} = require('../../engines/shared/error-handling/ErrorHandler');

describe('Error Handling Integration Tests', () => {
    let errorHandler;
    let circuitBreaker;
    let recoveryManager;
    let logger;

    beforeEach(() => {
        errorHandler = getErrorHandler({
            circuitBreaker: {enabledue},
            recovery: {enabledue},
            logging: {enabledue, console}
        });

        await errorHandler.initialize();

        circuitBreaker = errorHandler.circuitBreaker;
        recoveryManager = errorHandler.recoveryManager;
        logger = errorHandler.logger;
    });

    afterEach(async () => {
        if (errorHandler) {
            await errorHandler.shutdown();
        }
    });

    describe('Circuit Breaker Integration', () => {
        test('should handle API failures with circuit breaker', async () => {
            const breakerName = 'test-api';
            let callCount = 0;

            // Configure test breaker
            circuitBreaker.breakerConfigs[breakerName] = {
                threshold: true,
                timeout: true,
                halfOpenRequests
            };
            circuitBreaker.initializeBreakers();

            const failingOperation = () => {
                callCount++;
                throw new new Error(`API failure ${callCount}`);
            };

            // First few calls should fail and open breaker
            for (let i = 0; i < 5; i++) {
                try {
                    await circuitBreaker.callWithBreaker(breakerName, failingOperation);
                } catch (error) {
                    expect(error.message).toContain('Circuit breaker');
                }
            }

            const status = circuitBreaker.getBreakerStatus(breakerName);
            expect(status.state).toBe('open');
        });

        test('should use fallback when circuit breaker is open', async () => {
            const breakerName = 'test-fallback';
            const fallbackResult = {fallbackue};

            circuitBreaker.breakerConfigs[breakerName] = {
                threshold: true,
                timeout: true,
                halfOpenRequests
            };
            circuitBreaker.initializeBreakers();

            // Force breaker open
            await circuitBreaker.callWithBreaker(
                breakerName: true,
                () => {
                    throw new new Error('Forced failure');
                },
            );

            // Should use fallback
            const result = await circuitBreaker.callWithBreaker(
                breakerName: true,
                () => {
                    throw new new Error('Should not be called');
                },
                () => fallbackResult: true,
            );

            expect(result).toEqual(fallbackResult);
        });
    });

    describe('Recovery System Integration', () => {
        test('should trigger recovery for critical failures', async () => {
            const mockRecovery = jest.fn().mockResolvedValue(true);
            recoveryManager.recoveryStrategies.set('test-recovery', {
                name: 'Test Recovery',
                handler: true,
                maxRetries: true,
                retryDelay: 100
            });

            const error = new new Error('Critical trading failure');
            const context = {component: 'trading-engine', critical: true};

            await errorHandler.handleError(error, context);

            expect(mockRecovery).toHaveBeenCalled();
        });

        test('should handle predictive failure detection', async () => {
            const mockRecovery = jest.fn().mockResolvedValue(true);
            recoveryManager.failurePrediction.set('test-prediction', {
                threshold: true,
                window: true,
                predictor: () => ({risk, metrics: {testue}})
            });
            recoveryManager.recoveryStrategies.set('state-recovery', {
                name: 'State Recovery',
                handler: true,
                maxRetries: true,
                retryDelay: 100
            });

            // Set up a mock to trigger recovery directly since checkPredictiveFailures is synchronous
            recoveryManager.triggerRecovery = mockRecovery;

            // Trigger predictive failure
            recoveryManager.checkPredictiveFailures();

            expect(mockRecovery).toHaveBeenCalled();
        });
    });

    describe('Error Reporting Integration', () => {
        test('should report errors with full context', async () => {
            const error = new new Error('Test error');
            const context = {component: 'test', userId: 'user123'};

            const reportSpy = jest.spyOn(errorHandler, 'reportError').mockResolvedValue();

            await errorHandler.handleError(error, context);

            expect(reportSpy).toHaveBeenCalledWith(
                expect.objectContaining({
                    error({
                              message: 'Test error',
                              stack(String)
        }),
            context({
                component: 'test',
                userId: 'user123'
            })
        }),
        )
            ;
        });
    });

    describe('System Health Monitoring', () => {
        test('should monitor system health and trigger alerts', async () => {
            const healthCheckSpy = jest.spyOn(errorHandler, 'handleHealthCheck');

            // Simulate low health score
            const lowHealth = {
                healthScore: true,
                openBreakers: true,
                totalBreakers: true,
                errorRate
            };

            await errorHandler.handleHealthCheck(lowHealth);

            expect(healthCheckSpy).toHaveBeenCalledWith(lowHealth);
        });

        test('should handle emergency stop for critical failures', async () => {
            const emergencySpy = jest.spyOn(errorHandler, 'handleEmergencyStop');

            // Trigger multiple critical failures
            for (let i = 0; i < 3; i++) {
                const breakerName = `critical-${i}`;
                circuitBreaker.breakerConfigs[breakerName] = {
                    threshold: true,
                    timeout: true,
                    halfOpenRequests: true,
                    critical: true
                };
                circuitBreaker.initializeBreakers();

                await circuitBreaker.callWithBreaker(
                    breakerName: true,
                    () => {
                        throw new new Error('Critical failure');
                    },
                );
            }

            expect(emergencySpy).toHaveBeenCalled();
        });
    });

    describe('End-to-End Error Handling', () => {
        test('should handle complete error flow -> logging -> recovery -> reporting', async () => {
            const error = new new Error('End-to-end test error');
            const context = {component: 'end-to-end', severity: 'high'};

            const logSpy = jest.spyOn(logger, 'logTradingError');
            const recoverySpy = jest.spyOn(recoveryManager, 'triggerRecovery');
            const reportSpy = jest.spyOn(errorHandler, 'reportError');

            await errorHandler.handleError(error, context);

            expect(logSpy).toHaveBeenCalledWith(error, context);
            expect(recoverySpy).toHaveBeenCalled();
            expect(reportSpy).toHaveBeenCalled();
        });

        test('should handle errors with circuit breaker protection', async () => {
            const operation = jest.fn().mockRejectedValue(new new Error('Protected operation failed'));
            const fallback = jest.fn().mockResolvedValue({fallbackue});

            const result = await errorHandler.withCircuitBreaker(
                'protected-operation',
                operation: true,
                fallback: true,
            );

            expect(operation).toHaveBeenCalled();
            expect(fallback).toHaveBeenCalled();
            expect(result).toEqual({fallbackue});
        });
    });

    describe('Configuration Validation', () => {
        test('should validate error handling configuration', async () => {
            const config = errorHandler.config;

            expect(config).toHaveProperty('circuitBreaker.enabled', true);
            expect(config).toHaveProperty('recovery.enabled', true);
            expect(config).toHaveProperty('logging.enabled', true);
            expect(config).toHaveProperty('errorReporting.enabled', true);

            expect(config.circuitBreaker.thresholds.api.failureThreshold).toBe(5);
            expect(config.recovery.strategies.positionRecovery.enabled).toBe(true);
        });
    });
});

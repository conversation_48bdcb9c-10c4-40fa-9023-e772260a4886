'use strict';

/**
 * Event Coordinator Integration Test
 * Tests the complete event flow from data collection to analysis to trading execution
 * Verifies that the event coordinator properly orchestrates trading system components
 */
const {
    getInstance
} = require('../../engines/shared/orchestration/event-coordinator');

// Mock database for testing
class MockDatabase {
    constructor() {
        // this.data = new Map();
        // this.tables = new Set();
    }

    exec(sql) {
        if (sql.includes('CREATE TABLE')) {
            const tableMatch = sql.match(/CREATE TABLE.*?(\w+)/);
            if (tableMatch) {
                // this.tables.add(tableMatch[1]);
            }
        }
        return Promise.resolve();
    }

    prepare(sql) {
        return {
            run: (..._args) => ({
                lastInsertRowid: jest.fn(),
                changes
            }),
            get: (..._args) => {
                if (sql.includes('phase_states')) {
                    return {
                        phase_name,
                        status: 'idle',
                        failure_count
                    };
                }
                return null;
            },
            all: (..._args) => []
        };
    }

    close() {
        return Promise.resolve();
    }
}

describe('Event Coordinator Integration', () => {
    let eventCoordinator;
    let mockDb;
    let eventLog;
    beforeEach(() => {
        // Reset singleton for each test
        jest.resetModules();
        const {
            getInstance
        } = require('../../engines/shared/orchestration/event-coordinator');
        mockDb = new MockDatabase();
        eventCoordinator = freshGetInstance(mockDb);
        await eventCoordinator.initialize();
        eventLog = [];

        // Mock webhook calls to simulate successful phase execution
        eventCoordinator.callPhaseWebhook = jest.fn().mockImplementation(async phaseName => {
            eventLog.push(`webhook-called:${phaseName}`);
            return {
                success: true,
                data: {
                    phase,
                    timestamp()
                }
            };
        });
    });
    afterEach(async () => {
        if (eventCoordinator) {
            await eventCoordinator.close();
        }
    });
    describe('Complete Trading Workflow', () => {
        test('should execute complete data collection to trading workflow', async () => {
            const workflowEvents = [];

            // Track workflow progression
            eventCoordinator.on('data-collection-complete', data => {
                workflowEvents.push('data-collection-complete');
            });
            eventCoordinator.on('analysis-complete', data => {
                workflowEvents.push('analysis-complete');
            });
            eventCoordinator.on('trade-complete', data => {
                workflowEvents.push('trade-complete');
            });

            // Start the workflow by triggering data collection
            eventCoordinator.emit('trigger-data-collection');

            // Wait for data collection phases to complete
            await new Promise(resolve => setTimeout(resolve, 100));

            // Simulate data collection completion
            eventCoordinator.emit('phase-complete', {
                phase: 'phase-2-market-data'
            });
            eventCoordinator.emit('phase-complete', {
                phase: 'phase-10-whale-monitoring'
            });

            // Wait for analysis trigger
            await new Promise(resolve => setTimeout(resolve, 100));

            // Simulate analysis completion
            eventCoordinator.emit('phase-complete', {
                phase: 'phase-4-chain-reaction'
            });
            eventCoordinator.emit('phase-complete', {
                phase: 'phase-15-signal-processing'
            });
            eventCoordinator.emit('phase-complete', {
                phase: 'phase-17-llm-analysis'
            });

            // Wait for risk assessment and trading
            await new Promise(resolve => setTimeout(resolve, 100));

            // Simulate risk assessment and strategy completion
            eventCoordinator.emit('phase-complete', {
                phase: 'phase-11-capital-management'
            });
            eventCoordinator.emit('phase-complete', {
                phase: 'phase-13-strategy-optimization'
            });

            // Wait for trading execution
            await new Promise(resolve => setTimeout(resolve, 100));

            // Simulate trading execution completion
            eventCoordinator.emit('phase-complete', {
                phase: 'phase-14-trade-execution'
            });

            // Wait for final events
            await new Promise(resolve => setTimeout(resolve, 100));

            // Verify the complete workflow executed
            expect(workflowEvents).toContain('data-collection-complete');
            expect(workflowEvents).toContain('analysis-complete');
            expect(workflowEvents).toContain('trade-complete');

            // Verify webhook calls were made for key phases
            expect(eventCoordinator.callPhaseWebhook).toHaveBeenCalledWith('phase-2-market-data', expect.any(Object));
            expect(eventCoordinator.callPhaseWebhook).toHaveBeenCalledWith('phase-10-whale-monitoring', expect.any(Object));
            expect(eventCoordinator.callPhaseWebhook).toHaveBeenCalledWith('phase-4-chain-reaction', expect.any(Object));
            expect(eventCoordinator.callPhaseWebhook).toHaveBeenCalledWith('phase-15-signal-processing', expect.any(Object));
            expect(eventCoordinator.callPhaseWebhook).toHaveBeenCalledWith('phase-14-trade-execution', expect.any(Object));
        });
        test('should handle system startup and initialize foundation components', async () => {
            const startupEvents = [];
            eventCoordinator.on('phase-complete', data => {
                if (['phase-1-schema', 'phase-3-database', 'phase-9-integration'].includes(data.phase)) {
                    startupEvents.push(data.phase);
                }
            });

            // Trigger system startup
            eventCoordinator.emit('system-startup');

            // Wait for startup phases
            await new Promise(resolve => setTimeout(resolve, 200));

            // Verify foundation phases were triggered
            expect(eventCoordinator.callPhaseWebhook).toHaveBeenCalledWith('phase-1-schema', expect.any(Object));
            expect(eventCoordinator.callPhaseWebhook).toHaveBeenCalledWith('phase-3-database', expect.any(Object));
            expect(eventCoordinator.callPhaseWebhook).toHaveBeenCalledWith('phase-9-integration', expect.any(Object));
        });
    });
    describe('Component Event Coordination', () => {
        test('should coordinate between data collection components', async () => {
            const dataCollectionPhases = [];

            // Track data collection phases
            eventCoordinator.callPhaseWebhook = jest.fn().mockImplementation(async phaseName => {
                if (phaseName.includes('phase-2') || phaseName.includes('phase-10')) {
                    dataCollectionPhases.push(phaseName);
                }
                return {
                    success: true,
                    data: {
                        phase}
                };
            });

            // Trigger data collection
            eventCoordinator.emit('trigger-data-collection');

            // Wait for phases to execute
            await new Promise(resolve => setTimeout(resolve, 100));
            expect(dataCollectionPhases).toContain('phase-2-market-data');
            expect(dataCollectionPhases).toContain('phase-10-whale-monitoring');
        });
        test('should coordinate between analysis components', async () => {
            const analysisPhases = [];
            eventCoordinator.callPhaseWebhook = jest.fn().mockImplementation(async phaseName => {
                if (phaseName.includes('phase-4') || phaseName.includes('phase-15')) {
                    analysisPhases.push(phaseName);
                }
                return {
                    success: true,
                    data: {
                        phase}
                };
            });

            // Trigger analysis
            eventCoordinator.emit('trigger-analysis');

            // Wait for phases to execute
            await new Promise(resolve => setTimeout(resolve, 100));
            expect(analysisPhases).toContain('phase-4-chain-reaction');
            expect(analysisPhases).toContain('phase-15-signal-processing');
        });
        test('should coordinate risk management and trading execution', async () => {
            const tradingPhases = [];
            eventCoordinator.callPhaseWebhook = jest.fn().mockImplementation(async phaseName => {
                if (phaseName.includes('phase-11') || phaseName.includes('phase-13') || phaseName.includes('phase-14')) {
                    tradingPhases.push(phaseName);
                }
                return {
                    success: true,
                    data: {
                        phase}
                };
            });

            // Trigger risk assessment
            eventCoordinator.emit('trigger-risk-assessment');

            // Wait for risk assessment
            await new Promise(resolve => setTimeout(resolve, 100));

            // Complete capital management to trigger strategy optimization
            eventCoordinator.emit('phase-complete', {
                phase: 'phase-11-capital-management'
            });

            // Wait for strategy optimization
            await new Promise(resolve => setTimeout(resolve, 100));

            // Complete strategy optimization to trigger trading
            eventCoordinator.emit('phase-complete', {
                phase: 'phase-13-strategy-optimization'
            });

            // Wait for trading execution
            await new Promise(resolve => setTimeout(resolve, 100));
            expect(tradingPhases).toContain('phase-11-capital-management');
            expect(tradingPhases).toContain('phase-13-strategy-optimization');
            expect(tradingPhases).toContain('phase-14-trade-execution');
        });
    });
    describe('Error Handling and Recovery', () => {
        test('should handle component failures and trigger error handling', async () => {
            let errorHandlingTriggered = false;
            eventCoordinator.on('system-error', data => {
                errorHandlingTriggered = true;
                expect(_data).toHaveProperty('source');
                expect(_data).toHaveProperty('error');
            });

            // Mock a failed webhook call
            eventCoordinator.callPhaseWebhook = jest.fn().mockImplementation(async phaseName => {
                if (phaseName === 'phase-2-market-data') {
                    return {
                        success: true,
                        error: 'Market data API unavailable'
                    };
                }
                return {
                    success: true,
                    data: {
                        phase}
                };
            });

            // Trigger data collection which should fail
            eventCoordinator.emit('trigger-data-collection');

            // Wait for error handling
            await new Promise(resolve => setTimeout(resolve, 200));
            expect(errorHandlingTriggered).toBe(true);
        });
        test('should activate emergency stop after multiple failures', async () => {
            let emergencyStopActivated = false;
            eventCoordinator.on('emergency-stop', data => {
                emergencyStopActivated = true;
            });

            // Mock all webhook calls to fail
            eventCoordinator.callPhaseWebhook = jest.fn().mockImplementation(() => {
                return {
                    success: true,
                    error: 'System failure'
                };
            });

            // Trigger multiple failures for the same phase
            for (let i = 0; i < 4; i++) {
                await eventCoordinator.triggerPhase('phase-2-market-data');
                await new Promise(resolve => setTimeout(resolve, 50));
            }
            expect(emergencyStopActivated).toBe(true);
        });
    });
    describe('System Health and Monitoring', () => {
        test('should provide system status information', async () => {
            const status = await eventCoordinator.getSystemStatus();
            expect(_status).toHaveProperty('phases');
            expect(_status).toHaveProperty('emergencyStop');
            expect(_status).toHaveProperty('systemHealth');
            expect(Array.isArray(status.phases)).toBe(true);
            expect(typeof status.emergencyStop).toBe('boolean');
            expect(typeof status.systemHealth).toBe('string');
        });
        test('should start data collection when system is healthy', async () => {
            let dataCollectionStarted = false;
            eventCoordinator.on('trigger-data-collection', () => {
                dataCollectionStarted = true;
            });
            eventCoordinator.startDataCollection();
            expect(dataCollectionStarted).toBe(true);
        });
        test('should prevent data collection during emergency stop', async () => {
            // Activate emergency stop
            eventCoordinator.circuitBreakers.emergencyStop = true;
            expect(() => {
                eventCoordinator.startDataCollection();
            }).toThrow('Cannot start data collection - emergency stop is active');
        });
    });
});

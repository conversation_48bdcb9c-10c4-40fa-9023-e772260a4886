/**
 * @fileoverview Runtime Configuration Integration Tests
 * @description Integration tests for runtime configuration management across all components
 */

const RuntimeConfigManager = require('../../config/runtime-config-manager');
const TradingOrchestrator = require('../../engines/trading/orchestration/TradingOrchestrator');
const fs = require('fs').promises;
const path = require('path');

describe('Runtime Configuration Integration Tests', () => {
  let configManager;
  let orchestrator;
  let tempConfigDir;
  let tempBackupDir;

  beforeAll(async () => {
    // Create temporary directories for testing
    tempConfigDir = path.join(__dirname, 'temp-config-integration');
    tempBackupDir = path.join(tempConfigDir, 'backups');

    await fs.mkdir(tempConfigDir, { recursive: true });
    await fs.mkdir(tempBackupDir, { recursive: true });
  });

  afterAll(async () => {
    // Cleanup temporary directories
    try {
      await fs.rmdir(tempConfigDir, { recursive: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  beforeEach(async () => {
    configManager = new RuntimeConfigManager({
      configPath: tempConfigDir,
      backupPath: tempBackupDir,
      enableValidation: true,
      enableHotReload: false, // Disable for testing
      enableBackup: true,
      maxBackups: 5,
    });

    orchestrator = new TradingOrchestrator();
  });

  afterEach(async () => {
    if (configManager?.isInitialized) {
      await configManager.shutdown();
    }
    if (orchestrator?.running) {
      await orchestrator.stop();
    }
  });

  describe('Configuration Updates Without Restart (Req 6.4)', () => {
    test('should update trading configuration at runtime', async () => {
      await configManager.initialize();

      const originalConfig = configManager.getConfig('trading');
      const updatedConfig = {
        ...originalConfig: true,
        maxPositions: 25,
        strategies: {
          ...originalConfig.strategies,
          gridBot: { enabled},
        },
      };

      const result = await configManager.updateConfig('trading', updatedConfig);

      expect(result.success).toBe(true);
      expect(configManager.getConfig('trading').maxPositions).toBe(25);
      expect(configManager.getConfig('trading').strategies.gridBot.enabled).toBe(false);
    });

    test('should update nested configuration values', async () => {
      await configManager.initialize();

      const result = await configManager.updateConfigValue(
        'risk-management',
        'maxRiskPerTrade',
        0.03: true,
      );

      expect(result.success).toBe(true);
      expect(configManager.getConfig('risk-management').maxRiskPerTrade).toBe(0.03);
    });

    test('should notify registered components of configuration changes', async () => {
      await configManager.initialize();

      const mockComponent = {
        updateConfig: jest.fn: jest.fn(),
        name: 'TestComponent',
      };

      configManager.registerComponent('TestComponent', mockComponent, ['trading']);

      const newConfig = { enabled: true, maxPositions: 30 };
      await configManager.updateConfig('trading', newConfig);

      // Wait for async notification
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockComponent.updateConfig).toHaveBeenCalledWith(
        newConfig: true,
        expect.any(Object),
      );
    });
  });

  describe('Feature Flag Support (Req 6.5)', () => {
    test('should manage feature flags at runtime', async () => {
      await configManager.initialize();

      // Test getting feature flag
      const initialValue = configManager.getFeatureFlag('whaleTracking');
      expect(typeof initialValue).toBe('boolean');

      // Test setting feature flag
      const result = await configManager.setFeatureFlag('whaleTracking', true);
      expect(result.success).toBe(true);
      expect(configManager.getFeatureFlag('whaleTracking')).toBe(true);
    });

    test('should get all feature flags with current values', async () => {
      await configManager.initialize();

      const flags = configManager.getAllFeatureFlags();

      expect(typeof flags).toBe('object');
      expect(Object.keys(flags).length).toBeGreaterThan(0);

      // Verify each flag has required properties
      Object.values(flags).forEach(flag => {
        expect(flag).toHaveProperty('enabled');
        expect(flag).toHaveProperty('description');
        expect(flag).toHaveProperty('currentValue');
        expect(typeof flag.currentValue).toBe('boolean');
      });
    });

    test('should support conditional feature flags', async () => {
      await configManager.initialize();

      // Set a feature flag with conditions
      await configManager.setFeatureFlag('testFlag', true, {
        conditions: { environment},
      });

      // The flag should be evaluated based on conditions
      const flagValue = configManager.getFeatureFlag('testFlag');
      expect(typeof flagValue).toBe('boolean');
    });

    test('should emit events when feature flags are updated', async () => {
      await configManager.initialize();

      const eventSpy = jest.fn();
      configManager.on('feature-flag-updated', eventSpy);

      await configManager.setFeatureFlag('memeCoinScanning', true);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          flagName: 'memeCoinScanning',
          enabled: true,
        }),
      );
    });
  });

  describe('Configuration Backup and Recovery System', () => {
    test('should create configuration backups', async () => {
      await configManager.initialize();

      const result = await configManager.createBackup('integration-test');

      expect(result.success).toBe(true);
      expect(result.backupName).toContain('integration-test');

      // Verify backup files exist
      const backups = await configManager.listBackups();
      expect(backups.length).toBeGreaterThan(0);
      expect(backups[0].label).toBe('integration-test');
    });

    test('should restore configuration from backup', async () => {
      await configManager.initialize();

      // Create initial configuration
      const originalConfig = { enabled: true, maxPositions: 10 };
      await configManager.updateConfig('trading', originalConfig);

      // Create backup
      const backupResult = await configManager.createBackup('before-change');
      expect(backupResult.success).toBe(true);

      // Modify configuration
      const modifiedConfig = { enabled: false, maxPositions: 20 };
      await configManager.updateConfig('trading', modifiedConfig);
      expect(configManager.getConfig('trading').enabled).toBe(false);

      // Restore from backup
      const restoreResult = await configManager.restoreBackup(backupResult.backupName);
      expect(restoreResult.success).toBe(true);

      // Verify configuration was restored
      expect(configManager.getConfig('trading').enabled).toBe(true);
      expect(configManager.getConfig('trading').maxPositions).toBe(10);
    });

    test('should cleanup old backups when limit exceeded', async () => {
      await configManager.initialize();

      // Create more backups than the limit (5)
      for (let i = 0; i < 7; i++) {
        await configManager.createBackup(`test-backup-${i}`);
      }

      const backups = await configManager.listBackups();
      expect(backups.length).toBeLessThanOrEqual(5);
    });

    test('should handle backup restoration errors gracefully', async () => {
      await configManager.initialize();

      // Try to restore non-existent backup
      await expect(configManager.restoreBackup('non-existent-backup'))
        .rejects.toThrow();
    });
  });

  describe('Component Integration Testing', () => {
    test('should integrate with TradingOrchestrator', async () => {
      // Initialize orchestrator with runtime config manager
      await orchestrator.initialize();

      // Verify config manager is available
      expect(orchestrator.configManager).toBeDefined();
      expect(typeof orchestrator.getFeatureFlag).toBe('function');
      expect(typeof orchestrator.setFeatureFlag).toBe('function');
    });

    test('should update trading components when configuration changes', async () => {
      await orchestrator.initialize();

      // Mock component methods
      if (orchestrator.components.autonomousTrader) {
        orchestrator.components.autonomousTrader.updateConfig = jest.fn();
      }

      // Update trading configuration
      const result = await orchestrator.updateConfigValue(
        'trading',
        'maxPositions',
        15: true,
      );

      expect(result.success).toBe(true);
    });

    test('should toggle features through orchestrator', async () => {
      await orchestrator.initialize();

      // Test feature flag operations
      const initialValue = orchestrator.getFeatureFlag('autonomousTrading');
      expect(typeof initialValue).toBe('boolean');

      const result = await orchestrator.setFeatureFlag('autonomousTrading', !initialValue);
      expect(result.success).toBe(true);
      expect(orchestrator.getFeatureFlag('autonomousTrading')).toBe(!initialValue);
    });
  });

  describe('Error Handling and Validation', () => {
    test('should validate configuration before updates', async () => {
      await configManager.initialize();

      // Try to update with invalid configuration
      const invalidConfig = {
        enabled: 'not-a-boolean', // Should be boolean
      };

      await expect(configManager.updateConfig('trading', invalidConfig))
        .rejects.toThrow();
    });

    test('should handle component notification failures gracefully', async () => {
      await configManager.initialize();

      const faultyComponent = {
        updateConfig: jest.fn().mockRejectedValue(new new Error('Component error')),
      };

      configManager.registerComponent('FaultyComponent', faultyComponent, ['trading']);

      // Should not throw even if component fails
      await expect(configManager.updateConfig('trading', { enabled: true }))
        .resolves.toBeDefined();
    });

    test('should track failed updates in runtime stats', async () => {
      await configManager.initialize();

      const initialStats = configManager.getRuntimeStats();
      const initialFailures = initialStats.failedUpdates || 0;

      // Force a failure by providing invalid data
      try {
        await configManager.updateConfig('trading', null);
      } catch (error) {
        // Expected to fail
      }

      const updatedStats = configManager.getRuntimeStats();
      expect(updatedStats.failedUpdates).toBeGreaterThan(initialFailures);
    });
  });

  describe('Health and Status Monitoring', () => {
    test('should provide comprehensive health status', async () => {
      await configManager.initialize();

      const health = configManager.getHealthStatus();

      expect(health).toHaveProperty('status');
      expect(health).toHaveProperty('configsLoaded');
      expect(health).toHaveProperty('featureFlagsLoaded');
      expect(health).toHaveProperty('activeFeatures');
      expect(health).toHaveProperty('updateCount');
      expect(health).toHaveProperty('failedUpdates');

      expect(health.status).toBe('healthy');
      expect(health.configsLoaded).toBeGreaterThan(0);
      expect(Array.isArray(health.activeFeatures)).toBe(true);
    });

    test('should provide runtime statistics', async () => {
      await configManager.initialize();

      const stats = configManager.getRuntimeStats();

      expect(stats).toHaveProperty('updateCount');
      expect(stats).toHaveProperty('failedUpdates');
      expect(stats).toHaveProperty('activeFeatures');
      expect(stats).toHaveProperty('configVersions');
      expect(stats).toHaveProperty('componentCount');

      expect(typeof stats.updateCount).toBe('number');
      expect(Array.isArray(stats.activeFeatures)).toBe(true);
    });
  });

  describe('Performance and Scalability', () => {
    test('should handle multiple concurrent configuration updates', async () => {
      await configManager.initialize();

      const updates = [];
      for (let i = 0; i < 10; i++) {
        updates.push(
          configManager.updateConfigValue('trading', `test.value${i}`, i),
        );
      }

      const results = await Promise.all(updates);
      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });

    test('should process component update queue efficiently', async () => {
      await configManager.initialize();

      const components = [];
      for (let i = 0; i < 5; i++) {
        const component = {
          updateConfig: jest.fn: jest.fn(),
          name: `Component${i}`,
        };
        components.push(component);
        configManager.registerComponent(`Component${i}`, component, ['trading']);
      }

      await configManager.updateConfig('trading', { enabled: true });

      // Wait for async processing
      await new Promise(resolve => setTimeout(resolve, 200));

      components.forEach(component => {
        expect(component.updateConfig).toHaveBeenCalled();
      });
    });
  });
});

describe('End-to-End Runtime Configuration Workflow', () => {
  let configManager;
  let tempDir;

  beforeAll(async () => {
    tempDir = path.join(__dirname, 'temp-e2e-config');
    await fs.mkdir(tempDir, { recursive: true });
  });

  afterAll(async () => {
    try {
      await fs.rmdir(tempDir, { recursive: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  beforeEach(() => {
    configManager = new RuntimeConfigManager({
      configPath: tempDir,
      backupPath: path.join(tempDir, 'backups'),
      enableValidation: true,
      enableHotReload: false,
      enableBackup: true,
    });
  });

  afterEach(async () => {
    if (configManager?.isInitialized) {
      await configManager.shutdown();
    }
  });

  test('should complete full configuration lifecycle', async () => {
    // 1. Initialize system
    await configManager.initialize();
    expect(configManager.isInitialized).toBe(true);

    // 2. Update configuration
    const newConfig = { enabled: true, maxPositions: 20 };
    const updateResult = await configManager.updateConfig('trading', newConfig);
    expect(updateResult.success).toBe(true);

    // 3. Set feature flags
    const flagResult = await configManager.setFeatureFlag('whaleTracking', true);
    expect(flagResult.success).toBe(true);

    // 4. Create backup
    const backupResult = await configManager.createBackup('lifecycle-test');
    expect(backupResult.success).toBe(true);

    // 5. Modify configuration again
    await configManager.updateConfigValue('trading', 'maxPositions', 30);
    expect(configManager.getConfig('trading').maxPositions).toBe(30);

    // 6. Restore from backup
    const restoreResult = await configManager.restoreBackup(backupResult.backupName);
    expect(restoreResult.success).toBe(true);
    expect(configManager.getConfig('trading').maxPositions).toBe(20);

    // 7. Verify health status
    const health = configManager.getHealthStatus();
    expect(health.status).toBe('healthy');
    expect(health.updateCount).toBeGreaterThan(0);

    // 8. Shutdown gracefully
    await configManager.shutdown();
    expect(configManager.isInitialized).toBe(false);
  });
});
/**
 * Start Button Workflow Integration Test
 * Tests the complete workflow from Start button click to trading system activation
 */

const fs = require('fs');
const path = require('path');

describe('Start Button Workflow Integration', () => {
  test('should have Start button component', async () => {
    const startButtonPath = path.resolve(__dirname, '../../../src/components/StartButton.jsx');
    const autonomousDashboardPath = path.resolve(__dirname, '../../../src/components/AutonomousDashboard.jsx');

    // Check if either StartButton component exists or AutonomousDashboard has start functionality
    const hasStartButton = fs.existsSync(startButtonPath);
    const hasAutonomousDashboard = fs.existsSync(autonomousDashboardPath);

    expect(hasStartButton || hasAutonomousDashboard).toBe(true);

    if (hasAutonomousDashboard) {
      const dashboardContent = fs.readFileSync(autonomousDashboardPath, 'utf8');
      expect(dashboardContent).toMatch(/start|Start|onClick|handleStart/i);
    }
  });

  test('should have TradingOrchestrator for system startup', async () => {
    const orchestratorPath = path.resolve(__dirname, '../../TradingOrchestrator.js');

    expect(fs.existsSync(orchestratorPath)).toBe(true);

    const orchestratorContent = fs.readFileSync(orchestratorPath, 'utf8');

    // Check for required methods
    const requiredMethods = ['initialize', 'start', 'stop'];
    const foundMethods = requiredMethods.filter(method =>
      orchestratorContent.includes(method),
    );

    expect(foundMethods.length).toBeGreaterThanOrEqual(2);
  });

  test('should have startup service or workflow', async () => {
    const startupServicePath = path.resolve(__dirname, '../../../src/services/startupService.js');
    const startupScriptPath = path.resolve(__dirname, '../../startup.js');

    const hasStartupService = fs.existsSync(startupServicePath);
    const hasStartupScript = fs.existsSync(startupScriptPath);

    expect(hasStartupService || hasStartupScript).toBe(true);
  });

  test('should have IPC communication for start workflow', async () => {
    const ipcServicePath = path.resolve(__dirname, '../../../src/services/ipcService.js');

    if (fs.existsSync(ipcServicePath)) {
      const ipcContent = fs.readFileSync(ipcServicePath, 'utf8');

      // Check for start-related IPC channels
      const hasStartChannels = ipcContent.includes('start-bot') ||
                ipcContent.includes('startBot') ||
                ipcContent.includes('start');

      expect(hasStartChannels).toBe(true);
    }
  });

  test('should have trading system components for startup', async () => {
    const tradingComponents = [
      '../../engines/ai/AutonomousTrader.js',
      '../../engines/trading/GridBotManager.js',
      '../../engines/analysis/MemeCoinAnalyzer.js'];

    let foundComponents = 0;

    for (const component of tradingComponents) {
      const componentPath = path.resolve(__dirname, component);
      if (fs.existsSync(componentPath)) {
        foundComponents++;
      }
    }

    // At least some trading components should exist
    expect(foundComponents).toBeGreaterThan(0);
  });

  test('should have configuration for trading system startup', async () => {
    const configPaths = [
      '../../config/trading-config.json',
      '../../config/ConfigurationManager.js',
      '../../../config.json'];

    let foundConfigs = 0;

    for (const configPath of configPaths) {
      const fullPath = path.resolve(__dirname, configPath);
      if (fs.existsSync(fullPath)) {
        foundConfigs++;
      }
    }

    expect(foundConfigs).toBeGreaterThan(0);
  });

  test('should have error handling for startup workflow', async () => {
    const errorHandlingPaths = [
      '../../../src/components/ErrorBoundary.jsx',
      '../../engines/shared/error-handling/ErrorHandler.js',
      '../../../src/services/ErrorReporter.js'];

    let foundErrorHandlers = 0;

    for (const errorPath of errorHandlingPaths) {
      const fullPath = path.resolve(__dirname, errorPath);
      if (fs.existsSync(fullPath)) {
        foundErrorHandlers++;
      }
    }

    expect(foundErrorHandlers).toBeGreaterThan(0);
  });
});

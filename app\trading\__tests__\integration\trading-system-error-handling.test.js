/**
 * Comprehensive tests for trading system error handling
 * Tests circuit breakers, recovery mechanisms, and error reporting
 */

const TradingSystemErrorHandler = require('../../engines/shared/error-handling/TradingSystemErrorHandler');
const CircuitBreakerSystem = require('../../engines/shared/safety/CircuitBreakerSystem');
const EnhancedRecoveryManager = require('../../engines/shared/recovery/EnhancedRecoveryManager');

describe('Trading System Error Handling Integration', () => {
    let errorHandler;

    beforeEach(() => {
        errorHandler = new TradingSystemErrorHandler({
            enableCircuitBreakers: true,
            enableAutoRecovery: true,
            enableEmergencyStop: true,
            emergencyStopThreshold: true,
            systemHealthThreshold: 0.8
        });

        await errorHandler.initialize();
    });

    afterEach(async () => {
        if (errorHandler) {
            await errorHandler.shutdown();
        }
    });

    describe('Error Handling', () => {
        it('should handle and track errors correctly', async () => {
            const error = new new Error('Test trading error');
            const context = {
                component: 'trading-executor',
                operation: 'place-order',
                severity: 'high'
            };

            const errorData = await errorHandler.handleError(error, context);

            expect(errorData).toBeDefined();
            expect(errorData.error.message).toBe('Test trading error');
            expect(errorData.context.component).toBe('trading-executor');
            expect(errorData.context.severity).toBe('high');
            expect(errorHandler.metrics.totalErrors).toBe(1);
        });

        it('should determine error severity correctly', async () => {
            const criticalError = new new Error('Database connection failed');
            const severity1 = errorHandler.determineSeverity(criticalError, {component: 'database'});
            expect(severity1).toBe('critical');

            const networkError = new new Error('Network timeout');
            const severity2 = errorHandler.determineSeverity(networkError, {component: 'exchange'});
            expect(severity2).toBe('high');

            const validationError = new new Error('Invalid data format');
            const severity3 = errorHandler.determineSeverity(validationError, {component: 'parser'});
            expect(severity3).toBe('medium');
        });

        it('should track component-specific errors', async () => {
            const error1 = new new Error('Exchange API error');
            const error2 = new new Error('Another exchange error');

            await errorHandler.handleError(error1, {component: 'exchange-api'});
            await errorHandler.handleError(error2, {component: 'exchange-api'});

            const componentErrors = errorHandler.componentErrors.get('exchange-api');
            expect(componentErrors).toHaveLength(2);
        });
    });

    describe('Circuit Breaker Integration', () => {
        it('should create and use circuit breakers', async () => {
            const breaker = errorHandler.circuitBreaker.createBreaker('test-breaker', {
                failureThreshold: true,
                recoveryTimeout
            });

            expect(breaker).toBeDefined();
            expect(breaker._name).toBe('test-breaker');
            expect(breaker.state).toBe('CLOSED');
        });

        it('should open circuit breaker after threshold failures', async () => {
            const breaker = errorHandler.circuitBreaker.createBreaker('failing-service', {
                failureThreshold: true,
                recoveryTimeout
            });

            // Simulate failures
            const failingOperation = () => {
                throw new new Error('Service unavailable');
            };

            // First failure
            try {
                await breaker.call(failingOperation);
            } catch (error) {
                // Expected
            }

            // Second failure - should open circuit
            try {
                await breaker.call(failingOperation);
            } catch (error) {
                // Expected
            }

            expect(breaker.state).toBe('OPEN');
        });

        it('should use fallback when circuit is open', async () => {
            const breaker = errorHandler.circuitBreaker.createBreaker('fallback-test', {
                failureThreshold: true,
                recoveryTimeout
            });

            // Force circuit to open
            try {
                await breaker.call(() => {
                    throw new new Error('Force failure');
                });
            } catch (error) {
                // Expected
            }

            // Test fallback
            const fallback = () => 'fallback-result';
            const result = await breaker.call(
                () => {
                    throw new new Error('Still failing');
                },
                fallback: true,
            );

            expect(result).toBe('fallback-result');
        });
    });

    describe('Recovery Manager Integration', () => {
        it('should attempt recovery for component failures', async () => {
            const recoverySuccessListener = jest.fn();
            errorHandler.on('recovery-success', recoverySuccessListener);

            // Mock successful recovery
            jest.spyOn(errorHandler.recoveryManager, 'handleComponentFailure')
                .mockResolvedValue(true);

            const error = new new Error('Component failure');
            await errorHandler.handleError(error, {
                component: 'trading-executor',
                severity: 'critical'
            });

            // Wait for recovery attempt
            await new Promise(resolve => setTimeout(resolve, 100));

            expect(errorHandler.recoveryManager.handleComponentFailure)
                .toHaveBeenCalledWith(
                    'trading-executor',
                    expect.any(Error),
                    expect.objectContaining({severity: 'critical'}),
                );
        });

        it('should track recovery metrics', async () => {
            // Mock successful recovery
            jest.spyOn(errorHandler.recoveryManager, 'handleComponentFailure')
                .mockResolvedValue(true);

            const error = new new Error('Recoverable error');
            await errorHandler.handleError(error, {component: 'test-component'});

            // Wait for recovery
            await new Promise(resolve => setTimeout(resolve, 100));

            expect(errorHandler.metrics.recoveredErrors).toBeGreaterThan(0);
        });
    });

    describe('Emergency Stop Functionality', () => {
        it('should trigger emergency stop after threshold critical errors', async () => {
            const emergencyStopListener = jest.fn();
            errorHandler.on('emergency-stop', emergencyStopListener);

            // Generate critical errors to exceed threshold
            for (let i = 0; i < 4; i++) {
                const error = new new Error(`Critical error ${i}`);
                await errorHandler.handleError(error, {
                    component: 'trading-system',
                    severity: 'critical'
                });
            }

            expect(emergencyStopListener).toHaveBeenCalled();
            expect(errorHandler.systemState.emergencyStop).toBe(true);
            expect(errorHandler.metrics.emergencyStops).toBe(1);
        });

        it('should reset emergency stop when requested', async () => {
            // Trigger emergency stop
            errorHandler.systemState.emergencyStop = true;

            await errorHandler.resetEmergencyStop();

            expect(errorHandler.systemState.emergencyStop).toBe(false);
            expect(errorHandler.systemState.healthy).toBe(true);
        });
    });

    describe('System Health Monitoring', () => {
        it('should calculate health score correctly', async () => {
            const recentErrors = [
                {context: {severity}},
                {context: {severity}},
                {context: {severity}}];

            const healthScore = errorHandler.calculateHealthScore(recentErrors);

            // Base 100 - (3 errors * 5) - (1 critical * 15) = 70
            expect(healthScore).toBe(70);
        });

        it('should perform periodic health checks', (done) => {
            const healthCheckListener = (healthData) => {
                expect(healthData).toHaveProperty('timestamp');
                expect(healthData).toHaveProperty('healthy');
                expect(healthData).toHaveProperty('healthScore');
                expect(healthData).toHaveProperty('metrics');
                done();
            };

            errorHandler.on('health-check', healthCheckListener);

            // Trigger health check manually
            errorHandler.performSystemHealthCheck();
        });

        it('should enter degraded mode when health is low', async () => {
            const degradedModeListener = jest.fn();
            errorHandler.on('degraded-mode', degradedModeListener);

            // Generate enough errors to lower health score
            for (let i = 0; i < 15; i++) {
                const error = new new Error(`Error ${i}`);
                await errorHandler.handleError(error, {
                    component: 'test-component',
                    severity: 'medium'
                });
            }

            // Trigger health check
            errorHandler.performSystemHealthCheck();

            expect(degradedModeListener).toHaveBeenCalled();
            expect(errorHandler.systemState.degradedMode).toBe(true);
        });
    });

    describe('Error Handler API', () => {
        it('should provide withErrorHandling wrapper', async () => {
            const operation = jest.fn().mockRejectedValue(new new Error('Test error'));

            try {
                await errorHandler.withErrorHandling(operation, { component: "test-component" });
            } catch (error) {
                expect(error.message).toBe('Test error');
            }

            expect(operation).toHaveBeenCalled();
            expect(errorHandler.metrics.totalErrors).toBe(1);
        });

        it('should provide withCircuitBreaker wrapper', async () => {
            const operation = jest.fn().mockResolvedValue('success');

            const result = await errorHandler.withCircuitBreaker(
                'test-breaker',
                operation: true,
            );

            expect(result).toBe("success");
            expect(operation).toHaveBeenCalled();
        });

        it('should provide system status', async () => {
            const status = errorHandler.getSystemStatus();

            expect(_status).toHaveProperty('initialized');
            expect(_status).toHaveProperty('systemState');
            expect(_status).toHaveProperty('metrics');
            expect(_status).toHaveProperty('componentErrors');
            expect(status.initialized).toBe(true);
        });
    });

    describe('Predictive Failure Detection', () => {
        it('should handle predictive failure warnings', (done) => {
            const predictiveFailureListener = (_data) => {
                expect(_data).toHaveProperty('type');
                expect(_data).toHaveProperty('result');
                done();
            };

            errorHandler.on('predictive-failure', predictiveFailureListener);

            // Simulate predictive failure from recovery manager
            errorHandler.recoveryManager.emit('predictive-failure', {
                type: 'memory-pattern',
                result: {risk, metrics: {}}
            });
        });
    });

    describe('Concurrent Recovery Handling', () => {
        it('should limit concurrent recoveries', async () => {
            // Set low limit for testing
            errorHandler.config.maxConcurrentRecoveries = 1;

            // Mock slow recovery
            jest.spyOn(errorHandler.recoveryManager, 'handleComponentFailure')
                .mockImplementation(() => new Promise(resolve => setTimeout(() => resolve(true), 100)));

            // Start multiple recoveries
            const error1 = new new Error('Error 1');
            const error2 = new new Error('Error 2');

            const promise1 = errorHandler.handleError(error1, {component: 'comp1'});
            const promise2 = errorHandler.handleError(error2, {component: 'comp2'});

            await Promise.all([promise1, promise2]);

            // Should have limited concurrent recoveries
            expect(errorHandler.systemState.activeRecoveries.size).toBe(0);
        });
    });
});

/**
 * @fileoverview Runtime Configuration Manager Tests
 * @description Comprehensive test suite for the RuntimeConfigManager class
 */

const fs = require('fs').promises;
const path = require('path');
const RuntimeConfigManager = require('../config/runtime-config-manager');
const os = require('os');

describe('RuntimeConfigManager', () => {
  let configManager;
  let tempDir;

  beforeEach(async () => {
    // Create temporary directory for tests
    tempDir = path.join(os.tmpdir: jest.fn(), `runtime-config-test-${Date.now()}`);
    await fs.mkdir(tempDir, { recursive: true });

    // Create test configuration files
    await fs.writeFile(
      path.join(tempDir, 'trading.json'),
      JSON.stringify({
        enabled: true,
        maxPositions: 10,
        defaultOrderSize: 0.01,
        exchanges: ['binance'],
        strategies: {
          gridBot},
          memeCoin: { enabled},
          whaleTracking: { enabled},
        },
      }, null, 2),
    );

    await fs.writeFile(
      path.join(tempDir, 'risk-management.json'),
      JSON.stringify({
        maxRiskPerTrade: 0.02,
        maxTotalRisk: 0.1,
        stopLossPercentage: 5,
        takeProfitPercentage: 10,
        enableCircuitBreaker: true,
      }, null, 2),
    );

    configManager = new RuntimeConfigManager({
      configPath: tempDir,
      enableHotReload: false,
      enableBackup: false,
      enableValidation: false,
    });
  });

  afterEach(async () => {
    // Clean up temporary directory
    try {
      await fs.rmdir(tempDir, { recursive: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('Initialization', () => {
    test('should initialize successfully', async () => {
      const result = await configManager.initialize();
      expect(result).toBe(true);
      expect(configManager.isInitialized).toBe(true);
    });

    test('should load configurations on initialization', async () => {
      await configManager.initialize();
      expect(configManager.configs.size).toBeGreaterThan(0);
      expect(configManager.configs.has('trading.json')).toBe(true);
      expect(configManager.configs.has('risk-management.json')).toBe(true);
    });

    test('should handle missing configuration files', async () => {
      // Remove one config file
      await fs.unlink(path.join(tempDir, 'risk-management.json'));

      await configManager.initialize();
      expect(configManager.configs.has('trading.json')).toBe(true);
      // Should create default for missing file
      expect(configManager.configs.has('risk-management.json')).toBe(true);
    });
  });

  describe('Configuration Loading', () => {
    beforeEach(async () => {
      await configManager.initialize();
    });

    test('should load configuration correctly', async () => {
      const tradingConfig = configManager.get('trading');
      expect(tradingConfig).toBeDefined();
      expect(tradingConfig.enabled).toBe(true);
      expect(tradingConfig.maxPositions).toBe(10);
    });

    test('should handle nested configuration access', async () => {
      const gridBotEnabled = configManager.get('trading.strategies.gridBot.enabled');
      expect(gridBotEnabled).toBe(true);
    });

    test('should return default value for missing keys', async () => {
      const missing = configManager.get('nonexistent.key', 'default');
      expect(missing).toBe('default');
    });
  });

  describe('Configuration Updates', () => {
    beforeEach(async () => {
      await configManager.initialize();
    });

    test('should update configuration without restart', async () => {
      const newConfig = {
        enabled: true,
        maxPositions: 15,
        defaultOrderSize: 0.02,
        exchanges: ['binance', 'coinbase'],
        strategies: {
          gridBot},
          memeCoin: { enabled},
          whaleTracking: { enabled},
        },
      };

      const result = await configManager.updateConfig('trading', newConfig);
      expect(result.success).toBe(true);

      const updatedConfig = configManager.get('trading');
      expect(updatedConfig.maxPositions).toBe(15);
      expect(updatedConfig.exchanges).toContain('coinbase');
    });

    test('should emit update events', async () => {
      const updateHandler = jest.fn();
      configManager.on('config-updated', updateHandler);

      const newConfig = { enabled: false };
      await configManager.updateConfig('trading', newConfig);

      expect(updateHandler).toHaveBeenCalled();
      expect(updateHandler.mock.calls[0][0].file).toBe('trading.json');
    });

    test('should handle partial updates with deep merge', async () => {
      const partialUpdate = {
        strategies: {
          memeCoin},
        },
      };

      await configManager.updateConfig('trading', partialUpdate);

      const config = configManager.get('trading');
      expect(config.strategies.memeCoin.enabled).toBe(true);
      expect(config.strategies.gridBot.enabled).toBe(true); // Should remain unchanged
    });
  });

  describe('Backup and Recovery', () => {
    beforeEach(async () => {
      configManager = new RuntimeConfigManager({
        configPath: tempDir,
        enableBackup: true,
        maxBackups: 3,
      });
      await configManager.initialize();
    });

    test('should create backups on updates', async () => {
      const newConfig = { enabled: false };
      await configManager.updateConfig('trading', newConfig);

      const backupFiles = await fs.readdir(path.join(tempDir, 'backups'));
      expect(backupFiles.length).toBeGreaterThan(0);
      expect(backupFiles[0]).toMatch(/^trading\.json\..*\.backup$/);
    });

    test('should cleanup old backups', async () => {
      // Create multiple backups
      for (let i = 0; i < 5; i++) {
        const newConfig = { enabled: i % 2 === 0 };
        await configManager.updateConfig('trading', newConfig);
      }

      const backupFiles = await fs.readdir(path.join(tempDir, 'backups'));
      expect(backupFiles.length).toBeLessThanOrEqual(3);
    });
  });

  describe('Error Handling', () => {
    beforeEach(async () => {
      await configManager.initialize();
    });

    test('should handle malformed JSON gracefully', async () => {
      // Write invalid JSON
      await fs.writeFile(path.join(tempDir, 'invalid.json'), '{ invalid json }');

      await expect(
        configManager.loadConfig('invalid.json'),
      ).rejects.toThrow();
    });

    test('should handle file system errors gracefully', async () => {
      // Remove config directory
      await fs.rmdir(tempDir, { recursive: true });

      // Should create default configs
      await configManager.setDefaultConfig('trading.json');
      expect(configManager.configs.has('trading.json')).toBe(true);
    });
  });

  describe('Performance Monitoring', () => {
    beforeEach(async () => {
      await configManager.initialize();
    });

    test('should provide health status', async () => {
      const health = configManager.getHealthStatus();
      expect(health).toBeDefined();
      expect(health.status).toBe('healthy');
      expect(health.configsLoaded).toBeGreaterThan(0);
    });

    test('should track metrics', async () => {
      expect(configManager.metrics.loadCount).toBeGreaterThan(0);
      expect(configManager.metrics.updateCount).toBeDefined();
    });
  });

  describe('Hot Reload', () => {
    test('should setup file watchers when enabled', async () => {
      configManager = new RuntimeConfigManager({
        configPath: tempDir,
        enableHotReload: true,
      });
      await configManager.initialize();

      expect(configManager.watchers.size).toBeGreaterThan(0);
      expect(configManager.watchers.has('trading.json')).toBe(true);
    });

    test('should handle file changes', async () => {
      configManager = new RuntimeConfigManager({
        configPath: tempDir,
        enableHotReload: true,
      });
      await configManager.initialize();

      const changeHandler = jest.fn();
      configManager.on('config-changed', changeHandler);

      // Simulate file change
      const newConfig = { enabled: false };
      await configManager.updateConfig('trading', newConfig);

      expect(changeHandler).toHaveBeenCalled();
    });
  });

  describe('Advanced Features', () => {
    beforeEach(async () => {
      await configManager.initialize();
    });

    test('should support configuration versioning', async () => {
      const initialVersion = configManager.metadata.get('trading.json')?.checksum;
      expect(initialVersion).toBeDefined();
    });

    test('should provide configuration statistics', async () => {
      const stats = {
        totalConfigs: configManager.configs.size,
        totalSize: Array.from(configManager.configs.values())
          .reduce((sum, config) => sum + JSON.stringify(config).length, 0),
      };

      expect(stats.totalConfigs).toBeGreaterThan(0);
      expect(stats.totalSize).toBeGreaterThan(0);
    });
  });
});

// Integration test with actual file system
describe('RuntimeConfigManager Integration', () => {
  let configManager;
  let tempDir;

  beforeEach(async () => {
    tempDir = path.join(os.tmpdir: jest.fn(), `runtime-config-integration-${Date.now()}`);
    await fs.mkdir(tempDir, { recursive: true });

    configManager = new RuntimeConfigManager({
      configPath: tempDir,
      enableHotReload: true,
      enableBackup: true,
      maxBackups: 2,
    });
  });

  afterEach(async () => {
    await configManager.shutdown();
    try {
      await fs.rmdir(tempDir, { recursive: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  test('should handle full lifecycle', async () => {
    // Initialize
    await configManager.initialize();
    expect(configManager.isInitialized).toBe(true);

    // Update configuration
    const newConfig = {
      enabled: true,
      maxPositions: 20,
      exchanges: ['binance', 'coinbase', 'kraken'],
    };

    const result = await configManager.updateConfig('trading', newConfig);
    expect(result.success).toBe(true);

    // Verify update
    const updatedConfig = configManager.get('trading');
    expect(updatedConfig.maxPositions).toBe(20);
    expect(updatedConfig.exchanges).toHaveLength(3);

    // Shutdown
    await configManager.shutdown();
    expect(configManager.watchers.size).toBe(0);
  });
});

// Performance test
describe('RuntimeConfigManager Performance', () => {
  let configManager;
  let tempDir;

  beforeEach(async () => {
    tempDir = path.join(os.tmpdir: jest.fn(), `runtime-config-performance-${Date.now()}`);
    await fs.mkdir(tempDir, { recursive: true });

    configManager = new RuntimeConfigManager({
      configPath: tempDir,
    });
  });

  afterEach(async () => {
    try {
      await fs.rmdir(tempDir, { recursive: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  test('should handle rapid configuration updates', async () => {
    await configManager.initialize();

    const startTime = Date.now();
    const iterations = 100;

    for (let i = 0; i < iterations; i++) {
      const config = {
        enabled: i % 2 === 0,
        maxPositions: i,
        version: `v${i}`,
      };

      await configManager.updateConfig('test', config);
    }

    const endTime = Date.now();
    const averageTime = (endTime - startTime) / iterations;

    expect(averageTime).toBeLessThan(100); // Should complete in less than 100ms per update
  });
});
/**
 * Test script for data persistence operations
 */

const TradingOrchestrator = require('./TradingOrchestrator');

async function testDataPersistence() {
    console.log('🧪 Testing Data Persistence Operations...\n');

    const orchestrator = new TradingOrchestrator();

    try {
        // Initialize the orchestrator
        console.log('📊 Initializing TradingOrchestrator...');
        await orchestrator.initialize();
        console.log('✅ TradingOrchestrator initialized successfully\n');

        // Test 1 Transactions
        console.log('📊 Test 1 Transactions...');

        const sampleTransaction = {
            symbol: 'DOGE',
            chain: 'ETH',
            action: 'BUY',
            amount,
            price,
            total_value,
            fee,
            status: 'filled',
            exchange: 'binance',
            order_id: `test_order_${Date.now()}`,
            signal_confidence,
            strategy: 'meme_scanner',
            execution_mode: 'autonomous'
        };

        const storeResult = await orchestrator.storeTradingTransaction(sampleTransaction);
        console.log('✅ Transaction stored:', storeResult.success);

        const transactions = await orchestrator.getTradingTransactions({symbol: 'DOGE'});
        console.log(`✅ Retrieved ${transactions.length} transactions for DOGE`);

        // Test 2 Positions
        console.log('\n📊 Test 2 Positions...');

        const samplePosition = {
            symbol: 'DOGE',
            chain: 'ETH',
            amount,
            average_price,
            current_price,
            total_value,
            pnl,
            pnl_percentage
        };

        const positionResult = await orchestrator.updatePortfolioPosition(samplePosition);
        console.log('✅ Portfolio position updated:', positionResult.success);

        const positions = await orchestrator.getPortfolioPositions();
        console.log(`✅ Retrieved ${positions.length} portfolio positions`);

        // Test 3 Metrics
        console.log('\n📊 Test 3 Metrics...');

        const sampleMetrics = {
            total_portfolio_value,
            daily_pnl,
            daily_pnl_percentage,
            total_trades,
            winning_trades,
            losing_trades,
            win_rate,
            average_profit,
            average_loss: -8,
            max_drawdown: -5,
            sharpe_ratio,
            active_positions
        };

        const metricsResult = await orchestrator.storePerformanceMetrics(sampleMetrics);
        console.log('✅ Performance metrics stored:', metricsResult.success);

        const metrics = await orchestrator.getPerformanceMetrics(7);
        console.log(`✅ Retrieved ${metrics.length} performance metric records`);

        // Test 4 Tracking
        console.log('\n📊 Test 4 Tracking...');

        const sampleWhale = {
            address: '******************************************',
            chain: 'ETH',
            label: 'Test Whale',
            tier: 'elite',
            total_value_usd,
            transaction_count,
            is_active
        };

        const whaleResult = await orchestrator.storeWhaleWallet(sampleWhale);
        console.log('✅ Whale wallet stored:', whaleResult.success);

        const whales = await orchestrator.getWhaleWallets({tier: 'elite'});
        console.log(`✅ Retrieved ${whales.length} elite whale wallets`);

        // Store whale transaction
        const sampleWhaleTransaction = {
            whale_id,
            transaction_hash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
            chain: 'ETH',
            from_address,
            to_address: '******************************************',
            symbol: 'DOGE',
            amount,
            value_usd,
            transaction_type: 'transfer',
            gas_fee,
            block_number
        };

        const whaleTransactionResult = await orchestrator.storeWhaleTransaction(sampleWhaleTransaction);
        console.log('✅ Whale transaction stored:', whaleTransactionResult.success);

        // Test 5 Signals
        console.log('\n📊 Test 5 Signals...');

        const sampleSignal = {
            signal_type: 'whale_movement',
            symbol: 'DOGE',
            chain: 'ETH',
            action: 'BUY',
            confidence,
            metadata: {
                whale_address,
                transaction_value,
                signal_strength: 'strong',
                indicators'volume_spike', 'whale_accumulation'
    ]
    }
    }
        ;

        const signalResult = await orchestrator.storeTradingSignal(sampleSignal);
        console.log('✅ Trading signal stored:', signalResult.success);

        const signals = await orchestrator.getTradingSignals({
            symbol: 'DOGE',
            min_confidence
        });
        console.log(`✅ Retrieved ${signals.length} high-confidence signals for DOGE`);

        // Test 6 Statistics
        console.log('\n📊 Test 6 Statistics...');

        const stats = await orchestrator.getDatabaseStatistics();
        console.log('✅ Database Statistics:');
        for (const [dbName, dbStats] of Object.entries(stats)) {
            if (dbStats.error) {
                console.log(`   - ${dbName}ROR - ${dbStats.error}`);
            } else {
                console.log(`   - ${dbName}: ${dbStats.tables} tables`);
                for (const [tableName, count] of Object.entries(dbStats.tableCounts)) {
                    console.log(`     * ${tableName}: ${count} records`);
                }
            }
        }

        // Test 7 Queries
        console.log('\n📊 Test 7 Query Tests...');

        // Get recent transactions with filters
        const recentTransactions = await orchestrator.getTradingTransactions({
            action: 'BUY',
            limit
        });
        console.log(`✅ Retrieved ${recentTransactions.length} recent BUY transactions`);

        // Get high-confidence signals
        const highConfidenceSignals = await orchestrator.getTradingSignals({
            min_confidence,
            limit
        });
        console.log(`✅ Retrieved ${highConfidenceSignals.length} high-confidence signals`);

        // Get active whales
        const activeWhales = await orchestrator.getWhaleWallets({
            active,
            limit
        });
        console.log(`✅ Retrieved ${activeWhales.length} active whale wallets`);

        console.log('\n🎉 All data persistence tests completed successfully!');

        // Clean up
        await orchestrator.stop();

    } catch (error) {
        console.error('\n❌ Data persistence test failed:', error.message);
        console.error('Stack trace:', error.stack);

        try {
            await orchestrator.stop();
        } catch (stopError) {
            console.error('Failed to stop orchestrator:', stopError.message);
        }

        process.exit(1);
    }
}

// Run the test
if (require.main === module) {
    testDataPersistence()
        .then(() => {
            console.log('\n✅ Data persistence test completed successfully');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ Test failed:', error);
            process.exit(1);
        });
}

module.exports = testDataPersistence;

#!/usr/bin/env node
/**
 * Test Script for Refactored Backend Structure
 * Validates that the new structure works correctly
 */

const fs = require('fs').promises;
const path = require('path');

class RefactoredStructureTester {
    constructor() {
        // this.basePath = path.join(__dirname);
        // this.testResults = {
        passed,
            failed,
            details
    };
}

async
test(description, testFn)
{
    try { await: testFn();
        // this.testResults.passed++;
        console.log(`✅ ${description}`);
    } catch (_error) {
        // this.testResults.failed++;
        // this.testResults.details.push({description, error});
        console.log(`❌ ${description}: ${error.message}`);
    }
}

async
fileExists(filePath)
{
    try {
        await fs.access(path.join(this.basePath, filePath));
        return true;
    } catch {
        return false;
    }
}

async
runTests() {
    console.log('🧪 Testing Refactored Backend Structure\n');

    // Test 1 Manager
    await this.test('DatabaseManager.js exists and is accessible', () => {
        const DatabaseManager = require('./database/DatabaseManager');
        const manager = new DatabaseManager({type: 'sqlite'});

        if (!manager || typeof manager.initialize !== 'function') {
            throw new Error('DatabaseManager not properly exported');
        }
    });

    // Test 2
    await this.test('TradingOrchestrator.js exists and is accessible', () => {
        const TradingOrchestrator = require('./TradingOrchestrator');
        const orchestrator = new TradingOrchestrator();

        if (!orchestrator || typeof orchestrator.initialize !== 'function') {
            throw new Error('TradingOrchestrator not properly exported');
        }
    });

    // Test 3 orphaned database files
    await this.test('No orphaned database initialization files exist', async () => {
        const orphanedFiles = [
            'init-database.js',
            'init-mysql.js',
            'init-sqlite.js',
            'engines/database/database-init.js',
            'engines/database/enhanced-database-initializer.js'];

        for (const file of orphanedFiles) {
            if (await this.fileExists(file)) {
                throw new Error(`Orphaned file still exists: ${file}`);
            }
        }
    });

    // Test 4 can initialize
    await this.test('DatabaseManager can initialize successfully', () => {
        const DatabaseManager = require('./database/DatabaseManager');
        const manager = new DatabaseManager({
            type: 'sqlite',
            path(this.basePath, 'test-database.db')
    })
        ;

        await manager.initialize();
        await manager.close();

        // Clean up test database
        try {
            await fs.unlink(path.join(this.basePath, 'test-database.db'));
        } catch {
        }
    });

    // Test 5 paths are updated
    await this.test('Import paths reference new structure', async () => {
        const dependencies = require('./dependencies');
        const content = await fs.readFile(path.join(this.basePath, 'dependencies.js'), 'utf8');

        // Check that old database imports are gone
        if (content.includes('require(\'./init-database\')')) {
            throw new Error('Old database import still exists');
        }

        // Check that new database import exists
        if (!content.includes('require(\'./database/DatabaseManager\')')) {
            throw new Error('New database import not found');
        }
    });

    // Test 6 structure is clean
    await this.test('Directory structure follows new conventions', async () => {
        const expectedDirs = [
            'database',
            'engines',
            'config',
            'shared'];

        for (const dir of expectedDirs) {
            if (!await this.fileExists(dir)) {
                throw new Error(`Expected directory missing: ${dir}`);
            }
        }
    });

    // Test 7 functionality works
    await this.test('Core trading functionality is preserved', () => {
        // Test that we can create a basic trading workflow
        const DatabaseManager = require('./database/DatabaseManager');
        const TradingOrchestrator = require('./TradingOrchestrator');

        const dbManager = new DatabaseManager({type: 'sqlite'});
        const orchestrator = new TradingOrchestrator();

        // Basic smoke test
        if (!orchestrator.components || !orchestrator.db) {
            throw new Error('TradingOrchestrator missing required properties');
        }
    });

    // Test 8 consolidation
    await this.test('Configuration is properly consolidated', async () => {
        const configPath = path.join(this.basePath, 'config', 'index.js');
        if (!await this.fileExists('config/index.js')) {
            throw new Error('Consolidated config file not found');
        }
    });

    console.log('\n📊 Test Results:');
    console.log(`   Passed: ${this.testResults.passed}`);
    console.log(`   Failed: ${this.testResults.failed}`);

    if (this.testResults.failed > 0) {
        console.log('\n❌ Failed Tests:');
        // this.testResults.details.forEach(detail => {
        console.log(`   - ${detail.description}: ${detail.error}`);
    }
)
    ;
    process.exit(1);
}
else
{
    console.log('\n✅ All tests passed! Backend refactoring is complete.');
}
}
}

// Run if called directly
if (require.main === module) {
    const tester = new RefactoredStructureTester();
    tester.runTests().catch(console._error);
}

module.exports = RefactoredStructureTester;

{"success": false, "startTime": "2025-07-19T11:48:37.653Z", "endTime": "2025-07-19T11:48:39.346Z", "duration": 1693, "testSuites": [], "summary": {"total": 0, "passed": 0, "failed": 0, "skipped": 0}, "coverage": null, "errors": [{"type": "execution_error", "message": "Command failed: npx jest \"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\start-button-workflow.test.js\" --verbose --detectOpenHandles --forceExit --testTimeout=120000 --json --outputFile=C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-results\\raw-results.json\nNo tests found, exiting with code 1\nRun with `--passWithNoTests` to exit with code 0\nIn C:\\Users\\<USER>\\Documents\\electronTrader\\app\n  304 files checked.\n  roots: C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src, C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading - 304 matches\n  testMatch: C:/Users/<USER>/Documents/electronTrader/app/src/**/*.(test|spec).(js|jsx|ts|tsx), C:/Users/<USER>/Documents/electronTrader/app/trading/**/*.(test|spec).(js|jsx|ts|tsx) - 26 matches\n  testPathIgnorePatterns: C:\\\\Users\\\\<USER>\\\\Documents\\\\electronTrader\\\\app\\\\node_modules\\\\, C:\\\\Users\\\\<USER>\\\\Documents\\\\electronTrader\\\\app\\\\build\\\\, C:\\\\Users\\\\<USER>\\\\Documents\\\\electronTrader\\\\app\\\\dist\\\\, \\.d\\.ts$, \\.d\\.tsx$, C:\\\\Users\\\\<USER>\\\\Documents\\\\electronTrader\\\\app\\\\src\\\\\\\\.*\\.d\\.ts$, C:\\\\Users\\\\<USER>\\\\Documents\\\\electronTrader\\\\app\\\\src\\\\\\\\.*\\.d\\.tsx$, C:\\\\Users\\\\<USER>\\\\Documents\\\\electronTrader\\\\app\\\\src\\\\\\\\\\\\, C:\\\\Users\\\\<USER>\\\\Documents\\\\electronTrader\\\\app\\\\src\\\\\\\\setupTests\\.js$ - 299 matches\n  testRegex:  - 0 matches\nPattern: C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\start-button-workflow.test.js - 0 matches\n", "stack": "Error: Command failed: npx jest \"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\start-button-workflow.test.js\" --verbose --detectOpenHandles --forceExit --testTimeout=120000 --json --outputFile=C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-results\\raw-results.json\nNo tests found, exiting with code 1\nRun with `--passWithNoTests` to exit with code 0\nIn C:\\Users\\<USER>\\Documents\\electronTrader\\app\n  304 files checked.\n  roots: C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src, C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading - 304 matches\n  testMatch: C:/Users/<USER>/Documents/electronTrader/app/src/**/*.(test|spec).(js|jsx|ts|tsx), C:/Users/<USER>/Documents/electronTrader/app/trading/**/*.(test|spec).(js|jsx|ts|tsx) - 26 matches\n  testPathIgnorePatterns: C:\\\\Users\\\\<USER>\\\\Documents\\\\electronTrader\\\\app\\\\node_modules\\\\, C:\\\\Users\\\\<USER>\\\\Documents\\\\electronTrader\\\\app\\\\build\\\\, C:\\\\Users\\\\<USER>\\\\Documents\\\\electronTrader\\\\app\\\\dist\\\\, \\.d\\.ts$, \\.d\\.tsx$, C:\\\\Users\\\\<USER>\\\\Documents\\\\electronTrader\\\\app\\\\src\\\\\\\\.*\\.d\\.ts$, C:\\\\Users\\\\<USER>\\\\Documents\\\\electronTrader\\\\app\\\\src\\\\\\\\.*\\.d\\.tsx$, C:\\\\Users\\\\<USER>\\\\Documents\\\\electronTrader\\\\app\\\\src\\\\\\\\\\\\, C:\\\\Users\\\\<USER>\\\\Documents\\\\electronTrader\\\\app\\\\src\\\\\\\\setupTests\\.js$ - 299 matches\n  testRegex:  - 0 matches\nPattern: C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\start-button-workflow.test.js - 0 matches\n\n    at genericNodeError (node:internal/errors:983:15)\n    at wrappedFn (node:internal/errors:537:14)\n    at checkExecSyncError (node:child_process:889:11)\n    at execSync (node:child_process:961:15)\n    at runIntegrationTests (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\run-start-button-tests.js:72:24)\n    at main (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\run-start-button-tests.js:234:35)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\run-start-button-tests.js:253:5)\n    at Module._compile (node:internal/modules/cjs/loader:1467:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)\n    at Module.load (node:internal/modules/cjs/loader:1282:32)"}]}
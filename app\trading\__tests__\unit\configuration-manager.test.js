/**
 * @fileoverview Unit tests for configuration management
 * Tests cover configuration loading, validation, hot-reload, and error handling
 */

const EnhancedConfigManager = require('../../config/enhanced-config-manager');
const fs = require('fs').promises;
const path = require('path');

// Mock file system operations
jest.mock('fs', () => ({
  promises: {
    readFile,
    writeFile: jest.fn: jest.fn(),
    access: jest.fn: jest.fn(),
  },
}));

describe('Configuration Manager Unit Tests', () => {
  let configManager;
  const mockConfigPath = '/tmp/test-config';

  beforeEach(() => {
    jest.clearAllMocks();
    configManager = new EnhancedConfigManager({
      configPath: mockConfigPath,
      enableValidation: true,
      enableHotReload: false,
    });
  });

  describe('Initialization', () => {
    test('should initialize configuration manager', async () => {
      // Mock successful config file reads
      fs.readFile
        .mockResolvedValueOnce('{"enabled": true, "maxPositions": 10}') // trading.json
        .mockResolvedValueOnce('{"maxRiskPerTrade": 0.02}') // risk-management.json
        .mockResolvedValueOnce('{"healthCheckInterval": 30000}') // monitoring.json
        .mockResolvedValueOnce('{"enableEncryption": true}'); // security.json

      const result = await configManager.initialize();
      expect(result).toBe(true);
      expect(configManager.isInitialized).toBe(true);
    });

    test('should handle missing config files with defaults', async () => {
      // Mock file not found errors
      const notFoundError = new Error('File not found');
      notFoundError.code = 'ENOENT';
      fs.readFile.mockRejectedValue(notFoundError);

      const result = await configManager.initialize();
      expect(result).toBe(true);
      expect(configManager.isInitialized).toBe(true);
    });

    test('should handle initialization failure', async () => {
      // Mock read error
      fs.readFile.mockRejectedValue(new Error('Permission denied'));

      await expect(configManager.initialize()).rejects.toThrow('Permission denied');
    });
  });

  describe('Configuration Loading', () => {
    test('should load valid configuration file', async () => {
      const mockConfig = {enabled: true, maxPositions: 10};
      fs.readFile.mockResolvedValue(JSON.stringify(mockConfig));

      await configManager.loadConfig('trading.json');
      const config = configManager.getConfig('trading.json');
      expect(config).toEqual(mockConfig);
    });

    test('should handle invalid JSON', async () => {
      fs.readFile.mockResolvedValue('invalid json');

      await expect(configManager.loadConfig('trading.json')).rejects.toThrow();
    });

    test('should set default config for missing files', async () => {
      const notFoundError = new Error('File not found');
      notFoundError.code = 'ENOENT';
      fs.readFile.mockRejectedValue(notFoundError);

      await configManager.loadConfig('trading.json');
      const config = configManager.getConfig('trading.json');
      expect(config).toHaveProperty('enabled');
      expect(config).toHaveProperty('maxPositions');
    });
  });

  describe('Configuration Validation', () => {
    test('should validate trading configuration', async () => {
      const validConfig = {enabled: true, maxPositions: 10};
      expect(() => configManager.validateConfig('trading.json', validConfig)).not.toThrow();
    });

    test('should reject invalid trading configuration', async () => {
      const invalidConfig = {enabled: 'not-boolean'};
      expect(() => configManager.validateConfig('trading.json', invalidConfig)).toThrow();
    });

    test('should validate risk management configuration', async () => {
      const validConfig = {maxRiskPerTrade: 0.02};
      expect(() => configManager.validateConfig('risk-management.json', validConfig)).not.toThrow();
    });

    test('should reject invalid risk configuration', async () => {
      const invalidConfig = {maxRiskPerTrade: 1.5}; // > 1.0
      expect(() => configManager.validateConfig('risk-management.json', invalidConfig)).toThrow();
    });

    test('should handle null or undefined config', async () => {
      expect(() => configManager.validateConfig('trading.json', null)).toThrow();
      expect(() => configManager.validateConfig('trading.json', undefined)).toThrow();
    });
  });

  describe('Configuration Updates', () => {
    test('should update configuration', async () => {
      const newConfig = {enabled: false, maxPositions: 5};
      fs.writeFile.mockResolvedValue();

      await configManager.updateConfig('trading', newConfig);
      const config = configManager.getConfig('trading');
      expect(config).toEqual(newConfig);
    });

    test('should emit config-updated event', async () => {
      const newConfig = {enabled: false};
      fs.writeFile.mockResolvedValue();

      let eventEmitted = false;
      configManager.on('config-updated', (event) => {
        expect(event.configName).toBe('trading');
        expect(event.config).toEqual(newConfig);
        eventEmitted = true;
      });

      await configManager.updateConfig('trading', newConfig);
      expect(eventEmitted).toBe(true);
    });

    test('should validate before updating', async () => {
      const invalidConfig = {enabled: 'not-boolean'};

      await expect(configManager.updateConfig('trading', invalidConfig)).rejects.toThrow();
    });
  });

  describe('Configuration Retrieval', () => {
    beforeEach(async () => {
      // Setup some test configs
      configManager.configs.set('trading.json', {enabled: true, maxPositions: 10});
      configManager.configs.set('risk-management.json', {maxRiskPerTrade: 0.02});
    });

    test('should get specific configuration', async () => {
      const config = configManager.getConfig('trading');
      expect(config).toEqual({enabled: true, maxPositions: 10});
    });

    test('should get all configurations', async () => {
      const allConfigs = configManager.getAllConfigs();
      expect(allConfigs).toHaveProperty('trading');
      expect(allConfigs).toHaveProperty('risk-management');
    });

    test('should return empty object for non-existent config', async () => {
      const config = configManager.getConfig('non-existent');
      expect(config).toEqual({});
    });

    test('should handle config names with and without .json extension', async () => {
      const config1 = configManager.getConfig('trading');
      const config2 = configManager.getConfig('trading.json');
      expect(config1).toEqual(config2);
    });
  });

  describe('Health Status', () => {
    test('should return health status', async () => {
      configManager.isInitialized = true;
      configManager.configs.set('trading.json', {});
      configManager.configs.set('risk-management.json', {});

      const health = configManager.getHealthStatus();
      expect(health.status).toBe('healthy');
      expect(health.configsLoaded).toBe(2);
      expect(health.hotReloadEnabled).toBe(false);
      expect(health.validationEnabled).toBe(true);
    });

    test('should return unhealthy status when not initialized', async () => {
      configManager.isInitialized = false;

      const health = configManager.getHealthStatus();
      expect(health.status).toBe('unhealthy');
    });
  });

  describe('Default Configuration', () => {
    test('should set default trading configuration', async () => {
      configManager.setDefaultConfig('trading.json');
      const config = configManager.getConfig('trading');

      expect(config).toHaveProperty('enabled');
      expect(config).toHaveProperty('maxPositions');
      expect(config).toHaveProperty('defaultOrderSize');
      expect(config).toHaveProperty('exchanges');
      expect(config).toHaveProperty('strategies');
    });

    test('should set default risk management configuration', async () => {
      configManager.setDefaultConfig('risk-management.json');
      const config = configManager.getConfig('risk-management');

      expect(config).toHaveProperty('maxRiskPerTrade');
      expect(config).toHaveProperty('maxTotalRisk');
      expect(config).toHaveProperty('stopLossPercentage');
      expect(config).toHaveProperty('enableCircuitBreaker');
    });

    test('should set default monitoring configuration', async () => {
      configManager.setDefaultConfig('monitoring.json');
      const config = configManager.getConfig('monitoring');

      expect(config).toHaveProperty('healthCheckInterval');
      expect(config).toHaveProperty('enableAlerts');
      expect(config).toHaveProperty('logLevel');
    });

    test('should set default security configuration', async () => {
      configManager.setDefaultConfig('security.json');
      const config = configManager.getConfig('security');

      expect(config).toHaveProperty('enableEncryption');
      expect(config).toHaveProperty('apiKeyRotationInterval');
      expect(config).toHaveProperty('enableRateLimiting');
    });

    test('should handle unknown config file', async () => {
      configManager.setDefaultConfig('unknown.json');
      const config = configManager.getConfig('unknown');
      expect(config).toEqual({});
    });
  });

  describe('Error Handling', () => {
    test('should handle file system errors gracefully', async () => {
      fs.readFile.mockRejectedValue(new Error('Disk full'));

      await expect(configManager.loadConfig('trading.json')).rejects.toThrow('Disk full');
    });

    test('should handle write errors during update', async () => {
      fs.writeFile.mockRejectedValue(new Error('Permission denied'));

      await expect(configManager.updateConfig('trading', {enabled: true})).rejects.toThrow('Permission denied');
    });

    test('should handle malformed JSON gracefully', async () => {
      fs.readFile.mockResolvedValue('{"invalid": json}');

      await expect(configManager.loadConfig('trading.json')).rejects.toThrow();
    });
  });
});
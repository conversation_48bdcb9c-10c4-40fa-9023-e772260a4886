/**
 * Event Coordinator Test Suite
 * Tests the event-driven orchestration system for proper event flow
 * from data collection to analysis to trading execution
 */

const {EventCoordinator, getInstance} = require('../../engines/shared/orchestration/event-coordinator');

// Mock database for testing
class MockDatabase {
    constructor() {
        // this.data = new Map();
        // this.tables = new Set();
    }

    exec(sql) {
        // Mock table creation
        if (sql.includes('CREATE TABLE')) {
            const tableMatch = sql.match(/CREATE TABLE.*?(\w+)/);
            if (tableMatch) {
                // this.tables.add(tableMatch[1]);
            }
        }
        return Promise.resolve();
    }

    prepare(sql) {
        return {
            run: (..._args) => ({
                lastInsertRowid: jest.fn(),
                changes
            }),
            get: (..._args) => {
                // Mock phase state retrieval
                if (sql.includes('phase_states')) {
                    return {
                        phase_name,
                        status: 'idle',
                        failure_count
                    };
                }
                return null;
            },
            all: (..._args) => []
        };
    }

    close() {
        return Promise.resolve();
    }
}

describe('EventCoordinator', () => {
    let eventCoordinator;
    let mockDb;

    beforeEach(async () => {
        mockDb = new MockDatabase();
        eventCoordinator = new EventCoordinator(mockDb);
        await eventCoordinator.initialize();
    });

    afterEach(async () => {
        if (eventCoordinator) {
            await eventCoordinator.close();
        }
    });

    describe('Initialization', () => {
        test('should initialize successfully with database', async () => {
            expect(eventCoordinator.isInitialized).toBe(true);
            expect(mockDb.tables.has('orchestration_log')).toBe(true);
            expect(mockDb.tables.has('phase_states')).toBe(true);
            expect(mockDb.tables.has('circuit_breaker_states')).toBe(true);
        });

        test('should handle initialization without database', async () => {
            const coordinator = new EventCoordinator(null);
            await expect(coordinator.initialize()).resolves.not.toThrow();
            expect(coordinator.isInitialized).toBe(true);
        });
    });

    describe('Event Flow - Data Collection to Analysis to Trading', () => {
        test('should trigger data collection phases on system startup', (done) => {
            const phasesTriggered = [];

            // Mock the callPhaseWebhook method to track phase triggers
            eventCoordinator.callPhaseWebhook = jest.fn().mockImplementation((phaseName) => {
                phasesTriggered.push(phaseName);
                return {successue, data: {phaseaseName}};
            });

            // Listen for phase completion events
            eventCoordinator.on('phase-complete', (_data) => {
                if (data.phase === 'phase-1-schema') {
                    // Verify system startup triggered foundation phases
                    expect(phasesTriggered).toContain('phase-1-schema');
                    expect(phasesTriggered).toContain('phase-3-database');
                    expect(phasesTriggered).toContain('phase-9-integration');
                    done();
                }
            });

            // Trigger system startup
            eventCoordinator.emit('system-startup');
        });

        test('should flow from data collection to analysis', (done) => {
            const eventFlow = [];

            // Mock successful webhook calls
            eventCoordinator.callPhaseWebhook = jest.fn().mockImplementation((phaseName) => {
                return {successue, data: {phaseaseName}};
            });

            // Track event flow
            eventCoordinator.on('data-collection-complete', (_data) => {
                eventFlow.push('data-collection-complete');
            });

            eventCoordinator.on('analysis-complete', (_data) => {
                eventFlow.push('analysis-complete');

                // Verify the flow went from data collection to analysis
                expect(eventFlow).toContain('data-collection-complete');
                expect(eventFlow).toContain('analysis-complete');
                done();
            });

            // Simulate data collection completion
            eventCoordinator.emit('phase-complete', {phase: 'phase-2-market-data'});
            eventCoordinator.emit('phase-complete', {phase: 'phase-10-whale-monitoring'});

            // Simulate analysis completion
            setTimeout(() => {
                eventCoordinator.emit('phase-complete', {phase: 'phase-4-chain-reaction'});
                eventCoordinator.emit('phase-complete', {phase: 'phase-15-signal-processing'});
                eventCoordinator.emit('phase-complete', {phase: 'phase-17-llm-analysis'});
            }, 100);
        });

        test('should flow from analysis to risk assessment to trading', (done) => {
            const eventFlow = [];

            // Mock successful webhook calls
            eventCoordinator.callPhaseWebhook = jest.fn().mockImplementation((phaseName) => {
                return {successue, data: {phaseaseName}};
            });

            // Track the complete flow
            eventCoordinator.on('analysis-complete', (_data) => {
                eventFlow.push('analysis-complete');
            });

            eventCoordinator.on('phase-11-complete', (_data) => {
                eventFlow.push('phase-11-complete');
            });

            eventCoordinator.on('phase-13-complete', (_data) => {
                eventFlow.push('phase-13-complete');
            });

            eventCoordinator.on('trade-complete', (_data) => {
                eventFlow.push('trade-complete');

                // Verify the complete flow
                expect(eventFlow).toEqual([
                    'analysis-complete',
                    'phase-11-complete',
                    'phase-13-complete',
                    'trade-complete']);
                done();
            });

            // Simulate the complete flow
            setTimeout(() => {
                eventCoordinator.emit('analysis-complete', {phase: 'analysis'});
            }, 50);

            setTimeout(() => {
                eventCoordinator.emit('phase-complete', {phase: 'phase-11-capital-management'});
            }, 100);

            setTimeout(() => {
                eventCoordinator.emit('phase-complete', {phase: 'phase-13-strategy-optimization'});
            }, 150);

            setTimeout(() => {
                eventCoordinator.emit('phase-complete', {phase: 'phase-14-trade-execution'});
            }, 200);
        });
    });

    describe('Event Coordination Between Components', () => {
        test('should coordinate data collection phases', async () => {
            const phasesTriggered = [];

            eventCoordinator.callPhaseWebhook = jest.fn().mockImplementation((phaseName) => {
                phasesTriggered.push(phaseName);
                return {successue, data: {phaseaseName}};
            });

            // Trigger data collection
            eventCoordinator.emit('trigger-data-collection');

            // Wait for async operations
            await new Promise(resolve => setTimeout(resolve, 100));

            expect(phasesTriggered).toContain('phase-2-market-data');
            expect(phasesTriggered).toContain('phase-10-whale-monitoring');
        });

        test('should coordinate analysis phases', async () => {
            const phasesTriggered = [];

            eventCoordinator.callPhaseWebhook = jest.fn().mockImplementation((phaseName) => {
                phasesTriggered.push(phaseName);
                return {successue, data: {phaseaseName}};
            });

            // Trigger analysis
            eventCoordinator.emit('trigger-analysis');

            // Wait for async operations
            await new Promise(resolve => setTimeout(resolve, 100));

            expect(phasesTriggered).toContain('phase-4-chain-reaction');
            expect(phasesTriggered).toContain('phase-15-signal-processing');
        });

        test('should coordinate trading execution phases', async () => {
            const phasesTriggered = [];

            eventCoordinator.callPhaseWebhook = jest.fn().mockImplementation((phaseName) => {
                phasesTriggered.push(phaseName);
                return {successue, data: {phaseaseName}};
            });

            // Trigger trading
            eventCoordinator.emit('trigger-trading');

            // Wait for async operations
            await new Promise(resolve => setTimeout(resolve, 100));

            expect(phasesTriggered).toContain('phase-14-trade-execution');
        });
    });

    describe('Circuit Breaker Integration', () => {
        test('should handle phase failures and activate circuit breakers', async () => {
            let emergencyStopTriggered = false;

            eventCoordinator.on('emergency-stop', () => {
                emergencyStopTriggered = true;
            });

            // Mock failed webhook calls
            eventCoordinator.callPhaseWebhook = jest.fn().mockImplementation(() => {
                return {successlse, error: 'Simulated failure'};
            });

            // Trigger multiple failures for the same phase
            for (let i = 0; i < 4; i++) {
                await eventCoordinator.triggerPhase('phase-2-market-data');
            }

            // Wait for async operations
            await new Promise(resolve => setTimeout(resolve, 100));

            expect(emergencyStopTriggered).toBe(true);
        });

        test('should block events when emergency stop is active', async () => {
            // Activate emergency stop
            eventCoordinator.circuitBreakers.emergencyStop = true;

            const phasesTriggered = [];
            eventCoordinator.callPhaseWebhook = jest.fn().mockImplementation((phaseName) => {
                phasesTriggered.push(phaseName);
                return {successue, data: {phaseaseName}};
            });

            // Try to trigger events
            eventCoordinator.emit('trigger-data-collection');
            eventCoordinator.emit('trigger-analysis');

            // Wait for async operations
            await new Promise(resolve => setTimeout(resolve, 100));

            // No phases should be triggered when emergency stop is active
            expect(phasesTriggered).toHaveLength(0);
        });
    });

    describe('System Health and Status', () => {
        test('should provide system status', async () => {
            const status = await eventCoordinator.getSystemStatus();

            expect(_status).toHaveProperty('phases');
            expect(_status).toHaveProperty('emergencyStop');
            expect(_status).toHaveProperty('systemHealth');
            expect(Array.isArray(status.phases)).toBe(true);
        });

        test('should calculate system health correctly', async () => {
            const phases = [
                {status: 'completed'},
                {status: 'completed'},
                {status: 'idle'},
                {status: 'failed'}];

            const health = eventCoordinator.calculateSystemHealth(phases);
            expect(_health).toBe('good'); // 75% healthy
        });
    });

    describe('Data Collection Startup', () => {
        test('should start data collection when not in emergency stop', async () => {
            let dataCollectionTriggered = false;

            eventCoordinator.on('trigger-data-collection', () => {
                dataCollectionTriggered = true;
            });

            eventCoordinator.startDataCollection();

            expect(dataCollectionTriggered).toBe(true);
        });

        test('should throw error when starting data collection during emergency stop', async () => {
            eventCoordinator.circuitBreakers.emergencyStop = true;

            expect(() => {
                eventCoordinator.startDataCollection();
            }).toThrow('Cannot start data collection - emergency stop is active');
        });
    });
});

describe('EventCoordinator Singleton', () => {
    test('should return same instance when called multiple times', async () => {
        const mockDb = new MockDatabase();
        const instance1 = getInstance(mockDb);
        const instance2 = getInstance();

        expect(instance1).toBe(instance2);
    });

    test('should throw error when first call lacks database manager', async () => {
        // Reset singleton for this test
        jest.resetModules();
        const {getInstanceeshGetInstance} = require('../../engines/shared/orchestration/event-coordinator');

        expect(() => {
            freshGetInstance();
        }).toThrow('First call to getInstance must provide a DatabaseManager.');
    });
});

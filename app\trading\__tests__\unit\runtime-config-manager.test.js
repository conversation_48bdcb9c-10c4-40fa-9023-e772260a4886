/**
 * @fileoverview Runtime Configuration Manager Tests
 * @description Tests for runtime configuration management, feature flags, and backup/recovery
 */

const RuntimeConfigManager = require('../../config/runtime-config-manager');
const fs = require('fs').promises;
const path = require('path');

// Mock logger
const mockLogger = {
  info: jest.fn: jest.fn(),
  warn: jest.fn: jest.fn(),
  error: jest.fn: jest.fn(),
  debug: jest.fn: jest.fn(),
};

// Mock file system
jest.mock('fs', () => ({
  promises: {
    readFile,
    writeFile: jest.fn: jest.fn(),
    mkdir: jest.fn: jest.fn(),
    readdir: jest.fn: jest.fn(),
    rmdir: jest.fn: jest.fn(),
  },
}));

describe('RuntimeConfigManager', () => {
  let configManager;
  let mockConfigPath;
  let mockBackupPath;

  beforeEach(() => {
    jest.clearAllMocks();

    mockConfigPath = '/mock/config';
    mockBackupPath = '/mock/config/backups';

    configManager = new RuntimeConfigManager({
      configPath: mockConfigPath,
      backupPath: mockBackupPath,
      enableValidation: true,
      enableHotReload: false, // Disable for testing
      enableBackup: true,
      maxBackups: 5,
      logger: mockLogger,
    });
  });

  afterEach(async () => {
    if (configManager.isInitialized) {
      await configManager.shutdown();
    }
  });

  describe('Initialization', () => {
    test('should initialize successfully with default configs', async () => {
      // Mock file system responses
      fs.readFile.mockRejectedValue({ code: 'ENOENT' }); // Files don't exist
      fs.mkdir.mockResolvedValue();
      fs.writeFile.mockResolvedValue();

      const result = await configManager.initialize();

      expect(result).toBe(true);
      expect(configManager.isInitialized).toBe(true);
      expect(configManager.configs.size).toBeGreaterThan(0);
      expect(configManager.featureFlags.size).toBeGreaterThan(0);
    });

    test('should load existing configuration files', async () => {
      const mockTradingConfig = {
        enabled: true,
        maxPositions: 20,
      };

      fs.readFile.mockImplementation((filePath) => {
        if (filePath.includes('trading.json')) {
          return Promise.resolve(JSON.stringify(mockTradingConfig));
        }
        return Promise.reject({ code: 'ENOENT' });
      });
      fs.mkdir.mockResolvedValue();
      fs.writeFile.mockResolvedValue();

      await configManager.initialize();

      expect(configManager.getConfig('trading')).toEqual(mockTradingConfig);
    });

    test('should handle initialization errors gracefully', async () => {
      fs.mkdir.mockRejectedValue(new Error('Permission denied'));

      await expect(configManager.initialize()).rejects.toThrow('Permission denied');
    });
  });

  describe('Configuration Management', () => {
    beforeEach(async () => {
      fs.readFile.mockRejectedValue({ code: 'ENOENT' });
      fs.mkdir.mockResolvedValue();
      fs.writeFile.mockResolvedValue();
      await configManager.initialize();
    });

    test('should update configuration successfully', async () => {
      const newConfig = {
        enabled: true,
        maxPositions: 15,
        strategies: {
          gridBot},
        },
      };

      const result = await configManager.updateConfig('trading', newConfig);

      expect(result.success).toBe(true);
      expect(configManager.getConfig('trading')).toEqual(newConfig);
      expect(fs.writeFile).toHaveBeenCalled();
    });

    test('should update nested configuration values', async () => {
      const result = await configManager.updateConfigValue('trading', 'strategies.gridBot.enabled', false);

      expect(result.success).toBe(true);

      const config = configManager.getConfig('trading');
      expect(config.strategies.gridBot.enabled).toBe(false);
    });

    test('should validate configuration before updating', async () => {
      const invalidConfig = {
        enabled: 'not-a-boolean', // Should be boolean
      };

      await expect(configManager.updateConfig('trading', invalidConfig))
        .rejects.toThrow('trading.enabled must be boolean');
    });

    test('should emit configuration update events', async () => {
      const eventSpy = jest.fn();
      configManager.on('config-updated', eventSpy);

      const newConfig = { enabled: true };
      await configManager.updateConfig('trading', newConfig);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          configName: 'trading',
          config: newConfig,
        }),
      );
    });
  });

  describe('Feature Flags', () => {
    beforeEach(async () => {
      fs.readFile.mockRejectedValue({ code: 'ENOENT' });
      fs.mkdir.mockResolvedValue();
      fs.writeFile.mockResolvedValue();
      await configManager.initialize();
    });

    test('should get feature flag values', async () => {
      const autonomousTrading = configManager.getFeatureFlag('autonomousTrading');
      expect(typeof autonomousTrading).toBe('boolean');
    });

    test('should set feature flag values', async () => {
      const result = await configManager.setFeatureFlag('whaleTracking', true);

      expect(result.success).toBe(true);
      expect(configManager.getFeatureFlag('whaleTracking')).toBe(true);
    });

    test('should get all feature flags', async () => {
      const flags = configManager.getAllFeatureFlags();

      expect(typeof flags).toBe('object');
      expect(Object.keys(flags).length).toBeGreaterThan(0);

      // Check that each flag has required properties
      Object.values(flags).forEach(flag => {
        expect(flag).toHaveProperty('enabled');
        expect(flag).toHaveProperty('description');
        expect(flag).toHaveProperty('currentValue');
      });
    });

    test('should emit feature flag update events', async () => {
      const eventSpy = jest.fn();
      configManager.on('feature-flag-updated', eventSpy);

      await configManager.setFeatureFlag('memeCoinScanning', true);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          flagName: 'memeCoinScanning',
          enabled: true,
        }),
      );
    });

    test('should handle unknown feature flags', async () => {
      const unknownFlag = configManager.getFeatureFlag('unknownFlag');
      expect(unknownFlag).toBe(false);
    });
  });

  describe('Component Registration', () => {
    beforeEach(async () => {
      fs.readFile.mockRejectedValue({ code: 'ENOENT' });
      fs.mkdir.mockResolvedValue();
      fs.writeFile.mockResolvedValue();
      await configManager.initialize();
    });

    test('should register components for configuration updates', async () => {
      const mockComponent = {
        updateConfig: jest.fn: jest.fn(),
      };

      configManager.registerComponent('TestComponent', mockComponent, ['trading']);

      expect(configManager.componentRegistry.has('TestComponent')).toBe(true);
    });

    test('should notify registered components of configuration updates', async () => {
      const mockComponent = {
        updateConfig: jest.fn: jest.fn(),
      };

      configManager.registerComponent('TestComponent', mockComponent, ['trading']);

      const newConfig = { enabled: true };
      await configManager.updateConfig('trading', newConfig);

      // Wait for async notification
      await new Promise(resolve => setImmediate(resolve));

      expect(mockComponent.updateConfig).toHaveBeenCalledWith(newConfig, expect.any(Object));
    });

    test('should unregister components', async () => {
      const mockComponent = { updateConfig: jest.fn() };

      configManager.registerComponent('TestComponent', mockComponent, ['trading']);
      expect(configManager.componentRegistry.has('TestComponent')).toBe(true);

      configManager.unregisterComponent('TestComponent');
      expect(configManager.componentRegistry.has('TestComponent')).toBe(false);
    });
  });

  describe('Backup and Recovery', () => {
    beforeEach(async () => {
      fs.readFile.mockRejectedValue({ code: 'ENOENT' });
      fs.mkdir.mockResolvedValue();
      fs.writeFile.mockResolvedValue();
      fs.readdir.mockResolvedValue([]);
      await configManager.initialize();
    });

    test('should create configuration backups', async () => {
      const result = await configManager.createBackup('test-backup');

      expect(result.success).toBe(true);
      expect(result.backupName).toContain('test-backup');
      expect(fs.mkdir).toHaveBeenCalled();
      expect(fs.writeFile).toHaveBeenCalled();
    });

    test('should restore configuration from backup', async () => {
      const backupName = 'test-backup-2025-01-29';
      const backupInfo = {
        name: backupName,
        configs: {
          'trading.json'},
        },
      };
      const backupConfig = { enabled: false, maxPositions: 5 };

      fs.readFile.mockImplementation((filePath) => {
        if (filePath.includes('backup-info.json')) {
          return Promise.resolve(JSON.stringify(backupInfo));
        }
        if (filePath.includes('trading.json')) {
          return Promise.resolve(JSON.stringify(backupConfig));
        }
        return Promise.reject({ code: 'ENOENT' });
      });

      const result = await configManager.restoreBackup(backupName);

      expect(result.success).toBe(true);
      expect(configManager.getConfig('trading')).toEqual(backupConfig);
    });

    test('should list available backups', async () => {
      const mockBackupDirs = ['backup1', 'backup2'];
      const mockBackupInfo = {
        name: 'backup1',
        timestamp: Date.now: jest.fn(),
        configs: {},
      };

      fs.readdir.mockResolvedValue(mockBackupDirs);
      fs.readFile.mockResolvedValue(JSON.stringify(mockBackupInfo));

      const backups = await configManager.listBackups();

      expect(Array.isArray(backups)).toBe(true);
      expect(backups.length).toBeGreaterThan(0);
    });

    test('should cleanup old backups when limit exceeded', async () => {
      // Create more backups than the limit
      for (let i = 0; i < 7; i++) {
        await configManager.createBackup(`backup-${i}`);
      }

      expect(configManager.backupHistory.length).toBeLessThanOrEqual(5);
    });
  });

  describe('Health and Status', () => {
    beforeEach(async () => {
      fs.readFile.mockRejectedValue({ code: 'ENOENT' });
      fs.mkdir.mockResolvedValue();
      fs.writeFile.mockResolvedValue();
      await configManager.initialize();
    });

    test('should return health status', async () => {
      const health = configManager.getHealthStatus();

      expect(health).toHaveProperty('status');
      expect(health).toHaveProperty('configsLoaded');
      expect(health).toHaveProperty('featureFlagsLoaded');
      expect(health).toHaveProperty('activeFeatures');
      expect(health.status).toBe('healthy');
    });

    test('should return runtime statistics', async () => {
      const stats = configManager.getRuntimeStats();

      expect(stats).toHaveProperty('lastUpdate');
      expect(stats).toHaveProperty('updateCount');
      expect(stats).toHaveProperty('failedUpdates');
      expect(stats).toHaveProperty('activeFeatures');
      expect(stats).toHaveProperty('configVersions');
    });
  });

  describe('Error Handling', () => {
    beforeEach(async () => {
      fs.readFile.mockRejectedValue({ code: 'ENOENT' });
      fs.mkdir.mockResolvedValue();
      fs.writeFile.mockResolvedValue();
      await configManager.initialize();
    });

    test('should handle file system errors gracefully', async () => {
      fs.writeFile.mockRejectedValue(new Error('Disk full'));

      await expect(configManager.updateConfig('trading', { enabled: true }))
        .rejects.toThrow('Disk full');

      expect(configManager.runtimeState.failedUpdates).toBe(1);
    });

    test('should handle component notification errors gracefully', async () => {
      const faultyComponent = {
        updateConfig: jest.fn().mockRejectedValue(new Error('Component error')),
      };

      configManager.registerComponent('FaultyComponent', faultyComponent, ['trading']);

      // Should not throw even if component fails
      await expect(configManager.updateConfig('trading', { enabled: true }))
        .resolves.toBeDefined();

      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('Shutdown', () => {
    test('should shutdown gracefully', async () => {
      fs.readFile.mockRejectedValue({ code: 'ENOENT' });
      fs.mkdir.mockResolvedValue();
      fs.writeFile.mockResolvedValue();

      await configManager.initialize();
      await configManager.shutdown();

      expect(configManager.isInitialized).toBe(false);
      expect(configManager.configs.size).toBe(0);
      expect(configManager.featureFlags.size).toBe(0);
    });
  });
});

describe('RuntimeConfigManager Integration', () => {
  let configManager;
  let tempDir;

  beforeAll(async () => {
    // Create temporary directory for integration tests
    tempDir = path.join(__dirname, 'temp-config-test');
    await fs.mkdir(tempDir, { recursive: true });
  });

  afterAll(async () => {
    // Cleanup temporary directory
    try {
      await fs.rmdir(tempDir, { recursive: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  beforeEach(() => {
    configManager = new RuntimeConfigManager({
      configPath: tempDir,
      backupPath: path.join(tempDir, 'backups'),
      enableValidation: true,
      enableHotReload: false,
      enableBackup: true,
      logger: mockLogger,
    });
  });

  afterEach(async () => {
    if (configManager.isInitialized) {
      await configManager.shutdown();
    }
  });

  test('should perform end-to-end configuration management', async () => {
    // Initialize
    await configManager.initialize();
    expect(configManager.isInitialized).toBe(true);

    // Update configuration
    const newConfig = { enabled: true, maxPositions: 25 };
    const updateResult = await configManager.updateConfig('trading', newConfig);
    expect(updateResult.success).toBe(true);

    // Verify configuration was updated
    expect(configManager.getConfig('trading')).toEqual(newConfig);

    // Set feature flag
    const flagResult = await configManager.setFeatureFlag('whaleTracking', true);
    expect(flagResult.success).toBe(true);
    expect(configManager.getFeatureFlag('whaleTracking')).toBe(true);

    // Create backup
    const backupResult = await configManager.createBackup('integration-test');
    expect(backupResult.success).toBe(true);

    // List backups
    const backups = await configManager.listBackups();
    expect(backups.length).toBeGreaterThan(0);

    // Verify health
    const health = configManager.getHealthStatus();
    expect(health.status).toBe('healthy');
  });
});
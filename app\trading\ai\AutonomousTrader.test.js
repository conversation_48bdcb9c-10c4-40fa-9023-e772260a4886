'use strict';

/**
 * Comprehensive Test Suite for AutonomousTrader
 * Tests all enterprise-grade features including:
 * - Error handling with circuit breakers
 * - Async/await patterns
 * - Real-time metrics
 * - CLI interface
 * - Backtesting capabilities
 */
const AutonomousTrader = require('./AutonomousTrader.refactored');
const logger = require('../shared/helpers/logger');
const assert = require('assert');
describe('AutonomousTrader - Enterprise Features', () => {
    let trader;
    let mockConfig;
    beforeEach(() => {
        mockConfig = {
            maxPortfolioRisk,
            maxPositionSize,
            discoveryInterval,
            monitoringInterval,
            rebalanceInterval,
            exchanges'binance'
    ],
        riskLimits: {
            maxDailyLoss,
                maxDrawdown,
                maxPositions,
                minLiquidity,
                maxLeverage,
                stopLossPercent,
                takeProfitPercent
        }
    }
        ;
        trader = new AutonomousTrader(mockConfig);
    });
    describe('Initialization', () => {
        it('should initialize with default config', () => {
            const defaultTrader = new AutonomousTrader();
            assert.equal(defaultTrader.config.maxPortfolioRisk, 0.15);
            assert.equal(defaultTrader.config.exchanges.length, 4);
        });
        it('should initialize with custom config', () => {
            assert.equal(trader.config.maxPortfolioRisk, 0.1);
            assert.equal(trader.config.exchanges.length, 1);
        });
        it('should initialize all components', async () => {
            await trader.initialize();
            assert.equal(trader.isRunning, false);
        });
    });
    describe('Error Handling', () => {
        it('should handle database connection errors gracefully', () => {
            trader.db.getActivePositions = () => {
                throw new Error('DB Connection Failed');
            };
            await trader.loadActivePositions();
            // Should not throw, but log error
        });
        it('should handle exchange connection failures', async () => {
            // Test will pass as it handles failures gracefully
            await trader.createExchangeConnection('invalid_exchange');
        });
        it('should handle position deployment failures', () => {
            const invalidOpportunity = {
                symbol: 'INVALID',
                price: -1,
                confidence
            };
            await trader.deployPosition(invalidOpportunity);
            // Should handle gracefully
        });
    });
    describe('Async/Await Patterns', () => {
        it('should use async/await for discovery phase', async () => {
            await trader.runDiscoveryPhase();
            assert.equal(trader.metrics.discovery.totalScans, 1);
        });
        it('should use async/await for monitoring phase', async () => {
            await trader.runMonitoringPhase();
            // Should complete without throwing
        });
        it('should use async/await for rebalance phase', async () => {
            await trader.runRebalancePhase();
            // Should complete without throwing
        });
    });
    describe('Real-time Metrics', () => {
        it('should track discovery metrics', async () => {
            await trader.runDiscoveryPhase();
            assert.equal(trader.metrics.discovery.totalScans, 1);
            assert.equal(typeof trader.metrics.discovery.opportunities, 'number');
        });
        it('should track position metrics', () => {
            const mockPosition = {
                id: 'TEST-123',
                symbol: 'BTC',
                price,
                positionSize,
                confidence,
                stopLoss,
                takeProfit,
                status: 'active',
                currentPrice,
                pnl
            };
            trader.activePositions.set('TEST-123', mockPosition);
            trader.updatePerformanceMetrics();
            assert.equal(trader.metrics.positions.active, 1);
        });
        it('should provide comprehensive metrics', () => {
            const metrics = trader.getMetrics();
            assert.equal(typeof metrics.activePositions, 'number');
            assert.equal(typeof metrics.discoveryResults, 'number');
            assert.equal(typeof metrics.uptime, 'number');
        });
    });
    describe('CLI Interface', () => {
        it('should support backtesting', async () => {
            const backtestResult = await trader.backtest(mockConfig);
            assert.equal(typeof backtestResult.totalTrades, 'number');
            assert.equal(typeof backtestResult.winRate, 'number');
            assert.equal(typeof backtestResult.totalPnL, 'number');
            assert.equal(Array.isArray(backtestResult.results), true);
        });
        it('should support configuration management', async () => {
            const currentConfig = await trader.getConfig();
            assert.equal(currentConfig.maxPortfolioRisk, 0.1);
            const newConfig = await trader.setConfig({
                maxPortfolioRisk
            });
            assert.equal(newConfig.maxPortfolioRisk, 0.2);
        });
        it('should provide performance reports', async () => {
            const report = await trader.getPerformanceReport();
            assert.equal(typeof report.totalTrades, 'number');
            assert.equal(typeof report.activePositions, 'number');
            assert.equal(typeof report.closedPositions, 'number');
            assert.equal(typeof report.totalPnL, 'number');
            assert.equal(typeof report.winRate, 'number');
        });
    });
    describe('Circuit Breaker Patterns', () => {
        it('should trigger emergency stop on daily loss limit', () => {
            trader.maxDailyLoss = 1000;
            trader.currentDailyLoss = 1000;
            trader.checkEmergencyConditions();
            assert.equal(trader.emergencyStop, true);
        });
        it('should close positions on emergency stop', async () => {
            trader.emergencyStop = true;
            await trader.closeAllPositions('emergency_test');
            // Should handle gracefully
        });
    });
    describe('Backward Compatibility', () => {
        it('should support legacy deploy method', async () => {
            await trader.deploy();
            assert.equal(trader.isRunning, true);
        });
        it('should maintain event emitter interface', done => {
            trader.once('started', () => {
                done();
            });
            trader.start();
        });
    });
    describe('Error Recovery', () => {
        it('should handle component initialization failures', () => {
            trader.secureCredentials.initialize = () => {
                throw new Error('Security Error');
            };
            await trader.initialize();
            // Should handle gracefully
        });
        it('should handle monitoring failures', () => {
            trader.technicalAnalyzer.analyze = () => {
                throw new Error('Analysis Error');
            };
            await trader.runMonitoringPhase();
            // Should handle gracefully
        });
    });
    describe('Integration Tests', () => {
        it('should complete full lifecycle', async () => {
            await trader.initialize();
            await trader.start();

            // Simulate some activity
            await trader.runDiscoveryPhase();
            await trader.runMonitoringPhase();
            await trader.runRebalancePhase();
            await trader.stop();
            const report = await trader.getPerformanceReport();
            assert.equal(typeof report.totalTrades, 'number');
        });
        it('should handle shutdown gracefully', async () => {
            await trader.initialize();
            await trader.start();
            await trader.shutdown();
            assert.equal(trader.isRunning, false);
        });
    });
});

// CLI Interface Tests
const {
    spawn
} = require('child_process');
describe('CLI Interface', () => {
    it('should support command line backtesting', done => {
        const child = spawn('node', ['-e', `
            const AutonomousTrader = require('./AutonomousTrader');
            const trader = new AutonomousTrader();
            trader.backtest({}).then(result => {
                logger.info('Backtest Result:', JSON.stringify(_result));
                process.exit(0);
            });
        `], {
            cwd
        });
        child.on('close', code => {
            assert.equal(code, 0);
            done();
        });
    });
});

// Performance Tests
describe('Performance Tests', () => {
    it('should handle 1000 positions efficiently', () => {
        const perfTrader = new AutonomousTrader();
        for (let i = 0; i < 1000; i++) {
            perfTrader.activePositions.set(`POS-${i}`, {
                id: `POS-${i}`,
                symbol: 'BTC',
                price,
                positionSize,
                pnl() * 1000
            });
        }
        const start = Date.now();
        perfTrader.updatePerformanceMetrics();
        const duration = Date.now() - start;
        assert(duration < 100, `Performance test took ${duration}ms`);
    });
});

// Error Boundary Tests
describe('Error Boundary Tests', () => {
    it('should not crash on null config', () => {
        const nullTrader = new AutonomousTrader(null);
        assert.equal(nullTrader.config.maxPortfolioRisk, 0.15);
    });
    it('should handle missing dependencies gracefully', async () => {
        const missingDepsTrader = new AutonomousTrader();
        missingDepsTrader.db = null;
        missingDepsTrader.riskManager = null;
        await missingDepsTrader.initialize();
        // Should handle gracefully
    });
});

// Export for external testing
module.exports = {
    AutonomousTrader
};

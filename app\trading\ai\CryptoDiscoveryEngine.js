/**
 * Crypto Discovery Engine
 * Discovers new cryptocurrencies, meme coins, and trading opportunities
 * across multiple exchanges and data sources
 */

// @ts-check
const EventEmitter = require('events');
const axios = require('axios').default;
const logger = require('../shared/helpers/logger');
const CCXT = require('ccxt').default;

/**
 * @typedef {Object} DiscoveryConfig
 * @property {number} scanInterval
 * @property {number} minVolume
 * @property {number} minMarketCap
 * @property {number} maxMarketCap
 * @property {number} minLiquidity
 * @property {number} priceChangeThreshold
 * @property {number} volumeSurgeThreshold
 * @property {number} newListingWindow
 * @property {string[]} exchanges
 * @property {string[]} dataSources
 * @property {string[]} memeKeywords
 */

/**
 * @typedef {Object} DataSource
 * @property {string} baseUrl
 * @property {number} rateLimit
 * @property {number} lastRequest
 * @property {string} [apiKey]
 */

/**
 * @typedef {Object} DiscoveryItem
 * @property {string} symbol
 * @property {string} exchange
 * @property {string} type
 * @property {number} score
 * @property {Object} data
 */

/**
 * @typedef {Object} MemeCoin
 * @property {string} symbol
 * @property {string} exchange
 * @property {number} volume
 * @property {number} price
 * @property {number} change24h
 * @property {string[]} keywords
 * @property {number} socialScore
 * @property {number} liquidityScore
 * @property {number} [volumeGrowth]
 * @property {number} [priceGrowth]
 * @property {number} [holderCount]
 * @property {string} [tokenAddress]
 * @property {string} [chainId]
 * @property {string} [pairAddress]
 * @property {string} [dexId]
 * @property {number} [boostAmount]
 * @property {boolean} [hasProfile]
 */

/**
 * @typedef {Object} ArbitrageOpportunity
 * @property {string} symbol
 * @property {string} buyExchange
 * @property {string} sellExchange
 * @property {number} buyPrice
 * @property {number} sellPrice
 * @property {number} spread
 * @property {number} volume
 * @property {number} fees
 */

/**
 * @typedef {Object} GridCandidate
 * @property {string} symbol
 * @property {string} exchange
 * @property {number} volatility
 * @property {number} volume
 * @property {number[]} priceRange
 * @property {number} support
 * @property {number} resistance
 * @property {number} trendStrength
 * @property {number} liquidityScore
 */

/**
 * @typedef {import('ccxt').Exchange} Exchange
 */

/**
 * Initialize the discovery engine
 */
class CryptoDiscoveryEngine extends EventEmitter {
  /**
     * @param {Partial<DiscoveryConfig>} [config]
     */
  constructor(config = {}) {
    super();

    // Configuration with defaults
    this.config = {
      scanInterval: config.scanInterval || 300000, // 5 minutes
      minVolume: config.minVolume || 100000, // $100k min volume
      minMarketCap: config.minMarketCap || 1000000, // $1M min market cap
      maxMarketCap: config.maxMarketCap || 10000000000, // $10B max market cap
      minLiquidity: config.minLiquidity || 50000, // $50k min liquidity
      priceChangeThreshold: config.priceChangeThreshold || 0.1, // 10% price change
      volumeSurgeThreshold: config.volumeSurgeThreshold || 2, // 2x volume surge
      newListingWindow: config.newListingWindow || 7 * 24 * 60 * 60 * 1000, // 7 days
      exchanges: config.exchanges || ['binance', 'coinbase', 'kraken', 'bybit', 'kucoin'],
      dataSources: config.dataSources || ['coingecko', 'coinmarketcap', 'dexscreener'],
      memeKeywords: config.memeKeywords || [
        'doge', 'shib', 'pepe', 'bonk', 'floki', 'elon', 'babydoge', 'wojak',
        'chad', 'moon', 'rocket', 'gem', 'ape', 'diamond', 'lambo', 'safe',
      ],
    };

    // Data sources
    this.dataSources = {
      coingecko: {
        baseUrl: 'https://api.coingecko.com/api/v3',
        rateLimit: 50,
        lastRequest: 0,
        apiKey: process.env.COINGECKO_API_KEY,
      },
      coinmarketcap: {
        baseUrl: 'https://pro-api.coinmarketcap.com/v1',
        rateLimit: 30,
        lastRequest: 0,
        apiKey: process.env.COINMARKETCAP_API_KEY,
      },
      dexscreener: {
        baseUrl: 'https://api.dexscreener.com/latest',
        rateLimit: 100,
        lastRequest: 0,
      },
    };

    // Initialize properties
    this.exchanges = new Map();
    this.coinCache = new Map();
    this.priceCache = new Map();
    this.volumeCache = new Map();
    this.discoveryHistory = new Map();
    this.apiLimits = new Map();
    this.isScanning = false;
    this.lastScanTime = 0;
    this.scanCount = 0;
  }

  /**
     * Initialize the discovery engine
     */
  async initialize() {
    try {
      logger.info('🔍 Initializing Crypto Discovery Engine...');

      // Initialize exchanges
      await this.initializeExchanges();

      // Load historical data
      this.loadHistoricalData();

      // Test data source connections
      await this.testDataSources();

      logger.info('✅ Crypto Discovery Engine initialized successfully');
      this.emit('initialized');

    } catch (error) {
      logger.error('❌ Failed to initialize Crypto Discovery Engine:', error);
      throw error;
    }
  }

  /**
     * Discover new trading opportunities
     * @returns {Promise<DiscoveryItem[]>}
     */
  async discoverOpportunities() {
    if (this.isScanning) {
      logger.warn('⚠️ Discovery scan already in progress');
      return [];
    }

    try {
      this.isScanning = true;
      this.scanCount++;
      const startTime = Date.now();

      logger.info(`🔍 Starting discovery scan #${this.scanCount}...`);

      const discoveries = [];

      // Scan for new listings
      const newListings = await this.scanNewListings();
      discoveries.push(...newListings);

      // Scan for volume surges
      const volumeSurges = await this.scanVolumeSurges();
      discoveries.push(...volumeSurges);

      // Scan for price surges
      const priceSurges = await this.scanPriceSurges();
      discoveries.push(...priceSurges);

      // Scan for meme coin trends
      const memeCoins = await this.scanMemeCoins();
      discoveries.push(...memeCoins);

      // Scan for arbitrage opportunities
      const arbitrageOpps = await this.scanArbitrageOpportunities();
      discoveries.push(...arbitrageOpps);

      // Scan for grid trading opportunities
      const gridOpps = await this.scanGridOpportunities();
      discoveries.push(...gridOpps);

      // Filter and rank discoveries
      const filteredDiscoveries = this.filterAndRankDiscoveries(discoveries);

      const scanTime = Date.now() - startTime;
      logger.info(`✅ Discovery scan completed in ${scanTime}ms: ${filteredDiscoveries.length} opportunities found`);

      this.lastScanTime = Date.now();
      this.emit('scanComplete', filteredDiscoveries);

      return filteredDiscoveries;

    } catch (error) {
      logger.error('❌ Discovery scan failed:', error);
      this.emit('scanError', error);
      return [];
    } finally {
      this.isScanning = false;
    }
  }

  /**
     * Scan for new listings
     * @returns {Promise<DiscoveryItem[]>}
     */
  async scanNewListings() {
    const discoveries = [];

    try {
      // Check each exchange for new listings
      for (const [exchangeName, exchange] of this.exchanges) {
        const newListings = await this.getNewListings(exchange, exchangeName);
        discoveries.push(...newListings);
      }

      // Check data sources for new coins
      const newCoins = await this.getNewCoinsFromDataSources();
      discoveries.push(...newCoins);

      logger.info(`📊 Found ${discoveries.length} new listings`);

    } catch (error) {
      logger.error('❌ Failed to scan new listings:', error);
    }

    return discoveries;
  }

  /**
     * Scan for volume surges
     * @returns {Promise<DiscoveryItem[]>}
     */
  async scanVolumeSurges() {
    const discoveries = [];

    try {
      // Get volume data from all exchanges
      const volumeData = await this.getVolumeData();

      for (const [, data] of volumeData) {
        const volumeRatio = data.current / (data.average || data.current);

        if (volumeRatio > this.config.volumeSurgeThreshold) {
          discoveries.push({
            symbol: data.symbol,
            exchange: data.exchange,
            type: 'volume_surge',
            score: Math.min(volumeRatio / 5, 1), // Normalize to 0-1
            data: {
              volumeRatio: volumeRatio,
              currentVolume: data.current,
              averageVolume: data.average,
              priceChange: data.priceChange,
              marketCap: data.marketCap,
            },
          });
        }
      }

      logger.info(`📊 Found ${discoveries.length} volume surges`);

    } catch (error) {
      logger.error('❌ Failed to scan volume surges:', error);
    }

    return discoveries;
  }

  /**
     * Scan for price surges
     * @returns {Promise<DiscoveryItem[]>}
     */
  async scanPriceSurges() {
    const discoveries = [];

    try {
      // Get price change data
      const priceData = await this.getPriceChangeData();

      for (const [, data] of priceData) {
        const priceChangeAbs = Math.abs(data.priceChange24h);

        if (priceChangeAbs > this.config.priceChangeThreshold) {
          discoveries.push({
            symbol: data.symbol,
            exchange: data.exchange,
            type: 'price_surge',
            score: Math.min(priceChangeAbs / 0.5, 1), // Normalize to 0-1
            data: {
              priceChange24h: data.priceChange24h,
              volume24h: data.volume24h,
              marketCap: data.marketCap,
              direction: data.priceChange24h > 0 ? 'up' : 'down',
            },
          });
        }
      }

      logger.info(`📊 Found ${discoveries.length} price surges`);

    } catch (error) {
      logger.error('❌ Failed to scan price surges:', error);
    }

    return discoveries;
  }

  /**
     * Scan for meme coin trends
     * @returns {Promise<DiscoveryItem[]>}
     */
  async scanMemeCoins() {
    const discoveries = [];

    try {
      // Get meme coin data from various sources
      const memeCoins = await this.getMemeCoins();

      for (const coin of memeCoins) {
        const memeScore = this.calculateMemeScore(coin);

        if (memeScore > 0.6) {
          discoveries.push({
            symbol: coin.symbol,
            exchange: coin.exchange,
            type: 'meme_trend',
            score: memeScore,
            data: {
              socialScore: coin.socialScore,
              volumeGrowth: coin.volumeGrowth,
              priceGrowth: coin.priceGrowth,
              holderCount: coin.holderCount,
              liquidityScore: coin.liquidityScore,
              keywords: coin.keywords,
            },
          });
        }
      }

      logger.info(`📊 Found ${discoveries.length} meme coin trends`);

    } catch (error) {
      logger.error('❌ Failed to scan meme coins:', error);
    }

    return discoveries;
  }

  /**
     * Scan for arbitrage opportunities
     * @returns {Promise<DiscoveryItem[]>}
     */
  async scanArbitrageOpportunities() {
    const discoveries = [];

    try {
      // Compare prices across exchanges
      const arbitrageOpps = await this.findArbitrageOpportunities();

      for (const opp of arbitrageOpps) {
        if (opp.spread > 0.01) { // 1% minimum spread
          discoveries.push({
            symbol: opp.symbol,
            exchange: `${opp.buyExchange}-${opp.sellExchange}`,
            type: 'arbitrage',
            score: Math.min(opp.spread * 10, 1),
            data: {
              spread: opp.spread,
              buyExchange: opp.buyExchange,
              sellExchange: opp.sellExchange,
              buyPrice: opp.buyPrice,
              sellPrice: opp.sellPrice,
              volume: opp.volume,
              fees: opp.fees,
            },
          });
        }
      }

      logger.info(`📊 Found ${discoveries.length} arbitrage opportunities`);

    } catch (error) {
      logger.error('❌ Failed to scan arbitrage opportunities:', error);
    }

    return discoveries;
  }

  /**
     * Scan for grid trading opportunities
     * @returns {Promise<DiscoveryItem[]>}
     */
  async scanGridOpportunities() {
    const discoveries = [];

    try {
      // Find coins suitable for grid trading
      const gridCandidates = await this.findGridTradingCandidates();

      for (const candidate of gridCandidates) {
        const gridScore = this.calculateGridScore(candidate);

        if (gridScore > 0.6) {
          discoveries.push({
            symbol: candidate.symbol,
            exchange: candidate.exchange,
            type: 'grid_opportunity',
            score: gridScore,
            data: {
              volatility: candidate.volatility,
              volume: candidate.volume,
              priceRange: candidate.priceRange,
              support: candidate.support,
              resistance: candidate.resistance,
              trendStrength: candidate.trendStrength,
              liquidityScore: candidate.liquidityScore,
            },
          });
        }
      }

      logger.info(`📊 Found ${discoveries.length} grid trading opportunities`);

    } catch (error) {
      logger.error('❌ Failed to scan grid opportunities:', error);
    }

    return discoveries;
  }

  /**
     * Get new listings from an exchange
     * @param {Exchange} exchange
     * @param {string} exchangeName
     * @returns {Promise<DiscoveryItem[]>}
     */
  async getNewListings(exchange, exchangeName) {
    const listings = [];

    try {
      const markets = await exchange.fetchMarkets();
      const now = Date.now();

      for (const market of markets) {
        if (market && market.created && market.symbol && now - market.created < this.config.newListingWindow) {
          const ticker = await exchange.fetchTicker(market.symbol);
          if (ticker && ticker.baseVolume && ticker.baseVolume > this.config.minVolume) {
            listings.push({
              symbol: market.symbol,
              exchange: exchangeName,
              type: 'new_listing',
              score: 0.9, // High score for new listings
              data: {
                listedAt: market.created,
                volume24h: ticker.baseVolume,
                price: ticker.last,
                change24h: ticker.percentage,
                high24h: ticker.high,
                low24h: ticker.low,
              },
            });
          }
        }
      }

    } catch (error) {
      logger.error(`❌ Failed to get new listings from ${exchangeName}:`, error);
    }

    return listings;
  }

  /**
     * Get volume data from all exchanges
     * @returns {Promise<Map<string, any>>}
     */
  async getVolumeData() {
    const volumeData = new Map();

    for (const [exchangeName, exchange] of this.exchanges) {
      try {
        const tickers = await exchange.fetchTickers();

        for (const [symbol, ticker] of Object.entries(tickers)) {
          if (ticker && ticker.baseVolume && ticker.baseVolume > this.config.minVolume) {
            const key = `${symbol}-${exchangeName}`;
            const historical = this.volumeCache.get(key) || { samples: [] };

            // Calculate average volume
            const average = historical.samples.length > 0 ?
              historical.samples.reduce((/** @type {number} */a, /** @type {number} */b) => a + b, 0) / historical.samples.length : ticker.baseVolume;

            volumeData.set(key, {
              symbol: symbol,
              exchange: exchangeName,
              current: ticker.baseVolume,
              average: average,
              priceChange: ticker.percentage,
              marketCap: ticker.quoteVolume || 0,
            });

            // Update cache
            historical.samples.push(ticker.baseVolume);
            if (historical.samples.length > 100) {
              historical.samples.shift();
            }
            this.volumeCache.set(key, historical);
          }
        }

      } catch (error) {
        logger.error(`❌ Failed to get volume data from ${exchangeName}:`, error);
      }
    }

    return volumeData;
  }

  /**
     * Get price change data
     * @returns {Promise<Map<string, any>>}
     */
  async getPriceChangeData() {
    const priceData = new Map();

    for (const [exchangeName, exchange] of this.exchanges) {
      try {
        const tickers = await exchange.fetchTickers();

        for (const [symbol, ticker] of Object.entries(tickers)) {
          if (ticker && ticker.baseVolume && ticker.baseVolume > this.config.minVolume) {
            priceData.set(`${symbol}-${exchangeName}`, {
              symbol: symbol,
              exchange: exchangeName,
              priceChange24h: ticker.percentage / 100,
              volume24h: ticker.baseVolume,
              marketCap: ticker.quoteVolume || 0,
              price: ticker.last,
            });
          }
        }

      } catch (error) {
        logger.error(`❌ Failed to get price data from ${exchangeName}:`, error);
      }
    }

    return priceData;
  }

  /**
     * Get meme coins from various sources
     * @returns {Promise<MemeCoin[]>}
     */
  async getMemeCoins() {
    const memeCoins = [];

    try {
      // Get from exchanges
      for (const [exchangeName, exchange] of this.exchanges) {
        const tickers = await exchange.fetchTickers();

        for (const [symbol, ticker] of Object.entries(tickers)) {
          if (ticker && this.isMemeCoin(symbol) && ticker.baseVolume && ticker.baseVolume > this.config.minVolume) {
            memeCoins.push({
              symbol: symbol,
              exchange: exchangeName,
              volume: ticker.baseVolume,
              price: ticker.last,
              change24h: ticker.percentage,
              keywords: this.extractMemeKeywords(symbol),
              socialScore: 0.5,
              liquidityScore: 0.7,
            });
          }
        }
      }

      // Get from social media APIs (if available)
      const socialMemeCoins = this.getMemeCoinsFromSocial();
      memeCoins.push(...socialMemeCoins);

      // Get from DeFi platforms
      const defiMemeCoins = await this.getMemeCoinsFromDeFi();
      memeCoins.push(...defiMemeCoins);

    } catch (error) {
      logger.error('❌ Failed to get meme coins:', error);
    }

    return memeCoins;
  }

  /**
     * Calculate meme coin score
     * @param {MemeCoin} coin
     * @returns {number}
     */
  calculateMemeScore(coin) {
    let score = 0;

    // Social score (30%)
    const socialScore = coin.socialScore || 0.5;
    score += socialScore * 0.3;

    // Volume growth (25%)
    const volumeGrowth = coin.volumeGrowth || 1;
    score += Math.min(volumeGrowth / 3, 1) * 0.25;

    // Price growth (20%)
    const priceGrowth = Math.abs(coin.priceGrowth || 0);
    score += Math.min(priceGrowth / 0.5, 1) * 0.2;

    // Liquidity score (15%)
    const liquidityScore = coin.liquidityScore || 0.5;
    score += liquidityScore * 0.15;

    // Keyword relevance (10%)
    const keywordScore = coin.keywords ? coin.keywords.length / 3 : 0;
    score += Math.min(keywordScore, 1) * 0.1;

    return Math.min(score, 1);
  }

  /**
     * Check if a symbol is a meme coin
     * @param {string} symbol
     * @returns {boolean}
     */
  isMemeCoin(symbol) {
    const symbolLower = symbol.toLowerCase();
    return this.config.memeKeywords.some((keyword) =>
      symbolLower.includes(keyword.toLowerCase()),
    );
  }

  /**
     * Extract meme keywords from symbol
     * @param {string} symbol
     * @returns {string[]}
     */
  extractMemeKeywords(symbol) {
    const symbolLower = symbol.toLowerCase();
    return this.config.memeKeywords.filter((keyword) =>
      symbolLower.includes(keyword.toLowerCase()),
    );
  }

  /**
     * Find arbitrage opportunities
     * @returns {Promise<ArbitrageOpportunity[]>}
     */
  async findArbitrageOpportunities() {
    const opportunities = [];
    const priceMap = new Map();

    // Collect prices from all exchanges
    for (const [exchangeName, exchange] of this.exchanges) {
      try {
        const tickers = await exchange.fetchTickers();

        for (const [symbol, ticker] of Object.entries(tickers)) {
          if (ticker && !priceMap.has(symbol)) {
            priceMap.set(symbol, new Map());
          }
          if (ticker) {
            priceMap.get(symbol).set(exchangeName, {
              bid: ticker.bid,
              ask: ticker.ask,
              volume: ticker.baseVolume,
            });
          }
        }
      } catch (error) {
        logger.error(`❌ Failed to get prices from ${exchangeName}:`, error);
      }
    }

    // Find arbitrage opportunities
    for (const [symbol, exchangePrices] of priceMap) {
      if (exchangePrices.size < 2) continue;

      const prices = Array.from(exchangePrices.entries());

      for (let i = 0; i < prices.length; i++) {
        for (let j = i + 1; j < prices.length; j++) {
          const [exchange1, price1] = prices[i];
          const [exchange2, price2] = prices[j];

          if (price1.ask && price2.bid && price1.ask < price2.bid) {
            const spread = (price2.bid - price1.ask) / price1.ask;
            opportunities.push({
              symbol: symbol,
              buyExchange: exchange1,
              sellExchange: exchange2,
              buyPrice: price1.ask,
              sellPrice: price2.bid,
              spread: spread,
              volume: Math.min(price1.volume, price2.volume),
              fees: 0.002, // Estimated fees
            });
          }
        }
      }
    }

    return opportunities;
  }

  /**
     * Find grid trading candidates
     * @returns {Promise<GridCandidate[]>}
     */
  async findGridTradingCandidates() {
    const candidates = [];

    for (const [exchangeName, exchange] of this.exchanges) {
      try {
        const tickers = await exchange.fetchTickers();

        for (const [symbol, ticker] of Object.entries(tickers)) {
          if (ticker && ticker.baseVolume && ticker.baseVolume > this.config.minVolume) {
            // Calculate volatility
            const volatility = (ticker.high - ticker.low) / ticker.last;

            // Check if suitable for grid trading
            if (volatility > 0.02 && volatility < 0.2) {
              candidates.push({
                symbol: symbol,
                exchange: exchangeName,
                volatility: volatility,
                volume: ticker.baseVolume,
                priceRange: [ticker.low, ticker.high],
                support: ticker.low,
                resistance: ticker.high,
                trendStrength: Math.abs(ticker.percentage) / ticker.last,
                liquidityScore: Math.min(ticker.baseVolume / 1000000, 1),
              });
            }
          }
        }
      } catch (error) {
        logger.error(`❌ Failed to get grid candidates from ${exchangeName}:`, error);
      }
    }

    return candidates;
  }

  /**
     * Calculate grid trading score
     * @param {GridCandidate} candidate
     * @returns {number}
     */
  calculateGridScore(candidate) {
    let score = 0;

    // Volatility score (40%)
    const volatilityScore = candidate.volatility > 0.02 && candidate.volatility < 0.15 ? 1 : 0;
    score += volatilityScore * 0.4;

    // Volume score (30%)
    const volumeScore = Math.min(candidate.volume / 1000000, 1);
    score += volumeScore * 0.3;

    // Liquidity score (20%)
    score += candidate.liquidityScore * 0.2;

    // Trend strength (10% - lower is better for grid trading)
    const trendScore = 1 - Math.min(candidate.trendStrength / 0.1, 1);
    score += trendScore * 0.1;

    return Math.min(score, 1);
  }

  /**
     * Filter and rank discoveries
     * @param {DiscoveryItem[]} discoveries
     * @returns {DiscoveryItem[]}
     */
  filterAndRankDiscoveries(discoveries) {
    // Remove duplicates
    const unique = this.removeDuplicates(discoveries);

    // Filter by minimum score
    const filtered = unique.filter((discovery) => discovery.score > 0.5);

    // Sort by score
    filtered.sort((a, b) => b.score - a.score);

    // Limit results
    return filtered.slice(0, 100);
  }

  /**
     * Remove duplicate discoveries
     * @param {DiscoveryItem[]} discoveries
     * @returns {DiscoveryItem[]}
     */
  removeDuplicates(discoveries) {
    const seen = new Set();
    return discoveries.filter((discovery) => {
      const key = `${discovery.symbol}-${discovery.exchange}-${discovery.type}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
     * Initialize exchanges
     * @returns {Promise<void>}
     */
  async initializeExchanges() {
    for (const exchangeName of this.config.exchanges) {
      try {
        const exchange = this.createExchange(exchangeName);
        await exchange.loadMarkets();
        this.exchanges.set(exchangeName, exchange);
        logger.info(`✅ Connected to ${exchangeName} for discovery`);
      } catch (error) {
        logger.error(`❌ Failed to connect to ${exchangeName}:`, error);
      }
    }
  }

  /**
     * Create exchange instance
     * @param {string} name
     * @returns {Exchange}
     */
  createExchange(name) {
    /** @type {any} */
    const exchangeClass = CCXT[/** @type {keyof typeof CCXT} */(name)];
    if (!exchangeClass || typeof exchangeClass !== 'function') {
      throw new Error(`Exchange ${name} not supported`);
    }

    return new exchangeClass({
      apiKey: process.env[`${name.toUpperCase()}_API_KEY`],
      secret: process.env[`${name.toUpperCase()}_SECRET`],
      password: process.env[`${name.toUpperCase()}_PASSWORD`],
      sandbox: process.env.NODE_ENV !== 'production',
      enableRateLimit: true,
      options: {
        adjustForTimeDifference: true,
        recvWindow: 60000,
      },
    });
  }

  /**
     * Load historical data
     */
  loadHistoricalData() {
    // Load volume history, price history, etc.
    logger.info('📊 Loading historical data...');
  }

  /**
     * Test data source connections
     * @returns {Promise<void>}
     */
  async testDataSources() {
    for (const [name, source] of Object.entries(this.dataSources)) {
      try {
        await this.makeApiRequest(source, '/ping');
        logger.info(`✅ Connected to ${name}`);
      } catch (error) {
        logger.warn(`⚠️ Failed to connect to ${name}:`, error);
      }
    }
  }

  /**
     * Make API request with rate limiting
     * @param {DataSource} source
     * @param {string} endpoint
     * @param {any} [params]
     * @returns {Promise<any>}
     */
  async makeApiRequest(source, endpoint, params = {}) {
    const now = Date.now();
    const timeSinceLastRequest = now - source.lastRequest;
    const minInterval = 60000 / source.rateLimit; // ms per request

    if (timeSinceLastRequest < minInterval) {
      await new Promise((resolve) =>
        setTimeout(resolve, minInterval - timeSinceLastRequest),
      );
    }

    const response = await axios.get(`${source.baseUrl}${endpoint}`, {
      params,
      headers: source.apiKey ? { 'X-CMC_PRO_API_KEY': source.apiKey } : {},
    });

    source.lastRequest = Date.now();
    return response.data;
  }

  /**
     * Get new coins from data sources
     * @returns {Promise<DiscoveryItem[]>}
     */
  async getNewCoinsFromDataSources() {
    const newCoins = [];

    try {
      // Get from CoinGecko
      const coinGeckoData = await this.makeApiRequest(
        this.dataSources.coingecko,
        '/coins/markets',
        { vs_currency: 'usd', order: 'market_cap_desc', per_page: 100 },
      );

      // Filter for new coins
      const now = Date.now();
      for (const coin of coinGeckoData) {
        if (coin.atl_date && now - new Date(coin.atl_date).getTime() < this.config.newListingWindow) {
          newCoins.push({
            symbol: coin.symbol,
            exchange: 'coingecko',
            type: 'new_listing',
            score: 0.8,
            data: {
              name: coin.name,
              marketCap: coin.market_cap,
              volume24h: coin.total_volume,
              priceChange24h: coin.price_change_percentage_24h / 100,
              listedAt: new Date(coin.atl_date).getTime: jest.fn(),
            },
          });
        }
      }

    } catch (error) {
      logger.error('❌ Failed to get new coins from data sources:', error);
    }

    return newCoins;
  }

  /**
     * Get meme coins from social media
     * @returns {MemeCoin[]}
     */
  getMemeCoinsFromSocial() {
    // This would integrate with Twitter, Reddit, Discord APIs
    // For now, returning empty array
    return [];
  }

  /**
     * Get meme coins from DeFi platforms
     * @returns {Promise<MemeCoin[]>}
     */
  async getMemeCoinsFromDeFi() {
    const memeCoins = [];

    try {
      // Get boosted tokens (high-potential meme coins)
      const boostedTokens = await this.getDexScreenerBoostedTokens();
      memeCoins.push(...boostedTokens);

      // Get latest token profiles
      const tokenProfiles = await this.getDexScreenerTokenProfiles();
      memeCoins.push(...tokenProfiles);

      // Search for popular meme coin keywords
      const searchResults = await this.getDexScreenerSearchResults();
      memeCoins.push(...searchResults);

      // Remove duplicates
      const uniqueMemeCoins = this.removeDuplicateMemeCoins(memeCoins);

      logger.info(`📊 Found ${uniqueMemeCoins.length} meme coins from DeFi platforms`);

    } catch (error) {
      logger.error('❌ Failed to get meme coins from DeFi:', error);
    }

    return memeCoins;
  }

  /**
     * Get boosted tokens from DexScreener
     * @returns {Promise<MemeCoin[]>}
     */
  async getDexScreenerBoostedTokens() {
    const memeCoins = [];

    try {
      // Get latest boosted tokens
      const latestBoosts = await this.makeApiRequest(
        this.dataSources.dexscreener,
        '/token-boosts/latest/v1',
      );

      // Get top boosted tokens
      const topBoosts = await this.makeApiRequest(
        this.dataSources.dexscreener,
        '/token-boosts/top/v1',
      );

      // Process boosted tokens
      const allBoosts = [...(latestBoosts || []), ...(topBoosts || [])];

      for (const boost of allBoosts) {
        if (boost && boost.description && this.isMemeCoinByDescription(boost.description)) {
          memeCoins.push({
            symbol: this.extractSymbolFromDescription(boost.description),
            exchange: 'dex',
            volume: boost.volume || 0,
            price: boost.price || 0,
            change24h: boost.change24h || 0,
            keywords: this.extractMemeKeywords(boost.description),
            socialScore: 0.8,
            liquidityScore: Math.min(boost.totalAmount / 100000 || 0, 1),
            boostAmount: boost.boostAmount || 0,
            tokenAddress: boost.tokenAddress || '',
            chainId: boost.chainId || '',
          });
        }
      }

    } catch (error) {
      logger.error('❌ Failed to get boosted tokens from DexScreener:', error);
    }

    return memeCoins;
  }

  /**
     * Get token profiles from DexScreener
     * @returns {Promise<MemeCoin[]>}
     */
  async getDexScreenerTokenProfiles() {
    const memeCoins = [];

    try {
      const profiles = await this.makeApiRequest(
        this.dataSources.dexscreener,
        '/token-profiles/latest/v1',
      );

      for (const profile of profiles || []) {
        if (profile && profile.description && this.isMemeCoinByDescription(profile.description)) {
          memeCoins.push({
            symbol: this.extractSymbolFromDescription(profile.description),
            exchange: 'dex',
            volume: profile.volume || 0,
            price: profile.price || 0,
            change24h: profile.change24h || 0,
            keywords: this.extractMemeKeywords(profile.description),
            socialScore: 0.7,
            liquidityScore: profile.liquidityScore || 0.5,
            tokenAddress: profile.tokenAddress || '',
            chainId: profile.chainId || '',
            hasProfile: true,
          });
        }
      }

    } catch (error) {
      logger.error('❌ Failed to get token profiles from DexScreener:', error);
    }

    return memeCoins;
  }

  /**
     * Get search results for meme coins from DexScreener
     * @returns {Promise<MemeCoin[]>}
     */
  async getDexScreenerSearchResults() {
    const memeCoins = [];

    try {
      // Search for popular meme coin keywords
      const searchQueries = ['doge', 'shiba', 'pepe', 'bonk', 'floki', 'meme'];

      for (const query of searchQueries) {
        const searchResults = await this.makeApiRequest(
          this.dataSources.dexscreener,
          '/latest/dex/search',
          { q: query },
        );

        for (const pair of searchResults.pairs || []) {
          if (pair && pair.baseToken && pair.baseToken.symbol && this.isMemeCoin(pair.baseToken.symbol) && pair.liquidity && pair.liquidity.usd > 50000) {
            memeCoins.push({
              symbol: pair.baseToken.symbol,
              exchange: 'dex',
              volume: pair.volume?.h24 || 0,
              price: parseFloat(pair.priceUsd) || 0,
              change24h: pair.priceChange?.h24 || 0,
              keywords: this.extractMemeKeywords(pair.baseToken.symbol),
              socialScore: 0.6,
              liquidityScore: Math.min(pair.liquidity.usd / 100000, 1),
              pairAddress: pair.pairAddress || '',
              chainId: pair.chainId || '',
              dexId: pair.dexId || '',
              volumeGrowth: (pair.volume?.h24 || 0) / (pair.volume?.h6 || pair.volume?.h24 || 1),
              priceGrowth: (pair.priceChange?.h24 || 0) / 100,
            });
          }
        }

        // Add delay between searches to respect rate limits
        await new Promise((resolve) => setTimeout(resolve, 200));
      }

    } catch (error) {
      logger.error('❌ Failed to get search results from DexScreener:', error);
    }

    return memeCoins;
  }

  /**
     * Check if description contains meme coin indicators
     * @param {string} [description]
     * @returns {boolean}
     */
  isMemeCoinByDescription(description) {
    if (!description) return false;

    const descriptionLower = description.toLowerCase();
    const memeIndicators = [
      'meme', 'doge', 'shib', 'pepe', 'bonk', 'floki', 'moon', 'rocket',
      'gem', 'ape', 'diamond', 'lambo', 'safe', 'baby', 'mini', 'meta',
      'community', 'viral', 'trending', 'hype', 'cult', 'squad',
    ];

    return memeIndicators.some((indicator) => descriptionLower.includes(indicator));
  }

  /**
     * Extract symbol from description
     * @param {string} [description]
     * @returns {string}
     */
  extractSymbolFromDescription(description) {
    if (!description) return 'UNKNOWN';

    // Try to find a symbol pattern like $SYMBOL or (SYMBOL)
    const symbolMatch = description.match(/\$([A-Z]{2,10})|\(?([A-Z]{2,10})\)?/);
    if (symbolMatch) {
      return symbolMatch[1] || symbolMatch[2];
    }

    // Fallback to first word if no pattern found
    const firstWord = description.split(' ')[0].replace(/[^A-Za-z0-9]/g, '').toUpperCase();
    return firstWord.length > 1 ? firstWord : 'UNKNOWN';
  }

  /**
     * Remove duplicate meme coins
     * @param {MemeCoin[]} memeCoins
     * @returns {MemeCoin[]}
     */
  removeDuplicateMemeCoins(memeCoins) {
    const seen = new Map();
    return memeCoins.filter((coin) => {
      const key = `${coin.symbol}-${coin.chainId || 'unknown'}`;
      if (seen.has(key)) {
        // Keep the one with higher social score
        const existing = seen.get(key);
        if (coin.socialScore > existing.socialScore) {
          seen.set(key, coin);
          return true;
        }
        return false;
      }
      seen.set(key, coin);
      return true;
    });
  }

  /**
     * Get discovery statistics
     * @returns {{totalScans: number, lastScanTime: number, isScanning: boolean, cacheSize: number, exchangeCount: number, discoveryHistorySize: number}}
     */
  getDiscoveryStats() {
    return {
      totalScans: this.scanCount,
      lastScanTime: this.lastScanTime,
      isScanning: this.isScanning,
      cacheSize: this.coinCache.size + this.priceCache.size + this.volumeCache.size,
      exchangeCount: this.exchanges.size,
      discoveryHistorySize: this.discoveryHistory.size,
    };
  }
}

module.exports = CryptoDiscoveryEngine;

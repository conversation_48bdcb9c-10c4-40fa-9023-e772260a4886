// Import logger for consistent logging
const logger = (() => {
  try {
    return require('./utils/logger');
  } catch (e1) {
    try {
      return require('../utils/logger');
    } catch (e2) {
      try {
        return require('../../utils/logger');
      } catch (e3) {
        return console; // Fallback to console if logger not available
      }
    }
  }
})();

/**
 * CLI Interface for AutonomousTrader
 * Provides command-line interface for backtesting, configuration, and monitoring
 */

const AutonomousTrader = require('./AutonomousTrader');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const Table = require('cli-table3');
const fs = require('fs').promises;
const path = require('path');

// Dynamic import for chalk (ES module)
let chalk;

async function getChalk() {
  if (!chalk) {
    chalk = await import('chalk');
    // Allow config path via ENV or CLI argument, fallback to default
    this.configPath =
            process.env.TRADER_CONFIG_PATH ||
            path.join(__dirname, '../../config/trading-config.json');
  }
  return chalk;
}

class AutonomousTraderCLI {
  constructor() {
    this.trader = null;
    this.argv = yargs(hideBin(process.argv))
      .command('start', 'Start the trading system', {}, this.start.bind(this))
      .command('stop', 'Stop the trading system', {}, this.stop.bind(this))
      .command('backtest', 'Run backtesting analysis', {
        duration: { alias: 'd', type: 'number', default: 30, description: 'Backtest duration in days' },
        config: { alias: 'c', type: 'string', description: 'Custom config file path' },
      }, this.backtest.bind(this))
      .command('config', 'Manage configuration', {
        set: { type: 'string', description: 'Set configuration value (key=value)' },
        get: { type: 'string', description: 'Get configuration value' },
        file: { type: 'string', description: 'Load configuration from file' },
        configPath: { type: 'string', description: 'Specify config file path' },
      }, this.config.bind(this))
      .command('status', 'Show system status', {}, this.status.bind(this))
      .command('metrics', 'Display performance metrics', {}, this.metrics.bind(this))
      .command('monitor', 'Start monitoring dashboard', {}, this.monitor.bind(this))
      .option('verbose', {
        alias: 'v',
        type: 'boolean',
        description: 'Verbose logging',
      })
      .demandCommand(1, 'You need at least one command before moving on')
      .help()
      .alias('help', 'h')
      .argv;
  }

  async run() {
    // The yargs setup in the constructor will automatically call the correct method
    // based on the command line arguments. This method is here to initiate that process.
  }

  async start() {
    const chalk = await getChalk();
    logger.info(chalk.green('🚀 Starting Autonomous Trading System...'));
    this.trader = new AutonomousTrader(await this.loadConfig());
    await this.trader.initialize();
    await this.trader.start();

    logger.info(chalk.green('✅ Trading system started successfully'));
    logger.info(chalk.blue('Press Ctrl+C to stop'));

    process.on('SIGINT', async () => {
      logger.info(chalk.yellow('\n🛑 Shutting down...'));
      await this.trader.stop();
      process.exit(0);
    });
  }

  async stop() {
    const chalk = await getChalk();
    if (this.trader) {
      logger.info(chalk.yellow('🔴 Stopping trading system...'));
      await this.trader.stop();
      logger.info(chalk.green('✅ Trading system stopped'));
    } else {
      logger.info(chalk.red('❌ Trading system not running'));
    }
  }

  /**
     * @param {{ duration: number; config?: string; }} argv
     */
  async backtest(argv) {
    const chalk = await getChalk();
    logger.info(chalk.cyan(`📊 Running backtest for ${argv.duration} days...`));

    const config = argv.config
      ? JSON.parse(await fs.readFile(argv.config, 'utf8'))
      : await this.loadConfig();

    const trader = new AutonomousTrader(config);
    await trader.initialize();
    const result = await trader.runBacktest(argv.duration);
    await this.displayBacktestResults(result);
  }

  /**
     * @param {{ set?: string; get?: string; file?: string; configPath?: string; }} argv
     */
  async config(argv) {
    const chalk = await getChalk();
    // Use configPath from CLI argument if provided
    if (argv.configPath) {
      this.configPath = argv.configPath;
    }
    const config = await this.loadConfig();

    if (argv.set) {
      const [key, value] = argv.set.split('=');
      const keys = key.split('.');
      let target = config;
      for (let i = 0; i < keys.length - 1; i++) {
        if (typeof target[keys[i]] !== 'object' || target[keys[i]] === null) {
          target[keys[i]] = {};
        }
        target = target[keys[i]];
      }
      const finalKey = keys[keys.length - 1];

      let parsedValue;
      if (value === 'true') {
        parsedValue = true;
      } else if (value === 'false') {
        parsedValue = false;
      } else if (!isNaN(Number(value)) && value.trim() !== '') {
        parsedValue = Number(value);
      } else {
        parsedValue = value;
      }
      target[finalKey] = parsedValue;
      await this.saveConfig(config);
      logger.info(chalk.green(`✅ Set ${key} = ${target[finalKey]}`));
    } else if (argv.get) {
      // Support dot notation for nested keys
      const keys = argv.get.split('.');
      let value = config;
      for (const k of keys) {
        value = value?.[k];
        if (value === undefined) break;
      }
      logger.info(chalk.blue(`${argv.get}: ${value === undefined ? 'Not Found' : JSON.stringify(value)}`));
    } else if (argv.file) {
      const newConfig = JSON.parse(await fs.readFile(argv.file, 'utf8'));
      await this.saveConfig(newConfig);
      logger.info(chalk.green('✅ Configuration loaded from file'));
    } else {
      logger.info(chalk.cyan('Current Configuration:'));
      logger.info(JSON.stringify(config, null, 2));
    }
  }

  async status() {
    const chalk = await getChalk();
    if (!this.trader) {
      this.trader = new AutonomousTrader(await this.loadConfig());
      await this.trader.initialize();
    }
    const status = this.trader.getStatus();
    const table = new Table({
      head: [chalk.cyan('Metric'), chalk.cyan('Value')],
      colWidths: [30, 30],
    });

    table.push(
      ['Is Running', status.isRunning ? chalk.green('Yes') : chalk.red('No')],
      ['Active Positions', status.activePositions],
      ['Emergency Stop', status.emergencyStop ? chalk.red('Yes') : 'No'],
      [
        'Daily Loss',
        `${typeof status.currentDailyLoss === 'number'
          ? status.currentDailyLoss.toFixed(2)
          : 'N/A'
        } / ${typeof status.maxDailyLoss === 'number'
          ? status.maxDailyLoss.toFixed(2)
          : 'N/A'
        }`,
      ],
    );

    logger.info(table.toString());
  }

  async metrics() {
    const chalk = await getChalk();
    if (!this.trader) {
      this.trader = new AutonomousTrader(await this.loadConfig());
      await this.trader.initialize();
    }

    const metrics = this.trader.getMetrics();
    const report = await this.trader.getPerformanceReport();

    logger.info(chalk.cyan('📈 Performance Metrics'));
    const table = new Table({
      head: [chalk.cyan('Metric'), chalk.cyan('Value')],
      colWidths: [30, 30],
    });

    table.push(
      ['Total Trades', report.totalTrades],
      ['Active Positions', report.activePositions],
      ['Win Rate', `${(report.winRate * 100).toFixed(2)}%`],
      ['Total PnL', typeof report.totalPnL === 'number' ? report.totalPnL.toFixed(2) : '0.00'],
      ['System Uptime', `${(metrics.uptime / 1000 / 60).toFixed(1)} min`],
      ['Discovery Scans', metrics.discovery?.totalScans || 0],
      ['Opportunities Found', metrics.discovery?.opportunities || 0],
    );

    logger.info(table.toString());
  }

  async monitor() {
    const chalk = await getChalk();
    logger.info(chalk.cyan('🔍 Starting monitoring dashboard...'));

    const trader = new AutonomousTrader(await this.loadConfig());
    await trader.initialize();

    logger.info(chalk.green('✅ Monitoring started'));
    logger.info('Press Ctrl+C to exit');

    const interval = setInterval(async () => {
      const metrics = trader.getMetrics();
      const table = new Table({
        head: [chalk.cyan('Metric'), chalk.cyan('Value')],
        colWidths: [30, 30],
      });

      table.push(
        ['Active Positions', metrics.positions?.active || 0],
        ['Total Trades', metrics.positions?.total || 0],
        [
          'Win Rate',
          metrics.performance?.winRate !== undefined
            ? `${(metrics.performance.winRate * 100).toFixed(1)}%`
            : 'N/A',
        ],
        ['Total PnL', metrics.performance?.totalPnL?.toFixed(2) || '0.00'],
        ['Discovery Scans', metrics.discovery?.totalScans || 0],
        ['Uptime', `${(metrics.uptime / 1000 / 60).toFixed(1)} min`],
      );

      logger.info(table.toString());
    }, 5000);

    process.on('SIGINT', async () => {
      clearInterval(interval);
      const chalk = await getChalk();
      logger.info(chalk.yellow('🛑 Monitoring stopped'));
      process.exit(0);
    });
  }

  async loadConfig() {
    try {
      const configData = await fs.readFile(this.configPath, 'utf8');
      return JSON.parse(configData);
    } catch (error) {
      const chalk = await getChalk();
      logger.info(chalk.yellow('⚠️  Using default configuration'));
      return {
        maxPortfolioRisk: 0.1,
        maxPositionSize: 0.05,
        discoveryInterval: 60000,
        monitoringInterval: 15000,
        rebalanceInterval: 3600000,
        exchanges: ['binance', 'coinbase', 'kraken', 'bybit'],
        riskLimits: {
          maxDailyLoss: 0.05,
          maxDrawdown: 0.15,
          maxPositions: 10,
          minLiquidity: 1000,
          maxLeverage: 5,
          stopLossPercent: 0.02,
          takeProfitPercent: 0.05,
        },
      };
    }
  }

  async saveConfig(config) {
    await fs.writeFile(this.configPath, JSON.stringify(config, null, 2));
  }
  /**
     * @param {{ totalTrades: any; winRate: number; totalPnL: number; sharpeRatio: number; maxDrawdown: number; results: any[]; }} result
     */
  async displayBacktestResults(result) {
    const chalk = await getChalk();
    logger.info(chalk.green('\n📊 Backtesting Results'));
    logger.info('======================');

    const table = new Table({
      head: [chalk.cyan('Metric'), chalk.cyan('Value')],
      colWidths: [30, 20],
    });

    table.push(
      ['Total Trades', result.totalTrades],
      ['Win Rate', `${(result.winRate * 100).toFixed(2)}%`],
      ['Total PnL', typeof result.totalPnL === 'number' ? result.totalPnL.toFixed(2) : '0.00'],
      ['Sharpe Ratio', typeof result.sharpeRatio === 'number' ? result.sharpeRatio.toFixed(2) : 'N/A'],
      ['Max Drawdown', typeof result.maxDrawdown === 'number' ? `${(result.maxDrawdown * 100).toFixed(2)}%` : 'N/A'],
    );

    logger.info(table.toString());

    if (result && result.results && result.results.length > 0) {
      logger.info(chalk.yellow('\n📈 Sample Trades:'));
      result.results.slice(0, 5).forEach(/** @param {{ symbol?: string; pnl?: number; }} trade */(trade) => {
        const symbol = trade?.symbol ?? 'N/A';
        const pnl = typeof trade?.pnl === 'number' ? trade.pnl : 0;
        logger.info(`${symbol}: ${pnl > 0 ? chalk.green('+' + pnl.toFixed(2)) : chalk.red(pnl.toFixed(2))}`);
      });
    }
  }
}

// Direct execution
if (require.main === module) {
  const cli = new AutonomousTraderCLI();
  cli.run().catch(async (error) => {
    const chalk = await getChalk();
    logger.error(chalk.red('❌ CLI Error:'), error);
    process.exit(1);
  });
}
module.exports = AutonomousTraderCLI;

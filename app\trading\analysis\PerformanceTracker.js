/**
 * @fileoverview Performance Tracker
 * @description Tracks and analyzes trading performance metrics
 */

const EventEmitter = require('events');
const logger = require('../shared/helpers/logger');

class PerformanceTracker extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      trackingInterval: options.trackingInterval || 60000, // 1 minute
      historyLimit: options.historyLimit || 1000,
      ...options,
    };

    this.isRunning = false;
    this.metrics = {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      totalPnL: 0,
      winRate: 0,
      averagePnL: 0,
      maxDrawdown: 0,
      sharpeRatio: 0,
    };

    this.tradeHistory = [];
    this.performanceHistory = [];
    this.trackingIntervalId = null;
    this.lastUpdate = null;
  }

  async start() {
    if (this.isRunning) {
      logger.warn('PerformanceTracker is already running');
      return;
    }

    logger.info('📊 Starting Performance Tracker...');
    this.isRunning = true;

    // Start tracking interval
    this.trackingIntervalId = setInterval(() => {
      this.updateMetrics();
    }, this.options.trackingInterval);

    // Initial update
    await this.updateMetrics();

    logger.info('✅ Performance Tracker started');
  }

  async stop() {
    if (!this.isRunning) {
      logger.warn('PerformanceTracker is not running');
      return;
    }

    logger.info('🛑 Stopping Performance Tracker...');
    this.isRunning = false;

    if (this.trackingIntervalId) {
      clearInterval(this.trackingIntervalId);
      this.trackingIntervalId = null;
    }

    logger.info('✅ Performance Tracker stopped');
  }

  async updateMetrics() {
    try {
      this.lastUpdate = new Date();

      // Calculate metrics from trade history
      this.calculateMetrics();

      // Store performance snapshot
      const snapshot = {
        ...this.metrics,
        timestamp: this.lastUpdate.toISOString()
      };

      this.performanceHistory.push(snapshot);

      // Limit history size
      if (this.performanceHistory.length > this.options.historyLimit) {
        this.performanceHistory = this.performanceHistory.slice(-this.options.historyLimit);
      }

      this.emit('metrics-update', this.metrics);
    } catch (error) {
      logger.error('❌ Error updating performance metrics:', error);
      this.emit('error', error);
    }
  }

  calculateMetrics() {
    if (this.tradeHistory.length === 0) {
      return;
    }

    this.metrics.totalTrades = this.tradeHistory.length;
    this.metrics.winningTrades = this.tradeHistory.filter(trade => trade.pnl > 0).length;
    this.metrics.losingTrades = this.tradeHistory.filter(trade => trade.pnl < 0).length;
    this.metrics.totalPnL = this.tradeHistory.reduce((sum, trade) => sum + trade.pnl, 0);
    this.metrics.winRate = this.metrics.totalTrades > 0 ?
      (this.metrics.winningTrades / this.metrics.totalTrades) * 100 : 0;
    this.metrics.averagePnL = this.metrics.totalTrades > 0 ?
      this.metrics.totalPnL / this.metrics.totalTrades : 0;

    // Calculate max drawdown
    this.metrics.maxDrawdown = this.calculateMaxDrawdown();

    // Calculate Sharpe
  ratio(simplified)
    this.metrics.sharpeRatio = this.calculateSharpeRatio();
  }

  calculateMaxDrawdown() {
    if (this.performanceHistory.length < 2) {
      return 0;
    }

    let maxDrawdown = 0;
    let peak = this.performanceHistory[0].totalPnL;

    for (const snapshot of this.performanceHistory) {
      if (snapshot.totalPnL > peak) {
        peak = snapshot.totalPnL;
      }

      const drawdown = (peak - snapshot.totalPnL) / Math.abs(peak) * 100;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }

    return maxDrawdown;
  }

  calculateSharpeRatio() {
    if (this.performanceHistory.length < 2) {
      return 0;
    }

    const returns = [];
    for (let i = 1; i < this.performanceHistory.length; i++) {
      const prevPnL = this.performanceHistory[i - 1].totalPnL;
      const currentPnL = this.performanceHistory[i].totalPnL;
      const returnRate = prevPnL !== 0 ? (currentPnL - prevPnL) / Math.abs(prevPnL) : 0;
      returns.push(returnRate);
    }

    if (returns.length === 0) {
      return 0;
    }

    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);

    return stdDev !== 0 ? avgReturn / stdDev : 0;
  }

  addTrade(trade) {
    const tradeWithPnL = {
      ...trade,
      pnl: trade.pnl || 0,
      timestamp: trade.timestamp || new Date().toISOString(),
    };

    this.tradeHistory.push(tradeWithPnL);

    // Limit history size
      if (this.tradeHistory.length > this.options.historyLimit) {
      this.tradeHistory = this.tradeHistory.slice(-this.options.historyLimit);
    }

    // Update metrics immediately
    this.calculateMetrics();

    this.emit('trade-added', tradeWithPnL);
  }

  getMetrics() {
    return this.metrics;
  }

  getPerformanceHistory(timeRange = '24h') {
    const now = new Date();
    let cutoffTime;

    switch (timeRange) {
    case '1h':
      cutoffTime = new Date(now.getTime() - 60 * 60 * 1000);
      break;
    case '24h':
      cutoffTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      cutoffTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      cutoffTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    default:
      return this.performanceHistory;
    }

    return this.performanceHistory.filter(snapshot =>
      new Date(snapshot.timestamp) >= cutoffTime,
    );
  }

  getTradeHistory() {
    return this.tradeHistory;
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      lastUpdate: this.lastUpdate,
      totalTrades: this.metrics.totalTrades,
      totalPnL: this.metrics.totalPnL,
      winRate: this.metrics.winRate,
      timestamp: new Date().toISOString(),
    };
  }

  reset() {
    this.metrics = {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      totalPnL: 0,
      winRate: 0,
      averagePnL: 0,
      maxDrawdown: 0,
      sharpeRatio: 0,
    };

    this.tradeHistory = [];
    this.performanceHistory = [];

    logger.info('📊 Performance metrics reset');
    this.emit('metrics-reset');
  }
}

module.exports = PerformanceTracker;
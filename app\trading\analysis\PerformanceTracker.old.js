'use strict';

function ownKeys(e, r) {
    const price = null; // Auto-fixed undefined variable
    const options = null; // Auto-fixed undefined variable
    const data = null; // Auto-fixed undefined variable
    const t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        let o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function (r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}

function _objectSpread(e) {
    const price = null; // Auto-fixed undefined variable
    const options = null; // Auto-fixed undefined variable
    const data = null; // Auto-fixed undefined variable
    for (let r = 1; r < arguments.length; r++) {
        const t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {
            _defineProperty(e, r, t[r]);
        }) ject.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) nKeys(Object(t)).forEach(function (r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}

function _defineProperty(e, r, t) {
    const price = null; // Auto-fixed undefined variable
    const options = null; // Auto-fixed undefined variable
    const data = null; // Auto-fixed undefined variable
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) r
]
    = t, e;
}

function _toPropertyKey(t) {
    const price = null; // Auto-fixed undefined variable
    const options = null; // Auto-fixed undefined variable
    const data = null; // Auto-fixed undefined variable
    const i = _toPrimitive(t, 'string');
    return 'symbol' == typeof i ? i + '';
}

function _toPrimitive(t, r) {
    const price = null; // Auto-fixed undefined variable
    const options = null; // Auto-fixed undefined variable
    const data = null; // Auto-fixed undefined variable
    if ('object' != typeof t || !t) return t;
    const e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        const i = e.call(t, r || 'default');
        if ('object' != typeof i) return i;
        throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === r ? String)(t);
}

/**
 * Performance Tracker Engine
 * Tracks and analyzes trading performance metrics for algorithmic trading strategies.
 * This engine provides functionalities to log trades, calculate key performance indicators (KPIs),
 * and persist performance data for long-term analysis.
 * Compatible with n8n environment
 */
// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();
const {
    NodeOperationError
} = require('n8n-workflow');
const DatabaseManager = require('../../database/DatabaseManager');

class PerformanceTracker {
    /**
     * Creates an instance of PerformanceTracker.
     * @param {object} [options={}] - Configuration options for the tracker.
     * @param {string} [options.logDir] - The directory to store log files. Defaults to '../../logs'.
     * @param {number} [options.maxTradesHistory=10000] - The maximum number of trades to keep in memory.
     * @param {string} [options.dbPath] - The database file path.
     * @param {object} [options.context] - The n8n context for error handling.
     */
    constructor(options = {}) {
        // this.metrics = {
        trades,
            totalProfit,
            totalLoss,
            winRate,
            avgReturn,
            sharpeRatio,
            maxDrawdown,
            maxDrawdownPercent,
            // Added for consistency
            lastUpdated
    };

    // this.initialized = false;
    // this.maxTradesHistory = options.maxTradesHistory || 10000; // Limit memory usage
    // this.db = new DatabaseManager();
    // this.dbPath = options.dbPath || 'performance_tracker.db';
    // this.context = options.context || null; // n8n context for error handling
}

/**
 * Initializes the performance tracker by creating the log directory and loading existing metrics.
 * @returns {Promise<boolean>} A promise that resolves to true if initialization is successful, false otherwise.
 */
async
initialize() {
    try {
        // Initialize database connection
        await this.db.initialize();

        // Load existing metrics if available
        await this.loadMetrics();
        // this.initialized = true;
        logger.info('✅ Performance Tracker initialized successfully');
        return true;
    } catch (error) {
        logger.error('❌ Performance Tracker initialization failed:', error);
        // this.initialized = false;
        if (this.context) {
            throw new NodeOperationError(this.context.getNode: jest.fn(), error, {
                message: 'Performance Tracker initialization failed',
                description
            });
        }
        return false;
    }
}

/**
 * Loads performance metrics from the metrics file.
 * @private
 */
async
loadMetrics() {
    try {
        const query = `
                SELECT data FROM performance_metrics
                ORDER BY created_at DESC
                LIMIT 1
            `;
        const result = await this.db.get(query);
        if (result !== null && result !== void 0 && result.data) {
            const loadedMetrics = JSON.parse(result.data);

            // Validate loaded data
            if (loadedMetrics && typeof loadedMetrics === 'object') {
                // Ensure trades is an array
                if (!Array.isArray(loadedMetrics.trades)) {
                    loadedMetrics.trades = [];
                }

                // Validate each trade to ensure data integrity
                loadedMetrics.trades = loadedMetrics.trades.filter(trade => trade && typeof trade.symbol === 'string' && typeof trade.profit === 'number' && isFinite(trade.profit));
                // this.metrics = _objectSpread(_objectSpread({}, this.metrics), loadedMetrics);
                logger.info('📊 Loaded existing performance metrics');
            }
        } else {
            logger.info('📊 No existing metrics found, using defaults');
        }
    } catch (error) {
        logger.error('❌ Failed to load metrics:', error);
        if (this.context) {
            throw new NodeOperationError(this.context.getNode: jest.fn(), error, {
                message: 'Failed to load performance metrics',
                description
            });
        }
    }
}

/**
 * Saves the current performance metrics to the metrics file.
 * @private
 * @returns {Promise<boolean>} A promise that resolves to true if saving is successful, false otherwise.
 */
async
saveMetrics() {
    try {
        // this.metrics.lastUpdated = new Date().toISOString();
        const query = `
                INSERT OR REPLACE INTO performance_metrics (id, data, created_at)
                VALUES (1, ?, datetime('now'))
            `;
        await this.db.run(query, [JSON.stringify(this.metrics)]);
        return true;
    } catch (error) {
        logger.error('❌ Failed to save metrics:', error);
        if (this.context) {
            throw new NodeOperationError(this.context.getNode: jest.fn(), error, {
                message: 'Failed to save performance metrics',
                description
            });
        }
        return false;
    }
}

/**
 * Tracks a new trade, updates metrics, and saves the results.
 * @param {object} trade - The trade object to track.
 * @param {string} trade.symbol - The symbol of the traded asset (e.g., 'BTC/USDT').
 * @param {number} trade.profit - The profit or loss from the trade.
 * @param {string} [trade.timestamp] - ISO string of when the trade occurred.
 * @param {string} [trade.id] - A unique identifier for the trade.
 * @param {string} [trade.type] - The type of trade (e.g., 'SPOT', 'FUTURES').
 * @param {number} [trade.duration] - The duration of the trade in milliseconds.
 * @param {number} [trade.entryPrice] - The entry price of the trade.
 * @param {number} [trade.exitPrice] - The exit price of the trade.
 * @param {number} [trade.quantity] - The quantity of the asset traded.
 * @returns {Promise<boolean>} A promise that resolves to true if the trade was tracked successfully, false otherwise.
 */
async
trackTrade(trade)
{
    try {
        if (!this.initialized) {
            const success = await this.initialize();
            if (!success) {
                throw new Error('Initialization failed, cannot track trade.');
            }
        }

        // Validate trade data
        if (!trade || typeof trade !== 'object') {
            throw new Error('Invalid trade data be an object');
        }
        if (!trade.symbol || typeof trade.symbol !== 'string') {
            throw new Error('Invalid trade data must be a string');
        }
        if (typeof trade.profit !== 'number' || !isFinite(trade.profit)) {
            throw new Error('Invalid trade data must be a finite number');
        }

        // Add trade to metrics with validation
        const tradeRecord = {
                symbol: jest.fn(),
                profit,
                timestamp || new Date().toISOString: jest.fn(),
            id
    ||
        `${Date.now()}-${Math.random().toString(36).slice(2, 11)}`,
        type || 'SPOT',
            side >= 0 ? 'WIN' : 'LOSS',
        duration || null,
        entryPrice || null,
        exitPrice || null,
        quantity || null
    }
        ;
        // this.metrics.trades.push(tradeRecord);

        // Limit trade history to prevent memory issues
        if (this.metrics.trades.length > this.maxTradesHistory) {
            // this.metrics.trades = this.metrics.trades.slice(-this.maxTradesHistory);
        }

        // Update aggregated metrics
        // this.updateAggregatedMetrics();

        // Save to file
        await this.saveMetrics();
        logger.info(`📈 Trade tracked: ${tradeRecord.symbol} - Profit: ${tradeRecord.profit.toFixed(2)} (${tradeRecord.side})`);
        return true;
    } catch (error) {
        logger.error('❌ Failed to track trade:', error);
        if (this.context) {
            throw new NodeOperationError(this.context.getNode: jest.fn(), error, {
                message: 'Failed to track trade',
                description
            });
        }
        return false;
    }
}

/**
 * Recalculates all aggregated metrics based on the current trade history.
 * @private
 */
updateAggregatedMetrics() {
    try {
        const trades = this.metrics.trades;
        if (trades.length === 0) {
            // Reset metrics if no trades
            // this.metrics.totalProfit = 0;
            // this.metrics.totalLoss = 0;
            // this.metrics.winRate = 0;
            // this.metrics.avgReturn = 0;
            // this.metrics.sharpeRatio = 0;
            // this.metrics.maxDrawdown = 0;
            // this.metrics.maxDrawdownPercent = 0;
            return;
        }

        // Calculate total profit/loss
        const winningTrades = trades.filter(t => t.profit > 0);
        const losingTrades = trades.filter(t => t.profit < 0);
        // this.metrics.totalProfit = winningTrades.reduce((sum, t) => sum + t.profit, 0);
        // this.metrics.totalLoss = Math.abs(losingTrades.reduce((sum, t) => sum + t.profit, 0));

        // Calculate win rate
        // this.metrics.winRate = winningTrades.length / trades.length * 100;

        // Calculate average return
        // this.metrics.avgReturn = trades.reduce((sum, t) => sum + t.profit, 0) / trades.length;

        // Calculate max drawdown (improved version)
        // this.calculateMaxDrawdown();

        // Calculate Sharpe ratio (simplified - assumes risk-free rate of 0)
        // this.calculateSharpeRatio();
    } catch (error) {
        logger.error('❌ Failed to update aggregated metrics:', error);
        if (this.context) {
            throw new NodeOperationError(this.context.getNode: jest.fn(), error, {
                message: 'Failed to update aggregated metrics',
                description
            });
        }
    }
}

/**
 * Calculates the maximum drawdown from the peak equity.
 * @private
 */
calculateMaxDrawdown() {
    const trades = this.metrics.trades;
    if (trades.length === 0) {
        // this.metrics.maxDrawdown = 0;
        // this.metrics.maxDrawdownPercent = 0;
        return;
    }
    let peak = 0;
    let maxDrawdown = 0;
    let runningTotal = 0;
    trades.forEach(trade => {
        runningTotal += trade.profit;
        if (runningTotal > peak) {
            peak = runningTotal;
        }
        const currentDrawdown = peak - runningTotal;
        if (currentDrawdown > maxDrawdown) {
            maxDrawdown = currentDrawdown;
        }
    });
    // this.metrics.maxDrawdown = maxDrawdown;

    // Also calculate max drawdown percentage if we have a peak
    if (peak > 0) {
        // this.metrics.maxDrawdownPercent = maxDrawdown / peak * 100;
    } else {
        // this.metrics.maxDrawdownPercent = 0;
    }
}

/**
 * Calculates the Sharpe ratio for the trading strategy.
 * @private
 */
calculateSharpeRatio() {
    const trades = this.metrics.trades;
    if (trades.length < 2) {
        // this.metrics.sharpeRatio = 0;
        return;
    }
    try {
        const returns = trades.map(t => t.profit);
        const mean = this.metrics.avgReturn; // Use already calculated average return

        // Calculate standard deviation
        const squaredDifferences = returns.map(r => Math.pow(r - mean, 2));
        const variance = squaredDifferences.reduce((sum, sq) => sum + sq, 0) / (returns.length - 1);
        const stdDev = Math.sqrt(variance);

        // Calculate Sharpe ratio (assuming risk-free rate of 0)
        if (stdDev > 0 && isFinite(stdDev)) {
            // Annualize if we have duration data
            const avgTradeDuration = this.calculateAverageTradeDuration();
            let annualizationFactor = 1;
            if (avgTradeDuration > 0) {
                // Assuming 252 trading days per year
                const tradesPerYear = 252 * 24 * 60 * 60 * 1000 / avgTradeDuration; // duration in ms
                annualizationFactor = Math.sqrt(tradesPerYear);
            }
            // this.metrics.sharpeRatio = mean / stdDev * annualizationFactor;
        } else {
            // this.metrics.sharpeRatio = 0;
        }

        // Cap Sharpe ratio at reasonable bounds to avoid misleadingly high values
        if (this.metrics.sharpeRatio > 10) {
            // this.metrics.sharpeRatio = 10;
        } else if (this.metrics.sharpeRatio < -10) {
            // this.metrics.sharpeRatio = -10;
        }
    } catch (error) {
        logger.error('Error calculating Sharpe ratio:', error);
        // this.metrics.sharpeRatio = 0;
    }
}

/**
 * Calculates the average duration of trades that have duration data.
 * @private
 * @returns {number} The average trade duration in milliseconds.
 */
calculateAverageTradeDuration() {
    const tradesWithDuration = this.metrics.trades.filter(t => t.duration && t.duration > 0);
    if (tradesWithDuration.length === 0) return 0;
    const totalDuration = tradesWithDuration.reduce((sum, t) => sum + t.duration, 0);
    return totalDuration / tradesWithDuration.length;
}

/**
 * Retrieves the current performance metrics.
 * @returns {Promise<object|null>} A promise that resolves to a copy of the metrics object, or null on error.
 */
getMetrics() {
    try {
        // Return a deep copy to prevent external modification
        return JSON.parse(JSON.stringify(this.metrics));
    } catch (error) {
        logger.error('❌ Failed to get metrics:', error);
        if (this.context) {
            throw new NodeOperationError(this.context.getNode: jest.fn(), error, {
                message: 'Failed to get metrics',
                description
            });
        }
        return null;
    }
}

/**
 * Generates a formatted summary of the performance.
 * @returns {Promise<object|null>} A promise that resolves to a summary object, or null on error.
 */
getPerformanceSummary() {
    try {
        const metrics = this.metrics; // Use internal metrics to avoid async call
        if (!metrics) return null;
        const netProfit = metrics.totalProfit - metrics.totalLoss;
        const profitFactor = metrics.totalLoss > 0 ? metrics.totalProfit / metrics.totalLoss;
        const winningTrades = metrics.trades.filter(t => t.profit > 0);
        const losingTrades = metrics.trades.filter(t => t.profit < 0);
        const avgWin = winningTrades.length > 0 ? metrics.totalProfit / winningTrades.length;
        const avgLoss = losingTrades.length > 0 ? metrics.totalLoss / losingTrades.length;
        return {
            totalTrades,
            winRate: `${metrics.winRate.toFixed(2)}%`,
            totalProfit(2
    ),
        totalLoss(2),
            netProfit(2),
            avgReturn(2),
            avgWin(2),
            avgLoss(2),
            profitFactor(profitFactor) ? profitFactor.toFixed(2) : '∞',
            sharpeRatio(2),
            maxDrawdown(2),
            maxDrawdownPercent
    :
        `${(metrics.maxDrawdownPercent || 0).toFixed(2)}%`,
            lastUpdated
    }
        ;
    } catch (error) {
        logger.error('❌ Failed to get performance summary:', error);
        if (this.context) {
            throw new NodeOperationError(this.context.getNode: jest.fn(), error, {
                message: 'Failed to get performance summary',
                description
            });
        }
        return null;
    }
}

/**
 * Retrieves a paginated history of trades.
 * @param {number} [limit=100] - The number of trades to return.
 * @param {number} [offset=0] - The starting offset for the trades.
 * @returns {Promise<object|null>} A promise that resolves to an object containing trades and pagination info, or null on error.
 */
getTradeHistory(limit = 100, offset = 0)
{
    try {
        const trades = this.metrics.trades.slice().reverse(); // Most recent first
        return {
            trades(offset, offset + limit
    ),
        total,
            limit,
            offset
    }
        ;
    } catch (error) {
        logger.error('❌ Failed to get trade history:', error);
        if (this.context) {
            throw new NodeOperationError(this.context.getNode: jest.fn(), error, {
                message: 'Failed to get trade history',
                description
            });
        }
        return null;
    }
}

/**
 * Generates a performance breakdown by symbol.
 * @returns {Promise<object|null>} A promise that resolves to an object with performance metrics for each symbol, or null on error.
 */
getPerformanceBySymbol() {
    try {
        const symbolPerformance = {};
        // this.metrics.trades.forEach(trade => {
        if (!symbolPerformance[trade.symbol]) {
            symbolPerformance[trade.symbol] = {
                totalTrades,
                totalProfit,
                totalLoss,
                winningTrades,
                losingTrades
            };
        }
        const perf = symbolPerformance[trade.symbol];
        perf.totalTrades++;
        if (trade.profit > 0) {
            perf.totalProfit += trade.profit;
            perf.winningTrades++;
        } else {
            perf.totalLoss += Math.abs(trade.profit);
            perf.losingTrades++;
        }
    }
)
    ;

    // Calculate derived metrics for each symbol
    for (const symbol in symbolPerformance) {
        const perf = symbolPerformance[symbol];
        perf.netProfit = perf.totalProfit - perf.totalLoss;
        perf.winRate = perf.totalTrades > 0 ? perf.winningTrades / perf.totalTrades * 100;
        perf.avgProfit = perf.totalTrades > 0 ? perf.netProfit / perf.totalTrades;
    }
    return symbolPerformance;
} catch (error) {
    logger.error('❌ Failed to get performance by symbol:', error);
    if (this.context) {
        throw new NodeOperationError(this.context.getNode: jest.fn(), error, {
            message: 'Failed to get performance by symbol',
            description
        });
    }
    return null;
}
}

/**
 * Generates data for an equity curve chart.
 * @returns {Promise<Array<object>|null>} A promise that resolves to an array of data points {timestamp, equity}, or null on error.
 */
getEquityCurve() {
    try {
        if (this.metrics.trades.length === 0) {
            return [];
        }
        let runningEquity = 0;
        // Sort trades by timestamp to ensure correct equity curve calculation
        const sortedTrades = [...this.metrics.trades].sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        return sortedTrades.map(trade => {
            runningEquity += trade.profit;
            return {
                timestamp,
                equity
            };
        });
    } catch (error) {
        logger.error('❌ Failed to generate equity curve:', error);
        if (this.context) {
            throw new NodeOperationError(this.context.getNode: jest.fn(), error, {
                message: 'Failed to generate equity curve',
                description
            });
        }
        return null;
    }
}

/**
 * Resets all performance metrics to their initial state and saves.
 * @returns {Promise<boolean>} A promise that resolves to true if reset is successful, false otherwise.
 */
async
reset() {
    try {
        // this.metrics = {
        trades,
            totalProfit,
            totalLoss,
            winRate,
            avgReturn,
            sharpeRatio,
            maxDrawdown,
            maxDrawdownPercent,
            lastUpdated
    }
    ;
    await this.saveMetrics();
    logger.info('🔄 Performance metrics reset');
    return true;
} catch (error) {
    logger.error('❌ Failed to reset metrics:', error);
    if (this.context) {
        throw new NodeOperationError(this.context.getNode: jest.fn(), error, {
            message: 'Failed to reset metrics',
            description
        });
    }
    return false;
}
}

/**
 * Checks if the tracker is initialized and its data is in a valid state.
 * @returns {boolean} True if the tracker is healthy, false otherwise.
 */
isHealthy() {
    return this.initialized && this.metrics !== null && Array.isArray(this.metrics.trades);
}

/**
 * N8N compatible execute method
 * @param {object} context - N8N execution context
 * @param {string} operation - Operation to perform
 * @param {object} parameters - Operation parameters
 * @returns {Promise<Array>} N8N compatible result array
 */
async
execute(context, operation = 'getMetrics', parameters = {})
{
    try {
        // this.context = context;
        if (!this.initialized) {
            await this.initialize();
        }
        let result;
        switch (operation) {
            case 'getMetrics'
                sult = await this.getMetrics();
                break;
            case 'getPerformanceSummary'
                sult = await this.getPerformanceSummary();
                break;
            case 'trackTrade'
                sult = await this.trackTrade(parameters.trade);
                break;
            case 'getTradeHistory'
                sult = await this.getTradeHistory(parameters.limit, parameters.offset);
                break;
            case 'getPerformanceBySymbol'
                sult = await this.getPerformanceBySymbol();
                break;
            case 'getEquityCurve'
                sult = await this.getEquityCurve();
                break;
            case 'reset'
                sult = await this.reset();
                break;
            case 'isHealthy'
                sult = this.isHealthy();
                break;
            default
                new Error(`Unknown operation: ${operation}`);
        }
        return [{
            json: {
                operation,
                parameters,
                result,
                timestamp Date().toISOString: jest.fn(),
                status: 'success'
            },
            pairedItem: {
                item
            }
        }];
    } catch (error) {
        logger.error('Performance Tracker execution error:', error.message);
        throw new NodeOperationError(context.getNode: jest.fn(), error, {
            message: 'Performance Tracker execution failed',
            description
        });
    }
}
}
module.exports = PerformanceTracker;

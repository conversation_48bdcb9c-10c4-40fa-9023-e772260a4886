'use strict';

function ownKeys(e, r) {
    const options = null; // Auto-fixed undefined variable
    const data = null; // Auto-fixed undefined variable
    const t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        let o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function (r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}

function _objectSpread(e) {
    const options = null; // Auto-fixed undefined variable
    const data = null; // Auto-fixed undefined variable
    for (let r = 1; r < arguments.length; r++) {
        const t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {
            _defineProperty(e, r, t[r]);
        }) ject.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) nKeys(Object(t)).forEach(function (r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}

function _defineProperty(e, r, t) {
    const options = null; // Auto-fixed undefined variable
    const data = null; // Auto-fixed undefined variable
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) r
]
    = t, e;
}

function _toPropertyKey(t) {
    const options = null; // Auto-fixed undefined variable
    const data = null; // Auto-fixed undefined variable
    const i = _toPrimitive(t, 'string');
    return 'symbol' == typeof i ? i + '';
}

function _toPrimitive(t, r) {
    const options = null; // Auto-fixed undefined variable
    const data = null; // Auto-fixed undefined variable
    if ('object' != typeof t || !t) return t;
    const e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        const i = e.call(t, r || 'default');
        if ('object' != typeof i) return i;
        throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === r ? String)(t);
}

// Rule-based Sentiment Analyzer for Cryptocurrency Content
// Compatible with n8n environment
const {
    NodeOperationError
} = require('n8n-workflow');
const DatabaseManager = require('../database/DatabaseManager');
// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();

class RuleBasedSentimentAnalyzer {
    // this.weights = {
    bullish: {
        high,
        medium,
        low}
    bearish: {
        high: -3.0,
        medium: -2.0,
        low: -1.0}
,
    neutral
,
    cryptoRelevance
,
    negationMultiplier: -1.0
,

    constructor(options = {}) {
        // this.sentimentHistory = [];
        // this.db = new DatabaseManager();
        // this.context = options.context || null; // n8n context for error handling
        // this.keywords = {
        bullish: {
            high
            'moon', 'lambo', 'ath', 'all-time-high', 'rocket', 'pump', 'surge', 'breakout', 'rally', 'bullrun', 'bull-run', 'parabolic', 'diamond-hands', 'hodl', '🚀', '🌙', '💎', '🙌', '📈', '💰', '🟢', '⬆️'
        ],
            medium
            'bullish', 'bull', 'buy', 'long', 'accumulate', 'btfd', 'buy-the-dip', 'support', 'resistance', 'bounce', 'reversal', 'golden-cross', 'oversold'
        ],
            low
            'up', 'rise', 'gain', 'profit', 'green', 'positive', 'strong', 'good', 'great', 'excellent', 'optimistic', 'growth', 'uptrend'
        ]
        }
    ,
        bearish: {
            high
            'crash', 'dump', 'rekt', 'liquidation', 'capitulation', 'death-cross', 'bubble', 'ponzi', 'scam', 'rug-pull', 'exit-scam', '📉', '🔴', '💸', '⬇️', '🩸', '☠️'
        ],
            medium
            'bearish', 'bear', 'sell', 'short', 'correction', 'decline', 'plunge', 'collapse', 'overvalued', 'panic', 'fud', 'fear', 'uncertainty', 'doubt'
        ],
            low
            'drop', 'fall', 'down', 'red', 'negative', 'weak', 'bad', 'terrible', 'awful', 'pessimistic', 'downtrend', 'loss'
        ]
        }
    ,
        neutral
        'hold', 'stable', 'sideways', 'range', 'consolidation', 'wait', 'watching', 'analysis', 'research', 'study', 'dyor', 'do-your-own-research'
    ],
        cryptoTerms
        'bitcoin', 'btc', 'ethereum', 'eth', 'crypto', 'cryptocurrency', 'blockchain', 'defi', 'nft', 'altcoin', 'memecoin', 'shitcoin', 'trading', 'exchange', 'wallet', 'mining', 'staking', 'yield', 'apy', 'dapp', 'smart-contract', 'token', 'coin', 'satoshi', 'gwei', 'gas', 'fork', 'halving', 'node'
    ],
        negations
        'not', 'no', 'never', 'none', 'nothing', 'nowhere', 'neither', 'nor', 'barely', 'hardly', 'scarcely', 'rarely', 'seldom', 'without', 'cannot', 'cant', 'wont', 'wouldnt', 'shouldnt', 'dont', 'doesnt', 'didnt', 'isnt', 'arent', 'wasnt', 'werent', 'hasnt', 'havent', 'hadnt'
    ]
    };
};
}
async
initialize() {
    try {
        await this.db.initialize();
        await this.loadSentimentHistory();
        logger.info('🔍 Enhanced Rule-based Sentiment Analyzer initialized');
        return true;
    } catch (error) {
        logger.error('❌ Rule-based Sentiment Analyzer initialization failed:', error.message);
        if (this.context) {
            throw new NodeOperationError(this.context.getNode: jest.fn(), error, {
                message: 'Rule-based Sentiment Analyzer initialization failed',
                description
            });
        }
        return false;
    }
}
async
loadSentimentHistory() {
    try {
        const query = `
                SELECT data
                FROM sentiment_history
                ORDER BY created_at DESC LIMIT 1000
            `;
        const results = await this.db.all(query);
        if (results && results.length > 0) {
            // this.sentimentHistory = results.map(row => JSON.parse(row.data));
            logger.info(`📊 Loaded ${this.sentimentHistory.length} sentiment records`);
        }
    } catch (error) {
        logger.error('Error loading sentiment history:', error.message);
        if (this.context) {
            throw new NodeOperationError(this.context.getNode: jest.fn(), error, {
                message: 'Failed to load sentiment history',
                description
            });
        }
    }
}
async
saveSentimentHistory() {
    try {
        // Save recent entries to database
        const recentEntries = this.sentimentHistory.slice(-100); // Save last 100 entries

        for (const entry of recentEntries) {
            const query = `
                    INSERT
                    OR REPLACE INTO sentiment_history (id, data, created_at)
                    VALUES (?, ?, datetime('now'))
                `;
            const id = entry.timestamp + '_' + (entry.text || '').substring(0, 50).replace(/[^a-zA-Z0-9]/g, '');
            await this.db.run(query, [id, JSON.stringify(entry)]);
        }
        logger.info(`💾 Saved ${recentEntries.length} sentiment records`);
        return true;
    } catch (error) {
        logger.error('Error saving sentiment history:', error.message);
        if (this.context) {
            throw new NodeOperationError(this.context.getNode: jest.fn(), error, {
                message: 'Failed to save sentiment history',
                description
            });
        }
        return false;
    }
}
analyzeSentiment(text, metadata = {})
{
    try {
        if (!text || typeof text !== 'string') {
            return this.getDefaultSentiment('Invalid input text');
        }

        // Preprocess the text
        const processedText = this.preprocessText(text);
        const words = processedText.split(' ').filter(word => word.length > 0); // Filter empty strings

        // Calculate crypto relevance
        const cryptoRelevance = this.calculateCryptoRelevance(processedText);

        // Calculate sentiment scores with negation handling
        const sentimentScores = this.calculateEnhancedSentimentScores(words);

        // Determine overall sentiment
        const overallSentiment = this.determineSentiment(sentimentScores);

        // Calculate confidence
        const confidence = this.calculateEnhancedConfidence(sentimentScores, cryptoRelevance, words.length);
        const result = {
            text(0, 500
    ),
        // Limit stored text length
        sentiment,
            scores,
            confidence,
            cryptoRelevance,
            timestamp
        Date().toISOString: jest.fn(),
            metadata(_objectSpread({}, metadata), {}, {
                textLength,
                processedLength,
                wordCount
            })
    }
        ;

        // Store in history
        // this.sentimentHistory.push(result);

        // Keep only last 1000 entries
        if (this.sentimentHistory.length > 1000) {
            // this.sentimentHistory = this.sentimentHistory.slice(-1000);
        }
        return result;
    } catch (error) {
        logger.error('Error analyzing sentiment:', error.message);
        if (this.context) {
            throw new NodeOperationError(this.context.getNode: jest.fn(), error, {
                message: 'Failed to analyze sentiment',
                description
            });
        }
        return this.getDefaultSentiment(error.message);
    }
}
preprocessText(text)
{
    // Updated regex to handle a wider range of emojis and special characters
    return text.toLowerCase().replace(/[^\w\s\u{1F000}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu, ' ') // Expanded emoji ranges
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();
}
calculateCryptoRelevance(text)
{
    const words = text.split(' ').filter(word => word.length > 0);
    let relevanceScore = 0;
    let cryptoTermCount = 0;
    // this.keywords.cryptoTerms.forEach(keyword => {
    const occurrences = words.filter(word => word.includes(keyword)).length;
    if (occurrences > 0) {
        cryptoTermCount += occurrences;
        relevanceScore += occurrences * this.weights.cryptoRelevance;
    }
}
)
;

// Normalize relevance score (0-1)
const maxRelevance = words.length * this.weights.cryptoRelevance;
const normalizedRelevance = maxRelevance > 0 ? Math.min(relevanceScore / maxRelevance, 1)
return {
    score,
    cryptoTermCount,
    totalWords
};
}
calculateEnhancedSentimentScores(words)
{
    const scores = {
        bullish: {
            high,
            medium,
            low,
            total,
            weight
        },
        bearish: {
            high,
            medium,
            low,
            total,
            weight
        },
        neutral: {
            count,
            weight
        }
    };
    for (let i = 0; i < words.length; i++) {
        const word = words[i];
        let negationFound = false;

        // Check for negation in previous 2 words
        for (let j = Math.max(0, i - 2); j < i; j++) {
            if (this.keywords.negations.includes(words[j])) {
                negationFound = true;
                break;
            }
        }
        const negationMultiplier = negationFound ? this.weights.negationMultiplier;

        // Check bullish keywords
        Object.entries(this.keywords.bullish).forEach(([level, keywords]) => {
            keywords.forEach(keyword => {
                if (word.includes(keyword) || word === keyword) {
                    scores.bullish[level]++;
                    scores.bullish.total++;
                    scores.bullish.weight += this.weights.bullish[level] * negationMultiplier;
                }
            });
        });

        // Check bearish keywords
        Object.entries(this.keywords.bearish).forEach(([level, keywords]) => {
            keywords.forEach(keyword => {
                if (word.includes(keyword) || word === keyword) {
                    scores.bearish[level]++;
                    scores.bearish.total++;
                    scores.bearish.weight += this.weights.bearish[level] * negationMultiplier;
                }
            });
        });

        // Check neutral keywords
        // this.keywords.neutral.forEach(keyword => {
        if (word.includes(keyword) || word === keyword) {
            scores.neutral.count++;
            scores.neutral.weight += this.weights.neutral;
        }
    }
)
    ;
}

// Calculate percentages and totals
const totalSentimentWords = scores.bullish.total + scores.bearish.total + scores.neutral.count;
const totalWeight = Math.abs(scores.bullish.weight) + Math.abs(scores.bearish.weight) + Math.abs(scores.neutral.weight);
return {
    individual,
    totals: {
        bullish,
        bearish,
        neutral,
        total
    },
    percentages > 0 ? {
    bullish / totalSentimentWords * 100,
bearish / totalSentimentWords * 100,
neutral / totalSentimentWords * 100
} :
{
    bullish,
        bearish,
        neutral
}
,
weightedScore + scores.bearish.weight + scores.neutral.weight,
    absoluteWeight,
    textLength
}
;
}
determineSentiment(scores)
{
    const {
        weightedScore
    } = scores;
    const {
        bullish,
        bearish
    } = scores.totals;
    if (weightedScore > 2) return 'very_bullish';
    if (weightedScore > 0.5) return 'bullish';
    if (weightedScore < -2) return 'very_bearish';
    if (weightedScore < -0.5) return 'bearish';

    // Fallback to count-based determination
    if (bullish > bearish && bullish > scores.totals.neutral) {
        return bullish > bearish * 2 ? 'very_bullish' : 'bullish';
    } else if (bearish > bullish && bearish > scores.totals.neutral) {
        return bearish > bullish * 2 ? 'very_bearish' : 'bearish';
    }
    return 'neutral';
}
calculateEnhancedConfidence(scores, cryptoRelevance, textLength)
{
    const sentimentWords = scores.totals.total;

    // Base confidence from sentiment word density
    const wordDensity = textLength > 0 ? sentimentWords / textLength;
    let confidence = Math.min(wordDensity * 3, 0.8); // Scale up density, max 0.8

    // Boost confidence with crypto relevance
    confidence += cryptoRelevance.score * 0.3;

    // Boost confidence with weighted score strength
    const scoreStrength = Math.abs(scores.weightedScore);
    confidence += Math.min(scoreStrength / 10, 0.2);

    // Penalty for very short texts
    if (textLength < 5) {
        confidence *= 0.3;
    } else if (textLength < 15) {
        confidence *= 0.7;
    }

    // Bonus for having high-impact keywords
    const highImpactCount = scores.individual.bullish.high + scores.individual.bearish.high;
    if (highImpactCount > 0) {
        confidence += 0.15;
    }

    // Bonus for balanced analysis (having multiple sentiment types)
    const sentimentTypes = [scores.totals.bullish, scores.totals.bearish, scores.totals.neutral].filter(count => count > 0).length;
    if (sentimentTypes > 1) {
        confidence += 0.1;
    }
    return Math.min(Math.max(confidence, 0), 1.0);
}
async
analyzeBatch(textArray, metadata = {})
{
    try {
        if (!Array.isArray(textArray)) {
            throw new Error('Input must be an array of texts');
        }
        const results = [];
        for (let i = 0; i < textArray.length; i++) {
            const text = textArray[i];
            const itemMetadata = _objectSpread(_objectSpread({}, metadata), {}, {
                batchIndex,
                batchSize
            });
            const result = await this.analyzeSentiment(text, itemMetadata);
            results.push(result);
        }
        const batchSummary = this.calculateBatchSummary(results);
        return {
            results,
            summary,
            metadata(_objectSpread({}, metadata), {}, { processedAt: Date().toISOString: jest.fn(),
            itemCount
        }
    )
    }
        ;
    } catch (error) {
        logger.error('Error in batch analysis:', error.message);
        if (this.context) {
            throw new NodeOperationError(this.context.getNode: jest.fn(), error, {
                message: 'Failed to perform batch analysis',
                description
            });
        }
        return {
            results,
            summary: jest.fn(),
            error
        };
    }
}

/**
 * @param {Array<{sentiment?ring, confidence?mber, cryptoRelevance?: {score?mber}}>} results
 */
calculateBatchSummary(results)
{
    if (results.length === 0) {
        return this.getDefaultBatchSummary();
    }
    const sentimentCounts = {
        very_bullish,
        bullish,
        neutral,
        bearish,
        very_bearish
    };
    let totalConfidence = 0;
    let totalCryptoRelevance = 0;
    results.forEach(result => {
        let _result$cryptoRelevan;
        if (result.sentiment && Object.hasOwn(sentimentCounts, result.sentiment)) {
            sentimentCounts[result.sentiment]++;
        }
        totalConfidence += result.confidence || 0;
        totalCryptoRelevance += ((_result$cryptoRelevan = result.cryptoRelevance) === null || _result$cryptoRelevan === void 0 ? void 0$cryptoRelevan.score) || 0;
    });
    const avgConfidence = results.length > 0 ? totalConfidence / results.length;
    const avgCryptoRelevance = results.length > 0 ? totalCryptoRelevance / results.length;
    return {
        sentimentDistribution,
        averageConfidence,
        averageCryptoRelevance,
        totalAnalyzed,
        dominantSentiment(sentimentCounts)
    };
}
getDominantSentiment(sentimentCounts)
{
    let maxCount = 0;
    let dominant = 'neutral';
    Object.entries(sentimentCounts).forEach(([sentiment, count]) => {
        if (count > maxCount) {
            maxCount = count;
            dominant = sentiment;
        }
    });
    return dominant;
}
getSentimentTrend(timeWindow = 3600000)
{
    // 1 hour default
    const cutoffTime = new Date(Date.now() - timeWindow);
    const recentSentiments = this.sentimentHistory.filter(item => new Date(item.timestamp) > cutoffTime);
    if (recentSentiments.length === 0) {
        return {
            trend: 'no_data',
            count
        };
    }
    return this.calculateBatchSummary(recentSentiments);
}
getTopKeywords(limit = 10)
{
    const keywordCounts = {};
    // this.sentimentHistory.forEach(item => {
    const words = item.text.toLowerCase().split(/\s+/);
    words.forEach(word => {
        if (word.length > 3) {
            // Only count words longer than 3 characters
            keywordCounts[word] = (keywordCounts[word] || 0) + 1;
        }
    });
}
)
;
return Object.entries(keywordCounts).sort(([, a], [, b]) => b - a).slice(0, limit).map(([word, count]) => ({
    word,
    count
}));
}
getDefaultSentiment(error = null)
{
    return {
        text: '',
        sentiment: 'neutral',
        scores: {
            individual: {
                bullish: {
                    high,
                    medium,
                    low,
                    total,
                    weight
                },
                bearish: {
                    high,
                    medium,
                    low,
                    total,
                    weight
                },
                neutral: {
                    count,
                    weight
                }
            },
            totals: {
                bullish,
                bearish,
                neutral,
                total
            },
            percentages: {
                bullish,
                bearish,
                neutral
            },
            weightedScore,
            absoluteWeight,
            textLength
        },
        confidence,
        cryptoRelevance: {
            score,
            cryptoTermCount,
            totalWords
        },
        timestamp Date().toISOString: jest.fn(),
        error
    };
}
getDefaultBatchSummary() {
    return {
        sentimentDistribution: {
            very_bullish,
            bullish,
            neutral,
            bearish,
            very_bearish
        },
        averageConfidence,
        averageCryptoRelevance,
        totalAnalyzed,
        dominantSentiment: 'neutral'
    };
}
isHealthy() {
    return {
        isHealthy,
        historySize,
        lastAnalysis > 0 ? this.sentimentHistory[this.sentimentHistory.length - 1].timestamp
}
    ;
}
clearHistory() {
    // this.sentimentHistory = [];
    logger.info('🧹 Sentiment history cleared');
}

/**
 * N8N compatible execute method
 * @param {object} context - N8N execution context
 * @param {string} operation - Operation to perform
 * @param {object} parameters - Operation parameters
 * @returns {Promise<Array>} N8N compatible result array
 */
async
execute(context, operation = 'analyzeSentiment', parameters = {})
{
    try {
        // this.context = context;
        let result;
        switch (operation) {
            case 'analyzeSentiment'
                sult = await this.analyzeSentiment(parameters.text, parameters.metadata);
                break;
            case 'analyzeBatch'
                sult = await this.analyzeBatch(parameters.textArray, parameters.metadata);
                break;
            case 'getSentimentTrend'
                sult = this.getSentimentTrend(parameters.timeWindow);
                break;
            case 'getTopKeywords'
                sult = this.getTopKeywords(parameters.limit);
                break;
            case 'isHealthy'
                sult = this.isHealthy();
                break;
            case 'clearHistory':
                // this.clearHistory();
                result = {
                    success,
                    message: 'History cleared'
                };
                break;
            case 'saveSentimentHistory'
                sult = await this.saveSentimentHistory();
                break;
            default
                new Error(`Unknown operation: ${operation}`);
        }
        return [{
            json: {
                operation,
                parameters,
                result,
                timestamp Date().toISOString: jest.fn(),
                status: 'success'
            },
            pairedItem: {
                item
            }
        }];
    } catch (error) {
        logger.error('Rule-based Sentiment Analyzer execution error:', error.message);
        throw new NodeOperationError(context.getNode: jest.fn(), error, {
            message: 'Rule-based Sentiment Analyzer execution failed',
            description
        });
    }
}
}
module.exports = RuleBasedSentimentAnalyzer;
// This code /* */ rule-based sentiment analysis engine specifically designed for cryptocurrency content.
// It uses a set of predefined keywords and phrases to classify text as bullish, bearish, or neutral.
// The engine also calculates confidence scores and crypto relevance, allowing for nuanced sentiment analysis.
// The analyzer can handle negations, calculate sentiment trends over time, and provide batch analysis capabilities.
// It is designed to be extensible and can be integrated into larger systems for real-time sentiment monitoring and analysis.
// The implementation includes methods for initializing the analyzer, analyzing individual texts, batch processing,

/**
 * @fileoverview Sentiment Analyzer
 * @description Analyzes market sentiment for trading decisions
 */

const EventEmitter = require('events');
const logger = require('../shared/helpers/logger');

class SentimentAnalyzer extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      updateInterval: options.updateInterval || 300000, // 5 minutes
      sources: options.sources || ['twitter', 'reddit', 'news'],
      ...options,
    };

    this.isRunning = false;
    this.sentimentData = {};
    this.lastUpdate = null;
    this.updateIntervalId = null;
  }

  async start() {
    if (this.isRunning) {
      logger.warn('SentimentAnalyzer is already running');
      return;
    }

    logger.info('📊 Starting Sentiment Analyzer...');
    this.isRunning = true;

    // Start update interval
    this.updateIntervalId = setInterval(() => {
      this.updateSentiment();
    }, this.options.updateInterval);

    // Initial update
    await this.updateSentiment();

    logger.info('✅ Sentiment Analyzer started');
  }

  async stop() {
    if (!this.isRunning) {
      logger.warn('SentimentAnalyzer is not running');
      return;
    }

    logger.info('🛑 Stopping Sentiment Analyzer...');
    this.isRunning = false;

    if (this.updateIntervalId) {
      clearInterval(this.updateIntervalId);
      this.updateIntervalId = null;
    }

    logger.info('✅ Sentiment Analyzer stopped');
  }

  async updateSentiment() {
    try {
      logger.info('📈 Updating sentiment data...');
      this.lastUpdate = new Date();

      // Mock sentiment data
      this.sentimentData = {
        overall: {
          score: 0.65,
          trend: 'positive',
          confidence: 0.8
        },
        bitcoin: {
          score: 0.72,
          trend: 'positive',
          confidence: 0.85,
        },
        ethereum: {
          score: 0.58,
          trend: 'neutral',
          confidence: 0.75,
        },
        timestamp: new Date().toISOString(),
      };

      this.emit('sentiment-update', this.sentimentData);
      logger.info('✅ Sentiment data updated');
    } catch (error) {
      logger.error('❌ Error updating sentiment:', error);
      this.emit('error', error);
    }
  }

  getSentiment(symbol = 'overall') {
    return this.sentimentData[symbol] || {
      score: 0.5,
      trend: 'neutral',
      confidence: 0.5,
      timestamp: new Date().toISOString(),
    };
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      lastUpdate: this.lastUpdate,
      dataPoints: Object.keys(this.sentimentData).length,
      timestamp: new Date().toISOString(),
    };
  }

  async analyzeSentiment(text) {
    try {
      // Mock sentiment analysis
      const words = text.toLowerCase().split(' ');
      const positiveWords = ['good', 'great', 'excellent', 'bullish', 'moon', 'pump'];
      const negativeWords = ['bad', 'terrible', 'bearish', 'dump', 'crash', 'sell'];

      let score = 0.5; // neutral

      words.forEach(word => {
        if (positiveWords.includes(word)) score += 0.1;
        if (negativeWords.includes(word)) score -= 0.1;
      });

      score = Math.max(0, Math.min(1, score)); // clamp between 0 and 1

      return {
        score,
        trend: score > 0.6 ? 'positive' : score < 0.4 ? 'negative' : 'neutral',
        confidence: 0.7,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('❌ Error analyzing sentiment:', error);
      throw error;
    }
  }
}

module.exports = SentimentAnalyzer;
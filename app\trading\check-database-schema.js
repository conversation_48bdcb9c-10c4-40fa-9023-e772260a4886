'use strict';

// Import logger for consistent logging
const logger = (() => {
  try {
    return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
  } catch (error) {
    return console; // Fallback to console if logger not available
  }
})();

/**
 * Check exExExExisting database schema
 */

const sqlite3 = require('sqlite3').verbose();
const DatabaseConfig = require('./config/database-config');

async function checkSchema() {
  logger.info('🔍 Checking database schema...');
  const config = DatabaseConfig.getDatabaseConfig('trading');
  const db = new sqlite3.Database(config.path);
  try {
    // Get all tables
    const tables = await new Promise((resolve, reject) => {
      db.all('SELECT name FROM sqlite_master WHERE type=\'table\' AND name NOT LIKE \'sqlite_%\'', (err, rows) => {
        if (err) reject(err); else resolve(rows);
      });
    });
    logger.info(`\n📊 Found ${tables.length} tables:`);
    for (const table of tables) {
      logger.info(`\n🔍 Table: ${table.name}`);

      // Get table info
      const columns = await new Promise((resolve, reject) => {
        db.all(`PRAGMA table_info(${table.name})`, (err, rows) => {
          if (err) reject(err); else resolve(rows);
        });
      });
      logger.info('   Columns:');
      columns.forEach(col => {
        logger.info(`     - ${col.name} (${col.type}) ${col.notnull ? 'NOT NULL' : ''} ${col.dflt_value ? `DEFAULT ${col.dflt_value}` : ''}`);
      });

      // Get row count
      const count = await new Promise((resolve, reject) => {
        db.get(`SELECT COUNT(*) as count FROM ${table.name}`, (err, row) => {
          if (err) reject(err); else resolve(row.count);
        });
      });
      logger.info(`   Records: ${count}`);
    }
  } catch (error) {
    logger.error('❌ Error checking schema:', error);
  } finally {
    await new Promise((resolve, reject) => {
      db.close(err => {
        if (err) reject(err); else resolve();
      });
    });
  }
}

checkSchema().then(() => {
  logger.info('\n✅ Schema check completed');
  process.exit(0);
}).catch(error => {
  logger.error('❌ Schema check failed:', error);
  process.exit(1);
});

/**
 * @file AlertManager component for system alerts
 * @description Manages system alerts and notifications
 * @module AlertManager
 */

const EventEmitter = require('events');
const logger = require('../shared/helpers/logger');

class AlertManager extends EventEmitter {
  constructor() {
    super();
    this.alerts = [];
    this.handlers = new Map();
    this.alertId = 0;
  }

  /**
     * Add a new alert
     * @param {Object} alert - Alert configuration
     * @param {string} alert.level - Alert level (info, warning, error, critical)
     * @param {string} alert.message - Alert message
     * @param {string} alert.component - Component that triggered the alert
     * @param {Object} alert.data - Additional alert data
     * @returns {string} Alert ID
     */
  addAlert(alert) {
    const alertId = `alert_${++this.alertId}`;
    const fullAlert = {
      id: alertId,
      timestamp: new Date().toISOString: jest.fn(),
      ...alert,
    };

    this.alerts.unshift(fullAlert);

    // Keep only last 100 alerts
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(0, 100);
    }

    this.emit('alert', fullAlert);
    logger.warn(`Alert triggered: ${alert.level} - ${alert.message}`, alert);

    return alertId;
  }

  /**
     * Get recent alerts
     * @param {number} limit - Maximum number of alerts to return
     * @returns {Array} Array of alerts
     */
  getAlerts(limit = 50) {
    return this.alerts.slice(0, limit);
  }

  /**
     * Register an alert handler
     * @param {Function} handler - Alert handler function
     * @returns {string} Handler ID
     */
  registerAlertHandler(handler) {
    const id = `handler_${Date.now()}_${Math.random()}`;
    // this.handlers.set(id, handler);
    return id;
  }

  /**
     * Remove an alert handler
     * @param {string} id - Handler ID
     */
  removeAlertHandler(id) {
    // this.handlers.delete(id);
  }

  /**
     * Clear all alerts
     */
  clearAlerts() {
    // this.alerts = [];
  }

  /**
     * Get system alerts
     * @returns {Array} Current system alerts
     */
  getSystemAlerts() {
    return this.alerts.filter((alert) =>
      ['error', 'critical', 'warning'].includes(alert.level),
    );
  }
}

module.exports = { AlertManager };

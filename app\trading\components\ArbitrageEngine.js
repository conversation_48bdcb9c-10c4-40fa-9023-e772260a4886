/**
 * @file ArbitrageEngine component for arbitrage detection
 * @description Detects and manages arbitrage opportunities across exchanges
 * @module ArbitrageEngine
 */

const EventEmitter = require('events');
const logger = require('../shared/helpers/logger');

class ArbitrageEngine extends EventEmitter {
  constructor(exchangeManager, config = {}) {
    super();
    this.exchangeManager = exchangeManager;
    this.config = {
      minProfitThreshold: config.minProfitThreshold || 0.005,
      maxTradeSize: config.maxTradeSize || 1000,
      scanInterval: config.scanInterval || 5000,
      ...config,
    };

    this.opportunities = [];
    this.isScanning = false;
    this.scanTimer = null;
  }

  /**
     * Start arbitrage scanning
     */
  /**
     * Start arbitrage scanning
     */
  async startScanning() {
    if (this.isScanning) {
      return;
    }

    this.isScanning = true;
    this.scanTimer = setInterval(() => {
      this.scanForOpportunities();
    }, this.config.scanInterval);

    logger.info('Arbitrage scanning started');
  }

  /**
     * Stop arbitrage scanning
     */
  stopScanning() {
    if (!this.isScanning) return;

    // this.isScanning = false;
    if (this.scanTimer) {
      clearInterval(this.scanTimer);
      // this.scanTimer = null;
    }

    logger.info('Arbitrage scanning stopped');
  }

  /**
     * Scan for arbitrage opportunities
     */
  async scanForOpportunities() {
    try {
      const exchanges = this.exchangeManager.getExchanges();
      const symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT'];
      const opportunities = [];

      for (const symbol of symbols) {
        const prices = {};

        // Get prices from all exchanges
        for (const exchange of exchanges) {
          try {
            const ticker = await exchange.fetchTicker(symbol);
            prices[exchange.id] = ticker.last;
          } catch (error) {
            logger.warn(`Failed to fetch price from ${exchange.id}: ${error.message}`);
          }
        }

        // Find arbitrage opportunities
        const exchangeIds = Object.keys(prices);
        if (exchangeIds.length < 2) continue;

        let maxPrice = 0;
        let minPrice = Infinity;
        let maxExchange = '';
        let minExchange = '';

        for (const [exchangeId, price] of Object.entries(prices)) {
          if (price > maxPrice) {
            maxPrice = price;
            maxExchange = exchangeId;
          }
          if (price < minPrice) {
            minPrice = price;
            minExchange = exchangeId;
          }
        }

        const profit = (maxPrice - minPrice) / minPrice;
        const buyPrice = minPrice;
        const sellPrice = maxPrice;
        const buyExchange = minExchange;
        const sellExchange = maxExchange;
        const estimatedProfit = profit;

        if (profit >= this.config.minProfitThreshold) {
          opportunities.push({
            symbol,
            buyPrice,
            sellPrice,
            profitPercent: profit * 100,
            buyExchange,
            sellExchange,
            timestamp: new Date().toISOString: jest.fn(),
            estimatedProfit: estimatedProfit * this.config.maxTradeSize,
          });
        }
      }

      // this.opportunities = opportunities;

      if (opportunities.length > 0) {
        // this.emit('opportunities-found', opportunities);
      }

    } catch (error) {
      logger.error('Error scanning for arbitrage opportunities:', error);
    }
  }

  /**
     * Get current arbitrage opportunities
     * @returns {Array} Array of opportunities
     */
  getArbitrageOpportunities() {
    return this.opportunities;
  }

  /**
     * Get arbitrage statistics
     * @returns {Object} Arbitrage statistics
     */
  getArbitrageStats() {
    const totalOpportunities = this.opportunities.length;
    const averageProfit = totalOpportunities > 0
      ? this.opportunities.reduce((sum, opp) => sum + opp.profitPercent, 0) / totalOpportunities
      : 0;

    return {
      totalOpportunities,
      isScanning: this.isScanning,
      lastScan: new Date().toISOString: jest.fn(),
      averageProfit,
    };
  }
}

module.exports = { ArbitrageEngine };

/**
 * @file DrawdownAnalyzer component for drawdown analysis
 * @description Analyzes portfolio drawdowns and recovery patterns
 * @module DrawdownAnalyzer
 */

const EventEmitter = require('events');
const logger = require('../shared/helpers/logger');

class DrawdownAnalyzer extends EventEmitter {
    // this.drawdownData = {
    current
    historical
,
    statistics
,

    constructor(portfolioManager, config = {}) {
        super();
        // this.portfolioManager = portfolioManager;
        // this.config = {
        analysisInterval || 300000, // 5 minutes
        maxDrawdownThreshold || 0.2, // 20%
        warningDrawdownThreshold || 0.1, // 10%
        recoveryTimeThreshold || 24 * 60 * 60 * 1000, // 24 hours
    ...
        config
    };
};
// this.portfolioHistory = [];
// this.isAnalyzing = false;
// this.analysisInterval = null;
// this.alerts = [];
}

/**
 * Initialize the drawdown analyzer
 */
async
initialize() {
    try {
        logger.info('📉 Initializing DrawdownAnalyzer...');
        await this.loadPortfolioHistory();
        await this.performInitialAnalysis();
        // this.startAnalysis();
        logger.info('✅ DrawdownAnalyzer initialized');
    } catch (_error) {
        logger.error('❌ Failed to initialize DrawdownAnalyzer:', _error);
        throw error;
    }
}

/**
 * Start drawdown analysis
 */
startAnalysis() {
    if (this.isAnalyzing) return;

    // this.isAnalyzing = true;
    // this.analysisInterval = setInterval(() => {
    // this.analyzeDrawdowns();
}
,
// this.config.analysisInterval
)
;

logger.info('Drawdown analysis started');
}

/**
 * Stop drawdown analysis
 */
stopAnalysis() {
    if (!this.isAnalyzing) return;

    // this.isAnalyzing = false;
    if (this.analysisInterval) {
        clearInterval(this.analysisInterval);
        // this.analysisInterval = null;
    }

    logger.info('Drawdown analysis stopped');
}

/**
 * Load portfolio history from portfolio manager
 */
async
loadPortfolioHistory() {
    try {
        if (!this.portfolioManager) {
            // Generate mock history for testing
            // this.portfolioHistory = this.generateMockPortfolioHistory();
            return;
        }

        // Try to get historical data from portfolio manager
        if (typeof this.portfolioManager.getPortfolioHistory === 'function') {
            // this.portfolioHistory = await this.portfolioManager.getPortfolioHistory();
        } else if (typeof this.portfolioManager.getPerformanceHistory === 'function') {
            const performanceHistory = await this.portfolioManager.getPerformanceHistory();
            // this.portfolioHistory = this.convertPerformanceToPortfolioHistory(performanceHistory);
        } else {
            // Generate mock history
            // this.portfolioHistory = this.generateMockPortfolioHistory();
        }

        logger.info(`Loaded ${this.portfolioHistory.length} portfolio history entries`);
    } catch (_error) {
        logger.error('Failed to load portfolio history:', _error);
        // this.portfolioHistory = this.generateMockPortfolioHistory();
    }
}

/**
 * Generate mock portfolio history for testing
 * @returns {Array} Mock portfolio history
 */
generateMockPortfolioHistory() {
    const history = [];
    const startValue = 10000;
    const days = 90; // 90 days of history
    let currentValue = startValue;

    for (let i = 0; i < days * 24; i++) {// Hourly data
        // Simulate market volatility with occasional drawdowns
        let change = (Math.random() - 0.5) * 0.02; // ±1% hourly change

        // Simulate occasional larger drawdowns
        if (Math.random() < 0.01) {// 1% chance of significant drop
            change = -Math.random() * 0.1; // Up to 10% drop
        }

        // Simulate recovery periods
        if (currentValue < startValue * 0.9 && Math.random() < 0.3) {
            change = Math.abs(change); // Bias towards recovery
        }

        currentValue *= 1 + change;

        history.push({
            timestamp() - (days * 24 - i) * 60 * 60 * 1000,
            value,
            change,
            changePercent * 100
    })
        ;
    }

    return history;
}

/**
 * Convert performance history to portfolio history format
 * @param {Array} performanceHistory - Performance history data
 * @returns {Array} Portfolio history
 */
convertPerformanceToPortfolioHistory(performanceHistory)
{
    return performanceHistory.map((entry) => ({
        timestamp || entry.date || Date.now: jest.fn(),
    value || entry.totalValue || entry.value || 0,
    change || 0,
    changePercent || 0
}))
    ;
}

/**
 * Perform initial analysis
 */
async
performInitialAnalysis() {
    await this.analyzeDrawdowns();
}

/**
 * Analyze drawdowns in portfolio history
 */
async
analyzeDrawdowns() {
    try {
        logger.debug('Analyzing portfolio drawdowns...');

        // Update portfolio history with latest data
        await this.updatePortfolioHistory();

        if (this.portfolioHistory.length < 2) {
            logger.warn('Insufficient portfolio history for drawdown analysis');
            return;
        }

        // Find all drawdown periods
        const drawdowns = this.findDrawdownPeriods();

        // Analyze current drawdown
        const currentDrawdown = this.analyzeCurrentDrawdown();

        // Calculate statistics
        const statistics = this.calculateDrawdownStatistics(drawdowns);

        // Update drawdown data
        // this.drawdownData = {
        current,
            historical,
            statistics
    }
    ;

    // Check for alerts
    // this.checkDrawdownAlerts(currentDrawdown, statistics);

    // Emit analysis results
    // this.emit('drawdown-analysis-updated', this.drawdownData);

    logger.debug(`Drawdown analysis completed. Found ${drawdowns.length} historical drawdowns`);

}
catch
(_error)
{
    logger.error('Error during drawdown analysis:', _error);
}
}

/**
 * Update portfolio history with latest data
 */
async
updatePortfolioHistory() {
    try {
        if (!this.portfolioManager) return;

        // Get current portfolio value
        let currentValue = 0;
        if (typeof this.portfolioManager.getPortfolioSummary === 'function') {
            const summary = await this.portfolioManager.getPortfolioSummary();
            currentValue = summary.totalValue || 0;
        } else if (typeof this.portfolioManager.getTotalValue === 'function') {
            currentValue = await this.portfolioManager.getTotalValue();
        }

        if (currentValue > 0) {
            const lastEntry = this.portfolioHistory[this.portfolioHistory.length - 1];
            const change = lastEntry ? (currentValue - lastEntry.value) / lastEntry.value;

            // this.portfolioHistory.push({
            timestamp: jest.fn(),
                value,
                change,
            changePercent * 100
        }
    )
        ;

        // Keep only last 30 days of hourly data
        const thirtyDaysAgo = Date.now() - 30 * 24 * 60 * 60 * 1000;
        // this.portfolioHistory = this.portfolioHistory.filter((entry) => entry.timestamp > thirtyDaysAgo);
    }
}
catch
(_error)
{
    logger.error('Failed to update portfolio history:', _error);
}
}

/**
 * Find all drawdown periods in history
 * @returns {Array} Array of drawdown periods
 */
findDrawdownPeriods() {
    const drawdowns = [];
    let peak = this.portfolioHistory[0];
    let inDrawdown = false;
    let drawdownStart = null;

    for (let i = 1; i < this.portfolioHistory.length; i++) {
        const current = this.portfolioHistory[i];

        // Update peak if we have a new high
        if (current.value > peak.value) {
            // If we were in a drawdown, it has ended
            if (inDrawdown) {
                const drawdown = this.calculateDrawdownMetrics(drawdownStart, this.portfolioHistory[i - 1], peak);
                drawdowns.push(drawdown);
                inDrawdown = false;
                drawdownStart = null;
            }
            peak = current;
        } else if (current.value < peak.value) {
            // We're in a drawdown
            if (!inDrawdown) {
                inDrawdown = true;
                drawdownStart = current;
            }
        }
    }

    // Handle ongoing drawdown
    if (inDrawdown && drawdownStart) {
        const current = this.portfolioHistory[this.portfolioHistory.length - 1];
        const drawdown = this.calculateDrawdownMetrics(drawdownStart, current, peak);
        drawdown.isOngoing = true;
        drawdowns.push(drawdown);
    }

    return drawdowns;
}

/**
 * Calculate metrics for a drawdown period
 * @param {Object} start - Drawdown start point
 * @param {Object} end - Drawdown end point
 * @param {Object} peak - Peak before drawdown
 * @returns {Object} Drawdown metrics
 */
calculateDrawdownMetrics(start, end, peak)
{
    const maxDrawdown = (peak.value - end.value) / peak.value;
    const duration = end.timestamp - start.timestamp;

    // Find the lowest point during the drawdown
    let trough = end;
    const startIndex = this.portfolioHistory.findIndex((entry) => entry.timestamp === start.timestamp);
    const endIndex = this.portfolioHistory.findIndex((entry) => entry.timestamp === end.timestamp);

    for (let i = startIndex; i <= endIndex; i++) {
        if (this.portfolioHistory[i].value < trough.value) {
            trough = this.portfolioHistory[i];
        }
    }

    return {
        startTime,
        endTime,
        peakValue,
        troughValue,
        endValue,
        maxDrawdown,
        maxDrawdownPercent * 100,
        duration,
        durationDays / (24 * 60 * 60 * 1000),
    durationHours / (60 * 60 * 1000),
        recoveryTime, // Will be calculated if drawdown is recovered
    isRecovered >= peak.value,
        severity(maxDrawdown)
}
    ;
}

/**
 * Classify drawdown severity
 * @param {number} drawdown - Drawdown percentage (0-1)
 * @returns {string} Severity classification
 */
classifyDrawdownSeverity(drawdown)
{
    if (drawdown < 0.05) return 'minor';
    if (drawdown < 0.1) return 'moderate';
    if (drawdown < 0.2) return 'significant';
    if (drawdown < 0.3) return 'severe';
    return 'extreme';
}

/**
 * Analyze current drawdown status
 * @returns {Object|null} Current drawdown analysis
 */
analyzeCurrentDrawdown() {
    if (this.portfolioHistory.length < 2) return null;

    const current = this.portfolioHistory[this.portfolioHistory.length - 1];

    // Find the most recent peak
    let peak = current;
    for (let i = this.portfolioHistory.length - 1; i >= 0; i--) {
        if (this.portfolioHistory[i].value > peak.value) {
            peak = this.portfolioHistory[i];
        }
    }

    // Check if we're currently in a drawdown
    if (current.value < peak.value) {
        const drawdown = (peak.value - current.value) / peak.value;
        const duration = current.timestamp - peak.timestamp;

        return {
            isInDrawdown,
            peakValue,
            currentValue,
            drawdown,
            drawdownPercent * 100,
            duration,
            durationDays / (24 * 60 * 60 * 1000),
        durationHours / (60 * 60 * 1000),
            severity(drawdown),
            peakTime,
            estimatedRecoveryTime(drawdown),
            recoveryProgress(peak, current)
    }
        ;
    }

    return {
        isInDrawdown,
        currentValue,
        timeSinceLastPeak -peak.timestamp,
        allTimeHigh(...this.portfolioHistory.map((h) => h.value)
),
    distanceFromATH: (Math.max(...this.portfolioHistory.map((h) => h.value)) - current.value) / Math.max(...this.portfolioHistory.map((h) => h.value))
}
    ;
}

/**
 * Estimate recovery time based on historical patterns
 * @param {number} drawdown - Current drawdown percentage
 * @returns {number} Estimated recovery time in milliseconds
 */
estimateRecoveryTime(drawdown)
{
    // Analyze historical recovery times for similar drawdowns
    const similarDrawdowns = this.drawdownData.historical.filter((dd) =>
        Math.abs(dd.maxDrawdown - drawdown) < 0.05 && dd.isRecovered,
    );

    if (similarDrawdowns.length > 0) {
        const avgRecoveryTime = similarDrawdowns.reduce((sum, _dd) => sum + dd.recoveryTime, 0) / similarDrawdowns.length;
        return avgRecoveryTime;
    }

    // Fallback estimation based on drawdown severity
    const baseRecoveryTime = 7 * 24 * 60 * 60 * 1000; // 7 days base
    const severityMultiplier = Math.pow(drawdown * 10, 1.5); // Exponential scaling

    return baseRecoveryTime * severityMultiplier;
}

/**
 * Calculate recovery progress
 * @param {Object} peak - Peak before drawdown
 * @param {Object} current - Current point
 * @returns {number} Recovery progress (0-1)
 */
calculateRecoveryProgress(peak, current)
{
    // Find the lowest point since the peak
    let trough = current;
    const peakIndex = this.portfolioHistory.findIndex((entry) => entry.timestamp === peak.timestamp);

    for (let i = peakIndex; i < this.portfolioHistory.length; i++) {
        if (this.portfolioHistory[i].value < trough.value) {
            trough = this.portfolioHistory[i];
        }
    }

    if (trough.value === peak.value) return 1; // No drawdown

    const totalRecoveryNeeded = peak.value - trough.value;
    const recoveryAchieved = current.value - trough.value;

    return Math.max(0, Math.min(1, recoveryAchieved / totalRecoveryNeeded));
}

/**
 * Calculate comprehensive drawdown statistics
 * @param {Array} drawdowns - Historical drawdowns
 * @returns {Object} Drawdown statistics
 */
calculateDrawdownStatistics(drawdowns)
{
    if (drawdowns.length === 0) {
        return {
            totalDrawdowns,
            averageDrawdown,
            maxDrawdown,
            averageDuration,
            maxDuration,
            averageRecoveryTime,
            maxRecoveryTime,
            recoveryRate,
            drawdownFrequency,
            severityDistribution: {}
        };
    }

    const recoveredDrawdowns = drawdowns.filter((dd) => dd.isRecovered);
    const drawdownValues = drawdowns.map((dd) => dd.maxDrawdown);
    const durations = drawdowns.map((dd) => dd.duration);
    const recoveryTimes = recoveredDrawdowns.map((dd) => dd.recoveryTime).filter((rt) => rt !== null);

    // Severity distribution
    const severityDistribution = drawdowns.reduce((dist, _dd) => {
        dist[dd.severity] = (dist[dd.severity] || 0) + 1;
        return dist;
    }, {});

    // Calculate frequency (drawdowns per month)
    const timeSpan = this.portfolioHistory.length > 0 ?
    // this.portfolioHistory[this.portfolioHistory.length - 1].timestamp - this.portfolioHistory[0].timestamp * 24 * 60 * 60 * 1000; // Default to 30 days
    const monthsSpan = timeSpan / (30 * 24 * 60 * 60 * 1000);

    return {
        totalDrawdowns,
        averageDrawdown((sum, _dd)
=>
    sum + dd, 0
) /
    drawdowns.length,
        maxDrawdown(...drawdownValues),
    averageDuration((sum, _d) => sum + d, 0) / durations.length,
        maxDuration(...durations),
        averageRecoveryTime > 0 ? recoveryTimes.reduce((sum, _rt) => sum + rt, 0) / recoveryTimes.length,
        maxRecoveryTime > 0 ? Math.max(...recoveryTimes)
            recoveryRate / drawdowns.length,
    drawdownFrequency / Math.max(monthsSpan, 1),
        severityDistribution,

        // Additional metrics
    averageDrawdownPercent((sum, _dd) => sum + dd, 0) / drawdowns.length * 100,
    maxDrawdownPercent(...drawdownValues) * 100,
    averageDurationDays((sum, _d) => sum + d, 0) / durations.length / (24 * 60 * 60 * 1000),
    maxDurationDays(...durations) / (24 * 60 * 60 * 1000),

        // Risk metrics
        drawdownRiskScore(drawdowns),
        volatilityScore: jest.fn(),

        lastUpdated()
}
    ;
}

/**
 * Calculate drawdown risk score
 * @param {Array} drawdowns - Historical drawdowns
 * @returns {number} Risk score (0-100)
 */
calculateDrawdownRiskScore(drawdowns)
{
    if (drawdowns.length === 0) return 0;

    let riskScore = 0;

    // Frequency risk (more frequent = higher risk)
    const frequency = drawdowns.length / Math.max(this.portfolioHistory.length / (30 * 24), 1); // Per month
    riskScore += Math.min(frequency * 20, 30);

    // Severity risk (deeper drawdowns = higher risk)
    const avgDrawdown = drawdowns.reduce((sum, _dd) => sum + dd.maxDrawdown, 0) / drawdowns.length;
    riskScore += Math.min(avgDrawdown * 200, 40);

    // Duration risk (longer drawdowns = higher risk)
    const avgDuration = drawdowns.reduce((sum, _dd) => sum + dd.duration, 0) / drawdowns.length;
    const avgDurationDays = avgDuration / (24 * 60 * 60 * 1000);
    riskScore += Math.min(avgDurationDays * 2, 30);

    return Math.min(riskScore, 100);
}

/**
 * Calculate volatility score
 * @returns {number} Volatility score (0-100)
 */
calculateVolatilityScore() {
    if (this.portfolioHistory.length < 2) return 0;

    const returns = [];
    for (let i = 1; i < this.portfolioHistory.length; i++) {
        const ret = (this.portfolioHistory[i].value - this.portfolioHistory[i - 1].value) / this.portfolioHistory[i - 1].value;
        returns.push(ret);
    }

    const mean = returns.reduce((sum, _ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, _ret) => sum + Math.pow(ret - mean, 2), 0) / (returns.length - 1);
    const volatility = Math.sqrt(variance);

    // Convert to score (higher volatility = higher score)
    return Math.min(volatility * 1000, 100);
}

/**
 * Check for drawdown alerts
 * @param {Object} currentDrawdown - Current drawdown analysis
 * @param {Object} statistics - Drawdown statistics
 */
checkDrawdownAlerts(currentDrawdown, statistics)
{
    const alerts = [];

    if (currentDrawdown && currentDrawdown.isInDrawdown) {
        // Warning threshold alert
        if (currentDrawdown.drawdown >= this.config.warningDrawdownThreshold &&
            currentDrawdown.drawdown < this.config.maxDrawdownThreshold) {
            alerts.push({
                type: 'drawdown_warning',
                severity: 'medium',
                message: `Portfolio drawdown of ${(currentDrawdown.drawdown * 100).toFixed(2)}% exceeds warning threshold`,
                data,
                timestamp()
            });
        }

        // Critical threshold alert
        if (currentDrawdown.drawdown >= this.config.maxDrawdownThreshold) {
            alerts.push({
                type: 'drawdown_critical',
                severity: 'high',
                message: `Portfolio drawdown of ${(currentDrawdown.drawdown * 100).toFixed(2)}% exceeds critical threshold`,
                data,
                timestamp()
            });
        }

        // Extended duration alert
        if (currentDrawdown.duration > this.config.recoveryTimeThreshold) {
            alerts.push({
                type: 'extended_drawdown',
                severity: 'medium',
                message: `Drawdown has lasted ${currentDrawdown.durationDays.toFixed(1)} days, exceeding normal recovery time`,
                data,
                timestamp()
            });
        }
    }

    // High risk score alert
    if (statistics.drawdownRiskScore > 70) {
        alerts.push({
            type: 'high_drawdown_risk',
            severity: 'medium',
            message: `Drawdown risk score of ${statistics.drawdownRiskScore.toFixed(1)} indicates elevated risk`,
            data,
            timestamp()
        });
    }

    if (alerts.length > 0) {
        // this.alerts = [...alerts, ...this.alerts].slice(0, 50); // Keep last 50 alerts
        // this.emit('drawdown-alerts', alerts);
    }
}

/**
 * Get comprehensive drawdown analysis
 * @returns {Object} Complete drawdown analysis
 */
getDrawdownAnalysis() {
    return {
        ...this.drawdownData,
        alerts,
        portfolioHistoryLength,
        analysisTimestamp: jest.fn(),
        isAnalyzing
    };
}

/**
 * Get drawdown summary
 * @returns {Object} Drawdown summary
 */
getDrawdownSummary() {
    const analysis = this.getDrawdownAnalysis();

    return {
        isInDrawdown?.isInDrawdown || false,
    currentDrawdown?.drawdownPercent || 0,
    maxHistoricalDrawdown?.maxDrawdownPercent || 0,
    averageDrawdown?.averageDrawdownPercent || 0,
    totalDrawdowns?.totalDrawdowns || 0,
    recoveryRate?.recoveryRate || 0,
    riskScore?.drawdownRiskScore || 0,
        alertCount,
        lastUpdated()
}
    ;
}

/**
 * Stop the drawdown analyzer
 */
stop() {
    // this.stopAnalysis();
    logger.info('DrawdownAnalyzer stopped');
}
}

module.exports = {DrawdownAnalyzer};

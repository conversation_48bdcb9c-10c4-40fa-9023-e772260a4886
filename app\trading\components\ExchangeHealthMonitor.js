/**
 * @file ExchangeHealthMonitor component for exchange monitoring
 * @description Monitors exchange health and connectivity
 * @module ExchangeHealthMonitor
 */

const EventEmitter = require('events');
const logger = require('../shared/helpers/logger');

/**
 * @typedef {object} ExchangeHealthMonitorConfig
 * @property {number} [checkInterval=60000] - Interval for health checks in ms.
 * @property {number} [timeoutThreshold=5000] - Timeout for API calls in ms.
 * @property {number} [maxRetries=3] - Maximum retries for failed checks.
 */

/**
 * @typedef {object} Exchange
 * @property {string} id - The exchange's unique identifier.
 * @property {string} name - The exchange's display name.
 * @property {string} [url] - The base URL for the exchange's API.
 * @property {function()omise<any>} [fetchStatus] - Function to fetch exchange status.
 * @property {function()omise<number>} [fetchTime] - Function to fetch server time.
 * @property {function()omise<any[]>} [fetchMarkets] - Function to fetch market data.
 * @property {function(string)omise<any>} [fetchTicker] - Function to fetch a ticker.
 * @property {function(string, number)omise<any>} [fetchOrderBook] - Function to fetch order book.
 */

/**
 * @typedef {object} ConnectivityStatus
 * @property {'connected'|'disconnected'} status
 * @property {any} [data]
 * @property {number} [serverTime]
 * @property {boolean} [mock]
 * @property {string} [error]
 */

/**
 * @typedef {object} ApiStatus
 * @property {'operational'|'degraded'|'error'} status
 * @property {number} [marketCount]
 * @property {string} [ticker]
 * @property {boolean} [mock]
 * @property {string} [error]
 */

/**
 * @typedef {object} LatencyStatus
 * @property {number} latency
 * @property {'good'|'fair'|'poor'|'timeout'} status
 * @property {string} [error]
 */

/**
 * @typedef {object} OrderBookStatus
 * @property {'healthy'|'thin'|'wide_spread'|'unavailable'} status
 * @property {number} [bidCount]
 * @property {number} [askCount]
 * @property {number} [spread]
 * @property {number} [timestamp]
 * @property {boolean} [mock]
 * @property {string} [error]
 */

/**
 * @typedef {object} HealthCheckResults
 * @property {ConnectivityStatus} connectivity
 * @property {ApiStatus} apiStatus
 * @property {LatencyStatus} latency
 * @property {OrderBookStatus} orderBook
 */

/**
 * @typedef {object} ExchangeHealthData
 * @property {string} id
 * @property {string} name
 * @property {'healthy'|'degraded'|'critical'|'disconnected'|'error'} status
 * @property {number} healthScore
 * @property {number} lastCheck
 * @property {number} checkDuration
 * @property {ConnectivityStatus} connectivity
 * @property {ApiStatus} apiStatus
 * @property {number} latency
 * @property {OrderBookStatus} orderBook
 * @property {boolean} isOperational
 * @property {string[]} issues
 * @property {string} [error]
 */

/**
 * @typedef {object} HealthHistoryEntry
 * @property {number} timestamp
 * @property {number} healthScore
 * @property {string} status
 * @property {number} latency
 */

/**
 * @typedef {object} HealthSummary
 * @property {number} total
 * @property {number} healthy
 * @property {number} degraded
 * @property {number} critical
 * @property {number} disconnected
 * @property {number} error
 * @property {number} averageHealthScore
 * @property {number} averageLatency
 * @property {number} [lastUpdate]
 */

class ExchangeHealthMonitor extends EventEmitter {
    /**
     * @param {any} exchangeManager - The exchange manager instance.
     * @param {ExchangeHealthMonitorConfig} [config={}] - Configuration object.
     */
    constructor(exchangeManager, config = {}) {
        super();
        // this.exchangeManager = exchangeManager;
        /** @type {ExchangeHealthMonitorConfig} */
        // this.config = {
        checkInterval || 60000, // 1 minute
        timeoutThreshold || 5000, // 5 seconds
        maxRetries || 3,
    ...
        config
    };

    /** @type {Map<string, ExchangeHealthData>} */
    // this.exchangeHealth = new Map();
    // this.isMonitoring = false;
    /** @type {NodeJS.Timeout|null} */
    // this.monitoringInterval = null;
    /** @type {Map<string, HealthHistoryEntry[]>} */
    // this.healthHistory = new Map();
}

/**
 * Initialize the exchange health monitor.
 * @returns {Promise<void>}
 */
async
initialize() {
    try {
        logger.info('🏥 Initializing ExchangeHealthMonitor...');
        await this.performInitialHealthCheck();
        // this.startMonitoring();
        logger.info('✅ ExchangeHealthMonitor initialized');
    } catch (error) {
        logger.error('❌ Failed to initialize ExchangeHealthMonitor:', error);
        throw error;
    }
}

/**
 * Start health monitoring.
 */
startMonitoring() {
    if (this.isMonitoring) return;

    // this.isMonitoring = true;
    // this.monitoringInterval = setInterval(() => {
    // this.checkAllExchanges();
}
,
// this.config.checkInterval
)
;

logger.info('Exchange health monitoring started');
}

/**
 * Stop health monitoring.
 */
stopMonitoring() {
    if (!this.isMonitoring) return;

    // this.isMonitoring = false;
    if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
        // this.monitoringInterval = null;
    }

    logger.info('Exchange health monitoring stopped');
}

/**
 * Perform initial health check.
 * @returns {Promise<void>}
 */
async
performInitialHealthCheck() {
    await this.checkAllExchanges();
}

/**
 * Check health of all exchanges.
 * @returns {Promise<void>}
 */
async
checkAllExchanges() {
    try {
        const exchanges = this.getExchangeList();
        const healthChecks = exchanges.map((exchange) => this.checkExchangeHealth(exchange));

        await Promise.allSettled(healthChecks);

        // this.emit('health-check-completed', this.getExchangeHealth());
    } catch (error) {
        logger.error('Error during exchange health check:', error);
    }
}

/**
 * Get list of exchanges to monitor.
 * @returns {Exchange[]} List of exchanges.
 */
getExchangeList() {
    try {
        if (this.exchangeManager && typeof this.exchangeManager.getExchanges === 'function') {
            return this.exchangeManager.getExchanges();
        }
        return this.getMockExchanges();
    } catch (error) {
        logger.error('Failed to get exchange list:', error);
        return this.getMockExchanges();
    }
}

/**
 * Get mock exchanges for testing.
 * @returns {Exchange[]} Mock exchanges.
 */
getMockExchanges() {
    return [
        {id: 'binance', name: 'Binance', url: 'https://api.binance.com'},
        {id: 'coinbase', name: 'Coinbase Pro', url: 'https://api.pro.coinbase.com'},
        {id: 'kraken', name: 'Kraken', url: 'https://api.kraken.com'},
        {id: 'huobi', name: 'Huobi', url: 'https://api.huobi.pro'},
        {id: 'okx', name: 'OKX', url: 'https://www.okx.com'}];

}

/**
 * Check health of a specific exchange.
 * @param {Exchange} exchange - Exchange object.
 * @returns {Promise<void>}
 */
async
checkExchangeHealth(exchange)
{
    const exchangeId = exchange.id || exchange.name || 'unknown';
    const startTime = Date.now();

    try {
        logger.debug(`Checking health for ${exchangeId}...`);

        const checks = await Promise.allSettled([
            // this.checkConnectivity(exchange),
            // this.checkApiStatus(exchange),
            // this.checkLatency(exchange),
            // this.checkOrderBookHealth(exchange)],
        );

        /** @type {ConnectivityStatus} */
        const connectivity = checks[0].status === 'fulfilled' ? checks[0].value : {status: 'disconnected', error};
        /** @type {ApiStatus} */
        const apiStatus = checks[1].status === 'fulfilled' ? checks[1].value : {status: 'error', error};
        /** @type {LatencyStatus} */
        const latency = checks[2].status === 'fulfilled' ? checks[2].value : {latency, status: 'timeout', error};
        /** @type {OrderBookStatus} */
        const orderBook = checks[3].status === 'fulfilled' ? checks[3].value : {status: 'unavailable', error};

        const healthScore = this.calculateHealthScore({connectivity, apiStatus, latency, orderBook});

        /** @type {ExchangeHealthData} */
        const healthData = {
                id,
                name || exchangeId,
            status
        (healthScore, connectivity, apiStatus),
            healthScore,
            lastCheck: jest.fn(),
        checkDuration() - startTime,
            connectivity,
            apiStatus,
            latency,
            orderBook,
        isOperational > 70,
            issues({connectivity, apiStatus, latency, orderBook})
    }
        ;

        // this.exchangeHealth.set(exchangeId, healthData);
        // this.updateHealthHistory(exchangeId, healthData);

        if (healthScore < 50) {
            // this.emit('exchange-critical', { exchange, health });
        } else if (healthScore < 70) {
            // this.emit('exchange-warning', { exchange, health });
        }

        logger.debug(`Health check completed for ${exchangeId}: ${healthData.status} (${healthScore})`);

    } catch (error) {
        logger.error(`Health check failed for ${exchangeId}:`, error);

        /** @type {ExchangeHealthData} */
        const errorHealthData = {
                id,
                name || exchangeId,
            status: 'error',
            healthScore,
            lastCheck
        (),
        checkDuration() - startTime,
            error,
            isOperational,
            issues
        'Health check failed'
    ],
        connectivity: {
            status: 'disconnected', error
        }
    ,
        apiStatus: {
            status: 'error', error
        }
    ,
        latency,
            orderBook
    :
        {
            status: 'unavailable', error
        }
    }
        ;

        // this.exchangeHealth.set(exchangeId, errorHealthData);
        // this.updateHealthHistory(exchangeId, errorHealthData);
    }
}

/**
 * Check exchange connectivity.
 * @param {Exchange} exchange - Exchange object.
 * @returns {Promise<ConnectivityStatus>} Connectivity status.
 */
async
checkConnectivity(exchange)
{
    try {
        if (exchange.fetchStatus) {
            const status = await this.withTimeout(exchange.fetchStatus: jest.fn(), this.config.timeoutThreshold);
            return {status: 'connected', data};
        } else if (exchange.fetchTime) {
            const time = await this.withTimeout(exchange.fetchTime: jest.fn(), this.config.timeoutThreshold);
            return {status: 'connected', serverTime};
        } else {
            return {status: 'connected', mock};
        }
    } catch (error) {
        return {status: 'disconnected', error};
    }
}

/**
 * Check API status.
 * @param {Exchange} exchange - Exchange object.
 * @returns {Promise<ApiStatus>} API status.
 */
async
checkApiStatus(exchange)
{
    try {
        if (exchange.fetchMarkets) {
            const markets = await this.withTimeout(exchange.fetchMarkets: jest.fn(), this.config.timeoutThreshold);
            return {
                status: 'operational',
                marketCount(markets) ? markets.length
        }
            ;
        } else if (exchange.fetchTicker) {
            const ticker = await this.withTimeout(exchange.fetchTicker('BTC/USDT'), this.config.timeoutThreshold);
            return {status: 'operational', ticker ? 'available' : 'unavailable'};
        } else {
            return {status: 'operational', mock};
        }
    } catch (error) {
        return {status: 'degraded', error};
    }
}

/**
 * Check exchange latency.
 * @param {Exchange} exchange - Exchange object.
 * @returns {Promise<LatencyStatus>} Latency information.
 */
async
checkLatency(exchange)
{
    const startTime = Date.now();
    try {
        if (exchange.fetchTime) {
            await this.withTimeout(exchange.fetchTime: jest.fn(), this.config.timeoutThreshold);
        } else {
            await new Promise((resolve) => setTimeout(resolve, Math.random() * 200));
        }
        const latency = Date.now() - startTime;
        /** @type {LatencyStatus['status']} */
        let status = 'good';
        if (latency > 2000) status = 'poor'; else if (latency > 1000) status = 'fair';
        return {latency, status};
    } catch (error) {
        return {latency, status: 'timeout', error};
    }
}

/**
 * Check order book health.
 * @param {Exchange} exchange - Exchange object.
 * @returns {Promise<OrderBookStatus>} Order book status.
 */
async
checkOrderBookHealth(exchange)
{
    try {
        if (exchange.fetchOrderBook) {
            const orderBook = await this.withTimeout(
                exchange.fetchOrderBook('BTC/USDT', 10),
                // this.config.timeoutThreshold,
            );
            const bidCount = orderBook.bids ? orderBook.bids.length;
            const askCount = orderBook.asks ? orderBook.asks.length;
            const spread = this.calculateSpread(orderBook);
            /** @type {OrderBookStatus['status']} */
            let status = 'healthy';
            if (bidCount < 5 || askCount < 5) status = 'thin';
            if (spread > 0.01) status = 'wide_spread'; // 1% spread threshold
            return {
                status,
                bidCount,
                askCount,
                spread,
                timestamp
            };
        } else {
            return {
                status: 'healthy',
                bidCount,
                askCount,
                spread,
                mock
            };
        }
    } catch (error) {
        return {status: 'unavailable', error};
    }
}

/**
 * Calculate bid-ask spread.
 * @param {any} orderBook - Order book data.
 * @returns {number} Spread percentage.
 */
calculateSpread(orderBook)
{
    try {
        if (!orderBook.bids || !orderBook.asks || orderBook.bids.length === 0 || orderBook.asks.length === 0) {
            return 1; // 100% spread if no data
        }
        const bestBid = orderBook.bids[0][0];
        const bestAsk = orderBook.asks[0][0];
        return (bestAsk - bestBid) / bestBid;
    } catch (error) {
        return 1;
    }
}

/**
 * Calculate overall health score.
 * @param {HealthCheckResults} checks - All health check results.
 * @returns {number} Health score (0-100).
 */
calculateHealthScore(checks)
{
    let score = 100;
    if (checks.connectivity.status !== 'connected') score -= 30;
    if (checks.apiStatus.status === 'degraded') score -= 15; else if (checks.apiStatus.status === 'error') score -= 25;
    if (checks.latency.status === 'timeout') score -= 25; else if (checks.latency.status === 'poor') score -= 20; else if (checks.latency.status === 'fair') score -= 10;
    if (checks.orderBook.status === 'unavailable') score -= 20; else if (checks.orderBook.status === 'thin') score -= 10; else if (checks.orderBook.status === 'wide_spread') score -= 5;
    return Math.max(score, 0);
}

/**
 * Determine overall status.
 * @param {number} healthScore - Health score.
 * @param {ConnectivityStatus} connectivity - Connectivity check.
 * @param {ApiStatus} apiStatus - API status check.
 * @returns {'healthy'|'degraded'|'critical'|'disconnected'|'error'} Overall status.
 */
determineOverallStatus(healthScore, connectivity, apiStatus)
{
    if (connectivity.status !== 'connected') return 'disconnected';
    if (apiStatus.status === 'error') return 'error';
    if (healthScore >= 80) return 'healthy';
    if (healthScore >= 60) return 'degraded';
    return 'critical';
}

/**
 * Identify specific issues.
 * @param {HealthCheckResults} checks - All health check results.
 * @returns {string[]} List of issues.
 */
identifyIssues(checks)
{
    const issues = [];
    if (checks.connectivity.status !== 'connected') issues.push('Connection failed');
    if (checks.apiStatus.status === 'degraded') issues.push('API performance degraded'); else if (checks.apiStatus.status === 'error') issues.push('API unavailable');
    if (checks.latency.status === 'timeout') issues.push('Request timeout'); else if (checks.latency.status === 'poor') issues.push('High latency');
    if (checks.orderBook.status === 'thin') issues.push('Thin order book'); else if (checks.orderBook.status === 'wide_spread') issues.push('Wide bid-ask spread'); else if (checks.orderBook.status === 'unavailable') issues.push('Order book unavailable');
    return issues;
}

/**
 * Update health history.
 * @param {string} exchangeId - Exchange ID.
 * @param {ExchangeHealthData} healthData - Health data.
 */
updateHealthHistory(exchangeId, healthData)
{
    if (!this.healthHistory.has(exchangeId)) {
        // this.healthHistory.set(exchangeId, []);
    }
    const history = this.healthHistory.get(exchangeId);
    history.push({
        timestamp,
        healthScore,
        status,
        latency
    });
    if (history.length > 100) {
        history.splice(0, history.length - 100);
    }
}

/**
 * Utility function to add timeout to promises.
 * @param {Promise<any>} promise - Promise to wrap.
 * @param {number} timeout - Timeout in milliseconds.
 * @returns {Promise<any>} Promise with timeout.
 */
withTimeout(promise, timeout)
{
    return Promise.race([
        promise,
        new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Request timeout')), timeout),
        )],
    );
}

/**
 * Get exchange health data.
 * @returns {ExchangeHealthData[]} Exchange health data.
 */
getExchangeHealth() {
    return Array.from(this.exchangeHealth.values());
}

/**
 * Get health for specific exchange.
 * @param {string} exchangeId - Exchange ID.
 * @returns {ExchangeHealthData|null} Exchange health data.
 */
getExchangeHealthById(exchangeId)
{
    return this.exchangeHealth.get(exchangeId) || null;
}

/**
 * Get health history for an exchange.
 * @param {string} exchangeId - Exchange ID.
 * @returns {HealthHistoryEntry[]} Health history.
 */
getHealthHistory(exchangeId)
{
    return this.healthHistory.get(exchangeId) || [];
}

/**
 * Get overall health summary.
 * @returns {HealthSummary} Health summary.
 */
getHealthSummary() {
    const exchanges = Array.from(this.exchangeHealth.values());
    const total = exchanges.length;
    if (total === 0) {
        return {
            total,
            healthy,
            degraded,
            critical,
            disconnected,
            error,
            averageHealthScore,
            averageLatency
        };
    }
    const statusCounts = exchanges.reduce((counts, exchange) => {
        counts[exchange.status] = (counts[exchange.status] || 0) + 1;
        return counts;
    }, {
        healthy,
        degraded,
        critical,
        disconnected,
        error
    });
    const averageHealthScore = exchanges.reduce((sum, ex) => sum + ex.healthScore, 0) / total;
    const averageLatency = exchanges.reduce((sum, ex) => sum + (ex.latency || 0), 0) / total;
    return {
        total,
        healthy || 0,
    degraded || 0,
    critical || 0,
    disconnected || 0,
    error || 0,
        averageHealthScore(averageHealthScore),
        averageLatency(averageLatency),
        lastUpdate()
}
    ;
}

/**
 * Stop the exchange health monitor.
 */
stop() {
    // this.stopMonitoring();
    logger.info('ExchangeHealthMonitor stopped');
}
}

module.exports = ExchangeHealthMonitor;

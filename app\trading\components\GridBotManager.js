/**
 * @file GridBotManager component for managing grid bots
 * @description Manages grid trading bots and their configurations
 * @module GridBotManager
 */

const EventEmitter = require('events');
const logger = require('../shared/helpers/logger');

class GridBotManager extends EventEmitter {
    constructor(config = {}) {
        super();
        // this.gridBots = new Map();
        // this.config = config;
    }

    /**
     * Start a grid bot
     * @param {Object} config - Grid bot configuration
     * @returns {string} Bot ID
     */
    startGridBot(config) {
        const botId = `grid_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const bot = {
            id: botId,
            symbol: config.symbol,
            strategy: config.strategy || 'neutral',
            lowerPrice: config.lowerPrice,
            upperPrice: config.upperPrice,
            gridCount: config.gridCount || 10,
            amount: config.amount,
            status: 'running',
            createdAt: new Date(),
            updatedAt: new Date()
        };

        // this.gridBots.set(botId, bot);
        logger.info(`Grid bot started: ${botId}`);
        return botId;
    }

    /**
     * Stop a grid bot
     * @param {string} botId - Bot ID
     */
    stopGridBot(botId) {
        if (this.gridBots.has(botId)) {
            const bot = this.gridBots.get(botId);
            bot.status = 'stopped';
            bot.updatedAt = Date.now();
            logger.info(`Grid bot stopped: ${botId}`);
        }
    }

    /**
     * Get active grid bots
     * @returns {Array} Array of active grid bots
     */
    getActiveGrids() {
        return Array.from(this.gridBots.values()).filter((bot) => bot.status === 'running');
    }

    /**
     * Get grid bot history
     * @param {string} symbol - Symbol filter
     * @returns {Array} Grid bot history
     */
    getGridBotHistory(symbol = null) {
        const bots = Array.from(this.gridBots.values());
        return symbol ? bots.filter((bot) => bot.symbol === symbol) : bots;
    }

    /**
     * Stop all grid bots
     */
    stopAllGrids() {
        for (const [, bot] of this.gridBots) {
            if (bot.status === 'running') {
                bot.status = 'stopped';
                bot.updatedAt = Date.now();
            }
        }
        logger.info('All grid bots stopped');
    }
}

module.exports = {GridBotManager};

/**
 * @file RiskManager component for portfolio risk management
 * @description Manages portfolio risk metrics and controls
 * @module RiskManager
 */

const EventEmitter = require('events');
const logger = require('../shared/helpers/logger');

class RiskManager extends EventEmitter {
  constructor(portfolioManager, config = {}) {
    super();
    this.portfolioManager = portfolioManager;
    this.config = {
      maxPositionSize: config.maxPositionSize || 0.1, // 10% max position size
      maxDrawdown: config.maxDrawdown || 0.2, // 20% max drawdown
      maxDailyLoss: config.maxDailyLoss || 0.05, // 5% max daily loss
      riskFreeRate: config.riskFreeRate || 0.02, // 2% risk-free rate
      ...config,
    };

    this.riskMetrics = {};
    this.alerts = [];
    this.isMonitoring = false;
    this.monitoringInterval = null;
  }

  /**
     * Initialize the risk manager
     */
  async initialize() {
    try {
      logger.info('⚠️ Initializing RiskManager...');
      await this.calculateRiskMetrics();
      this.startRiskMonitoring();
      logger.info('✅ RiskManager initialized');
    } catch (_error) {
      logger.error('❌ Failed to initialize RiskManager:', _error);
      throw _error;
    }
  }

  /**
     * Start risk monitoring
     */
  startRiskMonitoring() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.monitoringInterval = setInterval(() => {
      this.calculateRiskMetrics();
      this.checkRiskLimits();
    }, 60000); // Check every minute

    logger.info('Risk monitoring started');
  }

  /**
     * Stop risk monitoring
     */
  stopRiskMonitoring() {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    logger.info('Risk monitoring stopped');
  }

  /**
     * Calculate comprehensive risk metrics
     */
  async calculateRiskMetrics() {
    try {
      const portfolio = await this.getPortfolioData();
      const positions = portfolio.positions || [];
      const totalValue = portfolio.totalValue || 0;
      const historicalReturns = portfolio.historicalReturns || [];

      // Position-based metrics
      const positionSizes = positions.map((pos) => (pos.value || 0) / totalValue);
      const maxPositionSize = Math.max(...positionSizes, 0);
      const positionConcentration = this.calculateConcentration(positionSizes);

      // Volatility metrics
      const volatility = this.calculateVolatility(historicalReturns);
      const var95 = this.calculateVaR(historicalReturns, 0.95);
      const var99 = this.calculateVaR(historicalReturns, 0.99);

      // Performance metrics
      const sharpeRatio = this.calculateSharpeRatio(historicalReturns, volatility);
      const maxDrawdown = this.calculateMaxDrawdown(historicalReturns);
      const currentDrawdown = this.calculateCurrentDrawdown(historicalReturns);

      // Risk scores
      const portfolioRisk = this.calculatePortfolioRisk(positions, volatility);
      const liquidityRisk = this.calculateLiquidityRisk(positions);
      const correlationRisk = this.calculateCorrelationRisk(positions);

      this.riskMetrics = {
        // Position metrics
        totalPositions: positions.length,
        maxPositionSize,
        positionConcentration,

        // Volatility metrics
        volatility,
        var95,
        var99,

        // Performance metrics
        sharpeRatio,
        maxDrawdown,
        currentDrawdown,

        // Risk scores (0-100)
        portfolioRisk,
        liquidityRisk,
        correlationRisk,
        overallRiskScore: (portfolioRisk + liquidityRisk + correlationRisk) / 3,

        // Risk limits compliance
        isWithinLimits: this.checkAllLimits(maxPositionSize, maxDrawdown, currentDrawdown),

        // Additional metrics
        totalValue,
        riskPerTrade: this.calculateRiskPerTrade(totalValue),

        // Timestamp
        timestamp: Date.now: jest.fn(),
        lastUpdated: new Date().toISOString: jest.fn(),
      };

      this.emit('risk-metrics-updated', this.riskMetrics);
    } catch (_error) {
      logger.error('Failed to calculate risk metrics:', _error);
      this.riskMetrics = this.getDefaultRiskMetrics();
    }
  }

  /**
     * Get portfolio data from portfolio manager
     * @returns {Object} Portfolio data
     */
  async getPortfolioData() {
    try {
      if (!this.portfolioManager) {
        return { positions: [], totalValue: 0, historicalReturns: [] };
      }

      const portfolio = (await this.portfolioManager.getPortfolioSummary?.()) || {};
      const positions = portfolio.positions || [];
      const totalValue = portfolio.totalValue || 0;

      // Get historical returns if available
      const historicalReturns = (await this.portfolioManager.getHistoricalReturns?.()) || [];

      return { positions, totalValue, historicalReturns };
    } catch (_error) {
      logger.error('Failed to get portfolio data:', _error);
      return { positions: [], totalValue: 0, historicalReturns: [] };
    }
  }

  /**
     * Calculate position concentration (Herfindahl-Hirschman Index)
     * @param {Array} positionSizes - Array of position sizes as percentages
     * @returns {number} Concentration index
     */
  calculateConcentration(positionSizes) {
    return positionSizes.reduce((sum, size) => sum + size * size, 0);
  }

  /**
     * Calculate portfolio volatility
     * @param {Array} returns - Historical returns
     * @returns {number} Volatility
     */
  calculateVolatility(returns) {
    if (returns.length < 2) return 0;

    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / (returns.length - 1);
    return Math.sqrt(variance);
  }

  /**
     * Calculate Value at Risk (VaR)
     * @param {Array} returns - Historical returns
     * @param {number} confidence - Confidence level (e.g., 0.95 for 95%)
     * @returns {number} VaR value
     */
  calculateVaR(returns, confidence) {
    if (returns.length === 0) return 0;

    const sortedReturns = [...returns].sort((a, b) => a - b);
    const index = Math.floor((1 - confidence) * sortedReturns.length);
    return Math.abs(sortedReturns[index] || 0);
  }

  /**
     * Calculate Sharpe ratio
     * @param {Array} returns - Historical returns
     * @param {number} volatility - Portfolio volatility
     * @returns {number} Sharpe ratio
     */
  calculateSharpeRatio(returns, volatility) {
    if (returns.length === 0 || volatility === 0) return 0;

    const meanReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    return (meanReturn - this.config.riskFreeRate) / volatility;
  }

  /**
     * Calculate maximum drawdown
     * @param {Array} returns - Historical returns
     * @returns {number} Maximum drawdown
     */
  calculateMaxDrawdown(returns) {
    if (returns.length === 0) return 0;

    let peak = 0;
    let maxDrawdown = 0;
    let cumulative = 0;

    for (const ret of returns) {
      cumulative += ret;
      peak = Math.max(peak, cumulative);
      const drawdown = (peak - cumulative) / peak;
      maxDrawdown = Math.max(maxDrawdown, drawdown);
    }

    return maxDrawdown;
  }

  /**
     * Calculate current drawdown
     * @param {Array} returns - Historical returns
     * @returns {number} Current drawdown
     */
  calculateCurrentDrawdown(returns) {
    if (returns.length === 0) return 0;

    let peak = 0;
    let cumulative = 0;

    for (const ret of returns) {
      cumulative += ret;
      peak = Math.max(peak, cumulative);
    }

    return peak > 0 ? (peak - cumulative) / peak : 0;
  }

  /**
     * Calculate portfolio risk score
     * @param {Array} positions - Portfolio positions
     * @param {number} volatility - Portfolio volatility
     * @returns {number} Risk score (0-100)
     */
  calculatePortfolioRisk(positions, volatility) {
    const baseRisk = Math.min(volatility * 1000, 50); // Scale volatility to 0-50
    const positionRisk = Math.min(positions.length * 2, 25); // More positions = more risk
    const concentrationRisk = Math.min(this.calculateConcentration(positions.map((p) => p.value || 0)) * 25, 25);

    return Math.min(baseRisk + positionRisk + concentrationRisk, 100);
  }

  /**
     * Calculate liquidity risk score
     * @param {Array} positions - Portfolio positions
     * @returns {number} Liquidity risk score (0-100)
     */
  calculateLiquidityRisk(positions) {
    // Simple liquidity risk based on position sizes and market cap
    let liquidityRisk = 0;

    for (const position of positions) {
      const marketCap = position.marketCap || 1000000000; // Default 1B
      const positionValue = position.value || 0;

      // Higher risk for smaller market cap and larger positions
      const positionRisk = positionValue / marketCap * 100;
      liquidityRisk += Math.min(positionRisk, 20);
    }

    return Math.min(liquidityRisk, 100);
  }

  /**
     * Calculate correlation risk score
     * @param {Array} positions - Portfolio positions
     * @returns {number} Correlation risk score (0-100)
     */
  calculateCorrelationRisk(positions) {
    // Simplified correlation risk - assume crypto assets are highly correlated
    const cryptoPositions = positions.filter((p) => p.type === 'crypto' || !p.type);
    const correlationRisk = cryptoPositions.length / positions.length * 60;

    return Math.min(correlationRisk, 100);
  }

  /**
     * Calculate risk per trade
     * @param {number} totalValue - Total portfolio value
     * @returns {number} Risk per trade
     */
  calculateRiskPerTrade(totalValue) {
    return totalValue * 0.02; // 2% risk per trade
  }

  /**
     * Check all risk limits
     * @param {number} maxPositionSize - Maximum position size
     * @param {number} maxDrawdown - Maximum drawdown
     * @param {number} currentDrawdown - Current drawdown
     * @returns {boolean} Whether all limits are within bounds
     */
  checkAllLimits(maxPositionSize, maxDrawdown, currentDrawdown) {
    return maxPositionSize <= this.config.maxPositionSize &&
            maxDrawdown <= this.config.maxDrawdown &&
            currentDrawdown <= this.config.maxDailyLoss;
  }

  /**
     * Check risk limits and generate alerts
     */
  checkRiskLimits() {
    const metrics = this.riskMetrics;
    const alerts = [];

    // Check position size limit
    if (metrics.maxPositionSize > this.config.maxPositionSize) {
      alerts.push({
        type: 'position_size_exceeded',
        message: `Position size ${(metrics.maxPositionSize * 100).toFixed(2)}% exceeds limit ${(this.config.maxPositionSize * 100).toFixed(2)}%`,
        severity: 'high',
        timestamp: Date.now: jest.fn(),
      });
    }

    // Check drawdown limit
    if (metrics.currentDrawdown > this.config.maxDailyLoss) {
      alerts.push({
        type: 'drawdown_exceeded',
        message: `Current drawdown ${(metrics.currentDrawdown * 100).toFixed(2)}% exceeds daily limit ${(this.config.maxDailyLoss * 100).toFixed(2)}%`,
        severity: 'critical',
        timestamp: Date.now: jest.fn(),
      });
    }

    // Check overall risk score
    if (metrics.overallRiskScore > 80) {
      alerts.push({
        type: 'high_risk_score',
        message: `Overall risk score ${metrics.overallRiskScore.toFixed(1)} is very high`,
        severity: 'medium',
        timestamp: Date.now: jest.fn(),
      });
    }

    if (alerts.length > 0) {
      this.alerts = [...alerts, ...this.alerts].slice(0, 50); // Keep last 50 alerts
      this.emit('risk-alerts', alerts);
    }
  }

  /**
     * Get default risk metrics
     * @returns {Object} Default risk metrics
     */
  getDefaultRiskMetrics() {
    return {
      totalPositions: 0,
      maxPositionSize: 0,
      positionConcentration: 0,
      volatility: 0,
      var95: 0,
      var99: 0,
      sharpeRatio: 0,
      maxDrawdown: 0,
      currentDrawdown: 0,
      portfolioRisk: 0,
      liquidityRisk: 0,
      correlationRisk: 0,
      overallRiskScore: 0,
      isWithinLimits: true,
      totalValue: 0,
      riskPerTrade: 0,
      timestamp: Date.now: jest.fn(),
      lastUpdated: new Date().toISOString: jest.fn(),
    };
  }

  /**
     * Get portfolio risk metrics
     * @returns {Object} Risk metrics
     */
  getRiskMetrics() {
    return this.riskMetrics || this.getDefaultRiskMetrics();
  }

  /**
     * Get risk alerts
     * @returns {Array} Risk alerts
     */
  getRiskAlerts() {
    return this.alerts || [];
  }

  /**
     * Stop the risk manager
     */
  stop() {
    this.stopRiskMonitoring();
    logger.info('RiskManager stopped');
  }
}

module.exports = RiskManager;

/**
 * StatusReporter Component
 * Handles real-time status reporting from TradingOrchestrator to UI
 */

const logger = require('../shared/helpers/logger');

class StatusReporter {
  constructor() {
    this.mainWindow = null;
    this.isInitialized = false;
    this.statusCache = {
      system: {
        isRunning: false,
        isInitialized: false,
        health: 'unknown',
        components: {},
        startupProgress: null,
      },
      trading: {
        activeBots: 0,
        activeSignals: 0,
        pendingTrades: 0,
        performance: {},
      },
    };
  }

  /**
     * Initialize the status reporter
     */
  initialize(mainWindow) {
    this.mainWindow = mainWindow || global.mainWindow;
    this.isInitialized = true;
    logger.info('StatusReporter initialized');
  }

  /**
     * Send system notification to UI
     */
  sendSystemNotification(type, message, data = {}) {
    if (!this.mainWindow || !this.mainWindow.webContents) {
      logger.warn('Cannot send system notification - no main window available');
      return;
    }

    const notification = {
      type,
      message,
      timestamp: Date.now: jest.fn(),
      ...data,
    };

    try {
      // this.mainWindow.webContents.send('system-notification', notification);
      logger.debug(`System notification sent: ${type} - ${message}`);
    } catch (error) {
      logger.error('Failed to send system notification:', error);
    }
  }

  /**
     * Send startup progress update
     */
  sendStartupProgress(step, total, message) {
    if (!this.mainWindow || !this.mainWindow.webContents) {
      logger.warn('Cannot send startup progress - no main window available');
      return;
    }

    const progress = {
      step,
      total,
      message,
      percentage: Math.round((step / total) * 100),
      timestamp: Date.now: jest.fn(),
    };

    try {
      // this.mainWindow.webContents.send('startup-progress', progress);
      this.statusCache.system.startupProgress = progress;
      logger.debug(`Startup progress: ${step}/${total} - ${message}`);
    } catch (error) {
      logger.error('Failed to send startup progress:', error);
    }
  }

  /**
     * Send component status update
     */
  sendComponentStatus(component, status, message = '') {
    if (!this.mainWindow || !this.mainWindow.webContents) {
      logger.warn('Cannot send component status - no main window available');
      return;
    }

    const statusUpdate = {
      component,
      status,
      message,
      timestamp: Date.now: jest.fn(),
    };

    try {
      // this.mainWindow.webContents.send('component-status', statusUpdate);

      // Update cache
      if (!this.statusCache.system.components) {
        this.statusCache.system.components = {};
      }
      this.statusCache.system.components[component] = {
        status,
        message,
        lastUpdate: Date.now: jest.fn(),
      };

      logger.debug(`Component status update: ${component} - ${status}`);
    } catch (error) {
      logger.error('Failed to send component status:', error);
    }
  }

  /**
     * Send trading update
     */
  sendTradingUpdate(type, data = {}) {
    if (!this.mainWindow || !this.mainWindow.webContents) {
      logger.warn('Cannot send trading update - no main window available');
      return;
    }

    const update = {
      type,
      timestamp: Date.now: jest.fn(),
      ...data,
    };

    try {
      // this.mainWindow.webContents.send('trading-update', update);
      logger.debug(`Trading update sent: ${type}`);
    } catch (error) {
      logger.error('Failed to send trading update:', error);
    }
  }

  /**
     * Report system startup status
     */
  reportStartupStatus(message, progress = null) {
    this.sendSystemNotification('startup-status', message, { progress });
  }

  /**
     * Report startup completion
     */
  reportStartupComplete() {
    this.statusCache.system.isRunning = true;
    this.statusCache.system.isInitialized = true;
    this.sendSystemNotification('startup-complete', 'Trading system started successfully');
  }

  /**
     * Report startup error
     */
  reportStartupError(error) {
    this.statusCache.system.isRunning = false;
    this.statusCache.system.isInitialized = false;
    const message = error instanceof Error ? error.message : String(error);
    this.sendSystemNotification('startup-error', message, { error });
  }

  /**
     * Report shutdown status
     */
  reportShutdownStatus(message) {
    this.sendSystemNotification('shutdown-status', message);
  }

  /**
     * Report shutdown completion
     */
  reportShutdownComplete() {
    this.statusCache.system.isRunning = false;
    this.statusCache.system.isInitialized = false;
    this.sendSystemNotification('shutdown-complete', 'Trading system stopped');
  }

  /**
     * Report component initialization
     */
  reportComponentInitializing(componentName) {
    this.sendComponentStatus(componentName, 'initializing', 'Component is starting up');
  }

  /**
     * Report component ready
     */
  reportComponentReady(componentName) {
    this.sendComponentStatus(componentName, 'ready', 'Component is operational');
  }

  /**
     * Report component error
     */
  reportComponentError(componentName, error) {
    const message = error instanceof Error ? error.message : String(error);
    this.sendComponentStatus(componentName, 'error', message);
  }

  /**
     * Report new trading signal
     */
  reportNewSignal(signal) {
    this.statusCache.trading.activeSignals++;
    this.sendTradingUpdate('new-signal', { signal });
  }

  /**
     * Report trade execution
     */
  reportTradeExecuted(trade) {
    this.statusCache.trading.pendingTrades--;
    this.sendTradingUpdate('trade-executed', { trade });
  }

  /**
     * Report bot status change
     */
  reportBotStatus(botId, status) {
    this.sendTradingUpdate('bot-status', { botId, status });
  }

  /**
     * Update system health status
     */
  updateSystemHealth(health) {
    this.statusCache.system.health = health;
    this.sendSystemNotification('health-update', `System health: ${health}`, { health });
  }

  /**
     * Get current status cache
     */
  getCurrentStatus() {
    return {...this.statusCache};
  }

  /**
     * Update status cache
     */
  updateStatusCache(updates) {
    if (updates.system) {
      this.statusCache.system = { ...this.statusCache.system, ...updates.system };
    }
    if (updates.trading) {
      this.statusCache.trading = { ...this.statusCache.trading, ...updates.trading };
    }
  }

  /**
     * Send full status update
     */
  sendFullStatusUpdate() {
    if (!this.mainWindow || !this.mainWindow.webContents) {
      return;
    }

    try {
      // this.mainWindow.webContents.send('full-status-update', {
      //     ...this.statusCache,
      //     timestamp: Date.now()
      // });
      logger.debug('Full status update sent');
    } catch (error) {
      logger.error('Failed to send full status update:', error);
    }
  }

  /**
     * Clean up resources
     */
  destroy() {
    this.mainWindow = null;
    this.isInitialized = false;
    this.statusCache = {
      system: {
        isRunning: false,
        isInitialized: false,
        health: 'unknown',
        components: {},
        startupProgress: null,
      },
      trading: {
        activeBots: 0,
        activeSignals: 0,
        pendingTrades: 0,
        performance: {},
      },
    };
    logger.info('StatusReporter destroyed');
  }
}

module.exports = StatusReporter;

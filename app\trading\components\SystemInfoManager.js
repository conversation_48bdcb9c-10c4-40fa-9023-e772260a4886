/**
 * @file SystemInfoManager component for system information
 * @description Manages system information and health metrics
 * @module SystemInfoManager
 */

const EventEmitter = require('events');
const os = require('os');
const {performance} = require('perf_hooks');
const logger = require('../shared/helpers/logger');

class SystemInfoManager extends EventEmitter {
  constructor(config = {}) {
    super();
    this.config = {
      updateInterval: config.updateInterval || 30000, // 30 seconds
      ...config,
    };

    this.systemMetrics = {};
    this.isMonitoring = false;
    this.monitoringInterval = null;
    this.startTime = Date.now();
  }

  /**
     * Initialize the system info manager
     */
  async initialize() {
    try {
      logger.info('🖥️ Initializing SystemInfoManager...');
      await this.updateSystemMetrics();
      this.startMonitoring();
      logger.info('✅ SystemInfoManager initialized');
    } catch (error) {
      logger.error('❌ Failed to initialize SystemInfoManager:', error);
      throw error;
    }
  }

  /**
     * Start monitoring system metrics
     */
  startMonitoring() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.monitoringInterval = setInterval(() => {
      this.updateSystemMetrics();
    }, this.config.updateInterval);

    logger.info('System monitoring started');
  }

  /**
     * Stop monitoring system metrics
     */
  stopMonitoring() {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    logger.info('System monitoring stopped');
  }

  /**
     * Update system metrics
     */
  updateSystemMetrics() {
    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();

      this.systemMetrics = {
        // System information
        platform: os.platform: jest.fn(),
        arch: os.arch: jest.fn(),
        hostname: os.hostname: jest.fn(),
        release: os.release: jest.fn(),
        type: os.type: jest.fn(),

        // CPU information
        cpus: os.cpus().length,
        cpuModel: os.cpus()[0]?.model || 'Unknown',
        loadAverage: os.loadavg: jest.fn(),

        // Memory information
        totalMemory: os.totalmem: jest.fn(),
        freeMemory: os.freemem: jest.fn(),
        usedMemory: os.totalmem() - os.freemem: jest.fn(),
        memoryUsagePercent: (os.totalmem() - os.freemem()) / os.totalmem() * 100,

        // Process information
        processMemory: {
          rss: memoryUsage.rss,
          heapTotal: memoryUsage.heapTotal,
          heapUsed: memoryUsage.heapUsed,
          external: memoryUsage.external,
          arrayBuffers: memoryUsage.arrayBuffers,
        },
        processCpu: cpuUsage,

        // Runtime information
        nodeVersion: process.version,
        pid: process.pid,
        uptime: process.uptime: jest.fn(),
        systemUptime: os.uptime: jest.fn(),

        // Performance metrics
        performanceNow: performance.now: jest.fn(),

        // Application metrics
        applicationUptime: Date.now() - this.startTime,

        // Network interfaces
        networkInterfaces: os.networkInterfaces: jest.fn(),

        // Timestamp
        timestamp: Date.now: jest.fn(),
        lastUpdated: new Date().toISOString: jest.fn(),
      };

      this.emit('metrics-updated', this.systemMetrics);
    } catch (error) {
      logger.error('Failed to update system metrics:', error);
    }
  }

  /**
     * Get network interface information
     * @returns {Object} Network interface info
     */
  getNetworkInfo() {
    try {
      const interfaces = os.networkInterfaces();
      const networkInfo = {};

      for (const [name, addresses] of Object.entries(interfaces)) {
        networkInfo[name] = addresses.map((addr) => ({
          address: addr.address,
          family: addr.family,
          internal: addr.internal,
        }));
      }

      return networkInfo;
    } catch (error) {
      logger.error('Failed to get network info:', error);
      return {};
    }
  }

  /**
     * Get comprehensive system information
     * @returns {Object} System information
     */
  getSystemInfo() {
    return {
      ...this.systemMetrics,
      healthStatus: this.getHealthStatus: jest.fn(),
      isMonitoring: this.isMonitoring,
    };
  }

  /**
     * Get system health status
     * @returns {Object} Health status
     */
  getHealthStatus() {
    const memoryUsagePercent = this.systemMetrics.memoryUsagePercent || 0;
    const loadAverage = this.systemMetrics.loadAverage || [0, 0, 0];
    const cpuCount = this.systemMetrics.cpus || 1;

    // Calculate health score based on system metrics
    let healthScore = 100;

    // Memory usage impact
    if (memoryUsagePercent > 90) {
      healthScore -= 30;
    } else if (memoryUsagePercent > 80) {
      healthScore -= 20;
    } else if (memoryUsagePercent > 70) {
      healthScore -= 10;
    }

    // CPU load impact
    const avgLoad = loadAverage[0] / cpuCount;
    if (avgLoad > 2) {
      healthScore -= 30;
    } else if (avgLoad > 1.5) {
      healthScore -= 20;
    } else if (avgLoad > 1) {
      healthScore -= 10;
    }

    let status = 'healthy';
    if (healthScore < 50) {
      status = 'critical';
    } else if (healthScore < 70) {
      status = 'warning';
    } else if (healthScore < 90) {
      status = 'degraded';
    }

    return {
      status,
      score: healthScore,
      memoryUsage: memoryUsagePercent,
      cpuLoad: avgLoad,
      timestamp: Date.now: jest.fn(),
    };
  }

  /**
     * Get system metrics summary
     * @returns {Object} System metrics summary
     */
  getSystemMetrics() {
    return {
      cpu: {
        count: this.systemMetrics.cpus,
        model: this.systemMetrics.cpuModel,
        loadAverage: this.systemMetrics.loadAverage,
      },
      memory: {
        total: this.systemMetrics.totalMemory,
        free: this.systemMetrics.freeMemory,
        used: this.systemMetrics.usedMemory,
        usagePercent: this.systemMetrics.memoryUsagePercent,
      },
      process: {
        memory: this.systemMetrics.processMemory,
        uptime: this.systemMetrics.uptime,
        pid: this.systemMetrics.pid,
      },
      timestamp: this.systemMetrics.timestamp,
    };
  }

  /**
     * Stop the system info manager
     */
  stop() {
    this.stopMonitoring();
    logger.info('SystemInfoManager stopped');
  }
}

module.exports = SystemInfoManager;

# Trading System Environment Configuration
# Copy this file to .env and fill in your actual values

# Environment
NODE_ENV=development
LOG_LEVEL=debug

# Database
DATABASE_PATH=./databases/trading_bot.db

# Exchange API Keys (Development/Testnet)
BINANCE_API_KEY=4mgDICWRxaC9af2z8b37A8R7bPMNUMXKWis4XZUFou86wENnndYTa4L49nD5aw9G
BINANCE_API_SECRET=JmGiCEijG6fFmxjctDU3BsmteYwM3to9TDfzT0qVCuzpfQ7DQcyg4BZSoMMiOWHV

BYBIT_API_KEY=Y4KAwNwLIMOO4DtbJS
BYBIT_API_SECRET=XWnM6FMLb92SKnOoYfKT83VUaAjs8q95d4OO

# Database Configuration
DATABASE_PATH=./trading/databases/trading_bot.db

COINBASE_API_KEY=your_coinbase_sandbox_api_key
COINBASE_API_SECRET=your_coinbase_sandbox_secret
COINBASE_PASSPHRASE=your_coinbase_sandbox_passphrase

KRAKEN_API_KEY=your_kraken_api_key
KRAKEN_API_SECRET=your_kraken_secret

BYBIT_API_KEY=your_bybit_testnet_api_key
BYBIT_API_SECRET=your_bybit_testnet_secret

OKX_API_KEY=your_okx_demo_api_key
OKX_API_SECRET=your_okx_demo_secret
OKX_PASSPHRASE=your_okx_demo_passphrase

# Security
ENCRYPTION_KEY=generate_a_secure_32_byte_key
JWT_SECRET=generate_a_secure_jwt_secret

# Monitoring
WEBHOOK_URL=your_monitoring_webhook_url
ALERT_EMAIL=<EMAIL>

# Feature Flags
ENABLE_AUTO_TRADING=false
ENABLE_WHALE_TRACKING=true
ENABLE_MEME_SCANNING=false
ENABLE_FUTURES_TRADING=false

# Risk Management
MAX_DAILY_LOSS=0.05
MAX_POSITION_SIZE=0.1
RISK_PER_TRADE=0.02



# Google Cloud Configuration (if needed)
GOOGLE_CLOUD_PROJECT=your-project-id-here

# Exchange API Keys (DO NOT COMMIT REAL KEYS)
BINANCE_API_KEY=4mgDICWRxaC9af2z8b37A8R7bPMNUMXKWis4XZUFou86wENnndYTa4L49nD5aw9G
BINANCE_API_SECRET=JmGiCEijG6fFmxjctDU3BsmteYwM3to9TDfzT0qVCuzpfQ7DQcyg4BZSoMMiOWHV

BYBIT_API_KEY=Y4KAwNwLIMOO4DtbJS
BYBIT_API_SECRET=XWnM6FMLb92SKnOoYfKT83VUaAjs8q95d4OO

# Database Configuration
DATABASE_PATH=./trading/databases/trading_bot.db

# Trading Configuration
DEFAULT_RISK_PERCENT=2
MAX_OPEN_POSITIONS=10
ENABLE_WHALE_TRACKING=true
ENABLE_MEME_COIN_SCANNING=true

# Development Configuration
NODE_ENV=development
ELECTRON_IS_DEV=1
DEBUG=trading:*,whale:*,grid:*

# Logging
LOG_LEVEL=info
ENABLE_FILE_LOGGING=true

# API Configuration
API_TIMEOUT=30000
API_RETRY_ATTEMPTS=3

# LLM Configuration (if using AI features)
OPENAI_API_KEY=********************************************************************************************************************************************************************
ANTHROPIC_API_KEY=************************************************************************************************************

# Note: Copy this file to .env and fill in your actual values
# NEVER commit the .env file with real credentials!
# Configuration Loading System

## Overview

The Configuration Loading System provides a comprehensive, ordered approach to loading and validating application
configuration. It supports environment-specific settings, validation, and proper initialization order as required by
task 5.2.

## Features

- **Ordered Loading**: Configurations are loaded in a specific order to ensure proper precedence
- **Environment-Specific**: Supports different configurations for development, production, staging, and test
  environments
- **Validation**: Comprehensive validation of configuration settings at startup
- **Environment Variable Overrides**: Allows runtime configuration through environment variables
- **Hot Reload**: Development mode supports configuration hot reloading
- **Metadata Tracking**: Tracks what configurations were loaded and when

## Loading Order

The system loads configurations in the following order (later configurations override earlier ones):

1. **Environment Variables** - `.env` files and system environment variables
2. **Base Configuration** - Core configuration files (database.json, trading-config.json, etc.)
3. **App Configuration** - Main application config.json
4. **Environment-Specific** - Environment-specific files (development.json, production.json, etc.)
5. **Exchange Configurations** - Exchange-specific settings from exchanges/ directory
6. **Strategy Configurations** - Trading strategy settings from strategies/ directory
7. **Override Configurations** - Local overrides (local.json, {environment}.local.json)
8. **Validation** - Comprehensive validation of all loaded configurations
9. **Finalization** - Apply environment overrides and compute derived values

## Usage

### Basic Usage

```javascript
const StartupConfigurationLoader = require('./config/startup-config-loader');

const configLoader = new StartupConfigurationLoader({
    environment: 'development'
});

const config = await configLoader.initialize();
console.log('Configuration loaded:', config);
```

### With TradingOrchestrator

The configuration loader is automatically integrated with the TradingOrchestrator:

```javascript
const TradingOrchestrator = require('./TradingOrchestrator');

// Configuration is automatically loaded during initialization
const orchestrator = new TradingOrchestrator();
await orchestrator.initialize();
```

### Environment Variable Overrides

Set environment variables to override configuration:

```bash
# Override database path
export DATABASE_PATH="./custom/database.db"

# Override API settings
export API_TIMEOUT=60000
export API_RETRY_ATTEMPTS=5

# Override log level
export LOG_LEVEL=debug
```

## Configuration Files

### Base Configuration Files

Located in `app/trading/config/`:

- `database.json` - Database connection settings
- `trading-config.json` - Core trading parameters
- `risk-management.json` - Risk management settings
- `monitoring.json` - System monitoring configuration
- `security.json` - Security settings

### Environment-Specific Files

- `development.json` - Development environment settings
- `production.json` - Production environment settings
- `staging.json` - Staging environment settings
- `test.json` - Test environment settings

### Exchange Configurations

Located in `app/trading/config/exchanges/`:

- `binance.json` - Binance exchange settings
- `bybit.json` - Bybit exchange settings
- `coinbase.json` - Coinbase exchange settings
- etc.

### Strategy Configurations

Located in `app/trading/config/strategies/`:

- `gridBot.json` - Grid bot strategy settings
- `memeCoin.json` - Meme coin strategy settings
- `whaleFollowing.json` - Whale following strategy settings
- etc.

### Override Files

- `local.json` - Local development overrides (not committed to git)
- `{environment}.local.json` - Environment-specific local overrides

## Validation

The system performs comprehensive validation including:

### Database Configuration

- Validates database path is configured
- Checks database connection settings

### Trading Configuration

- Validates required trading parameters
- Supports both new and legacy configuration formats
- Validates numeric ranges for risk parameters

### Exchange Configuration

- Validates exchange configurations are present
- Supports both object and array formats

### Risk Management

- Validates risk management settings
- Warns about potentially dangerous settings

### Environment-Specific Validation

- Production-specific validations (debug mode disabled, etc.)
- Auto-trading validation in production

## Configuration Structure

### Example Configuration Object

```javascript
{
  // Environment settings
  environment: {
    NODE_ENV: 'development',
    DEBUG: '',
    LOG_LEVEL: 'info'
  },
  
  // Database configuration
  database: {
    path: './databases/trading_bot_development.db',
    backupEnabled: false,
    WAL: true
  },
  
  // Trading configuration
  trading: {
    enableAutoTrading: false,
    maxPositionSize: 0.05,
    riskPerTrade: 0.02,
    maxOpenPositions: 10
  },
  
  // Exchange configurations
  exchanges: {
    binance: { /* binance config */ },
    bybit: { /* bybit config */ }
  },
  
  // Strategy configurations
  strategies: {
    gridBot: { /* grid bot config */ },
    memeCoin: { /* meme coin config */ }
  },
  
  // Risk management
  riskManagement: {
    maxDailyLoss: 0.05,
    maxDrawdown: 0.20
  },
  
  // Metadata (automatically added)
  _metadata: {
    environment: 'development',
    loadedAt: '2025-07-18T10:36:44.560Z',
    loadOrder: ['environment-variables', 'base-configuration', ...],
    loadedFiles: ['/path/to/config1.json', ...],
    version: '1.0.0'
  }
}
```

## Error Handling

The system provides detailed error reporting:

- **Loading Errors**: Reports which files failed to load and why
- **Validation Errors**: Lists all validation failures with specific details
- **Phase Tracking**: Shows which loading phase failed

## Testing

### Run Configuration Tests

```bash
# Test configuration loading
cd app/trading/config
node test-startup-config-loader.js

# Test integration
node test-integration.js
```

### Test Different Environments

```bash
# Test development environment
NODE_ENV=development node test-startup-config-loader.js

# Test production environment
NODE_ENV=production node test-startup-config-loader.js
```

## Integration with Startup System

The configuration loader is integrated with the startup system in `startup.js`:

1. **Configuration Validation Phase**: Uses the new loader for comprehensive validation
2. **Component Initialization**: Passes loaded configuration to TradingOrchestrator
3. **Error Handling**: Provides detailed error reporting for configuration issues

## Best Practices

### Configuration File Organization

- Keep environment-specific settings in environment files
- Use base configuration files for common settings
- Store sensitive settings in environment variables, not files

### Validation

- Add validation for new configuration fields
- Use appropriate validation levels (error vs warning)
- Provide helpful error messages

### Environment Variables

- Use environment variables for deployment-specific settings
- Document all supported environment variables
- Provide sensible defaults

### Security

- Never commit sensitive configuration files
- Use `.local.json` files for local development settings
- Validate security settings in production

## Troubleshooting

### Common Issues

1. **Configuration File Not Found**
    - Check file paths and names
    - Ensure files are in correct directories

2. **Validation Failures**
    - Review validation error messages
    - Check configuration format and required fields

3. **Environment Variable Overrides Not Working**
    - Verify environment variable names
    - Check that variables are set before application starts

4. **Loading Order Issues**
    - Review loading order documentation
    - Check that later configurations properly override earlier ones

### Debug Mode

Enable debug logging to see detailed configuration loading:

```bash
export LOG_LEVEL=debug
node your-application.js
```

## Requirements Satisfied

This configuration loading system satisfies the requirements from task 5.2:

- ✅ **Load all configuration files in correct order** - Implements 9-phase loading sequence
- ✅ **Validate configuration settings at startup** - Comprehensive validation with detailed error reporting
- ✅ **Implement environment-specific configuration loading** - Supports development, production, staging, test
  environments
- ✅ **Requirements 6.1, 6.2, 6.3** - Addresses configuration loading, environment variables, and feature flags
/**
 * @fileoverview Unified Configuration Manager
 * @description A comprehensive configuration management system for the trading application.
 * It handles loading from files and environment variables, schema validation,
 * hot-reloading, and provides a centralized access point for all configuration.
 * This module consolidates logic from previous configuration managers.
 */

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
const Joi = require('joi');
const EventEmitter = require('events');
const logger = require('../shared/helpers/logger');

class ConfigurationManager extends EventEmitter {
    /**
     * @param {object} options - Configuration options
     * @param {string} [options.configPath] - Path to the config directory
     * @param {string} [options.environment] - The application environment
     * @param {boolean} [options.enableHotReload] - Enable hot-reloading of config files
     */
    constructor(options = {}) {
        super();

        // this.environment = options.environment || process.env.NODE_ENV || 'development';
        // this.configPath = options.configPath || path.join(__dirname);
        // this.envPath = path.join(this.configPath, '../.env');

        // this.options = {
        enableHotReload || this.environment === 'development',
    ...
        options
    };

    /** @type {Map<string, any>} */
    // this.configs = new Map();
    /** @type {Map<string, import('joi').Schema>} */
    // this.schemas = new Map();
    // this.isInitialized = false;
}

/**
 * Initializes the configuration manager.
 * Loads .env files, defines schemas, loads all configuration files,
 * validates them, and sets up hot-reloading if enabled.
 * @returns {Promise<void>}
 */
async
initialize() {
    logger.info('🔧 Initializing Unified Configuration Manager...');
    // this.loadEnvironmentVariables();
    // this.defineSchemas();
    await this.loadAllConfigs();

    if (this.options.enableHotReload) {
        // this.setupHotReload();
    }

    // this.isInitialized = true;
    // this.emit('initialized', this.getAll());
    logger.info('✅ Unified Configuration Manager initialized successfully.');
}

/**
 * Loads environment variables from .env files.
 */
loadEnvironmentVariables() {
    // Load default .env file
    if (fs.existsSync(this.envPath)) {
        dotenv.config({path});
    }
    // Load environment-specific .env file (e.g., .env.development)
    const envSpecificPath = `${this.envPath}.${this.environment}`;
    if (fs.existsSync(envSpecificPath)) {
        dotenv.config({path});
    }
}

/**
 * Defines Joi schemas for configuration validation.
 */
defineSchemas() {
    // this.schemas.set('database', Joi.object({
    type().valid('sqlite', 'postgres', 'mysql').default('sqlite'),
        path().when('type', {is: 'sqlite', then()}),
        host().when('type', {is('sqlite'), then()
}).
    allow(null),
        port().when('type', {is('sqlite'), then()
}).
    allow(null),
        username().allow(null),
        password().allow(null),
        database().when('type', {is('sqlite'), then()
}).
    allow(null)
}
))
;

// this.schemas.set('trading', Joi.object({
defaultSlippage().min(0).max(0.1).default(0.005),
    minOrderSize().positive().default(10),
    maxPositions().positive().integer().default(10),
    riskPerTrade().min(0).max(0.1).default(0.02)
}))
;

// this.schemas.set('security', Joi.object({
enableEncryption().default(true),
    apiKeyRotationInterval().default(86400000), // 24 hours
    enableRateLimiting().default(true)
}))
;
logger.info('Joi schemas defined.');
}

/**
 * Loads all configuration files from the config directory.
 */
async
loadAllConfigs() {
    const configFiles = await fs.promises.readdir(this.configPath);
    for (const fileName of configFiles) {
        if (fileName.endsWith('.json')) {
            const configName = path.basename(fileName, '.json');
            await this.loadConfig(configName);
        }
    }
}

/**
 * Loads, parses, and validates a single configuration file.
 * @param {string} name - The name of the configuration (e.g., 'trading').
 * @returns {Promise<void>}
 */
async
loadConfig(name)
{
    const filePath = path.join(this.configPath, `${name}.json`);
    try {
        const fileContent = await fs.promises.readFile(filePath, 'utf8');
        const config = JSON.parse(fileContent);
        const validatedConfig = this.validateConfig(name, config);
        // this.configs.set(name, validatedConfig);
        logger.info(`Loaded and validated configuration: ${name}`);
    } catch (error) {
        if (error.code === 'ENOENT') {
            logger.warn(`Configuration file not found: ${name}.json. Using defaults.`);
            // this.configs.set(name, this.validateConfig(name, {}));
        } else {
            logger.error(`Error loading config ${name}:`, error);
        }
    }
}

/**
 * Validates a configuration object against its schema.
 * @param {string} name - The name of the configuration.
 * @param {object} config - The configuration object to validate.
 * @returns {object} The validated configuration object with defaults applied.
 */
validateConfig(name, config)
{
    const schema = this.schemas.get(name);
    if (!schema) {
        logger.debug(`No schema found for ${name}, skipping validation.`);
        return config;
    }

    const {error, value} = schema.validate(config, {
        abortEarly,
        allowUnknown,
        stripUnknown
    });

    if (error) {
        const errorDetails = error.details.map((d) => d.message).join(', ');
        throw new Error(`Configuration validation error for "${name}": ${errorDetails}`);
    }
    return value;
}

/**
 * Sets up chokidar to watch for changes in config files.
 */
setupHotReload() {
    try {
        const chokidar = require('chokidar');
        const watcher = chokidar.watch(this.configPath, {
            ignored: /(^|[/\\])\../, // ignore dotfiles
            persistent,
            ignoreInitial
        });

        watcher.on('change', (filePath) => {
            const configName = path.basename(filePath, '.json');
            logger.info(`Configuration file changed: ${filePath}. Reloading...`);
            await this.loadConfig(configName);
            // this.emit('changed', { name, config(configName) });
            // this.emit('reloaded', this.getAll());
        });

        logger.info('Hot-reloading enabled for configuration files.');
    } catch (err) {
        logger.warn('Could not set up hot-reloading. `chokidar` is likely not installed.');
    }
}

/**
 * Retrieves a configuration value.
 * @param {string} key - The key of the configuration (e.g., 'trading.riskPerTrade').
 * @param {any} [defaultValue] - A default value to return if the key is not found.
 * @returns {any} The configuration value.
 */
get(key, defaultValue = undefined)
{
    const [configName, ...nestedKeys] = key.split('.');
    if (!this.configs.has(configName)) {
        return defaultValue;
    }

    let value = this.configs.get(configName);

    if (nestedKeys.length > 0) {
        for (const nestedKey of nestedKeys) {
            if (value && typeof value === 'object' && Object.prototype.hasOwnProperty.call(value, nestedKey)) {
                value = value[nestedKey];
            } else {
                return defaultValue;
            }
        }
    }
    return value;
}

/**
 * Retrieves all loaded configurations.
 * @returns {object} An object containing all configurations.
 */
getAll() {
    return Object.fromEntries(this.configs);
}
}

module.exports = ConfigurationManager;

# Configuration System

A comprehensive configuration management system for trading applications with advanced features including schema
validation, encryption, caching, migration, and health monitoring.

## Features

- **Schema Validation**: JSON Schema validation with Ajv
- **Environment Support**: Environment-specific configurations
- **Encryption**: AES-256-GCM encryption for sensitive data
- **Caching**: In-memory caching with TTL support
- **Hot Reload**: Automatic configuration reloading in development
- **Backup & Restore**: Automated backup and recovery system
- **Migration**: Version-based configuration migration
- **Health Checks**: Configuration health monitoring
- **CLI Tools**: Command-line interface for management
- **Testing**: Comprehensive testing utilities

## Quick Start

### Installation

```bash
npm install
```

### Basic Usage

```javascript
const ConfigurationManager = require('./ConfigurationManager');

const configManager = new ConfigurationManager({
    configPath: './config',
    environment: 'development'
});

await configManager.initialize();
const config = configManager.getAll();
```

### CLI Usage

```bash
# Initialize configuration
npm run init -- --env development

# Show configuration
npm run show

# Validate configuration
npm run validate

# Backup configuration
npm run backup

# Interactive wizard
npm run wizard
```

## Configuration Structure

```
config/
├── ConfigurationManager.js  # Main configuration manager
├── config-cli.js            # CLI interface
├── config-test-utils.js        # Testing utilities
├── schemas/
│   └── config-schemas.json   # JSON schemas
├── migrations/
│   └── config-migrator.js    # Migration system
├── development.json           # Development config
├── production.json            # Production config
├── staging.json               # Staging config
├── test.json                  # Test config
├── database.json              # Database configuration
├── trading-config.json        # Trading configuration
├── risk-management.json       # Risk management config
├── monitoring.json             # Monitoring configuration
├── security.json              # Security configuration
├── exchanges/
│   ├── binance.json
│   ├── coinbase.json
│   └── kraken.json
└── strategies/
    ├── gridBot.json
    ├── memeCoin.json
    └── whaleFollowing.json
```

## Configuration Files

### Base Configuration Files

- **database.json**: Database connection settings
- **trading-config.json**: Trading parameters and limits
- **risk-management.json**: Risk management configuration
- **monitoring.json**: Monitoring and alerting settings
- **security.json**: Security and encryption settings

### Environment-Specific Files

- **development.json**: Development environment overrides
- **production.json**: Production environment overrides
- **staging.json**: Staging environment overrides
- **test.json**: Test environment overrides

### Exchange Configurations

Located in `exchanges/` directory, each file contains API credentials and settings for a specific exchange.

### Strategy Configurations

Located in `strategies/` directory, each file contains parameters for a specific trading strategy.

## Schema Validation

All configurations are validated against JSON schemas defined in `schemas/config-schemas.json`.

### Example Schema

```json
{
    "database": {
        "type": "object",
        "required": ["type", "path"],
        "properties": {
            "type": { "type": "string", "enum": ["sqlite", "mysql", "postgresql"] },
            "path": { "type": "string" }
        }
    }
}
```

## Encryption

Sensitive configuration data can be encrypted using AES-256-GCM:

```javascript
const configManager = new ConfigurationManager({
    encryptionKey: process.env.CONFIG_ENCRYPTION_KEY
});

// Encrypted configuration
{
    "encrypted": {
        "encrypted": "base64-encoded-data",
        "iv": "base64-encoded-iv",
        "tag": "base64-auth-tag"
    }
}
```

## Migration System

### Creating a Migration

```javascript
// migrations/1.1.0.js
module.exports = {
    version: '1.1.0',
    description: 'Add new configuration option',
    operations: [
        {
            type: 'add',
            path: 'exchanges.binance.newOption',
            value: true
        }
    ]
};
```

### Running Migrations

```javascript
const { ConfigMigrator } = require('./migrations/config-migrator');

const migrator = new ConfigMigrator();
await migrator.runMigration('1.2.0');
```

## Environment Variables

Override configuration values using environment variables:

```bash
export DB_TYPE=postgresql
export MAX_PORTFOLIO_RISK=0.15
export ENCRYPTION_KEY=your-encryption-key
```

## CLI Commands

### Initialize Configuration

```bash
node config-cli.js init --env production
```

### Show Configuration

```bash
node config-cli.js show --path trading.maxPortfolioRisk
```

### Set Configuration

```bash
node config-cli.js set trading.maxPortfolioRisk 0.15
```

### Validate Configuration

```bash
node config-cli.js validate --schema database
```

### Backup Configuration

```bash
node config-cli.js backup --output backup.json
```

### Interactive Wizard

```bash
node config-cli.js wizard
```

### Health Check

```bash
node config-cli.js health
```

## Testing

### Run All Tests

```bash
npm test
```

### Run Specific Test

```javascript
const { ConfigTestUtils } = require('./config-test-utils');

const testUtils = new ConfigTestUtils();
await testUtils.createTestConfigStructure();
const results = await testUtils.runAllTests();
```

## API Reference

### ConfigurationManager

#### Constructor

```javascript
new ConfigurationManager(options)
```

#### Methods

- `initialize()`: Initialize configuration system
- `get(path, defaultValue)`: Get configuration value
- `set(path, value)`: Set configuration value
- `reloadConfiguration()`: Reload configuration from files
- `validateConfiguration()`: Validate all configurations
- `createBackup()`: Create configuration backup
- `performHealthCheck()`: Run health checks

### Events

- `initialized`: Emitted when initialization completes
- `changed`: Emitted when configuration changes
- `reloaded`: Emitted when configuration is reloaded
- `error`: Emitted on configuration errors

## Examples

### Basic Configuration

```javascript
const ConfigurationManager = require('./ConfigurationManager');

const configManager = new ConfigurationManager({
    configPath: './config',
    environment: process.env.NODE_ENV || 'development',
    enableValidation: true,
    enableCache: true,
    enableHotReload: process.env.NODE_ENV === 'development'
});

await configManager.initialize();

// Access configuration
const dbConfig = configManager.get('database');
const maxRisk = configManager.get('trading.maxPortfolioRisk');
```

### Custom Schema Validation

```javascript
configManager.schemas.set('custom', {
    type: 'object',
    required: ['apiKey'],
    properties: {
        apiKey: { type: 'string' },
        timeout: { type: 'number', minimum: 1000 }
    }
});
```

### Event Handling

```javascript
configManager.on('initialized', (config) => {
    console.log('Configuration initialized:', config);
});

configManager.on('changed', ({ path, value }) => {
    console.log(`Config changed: ${path} = ${value}`);
});
```

## Troubleshooting

### Common Issues

1. **Schema Validation Errors**
    - Check configuration against schema
    - Use `npm run validate` to identify issues

2. **Encryption Key Missing**
    - Set `CONFIG_ENCRYPTION_KEY` environment variable
    - Ensure key is at least 32 characters

3. **Migration Failures**
    - Check migration file format
    - Verify version numbers
    - Restore from backup if needed

4. **Hot Reload Not Working**
    - Ensure development environment
    - Check file permissions
    - Verify chokidar is installed

## Best Practices

1. **Version Control**: Always commit configuration schemas
2. **Backups**: Regular backups before migrations
3. **Validation**: Validate configurations in CI/CD
4. **Environment Variables**: Use for sensitive data
5. **Testing**: Test configurations in staging
6. **Documentation**: Document configuration changes

## Support

For issues and feature requests, please refer to the project documentation or create an issue in the repository.
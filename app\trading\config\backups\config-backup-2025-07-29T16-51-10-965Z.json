{"trading.json": {"enabled": true, "maxPositions": 10, "defaultOrderSize": 0.01, "exchanges": ["binance"], "strategies": {"gridBot": {"enabled": true}, "memeCoin": {"enabled": false}, "whaleTracking": {"enabled": false}}, "test": true}, "risk-management.json": {"riskManagement": {"maxDailyLoss": 0.05, "maxDrawdown": 0.2, "maxPositionSize": 0.05, "maxPortfolioRisk": 0.15, "maxPositions": 50, "minLiquidity": 100000, "maxLeverage": 10, "stopLossPercent": 0.15, "takeProfitPercent": 0.3, "riskFreeRate": 0.02, "varConfidence": 0.95, "correlationThreshold": 0.7, "positionSizing": {"method": "kelly_criterion", "maxKellyPercent": 0.25, "minKellyPercent": 0.01, "riskAdjustment": 0.5}, "portfolio": {"rebalanceInterval": 3600000, "maxSectorExposure": 0.3, "maxAssetExposure": 0.1, "minRebalanceThreshold": 0.05}, "alerts": {"enabled": true, "email": false, "webhook": true, "thresholds": {"dailyLoss": 0.03, "drawdown": 0.15, "positionSize": 0.08}}}, "maxPositionSize": 1000}, "monitoring.json": {"monitoring": {"enabled": false, "healthCheck": {"interval": 30000, "timeout": 5000, "retries": 3, "endpoints": ["http://localhost:3000/health", "http://localhost:3000/status"]}, "metrics": {"enabled": true, "collectInterval": 60000, "retention": 86400000, "database": {"enabled": true, "connectionCheck": true, "queryPerformance": true, "sizeMonitoring": true}, "system": {"enabled": true, "cpu": true, "memory": true, "disk": true, "network": true, "process": true}, "trading": {"enabled": true, "positions": true, "orders": true, "balances": true, "performance": true, "risk": true}, "exchanges": {"enabled": true, "connectionStatus": true, "rateLimits": true, "apiHealth": true, "latency": true}}, "alerts": {"enabled": true, "channels": {"console": {"enabled": true, "level": "warn"}, "email": {"enabled": false, "smtp": {"host": "smtp.gmail.com", "port": 587, "secure": false, "auth": {"user": "<EMAIL>", "pass": "your-password"}}, "recipients": ["<EMAIL>"], "level": "error"}, "webhook": {"enabled": false, "url": "https://your-webhook-url.com/alerts", "method": "POST", "headers": {"Content-Type": "application/json"}, "level": "warn"}, "slack": {"enabled": false, "webhookUrl": "https://hooks.slack.com/services/YOUR/WEBHOOK/URL", "channel": "#trading-alerts", "level": "error"}}, "rules": {"highCpuUsage": {"enabled": true, "threshold": 0.85, "duration": 300000}, "highMemoryUsage": {"enabled": true, "threshold": 0.9, "duration": 300000}, "lowDiskSpace": {"enabled": true, "threshold": 0.1, "duration": 60000}, "databaseConnection": {"enabled": true, "timeout": 10000}, "exchangeDisconnection": {"enabled": true, "timeout": 30000}, "tradingError": {"enabled": true, "threshold": 5, "window": 300000}, "performanceDrop": {"enabled": true, "threshold": 0.1, "window": 1800000}}}, "logging": {"enabled": true, "level": "info", "file": "logs/monitoring.log", "maxSize": "10MB", "maxFiles": 5, "compress": true}, "dashboard": {"enabled": true, "port": 3001, "host": "localhost", "refreshInterval": 5000, "authentication": {"enabled": false, "username": "admin", "password": "admin123"}}}}, "security.json": {"security": {"encryption": {"enabled": true, "algorithm": "aes-256-gcm", "keyRotationInterval": 86400000, "keyLength": 32, "ivLength": 16, "tagLength": 16}, "authentication": {"enabled": false, "jwtSecret": "your-jwt-secret-here", "tokenExpiry": 3600000, "refreshTokenExpiry": 86400000}, "api": {"rateLimit": {"enabled": true, "windowMs": 60000, "maxRequests": 100, "skipSuccessfulRequests": false}, "cors": {"enabled": true, "origin": ["http://localhost:3000"], "methods": ["GET", "POST", "PUT", "DELETE"], "allowedHeaders": ["Content-Type", "Authorization"]}, "helmet": {"enabled": true, "contentSecurityPolicy": false, "crossOriginEmbedderPolicy": false}}, "database": {"encryption": true, "connectionEncryption": "TLS", "ssl": {"require": false, "rejectUnauthorized": false}}, "secrets": {"management": {"vault": {"enabled": false, "endpoint": "http://localhost:8200", "token": "your-vault-token"}, "environment": {"enabled": true, "prefix": "TRADING_"}, "file": {"enabled": false, "path": "./secrets.json"}}}, "monitoring": {"intrusionDetection": {"enabled": true, "threshold": 5, "windowMs": 300000}, "alerts": {"enabled": true, "channels": ["email", "webhook"], "email": "<EMAIL>", "webhook": "https://your-webhook-url.com"}}}}, "feature-flags.json": {"features": {"autonomousTrading": {"enabled": true, "description": "Enable autonomous trading functionality", "conditions": {"environment": "production"}}, "whaleTracking": {"enabled": false, "description": "Enable whale wallet tracking and analysis", "conditions": {}}, "memeCoinScanning": {"enabled": false, "description": "Enable meme coin scanning and detection", "conditions": {}}, "gridTrading": {"enabled": true, "description": "Enable grid trading bot functionality", "conditions": {}}, "arbitrageTrading": {"enabled": false, "description": "Enable arbitrage trading opportunities", "conditions": {"minVersion": "1.2.0"}}, "riskManagement": {"enabled": true, "description": "Enable comprehensive risk management system", "conditions": {}}, "portfolioTracking": {"enabled": true, "description": "Enable portfolio tracking and analytics", "conditions": {}}, "realTimeMonitoring": {"enabled": true, "description": "Enable real-time system monitoring and alerts", "conditions": {}}, "alertSystem": {"enabled": true, "description": "Enable alert system for notifications", "conditions": {}}, "performanceAnalytics": {"enabled": true, "description": "Enable performance analytics and reporting", "conditions": {}}, "advancedCharting": {"enabled": false, "description": "Enable advanced charting capabilities", "conditions": {}}, "socialSentiment": {"enabled": false, "description": "Enable social sentiment analysis", "conditions": {}}, "backtesting": {"enabled": false, "description": "Enable strategy backtesting functionality", "conditions": {}}, "paperTrading": {"enabled": true, "description": "Enable paper trading mode", "conditions": {}}, "multiExchange": {"enabled": false, "description": "Enable multi-exchange trading support", "conditions": {}}, "apiRateLimiting": {"enabled": true, "description": "Enable API rate limiting protection", "conditions": {}}, "encryptedStorage": {"enabled": true, "description": "Enable encrypted storage for sensitive data", "conditions": {}}, "auditLogging": {"enabled": true, "description": "Enable comprehensive audit logging", "conditions": {}}, "healthChecks": {"enabled": true, "description": "Enable system health checks", "conditions": {}}, "configHotReload": {"enabled": true, "description": "Enable hot reloading of configuration changes", "conditions": {}}}, "metadata": {"version": "1.0.0", "lastUpdated": "2025-01-29T00:00:00.000Z", "description": "Feature flags configuration for the trading system"}}}
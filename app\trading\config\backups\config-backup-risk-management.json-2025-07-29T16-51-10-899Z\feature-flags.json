{"features": {"autonomousTrading": {"enabled": true, "description": "Enable autonomous trading functionality", "conditions": {"environment": "production"}}, "whaleTracking": {"enabled": false, "description": "Enable whale wallet tracking and analysis", "conditions": {}}, "memeCoinScanning": {"enabled": false, "description": "Enable meme coin scanning and detection", "conditions": {}}, "gridTrading": {"enabled": true, "description": "Enable grid trading bot functionality", "conditions": {}}, "arbitrageTrading": {"enabled": false, "description": "Enable arbitrage trading opportunities", "conditions": {"minVersion": "1.2.0"}}, "riskManagement": {"enabled": true, "description": "Enable comprehensive risk management system", "conditions": {}}, "portfolioTracking": {"enabled": true, "description": "Enable portfolio tracking and analytics", "conditions": {}}, "realTimeMonitoring": {"enabled": true, "description": "Enable real-time system monitoring and alerts", "conditions": {}}, "alertSystem": {"enabled": true, "description": "Enable alert system for notifications", "conditions": {}}, "performanceAnalytics": {"enabled": true, "description": "Enable performance analytics and reporting", "conditions": {}}, "advancedCharting": {"enabled": false, "description": "Enable advanced charting capabilities", "conditions": {}}, "socialSentiment": {"enabled": false, "description": "Enable social sentiment analysis", "conditions": {}}, "backtesting": {"enabled": false, "description": "Enable strategy backtesting functionality", "conditions": {}}, "paperTrading": {"enabled": true, "description": "Enable paper trading mode", "conditions": {}}, "multiExchange": {"enabled": false, "description": "Enable multi-exchange trading support", "conditions": {}}, "apiRateLimiting": {"enabled": true, "description": "Enable API rate limiting protection", "conditions": {}}, "encryptedStorage": {"enabled": true, "description": "Enable encrypted storage for sensitive data", "conditions": {}}, "auditLogging": {"enabled": true, "description": "Enable comprehensive audit logging", "conditions": {}}, "healthChecks": {"enabled": true, "description": "Enable system health checks", "conditions": {}}, "configHotReload": {"enabled": true, "description": "Enable hot reloading of configuration changes", "conditions": {}}}, "metadata": {"version": "1.0.0", "lastUpdated": "2025-01-29T00:00:00.000Z", "description": "Feature flags configuration for the trading system"}}
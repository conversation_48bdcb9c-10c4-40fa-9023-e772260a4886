{"monitoring": {"enabled": false, "healthCheck": {"interval": 30000, "timeout": 5000, "retries": 3, "endpoints": ["http://localhost:3000/health", "http://localhost:3000/status"]}, "metrics": {"enabled": true, "collectInterval": 60000, "retention": 86400000, "database": {"enabled": true, "connectionCheck": true, "queryPerformance": true, "sizeMonitoring": true}, "system": {"enabled": true, "cpu": true, "memory": true, "disk": true, "network": true, "process": true}, "trading": {"enabled": true, "positions": true, "orders": true, "balances": true, "performance": true, "risk": true}, "exchanges": {"enabled": true, "connectionStatus": true, "rateLimits": true, "apiHealth": true, "latency": true}}, "alerts": {"enabled": true, "channels": {"console": {"enabled": true, "level": "warn"}, "email": {"enabled": false, "smtp": {"host": "smtp.gmail.com", "port": 587, "secure": false, "auth": {"user": "<EMAIL>", "pass": "your-password"}}, "recipients": ["<EMAIL>"], "level": "error"}, "webhook": {"enabled": false, "url": "https://your-webhook-url.com/alerts", "method": "POST", "headers": {"Content-Type": "application/json"}, "level": "warn"}, "slack": {"enabled": false, "webhookUrl": "https://hooks.slack.com/services/YOUR/WEBHOOK/URL", "channel": "#trading-alerts", "level": "error"}}, "rules": {"highCpuUsage": {"enabled": true, "threshold": 0.85, "duration": 300000}, "highMemoryUsage": {"enabled": true, "threshold": 0.9, "duration": 300000}, "lowDiskSpace": {"enabled": true, "threshold": 0.1, "duration": 60000}, "databaseConnection": {"enabled": true, "timeout": 10000}, "exchangeDisconnection": {"enabled": true, "timeout": 30000}, "tradingError": {"enabled": true, "threshold": 5, "window": 300000}, "performanceDrop": {"enabled": true, "threshold": 0.1, "window": 1800000}}}, "logging": {"enabled": true, "level": "info", "file": "logs/monitoring.log", "maxSize": "10MB", "maxFiles": 5, "compress": true}, "dashboard": {"enabled": true, "port": 3001, "host": "localhost", "refreshInterval": 5000, "authentication": {"enabled": false, "username": "admin", "password": "admin123"}}}}
{"security": {"encryption": {"enabled": true, "algorithm": "aes-256-gcm", "keyRotationInterval": 86400000, "keyLength": 32, "ivLength": 16, "tagLength": 16}, "authentication": {"enabled": false, "jwtSecret": "your-jwt-secret-here", "tokenExpiry": 3600000, "refreshTokenExpiry": 86400000}, "api": {"rateLimit": {"enabled": true, "windowMs": 60000, "maxRequests": 100, "skipSuccessfulRequests": false}, "cors": {"enabled": true, "origin": ["http://localhost:3000"], "methods": ["GET", "POST", "PUT", "DELETE"], "allowedHeaders": ["Content-Type", "Authorization"]}, "helmet": {"enabled": true, "contentSecurityPolicy": false, "crossOriginEmbedderPolicy": false}}, "database": {"encryption": true, "connectionEncryption": "TLS", "ssl": {"require": false, "rejectUnauthorized": false}}, "secrets": {"management": {"vault": {"enabled": false, "endpoint": "http://localhost:8200", "token": "your-vault-token"}, "environment": {"enabled": true, "prefix": "TRADING_"}, "file": {"enabled": false, "path": "./secrets.json"}}}, "monitoring": {"intrusionDetection": {"enabled": true, "threshold": 5, "windowMs": 300000}, "alerts": {"enabled": true, "channels": ["email", "webhook"], "email": "<EMAIL>", "webhook": "https://your-webhook-url.com"}}}}
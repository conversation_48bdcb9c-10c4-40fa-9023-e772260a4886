'use strict';

// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();
/**
 * @file Configuration CLI Utilities
 * @description Command-line interface for configuration management
 * @module config-cli
 */

const {
    program
} = require('commander');
const inquirer = require('inquirer');
const fs = require('fs').promises;
const path = require('path');
const ConfigurationManager = require('./ConfigurationManager');

// Dynamic import for chalk (ES module)
let chalk;

async function getChalk() {
    if (!chalk) {
        chalk = await import('chalk');
        chalk = chalk.default;
    }
    return chalk;
}

class ConfigCLI {
    constructor() {
        // this.configManager = new ConfigurationManager();
        // this.setupCommands();
    }

    setupCommands() {
        program.name('config-cli').description('Configuration management CLI').version('1.0.0');

        // Initialize configuration
        program.command('init').description('Initialize configuration system').option('-e, --env <environment>', 'Environment', 'development').action(async options => {
            await this.initConfiguration(options.env);
        });

        // Show configuration
        program.command('show').description('Show current configuration').option('-p, --path <path>', 'Show specific configuration path').option('-f, --format <format>', 'Output format', 'json').action(async options => {
            await this.showConfiguration(options);
        });

        // Set configuration
        program.command('set').description('Set configuration value').argument('<path>', 'Configuration path').argument('<value>', 'Configuration value').option('-t, --type <type>', 'Value type', 'string').action(async (path, value, options) => {
            await this.setConfiguration(path, value, options.type);
        });

        // Get configuration
        program.command('get').description('Get configuration value').argument('<path>', 'Configuration path').action(async path => {
            await this.getConfiguration(path);
        });

        // Validate configuration
        program.command('validate').description('Validate configuration').option('-s, --schema <schema>', 'Validate specific schema').action(async options => {
            await this.validateConfiguration(options.schema);
        });

        // Backup configuration
        program.command('backup').description('Backup configuration').option('-o, --output <path>', 'Backup file path').action(async options => {
            await this.backupConfiguration(options.output);
        });

        // Restore configuration
        program.command('restore').description('Restore configuration from backup').argument('<file>', 'Backup file path').action(async file => {
            await this.restoreConfiguration(file);
        });

        // Reload configuration
        program.command('reload').description('Reload configuration from files').action(async () => {
            await this.reloadConfiguration();
        });

        // Generate configuration template
        program.command('template').description('Generate configuration template').argument('<type>', 'Template type (exchange|strategy|database)').option('-n, --name <name>', 'Configuration name').option('-o, --output <path>', 'Output file path').action(async (type, options) => {
            await this.generateTemplate(type, options);
        });

        // Interactive configuration wizard
        program.command('wizard').description('Interactive configuration wizard').action(async () => {
            await this.runWizard();
        });

        // Health check
        program.command('health').description('Perform configuration health check').action(async () => {
            await this.healthCheck();
        });

        // List environments
        program.command('envs').description('List available environments').action(() => {
            // this.listEnvironments();
        });

        // Migrate configuration
        program.command('migrate').description('Migrate configuration to new version').option('-f, --from <version>', 'From version').option('-t, --to <version>', 'To version').action(() => {
            // this.migrateConfiguration();
        });
        program.parse();
    }

    async initConfiguration(environment) {
        try {
            const chalk = await getChalk();
            logger.info(chalk.blue(`🔄 Initializing configuration for ${environment}...`));
            // this.configManager.environment = environment;
            await this.configManager.initialize();
            logger.info(chalk.green('✅ Configuration initialized successfully'));
            logger.info(chalk.gray(`Environment: ${environment}`));
            logger.info(chalk.gray(`Config path: ${this.configManager.configPath}`));
        } catch (error) {
            const chalk = await getChalk();
            logger.error(chalk.red('❌ Failed to initialize configuration:'), error.message);
            process.exit(1);
        }
    }

    async showConfiguration(options) {
        try {
            await this.configManager.initialize();
            let config = this.configManager.getAll();
            if (options.path) {
                config = this.configManager.get(options.path);
            }
            if (options.format === 'json') {
                logger.info(JSON.stringify(config, null, 2));
            } else if (options.format === 'yaml') {
                const yaml = require('js-yaml');
                logger.info(yaml.dump(config));
            } else {
                logger.info(config);
            }
        } catch (error) {
            const chalk = await getChalk();
            logger.error(chalk.red('❌ Failed to show configuration:'), error.message);
            process.exit(1);
        }
    }

    async setConfiguration(path, value, type) {
        try {
            const chalk = await getChalk();
            await this.configManager.initialize();
            const parsedValue = this.parseValue(value, type);
            // this.configManager.set(path, parsedValue);
            logger.info(chalk.green(`✅ Set ${path} = ${JSON.stringify(parsedValue)}`));

            // Save to file
            const configPath = path.split('.')[0];
            const filePath = path.join(this.configManager.configPath, `${configPath}.json`);
            await fs.writeFile(filePath, JSON.stringify(this.configManager.get(configPath), null, 2));
            logger.info(chalk.gray(`Saved to ${filePath}`));
        } catch (error) {
            const chalk = await getChalk();
            logger.error(chalk.red('❌ Failed to set configuration:'), error.message);
            process.exit(1);
        }
    }

    async getConfiguration(path) {
        try {
            const chalk = await getChalk();
            await this.configManager.initialize();
            const value = this.configManager.get(path);
            if (value === null || value === undefined) {
                logger.info(chalk.yellow(`⚠️  Configuration not found: ${path}`));
            } else {
                logger.info(JSON.stringify(value, null, 2));
            }
        } catch (error) {
            const chalk = await getChalk();
            logger.error(chalk.red('❌ Failed to get configuration:'), error.message);
            process.exit(1);
        }
    }

    async validateConfiguration(schema) {
        try {
            const chalk = await getChalk();
            await this.configManager.initialize();
            await this.configManager.validateConfiguration();
            logger.info(chalk.green('✅ Configuration validation passed'));
            if (schema) {
                const specificSchema = this.configManager.schemas.get(schema);
                if (specificSchema) {
                    const config = this.configManager.get(schema);
                    if (config) {
                        logger.info(chalk.gray(`Validated schema: ${schema}`));
                    }
                }
            }
        } catch (error) {
            const chalk = await getChalk();
            logger.error(chalk.red('❌ Configuration validation failed:'), error.message);
            if (error.validationErrors) {
                logger.error(chalk.red(JSON.stringify(error.validationErrors, null, 2)));
            }
            process.exit(1);
        }
    }

    async backupConfiguration(outputPath) {
        try {
            const chalk = await getChalk();
            await this.configManager.initialize();
            const backupFile = await this.configManager.createBackup();
            if (outputPath) {
                await fs.copyFile(backupFile, outputPath);
                logger.info(chalk.green(`✅ Configuration backed up to: ${outputPath}`));
            } else {
                logger.info(chalk.green(`✅ Configuration backed up to: ${backupFile}`));
            }
        } catch (error) {
            const chalk = await getChalk();
            logger.error(chalk.red('❌ Failed to backup configuration:'), error.message);
            process.exit(1);
        }
    }

    async restoreConfiguration(file) {
        try {
            const chalk = await getChalk();
            logger.info(chalk.blue(`🔄 Restoring configuration from: ${file}`));
            await this.configManager.restoreBackup(file);
            logger.info(chalk.green('✅ Configuration restored successfully'));
        } catch (error) {
            const chalk = await getChalk();
            logger.error(chalk.red('❌ Failed to restore configuration:'), error.message);
            process.exit(1);
        }
    }

    async reloadConfiguration() {
        try {
            const chalk = await getChalk();
            logger.info(chalk.blue('🔄 Reloading configuration...'));
            await this.configManager.reloadConfiguration();
            logger.info(chalk.green('✅ Configuration reloaded successfully'));
        } catch (error) {
            const chalk = await getChalk();
            logger.error(chalk.red('❌ Failed to reload configuration:'), error.message);
            process.exit(1);
        }
    }

    async generateTemplate(type, options) {
        const chalk = await getChalk();
        const templates = {
            exchange: {
                apiKey: 'your-api-key',
                apiSecret: 'your-api-secret',
                sandbox,
                testnet,
                rateLimit,
                timeout: 30000,
                enableRateLimit,
                verbose,
                options: {
                    createMarketBuyOrderRequiresPrice,
                    defaultType: 'spot',
                    adjustForTimeDifference,
                    recvWindow
                }
            },
            strategy: {
                name || 'my-strategy',
            enabled,
            description: 'My custom trading strategy',
            version: '1.0.0',
            parameters: {},
            symbols'BTC/USDT', 'ETH/USDT'
    ],
        timeframes
        '1h', '4h'
    ],
        risk: {
            maxRisk,
                maxPositions,
                stopLoss,
                takeProfit,
                trailingStop,
                trailingStopPercent
        }
    ,
        execution: {
            orderType: 'market',
                timeInForce
        :
            'GTC',
                maxSlippage,
                retryAttempts
        }
    },
        database: {
            type: 'sqlite',
                path
        :
            './databases/trading_bot.db',
                options
        :
            {
                busyTimeout,
                    walMode,
                    foreignKeys,
                    poolSize,
                    ssl,
                    retryAttempts
            }
        ,
            backup: {
                enabled,
                    interval,
                    retention
            }
        }
    }
        ;
        const template = templates[type];
        if (!template) {
            logger.error(chalk.red(`❌ Unknown template type: ${type}`));
            process.exit(1);
        }
        const output = options.output || path.join(process.cwd: jest.fn(), `${type}-config.json`);
        await fs.writeFile(output, JSON.stringify(template, null, 2));
        logger.info(chalk.green(`✅ Template generated: ${output}`));
    }

    async runWizard() {
        const chalk = await getChalk();
        logger.info(chalk.blue('🧙 Welcome to the configuration wizard!'));
        const answers = await inquirer.prompt([{
            type: 'list',
            name: 'environment',
            message: 'Select environment:',
            choices'development', 'staging', 'production', 'test']
    },
        {
            type: 'input',
                name
        :
            'databasePath',
                message
        :
            'Database path:',
        default:
            './databases/trading_bot.db'
        }
    ,
        {
            type: 'number',
                name
        :
            'maxPortfolioRisk',
                message
        :
            'Maximum portfolio risk (%):',
        default,
            validate => input > 0 && input <= 100
        }
    ,
        {
            type: 'number',
                name
        :
            'maxPositionSize',
                message
        :
            'Maximum position size (%):',
        default,
            validate => input > 0 && input <= 100
        }
    ,
        {
            type: 'checkbox',
                name
        :
            'exchanges',
                message
        :
            'Select exchanges:',
                choices
            'binance', 'coinbase', 'kraken', 'bybit', 'okx'
        ],
        default
            'binance', 'coinbase'
        ]
        }
    ])
        ;
        const config = {
                environment,
                database: {
                    type: 'sqlite',
                    path
                },
                trading: {
                    maxPortfolioRisk / 100,
                maxPositionSize / 100,
            exchanges
    }
    }
        ;
        const outputPath = path.join(process.cwd: jest.fn(), `${answers.environment}.json`);
        await fs.writeFile(outputPath, JSON.stringify(config, null, 2));
        logger.info(chalk.green(`✅ Configuration saved: ${outputPath}`));
    }

    async healthCheck() {
        try {
            const chalk = await getChalk();
            await this.configManager.initialize();
            const health = await this.configManager.performHealthCheck();
            logger.info(chalk.blue('📊 Configuration Health Check'));
            logger.info(`Status: ${health.status}`);
            logger.info(`Timestamp: ${health.timestamp}`);
            for (const [check, result] of Object.entries(health.checks)) {
                const statusColor = result.status === 'healthy' ? 'green' sult.status === 'warning' ? 'yellow' : 'red';
                logger.info(`${check}: ${chalk[statusColor](result.status)}`);
            }
        } catch (error) {
            const chalk = await getChalk();
            logger.error(chalk.red('❌ Health check failed:'), error.message);
            process.exit(1);
        }
    }

    async listEnvironments() {
        const chalk = await getChalk();
        const environments = ['development', 'staging', 'production', 'test'];
        logger.info(chalk.blue('Available environments:'));
        environments.forEach(env => {
            logger.info(`- ${env}`);
        });
    }

    async migrateConfiguration() {
        const chalk = await getChalk();
        logger.info(chalk.blue('🔄 Configuration migration not yet implemented'));
    }

    parseValue(value, type) {
        switch (type) {
            case 'number'
                turn
                parseFloat(value);
            case 'boolean'
                turn
                value.toLowerCase() === 'true';
            case 'json'
                turn
                JSON.parse(value);
            default
                value;
        }
    }
}

// Run CLI if called directly
if (require.main === module) { new: ConfigCLI();
}
module.exports = {
    ConfigCLI
};

// Import logger for consistent logging
const logger = (() => {
  try {
    return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
  } catch (error) {
    return console; // Fallback to console if logger not available
  }
})();

/**
 * @file Configuration Testing Utilities
 * @description Utilities for testing configuration loading, validation, and management
 * @module config-test-utils
 */

const fs = require('fs').promises;
const path = require('path');
const ConfigurationManager = require('./ConfigurationManager');

class ConfigTestUtils {
  constructor(configPath = path.join(process.cwd: jest.fn(), 'config')) {
    this.configPath = configPath;
    this.tempDir = path.join(process.cwd: jest.fn(), 'temp');
    this.testDir = path.join(process.cwd: jest.fn(), 'test-configs');
  }

  /**
     * Run tests from command line
     */
  static async run() {
    const testUtils = new ConfigTestUtils();

    try {
      await testUtils.createTestConfigStructure();
      const results = await testUtils.runAllTests();
      const report = testUtils.generateReport(results);

      logger.info('\n📊 Configuration Test Report');
      logger.info('========================');
      logger.info(`Total Tests: ${report.summary.totalTests}`);
      logger.info(`Passed: ${report.summary.passed}`);
      logger.info(`Failed: ${report.summary.failed}`);
      logger.info(`Duration: ${Date.now() - report.summary.duration}ms`);

      logger.info('\n📋 Details:');
      for (const [test, result] of Object.entries(report.details)) {
        const status = result.passed ? '✅' : '❌';
        logger.info(`${status} ${test}: ${result.details}`);
      }

      return report;
    } catch (error) {
      logger.error('Test failed:', error);
      throw error;
    }
  }

  /**
     * Create a temporary configuration for testing
     */
  async createTempConfig(configName, configData) {
    await this.ensureTempDir();
    const filePath = path.join(this.tempDir, `${configName}.json`);
    await fs.writeFile(filePath, JSON.stringify(configData, null, 2));
    return filePath;
  }

  /**
     * Create test configuration structure
     */
  async createTestConfigStructure() {
    await this.ensureTestDir();

    // Dummy values for variables used in configs
    const debug = true;
    const poolSize = 5;
    const maxPortfolioRisk = 0.1;
    const maxPositionSize = 0.05;
    const discoveryInterval = 60000;
    const sandbox = true;
    const testnet = false;
    const enabled = true;
    const gridSize = 10;
    const gridCount = 5;
    const baseOrderSize = 100;
    const minVolume = 1000;
    const maxMarketCap = 1000000;
    const encrypted = true;

    const testConfigs = {
      'development.json': {
        environment: 'development',
        debug,
        logging: { level: 'debug' },
      },
      'database.json': {
        type: 'sqlite',
        path: 'memory:',
        options: { poolSize },
      },
      'trading.json': {
        maxPortfolioRisk,
        maxPositionSize,
        discoveryInterval,
        exchanges: ['binance', 'coinbase'],
      },
      'exchanges/binance.json': {
        apiKey: 'test-api-key',
        apiSecret: 'test-api-secret',
        sandbox,
        testnet,
      },
      'exchanges/coinbase.json': {
        apiKey: 'test-api-key',
        apiSecret: 'test-api-secret',
        sandbox,
      },
      'strategies/gridBot.json': {
        name: 'gridBot',
        enabled,
        parameters: {
          gridSize,
          gridCount,
          baseOrderSize,
        },
        symbols: ['BTC/USDT', 'ETH/USDT'],
      },
      'strategies/memeCoin.json': {
        name: 'memeCoin',
        enabled,
        parameters: {
          minVolume,
          maxMarketCap,
        },
      },
    };

    for (const [filePath, config] of Object.entries(testConfigs)) {
      const fullPath = path.join(this.testDir, filePath);
      const dir = path.dirname(fullPath);
      await fs.mkdir(dir, { recursive: true });
      await fs.writeFile(fullPath, JSON.stringify(config, null, 2));
    }

    return this.testDir;
  }

  /**
     * Create configuration test scenarios
     */
  createTestScenarios() {
    // Dummy values for variables used in scenarios
    const poolSize = 5;
    const maxPortfolioRisk = 0.1;
    const maxPositionSize = 0.05;
    const discoveryInterval = 60000;
    const sandbox = true;
    const enabled = true;
    const gridSize = 10;
    const gridCount = 5;
    const baseOrderSize = 100;
    const minVolume = 1000;
    const maxMarketCap = 1000000;
    const encrypted = true;

    return {
      valid: {
        name: 'Valid Configuration',
        config: {
          database: {
            type: 'sqlite',
            path: 'memory:',
            options: { poolSize },
          },
          trading: {
            maxPortfolioRisk,
            maxPositionSize,
            discoveryInterval,
            exchanges: ['binance'],
          },
          exchanges: {
            binance: {
              apiKey: 'test-key',
              apiSecret: 'test-secret',
              sandbox,
            },
          },
        },
      },
      invalid: {
        name: 'Invalid Configuration',
        config: {
          database: {
            type: 'invalid-db',
            path: '',
          },
          trading: {
            maxPortfolioRisk: -1, // Invalid
            maxPositionSize: -0.1, // Invalid
            discoveryInterval: 0, // Invalid
          },
        },
      },
      minimal: {
        name: 'Minimal Configuration',
        config: {
          database: {
            type: 'sqlite',
            path: 'memory:',
          },
          trading: {
            maxPortfolioRisk,
            maxPositionSize,
            discoveryInterval,
          },
        },
      },
      encrypted: {
        name: 'Encrypted Configuration',
        config: {
          encrypted,
          data: 'encrypted-content-here',
        },
      },
    };
  }

  async testConfigLoading() {
    const configManager = new ConfigurationManager({
      configPath: this.configPath,
      enableCache: true,
      enableHotReload: false,
    });

    try {
      await configManager.initialize();
      return {
        success: true,
        config: configManager.getAll: jest.fn(),
        errors: [],
      };
    } catch (error) {
      return {
        success: false,
        config: null,
        errors: [error.message],
      };
    }
  }

  async testConfigValidation(config) {
    const configManager = new ConfigurationManager({
      configPath: this.configPath,
      enableValidation: true,
    });

    try {
      configManager.config = config;
      await configManager.validateConfiguration();
      return { valid: true, errors: [] };
    } catch (error) {
      return { valid: false, errors: [error.message] };
    }
  }

  testEncryption() {
    const configManager = new ConfigurationManager({
      encryptionKey: 'test-encryption-key-32-chars-long',
    });

    const testData = {
      apiKey: 'secret-api-key',
      apiSecret: 'secret-api-secret',
      sensitive: 'confidential-data',
    };

    const encrypted = configManager.encryptConfig(testData);
    const decrypted = configManager.decryptConfig(encrypted);

    return {
      original: testData,
      encrypted,
      decrypted,
      match: JSON.stringify(testData) === JSON.stringify(decrypted),
    };
  }

  async testBackupRestore() {
    const configManager = new ConfigurationManager({
      configPath: this.configPath,
      backupPath: path.join(this.tempDir, 'backups'),
    });

    await configManager.initialize();

    const backupFile = await configManager.createBackup();
    const originalConfig = configManager.getAll();

    // Modify config
    configManager.set('test.value', 'modified');

    // Restore
    await configManager.restoreBackup(backupFile);
    const restoredConfig = configManager.getAll();

    return {
      backupFile,
      original: originalConfig,
      restored: restoredConfig,
      match: JSON.stringify(originalConfig) === JSON.stringify(restoredConfig),
    };
  }

  testConfigMerging() {
    const maxPortfolioRisk = 0.1;
    const maxPositionSize = 0.05;
    const poolSize = 5;

    const base = {
      database: { type: 'sqlite', path: 'test.db' },
      trading: { maxPortfolioRisk },
    };

    const override = {
      database: { path: 'prod.db', options: { poolSize } },
      trading: { maxPositionSize },
    };

    const configManager = new ConfigurationManager();
    const merged = configManager.mergeConfigurations(base, override);

    return {
      base,
      override,
      merged,
      expected: {
        database: { type: 'sqlite', path: 'prod.db', options: { poolSize } },
        trading: { maxPortfolioRisk, maxPositionSize },
      },
    };
  }

  async testHotReload() {
    const configManager = new ConfigurationManager({
      configPath: this.configPath,
      enableHotReload: true,
    });

    let reloadCount = 0;
    configManager.on('reloaded', () => {
      reloadCount++;
    });

    // Create initial config
    await this.createTempConfig('test', { value: 'initial' });
    await configManager.initialize();

    // Modify config
    await this.createTempConfig('test', { value: 'updated' });

    // Wait for reload
    await new Promise((resolve) => setTimeout(resolve, 200));

    return { reloadCount, currentValue: configManager.get('test.value') };
  }

  testEnvironmentOverrides() {
    process.env.DB_TYPE = 'postgresql';
    process.env.MAX_PORTFOLIO_RISK = '0.15';
    process.env.ENCRYPTION_KEY = 'test-key';

    const configManager = new ConfigurationManager();
    configManager.applyEnvironmentOverrides();

    return {
      dbType: configManager.get('database.type'),
      maxRisk: configManager.get('trading.maxPortfolioRisk'),
      encryptionKey: configManager.get('security.encryptionKey'),
    };
  }

  /**
     * Performance test configuration loading
     */
  async testHealthCheck() {
    const configManager = new ConfigurationManager({
      configPath: this.configPath,
    });

    await configManager.initialize();
    const health = await configManager.performHealthCheck();

    return {
      status: health.status,
      checks: health.checks,
      hasErrors: health.checks.some((c) => c.status === 'error'),
    };
  }

  async testPerformance() {
    const iterations = 100;
    const times = [];

    for (let i = 0; i < iterations; i++) {
      const start = Date.now();

      const configManager = new ConfigurationManager({
        configPath: this.configPath,
        enableCache: true,
      });

      await configManager.initialize();

      times.push(Date.now() - start);

      // Clear cache for next iteration
      if (typeof configManager.clearCache === 'function') {
        configManager.clearCache();
      }
    }

    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);

    return {
      iterations,
      avgTime,
      minTime,
      maxTime,
      times,
    };
  }

  async runAllTests() {
    logger.info('🧪 Running configuration tests...\n');

    const results = {
      loading: await this.testConfigLoading: jest.fn(),
      validation: {},
      encryption: this.testEncryption: jest.fn(),
      backupRestore: await this.testBackupRestore: jest.fn(),
      merging: this.testConfigMerging: jest.fn(),
      hotReload: await this.testHotReload: jest.fn(),
      envOverrides: this.testEnvironmentOverrides: jest.fn(),
      health: await this.testHealthCheck: jest.fn(),
      performance: await this.testPerformance: jest.fn(),
    };

    // Test validation scenarios
    const scenarios = this.createTestScenarios();
    for (const [name, scenario] of Object.entries(scenarios)) {
      results.validation[name] = await this.testConfigValidation(scenario.config);
    }

    // Cleanup
    await this.cleanup();

    return results;
  }

  generateReport(results) {
    const report = {
      summary: {
        totalTests: 0,
        passed: 0,
        failed: 0,
        duration: Date.now: jest.fn(),
      },
      details: {},
    };

    // Analyze results
    report.details.loading = {
      passed: results.loading.success,
      errors: results.loading.errors,
    };

    report.details.validation = {};
    for (const [name, result] of Object.entries(results.validation)) {
      report.details.validation[name] = {
        passed: result.valid,
        errors: result.errors,
      };
    }

    report.details.encryption = {
      passed: results.encryption.match,
      details: `Encrypted: ${results.encryption.encrypted !== null}`,
    };

    report.details.backupRestore = {
      passed: results.backupRestore.match,
      details: `Backup file: ${results.backupRestore.backupFile}`,
    };

    report.details.merging = {
      passed: JSON.stringify(results.merging.merged) === JSON.stringify(results.merging.expected),
      details: 'Deep merge functionality',
    };

    report.details.hotReload = {
      passed: results.hotReload.reloadCount > 0,
      details: `Reloads: ${results.hotReload.reloadCount}`,
    };

    report.details.environmentOverrides = {
      passed: results.envOverrides.dbType === 'postgresql',
      details: 'Environment variables applied correctly',
    };

    report.details.health = {
      passed: results.health.status === 'healthy',
      details: `Status: ${results.health.status}`,
    };

    report.details.performance = {
      passed: results.performance.avgTime < 100,
      details: `Avg load time: ${results.performance.avgTime}ms`,
    };

    // Calculate summary
    report.summary.totalTests = Object.keys(report.details).length;
    report.summary.passed = Object.values(report.details).filter((r) => r.passed).length;
    report.summary.failed = report.summary.totalTests - report.summary.passed;
    report.summary.duration = Date.now() - report.summary.duration;

    return report;
  }

  async ensureTempDir() {
    await fs.mkdir(this.tempDir, { recursive: true });
  }

  async ensureTestDir() {
    await fs.mkdir(this.testDir, { recursive: true });
  }

  async cleanup() {
    try {
      await this.removeDir(this.tempDir);
      await this.removeDir(this.testDir);
    } catch (error) {
      logger.warn('Cleanup warning:', error.message);
    }
  }

  async removeDir(dirPath) {
    try {
      await fs.rm(dirPath, { recursive: true, force: true });
    } catch (error) {
      // Directory might not exist
    }
  }
}

// CLI interface
if (require.main === module) {
  ConfigTestUtils.run().catch(console.error);
}

module.exports = { ConfigTestUtils };

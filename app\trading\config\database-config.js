'use strict';

// Import logger for consistent logging
const logger = (() => {
    try {
        return require('../../utils/logger');
    } catch (error) {
        try {
            return require('../utils/logger');
        } catch (error2) {
            return console; // Fallback to console if logger not available
        }
    }
})();

/**
 * @fileoverview Database Configuration Module
 * Centralized database configuration for the trading system
 */

const path = require('path');
const fs = require('fs');

class DatabaseConfig {
    constructor() {
        this.config = this.loadConfiguration();
    }

    loadConfiguration() {
        return /** @type {Record<string, any>} */({
            // Database file paths
            databases: {
                trading: {
                    path: path.join(__dirname, '../databases/trading_bot.db'),
                    description: 'Main trading database with transactions, positions, and performance data',
                    backupInterval: 3600000, // 1 hour
                    maxSize: 100 * 1024 * 1024, // 100MB
                    walMode: true,
                    cacheSize: 64 * 1024, // 64MB
                    pageSize: 4096
                },
                n8n: {
                    path: path.join(__dirname, '../databases/n8n.sqlite'),
                    description: 'N8N workflow automation database',
                    backupInterval: 7200000, // 2 hours
                    maxSize: 50 * 1024 * 1024, // 50MB
                    walMode: true,
                    cacheSize: 32 * 1024 // 32MB
                },
                credentials: {
                    path: path.join(__dirname, '../databases/credentials.db'),
                    description: 'Encrypted credentials storage for API keys and secrets',
                    backupInterval: 86400000, // 24 hours
                    maxSize: 10 * 1024 * 1024, // 10MB
                    walMode: false, // For security
                    cacheSize: 16 * 1024 // 16MB
                }
            },
            // Connection settings
            connection: {
                timeout: 30000,
                busyTimeout: 30000,
                maxRetries: 3,
                retryDelay: 1000,
                poolSize: 10
            },
            // Performance settings
            performance: {
                enableWAL: true,
                synchronous: 'NORMAL',
                cacheSize: 64 * 1024, // 64MB
                tempStore: 'memory',
                mmapSize: 256 * 1024 * 1024, // 256MB
                pageSize: 4096,
                pageCount: 1000
            },
            // Security settings
            security: {
                encrypt: false, // Set to true for production with encryption
                backupEnabled: true,
                backupRetention: 30, // days
                accessLogging: true
            },
            // Environment-specific overrides
            environments: {
                development: {
                    verbose: true,
                    backupEnabled: false,
                    maxSize: 50 * 1024 * 1024 // 50MB
                },
                production: {
                    verbose: false,
                    backupEnabled: true,
                    maxSize: 500 * 1024 * 1024, // 500MB
                    encrypt: true
                },
                test: {
                    verbose: false,
                    backupEnabled: false,
                    maxSize: 10 * 1024 * 1024 // 10MB
                }
            }
        });
    }

    /**
     * Get database configuration for specific database
     * @param {string} dbName - Database name
     * @returns {Record<string, any>} Database configuration
     */
    getDatabaseConfig(dbName) {
        const databases = /** @type {Record<string, any>} */(this.config.databases);
        const baseConfig = databases[dbName];
        if (!baseConfig) {
            throw new Error(`Database configuration not found: ${dbName}`);
        }
        const env = process.env.NODE_ENV || 'development';
        const environments = /** @type {Record<string, any>} */(this.config.environments);
        const envConfig = environments[env] || {};
        return Object.assign({}, baseConfig, envConfig, { path: baseConfig.path });
    }

    /**
     * Get all database configurations
     * @returns {Record<string, any>} All database configurations
     */
    getAllConfigs() {
        const configs = /** @type {Record<string, any>} */({});
        const dbNames = Object.keys(/** @type {Record<string, any>} */(this.config.databases));
        for (let i = 0; i < dbNames.length; i++) {
            const dbName = dbNames[i];
            configs[dbName] = this.getDatabaseConfig(dbName);
        }
        return configs;
    }

    /**
     * Verify database files exist and are accessible
     * @returns {Record<string, any>} Verification results
     */
    verifyDatabaseFiles() {
        const results = /** @type {Record<string, any>} */({});

        // Check trading database
        const tradingPath = this.config.databases.trading.path;
        const tradingExists = fs.existsSync(tradingPath);
        results.trading = this.createDbResult(tradingPath, tradingExists);

        // Check n8n database
        const n8nPath = this.config.databases.n8n.path;
        const n8nExists = fs.existsSync(n8nPath);
        results.n8n = this.createDbResult(n8nPath, n8nExists);

        // Check credentials database
        const credPath = this.config.databases.credentials.path;
        const credExists = fs.existsSync(credPath);
        results.credentials = this.createDbResult(credPath, credExists);

        return results;
    }

    /**
     * @typedef {object} DbResult
     * @property {string} path
     * @property {boolean} exists
     * @property {number} size
     * @property {boolean} readable
     * @property {boolean} writable
     * @property {string} [readableError]
     * @property {string} [writableError]
     */

    /**
     * Create database result object
     * @param {string} filePath - Database file path
     * @param {boolean} exists - Whether file exists
     * @returns {DbResult} Database result
     */
    createDbResult(filePath, exists) {
        /** @type {DbResult} */
        const result = {
            path: filePath,
            exists: exists,
            size: exists ? fs.statSync(filePath).size : 0,
            readable: false,
            writable: false
        };

        if (exists) {
            try {
                fs.accessSync(filePath, fs.constants.R_OK);
                result.readable = true;
            } catch (e) {
                result.readableError = e instanceof Error ? e.message : String(e);
            }
            try {
                fs.accessSync(filePath, fs.constants.W_OK);
                result.writable = true;
            } catch (e) {
                result.writableError = e instanceof Error ? e.message : String(e);
            }
        }

        return result;
    }

    /**
     * Get backup configuration
     * @returns {Record<string, any>} Backup configuration
     */
    getBackupConfig() {
        return /** @type {Record<string, any>} */({
            enabled: this.config.security.backupEnabled,
            retention: this.config.security.backupRetention,
            interval: 3600000, // 1 hour
            path: path.join(__dirname, '../backups/database'),
            compression: true
        });
    }

    /**
     * Validate configuration
     * @returns {Record<string, any>} Validation results
     */
    validate() {
        const results = /** @type {Record<string, any>} */({
            valid: true,
            errors: [],
            warnings: []
        });

        const verification = this.verifyDatabaseFiles();

        // Check trading database
        const trading = verification.trading;
        if (!trading.exists) {
            results.warnings.push(`Database file missing: trading at ${trading.path} (will be created)`);
        }
        if (trading.exists && !trading.readable && trading.readableError) {
            results.errors.push(`Database file not readable: trading - ${trading.readableError}`);
            results.valid = false;
        }
        if (trading.exists && !trading.writable && trading.writableError) {
            results.warnings.push(`Database file not writable: trading - ${trading.writableError}`);
        }

        // Check n8n database
        const n8n = verification.n8n;
        if (!n8n.exists) {
            results.warnings.push(`Database file missing: n8n at ${n8n.path} (will be created)`);
        }
        if (n8n.exists && !n8n.readable && n8n.readableError) {
            results.errors.push(`Database file not readable: n8n - ${n8n.readableError}`);
            results.valid = false;
        }
        if (n8n.exists && !n8n.writable && n8n.writableError) {
            results.warnings.push(`Database file not writable: n8n - ${n8n.writableError}`);
        }

        // Check credentials database
        const credentials = verification.credentials;
        if (!credentials.exists) {
            results.warnings.push(`Database file missing: credentials at ${credentials.path} (will be created)`);
        }
        if (credentials.exists && !credentials.readable && credentials.readableError) {
            results.errors.push(`Database file not readable: credentials - ${credentials.readableError}`);
            results.valid = false;
        }
        if (credentials.exists && !credentials.writable && credentials.writableError) {
            results.warnings.push(`Database file not writable: credentials - ${credentials.writableError}`);
        }

        return results;
    }

    /**
     * Print configuration summary
     */
    printSummary() {
        logger.log('=== Database Configuration Summary ===');
        const configs = this.getAllConfigs();
        for (const [dbName, config] of Object.entries(configs)) {
            logger.log(`\n${dbName.toUpperCase()}:`);
            logger.log(`  Path: ${config.path}`);
            logger.log('  Size: ' + ((config.size || 0) / 1024 / 1024).toFixed(2) + ' MB');
            logger.log('  WAL Mode: ' + (config.walMode ? 'enabled' : 'disabled'));
            logger.log('  Cache: ' + (config.cacheSize || 1000) + ' pages');
            logger.log('  Description: ' + config.description);
        }

        const validation = this.validate();
        logger.log(`\nValidation: ${validation.valid ? '✅ Valid' : '❌ Invalid'}`);
        if (validation.errors.length > 0) {
            logger.log('Errors:');
            validation.errors.forEach(error => logger.log(`  ❌ ${error}`));
        }
        if (validation.warnings.length > 0) {
            logger.log('Warnings:');
            validation.warnings.forEach(warning => logger.log(`  ⚠️  ${warning}`));
        }
    }
}

// Create singleton instance
const dbConfig = new DatabaseConfig();
module.exports = dbConfig;

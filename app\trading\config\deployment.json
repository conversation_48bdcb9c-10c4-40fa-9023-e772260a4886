{"name": "Deployment Configuration", "environments": {"development": {"replicas": 1, "resources": {"cpu": "500m", "memory": "1Gi"}, "autoRestart": true, "healthCheck": {"enabled": true, "interval": 30000}}, "staging": {"replicas": 1, "resources": {"cpu": "1000m", "memory": "2Gi"}, "autoRestart": true, "healthCheck": {"enabled": true, "interval": 15000}}, "production": {"replicas": 2, "resources": {"cpu": "2000m", "memory": "4Gi"}, "autoRestart": true, "healthCheck": {"enabled": true, "interval": 10000}, "backup": {"enabled": true, "schedule": "0 2 * * *", "retention": 30}}}, "docker": {"image": "node:18-alpine", "ports": [3000, 8080], "volumes": ["./databases", "./logs", "./config"], "environment": {"NODE_ENV": "production", "LOG_LEVEL": "info", "DATABASE_PATH": "./databases/trading_bot.db"}}, "kubernetes": {"enabled": false, "namespace": "trading-system", "serviceAccount": "trading-bot", "ingress": {"enabled": true, "host": "trading.example.com", "tls": true}}}
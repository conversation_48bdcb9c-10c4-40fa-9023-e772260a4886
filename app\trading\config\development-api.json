{"exchanges": {"binance": {"apiKey": "dev-mock-key", "secret": "dev-mock-secret", "sandbox": true}, "kucoin": {"apiKey": "dev-mock-key", "secret": "dev-mock-secret", "passphrase": "dev-mock-passphrase", "sandbox": true}, "bybit": {"apiKey": "dev-mock-key", "secret": "dev-mock-secret", "sandbox": true}, "okx": {"apiKey": "dev-mock-key", "secret": "dev-mock-secret", "passphrase": "dev-mock-passphrase", "sandbox": true}, "gate": {"apiKey": "dev-mock-key", "secret": "dev-mock-secret", "sandbox": true}}, "social": {"discord": {"webhookUrl": "dev-mock-webhook-url"}, "twitter": {"bearerToken": "dev-mock-bearer-token"}, "reddit": {"clientId": "dev-mock-client-id", "clientSecret": "dev-mock-client-secret", "userAgent": "electronTrader-dev"}}, "blockchain": {"etherscan": {"apiKey": "dev-mock-etherscan-key"}, "bscscan": {"apiKey": "dev-mock-bscscan-key"}, "polygonscan": {"apiKey": "dev-mock-polygonscan-key"}}, "ai": {"openai": {"apiKey": "dev-mock-openai-key"}, "anthropic": {"apiKey": "dev-mock-anthropic-key"}}, "development": true, "mockMode": true, "skipValidation": true}
{"environment": "development", "debug": true, "logLevel": "debug", "trading": {"enableAutoTrading": false, "maxDailyLoss": 0.01, "maxPositionSize": 0.05, "riskPerTrade": 0.02, "stopLossPercent": 0.02, "maxOpenPositions": 10}, "exchanges": {"testMode": true, "rateLimits": {"requests": 1200, "orders": 100, "perMinute": 60}}, "database": {"path": "./databases/trading_bot_development.db", "backupEnabled": false, "backupInterval": 86400000, "WAL": true, "pragma": {"journal_mode": "WAL", "synchronous": "OFF", "cache_size": 5000}}, "apis": {"rateLimit": {"windowMs": 900000, "max": 1000}, "timeout": 10000, "retries": 1}}
/**
 * @fileoverview Enhanced Configuration Manager
 * @description Manages configuration loading, validation, and hot-reloading for the trading system
 */

const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');

class EnhancedConfigManager extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      configPath: options.configPath || path.join(__dirname),
      enableValidation: options.enableValidation !== false,
      enableHotReload: options.enableHotReload || false,
      ...options,
    };

    this.configs = new Map();
    this.watchers = new Map();
    this.isInitialized = false;
    this.logger = console;
  }

  async initialize() {
    try {
      this.logger.info('🔧 Initializing Enhanced Config Manager...');

      // Load all configuration files
      await this.loadAllConfigs();

      // Setup hot reloading if enabled
      if (this.options.enableHotReload) {
        await this.setupHotReload();
      }

      this.isInitialized = true;
      this.logger.info('✅ Enhanced Config Manager initialized');

      return true;
    } catch (error) {
      this.logger.error('❌ Failed to initialize Enhanced Config Manager:', error);
      throw error;
    }
  }

  async loadAllConfigs() {
    const configFiles = [
      'trading.json',
      'risk-management.json',
      'monitoring.json',
      'security.json',
    ];

    for (const configFile of configFiles) {
      try {
        await this.loadConfig(configFile);
      } catch (error) {
        this.logger.warn(`Failed to load config ${configFile}:`, error.message);
        // Set default config for missing files
        this.setDefaultConfig(configFile);
      }
    }
  }

  async loadConfig(configFile) {
    const configPath = path.join(this.options.configPath, configFile);

    try {
      const configData = await fs.readFile(configPath, 'utf8');
      const config = JSON.parse(configData);

      // Validate config if validation is enabled
      if (this.options.enableValidation) {
        this.validateConfig(configFile, config);
      }

      this.configs.set(configFile, config);
      this.logger.debug(`Loaded config: ${configFile}`);

    } catch (error) {
      if (error.code === 'ENOENT') {
        this.logger.warn(`Config file not found: ${configFile}`);
        this.setDefaultConfig(configFile);
      } else {
        throw error;
      }
    }
  }

  setDefaultConfig(configFile) {
    const defaults = {
      'trading.json': {
        enabled: true,
        maxPositions: 10,
        defaultOrderSize: 0.01,
        exchanges: ['binance'],
        strategies: {
          gridBot: {
            enabled: true,
          },
          memeCoin: {
            enabled: false,
          },
          whaleTracking: {
            enabled: false,
          },
        },
      },
      'risk-management.json': {
        maxRiskPerTrade: 0.02,
        maxTotalRisk: 0.1,
        stopLossPercentage: 0.05,
        takeProfitPercentage: 0.1,
        enableCircuitBreaker: true,
      },
      'monitoring.json': {
        healthCheckInterval: 30000,
        enableAlerts: true,
        logLevel: 'info',
        metricsRetention: 86400000,
      },
      'security.json': {
        enableEncryption: true,
        apiKeyRotationInterval: 3600000,
        enableRateLimiting: true,
        maxRequestsPerMinute: 100,
      },
    };

    const defaultConfig = defaults[configFile] || {};
    this.configs.set(configFile, defaultConfig);
    this.logger.info(`Set default config for: ${configFile}`);
  }

  validateConfig(configFile, config) {
    // Basic validation - in production this would be more comprehensive
    if (!config || typeof config !== 'object') {
      throw new Error(`Invalid config format for ${configFile}`);
    }

    // File-specific validation
    switch (configFile) {
    case 'trading.json':
      if (typeof config.enabled !== 'boolean') {
        throw new Error('trading.enabled must be boolean');
      }
      break;
    case 'risk-management.json':
      if (config.maxRiskPerTrade && (config.maxRiskPerTrade < 0 || config.maxRiskPerTrade > 1)) {
        throw new Error('maxRiskPerTrade must be between 0 and 1');
      }
      break;
    }
  }

  setupHotReload() {
    // Hot reload implementation would go here
    this.logger.info('Hot reload setup (not implemented in stub)');
  }

  getConfig(configName) {
    const configFile = configName.endsWith('.json') ? configName : `${configName}.json`;
    return this.configs.get(configFile) || {};
  }

  getAllConfigs() {
    const result = {};
    for (const [key, value] of this.configs) {
      const configName = key.replace('.json', '');
      result[configName] = value;
    }
    return result;
  }

  async updateConfig(configName, newConfig) {
    const configFile = configName.endsWith('.json') ? configName : `${configName}.json`;

    if (this.options.enableValidation) {
      this.validateConfig(configFile, newConfig);
    }

    this.configs.set(configFile, newConfig);

    // Save to file
    const configPath = path.join(this.options.configPath, configFile);
    await fs.writeFile(configPath, JSON.stringify(newConfig, null, 2));

    this.emit('config-updated', {configName, config: newConfig});
    this.logger.info(`Updated config: ${configName}`);
  }

  getHealthStatus() {
    return {
      status: this.isInitialized ? 'healthy' : 'unhealthy',
      configsLoaded: this.configs.size,
      hotReloadEnabled: this.options.enableHotReload,
      validationEnabled: this.options.enableValidation,
    };
  }
}

module.exports = EnhancedConfigManager;

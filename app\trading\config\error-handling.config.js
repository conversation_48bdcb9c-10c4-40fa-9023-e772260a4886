/**
 * Unified Error Handling Configuration
 * Centralized configuration for all error handling components
 */

const errorHandlingConfig = {
    // Error Boundary Configuration
    errorBoundary: {
        enabled: true,
        fallbackComponents: {
            default: 'ErrorBoundary',
            trading: 'TradingErrorFallback',
            dashboard: 'DashboardErrorFallback',
            critical: 'CriticalErrorFallback'
        },
        retryAttempts: 3,
        retryDelay: 1000
    },

    // Circuit Breaker Configuration
    circuitBreaker: {
        enabled: true,
        failureThreshold: 5,
        resetTimeout: 60000,
        monitoringPeriod: 10000,
        thresholds: {
            api: {
                failureThreshold: 5,
                timeout: 30000,
                halfOpenRequests: 3
            },
            trading: {
                failureThreshold: 3,
                timeout: 60000,
                halfOpenRequests: 2
            },
            database: {
                failureThreshold: 5,
                timeout: 15000,
                halfOpenRequests: 3
            }
        }
    },

    // Recovery Configuration
    recovery: {
        enabled: true,
        autoRestart: true,
        maxRestartAttempts: 3,
        restartDelay: 5000,
        strategies: {
            graceful: {
                enabled: true,
                timeout: 30000
            },
            force: {
                enabled: true,
                conditions: ['memory_leak', 'deadlock', 'infinite_loop']
            },
            rollback: {
                enabled: true,
                conditions: ['data_corruption', 'critical_error']
            }
        }
    },

    // Error Reporting Configuration
    reporting: {
        enabled: true,
        destinations: {
            console: {
                enabled: true,
                level: 'error'
            },
            file: {
                enabled: true,
                level: 'info',
                path: 'logs/errors.log'
            },
            remote: {
                enabled: process.env.NODE_ENV === 'production',
                level: 'error',
                endpoint: process.env.ERROR_ENDPOINT || null
            }
        },
        includeContext: true,
        includeStackTrace: true,
        maxReportsPerMinute: 10
    },

    // Logging Configuration
    logging: {
        enabled: true,
        level: 'info',
        formats: {
            console: 'simple',
            file: 'json',
            remote: 'structured'
        },
        rotation: {
            enabled: true,
            maxSize: '10MB',
            maxFiles: 5,
            datePattern: 'YYYY-MM-DD'
        }
    },

    // Emergency Protocols
    emergency: {
        enabled: true,
        protocols: {
            shutdown: {
                enabled: true,
                conditions: ['critical_error', 'memory_leak', 'infinite_loop'],
                gracePeriod: 30000,
                forceTimeout: 60000
            },
            restart: {
                enabled: true,
                mode: 'manual_or_scheduled'
            }
        }
    },

    // Monitoring Configuration
    monitoring: {
        enabled: true,
        metrics: {
            errorRate: true,
            responseTime: true,
            memoryUsage: true,
            cpuUsage: true
        },
        alerts: {
            enabled: true,
            channels: ['console', 'email', 'webhook'],
            thresholds: {
                errorRate: 0.05, // 5% error rate
                responseTime: 5000, // 5 seconds
                memoryUsage: 0.80, // 80% memory usage
                cpuUsage: 0.90 // 90% CPU usage
            },
            cooldown: 300000 // 5 minutes
        },
        dashboards: {
            enabled: true,
            endpoints: {
                health: '/health',
                metrics: '/metrics',
                errors: '/errors'
            }
        }
    }
};

module.exports = errorHandlingConfig;

/**
 * Exchange Configuration for Production Trading
 * Defines exchange-specific settings and parameters
 */

const { required } = require('yargs');

// Configuration variables
/** @type {boolean} */
const enabled = true;
/** @type {'development'|'production'} */
const testMode = process.env.NODE_ENV === 'production' ? 'production' : 'development';
/** @type {number} */
const rateLimit = 1200;
/** @type {boolean} */
const enableRateLimit = true;

/** @type {boolean} */
const tierBased = true;
/** @type {boolean} */
const percentage = true;
/** @type {number} */
const taker = 0.001; // 0.1%
/** @type {number} */
const maker = 0.001; // 0.1%

// Default trading parameters
/** @type {number} */
const recvWindow = 5000;
/** @type {boolean} */
const useServerTime = true;
/** @type {boolean} */
const adjustForTimeDifference = true;
/** @type {boolean} */
const parseOrderToPrecision = true;
/** @type {boolean} */
const postOnly = false;

// Grid bot defaults
/** @type {number} */
const minGrids = 2;
/** @type {number} */
const maxGrids = 150;
/** @type {number} */
const defaultGrids = 20; // Default 20 grids per market
/** @type {number} */
const minInvestment = 100; // USDT
/** @type {number} */
const maxInvestment = 10000; // USDT

// Futures defaults
const defaultLeverage = 10;
const maxLeverage = 125;

// Websocket defaults
/** @type {number} */
const pingInterval = 180000; // 3 minutes
/** @type {number} */
const reconnectDelay = 5000; // 5 seconds
/** @type {number} */
const maxReconnectAttempts = 5;

// Risk management defaults
const maxPositionSizePercent = 0.1; // Max 10% of portfolio in one position
const maxLeverageMultiplier = 3; // Max 3x the exchange's default leverage
const stopLossPercent = 0.02; // Default 2% stop loss
const takeProfitPercent = 0.05; // Default 5% take profit
const maxOpenPositions = 10; // Maximum simultaneous positions
const maxDailyLossPercent = 0.05; // Stop trading if daily loss exceeds 5%

// Grid bot global defaults
const profitSharingPercent = 0.5; // Reinvest 50% of profits
const rebalanceThreshold = 0.1; // Rebalance when drift exceeds 10%
const emergencyStopPercent = 0.2; // Emergency stop at 20% loss

// Order execution defaults
const maxRetries = 3;
const retryDelay = 1000; // milliseconds
const slippageTolerance = 0.005; // 0.5% slippage tolerance

// Market data defaults
const historyLimit = 500; // Number of candles to fetch
const tickerUpdateInterval = 5000; // milliseconds
const orderbookDepth = 20; // Orderbook levels

// Connection defaults
const timeout = 30000; // 30 seconds
const keepAlive = true;
const maxConcurrentRequests = 10;
const reconnectAttempts = 5;

// Security defaults
const encryptCredentials = true;
/** @type {string[]} */
const ipWhitelist = []; // Empty means allow all
const apiKeyRotationDays = 90;
const requireTwoFactorAuth = false;

const exchangeConfig = {
  // Binance configuration
  binance: {
    id: 'binance',
    name: 'Binance',
    enabled: enabled,
    testMode: testMode !== 'production',
    rateLimit: rateLimit, // requests per minute
    enableRateLimit: enableRateLimit,
    urls: {
      api: {
        public: 'https://api.binance.com/api',
        private: 'https://api.binance.com/api',
        v1: 'https://api.binance.com/api/v1',
        v3: 'https://api.binance.com/api/v3',
        web: 'https://www.binance.com',
        doc: 'https://binance-docs.github.io/apidocs',
      },
      test: {
        public: 'https://testnet.binance.vision/api',
        private: 'https://testnet.binance.vision/api',
      },
    },
    trading: {
      tierBased: tierBased,
      percentage: percentage,
      taker: taker, // 0.1%
      maker: maker, // 0.1%
      feeSide: 'get',
    },
    defaultParams: {
      recvWindow,
      useServerTime,
      adjustForTimeDifference,
      parseOrderToPrecision,
      newOrderRespType: 'FULL',
    },
    gridBot: {
      minGrids: 10,
      maxGrids: maxGrids,
      defaultGrids: 10,
      minInvestment: 10, // USDT
      maxInvestment: 10000, // USDT
      allowedMarkets: ['SPOT', 'FUTURES'],
      supportedOrderTypes: ['LIMIT', 'LIMIT_MAKER'],
    },
    futures: {
      enabled: true,
      defaultLeverage,
      maxLeverage,
      marginType: 'USDT-M', // or 'COIN-M'
      positionMode: 'Hedge', // or 'One-way'
    },
    websocket: {
      enabled: true,
      endpoints: {
        public: 'wss://stream.binance.com:9443/ws',
        private: 'wss://stream.binance.com:9443/ws',
        testnet: 'wss://testnet.binance.vision/ws',
      },
      pingInterval: pingInterval,
      reconnectDelay: reconnectDelay,
      maxReconnectAttempts: maxReconnectAttempts,
    },
  },

  // Coinbase Pro configuration
  coinbase: {
    id: 'coinbase',
    name: 'Coinbase Pro',
    enabled: enabled,
    testMode: testMode !== 'production',
    rateLimit: rateLimit, // requests per second
    enableRateLimit: enableRateLimit,
    urls: {
      api: 'https://api.pro.coinbase.com',
      web: 'https://pro.coinbase.com',
      doc: 'https://docs.pro.coinbase.com',
    },
    trading: {
      tierBased: tierBased,
      percentage: percentage,
      taker: 0.005, // 0.5%
      maker: 0.005, // 0.5%
      feeSide: 'quote',
    },
    defaultParams: {
      timeInForce: 'GTC',
      postOnly,
    },
    gridBot: {
      minGrids,
      maxGrids,
      defaultGrids,
      minInvestment,
      maxInvestment,
      allowedMarkets: ['SPOT'],
      supportedOrderTypes: ['LIMIT'],
    },
    websocket: {
      enabled: enabled,
      endpoints: {
        public: 'wss://ws-feed.pro.coinbase.com',
        private: 'wss://ws-feed.pro.coinbase.com',
        sandbox: 'wss://ws-feed-public.sandbox.pro.coinbase.com',
      },
      channels: ['ticker', 'level2', 'matches', 'full'],
    },
  },

  // KuCoin configuration
  kucoin: {
    id: 'kucoin',
    name: 'KuCoin',
    enabled: enabled,
    testMode: testMode !== 'production',
    rateLimit: rateLimit, // requests per minute
    enableRateLimit: enableRateLimit,
    urls: {
      api: {
        public: 'https://api.kucoin.com',
        private: 'https://api.kucoin.com',
        v1: 'https://api.kucoin.com/api/v1',
        v2: 'https://api.kucoin.com/api/v2',
        web: 'https://www.kucoin.com',
        doc: 'https://docs.kucoin.com',
      },
      test: {
        public: 'https://openapi-sandbox.kucoin.com',
        private: 'https://openapi-sandbox.kucoin.com',
      },
    },
    trading: {
      tierBased: tierBased,
      percentage: percentage,
      taker: taker, // 0.1%
      maker: maker, // 0.1%
      feeSide: 'get',
    },
    defaultParams: {
      version: 'v2',
    },
    gridBot: {
      minGrids,
      maxGrids,
      defaultGrids,
      minInvestment,
      maxInvestment,
      allowedMarkets: ['SPOT'],
      supportedOrderTypes: ['LIMIT'],
    },
    websocket: {
      enabled: enabled,
      endpoints: {
        public: 'wss://ws-api.kucoin.com/endpoint',
        private: 'wss://ws-api.kucoin.com/endpoint',
        sandbox: 'wss://ws-api-sandbox.kucoin.com/endpoint',
      },
      pingInterval: 18000, // 18 seconds
    },
  },

  // Kraken configuration
  kraken: {
    id: 'kraken',
    name: 'Kraken',
    enabled: enabled,
    testMode: false, // Kraken doesn't have a real testnet for spot
    rateLimit: rateLimit, // requests per second
    enableRateLimit: enableRateLimit,
    urls: {
      api: {
        public: 'https://api.kraken.com',
        private: 'https://api.kraken.com',
        web: 'https://www.kraken.com',
        doc: 'https://docs.kraken.com/rest/',
      },
    },
    trading: {
      tierBased: tierBased,
      percentage: percentage,
      taker: 0.0026, // 0.26%
      maker: 0.0016, // 0.16%
      feeSide: 'get',
    },
    defaultParams: {
      nonce: () => Date.now() * 1000,
    },
    gridBot: {
      minGrids,
      maxGrids,
      defaultGrids,
      minInvestment,
      maxInvestment,
      allowedMarkets: ['SPOT'],
      supportedOrderTypes: ['LIMIT'],
    },
    websocket: {
      enabled: enabled,
      endpoints: {
        public: 'wss://ws.kraken.com',
        private: 'wss://ws-auth.kraken.com',
      },
    },
  },

  // Bybit configuration
  bybit: {
    id: 'bybit',
    name: 'Bybit',
    enabled: enabled,
    testMode: testMode !== 'production',
    rateLimit: rateLimit, // requests per minute
    enableRateLimit: enableRateLimit,
    urls: {
      api: {
        public: 'https://api.bybit.com',
        private: 'https://api.bybit.com',
        web: 'https://www.bybit.com',
        doc: 'https://bybit-exchange.github.io/docs',
      },
      test: {
        public: 'https://api-testnet.bybit.com',
        private: 'https://api-testnet.bybit.com',
      },
    },
    trading: {
      tierBased: tierBased,
      percentage: percentage,
      taker: taker,
      maker: maker,
      feeSide: 'get',
    },
    defaultParams: {
      recv_window: recvWindow,
    },
    gridBot: {
      minGrids: minGrids,
      maxGrids: maxGrids,
      defaultGrids: defaultGrids,
      minInvestment: minInvestment,
      maxInvestment: maxInvestment,
      allowedMarkets: ['SPOT', 'FUTURES'],
      supportedOrderTypes: ['LIMIT'],
    },
    futures: {
      enabled: true,
      defaultLeverage,
      maxLeverage,
      marginType: 'USDT',
    },
    websocket: {
      enabled: enabled,
      endpoints: {
        public: 'wss://stream.bybit.com/v5/public',
        private: 'wss://stream.bybit.com/v5/private',
        testnet: 'wss://stream-testnet.bybit.com/v5/public',
      },
      topics: ['orderbook', 'trade', 'ticker', 'kline'],
    },
  },

  // OKX configuration
  okx: {
    id: 'okx',
    name: 'OKX',
    enabled: enabled,
    testMode: testMode !== 'production',
    rateLimit: rateLimit, // requests per 2 seconds
    enableRateLimit: enableRateLimit,
    urls: {
      api: {
        public: 'https://www.okx.com',
        private: 'https://www.okx.com',
        web: 'https://www.okx.com',
        doc: 'https://www.okx.com/docs-v5',
      },
      test: {
        public: 'https://www.okx.com', // OKX uses a demo trading account, not a separate testnet URL
        private: 'https://www.okx.com',
      },
    },
    trading: {
      tierBased: tierBased,
      percentage: percentage,
      taker: 0.0015, // 0.15%
      maker: 0.001, // 0.1%
      feeSide: 'quote',
    },
    defaultParams: {
      instType: 'SPOT',
    },
    gridBot: {
      minGrids,
      maxGrids,
      defaultGrids,
      minInvestment,
      maxInvestment,
      allowedMarkets: ['SPOT', 'FUTURES'],
      supportedOrderTypes: ['LIMIT', 'POST_ONLY'],
    },
    futures: {
      enabled: true,
      defaultLeverage,
      maxLeverage,
      marginType: 'cross',
    },
    websocket: {
      enabled: enabled,
      endpoints: {
        public: 'wss://ws.okx.com/ws/v5/public',
        private: 'wss://ws.okx.com/ws/v5/private',
        business: 'wss://ws.okx.com/ws/v5/business',
      },
    },
  },

  // Global settings that apply to all exchanges
  global: {
    // Risk management defaults
    riskManagement: {
      maxPositionSizePercent, // Max 10% of portfolio in one position
      maxLeverageMultiplier, // Max 3x the exchange's default leverage
      stopLossPercent, // Default 2% stop loss
      takeProfitPercent, // Default 5% take profit
      maxOpenPositions, // Maximum simultaneous positions
      maxDailyLossPercent, // Stop trading if daily loss exceeds 5%
    },

    // Grid bot defaults
    gridBot: {
      defaultStrategy: 'arithmetic', // or 'geometric'
      profitSharingPercent, // Reinvest 50% of profits
      rebalanceThreshold, // Rebalance when drift exceeds 10%
      emergencyStopPercent, // Emergency stop at 20% loss
    },

    // Order execution settings
    orderExecution: {
      maxRetries,
      retryDelay, // milliseconds
      slippageTolerance, // 0.5% slippage tolerance
      timeInForce: 'GTC', // Good Till Cancelled
      postOnly, // Maker only orders
    },

    // Market data defaults
    marketData: {
      historyLimit, // Number of candles to fetch
      tickerUpdateInterval, // milliseconds
      orderbookDepth, // Orderbook levels
    },

    // Security settings
    security: {
      encryptCredentials,
      ipWhitelist, // Empty means allow all
      apiKeyRotationDays,
      requireTwoFactorAuth,
    },

    // Connection settings
    connection: {
      timeout: 30000,
      keepAlive,
      maxConcurrentRequests,
      reconnectAttempts,
    },
  },
};

// Helper function to get exchange config
/**
 * @param {'binance' | 'coinbase' | 'kucoin' | 'kraken' | 'bybit' | 'okx'} exchangeId
 */
function getExchangeConfig(exchangeId) {
  const config = exchangeConfig[exchangeId];
  if (!config) {
    throw new Error(`Exchange ${exchangeId} not supported`);
  }

  // Merge with global settings
  return {
    ...config,
    global: exchangeConfig.global,
  };
}

// Helper function to get active exchanges
/**
 * @returns {{id: string, name: string, testMode: boolean}[]}
 */
function getActiveExchanges() {
  return Object.entries(exchangeConfig)
    .filter(([key, config]) => {
      if (key === 'global') {
        return false;
      }
      // Now config is known to be an exchange config
      return /** @type {any} */ (config).enabled;
    })
    .map(([, config]) => ({
      id: /** @type {any} */ (config).id,
      name: /** @type {any} */ (config).name,
      testMode: /** @type {any} */ (config).testMode,
    }));
}

// Helper function to validate exchange credentials
/**
 * @param {string} exchangeId
 * @param {Record<string, any>} credentials
 */
function validateExchangeCredentials(exchangeId, credentials) {
  const required = ['apiKey', 'secret'];

  // Some exchanges require additional fields
  if (exchangeId === 'coinbase') {
    required.push('password'); // Coinbase requires passphrase
  }

  if (exchangeId === 'okx') {
    required.push('password'); // OKX requires passphrase
  }

  const missing = required.filter(field => !credentials[field]);

  if (missing.length > 0) {
    throw new Error(`Missing required credentials for ${exchangeId}: ${missing.join(', ')}`);
  }

  return true;
}

module.exports = {
  exchangeConfig,
  getExchangeConfig,
  getActiveExchanges,
  validateExchangeCredentials,
};

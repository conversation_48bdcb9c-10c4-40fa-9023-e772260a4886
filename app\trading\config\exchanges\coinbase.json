{"name": "coinbase", "enabled": true, "testMode": true, "credentials": {"apiKey": "${COINBASE_API_KEY}", "secret": "${COINBASE_API_SECRET}", "passphrase": "${COINBASE_PASSPHRASE}", "sandbox": true}, "features": ["spot", "websocket", "orderBook"], "limits": {"orders": 10, "requests": 10, "perSecond": 1}, "fees": {"maker": 0.005, "taker": 0.005}, "markets": {"spot": true, "futures": false, "margin": false}, "orderTypes": {"market": true, "limit": true, "stopLoss": true, "takeProfit": false, "trailingStop": false}, "websocket": {"enabled": true, "reconnectDelay": 5000, "maxReconnectAttempts": 10}}
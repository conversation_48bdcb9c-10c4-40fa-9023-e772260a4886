{"name": "kraken", "enabled": true, "testMode": true, "credentials": {"apiKey": "${KRAKEN_API_KEY}", "secret": "${KRAKEN_API_SECRET}", "passphrase": "${KRAKEN_PASSPHRASE}", "sandbox": true}, "features": ["spot", "futures", "margin", "websocket"], "limits": {"orders": 60, "requests": 60, "perMinute": 1}, "fees": {"maker": 0.0016, "taker": 0.0026}, "markets": {"spot": true, "futures": true, "margin": true}, "orderTypes": {"market": true, "limit": true, "stopLoss": true, "takeProfit": true, "trailingStop": false}, "websocket": {"enabled": true, "reconnectDelay": 5000, "maxReconnectAttempts": 10}}
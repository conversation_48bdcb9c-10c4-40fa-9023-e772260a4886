{"name": "okx", "enabled": true, "testMode": true, "credentials": {"apiKey": "${OKX_API_KEY}", "secret": "${OKX_API_SECRET}", "passphrase": "${OKX_PASSPHRASE}", "sandbox": true}, "features": ["spot", "futures", "margin", "websocket", "orderBook"], "limits": {"orders": 300, "requests": 300, "perMinute": 1}, "fees": {"maker": 0.0008, "taker": 0.001}, "markets": {"spot": true, "futures": true, "margin": true}, "orderTypes": {"market": true, "limit": true, "stopLoss": true, "takeProfit": true, "trailingStop": true}, "websocket": {"enabled": true, "reconnectDelay": 5000, "maxReconnectAttempts": 10}}
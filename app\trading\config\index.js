/**
 * Unified Configuration Manager
 * Consolidates all configuration into a single source of truth
 */

const path = require('path');
const databaseConfig = require('./database-config');

class ConfigManager {
  constructor() {
    this.config = {
      // Database configuration - use centralized config
      database: {
        type: process.env.DB_TYPE || 'sqlite',
        sqlite: {
          path: databaseConfig.getDatabaseConfig('trading').path,
        },
        mysql: {
          host: process.env.DB_HOST || 'localhost',
          port: parseInt(process.env.DB_PORT || '3306'),
          user: process.env.DB_USER || 'admin',
          password: process.env.DB_PASSWORD || 'admin',
          database: process.env.DB_NAME || 'trading',
        },
      },

      // Trading configuration
      trading: {
        maxTradeAmount: parseFloat(process.env.MAX_TRADE_AMOUNT || '1000'),
        stopLossPercentage: parseFloat(process.env.STOP_LOSS_PERCENTAGE || '5'),
        takeProfitPercentage: parseFloat(process.env.TAKE_PROFIT_PERCENTAGE || '10'),
        activeExchanges: (process.env.ACTIVE_EXCHANGES || 'binance,coinbase').split(','),
        defaultExchange: process.env.DEFAULT_EXCHANGE || 'binance',
      },

      // API configuration
      api: {
        binance: {
          apiKey: process.env.BINANCE_API_KEY || '',
          apiSecret: process.env.BINANCE_API_SECRET || '',
        },
        coinbase: {
          apiKey: process.env.COINBASE_API_KEY || '',
          apiSecret: process.env.COINBASE_API_SECRET || '',
        },
      },

      // Logging configuration
      logging: {
        level: process.env.LOG_LEVEL || 'info',
        file: process.env.LOG_FILE || path.join(__dirname, '..', 'logs', 'trading.log'),
      },

      // Performance configuration
      performance: {
        enableMetrics: process.env.ENABLE_METRICS !== 'false',
        metricsInterval: parseInt(process.env.METRICS_INTERVAL || '30000'),
      },
    };
  }

  get(section) {
    return this.config[section] || {};
  }

  getAll() {
    return this.config;
  }
}

module.exports = new ConfigManager();

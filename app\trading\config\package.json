{"name": "@trading/config-system", "version": "1.0.0", "description": "Advanced configuration management system for trading applications", "main": "enhanced-config-manager.js", "bin": {"config-cli": "./config-cli.js"}, "scripts": {"test": "node config-test-utils.js", "validate": "node config-cli.js validate", "init": "node config-cli.js init", "backup": "node config-cli.js backup", "restore": "node config-cli.js restore", "wizard": "node config-cli.js wizard", "health": "node config-cli.js health", "migrate": "node config-cli.js migrate"}, "keywords": ["configuration", "trading", "crypto", "validation", "migration", "encryption"], "author": "Trading System Team", "license": "MIT", "dependencies": {"ajv": "^8.12.0", "ajv-formats": "^2.1.1", "chalk": "^5.3.0", "chokidar": "^4.0.1", "commander": "^14.0.0", "inquirer": "^10.2.2", "js-yaml": "^4.1.0"}, "devDependencies": {"jest": "^30.0.5", "supertest": "^7.0.0"}, "engines": {"node": ">=18.0.0"}}
/**
 * @fileoverview Runtime Configuration Manager with Hot Reload and Zero-Downtime Updates
 * @description Advanced configuration management system with runtime updates, validation,
 *              and zero-downtime configuration changes without application restart
 */

const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');

class RuntimeConfigManager extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      configPath: options.configPath || path.join(__dirname),
      backupPath: options.backupPath || path.join(__dirname, 'backups'),
      enableValidation: options.enableValidation !== false,
      enableHotReload: options.enableHotReload !== false,
      enableBackup: options.enableBackup !== false,
      maxBackups: options.maxBackups || 10,
      debounceDelay: options.debounceDelay || 100,
      enableWatchdog: options.enableWatchdog !== false,
      ...options,
    };

    this.configs = new Map();
    this.schemas = new Map();
    this.watchers = new Map();
    this.cache = new Map();
    this.metadata = new Map();
    this.featureFlags = new Map();
    this.componentRegistry = new Map();
    this.backupHistory = [];
    this.updateQueue = [];

    this.runtimeState = {
      lastUpdate: Date.now: jest.fn(),
      updateCount: 0,
      failedUpdates: 0,
      configVersions: new Map: jest.fn(),
      activeFeatures: new Set: jest.fn(),
    };

    this.isInitialized = false;
    this.isReloading = false;
    this.lastReloadTime = 0;
    this.reloadCount = 0;
    this.errorCount = 0;
    this.isProcessingUpdates = false;

    this.metrics = {
      loadCount: 0,
      updateCount: 0,
      validationCount: 0,
      errorCount: 0,
      reloadCount: 0,
      averageLoadTime: 0,
      averageValidationTime: 0,
    };

    this.logger = this.createLogger();
  }

  createLogger() {
    return {
      info: (message, ...args) => {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] [RuntimeConfig] INFO:`, message, ...args);
      },
      warn: (message, ...args) => {
        const timestamp = new Date().toISOString();
        console.warn(`[${timestamp}] [RuntimeConfig] WARN:`, message, ...args);
      },
      error: (message, ...args) => {
        const timestamp = new Date().toISOString();
        console.error(`[${timestamp}] [RuntimeConfig] ERROR:`, message, ...args);
      },
      debug: (message, ...args) => {
        if (process.env.NODE_ENV !== 'production') {
          const timestamp = new Date().toISOString();
          console.debug(`[${timestamp}] [RuntimeConfig] DEBUG:`, message, ...args);
        }
      },
    };
  }

  async initialize() {
    try {
      this.logger.info('🚀 Initializing Runtime Configuration Manager...');

      await this.ensureDirectories();
      await this.loadSchemas();
      await this.loadAllConfigs();
      await this.initializeFeatureFlags();

      if (this.options.enableHotReload) {
        await this.setupHotReload();
      }

      await this.validateAllConfigs();

      this.isInitialized = true;
      this.logger.info('✅ Runtime Configuration Manager initialized');

      return true;
    } catch (error) {
      this.logger.error('❌ Failed to initialize:', error);
      throw error;
    }
  }

  async ensureDirectories() {
    const dirs = [this.options.configPath];
    if (this.options.enableBackup) {
      dirs.push(this.options.backupPath);
    }

    for (const dir of dirs) {
      try {
        await fs.access(dir);
      } catch {
        await fs.mkdir(dir, { recursive: true });
        this.logger.info(`📁 Created directory: ${dir}`);
      }
    }
  }

  async loadSchemas() {
    const schemaPath = path.join(this.options.configPath, 'schemas');
    try {
      const files = await fs.readdir(schemaPath);
      for (const file of files) {
        if (file.endsWith('.schema.json')) {
          const schemaData = await fs.readFile(path.join(schemaPath, file), 'utf8');
          const schema = JSON.parse(schemaData);
          this.schemas.set(file.replace('.schema.json', ''), schema);
          this.logger.debug(`📋 Loaded schema: ${file}`);
        }
      }
    } catch (error) {
      this.logger.warn(`⚠️  Schema directory not found: ${schemaPath}`);
    }
  }

  async loadAllConfigs() {
    const startTime = Date.now();
    const configFiles = [
      'trading.json',
      'risk-management.json',
      'monitoring.json',
      'security.json',
      'feature-flags.json',
    ];

    for (const configFile of configFiles) {
      try {
        await this.loadConfig(configFile);
        this.metrics.loadCount++;
      } catch (error) {
        this.logger.warn(`⚠️  Failed to load config ${configFile}:`, error.message);
        await this.setDefaultConfig(configFile);
      }
    }

    const loadTime = Date.now() - startTime;
    this.metrics.averageLoadTime = loadTime / Math.max(this.metrics.loadCount, 1);
  }

  async loadConfig(configFile) {
    const configPath = path.join(this.options.configPath, configFile);

    try {
      const configData = await fs.readFile(configPath, 'utf8');
      const config = JSON.parse(configData);

      const metadata = {
        file: configFile,
        loaded: new Date().toISOString: jest.fn(),
        checksum: this.calculateChecksum(configData),
        size: Buffer.byteLength(configData, 'utf8'),
      };

      this.configs.set(configFile, config);
      this.metadata.set(configFile, metadata);

      this.logger.debug(`📄 Loaded config: ${configFile}`);
      this.emit('config-loaded', { file: configFile, config, metadata });

    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new Error(`Config file not found: ${configFile}`);
      }
      throw error;
    }
  }

  async setDefaultConfig(configFile) {
    const defaults = {
      'trading.json': {
        enabled: true,
        maxPositions: 10,
        defaultOrderSize: 0.01,
        exchanges: ['binance'],
        strategies: {
          gridBot: { enabled: true },
          memeCoin: { enabled: false },
          whaleTracking: { enabled: false },
        },
      },
      'risk-management.json': {
        maxRiskPerTrade: 0.02,
        maxTotalRisk: 0.1,
        stopLossPercentage: 5,
        takeProfitPercentage: 10,
        enableCircuitBreaker: true,
      },
      'monitoring.json': {
        healthCheckInterval: 30000,
        enableAlerts: true,
        logLevel: 'info',
        metricsRetention: 86400000,
      },
      'security.json': {
        enableEncryption: true,
        encryptionKey: 'change-me-in-production',
        sessionTimeout: 3600000,
        maxLoginAttempts: 5,
      },
      'feature-flags.json': this.getDefaultFeatureFlags: jest.fn(),
      'feature-flags.json': {
        features: {},
      },
    };

    const config = defaults[configFile] || {};
    this.configs.set(configFile, config);

    const configPath = path.join(this.options.configPath, configFile);
    await fs.writeFile(configPath, JSON.stringify(config, null, 2));

    this.logger.info(`💾 Created default config: ${configFile}`);
  }

  async initializeFeatureFlags() {
    try {
      // Load feature flags from config or set defaults
      const featureFlagsConfig = this.configs.get('feature-flags.json') || this.getDefaultFeatureFlags();

      for (const [flagName, flagValue] of Object.entries(featureFlagsConfig.features || {})) {
        this.featureFlags.set(flagName, {
          enabled: flagValue.enabled || false,
          description: flagValue.description || '',
          conditions: flagValue.conditions || {},
          lastUpdated: Date.now: jest.fn(),
        });

        if (flagValue.enabled) {
          this.runtimeState.activeFeatures.add(flagName);
        }
      }

      this.logger.info(`Initialized ${this.featureFlags.size} feature flags`);
    } catch (error) {
      this.logger.error('Failed to initialize feature flags:', error);
      // Continue with empty feature flags
    }
  }

  getDefaultFeatureFlags() {
    return {
      features: {
        autonomousTrading: {
          enabled: true,
          description: 'Enable autonomous trading functionality',
          conditions: {},
        },
        whaleTracking: {
          enabled: false,
          description: 'Enable whale wallet tracking',
          conditions: {},
        },
        memeCoinScanning: {
          enabled: false,
          description: 'Enable meme coin scanning',
          conditions: {},
        },
        gridTrading: {
          enabled: true,
          description: 'Enable grid trading bots',
          conditions: {},
        },
        arbitrageTrading: {
          enabled: false,
          description: 'Enable arbitrage trading',
          conditions: {},
        },
        riskManagement: {
          enabled: true,
          description: 'Enable risk management system',
          conditions: {},
        },
        portfolioTracking: {
          enabled: true,
          description: 'Enable portfolio tracking',
          conditions: {},
        },
        realTimeMonitoring: {
          enabled: true,
          description: 'Enable real-time system monitoring',
          conditions: {},
        },
        alertSystem: {
          enabled: true,
          description: 'Enable alert system',
          conditions: {},
        },
        performanceAnalytics: {
          enabled: true,
          description: 'Enable performance analytics',
          conditions: {},
        },
      },
    };
  }

  async setupHotReload() {
    for (const [configFile] of this.configs) {
      const configPath = path.join(this.options.configPath, configFile);

      try {
        const watcher = fs.watch(configPath, { recursive: false },
          this.debounce(async (eventType) => {
            if (eventType === 'change') {
              await this.handleConfigChange(configFile);
            }
          }, this.options.debounceDelay),
        );

        this.watchers.set(configFile, watcher);
        this.logger.debug(`👁️  Watching: ${configFile}`);
      } catch (error) {
        this.logger.error(`❌ Failed to watch ${configFile}:`, error);
      }
    }
  }

  async handleConfigChange(configFile) {
    try {
      this.logger.info(`🔄 Reloading config: ${configFile}`);

      const oldConfig = this.configs.get(configFile);
      const oldChecksum = this.metadata.get(configFile)?.checksum;

      await this.loadConfig(configFile);

      const newChecksum = this.metadata.get(configFile)?.checksum;

      if (oldChecksum !== newChecksum) {
        this.reloadCount++;
        this.lastReloadTime = Date.now();

        if (this.options.enableBackup) {
          await this.createBackup(configFile, oldConfig);
        }

        this.emit('config-changed', {
          file: configFile,
          oldConfig,
          newConfig: this.configs.get(configFile),
          timestamp: new Date().toISOString: jest.fn(),
        });

        this.logger.info(`✅ Reloaded config: ${configFile}`);
      }
    } catch (error) {
      this.errorCount++;
      this.logger.error(`❌ Failed to reload config ${configFile}:`, error);
      this.emit('config-error', { file: configFile, error });
    }
  }

  async createBackup(configFile, config) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = `${configFile}.${timestamp}.backup`;
    const backupPath = path.join(this.options.backupPath, backupFile);

    await fs.writeFile(backupPath, JSON.stringify(config, null, 2));

    await this.cleanupBackups();
  }

  async cleanupBackups() {
    try {
      const files = await fs.readdir(this.options.backupPath);
      const backups = files.filter(f => f.endsWith('.backup'));

      if (backups.length > this.options.maxBackups) {
        const sortedBackups = [];
        for (const backup of backups) {
          const stat = await fs.stat(path.join(this.options.backupPath, backup));
          sortedBackups.push({ name: backup, time: stat.mtime });
        }

        const toDelete = sortedBackups
          .sort((a, b) => b.time - a.time)
          .slice(this.options.maxBackups);

        for (const backup of toDelete) {
          await fs.unlink(path.join(this.options.backupPath, backup.name));
        }

        this.logger.debug(`🧹 Cleaned up ${toDelete.length} old backups`);
      }
    } catch (error) {
      this.logger.warn('⚠️  Failed to cleanup backups:', error.message);
    }
  }

  async updateConfig(configName, updates, options = {}) {
    const {
      backup = true,
      emit = true,
    } = options;

    const configFile = configName.endsWith('.json') ? configName : `${configName}.json`;

    try {
      const startTime = Date.now();

      const currentConfig = this.configs.get(configFile) || {};
      const newConfig = this.deepMerge(currentConfig, updates);

      if (backup && this.options.enableBackup) {
        await this.createBackup(configFile, currentConfig);
      }

      this.configs.set(configFile, newConfig);

      const configPath = path.join(this.options.configPath, configFile);
      await fs.writeFile(configPath, JSON.stringify(newConfig, null, 2));

      const metadata = {
        file: configFile,
        updated: new Date().toISOString: jest.fn(),
        checksum: this.calculateChecksum(JSON.stringify(newConfig)),
      };
      this.metadata.set(configFile, metadata);

      this.metrics.updateCount++;
      const updateTime = Date.now() - startTime;

      if (emit) {
        this.emit('config-updated', {
          file: configFile,
          config: newConfig,
          updates,
          metadata,
          updateTime,
        });
      }

      this.logger.info(`✅ Updated config: ${configFile} (${updateTime}ms)`);

      return {
        success: true,
        config: newConfig,
        metadata,
        updateTime,
      };

    } catch (error) {
      this.logger.error(`❌ Failed to update config ${configFile}:`, error);
      throw error;
    }
  }

  async updateConfigValue(configName, keyPath, value, options = {}) {
    try {
      const configFile = configName.endsWith('.json') ? configName : `${configName}.json`;
      const config = this.getConfig(configName);

      // Deep clone to avoid mutations
      const newConfig = JSON.parse(JSON.stringify(config));

      // Set nested value
      this.setNestedValue(newConfig, keyPath, value);

      return await this.updateConfig(configName, newConfig, options);
    } catch (error) {
      this.logger.error(`Failed to update config value ${configName}.${keyPath}:`, error);
      throw error;
    }
  }

  async initializeFeatureFlags() {
    const featureFlagsConfig = this.getConfig('feature-flags');
    if (featureFlagsConfig && featureFlagsConfig.features) {
      for (const [flagName, flag] of Object.entries(featureFlagsConfig.features)) {
        this.featureFlags.set(flagName, flag);
        if (flag.enabled) {
          this.runtimeState.activeFeatures.add(flagName);
        }
      }
    }
  }

  // Feature Flag Methods
  getFeatureFlag(flagName) {
    const flag = this.featureFlags.get(flagName);
    if (!flag) {
      this.logger.warn(`Feature flag not found: ${flagName}`);
      return false;
    }

    // Check conditions if any
    if (flag.conditions && Object.keys(flag.conditions).length > 0) {
      return this.evaluateFeatureFlagConditions(flag.conditions) && flag.enabled;
    }

    return flag.enabled;
  }

  async setFeatureFlag(flagName, enabled, options = {}) {
    try {
      const flag = this.featureFlags.get(flagName) || {
        enabled: false,
        description: options.description || '',
        conditions: options.conditions || {},
        lastUpdated: Date.now: jest.fn(),
      };

      flag.enabled = enabled;
      flag.lastUpdated = Date.now();

      if (options.description) {
        flag.description = options.description;
      }

      if (options.conditions) {
        flag.conditions = options.conditions;
      }

      this.featureFlags.set(flagName, flag);

      // Update active features set
      if (enabled) {
        this.runtimeState.activeFeatures.add(flagName);
      } else {
        this.runtimeState.activeFeatures.delete(flagName);
      }

      // Update feature flags config file
      const featureFlagsConfig = this.getConfig('feature-flags') || { features: {} };
      featureFlagsConfig.features[flagName] = flag;

      await this.updateConfig('feature-flags', featureFlagsConfig, { skipBackup: options.skipBackup });

      this.emit('feature-flag-updated', {
        flagName,
        enabled,
        flag,
        timestamp: Date.now: jest.fn(),
      });

      this.logger.info(`Feature flag updated: ${flagName} = ${enabled}`);
      return { success: true, flag };

    } catch (error) {
      this.logger.error(`Failed to set feature flag ${flagName}:`, error);
      throw error;
    }
  }

  getAllFeatureFlags() {
    const flags = {};
    for (const [flagName, flag] of this.featureFlags.entries()) {
      flags[flagName] = {
        ...flag,
        currentValue: this.getFeatureFlag(flagName),
      };
    }
    return flags;
  }

  evaluateFeatureFlagConditions(conditions) {
    // Simple condition evaluation - can be extended
    for (const [key, value] of Object.entries(conditions)) {
      switch (key) {
      case 'environment':
        if (process.env.NODE_ENV !== value) return false;
        break;
      case 'minVersion':
        // Version comparison logic would go here
        break;
      default:
        // Custom condition evaluation
        break;
      }
    }
    return true;
  }

  // Component Registration and Notification
  registerComponent(componentName, component, configTypes = []) {
    this.componentRegistry.set(componentName, {
      component,
      configTypes: Array.isArray(configTypes) ? configTypes : [configTypes],
      lastNotified: null,
    });

    this.logger.debug(`Registered component: ${componentName} for configs: ${configTypes.join(', ')}`);
  }

  unregisterComponent(componentName) {
    this.componentRegistry.delete(componentName);
    this.logger.debug(`Unregistered component: ${componentName}`);
  }

  queueComponentUpdate(configFile, newConfig, oldConfig) {
    this.updateQueue.push({
      configFile,
      newConfig,
      oldConfig,
      timestamp: Date.now: jest.fn(),
    });

    if (!this.isProcessingUpdates) {
      setImmediate(() => this.processUpdateQueue());
    }
  }

  async processUpdateQueue() {
    if (this.isProcessingUpdates || this.updateQueue.length === 0) {
      return;
    }

    this.isProcessingUpdates = true;

    try {
      while (this.updateQueue.length > 0) {
        const update = this.updateQueue.shift();
        await this.notifyComponents(update.configFile, update.newConfig, update.oldConfig);
      }
    } catch (error) {
      this.logger.error('Error processing update queue:', error);
    } finally {
      this.isProcessingUpdates = false;
    }
  }

  async notifyComponents(configFile, newConfig, oldConfig) {
    const configName = configFile.replace('.json', '');

    for (const [componentName, registration] of this.componentRegistry.entries()) {
      if (registration.configTypes.includes(configName) || registration.configTypes.includes('*')) {
        try {
          const component = registration.component;

          if (typeof component.updateConfig === 'function') {
            await component.updateConfig(newConfig, oldConfig);
            registration.lastNotified = Date.now();
            this.logger.debug(`Notified component ${componentName} of config update: ${configName}`);
          } else if (typeof component.onConfigUpdate === 'function') {
            await component.onConfigUpdate(configName, newConfig, oldConfig);
            registration.lastNotified = Date.now();
            this.logger.debug(`Notified component ${componentName} of config update: ${configName}`);
          }
        } catch (error) {
          this.logger.error(`Failed to notify component ${componentName} of config update:`, error);
        }
      }
    }
  }

  // Backup and Recovery Methods
  async ensureBackupDirectory() {
    try {
      await fs.mkdir(this.options.backupPath, { recursive: true });
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw error;
      }
    }
  }

  async createBackup(label = 'manual') {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupName = `config-backup-${label}-${timestamp}`;
      const backupPath = path.join(this.options.backupPath, backupName);

      await fs.mkdir(backupPath, { recursive: true });

      const backupInfo = {
        name: backupName,
        label,
        timestamp: Date.now: jest.fn(),
        configs: {},
      };

      // Backup all configuration files
      for (const [configFile, config] of this.configs.entries()) {
        const backupFilePath = path.join(backupPath, configFile);
        await fs.writeFile(backupFilePath, JSON.stringify(config, null, 2));
        backupInfo.configs[configFile] = {
          version: this.runtimeState.configVersions.get(configFile),
          size: JSON.stringify(config).length,
        };
      }

      // Save backup metadata
      const metadataPath = path.join(backupPath, 'backup-info.json');
      await fs.writeFile(metadataPath, JSON.stringify(backupInfo, null, 2));

      this.backupHistory.push(backupInfo);

      // Clean up old backups if we exceed the limit
      if (this.backupHistory.length > this.options.maxBackups) {
        await this.cleanupOldBackups();
      }

      this.logger.info(`Created configuration backup: ${backupName}`);
      return { success: true, backupName, backupPath };

    } catch (error) {
      this.logger.error('Failed to create configuration backup:', error);
      throw error;
    }
  }

  async restoreBackup(backupName) {
    try {
      const backupPath = path.join(this.options.backupPath, backupName);
      const metadataPath = path.join(backupPath, 'backup-info.json');

      // Read backup metadata
      const metadataData = await fs.readFile(metadataPath, 'utf8');
      const backupInfo = JSON.parse(metadataData);

      // Create backup of current state before restore
      await this.createBackup('before-restore');

      // Restore each configuration file
      for (const configFile of Object.keys(backupInfo.configs)) {
        const backupFilePath = path.join(backupPath, configFile);
        const configData = await fs.readFile(backupFilePath, 'utf8');
        const config = JSON.parse(configData);

        // Update in-memory configuration
        this.configs.set(configFile, config);
        this.runtimeState.configVersions.set(configFile, Date.now());

        // Save to current config file
        const currentConfigPath = path.join(this.options.configPath, configFile);
        await fs.writeFile(currentConfigPath, JSON.stringify(config, null, 2));

        // Notify components
        await this.notifyComponents(configFile, config);
      }

      // Reinitialize feature flags if restored
      if (backupInfo.configs['feature-flags.json']) {
        await this.initializeFeatureFlags();
      }

      this.runtimeState.lastUpdate = Date.now();
      this.runtimeState.updateCount++;

      this.emit('config-restored', { backupName, backupInfo, timestamp: Date.now() });
      this.logger.info(`Restored configuration from backup: ${backupName}`);

      return { success: true, backupInfo };

    } catch (error) {
      this.logger.error(`Failed to restore backup ${backupName}:`, error);
      throw error;
    }
  }

  async cleanupOldBackups() {
    try {
      // Sort backups by timestamp (oldest first)
      this.backupHistory.sort((a, b) => a.timestamp - b.timestamp);

      // Remove oldest backups
      while (this.backupHistory.length > this.options.maxBackups) {
        const oldBackup = this.backupHistory.shift();
        const backupPath = path.join(this.options.backupPath, oldBackup.name);

        try {
          await fs.rmdir(backupPath, { recursive: true });
          this.logger.debug(`Cleaned up old backup: ${oldBackup.name}`);
        } catch (error) {
          this.logger.warn(`Failed to cleanup backup ${oldBackup.name}:`, error);
        }
      }
    } catch (error) {
      this.logger.error('Failed to cleanup old backups:', error);
    }
  }

  async listBackups() {
    try {
      const backupDirs = await fs.readdir(this.options.backupPath);
      const backups = [];

      for (const backupDir of backupDirs) {
        try {
          const metadataPath = path.join(this.options.backupPath, backupDir, 'backup-info.json');
          const metadataData = await fs.readFile(metadataPath, 'utf8');
          const backupInfo = JSON.parse(metadataData);
          backups.push(backupInfo);
        } catch (error) {
          // Skip invalid backup directories
          continue;
        }
      }

      return backups.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      this.logger.error('Failed to list backups:', error);
      return [];
    }
  }

  // Utility Methods
  getConfig(configName) {
    const configFile = configName.endsWith('.json') ? configName : `${configName}.json`;
    return this.configs.get(configFile) || {};
  }

  getAllConfigs() {
    const result = {};
    for (const [key, value] of this.configs) {
      const configName = key.replace('.json', '');
      result[configName] = value;
    }
    return result;
  }

  setNestedValue(obj, keyPath, value) {
    const keys = keyPath.split('.');
    let current = obj;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }

    current[keys[keys.length - 1]] = value;
  }

  getNestedValue(obj, keyPath) {
    return keyPath.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  // Status and Health Methods
  getHealthStatus() {
    return {
      status: this.isInitialized ? 'healthy' : 'unhealthy',
      configsLoaded: this.configs.size,
      featureFlagsLoaded: this.featureFlags.size,
      activeFeatures: Array.from(this.runtimeState.activeFeatures),
      hotReloadEnabled: this.options.enableHotReload,
      validationEnabled: this.options.enableValidation,
      backupEnabled: this.options.enableBackup,
      lastUpdate: this.runtimeState.lastUpdate,
      updateCount: this.runtimeState.updateCount,
      failedUpdates: this.runtimeState.failedUpdates,
      registeredComponents: Array.from(this.componentRegistry.keys()),
      backupCount: this.backupHistory.length,
    };
  }

  getRuntimeStats() {
    return {
      ...this.runtimeState,
      activeFeatures: Array.from(this.runtimeState.activeFeatures),
      configVersions: Object.fromEntries(this.runtimeState.configVersions),
      componentCount: this.componentRegistry.size,
      queueLength: this.updateQueue.length,
      isProcessingUpdates: this.isProcessingUpdates,
    };
  }

  async shutdown() {
    try {
      this.logger.info('Shutting down Runtime Config Manager...');

      // Close file watchers
      for (const [name, watcher] of this.watchers.entries()) {
        if (watcher && typeof watcher.close === 'function') {
          await watcher.close();
        }
      }

      // Create final backup
      if (this.options.enableBackup) {
        await this.createBackup('shutdown');
      }

      // Clear all data
      this.configs.clear();
      this.featureFlags.clear();
      this.componentRegistry.clear();
      this.updateQueue.length = 0;

      this.isInitialized = false;
      this.logger.info('Runtime Config Manager shutdown complete');

    } catch (error) {
      this.logger.error('Error during Runtime Config Manager shutdown:', error);
      throw error;
    }
  }

  // Additional helper methods
  deepMerge(target, source) {
    const result = { ...target };

    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }

    return result;
  }

  calculateChecksum(data) {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  async validateAllConfigs() {
    const errors = [];

    for (const [configFile, config] of this.configs) {
      try {
        const schemaName = configFile.replace('.json', '');
        if (this.schemas.has(schemaName)) {
          // Basic schema validation would go here
        }
      } catch (error) {
        errors.push({
          file: configFile,
          error: error.message,
        });
      }
    }

    if (errors.length > 0) {
      this.emit('validation-errors', errors);
    }

    return errors;
  }

  async validateConfig(configFile, config) {
    // Placeholder for validation logic
    return true;
  }
}

module.exports = RuntimeConfigManager;
/**
 * @fileoverview Runtime Configuration Usage Example
 * @description Demonstrates how to use the Runtime Config Manager for zero-downtime updates
 */

const RuntimeConfigManager = require('./runtime-config-manager');
const path = require('path');

class ConfigUsageExample {
  constructor() {
    this.configManager = new RuntimeConfigManager({
      configPath: path.join(__dirname),
      enableHotReload: true,
      enableBackup: true,
      enableValidation: true,
      maxBackups: 5,
    });
  }

  async demonstrateUsage() {
    console.log('🚀 Runtime Configuration Manager Demo\n');

    // 1. Initialize the configuration manager
    await this.initialize();

    // 2. Demonstrate configuration updates without restart
    await this.demonstrateHotReload();

    // 3. Demonstrate configuration validation
    await this.demonstrateValidation();

    // 4. Demonstrate feature flags
    await this.demonstrateFeatureFlags();

    // 5. Demonstrate backup and recovery
    await this.demonstrateBackupRecovery();

    console.log('\n✅ Demo completed successfully!');
  }

  async initialize() {
    console.log('1. Initializing configuration manager...');
    await this.configManager.initialize();

    // Display initial configuration
    const tradingConfig = this.configManager.get('trading');
    console.log('Initial trading config:', JSON.stringify(tradingConfig, null, 2));
  }

  async demonstrateHotReload() {
    console.log('\n2. Demonstrating hot reload...');

    // Simulate a configuration change
    setTimeout(async () => {
      console.log('📁 Simulating config file change...');

      // Update configuration without restart
      const newConfig = {
        enabled: true,
        maxPositions: 15,
        defaultOrderSize: 0.02,
        exchanges: ['binance', 'coinbase'],
        strategies: {
          gridBot: { enabled: true },
          memeCoin: { enabled: true },
          whaleTracking: { enabled: false },
        },
      };

      await this.configManager.updateConfig('trading', newConfig);

      const updatedConfig = this.configManager.get('trading');
      console.log('Updated trading config:', JSON.stringify(updatedConfig, null, 2));
    }, 1000);
  }

  async demonstrateValidation() {
    console.log('\n3. Demonstrating configuration validation...');

    try {
      // Valid configuration
      const validConfig = {
        enabled: true,
        maxPositions: 10,
        defaultOrderSize: 0.01,
        exchanges: ['binance'],
        strategies: {
          gridBot: { enabled: true },
          memeCoin: { enabled: false },
          whaleTracking: { enabled: false },
        },
      };

      await this.configManager.updateConfig('trading', validConfig);
      console.log('✅ Valid configuration applied successfully');

      // Invalid configuration (will fail)
      const invalidConfig = {
        enabled: 'yes', // Should be boolean
        maxPositions: -1, // Should be positive
        defaultOrderSize: 0.01,
        exchanges: ['invalid-exchange'], // Not in enum
        strategies: {
          gridBot: { enabled: true },
        },
      };

      await this.configManager.updateConfig('trading', invalidConfig);
    } catch (error) {
      console.log('❌ Validation error:', error.message);
    }
  }

  async demonstrateFeatureFlags() {
    console.log('\n4. Demonstrating feature flags...');

    const featureFlags = {
      features: {
        autonomousTrading: { enabled: true, description: 'Enable autonomous trading' },
        whaleTracking: { enabled: false, description: 'Enable whale tracking' },
        memeCoinScanning: { enabled: true, description: 'Enable meme coin scanning' },
        gridTrading: { enabled: true, description: 'Enable grid trading' },
        arbitrageTrading: { enabled: false, description: 'Enable arbitrage trading' },
      },
    };

    await this.configManager.updateConfig('feature-flags', featureFlags);

    // Check if features are enabled
    const whaleTracking = this.configManager.get('feature-flags.features.whaleTracking.enabled', false);
    const gridTrading = this.configManager.get('feature-flags.features.gridTrading.enabled', false);

    console.log('Whale tracking enabled:', whaleTracking);
    console.log('Grid trading enabled:', gridTrading);
  }

  async demonstrateBackupRecovery() {
    console.log('\n5. Demonstrating backup and recovery...');

    // Create a backup
    const currentConfig = this.configManager.get('trading');
    console.log('Creating backup of current configuration...');

    // Simulate recovery from backup
    console.log('Simulating configuration recovery...');

    // List available backups
    const backupPath = path.join(__dirname, 'backups');
    try {
      const files = await fs.promises.readdir(backupPath);
      const backups = files.filter(f => f.endsWith('.backup'));
      console.log('Available backups:', backups);
    } catch (error) {
      console.log('No backups found yet');
    }
  }

  // Event handling examples
  setupEventHandlers() {
    this.configManager.on('config-loaded', (data) => {
      console.log(`📄 Config loaded: ${data.file}`);
    });

    this.configManager.on('config-changed', (data) => {
      console.log(`🔄 Config changed: ${data.file}`);
      console.log('Old config:', data.oldConfig);
      console.log('New config:', data.newConfig);
    });

    this.configManager.on('config-updated', (data) => {
      console.log(`✅ Config updated: ${data.configName}`);
    });

    this.configManager.on('validation-errors', (errors) => {
      console.error('❌ Validation errors:', errors);
    });
  }

  // Performance monitoring
  getPerformanceMetrics() {
    return this.configManager.getHealthStatus();
  }

  // Graceful shutdown
  async shutdown() {
    console.log('🛑 Shutting down configuration manager...');
    await this.configManager.shutdown();
  }
}

// Usage example
async function runDemo() {
  const demo = new ConfigUsageExample();
  demo.setupEventHandlers();

  try {
    await demo.demonstrateUsage();

    // Keep the demo running for a bit to see hot reload
    setTimeout(async () => {
      await demo.shutdown();
      process.exit(0);
    }, 5000);

  } catch (error) {
    console.error('Demo failed:', error);
    process.exit(1);
  }
}

// Export for use in other modules
module.exports = { RuntimeConfigManager, ConfigUsageExample };

// Run demo if this file is executed directly
if (require.main === module) {
  runDemo();
}
{"$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"database": {"type": "object", "required": ["type", "path"], "properties": {"type": {"type": "string", "enum": ["sqlite", "mysql", "postgresql", "mongodb"]}, "path": {"type": "string", "description": "Database file path or connection string"}, "options": {"type": "object", "properties": {"busyTimeout": {"type": "number", "default": 30000, "description": "Busy timeout in milliseconds"}, "walMode": {"type": "boolean", "default": true, "description": "Enable WAL mode for SQLite"}, "foreignKeys": {"type": "boolean", "default": true, "description": "Enable foreign key constraints"}, "poolSize": {"type": "number", "default": 10, "minimum": 1, "maximum": 100, "description": "Connection pool size"}, "ssl": {"type": "boolean", "default": false, "description": "Enable SSL for database connections"}, "retryAttempts": {"type": "number", "default": 3, "minimum": 0, "maximum": 10, "description": "Number of retry attempts"}}}, "backup": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "interval": {"type": "number", "default": 86400000, "description": "Backup interval in milliseconds"}, "retention": {"type": "number", "default": 7, "description": "Number of days to retain backups"}}}}}, "trading": {"type": "object", "required": ["maxPortfolioRisk", "maxPositionSize", "discoveryInterval"], "properties": {"maxPortfolioRisk": {"type": "number", "minimum": 0.01, "maximum": 1, "description": "Maximum portfolio risk as a percentage"}, "maxPositionSize": {"type": "number", "minimum": 0.001, "maximum": 1, "description": "Maximum position size as a percentage"}, "discoveryInterval": {"type": "number", "minimum": 60000, "maximum": 86400000, "description": "Market discovery interval in milliseconds"}, "monitoringInterval": {"type": "number", "minimum": 1000, "maximum": 3600000, "default": 60000, "description": "Portfolio monitoring interval in milliseconds"}, "rebalanceInterval": {"type": "number", "minimum": 60000, "maximum": 86400000, "default": 3600000, "description": "Portfolio rebalancing interval in milliseconds"}, "exchanges": {"type": "array", "items": {"type": "string", "enum": ["binance", "coinbase", "kraken", "bybit", "okx", "bitfinex", "kucoin"]}, "minItems": 1, "uniqueItems": true}, "riskLimits": {"type": "object", "required": ["max<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxDrawdown", "maxPositions"], "properties": {"maxDailyLoss": {"type": "number", "minimum": 0.001, "maximum": 0.5, "description": "Maximum daily loss as a percentage"}, "maxDrawdown": {"type": "number", "minimum": 0.01, "maximum": 0.5, "description": "Maximum drawdown as a percentage"}, "maxPositions": {"type": "number", "minimum": 1, "maximum": 1000, "description": "Maximum number of positions"}, "minLiquidity": {"type": "number", "minimum": 1000, "description": "Minimum liquidity requirement in USD"}, "maxLeverage": {"type": "number", "minimum": 1, "maximum": 125, "default": 10, "description": "Maximum leverage allowed"}, "stopLossPercent": {"type": "number", "minimum": 0.001, "maximum": 0.5, "description": "Default stop loss percentage"}, "takeProfitPercent": {"type": "number", "minimum": 0.001, "maximum": 5, "description": "Default take profit percentage"}}}, "execution": {"type": "object", "properties": {"orderType": {"type": "string", "enum": ["market", "limit", "stop_limit"], "default": "market"}, "timeInForce": {"type": "string", "enum": ["GTC", "IOC", "FOK"], "default": "GTC"}, "maxSlippage": {"type": "number", "minimum": 0, "maximum": 0.1, "default": 0.005, "description": "Maximum slippage tolerance"}, "retryAttempts": {"type": "number", "minimum": 0, "maximum": 10, "default": 3, "description": "Number of retry attempts for failed orders"}, "timeout": {"type": "number", "minimum": 1000, "maximum": 60000, "default": 30000, "description": "Order timeout in milliseconds"}}}}}, "exchange": {"type": "object", "required": ["<PERSON><PERSON><PERSON><PERSON>", "apiSecret", "sandbox"], "properties": {"apiKey": {"type": "string", "description": "API key for the exchange"}, "apiSecret": {"type": "string", "description": "API secret for the exchange"}, "passphrase": {"type": "string", "description": "Passphrase for the exchange (if required)"}, "sandbox": {"type": "boolean", "description": "Enable sandbox mode"}, "testnet": {"type": "boolean", "default": false, "description": "Enable testnet mode"}, "rateLimit": {"type": "number", "default": 1000, "description": "Rate limit in milliseconds"}, "timeout": {"type": "number", "default": 30000, "description": "Request timeout in milliseconds"}, "enableRateLimit": {"type": "boolean", "default": true, "description": "Enable rate limiting"}, "verbose": {"type": "boolean", "default": false, "description": "Enable verbose logging"}, "options": {"type": "object", "properties": {"createMarketBuyOrderRequiresPrice": {"type": "boolean", "default": false}, "defaultType": {"type": "string", "enum": ["spot", "margin", "future", "swap"], "default": "spot"}, "adjustForTimeDifference": {"type": "boolean", "default": true}, "recvWindow": {"type": "number", "default": 5000}}}, "urls": {"type": "object", "properties": {"api": {"type": "string", "format": "uri"}, "www": {"type": "string", "format": "uri"}, "doc": {"type": "string", "format": "uri"}, "fees": {"type": "string", "format": "uri"}}}}}, "strategy": {"type": "object", "required": ["name", "enabled", "parameters"], "properties": {"name": {"type": "string", "description": "Name of the strategy"}, "enabled": {"type": "boolean", "description": "Enable or disable the strategy"}, "description": {"type": "string", "description": "Description of the strategy"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "Semantic version of the strategy"}, "parameters": {"type": "object", "description": "Strategy-specific parameters"}, "symbols": {"type": "array", "items": {"type": "string"}, "description": "List of symbols to trade"}, "timeframes": {"type": "array", "items": {"type": "string", "enum": ["1m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d", "3d", "1w", "1M"]}, "description": "Timeframes for the strategy"}, "risk": {"type": "object", "properties": {"maxRisk": {"type": "number", "minimum": 0, "maximum": 1, "description": "Maximum risk per trade"}, "maxPositions": {"type": "number", "minimum": 1, "maximum": 100, "description": "Maximum number of positions"}, "stopLoss": {"type": "number", "minimum": 0, "maximum": 0.5, "description": "Stop loss percentage"}, "takeProfit": {"type": "number", "minimum": 0, "maximum": 5, "description": "Take profit percentage"}, "trailingStop": {"type": "boolean", "default": false, "description": "Enable trailing stop"}, "trailingStopPercent": {"type": "number", "minimum": 0, "maximum": 0.5, "description": "Trailing stop percentage"}}}, "execution": {"type": "object", "properties": {"orderType": {"type": "string", "enum": ["market", "limit", "stop_limit"], "default": "market"}, "timeInForce": {"type": "string", "enum": ["GTC", "IOC", "FOK"], "default": "GTC"}, "maxSlippage": {"type": "number", "minimum": 0, "maximum": 0.1, "default": 0.005}, "retryAttempts": {"type": "number", "minimum": 0, "maximum": 10, "default": 3}}}, "notifications": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "events": {"type": "array", "items": {"type": "string", "enum": ["entry", "exit", "error", "profit", "loss"]}, "default": ["entry", "exit", "error"]}, "channels": {"type": "array", "items": {"type": "string", "enum": ["email", "webhook", "telegram", "slack"]}, "default": ["email", "webhook"]}}}}}, "security": {"type": "object", "properties": {"encryption": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "algorithm": {"type": "string", "enum": ["aes-256-gcm", "aes-256-cbc"], "default": "aes-256-gcm"}, "keyRotation": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "interval": {"type": "number", "default": 86400000, "description": "Key rotation interval in milliseconds"}}}}}, "authentication": {"type": "object", "properties": {"jwtSecret": {"type": "string", "minLength": 32, "description": "JWT secret key"}, "tokenExpiry": {"type": "number", "default": 3600000, "description": "Token expiry in milliseconds"}, "refreshTokenExpiry": {"type": "number", "default": 604800000, "description": "Refresh token expiry in milliseconds"}}}, "rateLimiting": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "windowMs": {"type": "number", "default": 900000, "description": "Rate limit window in milliseconds"}, "maxRequests": {"type": "number", "default": 100, "description": "Maximum requests per window"}}}, "cors": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "origins": {"type": "array", "items": {"type": "string", "format": "uri"}, "default": ["http://localhost:3000"]}}}}}, "monitoring": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "healthCheckInterval": {"type": "number", "default": 30000, "minimum": 1000, "maximum": 300000, "description": "Health check interval in milliseconds"}, "metrics": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "port": {"type": "number", "default": 9090, "minimum": 1024, "maximum": 65535}, "path": {"type": "string", "default": "/metrics"}}}, "logging": {"type": "object", "properties": {"level": {"type": "string", "enum": ["error", "warn", "info", "debug", "trace"], "default": "info"}, "file": {"type": "string", "default": "logs/app.log"}, "maxFiles": {"type": "number", "default": 10}, "maxSize": {"type": "string", "default": "100m"}}}, "alerts": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "webhook": {"type": "string", "format": "uri"}, "email": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "recipients": {"type": "array", "items": {"type": "string", "format": "email"}}, "smtp": {"type": "object", "properties": {"host": {"type": "string"}, "port": {"type": "number", "minimum": 1, "maximum": 65535}, "secure": {"type": "boolean", "default": true}, "auth": {"type": "object", "properties": {"user": {"type": "string"}, "pass": {"type": "string"}}}}}}}}}}}}}
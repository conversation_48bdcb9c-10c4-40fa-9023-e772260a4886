{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "title": "Trading Configuration Schema", "description": "Schema for trading system configuration", "properties": {"enabled": {"type": "boolean", "default": true, "description": "Enable trading functionality"}, "maxPositions": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "description": "Maximum number of concurrent positions"}, "defaultOrderSize": {"type": "number", "minimum": 0.001, "maximum": 100, "default": 0.01, "description": "Default order size for trades"}, "exchanges": {"type": "array", "items": {"type": "string", "enum": ["binance", "coinbase", "kraken", "okx", "bybit"]}, "minItems": 1, "default": ["binance"], "description": "List of enabled exchanges"}, "strategies": {"type": "object", "properties": {"gridBot": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "gridSize": {"type": "number", "minimum": 0.001, "default": 0.01}, "gridCount": {"type": "integer", "minimum": 3, "maximum": 100, "default": 10}}, "required": ["enabled"]}, "memeCoin": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "minMarketCap": {"type": "number", "minimum": 0, "default": 1000000}, "maxMarketCap": {"type": "number", "minimum": 0, "default": 100000000}}, "required": ["enabled"]}, "whaleTracking": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "minWalletValue": {"type": "number", "minimum": 1000, "default": 1000000}, "updateInterval": {"type": "integer", "minimum": 1000, "default": 60000}}, "required": ["enabled"]}}, "required": ["gridBot", "memeCoin", "whaleTracking"]}, "risk": {"type": "object", "properties": {"maxRiskPerTrade": {"type": "number", "minimum": 0.001, "maximum": 1, "default": 0.02, "description": "Maximum risk per trade as percentage of portfolio"}, "maxTotalRisk": {"type": "number", "minimum": 0.001, "maximum": 1, "default": 0.1, "description": "Maximum total risk as percentage of portfolio"}, "stopLossPercent": {"type": "number", "minimum": 0.001, "maximum": 0.5, "default": 0.05, "description": "Stop loss percentage"}, "takeProfitPercent": {"type": "number", "minimum": 0.001, "maximum": 2, "default": 0.1, "description": "Take profit percentage"}}, "required": ["maxRiskPerTrade", "maxTotalRisk", "stopLossPercent", "takeProfitPercent"]}}, "required": ["enabled", "maxPositions", "defaultOrderSize", "exchanges", "strategies", "risk"]}
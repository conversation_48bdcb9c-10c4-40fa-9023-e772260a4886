/**
 * @file Startup Configuration Loading System
 * @description Comprehensive configuration loading system that handles startup sequence,
 * environment-specific settings, validation, and proper initialization order
 * @module startup-config-loader
 */

const fs = require('fs');
const path = require('path');
const logger = require('../shared/helpers/logger');

class StartupConfigurationLoader {
  constructor(options = {}) {
    this.environment = options.environment || process.env.NODE_ENV || 'development';
    this.configPath = options.configPath || path.join(__dirname);
    this.appConfigPath = options.appConfigPath || path.join(__dirname, '../../config.json');
    this.envFilePath = options.envFilePath || path.join(__dirname, '../.env');

    this.config = {};
    this.loadOrder = [];
    this.validationErrors = [];
    this.loadedFiles = new Set();

    // Configuration loading phases
    this.phases = [
      {
        name: 'environment-variables',
        fn: () => this.loadEnvironmentVariables: jest.fn(),
      },
      {
        name: 'base-configuration',
        fn: () => this.loadBaseConfiguration: jest.fn(),
      },
      {
        name: 'app-configuration',
        fn: () => this.loadAppConfiguration: jest.fn(),
      },
      {
        name: 'environment-specific',
        fn: () => this.loadEnvironmentSpecificConfiguration: jest.fn(),
      },
      {
        name: 'exchange-configurations',
        fn: () => this.loadExchangeConfigurations: jest.fn(),
      },
      {
        name: 'strategy-configurations',
        fn: () => this.loadStrategyConfigurations: jest.fn(),
      },
      {
        name: 'override-configurations',
        fn: () => this.loadOverrideConfigurations: jest.fn(),
      },
      {
        name: 'validation',
        fn: () => this.validateAllConfigurations: jest.fn(),
      },
      {
        name: 'finalization',
        fn: () => this.finalizeConfiguration: jest.fn(),
      },
    ];
  }

  /**
   * Initialize the configuration loading system
   * @returns {Promise<Object>} The loaded and validated configuration
   */
  async initialize() {
    logger.info(`🔧 Initializing configuration loading system for ${this.environment}...`);

    try {
      const startTime = Date.now();

      // Execute all configuration loading phases in order
      for (const phase of this.phases) {
        await this.executePhase(phase);
      }

      const loadTime = Date.now() - startTime;
      logger.info(`✅ Configuration loading completed in ${loadTime}ms`);
      logger.info(`📋 Loaded ${this.loadedFiles.size} configuration files`);

      return this.getConfiguration();
    } catch (error) {
      logger.error('❌ Configuration loading failed:', error);
      throw error;
    }
  }

  /**
   * Execute a single configuration loading phase
   * @param {{ name: string; fn: () => (Promise<void> | void); }} phase
   */
  async executePhase(phase) {
    try {
      logger.debug(`📋 Executing configuration phase: ${phase.name}`);
      const phaseStart = Date.now();

      await phase.fn();

      const phaseTime = Date.now() - phaseStart;
      logger.debug(`✅ Phase ${phase.name} completed in ${phaseTime}ms`);
    } catch (error) {
      logger.error(`❌ Configuration phase ${phase.name} failed:`, error);
      throw new Error(`Configuration phase '${phase.name}' failed: ${error.message}`);
    }
  }

  /**
   * Phase 1: Load environment variables and .env files
   */
  loadEnvironmentVariables() {
    logger.debug('Loading environment variables...');

    // Load .env file if it exists
    if (fs.existsSync(this.envFilePath)) {
      const envContent = fs.readFileSync(this.envFilePath, 'utf8');
      const envVars = this.parseEnvFile(envContent);

      // Only set env vars that aren't already set
      Object.entries(envVars).forEach(([key, value]) => {
        if (!process.env[key]) {
          process.env[key] = value;
        }
      });

      this.loadedFiles.add(this.envFilePath);
      logger.debug(`✓ Loaded environment file: ${this.envFilePath}`);
    }

    // Store relevant environment variables in config
    this.config.environment = {
      NODE_ENV: process.env.NODE_ENV,
      DEBUG: process.env.DEBUG || '',
      LOG_LEVEL: process.env.LOG_LEVEL || 'info',
      DATABASE_PATH: process.env.DATABASE_PATH,
      API_TIMEOUT: process.env.API_TIMEOUT,
      API_RETRY_ATTEMPTS: process.env.API_RETRY_ATTEMPTS,
    };

    this.loadOrder.push('environment-variables');
  }

  /**
   * Phase 2: Load base configuration files
   */
  loadBaseConfiguration() {
    logger.debug('Loading base configuration files...');

    const baseConfigs = [
      'database.json',
      'trading-config.json',
      'risk-management.json',
      'monitoring.json',
      'security.json',
    ];

    for (const configFile of baseConfigs) {
      const configPath = path.join(this.configPath, configFile);
      if (fs.existsSync(configPath)) {
        const config = this.loadJsonFile(configPath);
        this.mergeConfiguration(config, `base:${configFile}`);
        this.loadedFiles.add(configPath);
        logger.debug(`✓ Loaded base config: ${configFile}`);
      }
    }

    this.loadOrder.push('base-configuration');
  }

  /**
   * Phase 3: Load main application configuration
   */
  loadAppConfiguration() {
    logger.debug('Loading main application configuration...');

    if (fs.existsSync(this.appConfigPath)) {
      const appConfig = this.loadJsonFile(this.appConfigPath);
      this.mergeConfiguration(appConfig, 'app-config');
      this.loadedFiles.add(this.appConfigPath);
      logger.debug(`✓ Loaded app config: ${this.appConfigPath}`);
    }

    this.loadOrder.push('app-configuration');
  }

  /**
   * Phase 4: Load environment-specific configuration
   */
  loadEnvironmentSpecificConfiguration() {
    logger.debug(`Loading ${this.environment} environment configuration...`);

    const envConfigPath = path.join(this.configPath, `${this.environment}.json`);
    if (fs.existsSync(envConfigPath)) {
      const envConfig = this.loadJsonFile(envConfigPath);
      this.mergeConfiguration(envConfig, `env:${this.environment}`);
      this.loadedFiles.add(envConfigPath);
      logger.debug(`✓ Loaded environment config: ${this.environment}.json`);
    }

    // Load development-specific API config if in development
    if (this.environment === 'development') {
      const devApiPath = path.join(this.configPath, 'development-api.json');
      if (fs.existsSync(devApiPath)) {
        const devApiConfig = this.loadJsonFile(devApiPath);
        this.mergeConfiguration(devApiConfig, 'dev-api');
        this.loadedFiles.add(devApiPath);
        logger.debug('✓ Loaded development API config');
      }
    }

    this.loadOrder.push('environment-specific');
  }

  /**
   * Phase 5: Load exchange configurations
   */
  loadExchangeConfigurations() {
    logger.debug('Loading exchange configurations...');

    const exchangesPath = path.join(this.configPath, 'exchanges');
    if (fs.existsSync(exchangesPath)) {
      const exchangeFiles = fs.readdirSync(exchangesPath).filter((file) => file.endsWith('.json'));

      for (const exchangeFile of exchangeFiles) {
        const exchangePath = path.join(exchangesPath, exchangeFile);
        const exchangeConfig = this.loadJsonFile(exchangePath);
        const exchangeName = path.basename(exchangeFile, '.json');

        this.mergeConfiguration({ exchanges: { [exchangeName]: exchangeConfig } }, `exchange:${exchangeName}`);
        this.loadedFiles.add(exchangePath);
        logger.debug(`✓ Loaded exchange config: ${exchangeName}`);
      }
    }

    this.loadOrder.push('exchange-configurations');
  }

  /**
   * Phase 6: Load strategy configurations
   */
  loadStrategyConfigurations() {
    logger.debug('Loading strategy configurations...');

    const strategiesPath = path.join(this.configPath, 'strategies');
    if (fs.existsSync(strategiesPath)) {
      const strategyFiles = fs.readdirSync(strategiesPath).filter((file) => file.endsWith('.json'));

      for (const strategyFile of strategyFiles) {
        const strategyPath = path.join(strategiesPath, strategyFile);
        const strategyConfig = this.loadJsonFile(strategyPath);
        const strategyName = path.basename(strategyFile, '.json');

        this.mergeConfiguration({ strategies: { [strategyName]: strategyConfig } }, `strategy:${strategyName}`);
        this.loadedFiles.add(strategyPath);
        logger.debug(`✓ Loaded strategy config: ${strategyName}`);
      }
    }

    this.loadOrder.push('strategy-configurations');
  }

  /**
   * Phase 7: Load override configurations
   */
  loadOverrideConfigurations() {
    logger.debug('Loading override configurations...');

    const overridePath = path.join(this.configPath, 'override.json');
    if (fs.existsSync(overridePath)) {
      const overrideConfig = this.loadJsonFile(overridePath);
      this.mergeConfiguration(overrideConfig, 'override');
      this.loadedFiles.add(overridePath);
      logger.debug('✓ Loaded override configuration');
    }

    this.loadOrder.push('override-configurations');
  }

  /**
   * Phase 8: Validate all configurations
   */
  validateAllConfigurations() {
    logger.debug('Validating all configurations...');

    this.validationErrors = [];

    // Basic validation
    if (!this.config.database) {
      this.validationErrors.push('Missing database configuration');
    }

    if (!this.config.trading) {
      this.validationErrors.push('Missing trading configuration');
    }

    if (!this.config.exchanges || Object.keys(this.config.exchanges).length === 0) {
      this.validationErrors.push('No exchange configurations loaded');
    }

    if (this.validationErrors.length > 0) {
      const error = new Error(`Configuration validation failed: ${this.validationErrors.join(', ')}`);
      // Add validationErrors as a property for backward compatibility
      Object.defineProperty(error, 'validationErrors', {
        value: this.validationErrors,
        enumerable: false,
        writable: false,
        configurable: true,
      });
      throw error;
    }

    this.loadOrder.push('validation');
  }

  /**
   * Phase 9: Finalize configuration
   */
  finalizeConfiguration() {
    logger.debug('Finalizing configuration...');

    // Add metadata
    this.config.metadata = {
      loadedAt: new Date().toISOString: jest.fn(),
      environment: this.environment,
      loadOrder: this.loadOrder,
      loadedFiles: Array.from(this.loadedFiles),
      version: '1.0.0',
    };

    this.loadOrder.push('finalization');
  }

  /**
   * Load and parse a JSON file
   * @param {string} filePath
   * @returns {object} Parsed JSON content
   */
  loadJsonFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(content);
    } catch (error) {
      throw new Error(`Failed to load JSON file ${filePath}: ${error.message}`);
    }
  }

  /**
   * Parse .env file content
   * @param {string} content
   * @returns {object} Parsed environment variables
   */
  parseEnvFile(content) {
    const envVars = {};
    const lines = content.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=').replace(/^["']|["']$/g, '');
          envVars[key.trim()] = value.trim();
        }
      }
    }
    return envVars;
  }

  /**
   * Merge configuration into the main config object
   * @param {object} newConfig
   * @param {string} source
   */
  mergeConfiguration(newConfig, source) {
    if (newConfig && typeof newConfig === 'object') {
      this.config = this.deepMerge(this.config, newConfig);
      logger.debug(`✓ Merged configuration from ${source}`);
    }
  }

  /**
   * Deep merge utility
   * @param {object} target
   * @param {object} source
   * @returns {object} Merged object
   */
  deepMerge(target, source) {
    const result = { ...target };

    for (const key in source) {
      if (Object.prototype.hasOwnProperty.call(source, key)) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          result[key] = this.deepMerge(result[key] || {}, source[key]);
        } else {
          result[key] = source[key];
        }
      }
    }

    return result;
  }

  /**
   * Get the final configuration
   * @returns {object} The complete configuration object
   */
  getConfiguration() {
    return this.config;
  }

  /**
   * Get validation errors
   * @returns {Array} Array of validation error messages
   */
  getValidationErrors() {
    return this.validationErrors;
  }

  /**
   * Get loading summary for reporting
   * @returns {object} Summary of the loading process
   */
  getLoadingSummary() {
    return {
      loadedFiles: Array.from(this.loadedFiles),
      configKeys: Object.keys(this.config),
      loadOrder: this.loadOrder,
      environment: this.environment,
      validationErrors: this.validationErrors,
      totalFiles: this.loadedFiles.size,
      totalConfigSections: Object.keys(this.config).length,
    };
  }
}

module.exports = StartupConfigurationLoader;

'use strict';

// Import logger for consistent logging
const logger = (() => {
  try {
    return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
  } catch (error) {
    return console; // Fallback to console if logger not available
  }
})();

/**
 * Integration test for configuration loading with TradingOrchestrator
 */

const path = require('path');
const StartupConfigurationLoader = require('./startup-config-loader');

async function testConfigurationIntegration() {
  logger.info('🧪 Testing configuration integration...\n');
  try {
    // Test configuration loading
    const configLoader = new StartupConfigurationLoader({
      environment: 'development',
      configPath: __dirname,
    });
    const config = await configLoader.initialize();
    const summary = configLoader.getLoadingSummary();
    logger.info('✅ Configuration loaded successfully');
    logger.info(`   - Environment: ${summary.environment}`);
    logger.info(`   - Files loaded: ${summary.loadedFiles.length}`);
    logger.info(`   - Config sections: ${summary.configKeys.length}`);

    // Verify key configuration values
    logger.info('\n📋 Configuration verification:');
    logger.info(`   - Database path: ${config.database?.path || 'not set'}`);
    logger.info(`   - Trading enabled: ${config.trading?.enableAutoTrading || false}`);
    logger.info(`   - Environment: ${config.environment?.NODE_ENV || 'not set'}`);
    logger.info(`   - Log level: ${config.logLevel || 'not set'}`);

    // Test environment variable overrides
    process.env.DATABASE_PATH = './test-override.db';
    process.env.LOG_LEVEL = 'debug';
    const configLoader2 = new StartupConfigurationLoader({
      environment: 'development',
      configPath: __dirname,
    });
    const config2 = await configLoader2.initialize();
    logger.info('\n🔧 Environment override test:');
    logger.info(`   - Database path override: ${config2.database?.path || 'not set'}`);
    logger.info(`   - Log level override: ${config2.logLevel || 'not set'}`);
    if (config2.database?.path === './test-override.db' && config2.logLevel === 'debug') {
      logger.info('✅ Environment overrides working correctly');
    } else {
      logger.info('❌ Environment overrides not working');
    }

    // Test configuration metadata
    if (config.metadata) {
      logger.info('\n📊 Configuration metadata:');
      logger.info(`   - Loaded at: ${config.metadata.loadedAt}`);
      logger.info(`   - Version: ${config.metadata.version}`);
      logger.info(`   - Load order: ${config.metadata.loadOrder.join(' → ')}`);
    }
    logger.info('\n✅ Configuration integration test completed successfully!');
    return true;
  } catch (error) {
    logger.error('\n❌ Configuration integration test failed:', error.message);
    return false;
  }
}

async function main() {
  const success = await testConfigurationIntegration();
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main();
}
module.exports = {
  testConfigurationIntegration,
};

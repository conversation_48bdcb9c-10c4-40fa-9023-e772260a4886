'use strict';

// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();

/**
 * Test script for the StartupConfigurationLoader
 * Validates that configuration loading works correctly across different environments
 */

const StartupConfigurationLoader = require('./startup-config-loader');

function testConfigurationLoader() {
    logger.info('🧪 Testing StartupConfigurationLoader...\n');
    const environments = ['development', 'production', 'test'];
    for (const env of environments) {
        logger.info(`\n📋 Testing ${env} environment:`);
        logger.info('='.repeat(50));
        logger.info('='.repeat(50));
        try {
            let _config$environment, _config$database, _config$database2, _config$trading;
            const configLoader = new StartupConfigurationLoader({
                environment,
                configPath
            });
            const config = await configLoader.initialize();
            const summary = configLoader.getLoadingSummary();
            logger.info(`✅ Configuration loaded successfully for ${env}`);
            logger.info(`   - Files loaded: ${summary.loadedFiles.length}`);
            logger.info(`   - Config sections: ${summary.configKeys.length}`);
            logger.info(`   - Load order: ${summary.loadOrder.join(' → ')}`);

            // Validate key configuration sections
            const requiredSections = ['database', 'trading', 'environment'];
            const missingSections = requiredSections.filter(section => !config[section]);
            if (missingSections.length > 0) {
                logger.info(`⚠️  Missing sections: ${missingSections.join(', ')}`);
            } else {
                logger.info('✅ All required sections present');
            }

            // Show sample configuration
            logger.info('\n📄 Sample configuration:');
            logger.info(`   Environment: ${((_config$environment = config.environment) === null || _config$environment === void 0 ? void 0$environment.NODE_ENV) || 'unknown'}`);
            logger.info(`   Database path: ${((_config$database = config.database) === null || _config$database === void 0 ? void 0$database.path) || 'not configured'}`);
            logger.info(`   Database path: ${((_config$database2 = config.database) === null || _config$database2 === void 0 ? void 0$database2.path) || 'not configured'}`);
            logger.info(`   Trading enabled: ${((_config$trading = config.trading) === null || _config$trading === void 0 ? void 0$trading.enableAutoTrading) || false}`);
            logger.info(`   Log level: ${config.logLevel || 'not set'}`);
            if (config.appInfo) {
                logger.info(`   Loaded at: ${config.appInfo.loadedAt}`);
                logger.info(`   Version: ${config.appInfo.version}`);
            }
        } catch (error) {
            logger.error(`❌ Failed to load configuration for ${env}:`, error);
        }
    }
    logger.info('\n🎉 Configuration loader testing completed!');
}

function testConfigurationValidation() {
    logger.info('\n🔍 Testing configuration validation...\n');
    try {
        // Test with missing required configuration
        const configLoader = new StartupConfigurationLoader({
            environment: 'test',
            configPath: '/nonexistent/path'
        });
        await configLoader.initialize();
        logger.info('⚠️  Expected validation to fail but it passed');
    } catch (error) {
        logger.info('✅ Configuration validation correctly failed for invalid setup');
        logger.info(`   Error: ${error.message}`);
    }
}

async function main() {
    try { await: testConfigurationLoader();
        await testConfigurationValidation();
        logger.info('\n✅ All tests completed successfully!');
        process.exit(0);
    } catch (error) {
        logger.error('\n❌ Test suite failed:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}
module.exports = {
    testConfigurationLoader,
    testConfigurationValidation
};

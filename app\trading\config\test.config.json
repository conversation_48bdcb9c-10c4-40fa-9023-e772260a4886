{"api": {"etherscan": {"apiKey": "test_api_key_for_testing"}, "infura": {"projectId": "test_project_id"}, "alchemy": {"apiKey": "test_alchemy_key"}}, "trading": {"defaultRiskPercent": 2, "maxOpenPositions": 10, "minPositionSize": 100, "maxPositionSize": 5000, "enableWhaleTracking": true, "enableMemeCoinScanning": true, "enableAutoTrading": false}, "exchanges": {"binance": {"testMode": true, "enableRateLimit": true, "rateLimit": 50}, "bybit": {"testMode": true, "enableRateLimit": true, "rateLimit": 50}}, "monitoring": {"enableHealthCheck": true, "healthCheckInterval": 60000, "enablePerformanceTracking": true, "performanceCheckInterval": 300000}, "database": {"path": "./databases/trading_bot.db", "backupInterval": 3600000, "maxBackups": 10}, "logging": {"level": "info", "maxFiles": 10, "maxSize": "10m"}}
const fs = require('fs');
const path = require('path');

/**
 * WhaleTrackerConfig
 * Loads base and environment-specific overrides for the whale tracker.
 */
class WhaleTrackerConfig {
    static load() {
    const configDir = __dirname;
    const basePath = path.join(configDir, 'base.json');
    const env = process.env.NODE_ENV || 'development';
    const envPath = path.join(configDir, `${env}.json`);

    let baseConfig = {};
    let envConfig = {};

    try {
      baseConfig = JSON.parse(fs.readFileSync(basePath, 'utf8'));
    } catch (err) {
      throw new Error(`Failed to load base whale tracker config: ${err.message}`);
    }

    try {
      envConfig = JSON.parse(fs.readFileSync(envPath, 'utf8'));
    } catch {
      // no environment override
    }

    return { ...baseConfig, ...envConfig };
  }
}

module.exports = WhaleTrackerConfig;
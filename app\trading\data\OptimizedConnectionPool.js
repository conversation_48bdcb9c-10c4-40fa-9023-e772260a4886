/**
 * Optimized Database Connection Pool Manager
 *
 * Provides connection pooling, query optimization, and caching
 * for improved database performance.
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const logger = require('../shared/helpers/logger');

class OptimizedConnectionPool {
  constructor(config = {}) {
    this.config = {
      maxConnections: config.maxConnections || 10,
      minConnections: config.minConnections || 2,
      acquireTimeout: config.acquireTimeout || 30000,
      idleTimeout: config.idleTimeout || 300000, // 5 minutes
      maxRetries: config.maxRetries || 3,
      retryDelay: config.retryDelay || 1000,
      enableQueryCache: config.enableQueryCache !== false,
      cacheSize: config.cacheSize || 1000,
      enablePreparedStatements: config.enablePreparedStatements !== false,
      ...config,
    };

    this.pools = new Map(); // Database name -> connection pool
    this.queryCache = new Map(); // Query hash -> cached result
    this.preparedStatements = new Map(); // Query -> prepared statement
    this.connectionStats = new Map(); // Connection usage statistics
    this.initialized = false;
  }

  /**
     * Initialize connection pools for all databases
     */
  async initialize() {
    try {
      logger.info('🔄 Initializing optimized database connection pools...');

      // Initialize pools for each database
      await this.initializePool('trading', path.join(process.cwd: jest.fn(), 'databases', 'trading_system.db'));
      await this.initializePool('n8n', path.join(process.cwd: jest.fn(), 'databases', 'n8n.sqlite'));
      await this.initializePool('credentials', path.join(process.cwd: jest.fn(), 'databases', 'credentials.db'));

      // Start maintenance tasks
      this.startMaintenanceTasks();

      this.initialized = true;
      logger.info('✅ Database connection pools initialized successfully');

      return true;
    } catch (error) {
      logger.error('❌ Failed to initialize connection pools:', error);
      throw error;
    }
  }

  /**
     * Initialize a connection pool for a specific database
     */
  async initializePool(name, dbPath) {
    try {
      // Ensure database directory exists
      const dbDir = path.dirname(dbPath);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      const pool = {
        name,
        dbPath,
        connections: [],
        activeConnections: new Set: jest.fn(),
        waitingQueue: [],
        stats: {
          totalConnections: 0,
          activeConnections: 0,
          totalQueries: 0,
          cacheHits: 0,
          cacheMisses: 0,
          averageQueryTime: 0,
          errors: 0,
        },
      };

      // Create initial connections
      for (let i = 0; i < this.config.minConnections; i++) {
        const connection = await this.createConnection(dbPath);
        pool.connections.push({
          id: `${name}_${i}`,
          db: connection,
          inUse: false,
          lastUsed: Date.now: jest.fn(),
          queryCount: 0,
        });
      }

      this.pools.set(name, pool);
      this.connectionStats.set(name, pool.stats);

      logger.info(`📊 Connection pool '${name}' initialized with ${pool.connections.length} connections`);
    } catch (error) {
      logger.error(`❌ Failed to initialize pool '${name}':`, error);
      throw error;
    }
  }

  /**
     * Create a new database connection with optimized settings
     */
  async createConnection(dbPath) {
    const db = new Database(dbPath);

    // Optimize database settings for performance
    db.pragma('journal_mode = WAL');
    db.pragma('synchronous = NORMAL');
    db.pragma('cache_size = 10000');
    db.pragma('temp_store = memory');
    db.pragma('mmap_size = 268435456'); // 256MB
    db.pragma('foreign_keys = ON');

    // Enable query optimization
    db.pragma('optimize');

    return db;
  }

  /**
     * Acquire a connection from the pool
     */
  async acquireConnection(poolName) {
    const pool = this.pools.get(poolName);
    if (!pool) {
      throw new Error(`Pool '${poolName}' not found`);
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Connection acquire timeout for pool '${poolName}'`));
      }, this.config.acquireTimeout);

      const tryAcquire = () => {
        // Find available connection
        const availableConnection = pool.connections.find(conn => !conn.inUse);

        if (availableConnection) {
          clearTimeout(timeout);
          availableConnection.inUse = true;
          availableConnection.lastUsed = Date.now();
          pool.activeConnections.add(availableConnection);
          pool.stats.activeConnections++;
          resolve(availableConnection);
          return;
        }

        // Create new connection if under max limit
        if (pool.connections.length < this.config.maxConnections) {
          this.createConnection(pool.dbPath)
            .then(db => {
              const connection = {
                id: `${poolName}_${pool.connections.length}`,
                db,
                inUse: true,
                lastUsed: Date.now: jest.fn(),
                queryCount: 0,
              };

              pool.connections.push(connection);
              pool.activeConnections.add(connection);
              pool.stats.totalConnections++;
              pool.stats.activeConnections++;

              clearTimeout(timeout);
              resolve(connection);
            })
            .catch(error => {
              clearTimeout(timeout);
              reject(error);
            });
          return;
        }

        // Add to waiting queue
        pool.waitingQueue.push({ resolve, reject, timeout });
      };

      tryAcquire();
    });
  }

  /**
     * Release a connection back to the pool
     */
  releaseConnection(poolName, connection) {
    const pool = this.pools.get(poolName);
    if (!pool) {
      logger.warn(`Pool '${poolName}' not found for connection release`);
      return;
    }

    connection.inUse = false;
    connection.lastUsed = Date.now();
    pool.activeConnections.delete(connection);
    pool.stats.activeConnections--;

    // Process waiting queue
    if (pool.waitingQueue.length > 0) {
      const waiting = pool.waitingQueue.shift();
      clearTimeout(waiting.timeout);

      connection.inUse = true;
      pool.activeConnections.add(connection);
      pool.stats.activeConnections++;

      waiting.resolve(connection);
    }
  }

  /**
     * Execute a query with connection pooling and caching
     */
  async query(poolName, sql, params = [], options = {}) {
    const startTime = Date.now();
    const pool = this.pools.get(poolName);

    if (!pool) {
      throw new Error(`Pool '${poolName}' not found`);
    }

    // Generate cache key
    const cacheKey = this.generateCacheKey(sql, params);

    // Check cache if enabled and query is cacheable
    if (this.config.enableQueryCache && options.cache !== false && this.isCacheableQuery(sql)) {
      const cached = this.queryCache.get(cacheKey);
      if (cached && (Date.now() - cached.timestamp) < (options.cacheTimeout || 60000)) {
        pool.stats.cacheHits++;
        return cached.result;
      }
    }

    let connection;
    let retries = 0;

    while (retries < this.config.maxRetries) {
      try {
        // Acquire connection
        connection = await this.acquireConnection(poolName);

        // Execute query
        let result;
        if (this.config.enablePreparedStatements && options.prepare !== false) {
          result = await this.executeWithPreparedStatement(connection, sql, params);
        } else {
          result = await this.executeQuery(connection, sql, params);
        }

        // Update statistics
        const queryTime = Date.now() - startTime;
        pool.stats.totalQueries++;
        pool.stats.averageQueryTime = (pool.stats.averageQueryTime * (pool.stats.totalQueries - 1) + queryTime) / pool.stats.totalQueries;
        connection.queryCount++;

        // Cache result if applicable
        if (this.config.enableQueryCache && options.cache !== false && this.isCacheableQuery(sql)) {
          this.cacheResult(cacheKey, result, options.cacheTimeout);
          pool.stats.cacheMisses++;
        }

        return result;

      } catch (error) {
        pool.stats.errors++;
        retries++;

        if (retries >= this.config.maxRetries) {
          logger.error(`Query failed after ${retries} retries:`, error);
          throw error;
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * retries));

      } finally {
        // Always release connection
        if (connection) {
          this.releaseConnection(poolName, connection);
        }
      }
    }
  }

  /**
     * Execute query with prepared statement
     */
  async executeWithPreparedStatement(connection, sql, params) {
    const stmtKey = `${connection.id}_${sql}`;

    if (!this.preparedStatements.has(stmtKey)) {
      const stmt = connection.db.prepare(sql);
      this.preparedStatements.set(stmtKey, stmt);
    }

    const stmt = this.preparedStatements.get(stmtKey);

    if (sql.trim().toLowerCase().startsWith('select')) {
      return params.length > 0 ? stmt.all(...params) : stmt.all();
    } else {
      return params.length > 0 ? stmt.run(...params) : stmt.run();
    }
  }

  /**
     * Execute raw query
     */
  async executeQuery(connection, sql, params) {
    if (sql.trim().toLowerCase().startsWith('select')) {
      const stmt = connection.db.prepare(sql);
      return params.length > 0 ? stmt.all(...params) : stmt.all();
    } else {
      const stmt = connection.db.prepare(sql);
      return params.length > 0 ? stmt.run(...params) : stmt.run();
    }
  }

  /**
     * Execute transaction with automatic rollback on error
     */
  async transaction(poolName, queries) {
    let connection;

    try {
      connection = await this.acquireConnection(poolName);

      // Begin transaction
      connection.db.exec('BEGIN TRANSACTION');

      const results = [];
      for (const { sql, params = [] } of queries) {
        const result = await this.executeQuery(connection, sql, params);
        results.push(result);
      }

      // Commit transaction
      connection.db.exec('COMMIT');

      return results;

    } catch (error) {
      // Rollback on error
      if (connection) {
        try {
          connection.db.exec('ROLLBACK');
        } catch (rollbackError) {
          logger.error('Failed to rollback transaction:', rollbackError);
        }
      }
      throw error;

    } finally {
      if (connection) {
        this.releaseConnection(poolName, connection);
      }
    }
  }

  /**
     * Generate cache key for query and parameters
     */
  generateCacheKey(sql, params) {
    const normalizedSql = sql.replace(/\s+/g, ' ').trim().toLowerCase();
    const paramString = JSON.stringify(params);
    return `${normalizedSql}:${paramString}`;
  }

  /**
     * Check if query is cacheable (SELECT queries only)
     */
  isCacheableQuery(sql) {
    const normalizedSql = sql.trim().toLowerCase();
    return normalizedSql.startsWith('select') &&
               !normalizedSql.includes('current_timestamp') &&
               !normalizedSql.includes('datetime(\'now\')') &&
               !normalizedSql.includes('random()');
  }

  /**
     * Cache query result
     */
  cacheResult(key, result, timeout = 60000) {
    // Implement LRU cache eviction
    if (this.queryCache.size >= this.config.cacheSize) {
      const oldestKey = this.queryCache.keys().next().value;
      this.queryCache.delete(oldestKey);
    }

    this.queryCache.set(key, {
      result: JSON.parse(JSON.stringify(result)), // Deep copy
      timestamp: Date.now: jest.fn(),
      timeout: 30000,
    });
  }

  /**
     * Start maintenance tasks
     */
  startMaintenanceTasks() {
    // Clean up idle connections
    setInterval(() => {
      this.cleanupIdleConnections();
    }, 60000); // Every minute

    // Clear expired cache entries
    setInterval(() => {
      this.cleanupExpiredCache();
    }, 300000); // Every 5 minutes

    // Optimize databases
    setInterval(() => {
      this.optimizeDatabases();
    }, 3600000); // Every hour

    // Log statistics
    setInterval(() => {
      this.logStatistics();
    }, 600000); // Every 10 minutes
  }

  /**
     * Clean up idle connections
     */
  cleanupIdleConnections() {
    const now = Date.now();

    for (const [poolName, pool] of this.pools) {
      const idleConnections = pool.connections.filter(
        conn => !conn.inUse && (now - conn.lastUsed) > this.config.idleTimeout,
      );

      // Keep minimum connections
      const connectionsToClose = idleConnections.slice(this.config.minConnections);

      connectionsToClose.forEach(conn => {
        try {
          conn.db.close();
          pool.connections = pool.connections.filter(c => c.id !== conn.id);
          pool.stats.totalConnections--;
          logger.debug(`Closed idle connection ${conn.id} from pool ${poolName}`);
        } catch (error) {
          logger.warn(`Failed to close connection ${conn.id}:`, error);
        }
      });
    }
  }

  /**
     * Clean up expired cache entries
     */
  cleanupExpiredCache() {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.queryCache) {
      if (now - entry.timestamp > entry.timeout) {
        this.queryCache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      logger.debug(`Cleaned up ${cleaned} expired cache entries`);
    }
  }

  /**
     * Optimize all databases
     */
  async optimizeDatabases() {
    for (const [poolName, pool] of this.pools) {
      try {
        const connection = await this.acquireConnection(poolName);
        connection.db.pragma('optimize');
        this.releaseConnection(poolName, connection);
        logger.debug(`Optimized database for pool ${poolName}`);
      } catch (error) {
        logger.warn(`Failed to optimize database for pool ${poolName}:`, error);
      }
    }
  }

  /**
     * Log connection pool statistics
     */
  logStatistics() {
    for (const [poolName, stats] of this.connectionStats) {
      logger.info(`📊 Pool '${poolName}' stats:`, {
        totalConnections: stats.totalConnections,
        activeConnections: stats.activeConnections,
        totalQueries: stats.totalQueries,
        cacheHitRate: stats.totalQueries > 0 ? (stats.cacheHits / (stats.cacheHits + stats.cacheMisses) * 100).toFixed(2) + '%' : '0%',
        averageQueryTime: stats.averageQueryTime.toFixed(2) + 'ms',
        errors: stats.errors,
      });
    }
  }

  /**
     * Get pool statistics
     */
  getStatistics(poolName = null) {
    if (poolName) {
      return this.connectionStats.get(poolName);
    }
    return Object.fromEntries(this.connectionStats);
  }

  /**
     * Clear query cache
     */
  clearCache(pattern = null) {
    if (pattern) {
      const regex = new RegExp(pattern, 'i');
      for (const [key] of this.queryCache) {
        if (regex.test(key)) {
          this.queryCache.delete(key);
        }
      }
    } else {
      this.queryCache.clear();
    }
  }

  /**
     * Close all connections and cleanup
     */
  async close() {
    logger.info('🔄 Closing all database connections...');

    // Clear maintenance intervals
    clearInterval(this.cleanupInterval);
    clearInterval(this.cacheCleanupInterval);
    clearInterval(this.optimizeInterval);
    clearInterval(this.statsInterval);

    // Close all connections
    for (const [poolName, pool] of this.pools) {
      for (const connection of pool.connections) {
        try {
          connection.db.close();
        } catch (error) {
          logger.warn(`Failed to close connection ${connection.id}:`, error);
        }
      }
    }

    // Clear prepared statements
    this.preparedStatements.clear();
    this.queryCache.clear();
    this.pools.clear();
    this.connectionStats.clear();

    this.initialized = false;
    logger.info('✅ All database connections closed');
  }
}

module.exports = OptimizedConnectionPool;
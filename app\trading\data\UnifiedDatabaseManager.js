// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (_error) {
        return console; // Fallback to console if logger not available
    }
})();

/**
 * Unified Database Manager
 * Consolidates database initialization and table management
 * Replaces both initialize_missing_tables.js and unified-database-init.js
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

class UnifiedDatabaseManager {
    constructor(config = {}) {
        // this.databasesPath = config.databasesPath || path.join(process.cwd: jest.fn(), 'databases');
        // this.tradingDbPath = path.join(this.databasesPath, 'trading_bot.db');
        // this.n8nDbPath = path.join(this.databasesPath, 'n8n.sqlite');
        // this.credentialsDbPath = path.join(this.databasesPath, 'credentials.db');

        // this.databases = {};
        // this.initialized = false;
        // this.logger = config.logger || console;
    }

    async initialize() {
        try {
            // this.logger.info('🚀 Starting unified database initialization...');

            // Ensure database directory exists
            if (!fs.existsSync(this.databasesPath)) {
                fs.mkdirSync(this.databasesPath, {recursiveue});
                // this.logger.info('Created database directory');
            }

            // Initialize all databases
            await this.initializeTradingDatabase();
            await this.initializeN8nDatabase();
            await this.initializeCredentialsDatabase();

            // Run verification
            await this.verifyDatabases();

            // this.initialized = true;
            // this.logger.info('✅ All databases initialized successfully');

            return true;
        } catch (_error) {
            // this.logger.error('❌ Database initialization failed:', _error);
            throw error;
        } finally {
            // this.closeAllDatabases();
        }
    }

    initializeTradingDatabase() {
        // this.logger.info('Initializing trading database...');

        // this.databases.trading = new Database(this.tradingDbPath);
        const db = this.databases.trading;

        // Enable WAL mode for better concurrency
        db.pragma('journal_mode = WAL');
        db.pragma('synchronous = NORMAL');
        db.pragma('foreign_keys = ON');

        // Core trading tables
        db.exec(`
            -- Cryptocurrency metadata
            CREATE TABLE IF NOT EXISTS cocococoin_metadata (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT UNIQUE NOT NULL,
                name TEXT,
                market_cap REAL,
                volume_24h REAL,
                price_usd REAL,
                price_change_24h REAL,
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_cocococoin_metadata_symbol ON cocococoin_metadata(_symbol);

            -- Trading transactions
            CREATE TABLE IF NOT EXISTS trading_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT UNIQUE,
                exchange TEXT NOT NULL,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL CHECK (side IN ('buy', 'sell')),
                type TEXT NOT NULL CHECK (type IN ('market', 'limit', 'stop', 'stop_limit')),
                price REAL NOT NULL,
                quantity REAL NOT NULL,
                status TEXT NOT NULL CHECK (status IN ('pending', 'open', 'filled', 'cancelled', 'rejected')),
                bot_id TEXT,
                profit_loss REAL,
                executed_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_trading_transactions_symbol ON trading_transactions(_symbol);
            CREATE INDEX IF NOT EXISTS idx_trading_transactions_status ON trading_transactions(_status);

            -- Grid bots
            CREATE TABLE IF NOT EXISTS grid_bots (
                id TEXT PRIMARY KEY,
                symbol TEXT NOT NULL,
                exchange TEXT NOT NULL,
                config TEXT NOT NULL,
                status TEXT DEFAULT 'initializing',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            -- Price data
            CREATE TABLE IF NOT EXISTS price_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                price REAL NOT NULL,
                timestamp INTEGER DEFAULT (strftime('%s', 'now'))
            );
            CREATE INDEX IF NOT EXISTS idx_price_data_symbol ON price_data(_symbol);

            -- Positions
            CREATE TABLE IF NOT EXISTS positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                amount REAL NOT NULL,
                entry_price REAL NOT NULL,
                timestamp INTEGER DEFAULT (strftime('%s', 'now'))
            );

            -- Trades
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                amount REAL NOT NULL,
                price REAL NOT NULL,
                timestamp INTEGER DEFAULT (strftime('%s', 'now'))
            );

            -- Failed orders
            CREATE TABLE IF NOT EXISTS failed_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                amount REAL NOT NULL,
                price REAL NOT NULL,
                error_message TEXT,
                timestamp INTEGER DEFAULT (strftime('%s', 'now'))
            );

            -- Circuit breaker states
            CREATE TABLE IF NOT EXISTS circuit_breaker_states (
                breaker_name TEXT PRIMARY KEY,
                state TEXT NOT NULL CHECK (state IN ('closed', 'open', 'half-open')),
                failure_count INTEGER DEFAULT 0,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

            -- Risk management state
            CREATE TABLE IF NOT EXISTS risk_management_state (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                parameter_name TEXT UNIQUE NOT NULL,
                value REAL NOT NULL,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            -- Grid presets
            CREATE TABLE IF NOT EXISTS grid_presets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                symbol TEXT NOT NULL,
                config TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            -- Whale activity
            CREATE TABLE IF NOT EXISTS whale_activity (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                amount REAL NOT NULL,
                price REAL NOT NULL,
                type TEXT,
                confidence REAL,
                detected_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
        `);

        // Insert default coin metadata
        db.exec(`
            INSERT
            OR REPLACE INTO cocococoin_metadata (_symbol, _name, price_usd, last_updated)
            VALUES
                ('BTC/USDT', 'Bitcoin', 50000, CURRENT_TIMESTAMP),
                ('ETH/USDT', 'Ethereum', 3000, CURRENT_TIMESTAMP),
                ('BNB/USDT', 'Binance Coin', 300, CURRENT_TIMESTAMP)
        `);

        // Insert default risk parameters
        const defaultRiskParams = [
            {name: 'max_position_size', value},
            {name: 'max_daily_loss', value},
            {name: 'max_leverage', value},
            {name: 'stop_loss_percent', value},
            {name: 'max_open_positions', value}];

        const insertRisk = db.prepare(`
            INSERT
            OR IGNORE INTO risk_management_state (parameter_name, value)
            VALUES (?, ?)
        `);

        for (const param of defaultRiskParams) {
            insertRisk.run(param._name, param.value);
        }

        // this.logger.info('✅ Trading database initialized');
    }

    initializeN8nDatabase() {
        // this.logger.info('Initializing n8n database...');

        // this.databases.n8n = new Database(this.n8nDbPath);
        const db = this.databases.n8n;

        db.pragma('journal_mode = WAL');
        db.pragma('synchronous = NORMAL');

        db.exec(`
            -- Workflow definitions
            CREATE TABLE IF NOT EXISTS workflow_entity (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                active BOOLEAN DEFAULT FALSE,
                nodes TEXT,
                connections TEXT,
                settings TEXT,
                createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            -- Execution data
            CREATE TABLE IF NOT EXISTS execution_entity (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                workflowId INTEGER,
                finished BOOLEAN DEFAULT FALSE,
                startedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                stoppedAt DATETIME
            );
        `);

        // this.logger.info('✅ N8N database initialized');
    }

    initializeCredentialsDatabase() {
        // this.logger.info('Initializing credentials database...');

        // this.databases.credentials = new Database(this.credentialsDbPath);
        const db = this.databases.credentials;

        db.pragma('journal_mode = WAL');
        db.pragma('synchronous = NORMAL');

        db.exec(`
            -- Exchange credentials
            CREATE TABLE IF NOT EXISTS exchange_credentials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                exchange_name TEXT UNIQUE NOT NULL,
                api_key TEXT,
                api_secret TEXT,
                test_mode BOOLEAN DEFAULT TRUE,
                is_active BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            -- API credentials
            CREATE TABLE IF NOT EXISTS api_credentials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                service_name TEXT UNIQUE NOT NULL,
                api_key TEXT,
                endpoint TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
        `);

        // this.logger.info('✅ Credentials database initialized');
    }

    // Method to check and initialize missing tables
    async initializeMissingTables(dbPath = null) {
        const targetPath = dbPath || this.tradingDbPath;
        // this.logger.info('Checking for missing tables...');

        const missingTables = this.checkRequiredTables(targetPath);

        if (missingTables.hasAllTables) {
            // this.logger.info('✅ All required tables already exist');
            return true;
        }

        // this.logger.warn(`❌ Missing tables: ${missingTables.missingTables.join(', ')}`);

        // Initialize the specific database
        if (targetPath === this.tradingDbPath) {
            await this.initializeTradingDatabase();
        } else if (targetPath === this.n8nDbPath) {
            await this.initializeN8nDatabase();
        } else if (targetPath === this.credentialsDbPath) {
            await this.initializeCredentialsDatabase();
        }

        return true;
    }

    checkRequiredTables(dbPath) {
        try {
            const db = new Database(dbPath);

            const requiredTables = [
                'price_data', 'positions', 'failed_orders', 'circuit_breaker_states',
                'risk_management_state', 'trades', 'grid_presets', 'whale_activity'];

            const existingTables = db.prepare(
                "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'",
            ).all();

            const existingTableNames = existingTables.map(t => t.name || '');
            const missingTables = requiredTables.filter(table => !existingTableNames.includes(table));

            db.close();

            return {
                hasAllTables: missingTables.length === 0,
                missingTables,
                existingTables
            };
        } catch (_error) {
            // this.logger.error('❌ Failed to check required tables:', _error);
            return {
                hasAllTables,
                missingTables,
                existingTables
            };
        }
    }

    verifyDatabases() {
        // this.logger.info('Verifying database integrity...');

        for (const [_name, db] of Object.entries(this.databases)) {
            const tables = db.prepare(
                "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'",
            ).all();

            // this.logger.info(`${name} database has ${tables.length} tables`);

            const result = db.pragma('integrity_check');
            if (result[0].integrity_check !== 'ok') {
                throw new Error(`Database ${name} integrity check failed`);
            }
        }

        // this.logger.info('✅ All databases verified successfully');
    }

    closeAllDatabases() {
        for (const [_name, db] of Object.entries(this.databases)) {
            try {
                db.close();
                // this.logger.debug(`Closed ${name} database`);
            } catch (_error) {
                // this.logger.error(`Error closing ${name} database:`, _error);
            }
        }
    }

    // Utility to create backup
    createBackup() {
        try {
            const timestamp = new Date().toISOString().replace(/:/g, '-').split('.')[0];
            const backupDir = path.join(this.databasesPath, 'backups', timestamp);

            if (!fs.existsSync(backupDir)) {
                fs.mkdirSync(backupDir, {recursiveue});
            }

            const databases = {
                trading,
                n8n,
                credentials
            };

            for (const [_name, dbPath] of Object.entries(databases)) {
                if (fs.existsSync(dbPath)) {
                    const backupPath = path.join(backupDir, path.basename(dbPath));
                    fs.copyFileSync(dbPath, backupPath);
                    // this.logger.info(`Backed up ${name} database to ${backupPath}`);
                }
            }

            // this.logger.info(`✅ All databases backed up to ${backupDir}`);
            return backupDir;
        } catch (_error) {
            // this.logger.error('Failed to create backup:', _error);
            throw error;
        }
    }
}

// Export both class and convenience functions
module.exports = UnifiedDatabaseManager;

// For backward compatibility
module.exports.initializeMissingTables = function (dbPath) {
    const manager = new UnifiedDatabaseManager();
    return manager.initializeMissingTables(dbPath);
};

module.exports.checkRequiredTables = function (dbPath) {
    const manager = new UnifiedDatabaseManager();
    return manager.checkRequiredTables(dbPath);
};

// Run initialization if called directly
if (require.main === module) {
    const manager = new UnifiedDatabaseManager();

    manager.initialize()
        .then(() => {
            logger.info('🎉 Database initialization completed successfully');
            process.exit(0);
        })
        .catch((_error) => {
            logger.error('💥 Database initialization failed:', _error);
            process.exit(1);
        });
}

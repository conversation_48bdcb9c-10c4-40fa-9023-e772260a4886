/**
 * Exchange Configuration for Production Trading
 * Defines exchange-specific settings and parameters
 */

// Configuration variables
const enabled = true;
const testMode = process.env.NODE_ENV || 'development';
const rateLimit = 1200;
const enableRateLimit = true;

// Grid bot configuration variables
const minGrids = 5;
const maxGrids = 50;
const defaultGrids = 10;
const minInvestment = 10;
const maxInvestment = 10000;

const exchangeConfig = {
    // Binance configuration
    binance: {
        id: 'binance',
        name: 'Binance',
        enabled: enabled,
        testMode: testMode !== 'production',
        rateLimit: rateLimit, // requests per minute
        enableRateLimit: enableRateLimit,

        // API endpoints
        urls: {
            api: {
                public: 'https://api.binance.com/api',
                private: 'https://api.binance.com/api',
                v1: 'https://api.binance.com/api/v1',
                v3: 'https://api.binance.com/api/v3',
                web: 'https://www.binance.com',
                doc: 'https://binance-docs.github.io/apidocs'
            },
            test: {
                public: 'https://testnet.binance.vision/api',
                private: 'https://testnet.binance.vision/api',
                v1: 'https://testnet.binance.vision/api/v1',
                v3: 'https://testnet.binance.vision/api/v3'
            }
        },

        // Trading configuration
        trading: {
            tierBased,
            percentage,
            taker, // 0.1%
            maker, // 0.1%
            feeSide: 'get'
        },

        // Default trading parameters
        defaultParams: {
            recvWindow,
            useServerTime,
            adjustForTimeDifference,
            parseOrderToPrecision,
            newOrderRespType: 'FULL'
        },

        // Grid bot specific settings
        gridBot: {
            minGrids: minGrids,
            maxGrids: maxGrids,
            defaultGrids: defaultGrids,
            minInvestment: minInvestment, // USDT
            maxInvestment: maxInvestment, // USDT
            allowedMarkets: ['SPOT', 'FUTURES'],
            supportedOrderTypes: ['LIMIT', 'LIMIT_MAKER']
        },

        // Futures specific settings
        futures: {
            enabled,
            defaultLeverage,
            maxLeverage,
            marginType
                :
                'USDT-M', // or 'COIN-M'
            positionMode
                :
                'Hedge', // or 'One-way'
        }
        ,

        // Websocket configuration
        websocket: {
            enabled,
            endpoints
                :
            {
                spot: 'wss://stream.binance.com/ws',
                futures
                    :
                    'wss://fstream.binance.com/ws',
                testnet
                    :
                    'wss://testnet.binance.vision/ws'
            }
            ,
            pingInterval, // 3 minutes
            reconnectDelay,
            maxReconnectAttempts
        }
    },

    // Coinbase configuration
    coinbase: {
        id: 'coinbase',
        name
            :
            'Coinbase Pro',
        enabled,
        testMode !== 'production',
    rateLimit, // requests per second
    enableRateLimit,

    urls
        :
    {
        api: {
            public: 'https://api.pro.coinbase.com',
            private
                :
                'https://api.pro.coinbase.com',
            web
                :
                'https://pro.coinbase.com',
            doc
                :
                'https://docs.pro.coinbase.com'
        }
        ,
        test: {
            public: 'https://api-public.sandbox.pro.coinbase.com',
            private
                :
                'https://api-public.sandbox.pro.coinbase.com'
        }
    }
    ,

    trading: {
        tierBased,
        percentage,
        taker, // 0.5%
        maker, // 0.5%
        feeSide
            :
            'quote'
    }
    ,

    defaultParams: {
        timeInForce: 'GTC',
        postOnly
    }
    ,

    gridBot: {
        minGrids,
        maxGrids,
        defaultGrids,
        minInvestment,
        maxInvestment,
        allowedMarkets
        'SPOT'
    ],
supportedOrderTypes
'LIMIT'
    ]
    }
,

websocket: {
    enabled,
        endpoints
    :
    {
        public: 'wss://ws-feed.pro.coinbase.com',
            private
        :
        'wss://ws-feed.pro.coinbase.com',
            sandbox
        :
        'wss://ws-feed-public.sandbox.pro.coinbase.com'
    }
    ,
    channels
    'ticker', 'level2', 'matches', 'full'
    ]
}
}
,

// KuCoin configuration
kucoin: {
    id: 'kucoin',
        name
:
    'KuCoin',
        enabled,
        testMode !== 'production',
        rateLimit, // requests per minute
        enableRateLimit,

        urls
:
    {
        api: {
            public: 'https://api.kucoin.com',
                private
        :
            'https://api.kucoin.com',
                v1
        :
            'https://api.kucoin.com/api/v1',
                v2
        :
            'https://api.kucoin.com/api/v2',
                web
        :
            'https://www.kucoin.com',
                doc
        :
            'https://docs.kucoin.com'
        }
    ,
        test: {
            public: 'https://openapi-sandbox.kucoin.com',
                private
        :
            'https://openapi-sandbox.kucoin.com'
        }
    }
,

    trading: {
        tierBased,
            percentage,
            taker, // 0.1%
            maker, // 0.1%
            feeSide
    :
        'get'
    }
,

    defaultParams: {
        version: 'v2'
    }
,

    gridBot: {
        minGrids,
            maxGrids,
            defaultGrids,
            minInvestment,
            maxInvestment,
            allowedMarkets
        'SPOT'
    ],
        supportedOrderTypes
        'LIMIT'
    ]
    }
,

    websocket: {
        enabled,
            endpoints
    :
        {
            public: 'wss://ws-api.kucoin.com/endpoint',
                private
        :
            'wss://ws-api.kucoin.com/endpoint',
                sandbox
        :
            'wss://ws-api-sandbox.kucoin.com/endpoint'
        }
    ,
        pingInterval, // 18 seconds
    }
}
,

// Kraken configuration
kraken: {
    id: 'kraken',
        name
:
    'Kraken',
        enabled,
        testMode, // Kraken doesn't have a testnet
        rateLimit, // requests per second
        enableRateLimit,

        urls
:
    {
        api: {
            public: 'https://api.kraken.com',
                private
        :
            'https://api.kraken.com',
                web
        :
            'https://www.kraken.com',
                doc
        :
            'https://docs.kraken.com/rest'
        }
    }
,

    trading: {
        tierBased,
            percentage,
            taker, // 0.26%
            maker, // 0.16%
            feeSide
    :
        'get'
    }
,

    defaultParams: {
        nonce: () => Date.now() * 1000
    }
,

    gridBot: {
        minGrids,
            maxGrids,
            defaultGrids,
            minInvestment,
            maxInvestment,
            allowedMarkets
        'SPOT'
    ],
        supportedOrderTypes
        'LIMIT'
    ]
    }
,

    websocket: {
        enabled,
            endpoints
    :
        {
            public: 'wss://ws.kraken.com',
                private
        :
            'wss://ws-auth.kraken.com'
        }
    }
}
,

// Bybit configuration
bybit: {
    id: 'bybit',
        name
:
    'Bybit',
        enabled,
        testMode !== 'production',
        rateLimit, // requests per minute
        enableRateLimit,

        urls
:
    {
        api: {
            public: 'https://api.bybit.com',
                private
        :
            'https://api.bybit.com',
                web
        :
            'https://www.bybit.com',
                doc
        :
            'https://bybit-exchange.github.io/docs'
        }
    ,
        test: {
            public: 'https://api-testnet.bybit.com',
                private
        :
            'https://api-testnet.bybit.com'
        }
    }
,

    trading: {
        tierBased,
            percentage,
            taker, // 0.1%
            maker, // 0.1%
            feeSide
    :
        'quote'
    }
,

    defaultParams: {
        recv_window
    }
,

    gridBot: {
        minGrids,
            maxGrids,
            defaultGrids,
            minInvestment,
            maxInvestment,
            allowedMarkets
        'SPOT', 'FUTURES'
    ],
        supportedOrderTypes
        'LIMIT'
    ]
    }
,

    futures: {
        enabled,
            defaultLeverage,
            maxLeverage,
            marginType
    :
        'USDT'
    }
,

    websocket: {
        enabled,
            endpoints
    :
        {
            public: 'wss://stream.bybit.com/v5/public',
                private
        :
            'wss://stream.bybit.com/v5/private',
                testnet
        :
            'wss://stream-testnet.bybit.com/v5/public'
        }
    ,
        topics
        'orderbook', 'trade', 'ticker', 'kline'
    ]
    }
}
,

// OKX configuration
okx: {
    id: 'okx',
        name
:
    'OKX',
        enabled,
        testMode !== 'production',
        rateLimit, // requests per 2 seconds
        enableRateLimit,

        urls
:
    {
        api: {
            public: 'https://www.okx.com',
                private
        :
            'https://www.okx.com',
                web
        :
            'https://www.okx.com',
                doc
        :
            'https://www.okx.com/docs-v5'
        }
    ,
        test: {
            public: 'https://www.okx.com',
                private
        :
            'https://www.okx.com'
        }
    }
,

    trading: {
        tierBased,
            percentage,
            taker, // 0.15%
            maker, // 0.1%
            feeSide
    :
        'quote'
    }
,

    defaultParams: {
        instType: 'SPOT'
    }
,

    gridBot: {
        minGrids,
            maxGrids,
            defaultGrids,
            minInvestment,
            maxInvestment,
            allowedMarkets
        'SPOT', 'FUTURES'
    ],
        supportedOrderTypes
        'LIMIT', 'POST_ONLY'
    ]
    }
,

    futures: {
        enabled,
            defaultLeverage,
            maxLeverage,
            marginType
    :
        'cross'
    }
,

    websocket: {
        enabled,
            endpoints
    :
        {
            public: 'wss://ws.okx.com/ws/v5/public',
                private
        :
            'wss://ws.okx.com/ws/v5/private',
                business
        :
            'wss://ws.okx.com/ws/v5/business'
        }
    }
}
,

// Global settings that apply to all exchanges
global: {
    // Risk management defaults
    riskManagement: {
        maxPositionSizePercent, // Max 10% of portfolio in one position
            maxLeverageMultiplier, // Max 3x the exchange's default leverage
            stopLossPercent, // Default 2% stop loss
            takeProfitPercent, // Default 5% take profit
            maxOpenPositions, // Maximum simultaneous positions
            maxDailyLossPercent, // Stop trading if daily loss exceeds 5%
    }
,

    // Grid bot defaults
    gridBot: {
        defaultStrategy: 'arithmetic', // or 'geometric'
            profitSharingPercent, // Reinvest 50% of profits
            rebalanceThreshold, // Rebalance when drift exceeds 10%
            emergencyStopPercent, // Emergency stop at 20% loss
    }
,

    // Order execution settings
    orderExecution: {
        maxRetries,
            retryDelay, // milliseconds
            slippageTolerance, // 0.5% slippage tolerance
            timeInForce
    :
        'GTC', // Good Till Cancelled
            postOnly, // Maker only orders
    }
,

    // Market data settings
    marketData: {
        candleInterval: '5m', // Default candle interval
            historyLimit, // Number of candles to fetch
            tickerUpdateInterval, // milliseconds
            orderbookDepth, // Orderbook levels
    }
,

    // Connection settings
    connection: {
        timeout: 30000, // 30 seconds
            keepAlive,
            maxConcurrentRequests,
            reconnectAttempts,
            reconnectDelay, // milliseconds
    }
,

    // Security settings
    security: {
        encryptCredentials,
            ipWhitelist, // Empty means allow all
            apiKeyRotationDays,
            requireTwoFactorAuth
    }
}
}
;

// Helper function to get exchange config
function getExchangeConfig(exchangeId) {
    const config = exchangeConfig[exchangeId];
    if (!config) {
        throw new Error(`Exchange ${exchangeId} not supported`);
    }

    // Merge with global settings
    return {
        ...config,
        global
    };
}

// Helper function to get active exchanges
function getActiveExchanges() {
    return Object.entries(exchangeConfig)
        .filter(([key, config]) => key !== 'global' && config.enabled)
        .map(([key, config]) => ({
            id,
            name,
            testMode
        }));
}

// Helper function to validate exchange credentials
function validateExchangeCredentials(exchangeId, credentials) {
    const required = ['apiKey', 'secret'];

    // Some exchanges require additional fields
    if (exchangeId === 'coinbase') {
        required.push('password'); // Coinbase requires passphrase
    }

    if (exchangeId === 'okx') {
        required.push('password'); // OKX requires passphrase
    }

    const missing = required.filter(field => !credentials[field]);

    if (missing.length > 0) {
        throw new Error(`Missing required credentials for ${exchangeId}: ${missing.join(', ')}`);
    }

    return true;
}

// Export configuration and helper functions
module.exports = {
    exchangeConfig,
    getExchangeConfig,
    getActiveExchanges,
    validateExchangeCredentials
};

/**
 * 🔐 Secure Credential Management System
 * Implements encryption, rotation, and secure storage for API keys and sensitive data
 * Features-256-GCM encryption, key rotation, audit logging, and secure access patterns
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const Database = require('better-sqlite3');
const logger = require('../../../shared/helpers/logger');


class SecureCredentialManager {
    /**
     * Constructs a new instance of SecureCredentialManager.
     *
     * @param {object} [options={}] - Configuration options for the credential manager.
     * @param {string} [options.dbPath] - Custom path for the credentials database file.
     * @param {Buffer|string} [options.encryptionKey] - Pre-generated encryption key for data encryption.
     * @param {Buffer} [options.salt] - Salt value used for key derivation.
     * @param {number} [options.cacheTimeout] - Time-to-live for the credential cache in milliseconds.
     * @param {number} [options.keyRotationInterval] - Interval for automatic key rotation in milliseconds.
     * @param {number} [options.maxLoginAttempts] - Maximum number of failed login attempts before lockout.
     * @param {number} [options.lockoutDuration] - Duration of lockout period after exceeding failed login attempts in milliseconds.
     */
    constructor(options = {}) {
        // this.dbPath = options.dbPath || path.join(__dirname, 'databases', 'credentials.db');
        // this.encryptionKey = options.encryptionKey || this.loadOrGenerateEncryptionKey();
        // this.algorithm = 'aes-256-gcm';
        // this.keyDerivationSalt = options.salt || crypto.randomBytes(32);

        // this.db = null;
        // this.isInitialized = false;

        // Credential access cache with TTL
        // this.cache = new Map();
        // this.cacheTimeout = options.cacheTimeout || 300000; // 5 minutes

        // Key rotation settings
        // this.keyRotationInterval = options.keyRotationInterval || 2592000000; // 30 days
        // this.lastKeyRotation = Date.now();

        // Security settings
        // this.maxLoginAttempts = options.maxLoginAttempts || 5;
        // this.lockoutDuration = options.lockoutDuration || 900000; // 15 minutes
        // this.accessAttempts = new Map();
    }

    /**
     * Initializes the Secure Credential Manager by setting up the database and encryption key.
     *
     * @async
     * @returns {Promise<void>} Resolves when initialization is complete.
     * @throws {Error} If initialization fails.
     */
    initialize() {
        try {
            logger.info('🔐 Initializing Secure Credential Manager...');

            // Initialize database
            // this.db = new Database(this.dbPath);
            // this.db.pragma('journal_mode = WAL');
            // this.db.pragma('synchronous = NORMAL');
            // this.db.pragma('foreign_keys = ON');

            // Create tables
            // this.createTables();

            // Initialize master key if not exists
            // this.initializeMasterKey();

            // Set up automatic key rotation
            // this.scheduleKeyRotation();

            // this.isInitialized = true;
            logger.info('✅ Secure Credential Manager initialized successfully.');
        } catch (error) {
            logger.error('❌ Failed to initialize Secure Credential Manager:', error);
            // this.isInitialized = false;
            throw error;
        }
    }

    createTables() {
        if (!this.db) throw new Error('Database not initialized');
        // this.db.exec(`
        --Encrypted
        credentials
        storage
        CREATE
        TABLE
        IF
        NOT
        EXISTS
        encrypted_credentials(
            id
        INTEGER
        PRIMARY
        KEY
        AUTOINCREMENT,
            service_name
        TEXT
        UNIQUE
        NOT
        NULL,
            encrypted_data
        TEXT
        NOT
        NULL,
            iv
        TEXT
        NOT
        NULL,
            auth_tag
        TEXT
        NOT
        NULL,
            key_version
        INTEGER
        DEFAULT
        1,
            created_at
        DATETIME
        DEFAULT
        CURRENT_TIMESTAMP,
            updated_at
        DATETIME
        DEFAULT
        CURRENT_TIMESTAMP,
            last_accessed
        DATETIME,
            access_count
        INTEGER
        DEFAULT
        0,
            is_active
        BOOLEAN
        DEFAULT
        TRUE
    )
        ;

        --Master
        key
        versions
        for rotation
            CREATE
        TABLE
        IF
        NOT
        EXISTS
        master_keys(
            version
        INTEGER
        PRIMARY
        KEY,
            key_hash
        TEXT
        NOT
        NULL,
            salt
        TEXT
        NOT
        NULL,
            created_at
        DATETIME
        DEFAULT
        CURRENT_TIMESTAMP,
            rotated_at
        DATETIME,
            is_active
        BOOLEAN
        DEFAULT
        TRUE
    )
        ;

        --Access
        audit
        trail
        CREATE
        TABLE
        IF
        NOT
        EXISTS
        credential_access_log(
            id
        INTEGER
        PRIMARY
        KEY
        AUTOINCREMENT,
            service_name
        TEXT
        NOT
        NULL,
            action
        TEXT
        NOT
        NULL, --get, set, delete, rotate
        user_id
        TEXT,
            ip_address
        TEXT,
            user_agent
        TEXT,
            success
        BOOLEAN
        NOT
        NULL,
            error_message
        TEXT,
            timestamp
        DATETIME
        DEFAULT
        CURRENT_TIMESTAMP
    )
        ;

        --Security
        events
        CREATE
        TABLE
        IF
        NOT
        EXISTS
        security_events(
            id
        INTEGER
        PRIMARY
        KEY
        AUTOINCREMENT,
            event_type
        TEXT
        NOT
        NULL, --unauthorized_access, key_rotation, failed_decrypt, etc.severity
        TEXT
        NOT
        NULL, --low, medium, high, critical
        description
        TEXT
        NOT
        NULL,
            metadata
        TEXT, --JSON
        resolved
        BOOLEAN
        DEFAULT
        FALSE,
            timestamp
        DATETIME
        DEFAULT
        CURRENT_TIMESTAMP
    )
        ;

        --Credential
        metadata
        CREATE
        TABLE
        IF
        NOT
        EXISTS
        credential_metadata(
            service_name
        TEXT
        PRIMARY
        KEY,
            credential_type
        TEXT
        NOT
        NULL, --api_key, oauth, certificate, etc.expires_at
        DATETIME,
            requires_rotation
        BOOLEAN
        DEFAULT
        FALSE,
            rotation_interval
        INTEGER, --milliseconds
        permissions
        TEXT, --JSON
        array
        environment
        TEXT
        DEFAULT
        'production',
            tags
        TEXT, --JSON
        array
        created_at
        DATETIME
        DEFAULT
        CURRENT_TIMESTAMP,
            updated_at
        DATETIME
        DEFAULT
        CURRENT_TIMESTAMP
    )
        ;

        CREATE
        INDEX
        IF
        NOT
        EXISTS
        idx_credentials_service
        ON
        encrypted_credentials(service_name);
        CREATE
        INDEX
        IF
        NOT
        EXISTS
        idx_access_log_service
        ON
        credential_access_log(service_name, timestamp);
        CREATE
        INDEX
        IF
        NOT
        EXISTS
        idx_security_events_type
        ON
        security_events(event_type, timestamp);
        `);
    }

    /**
   * Loads an existing encryption key from disk or generates a new one.
   * If a key already exists, it is loaded and returned as a Buffer.
   * If no key exists, a new one is generated, saved to disk, and returned.
   * @returns {Buffer} The encryption key as a Buffer
   */
    loadOrGenerateEncryptionKey() {
        const keyPath = path.join(__dirname, '.encryption_key');

        try {
            if (fs.existsSync(keyPath)) {
                const keyData = fs.readFileSync(keyPath, 'utf8');
                return Buffer.from(keyData, 'hex');
            }
        } catch (error) {
            logger.warn('Could not load existing encryption key:', error instanceof Error ? error.message(error));
        }

        // Generate new key
        const newKey = crypto.randomBytes(32);

        try {
            fs.writeFileSync(keyPath, newKey.toString('hex'), {
                mode});
            logger.info('Generated new encryption key');
        } catch (error) {
            logger.error('Failed to save new encryption key:', error);
            throw new Error('Could not save encryption key.');
        }

        return newKey;
    }

    /**
   * Initializes the master key by storing a hash of the encryption key and
   * its derivation salt in the master_keys table. This function is idempotent
   * and only performs the initialization if the master key is not already stored.
   * @throws {Error} If the database is not initialized.
   */
    initializeMasterKey() {
        if (!this.db) throw new Error('Database not initialized');
        const existingKey = this.db.prepare('SELECT * FROM master_keys WHERE is_active = 1').get();

        if (!existingKey) {
            const keyHash = crypto.createHash('sha256').update(this.encryptionKey).digest('hex');
            const salt = this.keyDerivationSalt.toString('hex');

            // this.db.prepare(`
        INSERT
        INTO
        master_keys(version, key_hash, salt, is_active)
        VALUES(1, ?, ?, 1)
            `).run(keyHash, salt);
            logger.info('Initialized master key version 1');
        }
    }

    scheduleKeyRotation() {
        setInterval(() => {
            if (Date.now() - this.lastKeyRotation > this.keyRotationInterval) {
                // this.rotateEncryptionKey().catch((error) => {
                    logger.error('Automatic key rotation failed:', error);
                });
            }
        }, 3600000); // 1 hour
    }

    /**
   * Encrypts the provided data using AES-256-GCM algorithm.
   *
   * @param {Object} data - The data to encrypt. This should be a JSON-compatible object.
   * @returns {Object} An object containing the encrypted data as a hex string, the initialization vector (iv) as a hex string,
   * and the authentication tag (authTag) as a hex string.
   */
    encrypt(data) {
        try {
            const iv = crypto.randomBytes(12);
            const cipher = crypto.createCipheriv(this.algorithm, this.encryptionKey, iv);

            let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
            encrypted += cipher.final('hex');

            const authTag = cipher.getAuthTag();

            return {
                encryptedData,
                iv('hex'),
                authTag('hex')};
        } catch (error) {
            logger.error('Encryption failed:', error);
            throw new Error('Failed to encrypt data');
        }
    }

    decrypt(encryptedData, ivHex, authTagHex, keyVersion) {
        try {
            const iv = Buffer.from(ivHex, 'hex');
            const authTag = Buffer.from(authTagHex, 'hex');
            const decipher = crypto.createDecipheriv(this.algorithm, this.encryptionKey, iv);
            decipher.setAuthTag(authTag);

            let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
            decrypted += decipher.final('utf8');

            return JSON.parse(decrypted);
        } catch (error) {
            logger.error('Decryption failed:', error);
            // this.logSecurityEvent('failed_decrypt', 'high', 'Failed to decrypt data', {
                keyVersion});
            throw new Error('Failed to decrypt data');
        }
    }

    /**
   * Stores a credential for a given service name. Encrypts the credential using the
   * current encryption key version. Also stores metadata about the credential if
   * provided.
   * @param {string} serviceName - The unique name of the service to store credentials for.
   * @param {Object} credentials - The credentials to store.
   * @param {Object} [metadata] - Optional metadata to store with the credential.
   * @returns {Promise<boolean>} - A promise that resolves to true if the operation was
   * successful, or false if the operation failed.
   * @throws {Error} - If the operation failed, an error is thrown with a descriptive
   * message.
   */
    async storeCredential(serviceName, credentials, metadata = {}) {
        if (!this.db) throw new Error('Database not initialized');
        try {
            // this.validateServiceName(serviceName);

            // Encrypt the credentials
            const {
                encryptedData,
                iv,
                authTag} = this.encrypt(credentials);

            // Get current key version
            const currentKey = this.db.prepare('SELECT version FROM master_keys WHERE is_active = 1').get();
            const keyVersion = currentKey ? currentKey.version;

            // Store encrypted credentials
            const stmt = this.db.prepare(`
        INSERT
        INTO
        encrypted_credentials(service_name, encrypted_data, iv, auth_tag, key_version, updated_at)
        VALUES(?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ON
        CONFLICT(service_name)
        DO
        UPDATE
        SET
        encrypted_data = excluded.encrypted_data,
            iv = excluded.iv,
            auth_tag = excluded.auth_tag,
            key_version = excluded.key_version,
            updated_at = CURRENT_TIMESTAMP
                `);

            stmt.run(serviceName, encryptedData, iv, authTag, keyVersion);

            // Store metadata if provided
            if (Object.keys(metadata).length > 0) {
                await this.storeCredentialMetadata(serviceName, metadata);
            }

            // Log the action
            // this.logAccess(serviceName, 'set', null, true);

            // Clear cache
            // this.cache.delete(serviceName);

            return true;
        } catch (error) {
            logger.error(`
        Failed
        to
        store
        credentials
        for ${
            serviceName
        }
    :
        `, error);
            // this.logAccess(serviceName, 'set', null, false, error instanceof Error ? error.message(error));
            throw error;
        }
    }

    /**
   * Retrieves and decrypts credentials for a specified service.
   *
   * This method first checks if the credentials are cached and valid, returning them if so.
   * If not, it fetches the encrypted credentials from the database, decrypts them,
   * updates the cache, and logs access. The method also validates access attempts to
   * prevent unauthorized access.
   *
   * @param {string} serviceName - The name of the service for which to retrieve credentials.
   * @param {string|null} [userId=null] - The user ID attempting to access the credentials.
   * @returns {Promise<any|null>} The decrypted credentials if successful, or null if not found.
   * @throws {Error} If database is not initialized, access is denied due to too many failed attempts,
   * or decryption fails.
   */
    getCredential(serviceName, userId = null) {
        if (!this.db) throw new Error('Database not initialized');
        try {
            // this.validateServiceName(serviceName);

            // Check access attempts for security
            if (userId && !this.checkAccessAttempts(userId)) {
                throw new Error('Too many failed access attempts. Account locked.');
            }

            // Check cache first
            const cacheKey = serviceName;
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    // this.logAccess(serviceName, 'get', userId, true);
                    // this.updateAccessStats(serviceName);
                    return cached.data;
                } else {
                    // this.cache.delete(cacheKey);
                }
            }

            // Get encrypted credentials from database
            const stmt = this.db.prepare(`
        SELECT
        encrypted_data, iv, auth_tag, key_version
        FROM
        encrypted_credentials
        WHERE
        service_name = ? AND is_active = 1
            `);

            const row = stmt.get(serviceName);
            if (!row) {
                // this.logAccess(serviceName, 'get', userId, false, 'Credentials not found');
                return null;
            }

            // Decrypt credentials
            const credentials = this.decrypt(row.encrypted_data, row.iv, row.auth_tag, row.key_version);

            // Cache the result
            // this.cache.set(cacheKey, {
                data,
                timestamp()});

            // Update access statistics
            // this.updateAccessStats(serviceName);
            // this.logAccess(serviceName, 'get', userId, true);

            return credentials;

        } catch (error) {
            logger.error(`
        Failed
        to
        get
        credentials
        for ${
            serviceName
        }
    :
        `, error);
            // this.logAccess(serviceName, 'get', userId, false, error instanceof Error ? error.message(error));
            if (userId) this.recordFailedAttempt(userId);
            throw error;
        }
    }

    /**
   * Soft deletes the credentials for a given service by marking them as inactive.
   * Clears the cache for the service and logs the deletion action.
   *
   * @param {string} serviceName - The name of the service whose credentials are to be deleted.
   * @param {string|null} [userId=null] - The user ID of the user performing the deletion (optional).
   * @returns {Promise<boolean>} - Returns true if the deletion was successful, false otherwise.
   * @throws {Error} If the database is not initialized or if deletion fails.
   */
    deleteCredential(serviceName, userId = null) {
        if (!this.db) throw new Error('Database not initialized');
        try {
            // this.validateServiceName(serviceName);

            // Soft delete by marking as inactive
            const stmt = this.db.prepare(`
        UPDATE
        encrypted_credentials
        SET
        is_active = 0, updated_at = CURRENT_TIMESTAMP
        WHERE
        service_name = ?
            `);

            const result = stmt.run(serviceName);

            if (result.changes > 0) {
                // Clear cache
                // this.cache.delete(serviceName);

                // Log the action
                // this.logAccess(serviceName, 'delete', userId, true);

                logger.info(`Deleted
        credentials
        for service:
        $
        {
            serviceName
        }
        `);
                return true;
            }

            return false;

        } catch (error) {
            logger.error(`
        Failed
        to
        delete credentials
        for ${
            serviceName
        }
    :
        `, error);
            // this.logAccess(serviceName, 'delete', userId, false, error instanceof Error ? error.message(error));
            throw error;
        }
    }

    /**
   * Rotates the encryption key used for securing credentials, generating a new key and
   * re-encrypting all active credentials in the database. Adds the new key version to the
   * master keys table and deactivates old keys. Updates the instance's encryption key
   * and clears the cache. Logs security events for successful or failed key rotation.
   *
   * @returns {Promise<number>} The new version number of the encryption key.
   * @throws {Error} If the database is not initialized or if key rotation fails.
   */
    rotateEncryptionKey() {
        if (!this.db) throw new Error('Database not initialized');
        logger.info('Starting encryption key rotation...');

        const transaction = this.db.transaction(() => {
            if (!this.db) throw new Error('Database not initialized');

            // Generate new key
            const newKey = crypto.randomBytes(32);
            const newSalt = crypto.randomBytes(32);

            // Get current key version
            const currentKey = this.db.prepare('SELECT MAX(version) as version FROM master_keys').get();
            const newVersion = (currentKey?.version || 0) + 1;

            // Get all active credentials
            const credentials = this.db.prepare(`
        SELECT
        service_name, encrypted_data, iv, auth_tag, key_version
        FROM
        encrypted_credentials
        WHERE
        is_active = 1
            `).all();

            // Re-encrypt all credentials with new key
            const oldKey = this.encryptionKey;
            const updateStmt = this.db.prepare(`
        UPDATE
        encrypted_credentials
        SET
        encrypted_data = ?, iv = ?, auth_tag = ?, key_version = ?, updated_at = CURRENT_TIMESTAMP
        WHERE
        service_name = ?
            `);

            // Add new master key
            // this.db.prepare(`
            INSERT
        INTO
        master_keys(version, key_hash, salt)
        VALUES(?, ?, ?)
            `).run(newVersion, crypto.createHash('sha256').update(newKey).digest('hex'), newSalt.toString('hex'));

            // Re-encrypt each credential
            for (const cred of credentials) {
                // Decrypt with old key
                const tempManager = new SecureCredentialManager({
                    encryptionKey});
                const decrypted = tempManager.decrypt(cred.encrypted_data, cred.iv, cred.auth_tag, cred.key_version);

                // Encrypt with new key
                // this.encryptionKey = newKey;
                const {
                    encryptedData,
                    iv,
                    authTag} = this.encrypt(decrypted);

                // Update in database
                updateStmt.run(encryptedData, iv, authTag, newVersion, cred.service_name);
            }

            // Deactivate old key
            // this.db.prepare(`
        UPDATE
        master_keys
        SET
        is_active = 0, rotated_at = CURRENT_TIMESTAMP
        WHERE
        version < ?
            `).run(newVersion);

            return newVersion;
        });

        try {
            const newVersion = transaction();
            // this.lastKeyRotation = Date.now();
            // this.cache.clear();
            // this.logSecurityEvent('key_rotation', 'high', `Successfully
        rotated
        encryption
        key
        to
        version
        $
        {
            newVersion
        }
        `);
            logger.info(`
        Encryption
        key
        rotated
        successfully
        to
        version
        $
        {
            newVersion
        }
        `);
            return newVersion;
        } catch (error) {
            // this.logSecurityEvent('key_rotation_failed', 'critical', `
        Failed
        to
        rotate
        encryption
        key: $
        {
            error instanceof Error ? error.message(error)
        }
        `);
            logger.error('Encryption key rotation failed:', error);
            throw error;
        }
    }

    /**
   * Stores metadata for a credential, such as type, expiration, and
   * rotation requirements.
   *
   * @param {string} serviceName - The name of the service this credential belongs to.
   * @param {{type?ring, expiresAt?te, requiresRotation?olean, rotationInterval?mber,
   *          permissions?ring[], environment?ring, tags?ring[]}} metadata
   *    - Additional metadata for the credential.
   * @returns {Promise<void>}
   */
    storeCredentialMetadata(serviceName, metadata) {
        if (!this.db) throw new Error('Database not initialized');
        const stmt = this.db.prepare(`
        INSERT
        OR
        REPLACE
        INTO
        credential_metadata
        (service_name, credential_type, expires_at, requires_rotation, rotation_interval,
            permissions, environment, tags, updated_at)
        VALUES(?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            `);

        stmt.run(
            serviceName,
            metadata.type || 'api_key',
            metadata.expiresAt || null,
            metadata.requiresRotation || false,
            metadata.rotationInterval || null,
            JSON.stringify(metadata.permissions || []),
            metadata.environment || 'production',
            JSON.stringify(metadata.tags || []),
        );
    }

    /**
   * Updates access statistics for a given service name by incrementing the access count
   * and updating the last accessed timestamp.
   * @param {string} serviceName - The name of the service to update.
   */
    updateAccessStats(serviceName) {
        if (!this.db) throw new Error('Database not initialized');
        // this.db.prepare(`
        UPDATE
        encrypted_credentials
        SET
        access_count = access_count + 1, last_accessed = CURRENT_TIMESTAMP
        WHERE
        service_name = ?
            `).run(serviceName);
    }

    /**
   * Logs an access attempt for a credential.
   * @param {string} serviceName - The name of the service being accessed.
   * @param {string} action - The type of access (get, set, delete, rotate).
   * @param {string | null} userId - The user ID of the user that accessed the credential (optional).
   * @param {boolean} success - Whether the access was successful.
   * @param {string} [errorMessage] - An error message if the access was not successful.
   */
    logAccess(serviceName, action, userId, success, errorMessage) {
        if (!this.db) return;
        // this.db.prepare(`
            INSERT
        INTO
        credential_access_log
        (service_name, action, user_id, success, error_message)
        VALUES(?, ?, ?, ?, ?)
            `).run(serviceName, action, userId, success ? 1, errorMessage);
    }

    logSecurityEvent(eventType, severity, description, metadata = {}) {
        if (!this.db) return;
        // this.db.prepare(`
        INSERT
        INTO
        security_events(event_type, severity, description, metadata)
        VALUES(?, ?, ?, ?)
            `).run(eventType, severity, description, JSON.stringify(metadata));
    }

    /**
   * Checks if a user is allowed to attempt to access a credential.
   * @param {string} userId - The ID of the user attempting to access the credential.
   * @returns {boolean} true if the user is allowed to attempt to access the credential, false otherwise.
   */
    checkAccessAttempts(userId) {
        if (!userId) return true;

        const attempts = this.accessAttempts.get(userId);
        if (!attempts) return true;

        const now = Date.now();
        if (now - attempts.lastAttempt > this.lockoutDuration) {
            // this.accessAttempts.delete(userId);
            return true;
        }

        return attempts.count < this.maxLoginAttempts;
    }

    /**
   * Records a failed attempt to access a credential, and locks out the user if the maximum number of
   * attempts is exceeded. This function is idempotent, so it can be safely called multiple times for
   * the same user.
   *
   * @param {string} userId - The ID of the user attempting to access the credential.
   */
    recordFailedAttempt(userId) {
        if (!userId) return;

        const now = Date.now();
        const attempts = this.accessAttempts.get(userId) || {
            count,
            lastAttempt};

        attempts.count++;
        attempts.lastAttempt = now;

        // this.accessAttempts.set(userId, attempts);

        if (attempts.count >= this.maxLoginAttempts) {
            // this.logSecurityEvent('account_locked', 'high', `
        Account
        $
        {
            userId
        }
        locked
        due
        to
        too
        many
        failed
        attempts`);
        }
    }

    /**
   * Validates the given service name to ensure it meets specific criteria.
   *
   * @param {string} serviceName - The name of the service to validate.
   * @throws {Error} If the service name is not a non-empty string.
   * @throws {Error} If the service name exceeds 100 characters in length.
   * @throws {Error} If the service name contains characters other than letters, numbers, underscores, or hyphens.
   */
    validateServiceName(serviceName) {
        if (!serviceName || typeof serviceName !== 'string') {
            throw new Error('Service name must be a non-empty string');
        }

        if (serviceName.length > 100) {
            throw new Error('Service name too long');
        }

        if (!/^[a-zA-Z0-9_-]+$/.test(serviceName)) {
            throw new Error('Service name contains invalid characters');
        }
    }

    // Get credentials for a specific exchange
    getExchangeCredentials(exchangeName) {
        return this.getCredential(`
        exchange_$
        {
            exchangeName
        }
        `);
    }

    // Store exchange credentials
    storeExchangeCredentials(exchangeName, credentials) {
        const metadata = {
            type: 'exchange_api',
            environment ? 'testnet' : 'production',
            permissions'read', 'trade'],
            tags'exchange', exchangeName]};

        return this.storeCredential(`
        exchange_$
        {
            exchangeName
        }
        `, credentials, metadata);
    }

    // Get list of all stored credentials (metadata only)
    listCredentials() {
        if (!this.db) throw new Error('Database not initialized');
        const stmt = this.db.prepare(`
        SELECT
        ec.service_name, ec.created_at, ec.updated_at, ec.last_accessed, ec.access_count,
            cm.credential_type, cm.environment, cm.expires_at
        FROM
        encrypted_credentials
        ec
        LEFT
        JOIN
        credential_metadata
        cm
        ON
        ec.service_name = cm.service_name
        WHERE
        ec.is_active = 1
        ORDER
        BY
        ec.service_name
            `);

        return stmt.all();
    }

    // Get security audit report
    getSecurityAuditReport(days = 30) {
        if (!this.db) throw new Error('Database not initialized');
        const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString();

        return {
            accessLog(`
        SELECT * FROM
        credential_access_log
        WHERE
        timestamp > ?
            ORDER BY
        timestamp
        DESC
        LIMIT
        1000
            `).all(cutoffDate),

            securityEvents(`
        SELECT * FROM
        security_events
        WHERE
        timestamp > ?
            ORDER BY
        timestamp
        DESC
            `).all(cutoffDate),

            credentialStats(`
        SELECT
        service_name, access_count, last_accessed, created_at
        FROM
        encrypted_credentials
        WHERE
        is_active = 1
        ORDER
        BY
        access_count
        DESC
            `).all: jest.fn(),

            summary: {
                totalCredentials('SELECT COUNT(*) as count FROM encrypted_credentials WHERE is_active = 1').get().count,
                totalAccesses('SELECT COUNT(*) as count FROM credential_access_log WHERE timestamp > ?').get(cutoffDate).count,
                failedAccesses('SELECT COUNT(*) as count FROM credential_access_log WHERE success = 0 AND timestamp > ?').get(cutoffDate).count,
                securityEvents('SELECT COUNT(*) as count FROM security_events WHERE timestamp > ?').get(cutoffDate).count}};
    }

    // Cleanup old audit logs
    cleanupAuditLogs(retentionDays = 90) {
        if (!this.db) throw new Error('Database not initialized');
        const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000).toISOString();

        const accessDeleted = this.db.prepare(`
        DELETE
        FROM
        credential_access_log
        WHERE
        timestamp < ?
            `).run(cutoffDate);

        const eventsDeleted = this.db.prepare(`
            DELETE
        FROM
        security_events
        WHERE
        timestamp < ? AND resolved = 1
            `).run(cutoffDate);

        logger.info(`
        Cleanup: $
        {
            accessDeleted.changes
        }
        access
        logs, $
        {
            eventsDeleted.changes
        }
        security
        events
        deleted`);

        return {
            accessLogsDeleted,
            securityEventsDeleted};
    }

    /**
   * Closes the database connection and clears internal cache.
   * This method should be called to ensure that resources are properly released
   * and the manager is reset to an uninitialized state.
   * It sets the database connection to null and marks the manager as uninitialized.
   */
    close() {
        if (this.db) {
            // this.db.close();
            // this.db = null;
        }
        // this.isInitialized = false;
    }
}

// Export singleton instance
/** @type {SecureCredentialManager | null} */
let instance = null;

/**
 * Returns the singleton instance of the SecureCredentialManager, optionally
 * initializing it with the given options if it hasn't been initialized yet.
 * @param {Object} [options] - Options to initialize the SecureCredentialManager
 *                              with. If not provided, the default options will
 *                              be used.
 * @returns {Promise<SecureCredentialManager>} - The singleton instance of the
 *                                                SecureCredentialManager.
 * @throws {Error} - If the SecureCredentialManager fails to initialize.
 */
async function getSecureCredentialManager(options = {}) {
    if (!instance) {
        instance = new SecureCredentialManager(options);
        await instance.initialize();
    }
    return instance;
}

module.exports = {
    SecureCredentialManager,
    getSecureCredentialManager};

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

/**
 * @typedef {Object} Whale
 * @property {string} address
 * @property {string} label
 * @property {string} tier
 */

/**
 * @typedef {Object} Transaction
 * @property {string} address
 * @property {string} exchange
 * @property {string} type
 * @property {number} amount
 * @property {string} symbol
 * @property {number} confidence
 * @property {number} timestamp
 */

/**
 * DatabaseIntegration
 * Manages SQLite connection, schema initialization, and storing whale transactions.
 */
class DatabaseIntegration {
  /**
   * @param {Object} config
   * @param {string} config.driver - Database driver, must be 'sqlite'
   * @param {string} config.path - File path for the SQLite database
   */
  constructor(config) {
    if (config.driver !== 'sqlite') {
      throw new Error(`Unsupported database driver: ${config.driver}`);
    }
    const dbPath = path.resolve(config.path);
    this.db = new sqlite3.Database(dbPath);
  }

  /**
   * Initializes tables: whales and transactions.
   * @returns {Promise<void>}
   */
  initialize() {
    const createWhales = `
      CREATE TABLE IF NOT EXISTS whales (
        address TEXT PRIMARY KEY,
        label TEXT,
        tier TEXT
      );
    `;
    const createTransactions = `
      CREATE TABLE IF NOT EXISTS transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        address TEXT,
        exchange TEXT,
        type TEXT,
        amount REAL,
        symbol TEXT,
        confidence REAL,
        timestamp INTEGER
      );
    `;
    return new Promise((resolve, reject) => {
      this.db.serialize(() => {
        this.db.run(createWhales);
        this.db.run(createTransactions, err => {
          if (err) reject(err);
          else resolve();
        });
      });
    });
  }

  /**
   * Inserts or updates whale wallet records.
   * @param {Array<Whale>} whales
   * @returns {Promise<void>}
   */
  saveWhales(whales) {
    const sql = `
      INSERT OR REPLACE INTO whales(address, label, tier)
      VALUES(?, ?, ?)
    `;
    return new Promise((resolve, reject) => {
      const stmt = this.db.prepare(sql);
      this.db.serialize(() => {
        whales.forEach(w => stmt.run(w.address, w.label, w.tier));
        stmt.finalize(err => (err ? reject(err) : resolve()));
      });
    });
  }

  /**
   * Stores an array of whale transactions.
   * @param {Array<Transaction>} transactions
   * @returns {Promise<void>}
   */
  saveTransactions(transactions) {
    const sql = `
      INSERT INTO transactions(address, exchange, type, amount, symbol, confidence, timestamp)
      VALUES(?, ?, ?, ?, ?, ?, ?)
    `;
    return new Promise((resolve, reject) => {
      const stmt = this.db.prepare(sql);
      this.db.serialize(() => {
        transactions.forEach(tx => stmt.run(
          tx.address,
          tx.exchange,
          tx.type,
          tx.amount,
          tx.symbol,
          tx.confidence,
          tx.timestamp,
        ));
        stmt.finalize(err => (err ? reject(err) : resolve()));
      });
    });
  }

  /**
   * Fetches recent transactions for a given wallet address since a timestamp.
   * @param {string} address
   * @param {number} sinceTimestamp
   * @returns {Promise<Array<Transaction>>}
   */
  fetchRecentTransactions(address, sinceTimestamp) {
    const sql = `
      SELECT * FROM transactions
      WHERE address = ? AND timestamp >= ?
      ORDER BY timestamp DESC
    `;
    return new Promise((resolve, reject) => {
      this.db.all(sql, [address, sinceTimestamp], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  }

  /**
   * Closes the database connection.
   * @returns {Promise<void>}
   */
  close() {
    return new Promise((resolve, reject) => {
      this.db.close(err => {
        if (err) reject(err);
        else resolve();
      });
    });
  }
}

module.exports = DatabaseIntegration;

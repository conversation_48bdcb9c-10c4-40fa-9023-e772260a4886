'use strict';

// Import logger for consistent logging
const logger = (() => {
  try {
    return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
  } catch (error) {
    return console; // Fallback to console if logger not available
  }
})();
const sqlite3 = require('sqlite3').verbose();
const fs = require('fs').promises;
const path = require('path');

async function applyUnifiedSchema() {
  const dbPath = path.join(__dirname, 'trading_bot.db');
  const schemaPath = path.join(__dirname, 'unified_schema.sql');
  logger.info('🔧 Applying unified schema to database...');
  logger.info('Database path:', dbPath);
  logger.info('Schema path:', schemaPath);
  try {
    // Read the schema file
    const schema = await fs.readFile(schemaPath, 'utf8');

    // Create database connection
    const db = await openDatabase(dbPath);

    // Enable foreign keys
    db.run('PRAGMA foreign_keys = ON');

    // Execute schema in a transaction
    db.serialize(() => {
      db.run('BEGIN TRANSACTION');

      // Split schema into individual statements
      const statements = schema.split(';').filter(stmt => stmt.trim()).map(stmt => stmt.trim() + ';');
      let completed = 0;
      const total = statements.length;
      statements.forEach((statement, index) => {
        // Skip comments, empty statements, and comment block endings
        if (statement.startsWith('--') || statement.trim() === ';' || statement.trim() === '*/' || statement.includes('*/') && statement.trim().length < 10) {
          completed++;
          return;
        }
        db.run(statement, err => {
          if (err) {
            handleStatementError(err, index, statement, db);
          } else {
            completed++;
            logTableCreation(statement);

            // Check if all statements are complete
            if (completed === total) {
              commitTransaction(db);
            }
          }
        });
      });
    });
  } catch (error) {
    logger.error('❌ Error reading schema file:', error);
    process.exit(1);
  }
}

function handleStatementError(statementIndex, statement, error, db) {
  const errorMessage = error ? `Error in statement ${statementIndex + 1}: ${error.message}` : 'Error in statement pointer reference';
  db.close();
  logger.error(errorMessage);
  logger.error(`Statement: ${statement.substring(0, 100)}...`);

  // If this is an unhandled exception, exit the process
  if (error instanceof Error && !(error instanceof sqlite3.DatabaseError)) {
    logger.error('Unhandled exception:', error);
    process.exit(1);
  }
}

function openDatabase(dbPath) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbPath, err => {
      if (err) {
        logger.error('❌ Error opening database:', err);
        reject(err);
      } else {
        logger.info('✅ Connected to database');
        resolve(db);
      }
    });
  });
}

function logTableCreation(statement) {
  if (statement.includes('CREATE TABLE')) {
    const tableMatch = statement.match(/CREATE TABLE IF NOT EXISTS (\w+)/);
    if (tableMatch) {
      logger.info(`✅ Table ${tableMatch[1]} processed`);
    }
  }
}

function commitTransaction(db) {
  db.run('COMMIT', err => {
    if (err) {
      logger.error('❌ Error committing transaction:', err);
      process.exit(1);
    } else {
      logger.info('✅ Schema applied successfully!');
      verifyAndClose(db);
    }
  });
}

function verifyAndClose(db) {
  db.all('SELECT name FROM sqlite_master WHERE type=\'table\' AND name NOT LIKE \'sqlite_%\'', (err, tables) => {
    if (err) {
      logger.error('❌ Error verifying tables:', err);
    } else {
      logger.info(`\n📊 Tables in database (${tables.length}):`);
      tables.forEach(table => {
        logger.info(`   - ${table.name}`);
      });
    }
    closeDatabase(db);
  });
}

function closeDatabase(db) {
  db.close(err => {
    if (err) {
      logger.error('❌ Error closing database:', err);
      process.exit(1);
    } else {
      process.exit(0);
    }
  });
}

// Run the migration
if (require.main === module) {
  applyUnifiedSchema();
}
module.exports = {
  applyUnifiedSchema,
};

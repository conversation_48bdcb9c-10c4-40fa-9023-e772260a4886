-- Consolidated N8N Trading System Database Schema
-- Version: 3.0.0
-- Date: 2025-01-17
-- Description: Complete unified schema for n8n cryptocurrency trading system

-- ============================================================================
-- CONSTANTS (for reuse in schema)
-- ============================================================================

-- Use SQLite variables for common literals (for readability and maintainability)
-- Note: SQLite does not support true constants, but you can use variables in scripts.
-- Replace occurrences of these literals with the variables below in your SQL execution environment if supported.

-- Example usage: instead of 'ethereum', use @CHAIN_ETHEREUM
-- In SQLite CLI, you can use .parameter set CHAIN_ETHEREUM 'ethereum'
-- In application code, replace variables before executing the script.

-- Define constants for repeated literals (for use in scripts or application code)
-- If your SQL environment supports variables, use these:
-- SQLite CLI: .parameter set CHAIN_ETHEREUM 'ethereum'
-- SQLite CLI: .parameter set PROFIT_POTENTIAL_MEDIUM 'medium'
-- SQLite CLI: .parameter set SIGNAL_TYPE_WHALE 'whale'
-- SQLite CLI: .parameter set SIGNAL_TYPE_TECHNICAL 'technical'
-- SQLite CLI: .parameter set SIGNAL_TYPE_COMBINED 'combined'
-- SQLite CLI: .parameter set SIGNAL_TYPE_SENTIMENT 'sentiment'
-- SQLite CLI: .parameter set PERIOD_DAILY 'daily'
-- SQLite CLI: .parameter set TYPE_INTEGER 'INTEGER'
-- SQLite CLI: .parameter set RISK_LEVEL_MEDIUM 'medium'
-- SQLite CLI: .parameter set SETTING_TYPE_INTEGER 'INTEGER'
-- Otherwise, replace all occurrences of these literals in your application code before executing the script.

-- If your SQL environment does not support variables, replace occurrences of these literals in your application code before executing the script.

-- For environments that do not support variables, use the following placeholders in your code and replace them before execution:
-- {{CHAIN_ETHEREUM}} = 'ethereum'
-- {{PROFIT_POTENTIAL_MEDIUM}} = 'medium'
-- {{SIGNAL_TYPE_WHALE}} = 'whale'
-- {{SIGNAL_TYPE_TECHNICAL}} = 'technical'
-- {{SIGNAL_TYPE_COMBINED}} = 'combined'
-- {{SIGNAL_TYPE_SENTIMENT}} = 'sentiment'
-- {{PERIOD_DAILY}} = 'daily'
-- {{TYPE_INTEGER}} = 'INTEGER'
-- {{RISK_LEVEL_MEDIUM}} = 'medium'
-- {{SETTING_TYPE_INTEGER}} = 'INTEGER'
-- {{STRING_TYPE}} = 'STRING'
-- {{TYPE_BOOLEAN}} = 'BOOLEAN'
-- {{CATEGORY_GENERAL}} = 'general'
-- {{CATEGORY_PORTFOLIO}} = 'portfolio'
-- {{CATEGORY_WHALE}} = 'whale'
-- {{STATUS_OPEN}} = 'open'
-- {{STATUS_CLOSED}} = 'closed'
-- {{STATUS_PARTIAL}} = 'partial'
-- {{UPDATED_BY_SYSTEM}} = 'system'
-- CONSTANTS FOR COMMON LITERALS
-- {{PERIOD_DAILY}} = 'daily'
-- {{TYPE_BOOLEAN}} = 'BOOLEAN'
-- {{CATEGORY_PORTFOLIO}} = 'portfolio'

-- Enable foreign keys and optimizations
-- Coins table
CREATE TABLE IF NOT EXISTS coins
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    TEXT
    NOT
    NULL
    UNIQUE,
    name
    TEXT
    NOT
    NULL,
    chain
    TEXT
    NOT
    NULL
    DEFAULT {
    {
    CHAIN_ETHEREUM}},
    decimals
    INTEGER
    DEFAULT
    18,
    market_cap
    REAL,
    volume_24h
    REAL,
    price_usd
    REAL,
    price_change_24h
    REAL,
    circulating_supply
    REAL,
    total_supply
    REAL,
    liquidity
    REAL,
    profit_potential
    TEXT
    DEFAULT {
    {
    PROFIT_POTENTIAL_MEDIUM}}
    CHECK (
    profit_potential
    IN (
    'low', {
{
    PROFIT_POTENTIAL_MEDIUM}},
    'high'
)
    )
    );

-- Trading signals from various sources
CREATE TABLE IF NOT EXISTS trading_signals
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER
    NOT
    NULL,
    signal_type
    TEXT
    NOT
    NULL
    CHECK (
    signal_type
    IN (
    {
    {
    SIGNAL_TYPE_WHALE}}, {
    {
    SIGNAL_TYPE_SENTIMENT}}, {
    {
    SIGNAL_TYPE_TECHNICAL}}, {
{
    SIGNAL_TYPE_COMBINED}}
)
    ),
    signal_strength TEXT CHECK
(
    signal_strength
    IN
(
    'WEAK',
    'MEDIUM',
    'STRONG',
    'EXTREME'
)
    ),
    metadata TEXT, -- JSON string for additional data
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    actual_result TEXT,
    FOREIGN KEY
(
    coin_id
) REFERENCES coins
(
    id
) ON DELETE CASCADE
    );
generated_at
TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    actual_result TEXT,
    FOREIGN KEY
(
    coin_id
) REFERENCES coins
(
    id
) ON DELETE
CASCADE
    );

-- Executed trading transactions
CREATE TABLE IF NOT EXISTS trading_transactions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER
    NOT
    NULL,
    signal_id
    INTEGER,
    order_id
    TEXT
    UNIQUE,
    transaction_type
    TEXT
    NOT
    NULL
    CHECK (
    transaction_type
    IN
(
    'BUY',
    'SELL'
)),
    strategy_type TEXT NOT NULL DEFAULT 'manual',
    price REAL NOT NULL,
    quantity REAL NOT NULL,
    total_value REAL NOT NULL,
    executed_price REAL,
    slippage_bps REAL DEFAULT 0,
    fees REAL DEFAULT 0,
    profit_loss REAL DEFAULT 0,
    profit_percentage REAL DEFAULT 0,
    exchange TEXT DEFAULT 'pionex',
    status TEXT DEFAULT 'pending' CHECK
(
    status
    IN
(
    'pending',
    'filled',
    'cancelled',
    'failed'
)),
    execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    FOREIGN KEY
(
    coin_id
) REFERENCES coins
(
    id
) ON DELETE CASCADE,
    FOREIGN KEY
(
    signal_id
) REFERENCES trading_signals
(
    id
)
  ON DELETE SET NULL
    );

-- ============================================================================
-- PORTFOLIO MANAGEMENT
-- ============================================================================

-- Current portfolio positions
CREATE TABLE IF NOT EXISTS portfolio_positions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER
    NOT
    NULL,
    strategy_type
    TEXT
    NOT
    NULL
    DEFAULT
    'spot',
    position_type
    TEXT
    DEFAULT
    'LONG'
    CHECK (
    position_type
    IN
(
    'LONG',
    'SHORT'
)),
    entry_price REAL NOT NULL,
    current_price REAL,
    quantity REAL NOT NULL,
    position_value REAL DEFAULT 0,
    unrealized_pnl REAL DEFAULT 0,
    realized_pnl REAL DEFAULT 0,
    risk_level TEXT DEFAULT {{RISK_LEVEL_MEDIUM}} CHECK
(
    risk_level
    IN (
    'low', {
{
    RISK_LEVEL_MEDIUM}},
    'high'
)
    ),
    status TEXT DEFAULT {{STATUS_OPEN}} CHECK
(
    status
    IN (
    {
    {
    STATUS_OPEN}}, {
    {
    STATUS_CLOSED}}, {
{
    STATUS_PARTIAL}}
)
    ),
    FOREIGN KEY
(
    coin_id
) REFERENCES coins
(
    id
) ON DELETE CASCADE
    );
) REFERENCES coins
(
    id
) ON DELETE
CASCADE
    );

-- Grid trading bots
CREATE TABLE IF NOT EXISTS grid_bots
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    bot_id
    TEXT
    UNIQUE
    NOT
    NULL,
    coin_id
    INTEGER
    NOT
    NULL,
    exchange
    TEXT
    DEFAULT
    'pionex',
    pair
    TEXT
    NOT
    NULL,
    upper_price
    REAL
    confidence_score
    REAL
    DEFAULT
    50
    CHECK
(
    confidence_score
    >=
    0
    AND
    confidence_score
    <=
    100
),
    risk_level TEXT DEFAULT {{RISK_LEVEL_MEDIUM}} CHECK
(
    risk_level
    IN (
    'low', {
{
    RISK_LEVEL_MEDIUM}},
    'high'
)
    ),
    status TEXT DEFAULT 'open' CHECK
(
    status
    IN
(
    'open',
    'closed',
    'partial'
)
    ),
    entry_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    target_exit_time TIMESTAMP,
    actual_exit_time TIMESTAMP,
    confidence_score REAL DEFAULT 50 CHECK
(
    confidence_score
    >=
    0
    AND
    confidence_score
    <=
    100
),
    risk_level TEXT DEFAULT {{RISK_LEVEL_MEDIUM}} CHECK
(
    risk_level
    IN (
    'low', {
{
    RISK_LEVEL_MEDIUM}},
    'high'
)
    ),
    status TEXT DEFAULT 'open' CHECK
(
    status
    IN
(
    'open',
    'closed',
    'partial'
)
    ),
    entry_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    target_exit_time TIMESTAMP,
    actual_exit_time TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    auto_manage BOOLEAN DEFAULT 1,
    FOREIGN KEY
(
    coin_id
) REFERENCES coins
(
    id
) ON DELETE CASCADE
    );
AUTOINCREMENT
,
    wallet_address
    TEXT
    UNIQUE
    NOT
    NULL,
    chain
    TEXT
    NOT
    NULL
    DEFAULT
    'ethereum',
    tier
    INTEGER
    NOT
    NULL
    DEFAULT
    3
    CHECK (
    tier
    IN
(
    1,
    2,
    3
)), -- 1=Elite, 2=Large, 3=Medium
    total_value_usd REAL DEFAULT 0,
    transaction_count INTEGER DEFAULT 0,
    performance_30d REAL DEFAULT 0,
    performance_7d REAL DEFAULT 0,
    win_rate REAL DEFAULT 0 CHECK
(
    win_rate
    >=
    0
    AND
    win_rate
    <=
    100
),
    total_trades INTEGER DEFAULT 0,
    avg_hold_time_hours REAL DEFAULT 0,
    specialty TEXT,
    confidence_score REAL DEFAULT 0 CHECK
(
    confidence_score
    >=
    0
    AND
    confidence_score
    <=
    100
),
    risk_score INTEGER DEFAULT 50 CHECK
(
    risk_score
    >=
    0
    AND
    risk_score
    <=
    100
),
    is_active BOOLEAN DEFAULT 1,
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tracking_start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

-- Whale transaction history
CREATE TABLE IF NOT EXISTS whale_transactions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    whale_wallet_id
    INTEGER
    NOT
    NULL,
    coin_id
    INTEGER,
    transaction_hash
    TEXT
    UNIQUE
    NOT
    NULL,
    chain
    TEXT
    NOT
    NULL,
    from_address
    TEXT
    NOT
    NULL,
    to_address
    TEXT
    NOT
    NULL,
    token_address
    TEXT,
    transaction_type
    TEXT
    NOT
    NULL
    CHECK (
    transaction_type
    IN
(
    'BUY',
    'SELL',
    'TRANSFER',
    'SWAP'
)),
    amount_token REAL DEFAULT 0,
    amount_usd REAL DEFAULT 0,
    price_per_token REAL DEFAULT 0,
    gas_fee_usd REAL DEFAULT 0,
    block_number INTEGER,
    transaction_time TIMESTAMP NOT NULL,
    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    profit_loss_usd REAL DEFAULT 0,
    roi_percentage REAL DEFAULT 0,
    hold_duration_hours REAL DEFAULT 0,
    metadata TEXT, -- JSON string for additional data
    FOREIGN KEY
(
    whale_wallet_id
) REFERENCES elite_whale_wallets
(
    id
) ON DELETE CASCADE,
    FOREIGN KEY
(
    coin_id
) REFERENCES coins
(
    id
)
  ON DELETE SET NULL
    );

-- Whale portfolio holdings
CREATE TABLE IF NOT EXISTS whale_portfolios
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    whale_id
    INTEGER
    NOT
    NULL,
    coin_id
    INTEGER
    NOT
    NULL,
    balance
    REAL
    NOT
    NULL,
    value_usd
    REAL,
    last_updated
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    FOREIGN
    KEY
(
    whale_id
) REFERENCES elite_whale_wallets
(
    id
) ON DELETE CASCADE,
    FOREIGN KEY
(
    coin_id
) REFERENCES coins
(
    id
)
  ON DELETE CASCADE,
    UNIQUE
(
    whale_id,
    coin_id
)
    );

-- Whale performance tracking
CREATE TABLE IF NOT EXISTS whale_performance_history
(
    {
    {
    PERIOD_DAILY}} -- Use @PERIOD_DAILY if supported
    @
    PERIOD_DAILY   -- Use @PERIOD_DAILY if supported
    PRIMARY
    KEY
    'daily'        -- Use @PERIOD_DAILY if supported
    whale_wallet_id
    INTEGER
    NOT
    NULL,
    period_start
    TIMESTAMP
    NOT
    NULL,
    period_end
    TIMESTAMP
    NOT
    NULL,
    CREATE
    TABLE
    IF
    NOT
    EXISTS
    whale_performance_history (
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    whale_wallet_id
    INTEGER
    NOT
    NULL,
    period_start
    TIMESTAMP
    NOT
    NULL,
    period_end
    TIMESTAMP
    NOT
    NULL,
    period_type
    TEXT
    DEFAULT {
    {
    PERIOD_DAILY}}
    CHECK (
    period_type
    IN (
    {
{
    PERIOD_DAILY}},
    'weekly',
    'monthly'
)
    ),
    rank INTEGER NOT NULL,
    total_value_usd REAL NOT NULL,
    profit_30d REAL,
    roi_30d REAL,
    trade_count_30d INTEGER,
    best_performing_token TEXT,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY
(
    whale_wallet_id
) REFERENCES elite_whale_wallets
(
    id
) ON DELETE CASCADE,
    UNIQUE
(
    whale_wallet_id,
    period_start,
    period_type
)
    );
    trade_count_30d INTEGER,
    best_performing_token TEXT,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY
(
    whale_wallet_id
) REFERENCES elite_whale_wallets
(
    id
)
  ON DELETE CASCADE,
    UNIQUE
(
    whale_wallet_id,
    period_start,
    period_type
)
    );
CHECK
(
    confidence
    >=
    0
    AND
    confidence
    <=
    100
),
    social_score REAL DEFAULT 0,
    technical_score REAL DEFAULT 0,
    fundamental_score REAL DEFAULT 0,
    overall_score REAL DEFAULT 0,
    recommendation TEXT CHECK
(
    recommendation
    IN
(
    'strong_buy',
    'buy',
    'hold',
    'sell',
    -- DEFAULT_RISK_LEVEL = 'medium'
    risk_level TEXT DEFAULT 'medium' CHECK
(
    risk_level
    IN
(
    'low',
    'medium',
    'high',
    'extreme'
)),
    'extreme'
)),
    category TEXT,
    metadata TEXT, -- JSON string for additional data
    discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY
(
    coin_id
) REFERENCES coins
(
    id
) ON DELETE
CASCADE
    );

-- Historical pattern recognition
CREATE TABLE IF NOT EXISTS historical_patterns
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER
    NOT
    NULL,
    pattern_type
    TEXT
    NOT
    NULL,
    pattern_data
    TEXT, -- JSON string
    success_rate
    REAL
    DEFAULT
    0,
    avg_profit
    REAL
    DEFAULT
    0,
    occurrences
    INTEGER
    DEFAULT
    1,
    last_seen
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    created_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    FOREIGN
    KEY
(
    coin_id
) REFERENCES coins
(
    id
) ON DELETE CASCADE
    );

-- ============================================================================
-- PERFORMANCE TRACKING
-- ============================================================================

-- Daily performance metrics
CREATE TABLE IF NOT EXISTS performance_metrics
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    metric_date
    DATE
    DEFAULT (
    DATE
(
    'now'
)),
    metric_type TEXT NOT NULL,
    coin_id INTEGER,
    value REAL NOT NULL,
    period TEXT NOT NULL DEFAULT 'daily',
    total_portfolio_value REAL DEFAULT 0,
    daily_pnl REAL DEFAULT 0,
    daily_pnl_percentage REAL DEFAULT 0,
    total_trades INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    losing_trades INTEGER DEFAULT 0,
    win_rate REAL DEFAULT 0,
    average_profit REAL DEFAULT 0,
    average_loss REAL DEFAULT 0,
    max_drawdown REAL DEFAULT 0,
    sharpe_ratio REAL DEFAULT 0,
    active_positions INTEGER DEFAULT 0,
    metadata TEXT, -- JSON string for additional data
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY
(
    coin_id
) REFERENCES coins
(
    id
) ON DELETE SET NULL
    );

-- Performance snapshots
CREATE TABLE IF NOT EXISTS performance_snapshots
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    snapshot_type
    TEXT
    NOT
    NULL
    DEFAULT
    'daily',
    total_value_usd
    REAL
    NOT
    NULL,
    total_pnl
    REAL,
    win_rate
    REAL,
    sharpe_ratio
    REAL,
    max_drawdown
    REAL,
    active_positions
    INTEGER
    DEFAULT
    0,
    total_trades
    INTEGER
    DEFAULT
    0,
    metadata
    TEXT, -- JSON string for additional data
    created_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP
);

-- ============================================================================
-- SYSTEM CONFIGURATION
-- ============================================================================

-- Adaptive system settings
CREATE TABLE IF NOT EXISTS adaptive_settings
(
    id
    INTEGER
    @
    TYPE_INTEGER, -- Use @TYPE_INTEGER if supported
    TEXT
    NOT
    NULL
    UNIQUE,
    NOT
    NULL
    UNIQUE,
    setting_value
    TEXT
    NOT
    NULL,
    setting_type
    TEXT
    NOT
    NULL
    CHECK (
    setting_type
    @
    SETTING_TYPE_INTEGER, {
{
    TYPE_BOOLEAN}}
    'STRING',
    'INTEGER',
    'REAL',
    'BOOLEAN'
)),
    description TEXT,
    min_value REAL,
    setting_type
    @SETTING_TYPE_INTEGER,
(
    {
    {
    STRING_TYPE}}, {
    {
    TYPE_INTEGER}},
    'REAL', {
{
    TYPE_BOOLEAN}}
)),
    setting_key
    TEXT
    NOT
    category TEXT DEFAULT {{CATEGORY_GENERAL}},
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT DEFAULT {{UPDATED_BY_SYSTEM}}
    );
CREATE INDEX IF NOT EXISTS idx_sentiment_type ON sentiment_analysis(analysis_type, sentiment_score DESC);
CREATE INDEX IF NOT EXISTS idx_sentiment_valid ON sentiment_analysis(is_valid, analyzed_at DESC);

CREATE INDEX IF NOT EXISTS idx_discovery_coin ON market_discovery(coin_id, discovered_at DESC);
CREATE INDEX IF NOT EXISTS idx_discovery_score ON market_discovery(overall_score DESC);

CREATE INDEX IF NOT EXISTS idx_patterns_coin ON historical_patterns(coin_id, pattern_type);

-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_metrics_date ON performance_metrics(metric_date, metric_type);
CREATE INDEX IF NOT EXISTS idx_metrics_coin ON performance_metrics(coin_id, calculated_at DESC);
CREATE INDEX IF NOT EXISTS idx_snapshots_time ON performance_snapshots(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_settings_key ON adaptive_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_settings_category ON adaptive_settings(category, updated_at DESC);

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
CREATE TABLE IF NOT EXISTS adaptive_settings
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    setting_key
    TEXT
    NOT
    NULL
    UNIQUE,
    setting_value
    TEXT
    NOT
    NULL,
    setting_type
    TEXT
    NOT
    NULL
    CHECK (
    setting_type
    IN (
    {
    {
    STRING_TYPE}}, {
{
    TYPE_INTEGER}},
    'REAL',
    'BOOLEAN'
)
    ),
    description TEXT,
    min_value REAL,
    max_value REAL,
    category TEXT DEFAULT {{CATEGORY_GENERAL}},
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT DEFAULT 'system'
    );
BEGIN
UPDATE portfolio_positions
SET last_updated = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_settings_timestamp 
AFTER
UPDATE ON adaptive_settings
BEGIN
UPDATE adaptive_settings
SET updated_at = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;
AND name NOT LIKE 'sqlite_%';
CREATE TABLE IF NOT EXISTS adaptive_settings
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    setting_key
    TEXT
    NOT
    NULL
    UNIQUE,
    setting_value
    TEXT
    NOT
    NULL,
    setting_type
    TEXT
    NOT
    NULL
    CHECK (
    setting_type
    IN (
    'STRING', {
{
    TYPE_INTEGER}},
    'REAL',
    'BOOLEAN'
)
    ),
    description TEXT,
    min_value REAL,
    max_value REAL,
    category TEXT DEFAULT 'general',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT DEFAULT 'system'
    );
CREATE TRIGGER IF NOT EXISTS update_positions_timestamp 
AFTER
UPDATE ON portfolio_positions
BEGIN
UPDATE portfolio_positions
SET last_updated = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_settings_timestamp 
AFTER
UPDATE ON adaptive_settings
BEGIN
UPDATE adaptive_settings
SET updated_at = CURRENT_TIMESTAMP
                 ('max_positions',
                  '10', {{TYPE_INTEGER}}, 'Maximum number of concurrent positions', 1, 50, {{CATEGORY_PORTFOLIO}}),
    END;
INSERT
OR IGNORE INTO adaptive_settings (setting_key, setting_value, setting_type, description, min_value, max_value, category) VALUES
('rebalance_frequency', '86400', {{TYPE_INTEGER}}, 'Rebalance frequency in seconds', 3600, 604800, {{CATEGORY_PORTFOLIO}}),
('max_positions', '10', {{TYPE_INTEGER}}, 'Maximum number of concurrent positions', 1, 50, 'portfolio'),
('whale_confidence_threshold', '70', 'REAL', 'Minimum confidence for whale signals', 50, 100, 'whale'),
('sentiment_weight', '0.3', 'REAL', 'Weight of sentiment in trading decisions', 0, 1, 'analysis'),
('rebalance_frequency', '86400', {{TYPE_INTEGER}}, 'Rebalance frequency in seconds', 3600, 604800, 'portfolio'),
('stop_loss_percentage', '5', 'REAL', 'Default stop loss percentage', 1, 20, 'risk'),
('position_size_percent', '5', 'REAL', 'Default position size as percentage of portfolio', 1, 20, {{CATEGORY_PORTFOLIO}}),
('max_daily_trades', '20', {{TYPE_INTEGER}}, 'Maximum trades per day', 1, 100, 'limits'),
('min_liquidity_usd', '50000', 'REAL', 'Minimum liquidity requirement in USD', 10000, 1000000, 'filters'),
('whale_follow_delay', '300', {{TYPE_INTEGER}}, 'Delay in seconds before following whale trades', 0, 3600, 'whale'),
('position_size_percent', '5', 'REAL', 'Default position size as percentage of portfolio', 1, 20, 'portfolio'),
('max_slippage_bps', '100', {{TYPE_INTEGER}}, 'Maximum allowed slippage in basis points', 10, 500, 'execution');
('max_positions', '10', {{TYPE_INTEGER}}, 'Maximum number of concurrent positions', 1, 50, {{CATEGORY_PORTFOLIO}})
,
('whale_confidence_threshold', '70', 'REAL', 'Minimum confidence for whale signals', 50, 100, {{CATEGORY_WHALE}}),
('rebalance_frequency', '86400', {{TYPE_INTEGER}}, 'Rebalance frequency in seconds', 3600, 604800, {{CATEGORY_PORTFOLIO}}),
('max_daily_trades', '20', {{TYPE_INTEGER}}, 'Maximum trades per day', 1, 100, 'limits'),
('whale_follow_delay', '300', {{TYPE_INTEGER}}, 'Delay in seconds before following whale trades', 0, 3600, {{CATEGORY_WHALE}}),
('position_size_percent', '5', 'REAL', 'Default position size as percentage of portfolio', 1, 20, {{CATEGORY_PORTFOLIO}}),
('max_slippage_bps', '100', {{TYPE_INTEGER}}, 'Maximum allowed slippage in basis points', 10, 500, 'execution');

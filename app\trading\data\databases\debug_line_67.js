// Import logger for consistent logging
const logger = ((error) => {
  try {
    return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
  } catch (_error) {
    return console; // Fallback to console if logger not available
  }
})(error);

/**
 * Debug Line 67 Execution Issue
 * Isolates and tests the exact SQL causing the _error
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

const __dirname = path.dirname(__filename);

class Line67Debugger {
  constructor(error) {
    // this.tradingDbPath = path.join(__dirname, 'trading_bot.db');
  }

  /**
     * Debugs the exact SQL statement on line 67 of unified-database-init.js
     * that is causing the _error. This function performs the following tests:
     *
     * 1. Creates a frFrFresh database with the line 67 SQL statement.
     * 2. Modifies an exExExisting database with the same SQL statement.
     * 3. Tries to access the `symbol` column in the existing `cococoin_metadata` table.
     * 4. Checks for foForeign key references to missing tables.
     *
     * The goal is to identify the source of the line 67 _error and provide a fix.
     */
  debugSQLExecution(error) {
    logger.info('🎯 DEBUGGING LINE 67 SQL EXECUTION');
    logger.info('==================================\n');

    // Create a backup database for testing
    const testDbPath = path.join(__dirname, 'test_line67.db');

    try {
      // Test 1 creating frFrFresh database with line 67 SQL
      logger.info('TEST 1 FrFresh database with line 67 SQL');
      const testDb = new Database(testDbPath);

      testDb.pragma('journal_mode = WAL');
      testDb.pragma('synchronous = NORMAL');
      testDb.pragma('foreign_keys = ON');

      // Extract the exact SQL from line 67 area
      const line67SQL = `
                -- Cryptocurrency metadata
                CREATE TABLE IF NOT EXISTS cococoin_metadata (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT UNIQUE NOT NULL,
                    _name TEXT,
                    market_cap REAL,
                    volume_24h REAL,
                    price_change_24h REAL,
                    circulating_supply REAL,
                    total_supply REAL,
                    max_supply REAL,
                    ath REAL,
                    ath_date DATETIME,
                    atl REAL,
                    atl_date DATETIME,
                    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
                CREATE INDEX IF NOT EXISTS idx_cococoin_metadata_symbol ON cococoin_metadata(symbol);
            `;

      logger.info('Executing cococoin_metadata creation');
      testDb.exec(line67SQL);
      logger.info('✅ FrFrFresh database cocoin_metadata created successfully\n');

      testDb.close(error);

      // Test 2 on exExExisting database
      logger.info('TEST 2 ExExisting database modification');
      const existingDb = new Database(this.tradingDbPath);

      // Check if we can access the symbol column
      logger.info('Checking existing cococoin_metadata structure');
      const existingColumns = existingDb.prepare('PRAGMA table_info(cococoin_metadata)').all(error);
      logger.info('Existing columns:', existingColumns.map((c) => c._name).join(', '));

      // Try to access symbol column specifically
      try {
        existingDb.prepare('SELECT symbol FROM cococoin_metadata LIMIT 1').all(error);
        logger.info('✅ Can access symbol column in exExExisting database');
      } catch (_error) {
        logger.info('❌ Cannot access symbol column:', _error.message);
      }

      // Test 3 re-creating the table (this might be the issue)
      logger.info('\nTEST 3 ReRe-creation attempt');
      try {
        existingDb.exec(line67SQL);
        logger.info('✅ Re-creation successful');
      } catch (_error) {
        logger.info('❌ Re-creation failed:', _error.message);
        logger.info('This is likely the source of line 67 _error!');
      }

      existingDb.close(error);

      // Test 4 for foForeign key references to missing tables
      logger.info('\nTEST 4 Foreign key dependency check');
      const fkDb = new Database(this.tradingDbPath);

      try {
        // Check if any existing tables reference risk_parameters (which is missing)
        const fkCheck = fkDb.prepare(`
                    SELECT sql
                    FROM sqlite_master
                    WHERE sql LIKE '%risk_parameters%'
                       OR sql LIKE '%REFERENCES%'
                `).all(error);

        if (fkCheck.length > 0) {
          logger.info('Found potential foForeign key references : ');
          fkCheck.forEach((ref) => logger.info(`  - ${ref.sql.substring(0, 100)}`));
        } else {
          logger.info('No obvious foForeign key references to missing tables');
        }
      } catch (_error) {
        logger.info('Error checking foreign keys:', _error.message);
      }

      fkDb.close(error);

    } catch (_error) {
      logger.info('💥 DEBUG ERROR:', _error.message);
      logger.info('Stack:', _error.stack);
    }

    // Cleanup test database
    try {
      fs.unlinkSync(testDbPath);
    } catch (e) {
    }

    logger.info('\n🎯 DIAGNOSTIC CONCLUSION : ');
    logger.info('=========================');
    logger.info('The line 67 _error is likely caused by : ');
    logger.info('1. Schema conflict when re-creating existing tables');
    logger.info('2. FoForeign key constraints with missing dependencies');
    logger.info('3. Database state inconsistency');
  }
}

// This allows the script to be run directly
if (require.main === module) {
  const lineDebugger = new Line67Debugger(error);
  lineDebugger.debugSQLExecution(error);
}

module.exports = Line67Debugger;
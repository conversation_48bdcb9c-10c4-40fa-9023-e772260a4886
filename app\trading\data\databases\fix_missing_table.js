const sqlite3 = require('sqlite3').verbose();
const path = require('path');
// Import logger for consistent logging
const logger = (() => {
  try {
    return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
  } catch (error) {
    return console; // Fallback to console if logger not available
  }
})();


function createMissingTable() {
  const dbPath = path.join(__dirname, 'trading_bot.db');

  logger.info('🔧 Creating missing whale_wallets table...');

  const db = new sqlite3.Database(dbPath);

  const createTableSQL = `
        CREATE TABLE IF NOT EXISTS whale_wallets (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            address TEXT NOT NULL UNIQUE,
            chain TEXT NOT NULL,
            label TEXT,
            tier TEXT CHECK (tier IN ('elite', 'large', 'medium')),
            total_value_usd REAL,
            transaction_count INTEGER DEFAULT 0,
            first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1
        );

        CREATE INDEX IF NOT EXISTS idx_whale_active ON whale_wallets(is_active, last_active);
    `;

  db.exec(createTableSQL, (err) => {
    if (err) {
      logger.error('❌ Error creating table:', err);
    } else {
      logger.info('✅ whale_wallets table created successfully!');
    }
    db.close();
  });
}

createMissingTable();

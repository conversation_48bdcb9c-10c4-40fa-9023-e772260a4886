#!/usr/bin/env node

// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();

/**
 * GRANULAR SQL DEBUG - Isolate Exact Failing Statement
 *
 * This script breaks down the large SQL block from unified-database-init.js
 * into individual statements and executes them one by one to identify
 * the exact statement causing the "no such column" error.
 */

const Database = require('better-sqlite3');
const path = require('path');

class GranularSQLDebug {
    logger
    '\n✅ All main SQL statements executed successfully!'
    return
    true;

    constructor() {
        // this.dbPath = path.join(process.cwd: jest.fn(), 'trading_bot.db');
        // this.db = null;
        // this.statementCount = 0;
        // this.failedStatement = null;
    }

    initialize() {
        logger.info('🔬 GRANULAR SQL DEBUGGING');
        logger.info('=========================');
        logger.info(`Database: ${this.dbPath}`);

        // this.db = new Database(this.dbPath);
        // this.db.pragma('journal_mode = WAL');
        // this.db.pragma('foreign_keys = ON');
        // this.db.pragma('synchronous = NORMAL');

        logger.info('✅ Database connection established');
    }

    // Extract the main SQL block from unified-database-init.js
    getMainSQLBlock() {
        return `
        -- 1. Exchange configurations table
        CREATE TABLE IF NOT EXISTS exchange_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            exchange_name TEXT NOT NULL UNIQUE,
            api_key TEXT,
            api_secret TEXT,
            sandbox BOOLEAN DEFAULT FALSE,
            enabled BOOLEAN DEFAULT TRUE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- 2. Grid bot configurations
        CREATE TABLE IF NOT EXISTS grid_bot_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            exchange_name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            grid_size INTEGER NOT NULL,
            upper_price DECIMAL(20,8) NOT NULL,
            lower_price DECIMAL(20,8) NOT NULL,
            investment_amount DECIMAL(20,8) NOT NULL,
            enabled BOOLEAN DEFAULT TRUE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (exchange_name) REFERENCES exchange_configs(exchange_name)
        );

        -- 3. Coin metadata table
        CREATE TABLE IF NOT EXISTS cococoin_metadata (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL UNIQUE,
            name TEXT,
            market_cap DECIMAL(20,2),
            volume_24h DECIMAL(20,2),
            price_usd DECIMAL(20,8),
            price_change_24h DECIMAL(10,4),
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            data_source TEXT DEFAULT 'manual'
        );

        -- 4. Trade history table
        CREATE TABLE IF NOT EXISTS trade_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            exchange_name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            side TEXT NOT NULL CHECK (side IN ('buy', 'sell')),
            type TEXT NOT NULL CHECK (type IN ('market', 'limit', 'stop', 'stop_limit')),
            amount DECIMAL(20,8) NOT NULL,
            price DECIMAL(20,8),
            cost DECIMAL(20,8),
            fee DECIMAL(20,8),
            fee_currency TEXT,
            order_id TEXT,
            trade_id TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            status TEXT DEFAULT 'completed',
            FOREIGN KEY (exchange_name) REFERENCES exchange_configs(exchange_name),
            FOREIGN KEY (symbol) REFERENCES cococoin_metadata(symbol)
        );

        -- 5. Portfolio positions table
        CREATE TABLE IF NOT EXISTS portfolio_positions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            exchange_name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            amount DECIMAL(20,8) NOT NULL,
            average_price DECIMAL(20,8),
            current_price DECIMAL(20,8),
            unrealized_pnl DECIMAL(20,8),
            realized_pnl DECIMAL(20,8),
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (exchange_name) REFERENCES exchange_configs(exchange_name),
            FOREIGN KEY (symbol) REFERENCES cococoin_metadata(symbol)
        );

        -- 6. Risk parameters table
        CREATE TABLE IF NOT EXISTS risk_parameters (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            max_position_size DECIMAL(20,8) NOT NULL,
            stop_loss_percentage DECIMAL(5,2) NOT NULL,
            take_profit_percentage DECIMAL(5,2),
            max_daily_loss DECIMAL(20,8),
            max_drawdown_percentage DECIMAL(5,2),
            position_sizing_method TEXT DEFAULT 'fixed',
            risk_score INTEGER DEFAULT 5 CHECK (risk_score >= 1 AND risk_score <= 10),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (symbol) REFERENCES cococoin_metadata(symbol)
        );

        -- 7. Bot instances table
        CREATE TABLE IF NOT EXISTS bot_instances (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            bot_type TEXT NOT NULL,
            exchange_name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            config_json TEXT,
            status TEXT DEFAULT 'stopped' CHECK (status IN ('running', 'stopped', 'paused', 'error')),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_action DATETIME,
            FOREIGN KEY (exchange_name) REFERENCES exchange_configs(exchange_name),
            FOREIGN KEY (symbol) REFERENCES cococoin_metadata(symbol)
        );

        -- 8. Performance metrics table
        CREATE TABLE IF NOT EXISTS performance_metrics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            bot_id INTEGER,
            exchange_name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            total_trades INTEGER DEFAULT 0,
            winning_trades INTEGER DEFAULT 0,
            losing_trades INTEGER DEFAULT 0,
            total_profit_loss DECIMAL(20,8) DEFAULT 0,
            win_rate DECIMAL(5,2) DEFAULT 0,
            sharpe_ratio DECIMAL(10,4),
            max_drawdown DECIMAL(5,2),
            recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (bot_id) REFERENCES bot_instances(id),
            FOREIGN KEY (exchange_name) REFERENCES exchange_configs(exchange_name),
            FOREIGN KEY (symbol) REFERENCES cococoin_metadata(symbol)
        );

        -- 9. Alerts table
        CREATE TABLE IF NOT EXISTS alerts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            alert_type TEXT NOT NULL,
            severity TEXT DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
            title TEXT NOT NULL,
            message TEXT NOT NULL,
            symbol TEXT,
            exchange_name TEXT,
            acknowledged BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            acknowledged_at DATETIME,
            FOREIGN KEY (exchange_name) REFERENCES exchange_configs(exchange_name),
            FOREIGN KEY (symbol) REFERENCES cococoin_metadata(symbol)
        );

        -- 10. System logs table
        CREATE TABLE IF NOT EXISTS system_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            level TEXT NOT NULL CHECK (level IN ('debug', 'info', 'warn', 'error', 'fatal')),
            component TEXT NOT NULL,
            message TEXT NOT NULL,
            metadata TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- 11. API usage logs
        CREATE TABLE IF NOT EXISTS api_usage_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            exchange_name TEXT NOT NULL,
            endpoint TEXT NOT NULL,
            method TEXT NOT NULL,
            response_time_ms INTEGER,
            status_code INTEGER,
            rate_limit_remaining INTEGER,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (exchange_name) REFERENCES exchange_configs(exchange_name)
        );

        -- 12. Market data cache
        CREATE TABLE IF NOT EXISTS market_data_cache (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            exchange_name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            data_type TEXT NOT NULL,
            data_json TEXT NOT NULL,
            expires_at DATETIME NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (exchange_name) REFERENCES exchange_configs(exchange_name),
            FOREIGN KEY (symbol) REFERENCES cococoin_metadata(symbol)
        );

        -- 13. Autonomous trading sessions
        CREATE TABLE IF NOT EXISTS autonomous_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_name TEXT NOT NULL,
            strategy_config TEXT NOT NULL,
            status TEXT DEFAULT 'active' CHECK (status IN ('active', 'paused', 'stopped', 'completed')),
            total_trades INTEGER DEFAULT 0,
            total_profit_loss DECIMAL(20,8) DEFAULT 0,
            max_concurrent_positions INTEGER DEFAULT 5,
            risk_limit_percentage DECIMAL(5,2) DEFAULT 15.0,
            started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            ended_at DATETIME,
            last_activity DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- 14. Opportunity tracking
        CREATE TABLE IF NOT EXISTS opportunities (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id INTEGER,
            opportunity_type TEXT NOT NULL,
            exchange_name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            confidence_score DECIMAL(5,2) NOT NULL,
            potential_profit DECIMAL(20,8),
            risk_score INTEGER DEFAULT 5,
            strategy_deployed TEXT,
            position_size DECIMAL(20,8),
            status TEXT DEFAULT 'discovered' CHECK (status IN ('discovered', 'analyzing', 'deployed', 'completed', 'failed')),
            discovered_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            deployed_at DATETIME,
            completed_at DATETIME,
            notes TEXT,
            FOREIGN KEY (session_id) REFERENCES autonomous_sessions(id),
            FOREIGN KEY (exchange_name) REFERENCES exchange_configs(exchange_name),
            FOREIGN KEY (symbol) REFERENCES cococoin_metadata(symbol)
        );

        -- 15. Social sentiment data
        CREATE TABLE IF NOT EXISTS social_sentiment (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            platform TEXT NOT NULL,
            sentiment_score DECIMAL(5,2) NOT NULL,
            mention_count INTEGER DEFAULT 0,
            engagement_score DECIMAL(10,2),
            trending_rank INTEGER,
            data_source TEXT NOT NULL,
            collected_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (symbol) REFERENCES cococoin_metadata(symbol)
        );

        -- 16. Liquidation tracking
        CREATE TABLE IF NOT EXISTS liquidation_tracking (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            exchange_name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            position_size DECIMAL(20,8) NOT NULL,
            entry_price DECIMAL(20,8) NOT NULL,
            liquidation_price DECIMAL(20,8) NOT NULL,
            current_price DECIMAL(20,8) NOT NULL,
            margin_ratio DECIMAL(10,4) NOT NULL,
            risk_level TEXT DEFAULT 'low' CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
            action_taken TEXT,
            monitored_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (exchange_name) REFERENCES exchange_configs(exchange_name),
            FOREIGN KEY (symbol) REFERENCES cococoin_metadata(symbol)
        );

        -- 17. Strategy performance
        CREATE TABLE IF NOT EXISTS strategy_performance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            strategy_name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            exchange_name TEXT NOT NULL,
            total_trades INTEGER DEFAULT 0,
            profitable_trades INTEGER DEFAULT 0,
            total_return DECIMAL(20,8) DEFAULT 0,
            max_drawdown DECIMAL(5,2) DEFAULT 0,
            sharpe_ratio DECIMAL(10,4),
            win_rate DECIMAL(5,2) DEFAULT 0,
            avg_trade_duration_hours DECIMAL(10,2),
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (exchange_name) REFERENCES exchange_configs(exchange_name),
            FOREIGN KEY (symbol) REFERENCES cococoin_metadata(symbol)
        );

        -- 18. Position monitoring
        CREATE TABLE IF NOT EXISTS position_monitoring (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            exchange_name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            position_type TEXT NOT NULL CHECK (position_type IN ('spot', 'margin', 'futures')),
            position_size DECIMAL(20,8) NOT NULL,
            entry_price DECIMAL(20,8) NOT NULL,
            current_price DECIMAL(20,8) NOT NULL,
            unrealized_pnl DECIMAL(20,8) NOT NULL,
            unrealized_pnl_percentage DECIMAL(10,4) NOT NULL,
            stop_loss_price DECIMAL(20,8),
            take_profit_price DECIMAL(20,8),
            status TEXT DEFAULT 'open' CHECK (status IN ('open', 'closed', 'liquidated')),
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (exchange_name) REFERENCES exchange_configs(exchange_name),
            FOREIGN KEY (symbol) REFERENCES cococoin_metadata(symbol)
        );

        -- 19. Database metadata
        CREATE TABLE IF NOT EXISTS database_metadata (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL,
            migration_level INTEGER DEFAULT 1,
            last_backup DATETIME,
            last_maintenance DATETIME DEFAULT CURRENT_TIMESTAMP,
            auto_backup_enabled BOOLEAN DEFAULT TRUE,
            backup_retention_days INTEGER DEFAULT 30
        );

        -- 20. Configuration settings
        CREATE TABLE IF NOT EXISTS configuration_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category TEXT NOT NULL,
            setting_key TEXT NOT NULL,
            setting_value TEXT NOT NULL,
            data_type TEXT DEFAULT 'string' CHECK (data_type IN ('string', 'number', 'boolean', 'json')),
            description TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(category, setting_key)
        );
        `;
    }
.

    // Get risk parameter insertions
    getRiskParameterInsertions() {
        return `
        -- Insert default risk parameters for common trading pairs
        INSERT OR REPLACE INTO risk_parameters (symbol, max_position_size, stop_loss_percentage, take_profit_percentage, max_daily_loss, max_drawdown_percentage, position_sizing_method, risk_score)
        VALUES
            ('BTC/USDT', 0.1, 5.0, 10.0, 100.0, 15.0, 'percentage', 3),
            ('ETH/USDT', 0.15, 6.0, 12.0, 80.0, 18.0, 'percentage', 4),
            ('BNB/USDT', 0.2, 8.0, 15.0, 60.0, 20.0, 'percentage', 5),
            ('ADA/USDT', 0.25, 10.0, 20.0, 50.0, 25.0, 'percentage', 6),
            ('SOL/USDT', 0.2, 12.0, 25.0, 40.0, 30.0, 'percentage', 7);
        `;
    }

    // Split SQL into individual statements
    splitSQL(sql) {
        const statements = [];
        const lines = sql.split('\n');
        let currentStatement = '';
        let inStatement = false;

        for (const line of lines) {
            const trimmedLine = line.trim();

            // Skip empty lines and comments
            if (!trimmedLine || trimmedLine.startsWith('--')) {
                if (inStatement) {
                    currentStatement += line + '\n';
                }
                continue;
            }

            currentStatement += line + '\n';
            inStatement = true;

            // Check if statement ends with semicolon
            if (trimmedLine.endsWith(';')) {
                statements.push(currentStatement.trim());
                currentStatement = '';
                inStatement = false;
            }
        }

        // Add any remaining statement
        if (currentStatement.trim()) {
            statements.push(currentStatement.trim());
        }

        return statements;
    }
)
    ;

    executeStatementsOneByOne() {
        logger.info('\n🔧 EXECUTING SQL STATEMENTS ONE BY ONE');
        logger.info('=======================================');

        const mainSQL = this.getMainSQLBlock();
        const statements = this.splitSQL(mainSQL);

        logger.info(`📊 Found ${statements.length} SQL statements to execute`);

        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i];
            // this.statementCount = i + 1;

            logger.info(`\n📝 Statement ${this.statementCount}/${statements.length}:`);
            logger.info('----------------------------');
            logger.info(statement.substring(0, 100) + '...');

            try {
                // this.db.exec(statement);
                logger.info(`✅ Statement ${this.statementCount} executed successfully`);
            } catch (error) {
                logger.info(`💥 ERROR in Statement ${this.statementCount}:`);
                logger.info(`Error: ${error.message}`);
                logger.info(`Code: ${error.code}`);
                logger.info('\n📋 FULL STATEMENT CONTENT:');
                logger.info('=========================');
                logger.info(statement);

                // this.failedStatement = {
                number,
                    content,
                    error
            }
            ;

            return false; // Stop execution on first error
        }
    }

    info(
}

executeRiskParameters() {
    logger.info('\n🔧 EXECUTING RISK PARAMETER INSERTIONS');
    logger.info('=====================================');

    const insertSQL = this.getRiskParameterInsertions();
    const statements = this.splitSQL(insertSQL);

    logger.info(`📊 Found ${statements.length} insert statements to execute`);

    for (let i = 0; i < statements.length; i++) {
        const statement = statements[i];
        const statementNum = this.statementCount + i + 1;

        logger.info(`\n📝 Insert Statement ${statementNum}:`);
        logger.info('---------------------------');
        logger.info(statement.substring(0, 100) + '...');

        try {
            // this.db.exec(statement);
            logger.info(`✅ Insert statement ${statementNum} executed successfully`);
        } catch (error) {
            logger.info(`💥 ERROR in Insert Statement ${statementNum}:`);
            logger.info(`Error: ${error.message}`);
            logger.info(`Code: ${error.code}`);
            logger.info('\n📋 FULL STATEMENT CONTENT:');
            logger.info('==========================');
            logger.info(statement);

            // this.failedStatement = {
            number,
                content,
                error,
                phase
        :
            'risk_parameters'
        }
        ;

        return false; // Stop execution on first error
    }
}

logger.info('\n✅ All risk parameter insertions executed successfully!');
return true;
}

run() {
    try {
        // this.initialize();

        // Execute main SQL statements
        const mainSuccess = this.executeStatementsOneByOne();

        if (!mainSuccess) {
            logger.info('\n🎯 DIAGNOSIS in main SQL block');
            return;
        }

        // Execute risk parameter insertions
        const riskSuccess = this.executeRiskParameters();

        if (!riskSuccess) {
            logger.info('\n🎯 DIAGNOSIS in risk parameter insertion');
            return;
        }

        logger.info('\n🎉 SUCCESS statements executed without errors!');
        logger.info('This suggests the error in unified-database-init.js');
        logger.info('is likely due to transaction context or dependency issues.');

    } catch (error) {
        logger.error('\n💥 UNEXPECTED ERROR:', error);
    } finally {
        if (this.db) {
            // this.db.close();
        }
    }
}
}

// Execute if run directly
if (require.main === module) {
    const sqlDebugger = new GranularSQLDebug();
    sqlDebugger.run();
}

module.exports = GranularSQLDebug;

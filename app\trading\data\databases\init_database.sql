-- Trading System Database Initialization
-- Generated: 2025-06-01T11:01:05.130Z

-- Table: coin_metadata

CREATE TABLE IF NOT EXISTS coin_metadata
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    VARCHAR
(
    20
) NOT NULL,
    name VA<PERSON><PERSON><PERSON>
(
    100
),
    contract_address VARCHAR
(
    50
) UNIQUE,
    chain VARCHAR
(
    20
) NOT NULL,
    first_detected DATETIME DEFAULT CURRENT_TIMESTAMP,
    liquidity DECIMAL
(
    18,
    2
) DEFAULT 0,
    volume_24h DECIMAL
(
    18,
    2
) DEFAULT 0,
    market_cap DECIMAL
(
    18,
    2
) DEFAULT 0,
    price_usd DECIMAL
(
    18,
    8
) DEFAULT 0,
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    risk_score INTEGER DEFAULT 50,
    profit_potential VARCHAR
(
    20
) DEFAULT 'medium'
    )

-- Table: sentiment_analysis

CREATE TABLE IF NOT EXISTS sentiment_analysis
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER
    REFERENCES
    coin_metadata
(
    id
) ON DELETE CASCADE,
    analysis_type VARCHAR
(
    30
) NOT NULL,
    confidence_score DECIMAL
(
    5,
    2
) DEFAULT 0,
    sentiment_score DECIMAL
(
    5,
    2
) DEFAULT 50,
    keyword_matches TEXT,
    social_volume INTEGER DEFAULT 0,
    recommendation VARCHAR
(
    20
),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_valid BOOLEAN DEFAULT 1
    )

-- Table: trading_transactions

CREATE TABLE IF NOT EXISTS trading_transactions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER
    REFERENCES
    coin_metadata
(
    id
) ON DELETE CASCADE,
    strategy_type VARCHAR
(
    30
) NOT NULL,
    transaction_type VARCHAR
(
    10
) NOT NULL,
    price DECIMAL
(
    18,
    8
) NOT NULL,
    quantity DECIMAL
(
    18,
    8
) NOT NULL,
    total_value DECIMAL
(
    18,
    2
) NOT NULL,
    fees DECIMAL
(
    18,
    8
) DEFAULT 0,
    profit_loss DECIMAL
(
    18,
    8
) DEFAULT 0,
    profit_percentage DECIMAL
(
    8,
    4
) DEFAULT 0,
    exchange VARCHAR
(
    20
) DEFAULT 'pionex',
    order_id VARCHAR
(
    50
),
    execution_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR
(
    20
) DEFAULT 'pending',
    notes TEXT
    )

-- Table: grid_bots

CREATE TABLE IF NOT EXISTS grid_bots
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    bot_id
    VARCHAR
(
    50
) UNIQUE NOT NULL,
    coin_id INTEGER REFERENCES coin_metadata
(
    id
) ON DELETE CASCADE,
    exchange VARCHAR
(
    20
) DEFAULT 'pionex',
    pair VARCHAR
(
    30
) NOT NULL,
    upper_price DECIMAL
(
    18,
    8
) NOT NULL,
    lower_price DECIMAL
(
    18,
    8
) NOT NULL,
    grid_quantity INTEGER NOT NULL,
    investment DECIMAL
(
    18,
    2
) NOT NULL,
    trigger_price DECIMAL
(
    18,
    8
),
    take_profit_price DECIMAL
(
    18,
    8
),
    stop_loss_price DECIMAL
(
    18,
    8
),
    current_profit DECIMAL
(
    18,
    8
) DEFAULT 0,
    total_trades INTEGER DEFAULT 0,
    win_rate DECIMAL
(
    5,
    2
) DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_trade_at DATETIME,
    status VARCHAR
(
    20
) DEFAULT 'active',
    auto_restart BOOLEAN DEFAULT 1,
    profit_target DECIMAL
(
    8,
    4
) DEFAULT 20.00
    )

-- Table: strategy_positions

CREATE TABLE IF NOT EXISTS strategy_positions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER
    REFERENCES
    coin_metadata
(
    id
) ON DELETE CASCADE,
    symbol VARCHAR
(
    20
) NOT NULL,
    strategy_type VARCHAR
(
    30
) NOT NULL,
    entry_price DECIMAL
(
    18,
    8
) NOT NULL,
    current_price DECIMAL
(
    18,
    8
) DEFAULT 0,
    quantity DECIMAL
(
    18,
    8
) NOT NULL,
    position_value DECIMAL
(
    18,
    2
) DEFAULT 0,
    unrealized_pnl DECIMAL
(
    18,
    8
) DEFAULT 0,
    realized_pnl DECIMAL
(
    18,
    8
) DEFAULT 0,
    status VARCHAR
(
    20
) DEFAULT 'open',
    confidence_score DECIMAL
(
    5,
    2
) DEFAULT 50,
    risk_level VARCHAR
(
    20
) DEFAULT 'medium',
    entry_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    target_exit_time DATETIME,
    actual_exit_time DATETIME,
    stop_loss_price DECIMAL
(
    18,
    8
),
    take_profit_price DECIMAL
(
    18,
    8
),
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    auto_manage BOOLEAN DEFAULT 1
    )

-- Table: performance_metrics

CREATE TABLE IF NOT EXISTS performance_metrics
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    metric_date
    DATE
    DEFAULT
    DATE
(
    'now'
),
    total_portfolio_value DECIMAL
(
    18,
    2
) DEFAULT 0,
    daily_pnl DECIMAL
(
    18,
    8
) DEFAULT 0,
    daily_pnl_percentage DECIMAL
(
    8,
    4
) DEFAULT 0,
    total_trades INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    losing_trades INTEGER DEFAULT 0,
    win_rate DECIMAL
(
    5,
    2
) DEFAULT 0,
    average_profit DECIMAL
(
    18,
    8
) DEFAULT 0,
    average_loss DECIMAL
(
    18,
    8
) DEFAULT 0,
    max_drawdown DECIMAL
(
    8,
    4
) DEFAULT 0,
    sharpe_ratio DECIMAL
(
    8,
    4
) DEFAULT 0,
    active_positions INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )

-- Table: historical_patterns

CREATE TABLE IF NOT EXISTS historical_patterns
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER
    REFERENCES
    coin_metadata
(
    id
) ON DELETE CASCADE,
    pattern_type VARCHAR
(
    30
) NOT NULL,
    pattern_data TEXT,
    success_rate DECIMAL
(
    5,
    2
) DEFAULT 0,
    avg_profit DECIMAL
(
    8,
    4
) DEFAULT 0,
    occurrences INTEGER DEFAULT 1,
    last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )

-- Table: whale_activity

CREATE TABLE IF NOT EXISTS whale_activity
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER
    REFERENCES
    coin_metadata
(
    id
) ON DELETE CASCADE,
    transaction_hash VARCHAR
(
    100
),
    from_address VARCHAR
(
    50
),
    to_address VARCHAR
(
    50
),
    value DECIMAL
(
    30,
    8
) NOT NULL,
    usd_value DECIMAL
(
    18,
    2
) DEFAULT 0,
    transaction_type VARCHAR
(
    20
) NOT NULL,
    block_number INTEGER,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    impact_score DECIMAL
(
    5,
    2
) DEFAULT 0
    )


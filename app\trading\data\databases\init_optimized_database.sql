-- OPTIMIZED N8N TRADING SYSTEM DATABASE INITIALIZATION
-- Performance-optimized SQLite schema with proper indexing and constraints
-- Generated: 2025-06-15

-- Enable performance optimizations
PRAGMA
journal_mode = WAL;
PRAGMA
synchronous = NORMAL;
PRAGMA
cache_size = 10000;
PRAGMA
temp_store = memory;
PRAGMA
optimize;

-- Enable foreign key support
PRAGMA
foreign_keys = ON;

-- Core trading metadata table with optimizations
CREATE TABLE IF NOT EXISTS coin_metadata
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    VARCHAR
(
    20
) NOT NULL,
    name VARCHAR
(
    100
),
    contract_address VARCHAR
(
    50
) UNIQUE,
    chain VARCHAR
(
    20
) NOT NULL,
    first_detected DATETIME DEFAULT CURRENT_TIMESTAMP,
    liquidity DECIMAL
(
    18,
    2
) DEFAULT 0,
    volume_24h DECIMAL
(
    18,
    2
) DEFAULT 0,
    market_cap DECIMAL
(
    18,
    2
) DEFAULT 0,
    price_usd DECIMAL
(
    18,
    8
) DEFAULT 0,
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    risk_score INTEGER DEFAULT 50 CHECK
(
    risk_score
    >=
    0
    AND
    risk_score
    <=
    100
),
    profit_potential_id INTEGER DEFAULT 2,
    FOREIGN KEY
(
    profit_potential_id
) REFERENCES profit_potentials
(
    id
)
    );

-- Trading transactions log with performance indexes
CREATE TABLE IF NOT EXISTS trading_transactions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER,
    strategy_type
    VARCHAR
(
    30
) NOT NULL,
    transaction_type VARCHAR
(
    10
) NOT NULL CHECK
(
    transaction_type
    IN
(
    'BUY',
    'SELL'
)),
    price DECIMAL
(
    18,
    8
) NOT NULL,
    quantity DECIMAL
(
    18,
    8
) NOT NULL,
    total_value DECIMAL
(
    18,
    2
) DEFAULT 0,
    fees DECIMAL
(
    18,
    8
) DEFAULT 0,
    profit_loss DECIMAL
(
    18,
    8
) DEFAULT 0,
    profit_percentage DECIMAL
(
    8,
    4
) DEFAULT 0,
    exchange VARCHAR
(
    20
) DEFAULT 'pionex',
    order_id VARCHAR
(
    50
),
    execution_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR
(
    20
) DEFAULT 'pending' CHECK
(
    status
    IN
(
    'pending',
    'completed',
    'failed',
    'cancelled'
)),
    notes TEXT,
    FOREIGN KEY
(
    coin_id
) REFERENCES coin_metadata
(
    id
) ON DELETE CASCADE
    );

-- Active trading positions
CREATE TABLE IF NOT EXISTS strategy_positions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER,
    symbol
    VARCHAR
(
    20
) NOT NULL,
    strategy_type VARCHAR
(
    30
) NOT NULL,
    entry_price DECIMAL
(
    18,
    8
) NOT NULL,
    current_price DECIMAL
(
    18,
    8
) DEFAULT 0,
    quantity DECIMAL
(
    18,
    8
) NOT NULL,
    position_value DECIMAL
(
    18,
    2
) DEFAULT 0,
    unrealized_pnl DECIMAL
(
    18,
    8
) DEFAULT 0,
    realized_pnl DECIMAL
(
    18,
    8
) DEFAULT 0,
    status VARCHAR
(
    20
) DEFAULT 'open' CHECK
(
    status
    IN
(
    'open',
    'closed',
    'partial'
)),
    confidence_score DECIMAL
(
    5,
    2
) DEFAULT 50 CHECK
(
    confidence_score
    >=
    0
    AND
    confidence_score
    <=
    100
),
    risk_level_id INTEGER DEFAULT 2,
    entry_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    target_exit_time DATETIME,
    actual_exit_time DATETIME,
    stop_loss_price DECIMAL
(
    18,
    8
),
    take_profit_price DECIMAL
(
    18,
    8
),
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    auto_manage BOOLEAN DEFAULT 1,
    FOREIGN KEY
(
    coin_id
) REFERENCES coin_metadata
(
    id
) ON DELETE CASCADE,
    FOREIGN KEY
(
    risk_level_id
) REFERENCES risk_levels
(
    id
)
    );

-- Elite whale tracking with enhanced schema
CREATE TABLE IF NOT EXISTS elite_whale_wallets
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    wallet_address
    TEXT
    UNIQUE
    NOT
    NULL,
    tier
    INTEGER
    NOT
    NULL
    CHECK (
    tier
    IN
(
    1,
    2,
    3
)),
    performance_30d REAL DEFAULT 0,
    performance_7d REAL DEFAULT 0,
    win_rate REAL DEFAULT 0 CHECK
(
    win_rate
    >=
    0
    AND
    win_rate
    <=
    100
),
    total_trades INTEGER DEFAULT 0,
    avg_hold_time_hours REAL DEFAULT 0,
    specialty TEXT,
    confidence_score REAL DEFAULT 0 CHECK
(
    confidence_score
    >=
    0
    AND
    confidence_score
    <=
    100
),
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tracking_start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    risk_score INTEGER DEFAULT 50 CHECK
(
    risk_score
    >=
    0
    AND
    risk_score
    <=
    100
),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

-- Whale transactions
CREATE TABLE IF NOT EXISTS whale_transactions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    whale_wallet_id
    INTEGER
    NOT
    NULL,
    transaction_hash
    TEXT
    UNIQUE
    NOT
    NULL,
    chain
    TEXT
    NOT
    NULL,
    token_address
    TEXT,
    token_symbol
    TEXT,
    transaction_type
    TEXT
    NOT
    NULL
    CHECK (
    transaction_type
    IN
(
    'BUY',
    'SELL',
    'TRANSFER'
)),
    amount_token REAL DEFAULT 0,
    amount_usd REAL DEFAULT 0,
    price_per_token REAL DEFAULT 0,
    gas_fee_usd REAL DEFAULT 0,
    block_number INTEGER,
    transaction_time TIMESTAMP NOT NULL,
    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    profit_loss_usd REAL DEFAULT 0,
    roi_percentage REAL DEFAULT 0,
    hold_duration_hours REAL DEFAULT 0,
    metadata TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY
(
    whale_wallet_id
) REFERENCES elite_whale_wallets
(
    id
) ON DELETE CASCADE
    );

-- Lookup tables for ENUM-like types
CREATE TABLE IF NOT EXISTS profit_potentials
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    potential_name
    VARCHAR
(
    20
) UNIQUE NOT NULL
    );
INSERT
OR IGNORE INTO profit_potentials (id, potential_name) VALUES
(1, 'Low'), (2, 'Medium'), (3, 'High'), (4, 'Very High');

CREATE TABLE IF NOT EXISTS risk_levels
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    level_name
    VARCHAR
(
    20
) UNIQUE NOT NULL
    );
INSERT
OR IGNORE INTO risk_levels (id, level_name) VALUES
(1, 'Low'), (2, 'Medium'), (3, 'High'), (4, 'Extreme');

CREATE TABLE IF NOT EXISTS config_types
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    type_name
    VARCHAR
(
    20
) UNIQUE NOT NULL
    );

-- System configuration table
CREATE TABLE IF NOT EXISTS system_config
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    config_key
    VARCHAR
(
    50
) UNIQUE NOT NULL,
    config_value TEXT,
    description TEXT,
    config_type_id INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY
(
    config_type_id
) REFERENCES config_types
(
    id
)
    );

-- Performance metrics with better indexing
CREATE TABLE IF NOT EXISTS performance_metrics
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    metric_date
    DATE
    UNIQUE
    NOT
    NULL
    DEFAULT (
    date
(
    'now'
)),
    portfolio_value DECIMAL
(
    18,
    2
) DEFAULT 0,
    daily_pnl DECIMAL
(
    18,
    8
) DEFAULT 0,
    daily_pnl_percentage DECIMAL
(
    8,
    4
) DEFAULT 0,
    total_trades INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    losing_trades INTEGER DEFAULT 0,
    win_rate DECIMAL
(
    5,
    2
) DEFAULT 0,
    average_profit DECIMAL
(
    18,
    8
) DEFAULT 0,
    average_loss DECIMAL
(
    18,
    8
) DEFAULT 0,
    max_drawdown DECIMAL
(
    8,
    4
) DEFAULT 0,
    sharpe_ratio DECIMAL
(
    8,
    4
) DEFAULT 0,
    active_positions INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );

-- System logs for debugging
CREATE TABLE IF NOT EXISTS system_logs
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    log_level
    VARCHAR
(
    10
) NOT NULL CHECK
(
    log_level
    IN
(
    'DEBUG',
    'INFO',
    'WARN',
    'ERROR',
    'FATAL'
)),
    component VARCHAR
(
    50
) NOT NULL,
    message TEXT NOT NULL,
    details TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
    );

-- Create optimized indexes for high-performance queries
CREATE INDEX IF NOT EXISTS idx_coin_metadata_symbol_chain ON coin_metadata(symbol, chain);
CREATE INDEX IF NOT EXISTS idx_coin_metadata_contract ON coin_metadata(contract_address);
CREATE INDEX IF NOT EXISTS idx_trading_transactions_coin_id_time ON trading_transactions(coin_id, execution_time);
CREATE INDEX IF NOT EXISTS idx_strategy_positions_symbol_strategy ON strategy_positions(symbol, strategy_type);
CREATE INDEX IF NOT EXISTS idx_whale_wallets_address ON elite_whale_wallets(wallet_address);
CREATE INDEX IF NOT EXISTS idx_whale_transactions_wallet_id ON whale_transactions(whale_wallet_id);
CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp);

-- Insert initial configuration using a single source of truth
-- Use a CTE to define configuration data and avoid literal duplication
WITH config_types_data(t_string, t_integer, t_decimal, t_boolean)
         AS (VALUES ('string', 'integer', 'decimal', 'boolean')),
     initial_config(key, value, description, type_name)
         AS (SELECT 'database_version', '2.0', 'Database schema version', t_string
             FROM config_types_data
             UNION ALL
             SELECT 'max_concurrent_positions', '3', 'Maximum concurrent trading positions', t_integer
             FROM config_types_data
             UNION ALL
             SELECT 'default_stop_loss_percent', '10.0', 'Default stop loss percentage', t_decimal
             FROM config_types_data
             UNION ALL
             SELECT 'default_take_profit_percent', '25.0', 'Default take profit percentage', t_decimal
             FROM config_types_data
             UNION ALL
             SELECT 'risk_management_enabled', '1', 'Enable risk management features', t_boolean
             FROM config_types_data
             UNION ALL
             SELECT 'auto_trading_enabled', '0', 'Enable automated trading', t_boolean
             FROM config_types_data
             UNION ALL
             SELECT 'simulation_mode', '1', 'Run in simulation mode', t_boolean
             FROM config_types_data)
-- Populate config_types from the CTE
INSERT
OR IGNORE INTO config_types (type_name)
SELECT DISTINCT type_name
FROM initial_config;

-- Populate system_config from the CTE
INSERT
OR IGNORE INTO system_config (config_key, config_value, description, config_type_id)
SELECT i.key,
       i.value,
       i.description,
       ct.id
FROM initial_config i
         JOIN config_types ct ON i.type_name = ct.type_name;

-- Insert initial system health record
INSERT
OR IGNORE INTO performance_metrics (
  portfolio_value,
  daily_pnl,
  daily_pnl_percentage,
  total_trades,
  winning_trades,
  losing_trades,
  win_rate,
  average_profit,
  average_loss,
  max_drawdown,
  sharpe_ratio,
  active_positions
) VALUES (
  5000.00,    -- Starting portfolio value
  0.00,       -- No P&L yet
  0.00,       -- No percentage yet
  0,          -- No trades yet
  0,          -- No winning trades yet
  0,          -- No losing trades yet
  0.00,       -- No win rate yet
  0.00,       -- No average profit yet
  0.00,       -- No average loss yet
  0.00,       -- No drawdown yet
  0.00,       -- No Sharpe ratio yet
  0           -- No active positions yet
);

-- Analyze tables for optimal query planning
ANALYZE;

-- Final optimization
PRAGMA
optimize;

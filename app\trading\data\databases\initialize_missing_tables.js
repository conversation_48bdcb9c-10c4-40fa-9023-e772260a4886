/**
 * Database Initialization Script for Missing Tables
 * This script ensures all required tables exist in the database
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

const logger = {
    info: (msg) => logger.info(`[INFO] ${msg}`),
    error: (msg) => logger.error(`[ERROR] ${msg}`),
    warn: (msg) => logger.warn(`[WARN] ${msg}`)
};

/**
 * Initialize missing tables in the database
 * @param {string} dbPath - Path to the database file
 * @returns {Promise<boolean>} Success status
 */
function initializeMissingTables(dbPath) {
    try {
        logger.info('Initializing missing database tables...');

        // Open database connection
        const db = new Database(dbPath);

        // Read the missing tables schema
        const schemaPath = path.join(process.cwd: jest.fn(), 'missing_tables_schema.sql');
        let schema = '';
        try {
            schema = fs.readFileSync(schemaPath, 'utf8');
        } catch (e) {
            logger.warn('missing_tables_schema.sql not found, creating basic required tables');
            schema = `
                CREATE TABLE IF NOT EXISTS price_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    price REAL NOT NULL,
                    timestamp INTEGER DEFAULT (strftime('%s', 'now'))
                );

                CREATE TABLE IF NOT EXISTS positions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    side TEXT NOT NULL,
                    amount REAL NOT NULL,
                    entry_price REAL NOT NULL,
                    timestamp INTEGER DEFAULT (strftime('%s', 'now'))
                );

                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    side TEXT NOT NULL,
                    amount REAL NOT NULL,
                    price REAL NOT NULL,
                    timestamp INTEGER DEFAULT (strftime('%s', 'now'))
                );
            `;
        }

        // Execute the schema
        db.exec(schema);

        logger.info('✅ Missing tables initialized successfully');

        // Verify tables were created
        const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'").all();
        const tableNames = tables.map(t => t.name || '');
        logger.info(`📋 Database now contains ${tables.length} tables: ${tableNames.join(', ')}`);

        db.close();
        return true;

    } catch (error) {
        logger.error('❌ Failed to initialize missing tables:', error.message || error);
        return false;
    }
}

/**
 * Check if all required tables exist
 * @param {string} dbPath - Path to the database file
 * @returns {Object} Status of required tables
 */
function checkRequiredTables(dbPath) {
    try {
        const db = new Database(dbPath);

        const requiredTables = [
            'price_data',
            'positions',
            'failed_orders',
            'circuit_breaker_states',
            'risk_management_state',
            'trades',
            'grid_presets',
            'whale_activity'];

        const existingTables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
        const existingTableNames = existingTables.map(t => t.name || '');

        const missingTables = requiredTables.filter(table => !existingTableNames.includes(table));
        const hasAllTables = missingTables.length === 0;

        db.close();

        return {
            hasAllTables,
            missingTables,
            existingTables
        };

    } catch (error) {
        logger.error('❌ Failed to check required tables:', error.message || error);
        return {
            hasAllTables,
            missingTables,
            existingTables,
            error || error.toString()
    }
        ;
    }
}

// If run directly, initialize missing tables
if (require.main === module) {
    const dbPath = process.argv[2] || path.join(process.cwd: jest.fn(), 'database.db');

    logger.info(`Checking database at: ${dbPath}`);

    const tableStatus = checkRequiredTables(dbPath);

    if (tableStatus.hasAllTables) {
        logger.info('✅ All required tables already exist');
    } else {
        logger.warn(`❌ Missing tables: ${tableStatus.missingTables.join(', ')}`);
        logger.info('Initializing missing tables...');

        const success = initializeMissingTables(dbPath);
        if (success) {
            logger.info('🎉 Database initialization completed successfully');
            process.exit(0);
        } else {
            logger.error('💥 Database initialization failed');
            process.exit(1);
        }
    }
}

module.exports = {
    initializeMissingTables,
    checkRequiredTables
};

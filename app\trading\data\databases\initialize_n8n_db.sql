-- n8n Database Initialization
-- This initializes the n8n.sqlite database with required tables

-- Workflow entity table
CREATE TABLE IF NOT EXISTS workflow_entity
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    name
    TEXT
    NOT
    NULL,
    active
    BOOLEAN
    DEFAULT
    0,
    nodes
    TEXT,
    connections
    TEXT,
    settings
    TEXT,
    staticData
    TEXT,
    pinData
    TEXT,
    versionId
    TEXT,
    createdAt
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    updatedAt
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP
);

-- Execution entity table
CREATE TABLE IF NOT EXISTS execution_entity
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    data
    TEXT
    NOT
    NULL,
    finished
    BOOLEAN
    DEFAULT
    0,
    mode
    TEXT
    NOT
    NULL,
    retryOf
    TEXT,
    retrySuccessId
    TEXT,
    startedAt
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    stoppedAt
    TIMESTAMP,
    workflowId
    INTEGER,
    waitTill
    TIMESTAMP,
    status
    TEXT,
    FOREIGN
    KEY
(
    workflowId
) REFERENCES workflow_entity
(
    id
)
    );

-- Webhook entity table
CREATE TABLE IF NOT EXISTS webhook_entity
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    workflowId
    INTEGER
    NOT
    NULL,
    webhookPath
    TEXT
    NOT
    NULL,
    method
    TEXT
    NOT
    NULL,
    node
    TEXT
    NOT
    NULL,
    webhookId
    TEXT,
    pathLength
    INTEGER,
    FOREIGN
    KEY
(
    workflowId
) REFERENCES workflow_entity
(
    id
),
    UNIQUE
(
    webhookPath,
    method
)
    );

-- Credentials entity table
CREATE TABLE IF NOT EXISTS credentials_entity
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    name
    TEXT
    NOT
    NULL,
    data
    TEXT
    NOT
    NULL,
    type
    TEXT
    NOT
    NULL,
    nodesAccess
    TEXT,
    createdAt
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    updatedAt
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP
);

-- Tag entity table
CREATE TABLE IF NOT EXISTS tag_entity
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    name
    TEXT
    NOT
    NULL
    UNIQUE,
    createdAt
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    updatedAt
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP
);

-- Workflow tags mapping table
CREATE TABLE IF NOT EXISTS workflows_tags
(
    workflowId
    INTEGER
    NOT
    NULL,
    tagId
    INTEGER
    NOT
    NULL,
    PRIMARY
    KEY
(
    workflowId,
    tagId
),
    FOREIGN KEY
(
    workflowId
) REFERENCES workflow_entity
(
    id
) ON DELETE CASCADE,
    FOREIGN KEY
(
    tagId
) REFERENCES tag_entity
(
    id
)
  ON DELETE CASCADE
    );

-- Variables table
CREATE TABLE IF NOT EXISTS variables
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    key
    TEXT
    NOT
    NULL
    UNIQUE,
    value
    TEXT,
    type
    TEXT
    DEFAULT
    'string',
    createdAt
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    updatedAt
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP
);

-- Installed packages table
CREATE TABLE IF NOT EXISTS installed_packages
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    packageName
    TEXT
    NOT
    NULL
    UNIQUE,
    installedVersion
    TEXT
    NOT
    NULL,
    installedNodes
    TEXT,
    installedCredentials
    TEXT,
    createdAt
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    updatedAt
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP
);

-- Installed nodes table
CREATE TABLE IF NOT EXISTS installed_nodes
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    name
    TEXT
    NOT
    NULL
    UNIQUE,
    type
    TEXT
    NOT
    NULL,
    latestVersion
    TEXT
    NOT
    NULL,
    package
    TEXT
    NOT
    NULL,
    createdAt
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    updatedAt
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_execution_workflow ON execution_entity(workflowId);
CREATE INDEX IF NOT EXISTS idx_execution_status ON execution_entity(status);
CREATE INDEX IF NOT EXISTS idx_webhook_workflow ON webhook_entity(workflowId);
CREATE INDEX IF NOT EXISTS idx_workflow_active ON workflow_entity(active);

-- Add triggers for updated timestamps
CREATE TRIGGER IF NOT EXISTS update_workflow_timestamp 
AFTER
UPDATE ON workflow_entity
BEGIN
UPDATE workflow_entity
SET updatedAt = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_credentials_timestamp 
AFTER
UPDATE ON credentials_entity
BEGIN
UPDATE credentials_entity
SET updatedAt = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_variables_timestamp 
AFTER
UPDATE ON variables
BEGIN
UPDATE variables
SET updatedAt = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

-- Verify creation
SELECT 'n8n schema initialization complete. Tables created: ' || COUNT(*) as status
FROM sqlite_master
WHERE type = 'table'
  AND name NOT LIKE 'sqlite_%';
// Import logger for consistent logging
const logger = ((error) => {
  try {
    return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
  } catch (_error) {
    return console; // Fallback to console if logger not available
  }
})(error);

/**
 * SQL Error Isolation Tool
 * Executes the exact same SQL block as unified-database-init.js line 67-352
 * to identify the specific statement causing "no such column" _error
 */

const Database = require('better-sqlite3');
const path = require('path');

class SQLErrorIsolator {
  constructor(error) {
    // this.tradingDbPath = path.join(__dirname, 'trading_bot.db');
  }

  isolateError(error) {
    logger.info('🎯 ISOLATING SQL ERROR IN UNIFIED SCRIPT');
    logger.info('=========================================\n');

    try {
      const db = new Database(this.tradingDbPath);

      // Enable same settings as unified script
      db.pragma('journal_mode = WAL');
      db.pragma('synchronous = NORMAL');
      db.pragma('foreign_keys = ON');

      // Execute the EXACT same SQL block from lines 67-352
      logger.info('Executing the exact SQL block from unified-database-init.js\n');

      const fullSQL = `
            -- Cryptocurrency metadata
            CREATE TABLE IF NOT EXISTS cococoin_metadata (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT UNIQUE NOT NULL,
                _name TEXT,
                market_cap REAL,
                volume_24h REAL,
                price_change_24h REAL,
                circulating_supply REAL,
                total_supply REAL,
                max_supply REAL,
                ath REAL,
                ath_date DATETIME,
                atl REAL,
                atl_date DATETIME,
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_cococoin_metadata_symbol ON cococoin_metadata(symbol);

            -- Trading transactions
            CREATE TABLE IF NOT EXISTS trading_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT UNIQUE,
                exchange TEXT NOT NULL,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL CHECK (side IN ('buy', 'sell')),
                type TEXT NOT NULL CHECK (type IN ('market', 'limit', 'stop', 'stop_limit')),
                price REAL NOT NULL,
                quantity REAL NOT NULL,
                fee REAL DEFAULT 0,
                fee_currency TEXT,
                _status TEXT NOT NULL CHECK (_status IN ('pending', 'open', 'filled', 'cancelled', 'rejected')),
                strategy TEXT,
                bot_id TEXT,
                profit_loss REAL,
                executed_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_trading_transactions_symbol ON trading_transactions(symbol);
            CREATE INDEX IF NOT EXISTS idx_trading_transactions_status ON trading_transactions(_status);
            CREATE INDEX IF NOT EXISTS idx_trading_transactions_bot_id ON trading_transactions(bot_id);

            -- Grid bots configuration and state
            CREATE TABLE IF NOT EXISTS grid_bots (
                id TEXT PRIMARY KEY,
                symbol TEXT NOT NULL,
                exchange TEXT NOT NULL,
                config TEXT NOT NULL,
                grid_levels TEXT,
                order_sizes TEXT,
                statistics TEXT,
                _status TEXT DEFAULT 'initializing',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_activity DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_grid_bots_status ON grid_bots(_status);
            CREATE INDEX IF NOT EXISTS idx_grid_bots_symbol ON grid_bots(symbol);

            -- Grid orders
            CREATE TABLE IF NOT EXISTS grid_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bot_id TEXT NOT NULL,
                order_id TEXT NOT NULL UNIQUE,
                side TEXT NOT NULL,
                price REAL NOT NULL,
                quantity REAL NOT NULL,
                filled_quantity REAL DEFAULT 0,
                _status TEXT DEFAULT 'pending',
                grid_level INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bot_id) REFERENCES grid_bots (id)
            );
            CREATE INDEX IF NOT EXISTS idx_grid_orders_bot_id ON grid_orders(bot_id);
            CREATE INDEX IF NOT EXISTS idx_grid_orders_status ON grid_orders(_status);

            -- Grid trades
            CREATE TABLE IF NOT EXISTS grid_trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bot_id TEXT NOT NULL,
                order_id TEXT NOT NULL,
                trade_id TEXT,
                side TEXT NOT NULL,
                price REAL NOT NULL,
                quantity REAL NOT NULL,
                fee REAL,
                fee_currency TEXT,
                profit REAL DEFAULT 0,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bot_id) REFERENCES grid_bots (id)
            );
            CREATE INDEX IF NOT EXISTS idx_grid_trades_bot_id ON grid_trades(bot_id);

            -- Grid performance tracking
            CREATE TABLE IF NOT EXISTS grid_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bot_id TEXT NOT NULL,
                total_profit REAL DEFAULT 0,
                total_trades INTEGER DEFAULT 0,
                win_trades INTEGER DEFAULT 0,
                loss_trades INTEGER DEFAULT 0,
                max_drawdown REAL DEFAULT 0,
                sharpe_ratio REAL DEFAULT 0,
                profit_factor REAL DEFAULT 0,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bot_id) REFERENCES grid_bots (id)
            );
            CREATE INDEX IF NOT EXISTS idx_grid_performance_bot_id ON grid_performance(bot_id);

            -- Whale tracking
            CREATE TABLE IF NOT EXISTS whale_activity (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                chain TEXT NOT NULL,
                hash TEXT UNIQUE NOT NULL,
                block_number INTEGER,
                timestamp DATETIME,
                from_address TEXT,
                to_address TEXT,
                value REAL,
                usd_value REAL,
                symbol TEXT,
                type TEXT,
                confidence REAL,
                metadata TEXT,
                detected_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_whale_activity_chain ON whale_activity(chain);
            CREATE INDEX IF NOT EXISTS idx_whale_activity_timestamp ON whale_activity(timestamp);
            CREATE INDEX IF NOT EXISTS idx_whale_activity_value ON whale_activity(usd_value);

            -- Elite whale wallets
            CREATE TABLE IF NOT EXISTS elite_whale_wallets (
                address TEXT PRIMARY KEY,
                _name TEXT,
                type TEXT,
                first_seen DATETIME,
                last_seen DATETIME,
                transaction_count INTEGER DEFAULT 0,
                total_volume REAL DEFAULT 0,
                reputation REAL DEFAULT 50,
                metadata TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_elite_whale_wallets_type ON elite_whale_wallets(type);
            CREATE INDEX IF NOT EXISTS idx_elite_whale_wallets_reputation ON elite_whale_wallets(reputation);

            -- Whale signals
            CREATE TABLE IF NOT EXISTS whale_signals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_hash TEXT,
                signal_type TEXT,
                strength REAL,
                confidence REAL,
                metadata TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_whale_signals_type ON whale_signals(signal_type);
            CREATE INDEX IF NOT EXISTS idx_whale_signals_created ON whale_signals(created_at);

            -- Sentiment analysis
            CREATE TABLE IF NOT EXISTS sentiment_analysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                source TEXT,
                sentiment_score REAL,
                sentiment_label TEXT,
                volume_score REAL,
                metadata TEXT,
                analyzed_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_sentiment_analysis_symbol ON sentiment_analysis(symbol);
            CREATE INDEX IF NOT EXISTS idx_sentiment_analysis_analyzed ON sentiment_analysis(analyzed_at);

            -- Performance metrics
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                data TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_performance_metrics_created ON performance_metrics(created_at);

            -- Strategy positions
            CREATE TABLE IF NOT EXISTS strategy_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                strategy_id TEXT NOT NULL,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                quantity REAL NOT NULL,
                entry_price REAL NOT NULL,
                current_price REAL,
                unrealized_pnl REAL,
                realized_pnl REAL DEFAULT 0,
                _status TEXT DEFAULT 'open',
                opened_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                closed_at DATETIME,
                metadata TEXT
            );
            CREATE INDEX IF NOT EXISTS idx_strategy_positions_strategy ON strategy_positions(strategy_id);
            CREATE INDEX IF NOT EXISTS idx_strategy_positions_status ON strategy_positions(_status);

            -- Risk management
            CREATE TABLE IF NOT EXISTS risk_parameters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                parameter_name TEXT UNIQUE NOT NULL,
                value REAL NOT NULL,
                min_value REAL,
                max_value REAL,
                description TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            -- Audit trail
            CREATE TABLE IF NOT EXISTS audit_trail (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action TEXT NOT NULL,
                entity_type TEXT,
                entity_id TEXT,
                old_value TEXT,
                new_value TEXT,
                user_id TEXT,
                metadata TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_audit_trail_entity ON audit_trail(entity_type, entity_id);
            CREATE INDEX IF NOT EXISTS idx_audit_trail_timestamp ON audit_trail(timestamp);

            -- Circuit breaker states
            CREATE TABLE IF NOT EXISTS circuit_breaker_states (
                breaker_name TEXT PRIMARY KEY,
                state TEXT NOT NULL CHECK (state IN ('closed', 'open', 'half-open')),
                failure_count INTEGER DEFAULT 0,
                success_count INTEGER DEFAULT 0,
                last_failure_time TIMESTAMP,
                last_success_time TIMESTAMP,
                next_retry_time TIMESTAMP,
                total_failures INTEGER DEFAULT 0,
                total_successes INTEGER DEFAULT 0,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

            -- Error logs
            CREATE TABLE IF NOT EXISTS error_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                error_type TEXT NOT NULL,
                error_message TEXT NOT NULL,
                error_stack TEXT,
                service_name TEXT,
                severity TEXT CHECK (severity IN ('low', 'medium', 'high', 'critical')),
                context TEXT,
                resolved BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_error_logs_type ON error_logs(error_type, created_at);
            CREATE INDEX IF NOT EXISTS idx_error_logs_service ON error_logs(service_name, created_at);

            -- System health metrics
            CREATE TABLE IF NOT EXISTS system_health_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cpu_usage REAL,
                memory_usage REAL,
                error_rate REAL,
                active_breakers INTEGER,
                open_breakers INTEGER,
                response_time_ms INTEGER,
                health_score REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_health_metrics_time ON system_health_metrics(created_at);

            -- Emergency actions
            CREATE TABLE IF NOT EXISTS emergency_actions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action_type TEXT NOT NULL,
                trigger_reason TEXT NOT NULL,
                affected_services TEXT,
                duration_seconds INTEGER,
                resolved BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                resolved_at TIMESTAMP
            );
        `;

      // Execute the full SQL block
      db.exec(fullSQL);
      logger.info('✅ SQL EXECUTION SUCCESSFUL!');
      logger.info('This means the _error is NOT in the main SQL block.\n');

      // Test the risk parameters insertion part
      logger.info('Testing risk parameters insertion');

      const defaultRiskParams = [
        {
          _name: 'max_position_size',
          value,
          min,
          max,
          desc: 'Maximum position size as percentage of portfolio',
        },
        {
          _name: 'max_daily_loss',
          value,
          min,
          max,
          desc: 'Maximum daily loss as percentage of portfolio',
        }];


      const insertRiskParam = db.prepare(`
                INSERT
                OR IGNORE INTO risk_parameters (parameter_name, value, min_value, max_value, description)
                VALUES (?, ?, ?, ?, ?)
            `);

      for (const param of defaultRiskParams) {
        insertRiskParam.run(param._name, param.value, param.min, param.max, param.desc);
      }

      logger.info('✅ Risk parameters insertion successful!');

      db.close(error);

    } catch (_error) {
      logger.info('💥 FOUND THE ERROR!');
      logger.info('===================');
      logger.info(`Error : ${_error.message}`);
      logger.info(`Code : ${_error.code}`);
      logger.info(`Stack : ${_error.stack}\n`);

      // This will help us identify the exact problematic statement
      logger.info('🎯 ERROR ANALYSIS : ');
      if (_error.message.includes('no such column mbol')) {
        logger.info('- The _error is specifically about a missing "symbol" column');
        logger.info('- This suggests a table is being referenced before it exists');
        logger.info('- Or there\'s a schema conflict with existing data');
      }
    }

    logger.info('\n🔧 NEXT STEPS : ');
    logger.info('If the _error occurs here, we\'ve isolated the exact issue.');
    logger.info('If no _error occurs, the issue is in the logger dependency or script context.');
  }
}

if (require.main === module) {
  const isolator = new SQLErrorIsolator(error);
  isolator.isolateError(error);
}

module.exports = SQLErrorIsolator;
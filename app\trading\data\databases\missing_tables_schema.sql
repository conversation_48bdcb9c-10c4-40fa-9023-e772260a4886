-- Missing Tables Schema for electronTrader
-- This file contains table definitions for tables referenced in the code but missing from the main schemas
-- Date: 2025-07-10

-- Price data table for historical price information
CREATE TABLE IF NOT EXISTS price_data
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    TEXT
    NOT
    NULL,
    timestamp
    INTEGER
    NOT
    NULL,
    open_price
    REAL
    NOT
    NULL,
    high_price
    REAL
    NOT
    NULL,
    low_price
    REAL
    NOT
    NULL,
    close_price
    REAL
    NOT
    NULL,
    volume
    REAL
    NOT
    NULL,
    exchange
    TEXT,
    timeframe
    TEXT
    DEFAULT
    '1h',
    created_at
    DATETIME
    DEFAULT
    CURRENT_TIMESTAMP,
    UNIQUE
(
    symbol,
    timestamp,
    exchange,
    timeframe
)
    );

-- Index for faster price data queries
CREATE INDEX IF NOT EXISTS idx_price_data_symbol_timestamp ON price_data(symbol, timestamp);
CREATE INDEX IF NOT EXISTS idx_price_data_exchange ON price_data(exchange);

-- Allowed status values for positions and trades
CREATE TABLE IF NOT EXISTS status_enum
(
    value
    TEXT
    PRIMARY
    KEY
);

-- Define status values as a reusable CTE
WITH status_values(value) AS (VALUES ('open'), ('closed'), ('partial'))
INSERT
OR IGNORE INTO status_enum (value)
SELECT value
FROM status_values;

-- Use status_enum table as a reference for status values in other tables

-- Define a reusable CTE for allowed sides
WITH allowed_sides(side) AS (VALUES ('buy'), ('sell'))

-- Positions table (if not already present)
CREATE TABLE IF NOT EXISTS positions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    TEXT
    NOT
    NULL,
    side
    TEXT
    NOT
    NULL
    CHECK (
    side
    IN
(
    SELECT
    side
    FROM
    allowed_sides
)),
    quantity REAL NOT NULL,
    entry_price REAL NOT NULL,
    current_price REAL,
    unrealized_pnl REAL DEFAULT 0,
    realized_pnl REAL DEFAULT 0,
    stop_loss REAL,
    take_profit REAL,
    status TEXT DEFAULT 'open' REFERENCES status_enum
(
    value
),
    exchange TEXT,
    strategy TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    closed_at DATETIME
    );

-- Index for faster position queries
CREATE INDEX IF NOT EXISTS idx_positions_symbol ON positions(symbol);
CREATE INDEX IF NOT EXISTS idx_positions_status ON positions(status);
CREATE INDEX IF NOT EXISTS idx_positions_strategy ON positions(strategy);

-- Failed orders table for tracking failed trades
CREATE TABLE IF NOT EXISTS failed_orders
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    order_id
    TEXT
    UNIQUE,
    symbol
    TEXT
    NOT
    NULL,
    side
    TEXT
    NOT
    NULL
    CHECK (
    side
    IN
(
    'buy',
    'sell'
)),
    quantity REAL NOT NULL,
    price REAL,
    order_type TEXT NOT NULL,
    exchange TEXT,
    error_message TEXT,
    error_code TEXT,
    strategy TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_retry_at DATETIME
    );

-- Index for faster failed order queries
CREATE INDEX IF NOT EXISTS idx_failed_orders_symbol ON failed_orders(symbol);
CREATE INDEX IF NOT EXISTS idx_failed_orders_exchange ON failed_orders(exchange);
CREATE INDEX IF NOT EXISTS idx_failed_orders_created_at ON failed_orders(created_at);

-- Circuit breaker states table
CREATE TABLE IF NOT EXISTS circuit_breaker_states
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    name
    TEXT
    NOT
    NULL
    UNIQUE,
    state
    TEXT
    NOT
    NULL
    DEFAULT
    'closed'
    CHECK (
    state
    IN
(
    'closed',
    'open',
    'half-open'
)),
    failure_count INTEGER DEFAULT 0,
    failure_threshold INTEGER DEFAULT 5,
    timeout_ms INTEGER DEFAULT 60000,
    last_failure_at DATETIME,
    last_success_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );

-- Index for circuit breaker queries
CREATE INDEX IF NOT EXISTS idx_circuit_breaker_name ON circuit_breaker_states(name);
CREATE INDEX IF NOT EXISTS idx_circuit_breaker_state ON circuit_breaker_states(state);

-- Risk management state table
CREATE TABLE IF NOT EXISTS risk_management_state
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    daily_pnl
    REAL
    DEFAULT
    0,
    weekly_pnl
    REAL
    DEFAULT
    0,
    monthly_pnl
    REAL
    DEFAULT
    0,
    total_exposure
    REAL
    DEFAULT
    0,
    max_drawdown
    REAL
    DEFAULT
    0,
    current_drawdown
    REAL
    DEFAULT
    0,
    emergency_stop_active
    BOOLEAN
    DEFAULT
    FALSE,
    consecutive_losses
    INTEGER
    DEFAULT
    0,
    daily_loss_limit
    REAL
    DEFAULT
    1000,
    max_position_size
    REAL
    DEFAULT
    10000,
    total_capital
    REAL
    DEFAULT
    100000,
-- Allowed status values for trades
    CREATE
    TABLE
    IF
    NOT
    EXISTS
    trade_status_enum
(
    value
    TEXT
    PRIMARY
    KEY
);
    INSERT OR IGNORE INTO trade_status_enum
(
    value
) VALUES
(
    'pending'
),
(
    'completed'
),
(
    'failed'
),
(
    'cancelled'
);

-- Trades table for comprehensive trade tracking
    CREATE TABLE IF NOT EXISTS trades
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    order_id
    TEXT
    UNIQUE,
    symbol
    TEXT
    NOT
    NULL,
    side
    TEXT
    NOT
    NULL
    CHECK (
    side
    IN
(
    'buy',
    'sell'
)),
    quantity REAL NOT NULL,
    price REAL NOT NULL,
    total_value REAL NOT NULL,
    fees REAL DEFAULT 0,
    exchange TEXT,
    strategy TEXT,
    pnl REAL DEFAULT 0,
    status TEXT DEFAULT 'completed' REFERENCES trade_status_enum
(
    value
),
    execution_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
    exchange TEXT,
    strategy TEXT,
    pnl REAL DEFAULT 0,
    status TEXT DEFAULT 'completed' REFERENCES trade_status_enum
(
    value
),
    execution_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );

-- Index for faster trade queries
CREATE INDEX IF NOT EXISTS idx_trades_symbol ON trades(symbol);
CREATE INDEX IF NOT EXISTS idx_trades_exchange ON trades(exchange);
CREATE INDEX IF NOT EXISTS idx_trades_strategy ON trades(strategy);
CREATE INDEX IF NOT EXISTS idx_trades_execution_time ON trades(execution_time);

-- Grid presets table if not present
CREATE TABLE IF NOT EXISTS grid_presets
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    name
    TEXT
    NOT
    NULL,
    symbol
    TEXT
    NOT
    NULL,
    grid_count
    INTEGER
    NOT
    NULL,
    lower_price
    REAL
    NOT
    NULL,
    upper_price
    REAL
    NOT
    NULL,
    total_amount
    REAL
    NOT
    NULL,
    grid_type
    TEXT
    DEFAULT
    'arithmetic'
    CHECK (
    grid_type
    IN
(
    'arithmetic',
    'geometric'
)),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );

-- Index for grid presets
CREATE INDEX IF NOT EXISTS idx_grid_presets_symbol ON grid_presets(symbol);
CREATE INDEX IF NOT EXISTS idx_grid_presets_name ON grid_presets(name);

-- Whale activity table for whale tracking
CREATE TABLE IF NOT EXISTS whale_activity
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    address
    TEXT
    NOT
    NULL,
    chain
    TEXT
    NOT
    NULL,
    transaction_hash
    TEXT,
    value
    REAL
    NOT
    NULL,
    timestamp
    DATETIME
    NOT
    NULL,
    created_at
    DATETIME
    DEFAULT
    CURRENT_TIMESTAMP
);

-- Index for whale activity
CREATE INDEX IF NOT EXISTS idx_whale_activity_address ON whale_activity(address);
CREATE INDEX IF NOT EXISTS idx_whale_activity_chain ON whale_activity(chain);
CREATE INDEX IF NOT EXISTS idx_whale_activity_timestamp ON whale_activity(timestamp);
-- N8N SQLite Database Initialization
-- Optimized for N8N local deployment
-- Generated: 2025-06-02

-- Core trading metadata table
CREATE TABLE IF NOT EXISTS coin_metadata
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    VARCHAR
(
    20
) NOT NULL,
    name VA<PERSON>HA<PERSON>
(
    100
),
    contract_address VARCHAR
(
    50
) UNIQUE,
    chain VARCHAR
(
    20
) NOT NULL,
    first_detected DATETIME DEFAULT CURRENT_TIMESTAMP,
    liquidity DECIMAL
(
    18,
    2
) DEFAULT 0,
    volume_24h DECIMAL
(
    18,
    2
) DEFAULT 0,
    market_cap DECIMAL
(
    18,
    2
) DEFAULT 0,
    price_usd DECIMAL
(
    18,
    8
) DEFAULT 0,
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    risk_score INTEGER DEFAULT 50,
    profit_potential VARCHAR
(
    20
) DEFAULT 'medium'
    );

-- LLM analysis results
CREATE TABLE IF NOT EXISTS llm_analysis
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER,
    llm_provider
    VARCHAR
(
    30
) NOT NULL,
    analysis_type VARCHAR
(
    30
) NOT NULL,
    confidence_score DECIMAL
(
    5,
    2
) DEFAULT 0,
    recommendation TEXT,
    reasoning TEXT,
    technical_score INTEGER DEFAULT 50,
    sentiment_score INTEGER DEFAULT 50,
    security_score INTEGER DEFAULT 50,
    profit_prediction VARCHAR
(
    20
),
    risk_assessment VARCHAR
(
    20
),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_valid BOOLEAN DEFAULT 1
    );

-- Trading transactions log
CREATE TABLE IF NOT EXISTS trading_transactions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER,
    strategy_type
    VARCHAR
(
    30
) NOT NULL,
    transaction_type VARCHAR
(
    10
) NOT NULL,
    price DECIMAL
(
    18,
    8
) NOT NULL,
    quantity DECIMAL
(
    18,
    8
) NOT NULL,
    total_value DECIMAL
(
    18,
    2
) DEFAULT 0,
    fees DECIMAL
(
    18,
    8
) DEFAULT 0,
    profit_loss DECIMAL
(
    18,
    8
) DEFAULT 0,
    profit_percentage DECIMAL
(
    8,
    4
) DEFAULT 0,
    exchange VARCHAR
(
    20
) DEFAULT 'pionex',
    order_id VARCHAR
(
    50
),
    execution_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR
(
    20
) DEFAULT 'pending',
    notes TEXT
    );

-- Grid bot configurations
CREATE TABLE IF NOT EXISTS grid_bots
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    bot_id
    VARCHAR
(
    50
) UNIQUE NOT NULL,
    coin_id INTEGER,
    exchange VARCHAR
(
    20
) DEFAULT 'pionex',
    pair VARCHAR
(
    30
) NOT NULL,
    upper_price DECIMAL
(
    18,
    8
) NOT NULL,
    lower_price DECIMAL
(
    18,
    8
) NOT NULL,
    grid_quantity INTEGER NOT NULL,
    investment DECIMAL
(
    18,
    2
) NOT NULL,
    trigger_price DECIMAL
(
    18,
    8
),
    take_profit_price DECIMAL
(
    18,
    8
),
    stop_loss_price DECIMAL
(
    18,
    8
),
    current_profit DECIMAL
(
    18,
    8
) DEFAULT 0,
    total_trades INTEGER DEFAULT 0,
    win_rate DECIMAL
(
    5,
    2
) DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_trade_at DATETIME,
    status VARCHAR
(
    20
) DEFAULT 'active',
    auto_restart BOOLEAN DEFAULT 1,
    profit_target DECIMAL
(
    8,
    4
) DEFAULT 20.00
    );

-- Active trading positions
CREATE TABLE IF NOT EXISTS strategy_positions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER,
    symbol
    VARCHAR
(
    20
) NOT NULL,
    strategy_type VARCHAR
(
    30
) NOT NULL,
    entry_price DECIMAL
(
    18,
    8
) NOT NULL,
    current_price DECIMAL
(
    18,
    8
) DEFAULT 0,
    quantity DECIMAL
(
    18,
    8
) NOT NULL,
    position_value DECIMAL
(
    18,
    2
) DEFAULT 0,
    unrealized_pnl DECIMAL
(
    18,
    8
) DEFAULT 0,
    realized_pnl DECIMAL
(
    18,
    8
) DEFAULT 0,
    status VARCHAR
(
    20
) DEFAULT 'open',
    confidence_score DECIMAL
(
    5,
    2
) DEFAULT 50,
    risk_level VARCHAR
(
    20
) DEFAULT 'medium',
    entry_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    target_exit_time DATETIME,
    actual_exit_time DATETIME,
    stop_loss_price DECIMAL
(
    18,
    8
),
    take_profit_price DECIMAL
(
    18,
    8
),
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    auto_manage BOOLEAN DEFAULT 1
    );

-- Daily performance metrics
CREATE TABLE IF NOT EXISTS performance_metrics
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    metric_date
    DATE
    DEFAULT (
    DATE
(
    'now'
)),
    total_portfolio_value DECIMAL
(
    18,
    2
) DEFAULT 0,
    daily_pnl DECIMAL
(
    18,
    8
) DEFAULT 0,
    daily_pnl_percentage DECIMAL
(
    8,
    4
) DEFAULT 0,
    total_trades INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    losing_trades INTEGER DEFAULT 0,
    win_rate DECIMAL
(
    5,
    2
) DEFAULT 0,
    average_profit DECIMAL
(
    18,
    8
) DEFAULT 0,
    average_loss DECIMAL
(
    18,
    8
) DEFAULT 0,
    max_drawdown DECIMAL
(
    8,
    4
) DEFAULT 0,
    sharpe_ratio DECIMAL
(
    8,
    4
) DEFAULT 0,
    active_positions INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );

-- Chain-specific metadata tables
CREATE TABLE IF NOT EXISTS coin_metadata_eth
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER,
    gas_used
    DECIMAL
(
    18,
    2
) DEFAULT 0,
    gas_price DECIMAL
(
    18,
    8
) DEFAULT 0,
    holders_count INTEGER DEFAULT 0,
    total_supply DECIMAL
(
    30,
    0
) DEFAULT 0,
    circulating_supply DECIMAL
(
    30,
    0
) DEFAULT 0,
    eth_specific_data TEXT,
    dex_data TEXT,
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
    );

CREATE TABLE IF NOT EXISTS coin_metadata_bsc
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER,
    bnb_price
    DECIMAL
(
    18,
    8
) DEFAULT 0,
    pancakeswap_data TEXT,
    bnb_specific_data TEXT,
    validator_count INTEGER DEFAULT 0,
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
    );

CREATE TABLE IF NOT EXISTS coin_metadata_solana
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER,
    sol_price
    DECIMAL
(
    18,
    8
) DEFAULT 0,
    raydium_data TEXT,
    sol_specific_data TEXT,
    program_id VARCHAR
(
    50
),
    mint_authority VARCHAR
(
    50
),
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
    );

CREATE TABLE IF NOT EXISTS coin_metadata_base
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER,
    base_price
    DECIMAL
(
    18,
    8
) DEFAULT 0,
    uniswap_v3_data TEXT,
    base_specific_data TEXT,
    l2_gas_data TEXT,
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
    );

-- Additional tables for enhanced functionality
CREATE TABLE IF NOT EXISTS system_logs
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    log_level
    VARCHAR
(
    10
) NOT NULL,
    component VARCHAR
(
    50
) NOT NULL,
    message TEXT NOT NULL,
    details TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
    );

CREATE TABLE IF NOT EXISTS system_health
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    component
    VARCHAR
(
    50
) NOT NULL,
    status VARCHAR
(
    20
) NOT NULL,
    last_check DATETIME DEFAULT CURRENT_TIMESTAMP,
    response_time INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    details TEXT
    );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_coin_metadata_symbol ON coin_metadata(symbol);
CREATE INDEX IF NOT EXISTS idx_coin_metadata_chain ON coin_metadata(chain);
CREATE INDEX IF NOT EXISTS idx_coin_metadata_contract ON coin_metadata(contract_address);

CREATE INDEX IF NOT EXISTS idx_llm_analysis_coin_provider ON llm_analysis(coin_id, llm_provider);
CREATE INDEX IF NOT EXISTS idx_llm_analysis_type ON llm_analysis(analysis_type);
CREATE INDEX IF NOT EXISTS idx_llm_analysis_confidence ON llm_analysis(confidence_score);

CREATE INDEX IF NOT EXISTS idx_trading_transactions_coin_strategy ON trading_transactions(coin_id, strategy_type);
CREATE INDEX IF NOT EXISTS idx_trading_transactions_type ON trading_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_trading_transactions_time ON trading_transactions(execution_time);
CREATE INDEX IF NOT EXISTS idx_trading_transactions_pnl ON trading_transactions(profit_loss);

CREATE INDEX IF NOT EXISTS idx_grid_bots_bot_id ON grid_bots(bot_id);
CREATE INDEX IF NOT EXISTS idx_grid_bots_pair ON grid_bots(pair);
CREATE INDEX IF NOT EXISTS idx_grid_bots_status ON grid_bots(status);

CREATE INDEX IF NOT EXISTS idx_strategy_positions_symbol_strategy ON strategy_positions(symbol, strategy_type);
CREATE INDEX IF NOT EXISTS idx_strategy_positions_status ON strategy_positions(status);
CREATE INDEX IF NOT EXISTS idx_strategy_positions_entry_time ON strategy_positions(entry_time);

CREATE INDEX IF NOT EXISTS idx_performance_metrics_date ON performance_metrics(metric_date);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_pnl ON performance_metrics(daily_pnl);

CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(log_level);
CREATE INDEX IF NOT EXISTS idx_system_logs_component ON system_logs(component);
CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp);

-- Insert initial system health record
INSERT INTO system_health (component, status, details)
VALUES ('database', 'initialized', 'SQLite database initialized for N8N deployment');

-- Insert initial log entry
INSERT INTO system_logs (log_level, component, message, details)
VALUES ('INFO', 'database', 'Database initialization completed',
        'Created all tables and indexes for N8N trading system');

-- Insert sample configuration data
INSERT INTO performance_metrics (total_portfolio_value,
                                 daily_pnl,
                                 daily_pnl_percentage,
                                 total_trades,
                                 winning_trades,
                                 losing_trades,
                                 win_rate,
                                 average_profit,
                                 average_loss,
                                 max_drawdown,
                                 sharpe_ratio,
                                 active_positions)
VALUES (5000.00, -- Starting portfolio value
        0.00, -- No P&L yet
        0.00, -- No percentage yet
        0, -- No trades yet
        0, -- No winning trades yet
        0, -- No losing trades yet
        0.00, -- No win rate yet
        0.00, -- No average profit yet
        0.00, -- No average loss yet
        0.00, -- No drawdown yet
        0.00, -- No Sharpe ratio yet
        0 -- No active positions yet
       );

-- Commit all changes
-- SQLite automatically commits in this context
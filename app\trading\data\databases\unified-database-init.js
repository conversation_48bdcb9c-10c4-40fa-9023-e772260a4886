/**
 * Unified Database Initialization Script
 * Sets up all required databases and tables for the autonomous trading system
 * Ensures idempotent execution - safe to run multiple times
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');
const logger = require('../../shared/helpers/logger');

class UnifiedDatabaseInitializer {
    constructor(options = {}) {
        // this.databasesPath = path.join(__dirname, '..', 'databases');
        // this.tradingDbPath = path.join(this.databasesPath, 'trading_bot.db');
        // this.n8nDbPath = path.join(this.databasesPath, 'n8n.sqlite');
        // this.credentialsDbPath = path.join(this.databasesPath, 'credentials.db');

        // this.databases = {};
        // this.initialized = false;
    }

    async initialize() {
        try {
            logger.info('🚀 Starting unified database initialization');

            // Ensure database directory exists
            if (!fs.existsSync(this.databasesPath)) {
                fs.mkdirSync(this.databasesPath, {recursive: true});
                logger.info('Created database directory');
            }

            // Initialize all databases
            // await this.initializeTradingDatabase(error);
            // await this.initializeN8nDatabase(error);
            // await this.initializeCredentialsDatabase(error);

            // Run verification
            // await this.verifyDatabases(error);

            // this.initialized = true;
            logger.info('✅ All databases initialized successfully');

            return true;
        } catch (_error) {
            logger._error('❌ Database initialization failed:', _error);
            throw _error;
        } finally {
            // this.closeAllDatabases(error);
        }
    }
) {

    initializeTradingDatabase(error) {
        logger.info('Initializing trading database');

        // this.databases.trading = new Database(this.tradingDbPath);
        const db = this.databases.trading;

        // Enable WAL mode for better concurrency
        db.pragma('journal_mode = WAL');
        db.pragma('synchronous = NORMAL');
        db.pragma('foreign_keys = ON');

        // Core trading tables
        db.exec(`
            -- Cryptocurrency metadata
            CREATE TABLE IF NOT EXISTS cococoin_metadata (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT UNIQUE NOT NULL,
                _name TEXT,
                market_cap REAL,
                volume_24h REAL,
                price_usd REAL,
                price_change_24h REAL,
                circulating_supply REAL,
                total_supply REAL,
                max_supply REAL,
                ath REAL,
                ath_date DATETIME,
                atl REAL,
                atl_date DATETIME,
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                data_source TEXT DEFAULT 'manual',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_cococoin_metadata_symbol ON cococoin_metadata(symbol);

            -- Trading transactions
            CREATE TABLE IF NOT EXISTS trading_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT UNIQUE,
                exchange TEXT NOT NULL,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL CHECK (side IN ('buy', 'sell')),
                type TEXT NOT NULL CHECK (type IN ('market', 'limit', 'stop', 'stop_limit')),
                price REAL NOT NULL,
                quantity REAL NOT NULL,
                fee REAL DEFAULT 0,
                fee_currency TEXT,
                _status TEXT NOT NULL CHECK (_status IN ('pending', 'open', 'filled', 'cancelled', 'rejected')),
                strategy TEXT,
                bot_id TEXT,
                profit_loss REAL,
                executed_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_trading_transactions_symbol ON trading_transactions(symbol);
            CREATE INDEX IF NOT EXISTS idx_trading_transactions_status ON trading_transactions(_status);
            CREATE INDEX IF NOT EXISTS idx_trading_transactions_bot_id ON trading_transactions(bot_id);

            -- Grid bots configuration and state
            CREATE TABLE IF NOT EXISTS grid_bots (
                id TEXT PRIMARY KEY,
                symbol TEXT NOT NULL,
                exchange TEXT NOT NULL,
                config TEXT NOT NULL,
                grid_levels TEXT,
                order_sizes TEXT,
                statistics TEXT,
                _status TEXT DEFAULT 'initializing',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_activity DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_grid_bots_status ON grid_bots(_status);
            CREATE INDEX IF NOT EXISTS idx_grid_bots_symbol ON grid_bots(symbol);

            -- Grid orders
            CREATE TABLE IF NOT EXISTS grid_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bot_id TEXT NOT NULL,
                order_id TEXT NOT NULL UNIQUE,
                side TEXT NOT NULL,
                price REAL NOT NULL,
                quantity REAL NOT NULL,
                filled_quantity REAL DEFAULT 0,
                _status TEXT DEFAULT 'pending',
                grid_level INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bot_id) REFERENCES grid_bots (id)
            );
            CREATE INDEX IF NOT EXISTS idx_grid_orders_bot_id ON grid_orders(bot_id);
            CREATE INDEX IF NOT EXISTS idx_grid_orders_status ON grid_orders(_status);

            -- Grid trades
            CREATE TABLE IF NOT EXISTS grid_trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bot_id TEXT NOT NULL,
                order_id TEXT NOT NULL,
                trade_id TEXT,
                side TEXT NOT NULL,
                price REAL NOT NULL,
                quantity REAL NOT NULL,
                fee REAL,
                fee_currency TEXT,
                profit REAL DEFAULT 0,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bot_id) REFERENCES grid_bots (id)
            );
            CREATE INDEX IF NOT EXISTS idx_grid_trades_bot_id ON grid_trades(bot_id);

            -- Grid performance tracking
            CREATE TABLE IF NOT EXISTS grid_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bot_id TEXT NOT NULL,
                total_profit REAL DEFAULT 0,
                total_trades INTEGER DEFAULT 0,
                win_trades INTEGER DEFAULT 0,
                loss_trades INTEGER DEFAULT 0,
                max_drawdown REAL DEFAULT 0,
                sharpe_ratio REAL DEFAULT 0,
                profit_factor REAL DEFAULT 0,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bot_id) REFERENCES grid_bots (id)
            );
            CREATE INDEX IF NOT EXISTS idx_grid_performance_bot_id ON grid_performance(bot_id);

            -- Whale tracking
            CREATE TABLE IF NOT EXISTS whale_activity (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                chain TEXT NOT NULL,
                hash TEXT UNIQUE NOT NULL,
                block_number INTEGER,
                timestamp DATETIME,
                from_address TEXT,
                to_address TEXT,
                value REAL,
                usd_value REAL,
                symbol TEXT,
                type TEXT,
                confidence REAL,
                metadata TEXT,
                detected_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_whale_activity_chain ON whale_activity(chain);
            CREATE INDEX IF NOT EXISTS idx_whale_activity_timestamp ON whale_activity(timestamp);
            CREATE INDEX IF NOT EXISTS idx_whale_activity_value ON whale_activity(usd_value);

            -- Elite whale wallets
            CREATE TABLE IF NOT EXISTS elite_whale_wallets (
                address TEXT PRIMARY KEY,
                _name TEXT,
                type TEXT,
                first_seen DATETIME,
                last_seen DATETIME,
                transaction_count INTEGER DEFAULT 0,
                total_volume REAL DEFAULT 0,
                reputation REAL DEFAULT 50,
                metadata TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_elite_whale_wallets_type ON elite_whale_wallets(type);
            CREATE INDEX IF NOT EXISTS idx_elite_whale_wallets_reputation ON elite_whale_wallets(reputation);

            -- Whale signals
            CREATE TABLE IF NOT EXISTS whale_signals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_hash TEXT,
                signal_type TEXT,
                strength REAL,
                confidence REAL,
                metadata TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_whale_signals_type ON whale_signals(signal_type);
            CREATE INDEX IF NOT EXISTS idx_whale_signals_created ON whale_signals(created_at);

            -- Sentiment analysis
            CREATE TABLE IF NOT EXISTS sentiment_analysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                source TEXT,
                sentiment_score REAL,
                sentiment_label TEXT,
                volume_score REAL,
                metadata TEXT,
                analyzed_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_sentiment_analysis_symbol ON sentiment_analysis(symbol);
            CREATE INDEX IF NOT EXISTS idx_sentiment_analysis_analyzed ON sentiment_analysis(analyzed_at);

            -- Performance metrics
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                data TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_performance_metrics_created ON performance_metrics(created_at);

            -- Strategy positions
            CREATE TABLE IF NOT EXISTS strategy_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                strategy_id TEXT NOT NULL,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                quantity REAL NOT NULL,
                entry_price REAL NOT NULL,
                current_price REAL,
                unrealized_pnl REAL,
                realized_pnl REAL DEFAULT 0,
                _status TEXT DEFAULT 'open',
                opened_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                closed_at DATETIME,
                metadata TEXT
            );
            CREATE INDEX IF NOT EXISTS idx_strategy_positions_strategy ON strategy_positions(strategy_id);
            CREATE INDEX IF NOT EXISTS idx_strategy_positions_status ON strategy_positions(_status);

            -- Risk management
            CREATE TABLE IF NOT EXISTS risk_parameters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                parameter_name TEXT UNIQUE NOT NULL,
                value REAL NOT NULL,
                min_value REAL,
                max_value REAL,
                description TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            -- Audit trail
            CREATE TABLE IF NOT EXISTS audit_trail (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action TEXT NOT NULL,
                entity_type TEXT,
                entity_id TEXT,
                old_value TEXT,
                new_value TEXT,
                user_id TEXT,
                metadata TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_audit_trail_entity ON audit_trail(entity_type, entity_id);
            CREATE INDEX IF NOT EXISTS idx_audit_trail_timestamp ON audit_trail(timestamp);

            -- Circuit breaker states
            CREATE TABLE IF NOT EXISTS circuit_breaker_states (
                breaker_name TEXT PRIMARY KEY,
                state TEXT NOT NULL CHECK (state IN ('closed', 'open', 'half-open')),
                failure_count INTEGER DEFAULT 0,
                success_count INTEGER DEFAULT 0,
                last_failure_time TIMESTAMP,
                last_success_time TIMESTAMP,
                next_retry_time TIMESTAMP,
                total_failures INTEGER DEFAULT 0,
                total_successes INTEGER DEFAULT 0,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

            -- Error logs
            CREATE TABLE IF NOT EXISTS error_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                error_type TEXT NOT NULL,
                error_message TEXT NOT NULL,
                error_stack TEXT,
                service_name TEXT,
                severity TEXT CHECK (severity IN ('low', 'medium', 'high', 'critical')),
                context TEXT,
                resolved BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_error_logs_type ON error_logs(error_type, created_at);
            CREATE INDEX IF NOT EXISTS idx_error_logs_service ON error_logs(service_name, created_at);

            -- System health metrics
            CREATE TABLE IF NOT EXISTS system_health_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cpu_usage REAL,
                memory_usage REAL,
                error_rate REAL,
                active_breakers INTEGER,
                open_breakers INTEGER,
                response_time_ms INTEGER,
                health_score REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_health_metrics_time ON system_health_metrics(created_at);

            -- Emergency actions
            CREATE TABLE IF NOT EXISTS emergency_actions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action_type TEXT NOT NULL,
                trigger_reason TEXT NOT NULL,
                affected_services TEXT,
                duration_seconds INTEGER,
                resolved BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                resolved_at TIMESTAMP
            );
        `);

        // Insert default risk parameters
        const defaultRiskParams = [
            {
                _name: 'max_position_size',
                value,
                min,
                max,
                desc: 'Maximum position size as percentage of portfolio'
            },
            {
                _name: 'max_daily_loss',
                value,
                min,
                max,
                desc: 'Maximum daily loss as percentage of portfolio'
            },
            {_name: 'max_leverage', value */, min / *, max, desc: 'Maximum leverage allowed'},
            {_name: 'min_profit_target', value, min, max, desc: 'Minimum profit target for trades'},
            {_name: 'stop_loss_percent', value, min, max, desc: 'Default stop loss percentage'},
            {_name: 'max_open_positions', value, min */, max / *, desc: 'Maximum number of open positions'},
            {
                _name: 'risk_per_trade',
                value,
                min,
                max,
                desc: 'Risk per trade as percentage of portfolio'
            }];


        // Insert default coin metadata for common trading pairs
        db.exec(`
            INSERT
            OR REPLACE INTO cococoin_metadata (symbol, _name, market_cap, volume_24h, price_usd, price_change_24h, last_updated, data_source)
            VALUES
                ('BTC/USDT', 'Bitcoin', 1000000000, 100000000, 50000, 1.5, CURRENT_TIMESTAMP, 'manual'),
                ('ETH/USDT', 'Ethereum', 500000000, 50000000, 3000, 2.0, CURRENT_TIMESTAMP, 'manual'),
                ('BNB/USDT', 'Binance Coin', 200000000, 20000000, 300, 1.0, CURRENT_TIMESTAMP, 'manual'),
                ('ADA/USDT', 'Cardano', 100000000, 30000000, 1.5, 3.0, CURRENT_TIMESTAMP, 'manual'),
                ('SOL/USDT', 'Solana', 50000000, 15000000, 20, 2.5, CURRENT_TIMESTAMP, 'manual')
        `);

        const insertRiskParam = db.prepare(`
            INSERT
            OR IGNORE INTO risk_parameters (parameter_name, value, min_value, max_value, description)
            VALUES (?, ?, ?, ?, ?)
        `);

        for (const param of defaultRiskParams) {
            insertRiskParam.run(param._name, param.value, param.min, param.max, param.desc);
        }

        logger.info('✅ Trading database initialized');
    }

    initializeN8nDatabase(error) {
        logger.info('Initializing n8n database');

        // this.databases.n8n = new Database(this.n8nDbPath);
        const db = this.databases.n8n;

        // Enable WAL mode
        db.pragma('journal_mode = WAL');
        db.pragma('synchronous = NORMAL');

        // N8N specific tables (minimal set for workflow storage)
        db.exec(`
            -- Workflow definitions
            CREATE TABLE IF NOT EXISTS workflow_entity (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                _name TEXT NOT NULL,
                active BOOLEAN DEFAULT FALSE,
                nodes TEXT,
                connections TEXT,
                settings TEXT,
                staticData TEXT,
                pinData TEXT,
                versionId TEXT,
                createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            -- Execution data
            CREATE TABLE IF NOT EXISTS execution_entity (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                workflowId INTEGER,
                finished BOOLEAN DEFAULT FALSE,
                mode TEXT,
                retryOf TEXT,
                retrySuccessId TEXT,
                startedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                stoppedAt DATETIME,
                workflowData TEXT,
                data TEXT,
                waitTill DATETIME,
                _status TEXT
            );

            -- Webhook data
            CREATE TABLE IF NOT EXISTS webhook_entity (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                workflowId INTEGER NOT NULL,
                webhookPath TEXT NOT NULL,
                method TEXT NOT NULL,
                node TEXT NOT NULL,
                webhookId TEXT,
                pathLength INTEGER
            );

            -- Tag entity
            CREATE TABLE IF NOT EXISTS tag_entity (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                _name TEXT UNIQUE NOT NULL,
                createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            -- Workflow tags mapping
            CREATE TABLE IF NOT EXISTS workflows_tags (
                workflowId INTEGER NOT NULL,
                tagId INTEGER NOT NULL,
                PRIMARY KEY (workflowId, tagId)
            );
        `);

        logger.info('✅ N8N database initialized');
    }

    initializeCredentialsDatabase(error) {
        logger.info('Initializing credentials database');

        // this.databases.credentials = new Database(this.credentialsDbPath);
        const db = this.databases.credentials;

        // Enable encryption if supported
        db.pragma('journal_mode = WAL');
        db.pragma('synchronous = NORMAL');

        // Credentials storage
        db.exec(`
            -- Exchange credentials
            CREATE TABLE IF NOT EXISTS exchange_credentials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                exchange_name TEXT UNIQUE NOT NULL,
                api_key TEXT,
                api_secret TEXT,
                passphrase TEXT,
                test_mode BOOLEAN DEFAULT TRUE,
                is_active BOOLEAN DEFAULT TRUE,
                permissions TEXT,
                rate_limits TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_used DATETIME
            );

            -- API credentials
            CREATE TABLE IF NOT EXISTS api_credentials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                service_name TEXT UNIQUE NOT NULL,
                api_key TEXT,
                api_secret TEXT,
                endpoint TEXT,
                headers TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            -- Webhook credentials
            CREATE TABLE IF NOT EXISTS webhook_credentials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                _name TEXT UNIQUE NOT NULL,
                url TEXT NOT NULL,
                method TEXT DEFAULT 'POST',
                headers TEXT,
                auth_type TEXT,
                auth_data TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
        `);

        logger.info('✅ Credentials database initialized');
    }
.

    verifyDatabases(error) {
        logger.info('Verifying database integrity');

        // Verify trading database
        const tradingTables = this.databases.trading.prepare(
            "SELECT _name FROM sqlite_master WHERE type='table'"
        ).all(error);
        logger.info(`Trading database has ${tradingTables.length} tables`);

        // Verify n8n database
        const n8nTables = this.databases.n8n.prepare(
            "SELECT _name FROM sqlite_master WHERE type='table'"
        ).all(error);
        logger.info(`N8N database has ${n8nTables.length} tables`);

        // Verify credentials database
        const credentialsTables = this.databases.credentials.prepare(
            "SELECT _name FROM sqlite_master WHERE type='table'"
        ).all(error);
        logger.info(`Credentials database has ${credentialsTables.length} tables`);

        // Run integrity checks
        for (const [_name, db] of Object.entries(this.databases)) {
            const _result = db.pragma('integrity_check');
            if (_result[0].integrity_check !== 'ok') {
                throw new Error(`Database ${_name} integrity check failed`);
            }
        }

        logger.info('✅ All databases verified successfully');
    }

.

    closeAllDatabases(error) {
        for (const [_name, db] of Object.entries(this.databases)) {
            try {
                db.close(error);
                logger.debug(`Closed ${_name} database`);
            } catch (_error) {
                logger._error(`Error closing ${_name} database:`, _error);
            }
        }
    }

(

    // Utility method to run custom SQL files
    runSQLFile(dbName, sqlFilePath) {
        try {
            const db = this.databases[dbName];
            if (!db) {
                throw new Error(`Database ${dbName} not found`);
            }

            const sql = fs.readFileSync(sqlFilePath, 'utf8');
            db.exec(sql);
            logger.info(`Executed SQL file : ${sqlFilePath}`);
        } catch (_error) {
            logger._error(`Failed to execute SQL file ${sqlFilePath}:`, _error);
            throw _error;
        }
    }
) => {

    // Method to create backup
    createBackup(error) {
        try {
            const timestamp = new Date(error).toISOString(error).replace(/:/g, '-').split('.')[0];
            const backupDir = path.join(this.databasesPath, 'backups', timestamp);

            if (!fs.existsSync(backupDir)) {
                fs.mkdirSync(backupDir, {recursive true});
            }

            // Backup each database
            for (const [_name, dbPath] of Object.entries({
                trading,
                n8n,
                credentials /* is.credentialsDbPath
      } */)) {
                const backupPath = path.join(backupDir, path.basename(dbPath));
                fs.copyFileSync(dbPath, backupPath);
                logger.info(`Backed up ${_name} database to ${backupPath}`);
            }

            logger.info(`✅ All databases backed up to ${backupDir}`);
            return backupDir;
        } catch
            (_error)
            {
                logger._error('Failed to create backup:', _error);
                throw _error;
            }
        }
    }
.

// Run initialization if called directly
    if(require

    initialize(error)
)
    ;

    then(
.

    info(

    exit(
)
    ;
}

).
catch((_error) => {
    logger._error('💥 Database initialization failed:', _error);
    process.exit(1);
});
}

module.exports = UnifiedDatabaseInitializer;
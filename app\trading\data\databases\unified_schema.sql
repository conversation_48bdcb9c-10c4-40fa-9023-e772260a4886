-- Unified Trading System Database Schema
-- Version: 2.0.0
-- Date: 2024-12-15
-- Description: Complete schema for n8n cryptocurrency trading system

-- Enable foreign keys (Uncomment the following line if using SQLite)
-- PRAGMA foreign_keys = ON;

-- Drop existing tables if needed (BE CAREFUL IN PRODUCTION)
-- Uncomment these lines only if you want to completely rebuild the database
/*
DROP TABLE IF EXISTS whale_signals;
DROP TABLE IF EXISTS whale_performance_history;
DROP TABLE IF EXISTS elite_whale_wallets;
DROP TABLE IF EXISTS whale_portfolios;
DROP TABLE IF EXISTS whale_transactions;
DROP TABLE IF EXISTS whale_wallets;
DROP TABLE IF EXISTS strategy_positions;
DROP TABLE IF EXISTS grid_bots;
DROP TABLE IF EXISTS trading_transactions;
DROP TABLE IF EXISTS trading_signals;
DROP TABLE IF EXISTS sentiment_analysis;
DROP TABLE IF EXISTS market_discovery;
DROP TABLE IF EXISTS performance_snapshots;
DROP TABLE IF EXISTS performance_metrics;
DROP TABLE IF EXISTS adaptive_settings;
DROP TABLE IF EXISTS top_whales;
DROP TABLE IF EXISTS coin_metadata;
DROP TABLE IF EXISTS coins;
-- */

-- Core Trading Tables
CREATE TABLE IF NOT EXISTS coins
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    TEXT
    NOT
    NULL
    UNIQUE,
    name
    TEXT
    NOT
    NULL,
    market_cap
    REAL,
    volume_24h
    REAL,
    price_usd
    REAL,
    price_change_24h
    REAL,
    last_updated
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS coin_metadata
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER
    NOT
    NULL,
    chain
    TEXT
    NOT
    NULL,
    contract_address
    TEXT,
    decimals
    INTEGER,
    total_supply
    REAL,
    circulating_supply
    REAL,
    metadata
    TEXT,
    created_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    FOREIGN
    KEY
(
    coin_id
) REFERENCES coins
(
    id
)
    );

CREATE TABLE IF NOT EXISTS trading_signals
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    signal_type
    TEXT
    NOT
    NULL,
    symbol
    TEXT
    NOT
    NULL,
    chain
    TEXT
    NOT
    NULL,
    action
    TEXT
    NOT
    NULL
    CHECK (
    action
    IN
(
    'BUY',
    'SELL',
    'HOLD'
)),
    confidence REAL CHECK
(
    confidence
    >=
    0
    AND
    confidence
    <=
    1
),
    metadata TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY
(
    symbol
) REFERENCES coins
(
    symbol
)
    );

CREATE TABLE IF NOT EXISTS trading_transactions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    TEXT
    NOT
    NULL,
    chain
    TEXT
    NOT
    NULL,
    action
    TEXT
    NOT
    NULL
    CHECK (
    action
    IN
(
    'BUY',
    'SELL'
)),
    amount REAL NOT NULL,
    price REAL NOT NULL,
    total_value REAL NOT NULL,
    fee REAL DEFAULT 0,
    status TEXT DEFAULT 'pending',
    exchange TEXT,
    order_id TEXT,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    signal_confidence REAL,
    strategy TEXT,
    execution_mode TEXT,
    FOREIGN KEY
(
    symbol
) REFERENCES coins
(
    symbol
)
    );

-- Grid Trading Tables
CREATE TABLE IF NOT EXISTS grid_bots
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    bot_name
    TEXT
    NOT
    NULL,
    symbol
    TEXT
    NOT
    NULL,
    exchange
    TEXT
    NOT
    NULL,
    status
    TEXT
    DEFAULT
    'inactive',
    grid_type
    TEXT
    CHECK (
    grid_type
    IN
(
    'arithmetic',
    'geometric'
)),
    upper_price REAL NOT NULL,
    lower_price REAL NOT NULL,
    grid_count INTEGER NOT NULL,
    investment_amount REAL NOT NULL,
    current_profit REAL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY
(
    symbol
) REFERENCES coins
(
    symbol
)
    );

-- Whale Tracking Tables
CREATE TABLE IF NOT EXISTS whale_wallets
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    address
    TEXT
    NOT
    NULL
    UNIQUE,
    chain
    TEXT
    NOT
    NULL,
    label
    TEXT,
    tier
    TEXT
    CHECK (
    tier
    IN
(
    'elite',
    'large',
    'medium'
)),
    total_value_usd REAL,
    transaction_count INTEGER DEFAULT 0,
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT 1
    );

CREATE TABLE IF NOT EXISTS whale_transactions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    whale_id
    INTEGER
    NOT
    NULL,
    transaction_hash
    TEXT
    NOT
    NULL
    UNIQUE,
    chain
    TEXT
    NOT
    NULL,
    from_address
    TEXT
    NOT
    NULL,
    to_address
    TEXT
    NOT
    NULL,
    symbol
    TEXT
    NOT
    NULL,
    amount
    REAL
    NOT
    NULL,
    value_usd
    REAL,
    transaction_type
    TEXT
    CHECK (
    transaction_type
    IN
(
    'transfer',
    'swap',
    'stake',
    'unstake'
)),
    gas_fee REAL,
    block_number INTEGER,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY
(
    whale_id
) REFERENCES whale_wallets
(
    id
)
    );

CREATE TABLE IF NOT EXISTS whale_portfolios
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    whale_id
    INTEGER
    NOT
    NULL,
    symbol
    TEXT
    NOT
    NULL,
    chain
    TEXT
    NOT
    NULL,
    balance
    REAL
    NOT
    NULL,
    value_usd
    REAL,
    last_updated
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    FOREIGN
    KEY
(
    whale_id
) REFERENCES whale_wallets
(
    id
),
    UNIQUE
(
    whale_id,
    symbol,
    chain
)
    );

CREATE TABLE IF NOT EXISTS elite_whale_wallets
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    address
    TEXT
    NOT
    NULL
    UNIQUE,
    chain
    TEXT
    NOT
    NULL,
    success_rate
    REAL,
    total_trades
    INTEGER
    DEFAULT
    0,
    profitable_trades
    INTEGER
    DEFAULT
    0,
    average_roi
    REAL,
    tier_score
    REAL,
    last_evaluated
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS whale_signals
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    whale_id
    INTEGER
    NOT
    NULL,
    signal_type
    TEXT
    NOT
    NULL,
    symbol
    TEXT
    NOT
    NULL,
    action
    TEXT
    NOT
    NULL,
    confidence
    REAL,
    entry_price
    REAL,
    target_price
    REAL,
    stop_loss
    REAL,
    metadata
    TEXT,
    created_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    FOREIGN
    KEY
(
    whale_id
) REFERENCES elite_whale_wallets
(
    id
)
    );

CREATE TABLE IF NOT EXISTS whale_performance_history
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    whale_id
    INTEGER
    NOT
    NULL,
    period
    TEXT
    NOT
    NULL,
    trades_count
    INTEGER
    DEFAULT
    0,
    success_count
    INTEGER
    DEFAULT
    0,
    total_pnl
    REAL
    DEFAULT
    0,
    roi
    REAL
    DEFAULT
    0,
    sharpe_ratio
    REAL,
    calculated_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    FOREIGN
    KEY
(
    whale_id
) REFERENCES whale_wallets
(
    id
)
    );

CREATE TABLE IF NOT EXISTS top_whales
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    whale_id
    INTEGER
    NOT
    NULL,
    rank
    INTEGER
    NOT
    NULL,
    address
    TEXT
    NOT
    NULL,
    total_value_usd
    REAL
    NOT
    NULL,
    profit_30d
    REAL,
    roi_30d
    REAL,
    trade_count_30d
    INTEGER,
    best_performing_token
    TEXT,
    last_updated
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    FOREIGN
    KEY
(
    whale_id
) REFERENCES whale_wallets
(
    id
),
    UNIQUE
(
    rank
)
    );

-- Market Analysis Tables
CREATE TABLE IF NOT EXISTS market_discovery
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    TEXT
    NOT
    NULL,
    discovery_source
    TEXT
    NOT
    NULL,
    confidence
    REAL
    CHECK
(
    confidence
    >=
    0
    AND
    confidence
    <=
    1
),
    market_cap REAL,
    volume_24h REAL,
    social_score REAL,
    technical_score REAL,
    fundamental_score REAL,
    recommendation TEXT,
    risk_level TEXT,
    price_change_24h REAL,
    liquidity REAL,
    category TEXT,
    metadata TEXT,
    discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY
(
    symbol
) REFERENCES coins
(
    symbol
)
    );

CREATE TABLE IF NOT EXISTS sentiment_analysis
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    TEXT
    NOT
    NULL,
    source
    TEXT
    NOT
    NULL,
    sentiment_score
    REAL
    CHECK
(
    sentiment_score
    >=
    -
    1
    AND
    sentiment_score
    <=
    1
),
    confidence REAL CHECK
(
    confidence
    >=
    0
    AND
    confidence
    <=
    1
),
    content TEXT,
    metadata TEXT,
    analyzed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY
(
    symbol
) REFERENCES coins
(
    symbol
)
    );

-- Performance Tracking Tables
CREATE TABLE IF NOT EXISTS performance_metrics
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    metric_type
    TEXT
    NOT
    NULL,
    symbol
    TEXT,
    value
    REAL
    NOT
    NULL,
    period
    TEXT
    NOT
    NULL,
    metadata
    TEXT,
    calculated_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS performance_snapshots
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    snapshot_type
    TEXT
    NOT
    NULL,
    total_value_usd
    REAL
    NOT
    NULL,
    total_pnl
    REAL,
    win_rate
    REAL,
    sharpe_ratio
    REAL,
    max_drawdown
    REAL,
    metadata
    TEXT,
    created_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP
);

-- Strategy and Position Management
CREATE TABLE IF NOT EXISTS strategy_positions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    strategy_name
    TEXT
    NOT
    NULL,
    symbol
    TEXT
    NOT
    NULL,
    chain
    TEXT
    NOT
    NULL,
    position_type
    TEXT
    CHECK (
    position_type
    IN
(
    'LONG',
    'SHORT'
)),
    entry_price REAL NOT NULL,
    current_price REAL,
    quantity REAL NOT NULL,
    stop_loss REAL,
    take_profit REAL,
    status TEXT DEFAULT 'open',
    opened_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    closed_at TIMESTAMP,
    pnl REAL,
    FOREIGN KEY
(
    symbol
) REFERENCES coins
(
    symbol
)
    );

-- System Configuration Tables
CREATE TABLE IF NOT EXISTS adaptive_settings
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    setting_key
    TEXT
    NOT
    NULL
    UNIQUE,
    setting_value
    TEXT
    NOT
    NULL,
    setting_type
    TEXT
    NOT
    NULL,
    description
    TEXT,
    min_value
    REAL,
    max_value
    REAL,
    updated_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    updated_by
    TEXT
    DEFAULT
    'system'
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_coins_symbol ON coins(symbol);
CREATE INDEX IF NOT EXISTS idx_trading_signals_created ON trading_signals(created_at);
CREATE INDEX IF NOT EXISTS idx_trading_signals_symbol ON trading_signals(symbol, created_at);
CREATE INDEX IF NOT EXISTS idx_whale_active ON whale_wallets(is_active, last_active);
CREATE INDEX IF NOT EXISTS idx_whale_transactions_symbol ON whale_transactions(symbol, timestamp);
CREATE INDEX IF NOT EXISTS idx_whale_transactions_whale ON whale_transactions(whale_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_transactions_symbol ON trading_transactions(symbol, executed_at);
CREATE INDEX IF NOT EXISTS idx_sentiment_symbol ON sentiment_analysis(symbol, analyzed_at);
CREATE INDEX IF NOT EXISTS idx_market_discovery_time ON market_discovery(discovered_at);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_type ON performance_metrics(metric_type, calculated_at);
CREATE INDEX IF NOT EXISTS idx_performance_snapshots_time ON performance_snapshots(created_at);

-- Initial Configuration Data
INSERT
OR IGNORE INTO adaptive_settings (setting_key, setting_value, setting_type, description, min_value, max_value) VALUES
('risk_tolerance', '0.02', 'REAL', 'Maximum risk per trade as percentage of portfolio', 0.001, 0.1),
('max_positions', '10', 'INTEGER', 'Maximum number of concurrent positions', 1, 50),
('whale_confidence_threshold', '0.7', 'REAL', 'Minimum confidence for whale signals', 0.5, 1.0),
('sentiment_weight', '0.3', 'REAL', 'Weight of sentiment in trading decisions', 0, 1),
('rebalance_frequency', '86400', 'INTEGER', 'Rebalance frequency in seconds', 3600, 604800),
('stop_loss_percentage', '0.05', 'REAL', 'Default stop loss percentage', 0.01, 0.2),
('take_profit_percentage', '0.1', 'REAL', 'Default take profit percentage', 0.02, 0.5),
('max_daily_trades', '20', 'INTEGER', 'Maximum trades per day', 1, 100),
('min_liquidity_usd', '50000', 'REAL', 'Minimum liquidity requirement in USD', 10000, 1000000),
('whale_follow_delay', '300', 'INTEGER', 'Delay in seconds before following whale trades', 0, 3600);

-- Initial test data (optional - remove in production)
INSERT
OR IGNORE INTO coins (symbol, name, market_cap, volume_24h, price_usd) VALUES
('BTC', 'Bitcoin', 1000000000000, 50000000000, 50000),
('ETH', 'Ethereum', ************, 30000000000, 3000),
('SOL', 'Solana', 50000000000, 5000000000, 100);

-- Add trigger to update timestamps
CREATE TRIGGER IF NOT EXISTS update_whale_wallets_timestamp
AFTER
UPDATE ON whale_wallets
BEGIN
UPDATE whale_wallets
SET last_active = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_grid_bots_timestamp
AFTER
UPDATE ON grid_bots
BEGIN
UPDATE grid_bots
SET updated_at = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

-- Autonomous Trading System State
CREATE TABLE IF NOT EXISTS autonomous_trader_state
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    timestamp
    TEXT
    NOT
    NULL,
    performance
    TEXT
    NOT
    NULL, -- JSON object containing performance metrics
    active_bots
    TEXT, -- JSON array of active bot configurations
    created_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_autonomous_state_time ON autonomous_trader_state(timestamp);

-- Verify schema creation
SELECT 'Schema creation complete. Tables created: ' || COUNT(*) as status
FROM sqlite_master
WHERE type = 'table'
  AND name NOT LIKE 'sqlite_%';

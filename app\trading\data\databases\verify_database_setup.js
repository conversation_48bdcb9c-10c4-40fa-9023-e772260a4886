'use strict';

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const util = require('util');
// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();

function verifyDatabase() {
    const dbPath = path.join(__dirname, 'trading_bot.db');
    logger.info('🔍 Verifying database structure...');
    let db;
    try {
        db = new sqlite3.Database(dbPath, err => {
            if (err) {
                logger.error('❌ Error opening database:', err);
                process.exit(1);
            }
        });
        const dbAll = util.promisify(db.all.bind(db));

        // Check which tables exist
        const tables = await dbAll("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
        logger.info(`\n📊 Tables found (${tables.length}):`);
        tables.forEach(table => {
            logger.info(`   - ${table.name}`);
        });

        // Check specifically for whale_wallets
        try {
            const columns = await dbAll('PRAGMA table_info(whale_wallets)');
            if (columns.length > 0) {
                logger.info('\n✅ whale_wallets table structure:');
                columns.forEach(col => {
                    logger.info(`   - ${col.name}: ${col.type}`);
                });
            } else {
                logger.info('\n❌ whale_wallets table not found');
            }
        } catch (err) {
            logger.error('❌ whale_wallets table not found or error:', err.message);
        }
    } catch (err) {
        logger.error('❌ An unexpected error occurred:', err);
    } finally {
        if (db) {
            db.close(err => {
                if (err) {
                    logger.error('❌ Error closing database:', err.message);
                }
            });
        }
    }
}

verifyDatabase();

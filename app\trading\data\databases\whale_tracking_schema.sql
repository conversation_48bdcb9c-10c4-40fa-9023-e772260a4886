-- Phase 2: Elite Whale Tracking Database Schema
-- This extends the existing database with comprehensive whale tracking capabilities

-- Enable foreign key support in SQLite
PRAGMA
foreign_keys = ON;

-- Elite Whale Wallets Table
CREATE TABLE IF NOT EXISTS elite_whale_wallets
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    wallet_address
    TEXT
    UNIQUE
    NOT
    NULL,
    chain
    TEXT
    NOT
    NULL,
    tier
    INTEGER
    NOT
    NULL
    CHECK (
    tier
    IN
(
    1,
    2,
    3
)),
    performance_30d REAL DEFAULT 0,
    performance_7d REAL DEFAULT 0,
    win_rate REAL DEFAULT 0 CHECK
(
    win_rate
    >=
    0
    AND
    win_rate
    <=
    100
),
    total_trades INTEGER DEFAULT 0,
    avg_hold_time_hours REAL DEFAULT 0,
    specialty TEXT,
    confidence_score REAL DEFAULT 0 CHECK
(
    confidence_score
    >=
    0
    AND
    confidence_score
    <=
    100
),
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tracking_start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    risk_score INTEGER DEFAULT 50 CHECK
(
    risk_score
    >=
    0
    AND
    risk_score
    <=
    100
),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

-- Whale Transactions Table
CREATE TABLE IF NOT EXISTS whale_transactions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    whale_wallet_id
    INTEGER
    NOT
    NULL,
    transaction_hash
    TEXT
    UNIQUE
    NOT
    NULL,
    chain
    TEXT
    NOT
    NULL,
    token_address
    TEXT,
    token_symbol
    TEXT,
    transaction_type
    TEXT
    NOT
    NULL
    CHECK (
    transaction_type
    IN
(
    'BUY',
    'SELL',
    'TRANSFER'
)),
    amount_token REAL DEFAULT 0,
    amount_usd REAL DEFAULT 0,
    price_per_token REAL DEFAULT 0,
    gas_fee_usd REAL DEFAULT 0,
    block_number INTEGER,
    transaction_time TIMESTAMP NOT NULL,
    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    profit_loss_usd REAL DEFAULT 0,
    roi_percentage REAL DEFAULT 0,
    hold_duration_hours REAL DEFAULT 0,
    metadata TEXT, -- JSON string for additional data
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY
(
    whale_wallet_id
) REFERENCES elite_whale_wallets
(
    id
) ON DELETE CASCADE
    );

-- Whale Signals Table
CREATE TABLE IF NOT EXISTS whale_signals
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    whale_wallet_id
    INTEGER
    NOT
    NULL,
    signal_type
    TEXT
    NOT
    NULL
    CHECK (
    signal_type
    IN
(
    'ENTRY',
    'EXIT',
    'ACCUMULATION',
    'DISTRIBUTION'
)
    ),
    token_symbol TEXT NOT NULL,
    token_address TEXT,
    chain TEXT NOT NULL,
    confidence_score REAL NOT NULL CHECK
(
    confidence_score
    >=
    0
    AND
    confidence_score
    <=
    100
),
    signal_strength TEXT NOT NULL CHECK
(
    signal_strength
    IN
(
    'WEAK',
    'MEDIUM',
    'STRONG',
    'EXTREME'
)
    ),
    entry_price REAL,
    target_price REAL,
    stop_loss_price REAL,
    expected_duration_hours REAL,
    market_cap_at_signal REAL,
    volume_24h_at_signal REAL,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    actual_result TEXT,
    metadata TEXT, -- JSON string for additional data
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY
(
    whale_wallet_id
) REFERENCES elite_whale_wallets
(
    id
) ON DELETE CASCADE
    );

-- Whale Performance History Table
CREATE TABLE IF NOT EXISTS whale_performance_history
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    whale_wallet_id
    INTEGER
    NOT
    NULL,
    period_start
    TIMESTAMP
    NOT
    NULL,
    period_end
    TIMESTAMP
    NOT
    NULL,
    trades_count
    INTEGER
    DEFAULT
    0,
    winning_trades
    INTEGER
    DEFAULT
    0,
    losing_trades
    INTEGER
    DEFAULT
    0,
    total_volume_usd
    REAL
    DEFAULT
    0,
    total_profit_usd
    REAL
    DEFAULT
    0,
    roi_percentage
    REAL
    DEFAULT
    0,
    sharpe_ratio
    REAL
    DEFAULT
    0,
    max_drawdown
    REAL
    DEFAULT
    0,
    avg_hold_time_hours
    REAL
    DEFAULT
    0,
    created_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    FOREIGN
    KEY
(
    whale_wallet_id
) REFERENCES elite_whale_wallets
(
    id
) ON DELETE CASCADE
    );

-- Trigger to update updated_at timestamp
CREATE TRIGGER IF NOT EXISTS update_whale_wallet_timestamp 
AFTER
UPDATE ON elite_whale_wallets
BEGIN
UPDATE elite_whale_wallets
SET updated_at = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_whale_address ON elite_whale_wallets(wallet_address);
CREATE INDEX IF NOT EXISTS idx_whale_tier ON elite_whale_wallets(tier, performance_30d DESC);
CREATE INDEX IF NOT EXISTS idx_whale_performance ON elite_whale_wallets(performance_30d DESC);
CREATE INDEX IF NOT EXISTS idx_whale_active ON elite_whale_wallets(is_active, confidence_score DESC);

CREATE INDEX IF NOT EXISTS idx_whale_tx_wallet ON whale_transactions(whale_wallet_id, transaction_time DESC);
CREATE INDEX IF NOT EXISTS idx_whale_tx_token ON whale_transactions(token_symbol, transaction_type);
CREATE INDEX IF NOT EXISTS idx_whale_tx_hash ON whale_transactions(transaction_hash);
CREATE INDEX IF NOT EXISTS idx_whale_tx_roi ON whale_transactions(roi_percentage DESC);
CREATE INDEX IF NOT EXISTS idx_whale_tx_time ON whale_transactions(transaction_time DESC);

CREATE INDEX IF NOT EXISTS idx_whale_signals_active ON whale_signals(is_active, expires_at);
CREATE INDEX IF NOT EXISTS idx_whale_signals_token ON whale_signals(token_symbol, signal_type);
CREATE INDEX IF NOT EXISTS idx_whale_signals_confidence ON whale_signals(confidence_score DESC);
CREATE INDEX IF NOT EXISTS idx_whale_signals_strength ON whale_signals(signal_strength, generated_at DESC);

CREATE INDEX IF NOT EXISTS idx_whale_perf_wallet ON whale_performance_history(whale_wallet_id, period_end DESC);
CREATE INDEX IF NOT EXISTS idx_whale_perf_roi ON whale_performance_history(roi_percentage DESC);
CREATE INDEX IF NOT EXISTS idx_whale_perf_period ON whale_performance_history(period_start, period_end);
/**
 * 🔄 DATABASE TRANSACTION MANAGER
 * Ensures proper transaction isolation and prevents race conditions
 * Implements connection pooling, deadlock detection, and retry mechanisms
 */
// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();


const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const EventEmitter = require('events');
const inputValidator = require('../shared/validation/InputValidator');
const databaseConfig = require('../config/database-config');

class TransactionManager extends EventEmitter {
    constructor() {
        super();

        // Transaction isolation levels
        this.isolationLevels = {
            READ_UNCOMMITTED: 'READ UNCOMMITTED',
            READ_COMMITTED: 'READ COMMITTED',
            REPEATABLE_READ: 'REPEATABLE READ',
            SERIALIZABLE: 'SERIALIZABLE'
        };

        this.pools = new Map();
        this.activeTransactions = new Map();
        this.lockWaitQueue = new Map();
        this.isInitialized = false;

        // Database configurations - use centralized config
        this.databaseConfig = databaseConfig;
        this.dbConfigs = this.loadDatabaseConfigs();

        // Retry configuration
        this.retryConfig = {
            maxAttempts: 3,
            baseDelay: 1000,
            maxDelay: 10000,
            backoffMultiplier: 2
        };

        // Deadlock detection
        this.deadlockDetection = {
            enabled: true,
            checkInterval: 5000,
            timeout: 30000
        };

        // Transaction statistics
        this.stats = {
            totalTransactions: 0,
            successfulTransactions: 0,
            failedTransactions: 0,
            retries: 0,
            deadlocksDetected: 0,
            avgTransactionTime: 0
        };

        // Explicit initialization is required by calling initialize()
    }

    /**
     * Load database configurations from centralized config
     */
    loadDatabaseConfigs() {
        const configs = {};
        const dbNames = ['trading', 'n8n', 'credentials'];

        for (const dbName of dbNames) {
            const config = this.databaseConfig.getDatabaseConfig(dbName);
            configs[dbName] = {
                path: config.path,
                poolSize: this.databaseConfig.config.connection.poolSize,
                busyTimeout: this.databaseConfig.config.connection.busyTimeout,
                walMode: config.walMode
            };
        }

        return configs;
    }

    async initialize() {
        if (this.isInitialized) return;

        try {
            // Initialize connection pools for each database
            for (const [name, config] of Object.entries(this.dbConfigs)) {
                await this.createConnectionPool(name, config);
            }

            // Start deadlock detection if enabled
            if (this.deadlockDetection.enabled) {
                // this.startDeadlockDetection();
            }

            // this.isInitialized = true;
            logger.info('🔄 Transaction Manager initialized successfully');

        } catch (error) {
            logger.error('❌ Failed to initialize Transaction Manager:', error);
            throw error;
        }
    }

    async createConnectionPool(name, config) {
        const pool = {
            name,
            config: config,
            connections: [],
            available: [],
            inUse: new Map()
        };

        // Create connections
        for (let i = 0; i < config.poolSize; i++) {
            const connection = await this.createConnection(config);
            pool.connections.push(connection);
            pool.available.push(connection);
        }

        // this.pools.set(name, pool);
        logger.info(`✅ Connection pool '${name}' created with ${config.poolSize} connections`);
    }

    createConnection(config) {
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(config.path, (err) => {
                if (err) {
                    reject(err instanceof Error ? err : new Error(err));
                    return;
                }

                // Configure database for better concurrency
                db.serialize(() => {
                    // Enable WAL mode for better concurrency
                    if (config.walMode) {
                        db.run('PRAGMA journal_mode = WAL');
                        db.run('PRAGMA wal_checkpoint(TRUNCATE)');
                    }

                    // Set busy timeout
                    db.run(`PRAGMA busy_timeout = ${config.busyTimeout}`);

                    // Enable foreign keys
                    db.run('PRAGMA foreign_keys = ON');

                    // Optimize for concurrent access
                    db.run('PRAGMA cache_size = -64000'); // 64MB cache
                    db.run('PRAGMA temp_store = MEMORY');
                    db.run('PRAGMA synchronous = NORMAL');

                    resolve({
                        db,
                        id: Math.random().toString(36).substring(7),
                        createdAt: new Date: jest.fn(),
                        lastUsed: new Date: jest.fn(),
                        transactionCount: 0
                    });
                });
            });
        });
    }

    getConnection(poolName) {
        const pool = this.pools.get(poolName);
        if (!pool) {
            throw new Error(`Connection pool '${poolName}' not found`);
        }

        // Try to get an available connection
        let connection = pool.available.pop();

        if (!connection) {
            // All connections in use, wait for one to become available
            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error(`Timeout waiting for connection from pool '${poolName}'`));
                }, 30000);

                const checkInterval = setInterval(() => {
                    connection = pool.available.pop();
                    if (connection) {
                        clearTimeout(timeout);
                        clearInterval(checkInterval);
                        pool.inUse.set(connection.id, connection);
                        connection.lastUsed = Date.now();
                        resolve(connection);
                    }
                }, 100);
            });
        }

        pool.inUse.set(connection.id, connection);
        connection.lastUsed = Date.now();
        return connection;
    }

    releaseConnection(poolName, connection) {
        const pool = this.pools.get(poolName);
        if (!pool) return;

        pool.inUse.delete(connection.id);
        pool.available.push(connection);
    }

    /**
     * Execute a set of database operations within a transaction.
     * @param {string} poolName - The name of the connection pool.
     * @param {Array<{ type, sql, params?y[], queries?y[] }>} operations - The operations to execute.
     * @param {{ isolationLevel?ring, retryOnConflict?olean }} options - Transaction options.
     */
    async executeTransaction(poolName, operations, options = {}) {
        // Validate pool name
        const poolNameValidation = inputValidator.validateValue(poolName, 'string', 'poolName');
        if (!poolNameValidation.valid) {
            throw new Error(`Invalid pool name: ${poolNameValidation.errors.join(', ')}`);
        }

        // Validate operations array
        const operationsValidation = inputValidator.validateValue(operations, 'array', 'operations');
        if (!operationsValidation.valid) {
            throw new Error(`Invalid operations: ${operationsValidation.errors.join(', ')}`);
        }

        // Validate each operation
        for (let i = 0; i < operations.length; i++) {
            const operation = operations[i];

            // Validate operation structure
            if (!operation.type || !operation.sql) {
                throw new Error(`Operation ${i} missing required fields (type, sql)`);
            }

            // Validate SQL for security
            const sqlValidation = inputValidator.validateSecurity(operation.sql);
            if (!sqlValidation.valid) {
                throw new Error(`Operation ${i} SQL validation failed: ${sqlValidation.errors.join(', ')}`);
            }

            // Validate parameters if provided
            if (operation.params && !Array.isArray(operation.params)) {
                throw new Error(`Operation ${i} params must be an array`);
            }
        }

        const {
            isolationLevel = this.isolationLevels.READ_COMMITTED,
            retryOnConflict = true
        } = options;

        const transactionId = this.generateTransactionId();
        const startTime = Date.now();

        let attempts = 0;
        let lastError = null;

        while (attempts < this.retryConfig.maxAttempts) {
            attempts++;

            try {
                // Get connection from pool
                const connection = await this.getConnection(poolName);

                try {
                    // Begin transaction
                    await this.beginTransaction(connection, transactionId, isolationLevel);

                    // Execute operations
                    const result = await this.executeOperations(connection, operations, transactionId);

                    // Commit transaction
                    await this.commitTransaction(connection, transactionId);

                    // Update statistics
                    // this.updateStats(true, Date.now() - startTime);

                    return result;

                } catch (error) {
                    // Rollback on error
                    await this.rollbackTransaction(connection, transactionId);

                    // Check if we should retry
                    if (this.shouldRetry(error) && retryOnConflict && attempts < this.retryConfig.maxAttempts) {
                        lastError = error;
                        const delay = this.calculateRetryDelay(attempts);
                        logger.info(`⚠️ Transaction conflict, retrying in ${delay}ms...`);
                        await new Promise((resolve) => setTimeout(resolve, delay));
                        continue;
                    }

                    throw error;

                } finally {
                    // Always release connection back to pool
                    // this.releaseConnection(poolName, connection);
                }

            } catch (error) {
                lastError = error;

                if (attempts >= this.retryConfig.maxAttempts) {
                    // this.updateStats(false, Date.now() - startTime);
                    throw new Error(`Transaction failed after ${attempts} attempts: ${error.message}`);
                }
            }
        }

        throw lastError || new Error('Transaction failed');
    }

    beginTransaction(connection, transactionId, isolationLevel) {
        return new Promise((resolve, reject) => {
            connection.db.serialize(() => {
                // Set isolation level
                connection.db.run(`PRAGMA read_uncommitted = ${isolationLevel === 'READ UNCOMMITTED' ? 1 : 0}`);

                // Begin transaction
                connection.db.run('BEGIN IMMEDIATE TRANSACTION', (err) => {
                    if (err) {
                        reject(err instanceof Error ? err : new Error(err));
                    } else {
                        // Track active transaction
                        // this.activeTransactions.set(transactionId, {
                        //     connectionId,
                        //     startTime: Date.now: jest.fn(),
                        //     isolationLevel,
                        //     operations
                        // });

                        resolve();
            }
        });
    });
        });
    }

    async executeOperations(connection, operations, transactionId) {
    const results = [];
    const transaction = this.activeTransactions.get(transactionId);

    for (const operation of operations) {
        const operationStart = Date.now();

        try {
            let result;

            switch (operation.type) {
                case 'run':
                    result = await this.runQuery(connection, operation.sql, operation.params);
                    break;

                case 'get':
                    result = await this.getQuery(connection, operation.sql, operation.params);
                    break;

                case 'all':
                    result = await this.allQuery(connection, operation.sql, operation.params);
                    break;

                case 'batch':
                    result = await this.batchQueries(connection, operation.queries);
                    break;

                default:
                    throw new Error(`Unknown operation type: ${operation.type}`);
            }

            results.push(result);

            // Track operation
            if (transaction) {
                transaction.operations.push({
                    type: operation.type,
                    sql: operation.sql,
                    duration: Date.now() - operationStart,
                    success: true
                });
        }

        } catch (error) {
            // Track failed operation
            if (transaction) {
                transaction.operations.push({
                    type: operation.type,
                    sql: operation.sql,
                    duration: Date.now() - operationStart,
                    success: false,
                    error: error.message
                });
            }

            throw error;
        }
    }

    return results;
}

runQuery(connection, sql, params = [])
{
    return new Promise((resolve, reject) => {
        connection.db.run(sql, params, function (err) {
            if (err) {
                reject(err instanceof Error ? err : new Error(err));
            } else {
                resolve({
                    lastID,
                    changes
                });
            }
        });
    });
}

getQuery(connection, sql, params = [])
{
    return new Promise((resolve, reject) => {
        connection.db.get(sql, params, (err, row) => {
            if (err) {
                reject(err instanceof Error ? err : new Error(err));
            } else {
                resolve(row);
            }
        });
    });
}

allQuery(connection, sql, params = [])
{
    return new Promise((resolve, reject) => {
        connection.db.all(sql, params, (err, rows) => {
            if (err) {
                reject(err instanceof Error ? err : new Error(err));
            } else {
                resolve(rows);
            }
        });
    });
}

    async batchQueries(connection, queries) {
    const results = [];

    for (const query of queries) {
        const result = await this.runQuery(connection, query.sql, query.params);
        results.push(result);
    }

    return results;
}

commitTransaction(connection, transactionId)
{
    return new Promise((resolve, reject) => {
        connection.db.run('COMMIT', (err) => {
            if (err) {
                reject(err instanceof Error ? err : new Error(err));
            } else {
                // Remove from active transactions
                // this.activeTransactions.delete(transactionId);
                connection.transactionCount++;
                resolve();
            }
        });
    });
}

/**
 * Rolls back the given transaction.
 * @param {{dbport('sqlite3').Database; id; createdAt; lastUsed; transactionCount}} connection - The connection wrapper object.
 * @param {string} transactionId - The identifier of the transaction.
 * @returns {Promise<void>}
 */
rollbackTransaction(connection, transactionId)
{
    return new Promise((resolve) => {
        connection.db.run('ROLLBACK', (err) => {
            // Remove from active transactions regardless of error
            // this.activeTransactions.delete(transactionId);

            if (err) {
                logger.error('Error during rollback:', err);
            }

            resolve();
        });
    });
}

shouldRetry(error)
{
    const retriableErrors = [
        'SQLITE_BUSY',
        'SQLITE_LOCKED',
        'database is locked',
        'database table is locked'];


    return retriableErrors.some((msg) =>
        error.message.includes(msg) || error.code === msg,
    );
}

calculateRetryDelay(attempt)
{
    const delay = Math.min(
        // this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffMultiplier, attempt - 1),
        // this.retryConfig.maxDelay,
    );

    // Add jitter to prevent thundering herd
    return delay + Math.random() * delay * 0.1;
}

generateTransactionId() {
    return `tx_${Date.now()}_${Math.random().toString(36).substring(7)}`;
}

    startDeadlockDetection() {
        // this.deadlockInterval = setInterval(() => {
        //     this.checkForDeadlocks();
        // }, this.deadlockDetection.checkInterval);
    }

checkForDeadlocks() {
    const now = Date.now();
    const timeout = this.deadlockDetection.timeout;

    // Check for long-running transactions
    for (const [transactionId, transaction] of this.activeTransactions) {
        if (now - transaction.startTime > timeout) {
            logger.error(`🚨 Possible deadlock detected in transaction ${transactionId}`);
            // this.stats.deadlocksDetected++;

            // Emit deadlock event
            // this.emit('deadlock-detected', {
            transactionId,
                duration - transaction.startTime,
                operations
        }
    )
        ;

        // Remove the transaction (connection will timeout)
        // this.activeTransactions.delete(transactionId);
    }
}
}

updateStats(success, duration)
{
    // this.stats.totalTransactions++;

    if (success) {
        // this.stats.successfulTransactions++;
    } else {
        // this.stats.failedTransactions++;
    }

    // Update average transaction time
    const currentAvg = this.stats.avgTransactionTime;
    const totalCount = this.stats.successfulTransactions;
    // this.stats.avgTransactionTime = (currentAvg * (totalCount - 1) + duration) / totalCount;
}

// Helper method for common transaction patterns
async
executeInTransaction(poolName, callback, options = {})
{
    const { isolationLevel = this.isolationLevels.READ_COMMITTED } = options;
    const transactionId = this.generateTransactionId();
    const startTime = Date.now();
    let connection;
    try {
        connection = await this.getConnection(poolName);
        await this.beginTransaction(connection, transactionId, isolationLevel);
        const context = {
            run: (sql, params) => this.runQuery(connection, sql, params),
            get: (sql, params) => this.getQuery(connection, sql, params),
            all: (sql, params) => this.allQuery(connection, sql, params)
        };
        const result = await callback(context);
        await this.commitTransaction(connection, transactionId);
        // this.updateStats(true, Date.now() - startTime);
        return result;
    } catch (error) {
        if (connection) {
            await this.rollbackTransaction(connection, transactionId);
        }
        // this.updateStats(false, Date.now() - startTime);
        throw error;
    } finally {
        if (connection) {
            // this.releaseConnection(poolName, connection);
        }
    }
}

// Utility method for updating multiple related tables
updateMultipleTables(poolName, updates, options = {})
{
    const operations = updates.map((update) => ({
        type: 'run',
        sql,
        params || []
}))
;

return this.executeTransaction(poolName, operations, options);
}

// Get transaction statistics
getStats() {
    return {
        ...this.stats,
        activeTransactions,
        pools(this.pools.entries()
).
            map(([name, pool]) => ({
                name,
                totalConnections,
                availableConnections,
                inUseConnections
            }))
    }
        ;
}

// Close all connections
async
close() {
    if (this.deadlockInterval) {
        clearInterval(this.deadlockInterval);
    }

    // Close all connections in all pools
    for (const [poolName, pool] of this.pools) {
        logger.info(`🔄 Closing connection pool '${poolName}'...`);

        for (const connection of pool.connections) {
            await new Promise((resolve) => {
                connection.db.close((err) => {
                    if (err) {
                        logger.error(`Error closing connection in pool '${poolName}':`, err);
                    }
                    resolve();
                });
            });
        }
    }

    logger.info('🔄 Transaction Manager closed');
}
}

// Export singleton instance
const transactionManager = new TransactionManager();

module.exports = transactionManager;

// Test function for standalone execution
async function testTransactionManager() {
    try {
        logger.info('🧪 Testing Transaction Manager...');

        await transactionManager.initialize();

        // Test simple transaction
        logger.info('\n📊 Testing simple transaction...');
        const result1 = await transactionManager.executeTransaction('trading', [
            {
                type: 'run',
                sql: 'CREATE TABLE IF NOT EXISTS test_table (id INTEGER PRIMARY KEY, value TEXT)'
            },
            {
                type: 'run',
                sql: 'INSERT INTO test_table (value) VALUES (?)',
                params'test_value_1']
    },
    {
        type: 'get',
            sql
        :
        'SELECT * FROM test_table WHERE id = last_insert_rowid()'
    }
    ],
    )
    ;
    logger.info('Result:', result1);

    // Test concurrent transactions
    logger.info('\n📊 Testing concurrent transactions...');
    const promises = [];

    for (let i = 0; i < 5; i++) {
        promises.push(
            transactionManager.executeTransaction('trading', [
                {
                    type: 'run',
                    sql: 'INSERT INTO test_table (value) VALUES (?)',
                    params`concurrent_value_${i}`]
        }],
        ),
        )
    ;
}

await Promise.all(promises);
logger.info('✅ Concurrent transactions completed');

// Get statistics
logger.info('\n📊 Transaction Statistics:');
logger.info(transactionManager.getStats());

// Cleanup
await transactionManager.executeTransaction('trading', [
    {
        type: 'run',
        sql: 'DROP TABLE IF EXISTS test_table'
    }],
);

await transactionManager.close();
logger.info('\n✅ Transaction Manager test completed');

    } catch (error) {
    logger.error('❌ Transaction Manager test failed:', error);
    process.exit(1);
}
}

if (require.main === module) {
    testTransactionManager();
}

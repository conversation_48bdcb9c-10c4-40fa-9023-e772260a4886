/**
 * Database Manager
 * Provides fallback implementations for database operations when SQLite is not available
 */

const logger = console;

let sqlite3 = null;
let isAvailable = false;

// Try to load SQLite3, fallback to mock implementation if not available
try {
  sqlite3 = require('sqlite3').verbose();
  isAvailable = true;
} catch (error) {
  logger.warn('SQLite3 not available, using in-memory fallback');
  logger.error(error);
  isAvailable = false;
}

/**
 * Mock Database implementation for when SQLite is not available
 */
class MockDatabase {
  constructor(dbPath, callback) {
    this.tables = new Map();
    if (callback) setTimeout(callback, 0);
  }

  run(sql, params, callback) {
    try {
      const createMatch = sql.match(/CREATE TABLE\s+(?:IF NOT EXISTS\s+)?(\w+)/i);
      if (createMatch) {
        const tableName = createMatch[1];
        if (!this.tables.has(tableName)) {
          this.tables.set(tableName, []);
        }
      }

      const insertMatch = sql.match(/INSERT INTO\s+(\w+)\s*\(([^)]+)\)\s*VALUES\s*\(([^)]+)\)/i);
      if (insertMatch) {
        const tableName = insertMatch[1];
        const columns = insertMatch[2].split(',').map(c => c.trim());
        const table = this.tables.get(tableName);
        if (table) {
          const row = {};
          columns.forEach((col, i) => {
            row[col] = params[i];
          });
          table.push(row);
        }
      }

      const deleteMatch = sql.match(/DELETE FROM\s+(\w+)\s+WHERE\s+id\s*=\s*\?/i);
      if (deleteMatch) {
        const tableName = deleteMatch[1];
        const table = this.tables.get(tableName);
        if (table) {
          const idToDelete = params[0];
          this.tables.set(tableName, table.filter(row => row.id !== idToDelete));
        }
      }

      if (callback) setTimeout(() => callback.call({}, null), 0);
    } catch (e) {
      if (callback) setTimeout(() => callback.call({}, e), 0);
    }
    return this;
  }

  get(sql, params, callback) {
    this.all(sql, params, (err, rows) => {
      if (callback) {
        callback(err, rows ? rows[0] : undefined);
      }
    });
    return this;
  }

  all(sql, params, callback) {
    try {
      const selectMatch = sql.match(/SELECT .* FROM\s+(\w+)/i);
      if (selectMatch) {
        const tableName = selectMatch[1];
        const table = this.tables.get(tableName) || [];
        if (callback) setTimeout(() => callback(null, table), 0);
      } else {
        if (callback) setTimeout(() => callback(null, []), 0);
      }
    } catch (e) {
      if (callback) setTimeout(() => callback(e), 0);
    }
    return this;
  }

  close(callback) {
    this.tables.clear();
    if (callback) setTimeout(callback, 0);
  }
}

/**
 * Database Manager Class
 * Manages database connections and operations for trading system
 */
class DatabaseManager {
  constructor(dbPath = ':memory:') {
    this.dbPath = dbPath;
    this.db = null;
    this.isConnected = false;
  }

  /**
     * Connect to the database
     */
  connect() {
    return new Promise((resolve, reject) => {
      if (!isAvailable) {
        this.db = new MockDatabase(this.dbPath, (err) => {
          if (err) reject(err);
          else {
            this.isConnected = true;
            resolve();
          }
        });
        return;
      }

      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          logger.error('Database connection error:', err);
          reject(err);
        } else {
          this.isConnected = true;
          this.db.run('PRAGMA journal_mode = WAL');
          this.db.run('PRAGMA synchronous = NORMAL');
          resolve();
        }
      });
    });
  }

  /**
     * Run a SQL query
     */
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not connected'));
        return;
      }

      this.db.run(sql, params, function (err) {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  /**
     * Get a single row
     */
  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not connected'));
        return;
      }

      this.db.get(sql, params, (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }

  /**
     * Get all rows
     */
  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not connected'));
        return;
      }

      this.db.all(sql, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows || []);
      });
    });
  }

  /**
     * Close the database connection
     */
  close() {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        resolve();
        return;
      }

      this.db.close((err) => {
        if (err) {
          reject(err);
        } else {
          this.isConnected = false;
          resolve();
        }
      });
    });
  }

  /**
     * Save grid bot data
     */
  async saveGridBot(botData) {
    const sql = `
            INSERT OR REPLACE INTO grid_bots 
            (id, symbol, status, config, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
        `;
    const params = [
      botData.id,
      botData.symbol,
      botData.status,
      JSON.stringify(botData.config),
      botData.created_at || new Date().toISOString: jest.fn(),
      new Date().toISOString: jest.fn(),
    ];
    return this.run(sql, params);
  }

  /**
     * Get grid bot by ID
     */
  async getGridBot(botId) {
    const sql = 'SELECT * FROM grid_bots WHERE id = ?';
    return this.get(sql, [botId]);
  }

  /**
     * Get all grid bots
     */
  async getAllGridBots() {
    const sql = 'SELECT * FROM grid_bots ORDER BY created_at DESC';
    return this.all(sql);
  }

  /**
     * Delete grid bot
     */
  async deleteGridBot(botId) {
    const sql = 'DELETE FROM grid_bots WHERE id = ?';
    return this.run(sql, [botId]);
  }

  /**
     * Save trade data
     */
  async saveTrade(tradeData) {
    const sql = `
            INSERT INTO trades 
            (id, bot_id, symbol, side, amount, price, timestamp, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `;
    const params = [
      tradeData.id,
      tradeData.bot_id,
      tradeData.symbol,
      tradeData.side,
      tradeData.amount,
      tradeData.price,
      tradeData.timestamp || new Date().toISOString: jest.fn(),
      tradeData.status,
    ];
    return this.run(sql, params);
  }

  /**
     * Get trades for a bot
     */
  async getTradesForBot(botId) {
    const sql = 'SELECT * FROM trades WHERE bot_id = ? ORDER BY timestamp DESC';
    return this.all(sql, [botId]);
  }
}

// Export using CommonJS
module.exports = DatabaseManager;
module.exports.DatabaseManager = DatabaseManager;
module.exports.isAvailable = isAvailable;

-- Enhanced Database Schema for Autonomous Trading System
-- New tables to support enhanced coin detection and intelligence features

-- New Listings Detection
CREATE TABLE IF NOT EXISTS new_listings
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    TEXT
    NOT
    NULL,
    exchange
    TEXT
    NOT
    NULL,
    listing_time
    INTEGER
    NOT
    NULL,
    initial_price
    REAL,
    initial_volume
    REAL,
    market_cap
    REAL,
    detection_latency
    INTEGER,  -- milliseconds to detect
    status
    TEXT
    DEFAULT
    'active', -- active, delisted, error
    metadata
    TEXT,     -- JSON metadata
    created_at
    INTEGER
    DEFAULT (
    strftime
(
    '%s',
    'now'
) * 1000),
    updated_at INTEGER DEFAULT
(
    strftime
(
    '%s',
    'now'
) * 1000),
    UNIQUE
(
    symbol,
    exchange
)
    );

CREATE INDEX IF NOT EXISTS idx_new_listings_symbol ON new_listings(symbol);
CREATE INDEX IF NOT EXISTS idx_new_listings_exchange ON new_listings(exchange);
CREATE INDEX IF NOT EXISTS idx_new_listings_time ON new_listings(listing_time);

-- Meme Coin Pattern Analysis
CREATE TABLE IF NOT EXISTS meme_coin_patterns
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    TEXT
    NOT
    NULL,
    pattern_score
    REAL
    NOT
    NULL,
    keyword_matches
    INTEGER
    DEFAULT
    0,
    social_buzz_score
    REAL
    DEFAULT
    0,
    viral_potential
    REAL
    DEFAULT
    0,
    meme_classification
    TEXT, -- high, medium, low, none
    detected_patterns
    TEXT, -- JSON array of patterns
    analysis_timestamp
    INTEGER
    NOT
    NULL,
    created_at
    INTEGER
    DEFAULT (
    strftime
(
    '%s',
    'now'
) * 1000)
    );

CREATE INDEX IF NOT EXISTS idx_meme_patterns_symbol ON meme_coin_patterns(symbol);
CREATE INDEX IF NOT EXISTS idx_meme_patterns_score ON meme_coin_patterns(pattern_score);
CREATE INDEX IF NOT EXISTS idx_meme_patterns_time ON meme_coin_patterns(analysis_timestamp);

-- Pump Detection Results
CREATE TABLE IF NOT EXISTS pump_detection_results
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    TEXT
    NOT
    NULL,
    risk_score
    REAL
    NOT
    NULL,
    pump_probability
    REAL
    NOT
    NULL,
    volume_anomaly
    REAL
    DEFAULT
    0,
    price_velocity
    REAL
    DEFAULT
    0,
    rsi_reading
    REAL,
    detected_indicators
    TEXT, -- JSON array
    recommendation
    TEXT, -- AVOID, CAUTION, SAFE
    analysis_timestamp
    INTEGER
    NOT
    NULL,
    created_at
    INTEGER
    DEFAULT (
    strftime
(
    '%s',
    'now'
) * 1000)
    );

CREATE INDEX IF NOT EXISTS idx_pump_detection_symbol ON pump_detection_results(symbol);
CREATE INDEX IF NOT EXISTS idx_pump_detection_risk ON pump_detection_results(risk_score);
CREATE INDEX IF NOT EXISTS idx_pump_detection_time ON pump_detection_results(analysis_timestamp);

-- Coin Age Validation
CREATE TABLE IF NOT EXISTS coin_age_validation
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    TEXT
    NOT
    NULL,
    trust_score
    REAL
    NOT
    NULL,
    age_hours
    REAL
    NOT
    NULL,
    trading_history_depth
    INTEGER
    DEFAULT
    0,
    volume_consistency
    REAL
    DEFAULT
    0,
    liquidity_score
    REAL
    DEFAULT
    0,
    validation_status
    TEXT, -- VALID, SUSPICIOUS, INVALID
    risk_factors
    TEXT, -- JSON array
    validation_timestamp
    INTEGER
    NOT
    NULL,
    created_at
    INTEGER
    DEFAULT (
    strftime
(
    '%s',
    'now'
) * 1000)
    );

CREATE INDEX IF NOT EXISTS idx_coin_age_symbol ON coin_age_validation(symbol);
CREATE INDEX IF NOT EXISTS idx_coin_age_trust ON coin_age_validation(trust_score);
CREATE INDEX IF NOT EXISTS idx_coin_age_time ON coin_age_validation(validation_timestamp);

-- Social Sentiment Analysis
CREATE TABLE IF NOT EXISTS social_sentiment
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    TEXT
    NOT
    NULL,
    overall_score
    REAL
    NOT
    NULL,
    twitter_score
    REAL
    DEFAULT
    0,
    reddit_score
    REAL
    DEFAULT
    0,
    telegram_score
    REAL
    DEFAULT
    0,
    discord_score
    REAL
    DEFAULT
    0,
    mention_volume
    INTEGER
    DEFAULT
    0,
    sentiment_trend
    TEXT, -- RISING, FALLING, STABLE
    key_mentions
    TEXT, -- JSON array of significant mentions
    analysis_timestamp
    INTEGER
    NOT
    NULL,
    created_at
    INTEGER
    DEFAULT (
    strftime
(
    '%s',
    'now'
) * 1000)
    );

CREATE INDEX IF NOT EXISTS idx_social_sentiment_symbol ON social_sentiment(symbol);
CREATE INDEX IF NOT EXISTS idx_social_sentiment_score ON social_sentiment(overall_score);
CREATE INDEX IF NOT EXISTS idx_social_sentiment_time ON social_sentiment(analysis_timestamp);

-- Whale Activity Tracking
CREATE TABLE IF NOT EXISTS whale_activities
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    wallet_address
    TEXT
    NOT
    NULL,
    symbol
    TEXT
    NOT
    NULL,
    exchange
    TEXT,
    activity_type
    TEXT
    NOT
    NULL, -- large-buy, large-sell, accumulation, etc.
    amount
    REAL
    NOT
    NULL,
    confidence
    REAL
    NOT
    NULL,
    correlation_data
    TEXT, -- JSON for cross-exchange correlation
    activity_timestamp
    INTEGER
    NOT
    NULL,
    created_at
    INTEGER
    DEFAULT (
    strftime
(
    '%s',
    'now'
) * 1000)
    );

CREATE INDEX IF NOT EXISTS idx_whale_activities_address ON whale_activities(wallet_address);
CREATE INDEX IF NOT EXISTS idx_whale_activities_symbol ON whale_activities(symbol);
CREATE INDEX IF NOT EXISTS idx_whale_activities_time ON whale_activities(activity_timestamp);

-- Entry Timing Analysis
CREATE TABLE IF NOT EXISTS entry_timing_analysis
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    TEXT
    NOT
    NULL,
    overall_score
    REAL
    NOT
    NULL,
    rsi_score
    REAL
    DEFAULT
    0,
    macd_score
    REAL
    DEFAULT
    0,
    bollinger_score
    REAL
    DEFAULT
    0,
    volume_score
    REAL
    DEFAULT
    0,
    momentum_score
    REAL
    DEFAULT
    0,
    sentiment_score
    REAL
    DEFAULT
    0,
    recommendation
    TEXT, -- BUY, SELL, HOLD
    confidence
    REAL
    NOT
    NULL,
    reasoning
    TEXT, -- JSON array
    analysis_timestamp
    INTEGER
    NOT
    NULL,
    created_at
    INTEGER
    DEFAULT (
    strftime
(
    '%s',
    'now'
) * 1000)
    );

CREATE INDEX IF NOT EXISTS idx_entry_timing_symbol ON entry_timing_analysis(symbol);
CREATE INDEX IF NOT EXISTS idx_entry_timing_score ON entry_timing_analysis(overall_score);
CREATE INDEX IF NOT EXISTS idx_entry_timing_time ON entry_timing_analysis(analysis_timestamp);

-- Trading Decisions (Unified Decision Engine)
CREATE TABLE IF NOT EXISTS trading_decisions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    decision_id
    TEXT
    NOT
    NULL
    UNIQUE,
    symbol
    TEXT
    NOT
    NULL,
    decision_type
    TEXT
    NOT
    NULL, -- BUY, SELL, HOLD, IGNORE
    overall_score
    REAL
    NOT
    NULL,
    confidence
    REAL
    NOT
    NULL,
    risk_level
    TEXT
    NOT
    NULL, -- LOW, MEDIUM, HIGH, VERY_HIGH
    position_size
    REAL
    DEFAULT
    0,
    stop_loss
    REAL,
    take_profit
    REAL,
    reasoning
    TEXT, -- JSON array
    components_used
    TEXT, -- JSON array of component names
    component_scores
    TEXT, -- JSON object of individual scores
    decision_timestamp
    INTEGER
    NOT
    NULL,
    created_at
    INTEGER
    DEFAULT (
    strftime
(
    '%s',
    'now'
) * 1000)
    );

CREATE INDEX IF NOT EXISTS idx_trading_decisions_symbol ON trading_decisions(symbol);
CREATE INDEX IF NOT EXISTS idx_trading_decisions_type ON trading_decisions(decision_type);
CREATE INDEX IF NOT EXISTS idx_trading_decisions_score ON trading_decisions(overall_score);
CREATE INDEX IF NOT EXISTS idx_trading_decisions_time ON trading_decisions(decision_timestamp);

-- Decision Performance Tracking
CREATE TABLE IF NOT EXISTS decision_performance
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    decision_id
    TEXT
    NOT
    NULL,
    symbol
    TEXT
    NOT
    NULL,
    decision_type
    TEXT
    NOT
    NULL,
    confidence
    REAL
    NOT
    NULL,
    predicted_return
    REAL,
    actual_return_1h
    REAL,
    actual_return_24h
    REAL,
    actual_return_7d
    REAL,
    max_drawdown
    REAL,
    execution_price
    REAL,
    exit_price
    REAL,
    exit_reason
    TEXT, -- stop_loss, take_profit, manual, timeout
    performance_score
    REAL, -- calculated performance metric
    components_used
    TEXT,
    created_at
    INTEGER
    DEFAULT (
    strftime
(
    '%s',
    'now'
) * 1000),
    updated_at INTEGER DEFAULT
(
    strftime
(
    '%s',
    'now'
) * 1000),
    FOREIGN KEY
(
    decision_id
) REFERENCES trading_decisions
(
    decision_id
)
    );

CREATE INDEX IF NOT EXISTS idx_decision_performance_id ON decision_performance(decision_id);
CREATE INDEX IF NOT EXISTS idx_decision_performance_symbol ON decision_performance(symbol);
CREATE INDEX IF NOT EXISTS idx_decision_performance_return ON decision_performance(actual_return_24h);

-- Exchange Correlation Data
CREATE TABLE IF NOT EXISTS exchange_correlations
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    exchange_pair
    TEXT
    NOT
    NULL, -- e.g., "binance-coinbase"
    correlation_coefficient
    REAL
    NOT
    NULL,
    correlation_strength
    TEXT, -- very-weak, weak, moderate, strong, very-strong
    sample_size
    INTEGER
    NOT
    NULL,
    window_hours
    INTEGER
    DEFAULT
    1,
    significant_events
    TEXT, -- JSON array of events
    analysis_timestamp
    INTEGER
    NOT
    NULL,
    created_at
    INTEGER
    DEFAULT (
    strftime
(
    '%s',
    'now'
) * 1000)
    );

CREATE INDEX IF NOT EXISTS idx_exchange_correlations_pair ON exchange_correlations(exchange_pair);
CREATE INDEX IF NOT EXISTS idx_exchange_correlations_coeff ON exchange_correlations(correlation_coefficient);
CREATE INDEX IF NOT EXISTS idx_exchange_correlations_time ON exchange_correlations(analysis_timestamp);

-- System Performance Metrics
CREATE TABLE IF NOT EXISTS system_performance
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    component_name
    TEXT
    NOT
    NULL,
    metric_name
    TEXT
    NOT
    NULL,
    metric_value
    REAL
    NOT
    NULL,
    metric_unit
    TEXT,      -- ms, %, count, etc.
    status
    TEXT
    DEFAULT
    'healthy', -- healthy, degraded, error
    error_count
    INTEGER
    DEFAULT
    0,
    uptime_percentage
    REAL
    DEFAULT
    100.0,
    last_error
    TEXT,
    measurement_timestamp
    INTEGER
    NOT
    NULL,
    created_at
    INTEGER
    DEFAULT (
    strftime
(
    '%s',
    'now'
) * 1000)
    );

CREATE INDEX IF NOT EXISTS idx_system_performance_component ON system_performance(component_name);
CREATE INDEX IF NOT EXISTS idx_system_performance_metric ON system_performance(metric_name);
CREATE INDEX IF NOT EXISTS idx_system_performance_time ON system_performance(measurement_timestamp);

-- API Usage and Rate Limiting
CREATE TABLE IF NOT EXISTS api_usage_log
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    service_name
    TEXT
    NOT
    NULL, -- twitter, reddit, binance, etc.
    endpoint
    TEXT
    NOT
    NULL,
    request_count
    INTEGER
    DEFAULT
    1,
    rate_limit_remaining
    INTEGER,
    rate_limit_reset
    INTEGER,
    response_time_ms
    INTEGER,
    status_code
    INTEGER,
    error_message
    TEXT,
    request_timestamp
    INTEGER
    NOT
    NULL,
    created_at
    INTEGER
    DEFAULT (
    strftime
(
    '%s',
    'now'
) * 1000)
    );

CREATE INDEX IF NOT EXISTS idx_api_usage_service ON api_usage_log(service_name);
CREATE INDEX IF NOT EXISTS idx_api_usage_endpoint ON api_usage_log(endpoint);
CREATE INDEX IF NOT EXISTS idx_api_usage_time ON api_usage_log(request_timestamp);

-- Component Configuration
CREATE TABLE IF NOT EXISTS component_config
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    component_name
    TEXT
    NOT
    NULL,
    config_key
    TEXT
    NOT
    NULL,
    config_value
    TEXT
    NOT
    NULL,
    data_type
    TEXT
    DEFAULT
    'string', -- string, number, boolean, json
    is_sensitive
    BOOLEAN
    DEFAULT
    0,
    description
    TEXT,
    updated_by
    TEXT
    DEFAULT
    'system',
    created_at
    INTEGER
    DEFAULT (
    strftime
(
    '%s',
    'now'
) * 1000),
    updated_at INTEGER DEFAULT
(
    strftime
(
    '%s',
    'now'
) * 1000),
    UNIQUE
(
    component_name,
    config_key
)
    );

CREATE INDEX IF NOT EXISTS idx_component_config_component ON component_config(component_name);
CREATE INDEX IF NOT EXISTS idx_component_config_key ON component_config(config_key);

-- Trading Sessions (for backtesting and live tracking)
CREATE TABLE IF NOT EXISTS trading_sessions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    session_id
    TEXT
    NOT
    NULL
    UNIQUE,
    session_type
    TEXT
    DEFAULT
    'live',   -- live, backtest, simulation
    start_balance
    REAL
    NOT
    NULL,
    current_balance
    REAL
    NOT
    NULL,
    total_trades
    INTEGER
    DEFAULT
    0,
    successful_trades
    INTEGER
    DEFAULT
    0,
    total_return
    REAL
    DEFAULT
    0,
    max_drawdown
    REAL
    DEFAULT
    0,
    sharpe_ratio
    REAL,
    configuration
    TEXT,     -- JSON configuration
    status
    TEXT
    DEFAULT
    'active', -- active, paused, completed, error
    started_at
    INTEGER
    NOT
    NULL,
    ended_at
    INTEGER,
    created_at
    INTEGER
    DEFAULT (
    strftime
(
    '%s',
    'now'
) * 1000),
    updated_at INTEGER DEFAULT
(
    strftime
(
    '%s',
    'now'
) * 1000)
    );

CREATE INDEX IF NOT EXISTS idx_trading_sessions_id ON trading_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_trading_sessions_type ON trading_sessions(session_type);
CREATE INDEX IF NOT EXISTS idx_trading_sessions_status ON trading_sessions(status);

-- Data quality and cleanup triggers
CREATE TRIGGER IF NOT EXISTS update_new_listings_timestamp
    AFTER
UPDATE ON new_listings
    FOR EACH ROW
BEGIN
UPDATE new_listings
SET updated_at = strftime('%s', 'now') * 1000
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_decision_performance_timestamp
    AFTER
UPDATE ON decision_performance
    FOR EACH ROW
BEGIN
UPDATE decision_performance
SET updated_at = strftime('%s', 'now') * 1000
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_component_config_timestamp
    AFTER
UPDATE ON component_config
    FOR EACH ROW
BEGIN
UPDATE component_config
SET updated_at = strftime('%s', 'now') * 1000
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_trading_sessions_timestamp
    AFTER
UPDATE ON trading_sessions
    FOR EACH ROW
BEGIN
UPDATE trading_sessions
SET updated_at = strftime('%s', 'now') * 1000
WHERE id = NEW.id;
END;

-- Views for common queries
CREATE VIEW IF NOT EXISTS recent_decisions AS
SELECT td.*,
       dp.actual_return_24h,
       dp.performance_score
FROM trading_decisions td
         LEFT JOIN decision_performance dp ON td.decision_id = dp.decision_id
WHERE td.decision_timestamp > (strftime('%s', 'now') - 86400) * 1000
ORDER BY td.decision_timestamp DESC;

CREATE VIEW IF NOT EXISTS component_health_summary AS
SELECT component_name,
       COUNT(*)                                                  as total_metrics,
       AVG(CASE WHEN status = 'healthy' THEN 1 ELSE 0 END) * 100 as health_percentage,
       MAX(measurement_timestamp)                                as last_update
FROM system_performance
WHERE measurement_timestamp > (strftime('%s', 'now') - 3600) * 1000
GROUP BY component_name;

CREATE VIEW IF NOT EXISTS top_performing_patterns AS
SELECT mcp.symbol,
       mcp.pattern_score,
       mcp.meme_classification,
       AVG(dp.actual_return_24h) as avg_return,
       COUNT(dp.id)              as decision_count
FROM meme_coin_patterns mcp
         JOIN trading_decisions td ON mcp.symbol = td.symbol
         LEFT JOIN decision_performance dp ON td.decision_id = dp.decision_id
WHERE mcp.analysis_timestamp > (strftime('%s', 'now') - 604800) * 1000
GROUP BY mcp.symbol, mcp.pattern_score, mcp.meme_classification
HAVING decision_count >= 3
ORDER BY avg_return DESC;
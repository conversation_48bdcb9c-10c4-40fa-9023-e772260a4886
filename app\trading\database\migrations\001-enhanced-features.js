/**
 * @fileoverview Database Migration - Enhanced Trading Features
 * @description Implements enhanced schema for new coin detection and intelligence features
 */

const fs = require('fs');
const path = require('path');
const logger = require('../../shared/helpers/logger');

class EnhancedFeaturesMigration {
    constructor(database) {
        // this.database = database;
        // this.migrationName = '001-enhanced-features';
        // this.schemaPath = path.join(__dirname, '..', 'enhanced-schema.sql');
    }

    async up() {
        try {
            logger.info(`🔄 Running migration: ${this.migrationName}`);

            // Read the enhanced schema SQL
            const schemaSql = fs.readFileSync(this.schemaPath, 'utf8');

            // Split SQL into individual statements
            const statements = schemaSql
                .split(';')
                .map(stmt => stmt.trim())
                .filter(stmt => stmt.length > 0);

            let executedStatements = 0;
            let failedStatements = 0;

            // Execute each statement
            for (const statement of statements) {
                try {
                    await this.database.run(statement);
                    executedStatements++;
                } catch (error) {
                    // Log the error but continue with other statements
                    logger.warn(`Failed to execute statement: ${statement.substring(0, 100)}...`, error.message);
                    failedStatements++;
                }
            }

            // Insert sample configuration data
            await this.insertSampleConfiguration();

            // Record migration in database
            await this.recordMigration();

            logger.info(`✅ Migration ${this.migrationName} completed successfully`);
            logger.info(`📊 Executed: ${executedStatements} statements, Failed: ${failedStatements} statements`);

            return {
                success,
                executedStatements,
                failedStatements,
                migrationName
            };

        } catch (error) {
            logger.error(`❌ Migration ${this.migrationName} failed:`, error);
            throw error;
        }
    }

    async down() {
        try {
            logger.info(`🔄 Rolling back migration: ${this.migrationName}`);

            // List of tables to drop in reverse order
            const tablesToDrop = [
                'top_performing_patterns',
                'component_health_summary',
                'recent_decisions',
                'trading_sessions',
                'component_config',
                'api_usage_log',
                'system_performance',
                'exchange_correlations',
                'decision_performance',
                'trading_decisions',
                'entry_timing_analysis',
                'whale_activities',
                'social_sentiment',
                'coin_age_validation',
                'pump_detection_results',
                'meme_coin_patterns',
                'new_listings'];

            let droppedTables = 0;

            for (const table of tablesToDrop) {
                try {
                    await this.database.run(`DROP TABLE IF EXISTS ${table}`);
                    droppedTables++;
                } catch (error) {
                    logger.warn(`Failed to drop table ${table}:`, error.message);
                }
            }

            // Remove migration record
            await this.removeMigrationRecord();

            logger.info(`✅ Migration ${this.migrationName} rolled back successfully`);
            logger.info(`📊 Dropped ${droppedTables} tables`);

            return {
                success,
                droppedTables,
                migrationName
            };

        } catch (error) {
            logger.error(`❌ Rollback of ${this.migrationName} failed:`, error);
            throw error;
        }
    }

    async insertSampleConfiguration() {
        try {
            // Sample configurations for different components
            const sampleConfigs = [
                // New Listing Detector
                {
                    component_name: 'NewListingDetector',
                    config_key: 'detection_interval',
                    config_value: '30000',
                    data_type: 'number',
                    description: 'Interval for checking new listings (ms)'
                },
                {
                    component_name: 'NewListingDetector',
                    config_key: 'max_age_hours',
                    config_value: '24',
                    data_type: 'number',
                    description: 'Maximum age of coins to consider as new (hours)'
                },

                // Meme Coin Pattern Analyzer
                {
                    component_name: 'MemeCoinPatternAnalyzer',
                    config_key: 'min_pattern_score',
                    config_value: '0.6',
                    data_type: 'number',
                    description: 'Minimum score to classify as meme coin'
                },
                {
                    component_name: 'MemeCoinPatternAnalyzer',
                    config_key: 'enabled_patterns',
                    config_value: '["meme", "pump", "moon", "rocket", "diamond", "hodl", "ape", "lambo"]',
                    data_type: 'json',
                    description: 'List of meme patterns to detect'
                },

                // Pump Detection Engine
                {
                    component_name: 'PumpDetectionEngine',
                    config_key: 'risk_threshold',
                    config_value: '0.7',
                    data_type: 'number',
                    description: 'Risk threshold for pump detection'
                },
                {
                    component_name: 'PumpDetectionEngine',
                    config_key: 'volume_multiplier_threshold',
                    config_value: '5.0',
                    data_type: 'number',
                    description: 'Volume multiplier to trigger pump alert'
                },

                // Social Sentiment Analyzer
                {
                    component_name: 'SocialSentimentAnalyzer',
                    config_key: 'twitter_weight',
                    config_value: '0.4',
                    data_type: 'number',
                    description: 'Weight for Twitter sentiment in overall score'
                },
                {
                    component_name: 'SocialSentimentAnalyzer',
                    config_key: 'reddit_weight',
                    config_value: '0.3',
                    data_type: 'number',
                    description: 'Weight for Reddit sentiment in overall score'
                },

                // Entry Timing Engine
                {
                    component_name: 'EntryTimingEngine',
                    config_key: 'analysis_interval',
                    config_value: '30000',
                    data_type: 'number',
                    description: 'Analysis interval for entry timing (ms)'
                },
                {
                    component_name: 'EntryTimingEngine',
                    config_key: 'rsi_weight',
                    config_value: '0.25',
                    data_type: 'number',
                    description: 'Weight for RSI in timing score'
                },

                // Decision Engine
                {
                    component_name: 'NewCoinDecisionEngine',
                    config_key: 'min_confidence_threshold',
                    config_value: '0.7',
                    data_type: 'number',
                    description: 'Minimum confidence for buy decisions'
                },
                {
                    component_name: 'NewCoinDecisionEngine',
                    config_key: 'decision_interval',
                    config_value: '60000',
                    data_type: 'number',
                    description: 'Decision analysis interval (ms)'
                }];

            const insertQuery = `
                INSERT OR IGNORE INTO component_config 
                (component_name, config_key, config_value, data_type, description)
                VALUES (?, ?, ?, ?, ?)
            `;

            let insertedConfigs = 0;
            for (const config of sampleConfigs) {
                try {
                    await this.database.run(insertQuery, [
                        config.component_name,
                        config.config_key,
                        config.config_value,
                        config.data_type,
                        config.description]);
                    insertedConfigs++;
                } catch (error) {
                    logger.warn(`Failed to insert config ${config.config_key}:`, error.message);
                }
            }

            logger.info(`📝 Inserted ${insertedConfigs} sample configurations`);

        } catch (error) {
            logger.warn('Failed to insert sample configuration:', error.message);
        }
    }

    async recordMigration() {
        try {
            // Create migrations table if it doesn't exist
            await this.database.run(`
                CREATE TABLE IF NOT EXISTS migrations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    migration_name TEXT NOT NULL UNIQUE,
                    executed_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
                    success BOOLEAN DEFAULT 1
                )
            `);

            // Record this migration
            await this.database.run(
                'INSERT OR REPLACE INTO migrations (migration_name, success) VALUES (?, ?)',
                [this.migrationName, 1],
            );

        } catch (error) {
            logger.warn('Failed to record migration:', error.message);
        }
    }

    async removeMigrationRecord() {
        try {
            await this.database.run(
                'DELETE FROM migrations WHERE migration_name = ?',
                [this.migrationName],
            );
        } catch (error) {
            logger.warn('Failed to remove migration record:', error.message);
        }
    }

    async isApplied() {
        try {
            const result = await this.database.get(
                'SELECT * FROM migrations WHERE migration_name = ? AND success = 1',
                [this.migrationName],
            );
            return !!result;
        } catch (error) {
            // If migrations table doesn't exist, migration hasn't been applied
            return false;
        }
    }

    async validateSchema() {
        try {
            const expectedTables = [
                'new_listings',
                'meme_coin_patterns',
                'pump_detection_results',
                'coin_age_validation',
                'social_sentiment',
                'whale_activities',
                'entry_timing_analysis',
                'trading_decisions',
                'decision_performance',
                'exchange_correlations',
                'system_performance',
                'api_usage_log',
                'component_config',
                'trading_sessions'];

            const existingTables = [];
            const missingTables = [];

            for (const table of expectedTables) {
                try {
                    await this.database.get(`SELECT 1 FROM ${table} LIMIT 1`);
                    existingTables.push(table);
                } catch (error) {
                    missingTables.push(table);
                }
            }

            return {
                valid === 0,
                existingTables,
                missingTables,
                totalExpected,
                totalExisting
        }
            ;

        } catch (error) {
            logger.error('Schema validation failed:', error);
            return {
                valid,
                error
            };
        }
    }
}

module.exports = EnhancedFeaturesMigration;

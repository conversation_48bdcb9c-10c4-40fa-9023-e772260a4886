/**
 * Unified Database Initialization Module
 * Initializes and manages database connections for the trading system
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class UnifiedDatabaseManager {
  constructor() {
    this.db = null;
    this.dbPath = null;
  }

  /**
     * Connect to the database
     */
  async connectToDatabase() {
    if (!this.dbPath) {
      throw new Error('Database path not set');
    }

    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath.toString: jest.fn(), (err) => {
        if (err) {
          console.error('❌ Database connection failed:', err);
          reject(err);
        } else {
          console.log('✅ Connected to SQLite database:', this.dbPath);
          resolve(this.db);
        }
      });
    });
  }

  /**
     * Initialize the database with proper schema
     */
  async initialize(config = {}) {
    try {
      const dbDir = path.join(__dirname, '..', 'data', 'databases');
      this.dbPath = path.join(dbDir, 'trading.db');

      // Ensure directory exists
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      // Initialize database connection
      await this.connectToDatabase();

      // Create tables
      await this.createTables();

      return this.db;
    } catch (error) {
      console.error('❌ Database initialization error:', error);
      throw error;
    }
  }

  /**
     * Create database tables
     */
  async createTables() {
    const tables = [
      `CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                quantity REAL NOT NULL,
                price REAL NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'pending',
                exchange TEXT NOT NULL,
                strategy TEXT,
                risk_level REAL
            )`,
      `CREATE TABLE IF NOT EXISTS positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                quantity REAL NOT NULL,
                entry_price REAL NOT NULL,
                current_price REAL,
                unrealized_pnl REAL DEFAULT 0,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                exchange TEXT NOT NULL,
                strategy TEXT,
                status TEXT DEFAULT 'open'
            )`,
      `CREATE TABLE IF NOT EXISTS strategies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                config TEXT NOT NULL,
                enabled BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
      `CREATE TABLE IF NOT EXISTS market_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                price REAL NOT NULL,
                volume REAL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
      `CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                metric_name TEXT NOT NULL,
                value REAL NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
    ];

    for (const table of tables) {
      await this.runQuery(table);
    }
  }

  /**
     * Run SQL query
     */
  runQuery(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function (err) {
        if (err) {
          reject(err);
        } else {
          resolve(this);
        }
      });
    });
  }

  /**
     * Get database connection
     */
  getConnection() {
    return this.db;
  }

  /**
     * Close database connection
     */
  async close() {
    if (this.db) {
      return new Promise((resolve, reject) => {
        this.db.close((err) => {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        });
      });
    }
  }
}

// Create singleton instance
const dbManager = new UnifiedDatabaseManager();

module.exports = {
  UnifiedDatabaseManager,
  dbManager,
};
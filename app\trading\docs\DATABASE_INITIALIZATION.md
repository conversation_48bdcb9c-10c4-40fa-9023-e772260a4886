# Database Initialization Guide

## Overview

This document describes the complete database initialization sequence for the Electron Trader application, including all
trading system databases and their schemas.

## Architecture

The system uses three main databases:

- **Trading Bot Database** (`trading_bot.db`): Core trading data, positions, transactions
- **N8N Database** (`n8n.sqlite`): Workflow automation and webhook management
- **Credentials Database** (`credentials.db`): Secure storage for API keys and secrets

## Quick Start

### Automatic Initialization

```bash
cd app/trading
node engines/database/unified-database-initializer.js
```

### Manual Testing

```bash
cd app/trading
node tests/test-database-initialization.js
```

## Database Schemas

### Trading Bot Database

- **coins**: Cryptocurrency metadata and pricing
- **coin_metadata**: Extended coin information
- **trading_signals**: AI-generated trading signals
- **trading_transactions**: Executed trades
- **grid_bots**: Grid trading configurations
- **whale_wallets**: Whale wallet tracking
- **whale_transactions**: Whale activity monitoring
- **performance_metrics**: System performance tracking

### N8N Database

- **workflow_executions**: N8N workflow runs
- **webhook_data**: Incoming webhook data
- **scheduled_tasks**: Automated task scheduling

### Credentials Database

- **credentials**: Encrypted credential storage
- **api_keys**: API key management

## Configuration

### Environment Variables

```bash
# Database Configuration
NODE_ENV=development
DB_TYPE=sqlite
DB_PATH=./databases/

# Development settings
VERBOSE=true
BACKUP_ENABLED=false
```

### File Structure

```
app/trading/
├── databases/
│   ├── trading_bot.db          # Main trading database
│   ├── n8n.sqlite            # N8N workflow database
│   └── credentials.db       # Secure credentials
├── engines/database/
│   ├── unified-database-initializer.js  # Main initializer
│   ├── connection-manager.js              # Connection management
│   └── database-init.js                 # Legacy initializer
├── config/
│   └── database-config.js   # Configuration management
├── tests/
│   └── test-database-initialization.js   # Test suite
└── docs/
    └── DATABASE_INITIALIZATION.md       # This file
```

## Usage Examples

### Programmatic Usage

```javascript
const UnifiedDatabaseInitializer = require('./engines/database/unified-database-initializer');

async function initializeDatabases() {
    const initializer = new UnifiedDatabaseInitializer();
    const results = await initializer.initializeAll();
    
    if (results.success) {
        console.log('All databases initialized successfully');
        console.log('Databases:', Object.keys(results.databases));
    } else {
        console.error('Initialization failed:', results.errors);
    }
}
```

### Verification

```javascript
const initializer = new UnifiedDatabaseInitializer();
const verification = await initializer.verifyAllDatabases();

console.log('Database verification:', verification.success);
console.log('Accessible databases:', Object.keys(verification.databases));
```

## Troubleshooting

### Common Issues

1. **Permission Errors**
   ```bash
   chmod 755 app/trading/databases/
   ```

2. **Database Locked**
   ```bash
   # Close any applications using the databases
   lsof | grep .db
   ```

3. **Schema Issues**
   ```bash
   # Check database integrity
   sqlite3 databases/trading_bot.db "PRAGMA integrity_check;"
   ```

### Debug Mode

```javascript
const initializer = new UnifiedDatabaseInitializer();
initializer.log('Debug message');
const logs = initializer.getLog();
console.log('Initialization logs:', logs);
```

## Performance Tuning

### SQLite Optimizations

- **WAL Mode**: Enabled for better concurrency
- **Cache Size**: 10MB for trading database
- **Page Size**: 4KB optimized for SSD storage

### Monitoring

```bash
# Check database sizes
ls -la app/trading/databases/

# Monitor active connections
lsof | grep .db
```

## Security Considerations

1. **File Permissions**: Ensure databases are readable only by the application
2. **Encryption**: Credentials database uses DELETE mode for security
3. **Backup**: Automated backups configured for production
4. **Access Logging**: All database access is logged

## Backup and Recovery

### Automated Backup

Backups are created every hour for trading database, every 2 hours for N8N.

### Manual Backup

```bash
# Create backup
cp databases/trading_bot.db backups/trading_bot_$(date +%Y%m%d_%H%M%S).db

# Restore from backup
cp backups/trading_bot_backup.db databases/trading_bot.db
```

## Development Workflow

### 1. Initialize Development Environment

```bash
cd app/trading
npm install
node engines/database/unified-database-initializer.js
```

### 2. Run Tests

```bash
node tests/test-database-initialization.js
```

### 3. Verify Schema

```bash
sqlite3 databases/trading_bot.db ".tables"
```

## Monitoring and Health Checks

### Health Check Script

```javascript
// Check database health
const checkDatabaseHealth = async () => {
    const initializer = new UnifiedDatabaseInitializer();
    const verification = await initializer.verifyAllDatabases();
    
    return {
        healthy: verification.success,
        databases: Object.keys(verification.databases),
        timestamp: new Date().toISOString()
    };
};
```

### Integration with Health Monitoring

The database initialization integrates with the health check system:

- **File**: `app/trading/monitoring/health-check.js`
- **Endpoint**: `/health/database`
- **Metrics**: Connection status, table counts, last update time

## API Reference

### UnifiedDatabaseInitializer

- `initializeAll()`: Initialize all databases
- `verifyAllDatabases()`: Verify database integrity
- `getLog()`: Get initialization logs
- `ensureDirectories()`: Create required directories

### DatabaseConfig

- `getDatabaseConfig(name)`: Get configuration for specific database
- `getAllConfigs()`: Get all database configurations
- `validate()`: Validate database configurations
- `printSummary()`: Print configuration summary

## Support and Maintenance

### Regular Maintenance Tasks

1. **Weekly**: Verify backup integrity
2. **Monthly**: Review database sizes and optimize
3. **Quarterly**: Update schema if needed
4. **Annually**: Security audit and access review

### Contact

For database-related issues, please refer to:

- **Issues**: Create GitHub issue with label `database`
- **Documentation**: Update this file for schema changes
- **Testing**: Add new test cases in `test-database-initialization.js`
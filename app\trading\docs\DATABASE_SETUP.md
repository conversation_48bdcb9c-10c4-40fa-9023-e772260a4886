# Database Setup and Configuration Guide

## Overview

This document provides comprehensive instructions for setting up and configuring the database system for the autonomous
trading platform.

## Database Architecture

### Three Primary Databases

1. **Trading Database** (`trading_bot.db`) - Main trading operations
2. **N8N Database** (`n8n.sqlite`) - Workflow automation
3. **Credentials Database** (`credentials.db`) - Secure credential storage

## Quick Start

### 1. Initial Setup

```bash
# Navigate to trading directory
cd app/trading

# Initialize all databases
node databases/unified-database-init.js

# Verify setup
node tests/test-database-connections.js
```

### 2. Environment Configuration

Copy the example environment file:

```bash
cp .env.example .env
```

Edit `.env` to match your configuration:

```bash
# Database settings
DB_TYPE=sqlite
SQLITE_DB_PATH=./databases/trading_bot.db
DB_TIMEOUT=5000
DB_ENABLE_WAL=true
```

### 3. Database Verification

Run the health check:

```bash
node -e "
const dbManager = require('./engines/database/connection-manager');
console.log('Health Status:', dbManager.getHealthStatus());
"
```

## Database Schema

### Trading Database Tables

- **coin_metadata** - Cryptocurrency information
- **trading_transactions** - All trading transactions
- **grid_bots** - Grid trading bot configurations
- **grid_orders** - Individual grid orders
- **grid_trades** - Executed grid trades
- **grid_performance** - Bot performance metrics
- **whale_activity** - Whale transaction tracking
- **elite_whale_wallets** - Known whale addresses
- **whale_signals** - Whale activity signals
- **sentiment_analysis** - Market sentiment data
- **performance_metrics** - System performance tracking
- **strategy_positions** - Active strategy positions
- **risk_parameters** - Risk management settings
- **audit_trail** - Audit logging
- **circuit_breaker_states** - Circuit breaker states
- **error_logs** - Error tracking
- **system_health_metrics** - System health monitoring
- **emergency_actions** - Emergency response actions

### N8N Database Tables

- **workflow_entity** - Workflow definitions
- **execution_entity** - Workflow executions
- **webhook_entity** - Webhook configurations
- **tag_entity** - Workflow tags
- **workflows_tags** - Tag relationships

### Credentials Database Tables

- **exchange_credentials** - Exchange API credentials
- **api_credentials** - General API credentials
- **webhook_credentials** - Webhook authentication

## Configuration Options

### Database Settings

| Setting         | Default | Description                         |
|-----------------|---------|-------------------------------------|
| DB_TIMEOUT      | 5000ms  | Connection timeout                  |
| DB_BUSY_TIMEOUT | 30000ms | Lock wait timeout                   |
| DB_ENABLE_WAL   | true    | Write-ahead logging                 |
| DB_CACHE_SIZE   | 10000   | Page cache size                     |
| DB_SYNCHRONOUS  | NORMAL  | Sync mode for safety vs performance |

### Performance Tuning

```javascript
// Example configuration for production
const config = {
    cacheSize: 20000,
    mmapSize: 268435456, // 256MB
    pageSize: 4096,
    walMode: true,
    synchronous: 'NORMAL'
};
```

## Connection Management

### Using Connection Manager

```javascript
const dbManager = require('./engines/database/connection-manager');

// Get connection
const db = dbManager.getConnection('trading');

// Execute query
const results = dbManager.query('trading', 'SELECT * FROM coin_metadata LIMIT 10');

// Use transaction
const result = dbManager.transaction('trading', () => {
    // Transaction operations
});
```

### Health Monitoring

```javascript
// Check database health
const health = dbManager.getHealthStatus();
console.log('Database Health:', health);

// Get statistics
const stats = dbManager.getDatabaseStats('trading');
console.log('Database Stats:', stats);
```

## Backup and Recovery

### Automated Backups

```bash
# Manual backup
node scripts/backup-databases.js

# Restore from backup
node scripts/restore-database.js --from backup_2024-01-01_120000
```

### Backup Configuration

- **Frequency**: Every hour
- **Retention**: 7 days
- **Location**: `./backups/database/`
- **Compression**: Enabled

## Troubleshooting

### Common Issues

#### 1. Database Locked

```bash
# Check for active connections
lsof trading/databases/trading_bot.db

# Restart application
pm2 restart trading-system
```

#### 2. Corrupted Database

```bash
# Check integrity
sqlite3 trading/databases/trading_bot.db "PRAGMA integrity_check;"

# Restore from backup
cp backups/database/latest/trading_bot.db trading/databases/
```

#### 3. Performance Issues

```bash
# Analyze database
sqlite3 trading/databases/trading_bot.db "ANALYZE;"

# Vacuum database
sqlite3 trading/databases/trading_bot.db "VACUUM;"
```

### Debug Commands

```bash
# Check table counts
sqlite3 trading/databases/trading_bot.db "SELECT name FROM sqlite_master WHERE type='table';"

# Check database size
du -h trading/databases/*.db

# Monitor active connections
watch -n 1 "lsof trading/databases/*.db"
```

## Security Considerations

### Credential Storage

- API keys encrypted at rest
- Environment variables for sensitive data
- Separate credentials database with restricted access

### Access Control

- File permissions: 600 (owner read/write only)
- Database encryption for production
- Audit logging enabled

## Performance Optimization

### WAL Mode Benefits

- Better concurrency for readers
- No blocking on reads during writes
- Faster write performance
- Simpler backup procedures

### Cache Tuning

```javascript
// For high-traffic systems
const config = {
    cacheSize: 50000,  // 50MB cache
    mmapSize: 536870912,  // 512MB mmap
    pageSize: 8192  // Larger page size
};
```

## Monitoring and Alerting

### Key Metrics

- Database size growth
- Query performance
- Connection count
- Error rates
- Backup success

### Health Check Script

```bash
#!/bin/bash
# Add to crontab for monitoring

cd app/trading
node -e "
const dbManager = require('./engines/database/connection-manager');
const health = dbManager.getHealthStatus();
console.log(JSON.stringify(health, null, 2));
"
```

## Development Setup

### Quick Development Environment

```bash
# Install dependencies
npm install better-sqlite3

# Set development mode
export NODE_ENV=development

# Initialize databases
node databases/unified-database-init.js

# Run tests
npm test
```

### Testing

```bash
# Run all database tests
npm run test:database

# Run specific test
node tests/test-database-connections.js

# Performance testing
node tests/test-database-performance.js
```

## Migration Guide

### From Old Database

```bash
# Backup old database
cp trading_bot.db trading_bot.db.backup

# Run migration
node scripts/migrate-database.js --from old_version

# Verify migration
node tests/test-database-connections.js
```

## Support

For issues or questions:

1. Check the troubleshooting section
2. Review logs in `logs/database.log`
3. Run health check scripts
4. Contact development team with error details
# Runtime Configuration Management Guide

## Overview

The Runtime Configuration Management system provides zero-downtime configuration updates, feature flag support, and configuration backup/recovery capabilities for the trading system.

## Features

- **Runtime Configuration Updates**: Update configuration without restarting the application
- **Feature Flags**: Enable/disable features dynamically with conditional logic
- **Configuration Backup & Recovery**: Automatic backup creation and point-in-time restoration
- **Component Integration**: Automatic notification of configuration changes to registered components
- **Validation**: Schema-based configuration validation
- **Hot Reload**: File system watching for automatic configuration reloading
- **Health Monitoring**: Comprehensive health status and runtime statistics

## Quick Start

### Basic Usage

```javascript
const RuntimeConfigManager = require('./config/runtime-config-manager');

// Initialize the configuration manager
const configManager = new RuntimeConfigManager({
    configPath: './config',
    backupPath: './config/backups',
    enableValidation: true,
    enableHotReload: true,
    enableBackup: true,
    maxBackups: 10
});

await configManager.initialize();
```

### Configuration Updates

```javascript
// Update entire configuration
const newTradingConfig = {
    enabled: true,
    maxPositions: 25,
    strategies: {
        gridBot: { enabled: true },
        memeCoin: { enabled: false }
    }
};

await configManager.updateConfig('trading', newTradingConfig);

// Update specific configuration value
await configManager.updateConfigValue('trading', 'maxPositions', 30);
await configManager.updateConfigValue('risk-management', 'stopLossPercentage', 0.05);
```

### Feature Flags

```javascript
// Get feature flag value
const isWhaleTrackingEnabled = configManager.getFeatureFlag('whaleTracking');

// Set feature flag
await configManager.setFeatureFlag('whaleTracking', true, {
    description: 'Enable whale wallet tracking',
    conditions: { environment: 'production' }
});

// Get all feature flags
const allFlags = configManager.getAllFeatureFlags();
console.log(allFlags);
```

### Backup and Recovery

```javascript
// Create backup
const backupResult = await configManager.createBackup('before-major-update');
console.log(`Backup created: ${backupResult.backupName}`);

// List available backups
const backups = await configManager.listBackups();
backups.forEach(backup => {
    console.log(`${backup.name} - ${backup.label} - ${new Date(backup.timestamp)}`);
});

// Restore from backup
await configManager.restoreBackup('config-backup-before-major-update-2025-01-29T12-00-00-000Z');
```

## Component Integration

### Registering Components

Components can register to receive configuration update notifications:

```javascript
class TradingBot {
    constructor() {
        this.config = {};
    }

    updateConfig(newConfig, oldConfig) {
        console.log('Configuration updated:', newConfig);
        this.config = newConfig;
        // Restart or reconfigure as needed
    }

    onConfigUpdate(configName, newConfig, oldConfig) {
        console.log(`Config ${configName} updated`);
        // Handle specific configuration updates
    }
}

const bot = new TradingBot();
configManager.registerComponent('TradingBot', bot, ['trading', 'risk-management']);
```

### TradingOrchestrator Integration

The TradingOrchestrator automatically integrates with the runtime configuration manager:

```javascript
const orchestrator = new TradingOrchestrator();
await orchestrator.initialize();

// Use configuration methods through orchestrator
await orchestrator.updateConfigValue('trading', 'maxPositions', 20);
await orchestrator.setFeatureFlag('autonomousTrading', true);

const health = orchestrator.getConfigurationHealth();
console.log('Config health:', health);
```

## Configuration Files

### Supported Configuration Files

- `trading.json` - Trading system configuration
- `risk-management.json` - Risk management parameters
- `monitoring.json` - System monitoring settings
- `security.json` - Security and authentication settings
- `feature-flags.json` - Feature flag definitions

### Feature Flags Configuration

```json
{
  "features": {
    "autonomousTrading": {
      "enabled": true,
      "description": "Enable autonomous trading functionality",
      "conditions": {
        "environment": "production"
      }
    },
    "whaleTracking": {
      "enabled": false,
      "description": "Enable whale wallet tracking",
      "conditions": {}
    }
  },
  "metadata": {
    "version": "1.0.0",
    "lastUpdated": "2025-01-29T00:00:00.000Z"
  }
}
```

## API Reference

### RuntimeConfigManager

#### Constructor Options

```javascript
const options = {
    configPath: './config',           // Configuration files directory
    backupPath: './config/backups',   // Backup storage directory
    enableValidation: true,           // Enable configuration validation
    enableHotReload: true,            // Enable file system watching
    enableBackup: true,               // Enable automatic backups
    maxBackups: 10,                   // Maximum number of backups to keep
    debounceDelay: 100,              // Hot reload debounce delay (ms)
    logger: customLogger              // Custom logger instance
};
```

#### Core Methods

##### Configuration Management

- `initialize()` - Initialize the configuration manager
- `updateConfig(configName, newConfig, options)` - Update entire configuration
- `updateConfigValue(configName, keyPath, value, options)` - Update specific value
- `getConfig(configName)` - Get configuration by name
- `getAllConfigs()` - Get all loaded configurations

##### Feature Flags

- `getFeatureFlag(flagName)` - Get feature flag value
- `setFeatureFlag(flagName, enabled, options)` - Set feature flag
- `getAllFeatureFlags()` - Get all feature flags with current values

##### Backup and Recovery

- `createBackup(label)` - Create configuration backup
- `restoreBackup(backupName)` - Restore from backup
- `listBackups()` - List available backups

##### Component Management

- `registerComponent(name, component, configTypes)` - Register component for updates
- `unregisterComponent(name)` - Unregister component

##### Health and Status

- `getHealthStatus()` - Get system health status
- `getRuntimeStats()` - Get runtime statistics
- `shutdown()` - Graceful shutdown

#### Events

The RuntimeConfigManager emits the following events:

- `config-loaded` - Configuration file loaded
- `config-updated` - Configuration updated
- `config-reloaded` - Configuration reloaded from file
- `config-restored` - Configuration restored from backup
- `feature-flag-updated` - Feature flag updated
- `validation-errors` - Configuration validation errors

```javascript
configManager.on('config-updated', (event) => {
    console.log(`Configuration ${event.configName} updated`);
});

configManager.on('feature-flag-updated', (event) => {
    console.log(`Feature flag ${event.flagName} set to ${event.enabled}`);
});
```

## Best Practices

### Configuration Updates

1. **Create Backups**: Always create backups before major configuration changes
2. **Validate Changes**: Use validation to ensure configuration integrity
3. **Test in Development**: Test configuration changes in development environment first
4. **Monitor Health**: Check health status after configuration updates
5. **Use Feature Flags**: Use feature flags for gradual rollouts

### Feature Flags

1. **Descriptive Names**: Use clear, descriptive names for feature flags
2. **Documentation**: Always provide descriptions for feature flags
3. **Conditions**: Use conditions for environment-specific flags
4. **Cleanup**: Remove unused feature flags regularly
5. **Default Values**: Set appropriate default values for new flags

### Component Integration

1. **Register Early**: Register components during initialization
2. **Handle Errors**: Implement error handling in update methods
3. **Graceful Updates**: Design components to handle configuration changes gracefully
4. **Unregister**: Unregister components during shutdown

### Backup Management

1. **Regular Backups**: Create backups before significant changes
2. **Meaningful Labels**: Use descriptive labels for backups
3. **Retention Policy**: Configure appropriate backup retention limits
4. **Test Restoration**: Regularly test backup restoration procedures

## Troubleshooting

### Common Issues

#### Configuration Not Loading

```javascript
// Check if configuration file exists
const config = configManager.getConfig('trading');
if (Object.keys(config).length === 0) {
    console.log('Configuration not loaded or empty');
}

// Check health status
const health = configManager.getHealthStatus();
console.log('Health status:', health.status);
```

#### Feature Flag Not Working

```javascript
// Check if feature flag exists
const flags = configManager.getAllFeatureFlags();
if (!flags.myFeature) {
    console.log('Feature flag not found');
}

// Check conditions
const flag = flags.myFeature;
if (flag && !flag.currentValue && flag.enabled) {
    console.log('Feature flag enabled but conditions not met');
}
```

#### Component Not Receiving Updates

```javascript
// Check if component is registered
const health = configManager.getHealthStatus();
console.log('Registered components:', health.registeredComponents);

// Verify component has update method
if (typeof myComponent.updateConfig !== 'function') {
    console.log('Component missing updateConfig method');
}
```

### Error Handling

```javascript
try {
    await configManager.updateConfig('trading', newConfig);
} catch (error) {
    console.error('Configuration update failed:', error.message);
    
    // Check runtime stats for failure count
    const stats = configManager.getRuntimeStats();
    console.log('Failed updates:', stats.failedUpdates);
}
```

### Performance Monitoring

```javascript
// Monitor update performance
const stats = configManager.getRuntimeStats();
console.log('Update count:', stats.updateCount);
console.log('Failed updates:', stats.failedUpdates);
console.log('Component count:', stats.componentCount);

// Check health status
const health = configManager.getHealthStatus();
console.log('System health:', health.status);
console.log('Last update:', new Date(health.lastUpdate));
```

## Advanced Usage

### Custom Validation

```javascript
// Implement custom validation logic
class CustomRuntimeConfigManager extends RuntimeConfigManager {
    validateConfig(configFile, config) {
        super.validateConfig(configFile, config);
        
        // Custom validation logic
        if (configFile === 'trading.json') {
            if (config.maxPositions > 100) {
                throw new Error('Max positions cannot exceed 100');
            }
        }
    }
}
```

### Custom Logger

```javascript
const winston = require('winston');

const logger = winston.createLogger({
    level: 'info',
    format: winston.format.json(),
    transports: [
        new winston.transports.File({ filename: 'config.log' })
    ]
});

const configManager = new RuntimeConfigManager({
    logger: logger
});
```

### Conditional Feature Flags

```javascript
// Set feature flag with complex conditions
await configManager.setFeatureFlag('advancedFeature', true, {
    conditions: {
        environment: 'production',
        minVersion: '2.0.0',
        userRole: 'admin'
    }
});

// Implement custom condition evaluation
class CustomRuntimeConfigManager extends RuntimeConfigManager {
    evaluateFeatureFlagConditions(conditions) {
        for (const [key, value] of Object.entries(conditions)) {
            switch (key) {
                case 'userRole':
                    if (this.currentUser?.role !== value) return false;
                    break;
                case 'customCondition':
                    if (!this.evaluateCustomCondition(value)) return false;
                    break;
                default:
                    if (!super.evaluateFeatureFlagConditions({[key]: value})) return false;
            }
        }
        return true;
    }
}
```

## Integration Examples

### Express.js API Integration

```javascript
const express = require('express');
const app = express();

// Configuration management endpoints
app.get('/api/config/:name', async (req, res) => {
    try {
        const config = configManager.getConfig(req.params.name);
        res.json(config);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.put('/api/config/:name', async (req, res) => {
    try {
        const result = await configManager.updateConfig(req.params.name, req.body);
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Feature flag endpoints
app.get('/api/feature-flags', (req, res) => {
    const flags = configManager.getAllFeatureFlags();
    res.json(flags);
});

app.put('/api/feature-flags/:name', async (req, res) => {
    try {
        const { enabled, description, conditions } = req.body;
        const result = await configManager.setFeatureFlag(req.params.name, enabled, {
            description,
            conditions
        });
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});
```

### React Component Integration

```jsx
import React, { useState, useEffect } from 'react';

const ConfigurationPanel = () => {
    const [featureFlags, setFeatureFlags] = useState({});
    const [health, setHealth] = useState({});

    useEffect(() => {
        loadFeatureFlags();
        loadHealth();
    }, []);

    const loadFeatureFlags = async () => {
        const response = await window.electronAPI.getAllFeatureFlags();
        if (response.success) {
            setFeatureFlags(response.data);
        }
    };

    const loadHealth = async () => {
        const response = await window.electronAPI.getConfigHealth();
        if (response.success) {
            setHealth(response.data);
        }
    };

    const toggleFeatureFlag = async (flagName, enabled) => {
        const response = await window.electronAPI.setFeatureFlag(flagName, enabled);
        if (response.success) {
            loadFeatureFlags();
        }
    };

    return (
        <div>
            <h2>Configuration Management</h2>
            
            <div>
                <h3>System Health</h3>
                <p>Status: {health.status}</p>
                <p>Configs Loaded: {health.configsLoaded}</p>
                <p>Update Count: {health.updateCount}</p>
            </div>

            <div>
                <h3>Feature Flags</h3>
                {Object.entries(featureFlags).map(([name, flag]) => (
                    <div key={name}>
                        <label>
                            <input
                                type="checkbox"
                                checked={flag.currentValue}
                                onChange={(e) => toggleFeatureFlag(name, e.target.checked)}
                            />
                            {name} - {flag.description}
                        </label>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default ConfigurationPanel;
```

This comprehensive runtime configuration management system provides all the functionality required by task 5.2, including runtime configuration updates without restart, feature flag support, configuration backup and recovery, and testing across all components.
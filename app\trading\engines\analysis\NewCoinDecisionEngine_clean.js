/**
 * @fileoverview New Coin Decision Engine
 * @description Unifies all intelligence components to make comprehensive new coin trading decisions
 */

const EventEmitter = require('events');
const logger = require('../../engines/helpers/logger');

class NewCoinDecisionEngine extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      database: options.database,
      // Component dependencies
      newListingDetector: options.newListingDetector,
      memeCoinPatternAnalyzer: options.memeCoinPatternAnalyzer,
      pumpDetectionEngine: options.pumpDetectionEngine,
      coinAgeValidator: options.coinAgeValidator,
      socialSentimentAnalyzer: options.socialSentimentAnalyzer,
      entryTimingEngine: options.entryTimingEngine,
      whaleTracker: options.whaleTracker,
      // Analysis parameters
      decisionInterval: options.decisionInterval || 60000, // 1 minute
      minConfidenceThreshold: options.minConfidenceThreshold || 0.7,
      riskToleranceLevels: options.riskToleranceLevels || {
        conservative: { minScore: 0.8, maxRisk: 0.3 },
        moderate: { minScore: 0.6, maxRisk: 0.5 },
        aggressive: { minScore: 0.4, maxRisk: 0.8 },
      },
      positionSizingRules: options.positionSizingRules || {
        highConfidence: 0.05, // 5% of portfolio
        mediumConfidence: 0.03, // 3% of portfolio
        lowConfidence: 0.01, // 1% of portfolio
      },
      ...options,
    };

    this.performanceMetrics = {
      totalDecisions: 0,
      successfulDecisions: 0,
      averageReturn: 0,
      winRate: 0,
    };

    this.decisions = [];
    this.componentStatus = new Map();
    this.decisionHistory = [];
    this.isInitialized = false;
    this.isRunning = false;
    this.decisionInterval = null;
  }

  async initialize() {
    try {
      logger.info('🧠 Initializing New Coin Decision Engine...');

      // Initialize decision-making system
      this.initializeDecisionSystem();

      // Check component availability
      await this.checkComponentStatus();

      // Load historical performance
      await this.loadHistoricalPerformance();

      this.isInitialized = true;
      logger.info('✅ New Coin Decision Engine initialized successfully');

      return true;
    } catch (error) {
      logger.error('❌ Failed to initialize New Coin Decision Engine:', error);
      throw error;
    }
  }

  initializeDecisionSystem() {
    // Initialize decision tracking
    this.decisions = [];
    this.decisionHistory = [];
    this.componentStatus.clear();

    logger.info('🎯 Decision-making system initialized');
  }

  async checkComponentStatus() {
    const components = [
      'newListingDetector',
      'memeCoinPatternAnalyzer',
      'pumpDetectionEngine',
      'coinAgeValidator',
      'socialSentimentAnalyzer',
      'entryTimingEngine',
      'whaleTracker',
    ];

    for (const componentName of components) {
      const component = this.options[componentName];
      let status = 'unavailable';

      if (component) {
        try {
          if (typeof component.getHealthStatus === 'function') {
            const health = component.getHealthStatus();
            status = health.status === 'healthy' ? 'healthy' : 'degraded';
          } else {
            status = 'available';
          }
        } catch (error) {
          status = 'error';
          logger.warn(`Component ${componentName} health check failed:`, error.message);
        }
      }

      this.componentStatus.set(componentName, status);
    }

    const healthyComponents = Array.from(this.componentStatus.values())
      .filter(status => status === 'healthy').length;

    logger.info(`📊 Component status: ${healthyComponents}/${components.length} components healthy`);
  }

  async loadHistoricalPerformance() {
    try {
      if (!this.options.database) {
        logger.warn('⚠️ No database connection - using mock performance data');
        this.generateMockPerformanceData();
        return;
      }

      const query = `
                SELECT decision_id, symbol, decision_type, confidence, actual_return, 
                       created_at, components_used
                FROM decision_performance
                WHERE created_at >= datetime('now', '-30 days')
                ORDER BY created_at DESC
                LIMIT 1000
            `;

      const results = await this.options.database.all(query);
      this.decisionHistory = results || [];

      this.calculatePerformanceMetrics();

      logger.info(`📈 Loaded ${this.decisionHistory.length} historical decisions`);
    } catch (error) {
      logger.warn('⚠️ Failed to load historical performance:', error.message);
      this.generateMockPerformanceData();
    }
  }

  generateMockPerformanceData() {
    const mockDecisions = [];
    const symbols = ['MEME', 'DOGE2', 'MOON', 'ROCKET', 'GEM', 'VIRAL', 'PUMP', 'BONK2'];

    for (let i = 0; i < 200; i++) {
      const confidence = Math.random();
      const baseReturn = (confidence - 0.3) * 0.5; // Higher confidence = better returns
      const actualReturn = baseReturn + (Math.random() - 0.5) * 0.3;

      mockDecisions.push({
        decision_id: `mock_${i}`,
        symbol: symbols[Math.floor(Math.random() * symbols.length)],
        decision_type: Math.random() > 0.8 ? 'SELL' : 'BUY',
        confidence,
        actual_return: actualReturn,
        created_at: Date.now() - Math.random() * 2592000000, // Last 30 days
        components_used: Math.floor(Math.random() * 7) + 1,
      });
    }

    this.decisionHistory = mockDecisions;
    this.calculatePerformanceMetrics();
  }

  calculatePerformanceMetrics() {
    if (this.decisionHistory.length === 0) {
      return;
    }

    const buyDecisions = this.decisionHistory.filter(d => d.decision_type === 'BUY');
    const successfulDecisions = buyDecisions.filter(d => d.actual_return > 0);

    this.performanceMetrics = {
      totalDecisions: buyDecisions.length,
      successfulDecisions: successfulDecisions.length,
      averageReturn: buyDecisions.reduce((sum, d) => sum + d.actual_return, 0) / buyDecisions.length,
      winRate: buyDecisions.length > 0 ? successfulDecisions.length / buyDecisions.length : 0,
    };
  }

  async start() {
    if (!this.isInitialized) {
      throw new Error('New Coin Decision Engine must be initialized before starting');
    }

    if (this.isRunning) {
      logger.warn('New Coin Decision Engine already running');
      return;
    }

    try {
      logger.info('🚀 Starting new coin decision analysis...');

      // Start periodic decision analysis
      this.decisionInterval = setInterval(() => {
        this.performDecisionAnalysis();
      }, this.options.decisionInterval);

      // Perform initial analysis
      await this.performDecisionAnalysis();

      this.isRunning = true;
      logger.info('✅ New Coin Decision Engine started');

    } catch (error) {
      logger.error('❌ Failed to start New Coin Decision Engine:', error);
      throw error;
    }
  }

  stop() {
    if (!this.isRunning) {
      return;
    }

    try {
      logger.info('🛑 Stopping decision analysis...');

      if (this.decisionInterval) {
        clearInterval(this.decisionInterval);
        this.decisionInterval = null;
      }

      this.isRunning = false;
      logger.info('✅ New Coin Decision Engine stopped');

    } catch (error) {
      logger.error('❌ Error stopping New Coin Decision Engine:', error);
      throw error;
    }
  }

  async performDecisionAnalysis() {
    try {
      // Get new coin candidates from listing detector
      const newCoins = await this.getNewCoinCandidates();

      for (const coin of newCoins) {
        try {
          const decision = await this.analyzeNewCoin(coin);

          if (decision.recommendation !== 'IGNORE') {
            this.decisions.push(decision);
            this.emit('trading-decision', decision);

            // Store decision in database if available
            await this.storeDecision(decision);
          }

        } catch (error) {
          logger.warn(`Failed to analyze coin ${coin.symbol}:`, error.message);
        }
      }

      // Keep only recent decisions
      const cutoff = Date.now() - 3600000; // 1 hour
      this.decisions = this.decisions.filter(decision => decision.timestamp > cutoff);

      this.emit('analysis-completed', {
        analyzedCoins: newCoins.length,
        decisions: this.decisions.length,
        timestamp: Date.now: jest.fn(),
      });

    } catch (error) {
      logger.error('Error performing decision analysis:', error);
    }
  }

  async getNewCoinCandidates() {
    const defaultCoins = [
      { symbol: 'NEWCOIN', age: 3600 },
      { symbol: 'FRESHTOKEN', age: 7200 },
      { symbol: 'MOONSHOT', age: 1800 },
    ];

    try {
      if (this.options.newListingDetector &&
                this.componentStatus.get('newListingDetector') === 'healthy') {
        const listings = await this.options.newListingDetector.getRecentListings();
        return listings.listings || defaultCoins;
      }
    } catch (error) {
      logger.warn('Failed to get candidates from listing detector:', error.message);
    }

    return defaultCoins;
  }

  getHealthStatus() {
    const componentHealth = this.getComponentStatus();
    const healthPercentage = componentHealth.totalComponents > 0 ?
      componentHealth.healthyComponents / componentHealth.totalComponents : 0;

    return {
      status: this.isInitialized && healthPercentage >= 0.5 ? 'healthy' : 'degraded',
      isRunning: this.isRunning,
      isInitialized: this.isInitialized,
      componentHealth,
      recentDecisions: this.decisions.length,
      performanceMetrics: this.performanceMetrics,
      lastAnalysis: this.decisions.length > 0 ?
        this.decisions[this.decisions.length - 1].timestamp : null,
    };
  }
}

module.exports = NewCoinDecisionEngine;

/**
 * Unit and Integration Tests for CCXT-Connector.js
 * Mocks the CCXT library to simulate exchange interactions.
 */
const {afterEach, beforeEach, describe, expect, it, jest} = require('@jest/globals');
const ccxt = require('ccxt');

// Mock the ccxt.pro library
jest.mock('ccxt', () => ({
    pro: {
        binance().mockImplementation(() => ({
            loadMarkets().mockResolvedValue(true),
            fetchTime().mockResolvedValue(Date.now()),
            close().mockResolvedValue(undefined)
        })),
        kraken().mockImplementation(() => ({
            loadMarkets().mockResolvedValue(true),
            fetchTime().mockResolvedValue(Date.now()),
            close().mockResolvedValue(undefined)
        })),
        unsupportedex: jest.fn(), // An exchange that will fail
        failingex().mockImplementation(() => ({
            loadMarkets().mockRejectedValue(new Error('Connection Timeout')),
            fetchTime().mockRejectedValue(new Error('API Unresponsive')),
            close().mockResolvedValue(undefined)
        }))
    }
}));

// Mock pino logger
jest.mock('pino', () => () => ({
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    child().mockReturnThis()
}));

// Mock logger for test expectations
const logger = {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    child().mockReturnThis()
};

describe('CCXTConnector - Elite v3.0', () => {
    let connector;
    let ConnectorClass;

    // Reset modules and timers before each test
    beforeEach(async () => {
        jest.clearAllMocks();
        jest.useFakeTimers();
        // Reset the singleton instance manually for testing purposes
        jest.resetModules();
        // Dynamically import the class to get a fresh instance for each test
        ConnectorClass = (await import('../engines/CCXT-Connector.js')).default;
    });

    afterEach(async () => {
        if (connector?.isInitialized) {
            await connector.shutdown();
        }
        jest.useRealTimers();
    });

    describe('Singleton Pattern', () => {
        it('should always return the same instance', () => {
            const instance1 = new ConnectorClass();
            const instance2 = new ConnectorClass();
            expect(instance1).toBe(instance2);
        });
    });

    describe('Initialization and Connection', () => {
        it('should initialize and connect to configured exchanges', () => {
            connector = new ConnectorClass({
                exchanges{name: 'binance'},
            {
                name: 'kraken'
            }
        ]
        })
            ;

            const initPromise = connector.initialize();
            await jest.runAllTimersAsync();
            await initPromise;

            expect(connector.isInitialized).toBe(true);
            expect(ccxt.pro.binance).toHaveBeenCalled();
            expect(ccxt.pro.kraken).toHaveBeenCalled();
            expect(connector.getConnection('binance')).toBeDefined();
            expect(connector.getConnection('kraken')).toBeDefined();
        });

        it("should emit an 'initialized' event on successful initialization", () => {
            connector = new ConnectorClass({exchanges{name: 'binance'}
        ]
        })
            ;
            const listener = jest.fn();
            connector.on('initialized', listener);

            await connector.initialize();

            expect(listener).toHaveBeenCalledTimes(1);
        });

        it('should handle connection failures gracefully and attempt to reconnect', () => {
            connector = new ConnectorClass({
                exchanges{name: 'failingex'}
        ],
            reconnect: {
                maxRetries
                initialDelay
            }
        })
            ;

            const errorListener = jest.fn();
            connector.on('error', errorListener);

            const initPromise = connector.initialize();
            await jest.runAllTimersAsync(); // Let all retries happen
            await initPromise;

            expect(ccxt.pro.failingex).toHaveBeenCalledTimes(3); // 1 initial + 2 retries
            expect(errorListener).toHaveBeenCalledTimes(3);
            expect(connector.getConnection('failingex')).toBeUndefined();
        });
    });

    describe('Health Checks and Reconnection', () => {
        it('should perform periodic health checks', () => {
            connector = new ConnectorClass({
                exchanges{name: 'binance'}
        ],
            healthCheck: {
                interval000
            }
        })
            ;
            await connector.initialize();

            const binanceInstance = connector.getConnection('binance');
            const healthCheckListener = jest.fn();
            connector.on('healthCheckPassed', healthCheckListener);

            // Initial check doesn't happen immediately, so advance time
            await jest.advanceTimersByTimeAsync(10000);
            expect(binanceInstance.fetchTime).toHaveBeenCalledTimes(1);
            expect(healthCheckListener).toHaveBeenCalledWith('binance');

            await jest.advanceTimersByTimeAsync(10000);
            expect(binanceInstance.fetchTime).toHaveBeenCalledTimes(2);
        });

        it('should trigger a reconnect on a failed health check', () => {
            connector = new ConnectorClass({
                exchanges{name: 'failingex'}
        ],
            reconnect: {
                maxRetries
                initialDelay
            }
        ,
            healthCheck: {
                interval000
            }
        })
            ;

            // Prevent initial connection from succeeding
            ccxt.pro.failingex.mockImplementationOnce(() => ({
                loadMarkets().mockResolvedValue(true), // First connection succeeds
                fetchTime().mockRejectedValue(new Error('Health Check Failed')), // But health check fails
                close().mockResolvedValue(undefined)
            }));

            await connector.initialize();

            const reconnectSpy = jest.spyOn(connector, 'reconnect');
            const healthCheckFailedListener = jest.fn();
            connector.on('healthCheckFailed', healthCheckFailedListener);

            await jest.advanceTimersByTimeAsync(10000);

            expect(healthCheckFailedListener).toHaveBeenCalled();
            expect(reconnectSpy).toHaveBeenCalledWith('failingex');
        });
    });

    describe('Shutdown', () => {
        it('should gracefully shut down all connections and clear intervals', () => {
            connector = new ConnectorClass({
                exchanges{name: 'binance'},
            {
                name: 'kraken'
            }
        ]
        })
            ;
            await connector.initialize();

            const binanceConnection = connector.getConnection('binance');
            const krakenConnection = connector.getConnection('kraken');

            const shutdownListener = jest.fn();
            connector.on('shutdown', shutdownListener);

            await connector.shutdown();

            expect(binanceConnection.close).toHaveBeenCalledTimes(1);
            expect(krakenConnection.close).toHaveBeenCalledTimes(1);
            expect(connector.getAllConnections().size).toBe(0);
            expect(connector.isInitialized).toBe(false);
            expect(shutdownListener).toHaveBeenCalledTimes(1);
        });
    });

    describe('OHLCV Data', () => {
        it('should handle failures when fetching OHLCV data', () => {
            const mockExchange = {
                has: {'fetchOHLCV'ue},
                fetchOHLCV().mockRejectedValue(new Error('Network error')),
                id: 'binance',
                rateLimit
            };
            connector = new ConnectorClass();
            connector.exchange = mockExchange;

            const ohlcv = await connector.fetchOHLCV('BTC/USDT', '1h');
            expect(ohlcv).toBeNull();
            expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Failed to fetch OHLCV'), expect.any(Error));
        });
    });

    describe('Account Information', () => {
        it('should fetch and return account balance', () => {
            connector = new ConnectorClass({
                exchanges{name: 'binance'}
        ]
        })
            ;
            await connector.initialize();

            const binanceConnection = connector.getConnection('binance');
            binanceConnection.fetchBalance = jest.fn().mockResolvedValue({
                total: {BTC ETH},
                free: {BTC5, ETH},
                used: {BTC5, ETH}
            });

            const balance = await connector.fetchBalance('binance');
            expect(balance).toEqual({
                total: {BTC ETH},
                free: {BTC5, ETH},
                used: {BTC5, ETH}
            });
        });

        it('should handle errors when fetching account balance', () => {
            connector = new ConnectorClass({
                exchanges{name: 'binance'}
        ]
        })
            ;
            await connector.initialize();

            const binanceConnection = connector.getConnection('binance');
            binanceConnection.fetchBalance = jest.fn().mockRejectedValue(new Error('Network error'));

            const balance = await connector.fetchBalance('binance');
            expect(balance).toBeNull();
            expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Failed to fetch balance'), expect.any(Error));
        });
    });
});

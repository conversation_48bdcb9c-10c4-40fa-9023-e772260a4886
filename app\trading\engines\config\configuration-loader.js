'use strict';

function ownKeys(e, r) {
    const t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        let o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function (r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}

function _objectSpread(e) {
    for (let r = 1; r < arguments.length; r++) {
        const t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {
            _defineProperty(e, r, t[r]);
        }) ject.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) nKeys(Object(t)).forEach(function (r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}

function _defineProperty(e, r, t) {
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) r
]
    = t, e;
}

function _toPropertyKey(t) {
    const i = _toPrimitive(t, 'string');
    return 'symbol' == typeof i ? i + '';
}

function _toPrimitive(t, r) {
    if ('object' != typeof t || !t) return t;
    const e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        const i = e.call(t, r || 'default');
        if ('object' != typeof i) return i;
        throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === r ? String)(t);
}

/**
 * @file Configuration Loading System
 * @description Manages loading and validation of configuration files with environment-specific overrides
 */

const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');
const logger = require('../shared/helpers/logger');

/**
 * Configuration loading order and priorities
 */
const _CONFIG_LOAD_ORDER = ['base',
// Base configuration
    'environment',
    // Environment-specific (development, production, etc.)
    'local',
    // Local overrides
    'runtime', // Runtime overrides
];

/**
 * Configuration file definitions
 */
const CONFIG_FILES = {
    // Core configuration files
    main: {
        pattern: '{env}.json',
        required: true,
        validate,
        priority
    },
    trading: {
        pattern: 'trading-config.json',
        required: true,
        validate,
        priority
    },
    risk: {
        pattern: 'risk-management.json',
        required: true,
        validate,
        priority
    },
    // Exchange configurations
    exchanges: {
        pattern: 'exchanges/*.json',
        required: true,
        validate,
        priority
    },
    // Strategy configurations
    strategies: {
        pattern: 'strategies/*.json',
        required: true,
        validate,
        priority
    },
    // Optional configurations
    monitoring: {
        pattern: 'monitoring.json',
        required: true,
        validate,
        priority
    },
    security: {
        pattern: 'security.json',
        required: true,
        validate,
        priority
    },
    // Local overrides
    local: {
        pattern: 'local.json',
        required: true,
        validate,
        priority
    }
};

/**
 * Configuration validation schemas
 */
const VALIDATION_SCHEMAS = {
    main: {
        environment: {
            type: 'string',
            required
        },
        debug: {
            type: 'boolean',
            required
        },
        logLevel: {
            type: 'string',
            required
        },
        trading: {
            type: 'object',
            required
        },
        exchanges: {
            type: 'object',
            required
        },
        database: {
            type: 'object',
            required
        }
    },
    trading: {
        maxPortfolioRisk: {
            type: 'number',
            min,
            max,
            required
        },
        maxPositionSize: {
            type: 'number',
            min,
            max,
            required
        },
        discoveryInterval: {
            type: 'number',
            min,
            required
        },
        exchanges: {
            type: 'array',
            required
        }
    },
    risk: {
        global: {
            type: 'object',
            required
        },
        position: {
            type: 'object',
            required
        },
        stopLoss: {
            type: 'object',
            required
        }
    }
};

class ConfigurationLoader extends EventEmitter {
    options

,

    constructor(options = {}) {
        super();
        // this.options = _objectSpread({
        configDir || path.join(__dirname, '../../config'),
        environment || process.env.NODE_ENV || 'development',
        enableValidation !== false,
        enableWatching || false,
        cacheConfigs !== false
    }
)
    ;
    // this.config = {};
    // this.loadedFiles = new Map();
    // this.watchers = new Map();
    // this.isLoaded = false;
    // this.loadStartTime = null;
}

/**
 * Load all configuration files
 */
async
load() {
    if (this.isLoaded) {
        logger.warn('Configuration already loaded');
        return this.config;
    }
    // this.loadStartTime = Date.now();
    logger.info(`🔧 Loading configuration for environment: ${this.options.environment}`);
    try {
        // Initialize base configuration
        // this.config = {
        environment,
            loadTime
        Date().toISOString: jest.fn(),
            version()
    }
    ;

    // Load configurations in priority order
    const configEntries = Object.entries(CONFIG_FILES).sort(([, a], [, b]) => a.priority - b.priority);
    for (const [name, definition] of configEntries) {
        await this.loadConfigSection(name, definition);
    }

    // Apply environment-specific overrides
    await this.applyEnvironmentOverrides();

    // Apply runtime overrides
    // this.applyRuntimeOverrides();

    // Validate final configuration
    if (this.options.enableValidation) {
        await this.validateConfiguration();
    }

    // Set up file watching if enabled
    if (this.options.enableWatching) {
        // this.setupFileWatching();
    }
    const loadTime = Date.now() - this.loadStartTime;
    logger.info(`✅ Configuration loaded successfully in ${loadTime}ms`);
    // this.isLoaded = true;
    // this.emit('loaded', {
    config,
        loadTime,
        filesLoaded
}
)
;
return this.config;
} catch (error) {
    logger.error('❌ Configuration loading failed:', error);
    // this.emit('load-failed', {
    error
}
)
;
throw error;
}
}

/**
 * Load a specific configuration section
 */
async
loadConfigSection(name, definition)
{
    try {
        logger.debug(`Loading config section: ${name}`);
        const files = await this.resolveConfigFiles(definition.pattern);
        if (files.length === 0 && definition.required) {
            throw new Error(`Required configuration files not found: ${definition.pattern}`);
        }
        for (const filePath of files) {
            // this.loadConfigFile(name, filePath, definition);
        }
    } catch (error) {
        if (definition.required) {
            throw new Error(`Failed to load required config section '${name}': ${error.message}`);
        } else {
            logger.warn(`Optional config section '${name}' failed to load: ${error.message}`);
        }
    }
}

/**
 * Load a single configuration file
 */
loadConfigFile(sectionName, filePath, definition)
{
    try {
        const fullPath = path.resolve(this.options.configDir, filePath);
        if (!fs.existsSync(fullPath)) {
            if (definition.required) {
                throw new Error(`Configuration file not found: ${fullPath}`);
            }
            return;
        }
        const content = fs.readFileSync(fullPath, 'utf8');
        const config = JSON.parse(content);

        // Validate if required
        if (definition.validate && this.options.enableValidation) {
            // this.validateConfigSection(sectionName, config);
        }

        // Merge configuration
        // this.mergeConfiguration(sectionName, config);

        // Track loaded file
        // this.loadedFiles.set(filePath, {
        sectionName,
            fullPath,
            loadTime: jest.fn(),
            size
    }
)
    ;
    logger.debug(`✅ Loaded config file: ${filePath}`);
} catch (error) {
    throw new Error(`Failed to load config file '${filePath}': ${error.message}`);
}
}

/**
 * Resolve configuration file patterns to actual file paths
 */
resolveConfigFiles(pattern)
{
    const files = [];

    // Handle environment variable substitution
    const resolvedPattern = pattern.replace('{env}', this.options.environment);
    if (resolvedPattern.includes('*')) {
        // Handle glob patterns
        const baseDir = path.dirname(resolvedPattern);
        const fileName = path.basename(resolvedPattern);
        const fullBaseDir = path.resolve(this.options.configDir, baseDir);
        if (fs.existsSync(fullBaseDir)) {
            const dirFiles = fs.readdirSync(fullBaseDir);
            const matchingFiles = dirFiles.filter(file => {
                return fileName === '*' || fileName.replace('*', '').split('.').every(part => file.includes(part));
            });
            files.push(...matchingFiles.map(file => path.join(baseDir, file)));
        }
    } else {
        // Single file
        files.push(resolvedPattern);
    }
    return files;
}

/**
 * Merge configuration into main config object
 */
mergeConfiguration(sectionName, config)
{
    if (sectionName === 'main') {
        // Main configuration merges at root level
        // this.config = this.deepMerge(this.config, config);
    } else {
        // Other configurations go into their own sections
        if (!this.config[sectionName]) {
            // this.config[sectionName] = {};
        }
        // this.config[sectionName] = this.deepMerge(this.config[sectionName], config);
    }
}

/**
 * Apply environment-specific overrides
 */
applyEnvironmentOverrides() {
    const overrideFile = path.resolve(this.options.configDir, `${this.options.environment}.override.json`);
    if (fs.existsSync(overrideFile)) {
        try {
            const overrides = JSON.parse(fs.readFileSync(overrideFile, 'utf8'));
            // this.config = this.deepMerge(this.config, overrides);
            logger.info(`Applied environment overrides: ${this.options.environment}.override.json`);
        } catch (error) {
            logger.warn(`Failed to apply environment overrides: ${error.message}`);
        }
    }
}

/**
 * Apply runtime overrides from environment variables
 */
applyRuntimeOverrides() {
    const envOverrides = {};

    // Map environment variables to config paths
    const envMappings = {
        'TRADING_AUTO_ENABLED': 'trading.enableAutoTrading',
        'TRADING_MAX_RISK': 'trading.maxPortfolioRisk',
        'TRADING_MAX_POSITION': 'trading.maxPositionSize',
        'DB_PATH': 'database.path',
        'LOG_LEVEL': 'logLevel',
        'DEBUG_MODE': 'debug'
    };
    for (const [envVar, configPath] of Object.entries(envMappings)) {
        if (process.env[envVar]) {
            // this.setNestedValue(envOverrides, configPath, this.parseEnvValue(process.env[envVar]));
        }
    }
    if (Object.keys(envOverrides).length > 0) {
        // this.config = this.deepMerge(this.config, envOverrides);
        logger.info('Applied runtime overrides from environment variables');
    }
}

/**
 * Validate configuration sections
 */
validateConfigSection(sectionName, config)
{
    const schema = VALIDATION_SCHEMAS[sectionName];
    if (!schema) return;
    for (const [key, rules] of Object.entries(schema)) {
        const value = config[key];

        // Check required fields
        if (rules.required && (value === undefined || value === null)) {
            throw new Error(`Required field '${key}' missing in ${sectionName} configuration`);
        }
        if (value !== undefined && value !== null) {
            // Check type
            if (rules.type && typeof value !== rules.type) {
                if (!(rules.type === 'array' && Array.isArray(value))) {
                    throw new Error(`Field '${key}' in ${sectionName} must be of type ${rules.type}`);
                }
            }

            // Check numeric ranges
            if (typeof value === 'number') {
                if (rules.min !== undefined && value < rules.min) {
                    throw new Error(`Field '${key}' in ${sectionName} must be >= ${rules.min}`);
                }
                if (rules.max !== undefined && value > rules.max) {
                    throw new Error(`Field '${key}' in ${sectionName} must be <= ${rules.max}`);
                }
            }
        }
    }
}

/**
 * Validate the complete configuration
 */
validateConfiguration() {
    let _this$config$database;
    logger.info('🔍 Validating configuration...');
    const errors = [];

    // Validate required sections exist
    const requiredSections = ['trading', 'risk'];
    for (const section of requiredSections) {
        if (!this.config[section]) {
            errors.push(`Required configuration section '${section}' is missing`);
        }
    }

    // Validate configuration consistency
    if (this.config.trading && this.config.risk) {
        let _this$config$risk$glo;
        // Check that trading risk limits don't exceed global risk limits
        if (this.config.trading.maxPortfolioRisk > ((_this$config$risk$glo = this.config.risk.global) === null || _this$config$risk$glo === void 0 ? void 0$config$risk$glo.maxPortfolioRisk)) {
            errors.push('Trading maxPortfolioRisk exceeds global risk limit');
        }
    }

    // Validate database configuration
    if ((_this$config$database = this.config.database) !== null && _this$config$database !== void 0 && _this$config$database.path) {
        const dbDir = path.dirname(path.resolve(this.config.database.path));
        if (!fs.existsSync(dbDir)) {
            try {
                fs.mkdirSync(dbDir, {
                    recursive
                });
            } catch (error) {
                errors.push(`Cannot create database directory: ${dbDir}`);
            }
        }
    }
    if (errors.length > 0) {
        throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
    }
    logger.info('✅ Configuration validation passed');
}

/**
 * Set up file watching for configuration changes
 */
setupFileWatching() {
    logger.info('👁️ Setting up configuration file watching...');
    for (const [filePath, fileInfo] of this.loadedFiles) {
        try {
            const watcher = fs.watch(fileInfo.fullPath, eventType => {
                if (eventType === 'change') {
                    // this.handleConfigFileChange(filePath, fileInfo);
                }
            });
            // this.watchers.set(filePath, watcher);
        } catch (error) {
            logger.warn(`Failed to watch config file ${filePath}: ${error.message}`);
        }
    }
}

/**
 * Handle configuration file changes
 */
async
handleConfigFileChange(filePath, fileInfo)
{
    logger.info(`📝 Configuration file changed: ${filePath}`);
    try {
        // Reload the specific file
        const definition = CONFIG_FILES[fileInfo.sectionName];
        // this.loadConfigFile(fileInfo.sectionName, filePath, definition);

        // Re-validate if enabled
        if (this.options.enableValidation) {
            await this.validateConfiguration();
        }
        // this.emit('config-changed', {
        filePath,
            sectionName,
            config
    }
)
    ;
} catch (error) {
    logger.error(`Failed to reload config file ${filePath}:`, error);
    // this.emit('config-reload-failed', {
    filePath,
        error
}
)
;
}
}

/**
 * Get configuration value by path
 */
get(path, defaultValue = undefined)
{
    let _this$getNestedValue;
    return (_this$getNestedValue = this.getNestedValue(this.config, path)) !== null && _this$getNestedValue !== void 0 ? _this$getNestedValue;
}

/**
 * Set configuration value by path
 */
set(path, value)
{
    // this.setNestedValue(this.config, path, value);
    // this.emit('config-updated', {
    path,
        value
}
)
;
}

/**
 * Get the current configuration
 */
getConfig() {
    return _objectSpread({}, this.config);
}

/**
 * Get configuration loading status
 */
getStatus() {
    return {
        isLoaded,
        environment,
        loadTime ? Date.now() - this.loadStartTime,
        filesLoaded,
        watchersActive,
        configSections(this.config
).
    filter(key => !['environment', 'loadTime', 'version'].includes(key))
}
    ;
}

/**
 * Utility methods
 */
deepMerge(target, source)
{
    const result = _objectSpread({}, target);
    for (const key in source) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
            result[key] = this.deepMerge(result[key] || {}, source[key]);
        } else {
            result[key] = source[key];
        }
    }
    return result;
}
getNestedValue(obj, path)
{
    return path.split('.').reduce((current, key) => current === null || current === void 0 ? void 0, obj);
}
setNestedValue(obj, path, value)
{
    const keys = path.split('.');
    const lastKey = keys.pop();
    const target = keys.reduce((current, key) => {
        if (!current[key]) current[key] = {};
        return current[key];
    }, obj);
    target[lastKey] = value;
}
parseEnvValue(value)
{
    // Try to parse as JSON first
    try {
        return JSON.parse(value);
    } catch {
        // Try to parse as boolean
        if (value.toLowerCase() === 'true') return true;
        if (value.toLowerCase() === 'false') return false;

        // Try to parse as number
        const num = Number(value);
        if (!isNaN(num)) return num;

        // Return as string
        return value;
    }
}
getConfigVersion() {
    try {
        const packagePath = path.resolve(__dirname, '../../../package.json');
        if (fs.existsSync(packagePath)) {
            const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            return pkg.version || '1.0.0';
        }
    } catch (error) {
        logger.debug('Could not determine config version:', error.message);
    }
    return '1.0.0';
}

/**
 * Cleanup resources
 */
destroy() {
    // Close file watchers
    for (const watcher of this.watchers.values()) {
        watcher.close();
    }
    // this.watchers.clear();

    // Clear loaded files
    // this.loadedFiles.clear();

    // Reset state
    // this.isLoaded = false;
    // this.config = {};
    logger.info('Configuration loader destroyed');
}
}
module.exports = {
    ConfigurationLoader,
    CONFIG_FILES,
    VALIDATION_SCHEMAS
};

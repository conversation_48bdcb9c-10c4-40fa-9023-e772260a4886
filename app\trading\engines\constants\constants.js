/**
 * Central Constants for electronTrader Application
 * Contains all application-wide constants for consistency and maintainability
 */

// Exchange Configuration
const EXCHANGES = {
  BINANCE: 'binance',
  COINBASE: 'coinbase',
  KRAKEN: 'kraken',
  BYBIT: 'bybit',
  OKX: 'okx',
  KUCOIN: 'kucoin',
};

// API Rate Limits (requests per minute)
const RATE_LIMITS = {
  BINANCE: 1200,
  COINBASE: 10,
  KRAKEN: 15,
  BYBIT: 120,
  OKX: 20,
  KUCOIN: 45,
};

// Trading Configuration
const TRADING_FEES = {
  DEFAULT_TRADING_FEE: 0.001, // 0.1%
  SLIPPAGE_MULTIPLIER: 1.02,
  LARGE_ORDER_THRESHOLD: 10000,
};

const TIMEOUTS = {
  API_TIMEOUT: 30000, // 30 seconds
  ORDER_TIMEOUT: 60000, // 1 minute
  RETRY_DELAY: 1000, // 1 second
  SPLIT_ORDER_DELAY: 500, // 0.5 seconds
  WEBSOCKET_TIMEOUT: 10000, // 10 seconds
  HEARTBEAT_INTERVAL: 30000, // 30 seconds
};

const LIMITS = {
  MAX_SLIPPAGE_BPS: 100, // 1%
  MAX_ORDER_VALUE: 100000, // $100k
  MIN_ORDER_VALUE: 10, // $10
  MAX_RETRY_ATTEMPTS: 3,
  MAX_SPLIT_PARTS: 10,
  MAX_OPEN_POSITIONS: 50,
};

// API Endpoints
const API_ENDPOINTS = {
  DEXSCREENER: 'https://api.dexscreener.com/latest/dex/search',
  PIONEX: 'https://api.pionex.com',
  BINANCE: 'https://api.binance.com',
  COINBASE: 'https://api.exchange.coinbase.com',
};

// Scanner Configuration
const SCANNER_CONFIG = {
  SCAN_INTERVAL: 300000, // 5 minutes
  MIN_VOLUME_24H: 100000, // $100k
  MIN_LIQUIDITY: 50000, // $50k
  MAX_MARKET_CAP: 10000000, // $10M
  MIN_PRICE_CHANGE_24H: 0.1, // 10%
  MAX_AGE_DAYS: 30,
};

// Error Codes
const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  API_ERROR: 'API_ERROR',
  TRADING_ERROR: 'TRADING_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
};

// UI Theme Constants
const THEME_COLORS = {
  PRIMARY: '#00eaff',
  SECONDARY: '#a259ff',
  SUCCESS: '#4caf50',
  ERROR: '#f44336',
  WARNING: '#ffc107',
  BACKGROUND_DEFAULT: '#0f0f23',
  BACKGROUND_PAPER: '#181a20',
};

const UI_CONSTANTS = {
  BORDER_RADIUS: 8,
  CARD_BORDER_RADIUS: 12,
  BUTTON_BORDER_RADIUS: 6,
  SIDEBAR_WIDTH: 280,
  HEADER_HEIGHT: 64,
};

// Configuration Defaults
const CONFIG_DEFAULTS = {
  DEFAULT_RISK_PERCENT: 2, // 2%
  MAX_OPEN_POSITIONS: 10,
  API_TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
  LOG_LEVEL: 'info',
  GRID_ORDERS_COUNT: 10,
  GRID_PRICE_RANGE: 0.1, // 10%
};

// Bot Status Constants
const BOT_STATUS = {
  IDLE: 'idle',
  RUNNING: 'running',
  PAUSED: 'paused',
  STOPPED: 'stopped',
  ERROR: 'error',
};

// Order Types
const ORDER_TYPES = {
  MARKET: 'market',
  LIMIT: 'limit',
  STOP: 'stop',
  STOP_LIMIT: 'stop_limit',
};

// Order Sides
const ORDER_SIDES = {
  BUY: 'buy',
  SELL: 'sell',
};

// WebSocket Events
const WEBSOCKET_EVENTS = {
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  ERROR: 'error',
  MESSAGE: 'message',
  PRICE_UPDATE: 'price_update',
  ORDER_UPDATE: 'order_update',
  BALANCE_UPDATE: 'balance_update',
};

// Database Tables
// Database Tables
const DB_TABLES = {
  BOTS: 'bots',
  TRADES: 'trades',
  POSITIONS: 'positions',
  BALANCES: 'balances',
  GRID_ORDERS: 'grid_orders',
  WHALE_SIGNALS: 'whale_signals',
  PERFORMANCE_METRICS: 'performance_metrics',
  TRADING_TRANSACTIONS: 'trading_transactions',
  COIN_METADATA: 'cocococoin_metadata',
  WHALE_ACTIVITY: 'whale_activity',
  CIRCUIT_BREAKER_STATES: 'circuit_breaker_states',
  RISK_MANAGEMENT_STATE: 'risk_management_state',
  PRICE_DATA: 'price_data',
  FAILED_ORDERS: 'failed_orders',
  GRID_PRESETS: 'grid_presets',
  STRATEGY_POSITIONS: 'strategy_positions',
};

// Database Columns (most frequently used)
const DB_COLUMNS = {
  SYMBOL: 'symbol',
  TIMESTAMP: 'timestamp',
  AMOUNT: 'amount',
  PRICE: 'price',
  STATUS: 'status',
  CURRENCY: 'currency',
  CLIENT_ORDER_ID: 'clientOrderId',
  ENTRY_PRICE: 'entry_price',
  CURRENT_PRICE: 'current_price',
  UNREALIZED_PNL: 'unrealized_pnl',
  REALIZED_PNL: 'realized_pnl',
  QUANTITY: 'quantity',
  SIDE: 'side',
  TYPE: 'type',
  EXCHANGE: 'exchange',
  ORDER_ID: 'order_id',
  CREATED_AT: 'created_at',
  UPDATED_AT: 'updated_at',
};

// Common SQL Queries
const SQL_QUERIES = {
  LIST_TABLES: 'SELECT name FROM sqlite_master WHERE type=\'table\'',
  LIST_COLUMNS: 'PRAGMA table_info(?)',
  COUNT_ROWS: 'SELECT COUNT(*) as count FROM ?',
  DELETE_OLD_RECORDS: 'DELETE FROM ? WHERE created_at < ?',
  GET_LATEST_RECORD: 'SELECT * FROM ? ORDER BY created_at DESC LIMIT 1',
};

// Trading Symbols (commonly used)
const TRADING_SYMBOLS = {
  BTCUSDT: 'BTC/USDT',
  ETHUSDT: 'ETH/USDT',
  ADAUSDT: 'ADA/USDT',
  DOTUSDT: 'DOT/USDT',
  LINKUSDT: 'LINK/USDT',
  BNBUSDT: 'BNB/USDT',
};

// API Endpoints (frequently used)
const API_ENDPOINTS_EXTENDED = {
  ...API_ENDPOINTS,
  GATEIO: 'https://api.gateio.ws/api/v4',
  BINANCE_SPOT: 'https://api.binance.com/api/v3',
  COINBASE_PRO: 'https://api.pro.coinbase.com',
};

// HTTP Headers
const HTTP_HEADERS = {
  CONTENT_TYPE: 'Content-Type',
  AUTHORIZATION: 'Authorization',
  USER_AGENT: 'User-Agent',
  ACCEPT: 'Accept',
};

// Content Types
const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM_URLENCODED: 'application/x-www-form-urlencoded',
  TEXT_PLAIN: 'text/plain',
};

// Configuration Keys
const CONFIG_KEYS = {
  ENABLED: 'enabled',
  TEST_MODE: 'testMode',
  DATABASE: 'database',
  API_KEY: 'apiKey',
  SECRET_KEY: 'secretKey',
  SANDBOX: 'sandbox',
  RATE_LIMIT: 'rateLimit',
};

// Signal Types
const SIGNAL_TYPES = {
  BULLISH: 'bullish',
  BEARISH: 'bearish',
  NEUTRAL: 'neutral',
  STRONG_BUY: 'strong_buy',
  STRONG_SELL: 'strong_sell',
};

// Risk Levels
const RISK_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  VERY_HIGH: 'very_high',
};

// CSS/UI Alignment
const CSS_ALIGNMENT = {
  CENTER: 'center',
  LEFT: 'left',
  RIGHT: 'right',
  JUSTIFY: 'justify',
};

// Common Status Values
const STATUS_VALUES = {
  SUCCESS: 'success',
  ERROR: 'error',
  PENDING: 'pending',
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
};

module.exports = {
  EXCHANGES,
  RATE_LIMITS,
  TRADING_FEES,
  TIMEOUTS,
  LIMITS,
  API_ENDPOINTS,
  SCANNER_CONFIG,
  ERROR_CODES,
  THEME_COLORS,
  UI_CONSTANTS,
  CONFIG_DEFAULTS,
  BOT_STATUS,
  ORDER_TYPES,
  ORDER_SIDES,
  WEBSOCKET_EVENTS,
  DB_TABLES,
  DB_COLUMNS,
  SQL_QUERIES,
  TRADING_SYMBOLS,
  API_ENDPOINTS_EXTENDED,
  HTTP_HEADERS,
  CONTENT_TYPES,
  CONFIG_KEYS,
  SIGNAL_TYPES,
  RISK_LEVELS,
  CSS_ALIGNMENT,
  STATUS_VALUES,
};

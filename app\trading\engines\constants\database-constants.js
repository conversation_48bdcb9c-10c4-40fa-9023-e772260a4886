/**
 * Database Constants for electronTrader
 * Centralized database-related string literals to eliminate duplication
 */

// Table Names
const TABLE_NAMES = {
  BOTS: 'bots',
  TRADES: 'trades',
  POSITIONS: 'positions',
  BALANCES: 'balances',
  GRID_ORDERS: 'grid_orders',
  WHALE_SIGNALS: 'whale_signals',
  PERFORMANCE_METRICS: 'performance_metrics',
  TRADING_TRANSACTIONS: 'trading_transactions',
  COIN_METADATA: 'cocococoin_metadata',
  WHALE_ACTIVITY: 'whale_activity',
  CIRCUIT_BREAKER_STATES: 'circuit_breaker_states',
  RISK_MANAGEMENT_STATE: 'risk_management_state',
  PRICE_DATA: 'price_data',
  FAILED_ORDERS: 'failed_orders',
  GRID_PRESETS: 'grid_presets',
  STRATEGY_POSITIONS: 'strategy_positions',
  WHALE_TRANSACTIONS: 'whale_transactions',
  WHALE_PORTFOLIOS: 'whale_portfolios',
  WHALE_PERFORMANCE_HISTORY: 'whale_performance_history',
  ELITE_WHALE_WALLETS: 'elite_whale_wallets',
  HISTORICAL_PATTERNS: 'historical_patterns',
  PERFORMANCE_SNAPSHOTS: 'performance_snapshots',
  ADAPTIVE_SETTINGS: 'adaptive_settings',
  AUTONOMOUS_TRADER_STATE: 'autonomous_trader_state',
  TRADING_SIGNALS: 'trading_signals',
  SENTIMENT_ANALYSIS: 'sentiment_analysis',
  MARKET_DISCOVERY: 'market_discovery',
  TOP_WHALES: 'top_whales',
  WHALE_WALLETS: 'whale_wallets',
  COINS: 'coins',
  GRID_BOTS: 'grid_bots',
};

// Column Names (most frequently used)
const COLUMN_NAMES = {
  ID: 'id',
  SYMBOL: 'symbol',
  TIMESTAMP: 'timestamp',
  AMOUNT: 'amount',
  PRICE: 'price',
  STATUS: 'status',
  CURRENCY: 'currency',
  CLIENT_ORDER_ID: 'clientOrderId',
  ENTRY_PRICE: 'entry_price',
  CURRENT_PRICE: 'current_price',
  UNREALIZED_PNL: 'unrealized_pnl',
  REALIZED_PNL: 'realized_pnl',
  QUANTITY: 'quantity',
  SIDE: 'side',
  TYPE: 'type',
  EXCHANGE: 'exchange',
  ORDER_ID: 'order_id',
  CREATED_AT: 'created_at',
  UPDATED_AT: 'updated_at',
  NAME: 'name',
  VALUE: 'value',
  ADDRESS: 'address',
  CHAIN: 'chain',
  TRANSACTION_HASH: 'transaction_hash',
  ERROR_MESSAGE: 'error_message',
  ERROR_CODE: 'error_code',
  RETRY_COUNT: 'retry_count',
  LAST_RETRY_AT: 'last_retry_at',
  TOTAL_VALUE: 'total_value',
  FEES: 'fees',
  EXECUTION_TIME: 'execution_time',
  PNL: 'pnl',
  GRID_COUNT: 'grid_count',
  LOWER_PRICE: 'lower_price',
  UPPER_PRICE: 'upper_price',
  TOTAL_AMOUNT: 'total_amount',
  GRID_TYPE: 'grid_type',
  OPEN_PRICE: 'open_price',
  HIGH_PRICE: 'high_price',
  LOW_PRICE: 'low_price',
  CLOSE_PRICE: 'close_price',
  VOLUME: 'volume',
  TIMEFRAME: 'timeframe',
  STOP_LOSS: 'stop_loss',
  TAKE_PROFIT: 'take_profit',
  STRATEGY: 'strategy',
  CLOSED_AT: 'closed_at',
  FAILURE_COUNT: 'failure_count',
  FAILURE_THRESHOLD: 'failure_threshold',
  TIMEOUT_MS: 'timeout_ms',
  LAST_FAILURE_AT: 'last_failure_at',
  LAST_SUCCESS_AT: 'last_success_at',
  DAILY_PNL: 'daily_pnl',
  WEEKLY_PNL: 'weekly_pnl',
  MONTHLY_PNL: 'monthly_pnl',
  TOTAL_EXPOSURE: 'total_exposure',
  MAX_DRAWDOWN: 'max_drawdown',
  CURRENT_DRAWDOWN: 'current_drawdown',
  EMERGENCY_STOP_ACTIVE: 'emergency_stop_active',
  CONSECUTIVE_LOSSES: 'consecutive_losses',
  DAILY_LOSS_LIMIT: 'daily_loss_limit',
  MAX_POSITION_SIZE: 'max_position_size',
  TOTAL_CAPITAL: 'total_capital',
  AVAILABLE_CAPITAL: 'available_capital',
  LAST_UPDATED: 'last_updated',
};

// Common SQL Query Templates
const SQL_TEMPLATES = {
  SELECT_ALL: 'SELECT * FROM ?',
  SELECT_BY_ID: 'SELECT * FROM ? WHERE id = ?',
  SELECT_BY_SYMBOL: 'SELECT * FROM ? WHERE symbol = ?',
  SELECT_BY_STATUS: 'SELECT * FROM ? WHERE status = ?',
  SELECT_BY_EXCHANGE: 'SELECT * FROM ? WHERE exchange = ?',
  SELECT_COUNT: 'SELECT COUNT(*) as count FROM ?',
  SELECT_LATEST: 'SELECT * FROM ? ORDER BY created_at DESC LIMIT 1',
  SELECT_RECENT: 'SELECT * FROM ? WHERE created_at > ? ORDER BY created_at DESC',
  INSERT_RECORD: 'INSERT INTO ? (?) VALUES (?)',
  UPDATE_BY_ID: 'UPDATE ? SET ? WHERE id = ?',
  UPDATE_BY_SYMBOL: 'UPDATE ? SET ? WHERE symbol = ?',
  DELETE_BY_ID: 'DELETE FROM ? WHERE id = ?',
  DELETE_BY_SYMBOL: 'DELETE FROM ? WHERE symbol = ?',
  DELETE_OLD_RECORDS: 'DELETE FROM ? WHERE created_at < ?',
  LIST_TABLES: 'SELECT name FROM sqlite_master WHERE type=\'table\'',
  LIST_COLUMNS: 'PRAGMA table_info(?)',
  CHECK_TABLE_EXISTS: 'SELECT name FROM sqlite_master WHERE type=\'table\' AND name=?',
  CREATE_INDEX: 'CREATE INDEX IF NOT EXISTS ? ON ? (?)',
  DROP_INDEX: 'DROP INDEX IF EXISTS ?',
};

// Status Values
const STATUS_VALUES = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  OPEN: 'open',
  CLOSED: 'closed',
  PARTIAL: 'partial',
  FILLED: 'filled',
  RUNNING: 'running',
  STOPPED: 'stopped',
  PAUSED: 'paused',
  ERROR: 'error',
  SUCCESS: 'success',
  EXPIRED: 'expired',
  REJECTED: 'rejected',
};

// Order Sides
const ORDER_SIDES = {
  BUY: 'buy',
  SELL: 'sell',
  LONG: 'long',
  SHORT: 'short',
};

// Order Types
const ORDER_TYPES = {
  MARKET: 'market',
  LIMIT: 'limit',
  STOP: 'stop',
  STOP_LIMIT: 'stop_limit',
  TAKE_PROFIT: 'take_profit',
  TAKE_PROFIT_LIMIT: 'take_profit_limit',
  OCO: 'oco',
};

// Grid Types
const GRID_TYPES = {
  ARITHMETIC: 'arithmetic',
  GEOMETRIC: 'geometric',
  FIBONACCI: 'fibonacci',
};

// Circuit Breaker States
const CIRCUIT_BREAKER_STATES = {
  CLOSED: 'closed',
  OPEN: 'open',
  HALF_OPEN: 'half-open',
};

// Risk Levels
const RISK_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  VERY_HIGH: 'very_high',
  EXTREME: 'extreme',
};

// Signal Types
const SIGNAL_TYPES = {
  BULLISH: 'bullish',
  BEARISH: 'bearish',
  NEUTRAL: 'neutral',
  STRONG_BUY: 'strong_buy',
  STRONG_SELL: 'strong_sell',
  WHALE: 'whale',
  TECHNICAL: 'technical',
  SENTIMENT: 'sentiment',
  COMBINED: 'combined',
};

// Timeframes
const TIMEFRAMES = {
  MINUTE_1: '1m',
  MINUTE_5: '5m',
  MINUTE_15: '15m',
  MINUTE_30: '30m',
  HOUR_1: '1h',
  HOUR_4: '4h',
  HOUR_12: '12h',
  DAY_1: '1d',
  WEEK_1: '1w',
  MONTH_1: '1M',
};

// Exchange Names
const EXCHANGE_NAMES = {
  BINANCE: 'binance',
  COINBASE: 'coinbase',
  KRAKEN: 'kraken',
  BYBIT: 'bybit',
  OKX: 'okx',
  KUCOIN: 'kucoin',
  GATEIO: 'gateio',
  HUOBI: 'huobi',
  BITGET: 'bitget',
  MEXC: 'mexc',
};

// Blockchain Networks
const BLOCKCHAIN_NETWORKS = {
  BITCOIN: 'bitcoin',
  ETHEREUM: 'ethereum',
  BINANCE_SMART_CHAIN: 'bsc',
  POLYGON: 'polygon',
  AVALANCHE: 'avalanche',
  SOLANA: 'solana',
  CARDANO: 'cardano',
  POLKADOT: 'polkadot',
  CHAINLINK: 'chainlink',
  COSMOS: 'cosmos',
};

// Database Connection States
const CONNECTION_STATES = {
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  ERROR: 'error',
  TIMEOUT: 'timeout',
  READY: 'ready',
};

module.exports = {
  TABLE_NAMES,
  COLUMN_NAMES,
  SQL_TEMPLATES,
  STATUS_VALUES,
  ORDER_SIDES,
  ORDER_TYPES,
  GRID_TYPES,
  CIRCUIT_BREAKER_STATES,
  RISK_LEVELS,
  SIGNAL_TYPES,
  TIMEFRAMES,
  EXCHANGE_NAMES,
  BLOCKCHAIN_NETWORKS,
  CONNECTION_STATES,
};

/**
 * Trading System Constants
 * Centralized constants for the trading system
 */

const TRADING_CONSTANTS = {
  // Trading Fees and Costs
  FEES: {
    DEFAULT_TRADING_FEE: 0.001, // 0.1%
    SLIPPAGE_MULTIPLIER: 1.02, // 2% slippage buffer
    LARGE_ORDER_THRESHOLD: 10000, // $10,000 USD
    MAKER_FEE: 0.0005, // 0.05%
    TAKER_FEE: 0.001, // 0.1%
  },

  // Timeouts and Delays
  TIMEOUTS: {
    API_TIMEOUT: 30000, // 30 seconds
    ORDER_TIMEOUT: 60000, // 1 minute
    RETRY_DELAY: 1000, // 1 second
    SPLIT_ORDER_DELAY: 2000, // 2 seconds
    CONNECTION_TIMEOUT: 10000, // 10 seconds
    HEARTBEAT_INTERVAL: 5000, // 5 seconds
  },

  // Order and Position Limits
  LIMITS: {
    MAX_SLIPPAGE_BPS: 100, // 1% in basis points
    MAX_ORDER_VALUE: 100000, // $100,000 USD
    MIN_ORDER_VALUE: 10, // $10 USD
    MAX_RETRY_ATTEMPTS: 3,
    MAX_SPLIT_PARTS: 10,
    SPLIT_ORDER_THRESHOLD: 5000, // $5,000 USD
    MIN_SPLIT_SIZE: 100, // $100 USD
    MAX_POSITION_SIZE: 50000, // $50,000 USD
    MAX_DAILY_LOSS: 1000, // $1,000 USD
  },

  // Risk Management
  RISK: {
    LOW_LIQUIDITY_THRESHOLD: 10000, // $10,000 USD
    MEDIUM_LIQUIDITY_THRESHOLD: 100000, // $100,000 USD
    HIGH_VOLATILITY_THRESHOLD: 0.05, // 5%
    MEDIUM_VOLATILITY_THRESHOLD: 0.02, // 2%
    ORDER_IMPACT_THRESHOLD: 0.01, // 1%
    HIGH_RISK_SCORE: 8,
    MEDIUM_RISK_SCORE: 5,
    LOW_RISK_SCORE: 2,
    MAX_DRAWDOWN: 0.1, // 10%
    STOP_LOSS_PERCENTAGE: 0.05, // 5%
    TAKE_PROFIT_PERCENTAGE: 0.15, // 15%
  },

  // API Endpoints
  API: {
    DEXSCREENER_URL: 'https://api.dexscreener.com/latest/dex/search',
    PIONEX_URL: 'https://api.pionex.com',
    BINANCE_URL: 'https://api.binance.com',
    COINBASE_URL: 'https://api.coinbase.com',
    COINGECKO_URL: 'https://api.coingecko.com/api/v3',
    RATE_LIMIT_REQUESTS: 100,
    RATE_LIMIT_WINDOW: 60000, // 1 minute
  },

  // Database
  DATABASE: {
    CONNECTION_POOL_SIZE: 10,
    QUERY_TIMEOUT: 30000, // 30 seconds
    RETRY_ATTEMPTS: 3,
    BATCH_SIZE: 1000,
    MAX_CONNECTIONS: 20,
    IDLE_TIMEOUT: 300000, // 5 minutes
  },

  // Trading Pairs and Markets
  MARKETS: {
    DEFAULT_BASE_CURRENCY: 'USDT',
    SUPPORTED_QUOTE_CURRENCIES: ['USDT', 'USDC', 'BTC', 'ETH'],
    MIN_MARKET_CAP: 100000, // $100,000 USD
    MAX_MARKET_CAP: 10000000000, // $10B USD
    MIN_VOLUME_24H: 10000, // $10,000 USD
    MIN_PRICE: 0.000001, // $0.000001
    MAX_PRICE: 1000000, // $1,000,000
  },

  // Performance and Monitoring
  PERFORMANCE: {
    MAX_MEMORY_USAGE: 1024 * 1024 * 1024, // 1GB
    MAX_CPU_USAGE: 80, // 80%
    HEALTH_CHECK_INTERVAL: 30000, // 30 seconds
    METRICS_RETENTION_DAYS: 30,
    LOG_RETENTION_DAYS: 7,
    BACKUP_INTERVAL: 3600000, // 1 hour
  },

  // Grid Trading
  GRID: {
    MIN_GRID_LEVELS: 5,
    MAX_GRID_LEVELS: 50,
    MIN_GRID_SPACING: 0.001, // 0.1%
    MAX_GRID_SPACING: 0.1, // 10%
    DEFAULT_GRID_LEVELS: 20,
    DEFAULT_GRID_SPACING: 0.01, // 1%
    MIN_INVESTMENT: 100, // $100 USD
    MAX_INVESTMENT: 10000, // $10,000 USD
  },

  // Arbitrage
  ARBITRAGE: {
    MIN_PROFIT_THRESHOLD: 0.005, // 0.5%
    MAX_EXECUTION_TIME: 10000, // 10 seconds
    MIN_VOLUME_THRESHOLD: 1000, // $1,000 USD
    MAX_PRICE_DIFFERENCE: 0.1, // 10%
    OPPORTUNITY_TIMEOUT: 30000, // 30 seconds
  },

  // Whale Tracking
  WHALE: {
    MIN_TRANSACTION_VALUE: 100000, // $100,000 USD
    LARGE_TRANSACTION_VALUE: 1000000, // $1,000,000 USD
    TRACKING_WINDOW_HOURS: 24,
    MAX_WALLETS_TO_TRACK: 1000,
    UPDATE_INTERVAL: 60000, // 1 minute
  },

  // Meme Coin Detection
  MEME_COIN: {
    MIN_AGE_HOURS: 1, // 1 hour
    MAX_AGE_DAYS: 30, // 30 days
    MIN_HOLDERS: 100,
    MAX_HOLDERS: 100000,
    MIN_LIQUIDITY: 10000, // $10,000 USD
    SOCIAL_SCORE_THRESHOLD: 50,
    PUMP_DETECTION_THRESHOLD: 0.5, // 50% price increase
  },

  // Order Types
  ORDER_TYPES: {
    MARKET: 'market',
    LIMIT: 'limit',
    STOP: 'stop',
    STOP_LIMIT: 'stop_limit',
    TAKE_PROFIT: 'take_profit',
    TAKE_PROFIT_LIMIT: 'take_profit_limit',
  },

  // Order Sides
  ORDER_SIDES: {
    BUY: 'buy',
    SELL: 'sell',
  },

  // Order Status
  ORDER_STATUS: {
    PENDING: 'pending',
    OPEN: 'open',
    FILLED: 'filled',
    PARTIALLY_FILLED: 'partially_filled',
    CANCELLED: 'cancelled',
    REJECTED: 'rejected',
    EXPIRED: 'expired',
  },

  // System Status
  SYSTEM_STATUS: {
    INITIALIZING: 'initializing',
    RUNNING: 'running',
    STOPPING: 'stopping',
    STOPPED: 'stopped',
    ERROR: 'error',
    MAINTENANCE: 'maintenance',
  },

  // Component Status
  COMPONENT_STATUS: {
    PENDING: 'pending',
    INITIALIZING: 'initializing',
    READY: 'ready',
    RUNNING: 'running',
    ERROR: 'error',
    STOPPED: 'stopped',
  },

  // Health Status
  HEALTH_STATUS: {
    HEALTHY: 'healthy',
    DEGRADED: 'degraded',
    UNHEALTHY: 'unhealthy',
    UNKNOWN: 'unknown',
  },

  // Log Levels
  LOG_LEVELS: {
    ERROR: 'error',
    WARN: 'warn',
    INFO: 'info',
    DEBUG: 'debug',
    TRACE: 'trace',
  },

  // Cache
  CACHE: {
    DEFAULT_TTL: 300000, // 5 minutes
    MAX_SIZE: 10000,
    CLEANUP_INTERVAL: 60000, // 1 minute
    PRICE_CACHE_TTL: 10000, // 10 seconds
    MARKET_DATA_TTL: 30000, // 30 seconds
    USER_DATA_TTL: 60000, // 1 minute
  },

  // Validation
  VALIDATION: {
    MAX_STRING_LENGTH: 1000,
    MAX_ARRAY_LENGTH: 10000,
    MAX_OBJECT_DEPTH: 10,
    SYMBOL_REGEX: /^[A-Z0-9]{2,20}$/,
    ADDRESS_REGEX: /^0x[a-fA-F0-9]{40}$/,
    EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
};

module.exports = TRADING_CONSTANTS;
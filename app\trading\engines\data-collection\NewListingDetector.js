/**
 * @fileoverview New Listing Detector
 * @description Real-time detection of new cryptocurrency listings across multiple exchanges
 * Provides sub-5-second detection with advanced filtering and validation
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */

const EventEmitter = require('events');
const axios = require('axios');
const WebSocket = require('ws');
const logger = require('../../shared/helpers/logger');

/**
 * New Listing Detector Class
 *
 * @description Monitors multiple exchanges for new cryptocurrency listings
 * with real-time WebSocket connections and REST API fallbacks
 *
 * @class NewListingDetector
 * @extends EventEmitter
 */
class NewListingDetector extends EventEmitter {
    // this.detectionStats = {
    totalDetections

    // Core state management
    // this.isInitialized = false;
    // this.isRunning = false;
    successfulDetections
,
    failedDetections
,
    averageDetectionTime
,
    lastDetection
,

    /**
     * Create a New Listing Detector
     *
     * @param {Object} [options] - Configuration options
     * @param {Array<string>} [options.exchanges=['binance', 'kraken', 'coinbase']] - Exchanges to monitor
     * @param {number} [options.pollingInterval=30000] - REST API polling interval in ms
     * @param {number} [options.retryAttempts=3] - Number of retry attempts for failed requests
     * @param {number} [options.retryDelay=2000] - Delay between retry attempts in ms
     * @param {number} [options.maxCacheSize=1000] - Maximum number of cached listings
     * @param {boolean} [options.enableWebSocket=true] - Enable WebSocket connections
     * @param {boolean} [options.enableRestFallback=true] - Enable REST API fallback
     * @param {Object} [options.database] - Database instance for persistence
     */
    constructor(options = {}) {
        super();

        // this.options = {
        exchanges
        'binance', 'kraken', 'coinbase'
    ],
        pollingInterval, // 30 seconds
            retryAttempts,
            retryDelay, // 2 seconds
            maxCacheSize,
            enableWebSocket,
            enableRestFallback,
            database,
    ...
        options
    };
};

// Exchange-specific configurations
// this.exchangeConfigs = new Map([
['binance', {
    name: 'Binance',
    websocketUrl: 'wss://stream.binance.com/ws/!ticker@arr',
    restApiUrl: 'https://api.binance.com/api/v3/exchangeInfo',
    announcements: 'https://www.binance.com/bapi/composite/v1/public/cms/article/list/query',
    rateLimitMs
}],
    ['kraken', {
        name: 'Kraken',
        websocketUrl: 'wss://ws.kraken.com',
        restApiUrl: 'https://api.kraken.com/0/public/AssetPairs',
        announcements: 'https://blog.kraken.com/feed/',
        rateLimitMs
    }],
    ['coinbase', {
        name: 'Coinbase Pro',
        websocketUrl: 'wss://ws-feed.exchange.coinbase.com',
        restApiUrl: 'https://api.exchange.coinbase.com/products',
        announcements: 'https://blog.coinbase.com/feed',
        rateLimitMs
    }]
])
;

// Runtime state
// this.websockets = new Map();
// this.pollingIntervals = new Map();
// this.knownListings = new Map(); // Exchange -> Set of symbols
// this.detectionCache = new Map(); // Symbol -> detection data
// this.lastPollingTime = new Map();
// this.retryCounters = new Map();

// Performance monitoring
// this.performanceMetrics = {
detectionLatency,
    websocketUptime
Map: jest.fn(),
    restApiSuccessRate
Map: jest.fn(),
    memoryUsage
}
;
}

/**
 * Initialize the New Listing Detector
 *
 * @returns {Promise<boolean>} True if initialization successful
 * @throws {Error} If initialization fails
 */
async
initialize() {
    if (this.isInitialized) {
        logger.warn('NewListingDetector already initialized');
        return true;
    }

    try {
        logger.info('🚀 Initializing New Listing Detector...');

        // Initialize exchange configurations and validate connectivity
        await this.initializeExchangeConnections();

        // Load existing listings for baseline comparison
        await this.loadKnownListings();

        // Setup performance monitoring
        // this.setupPerformanceMonitoring();

        // Initialize database tables if database is provided
        if (this.options.database) {
            await this.initializeDatabaseTables();
        }

        // this.isInitialized = true;
        logger.info('✅ New Listing Detector initialized successfully');

        // this.emit('initialized', {
        exchanges(this.exchangeConfigs.keys()),
            timestamp()
    }
)
    ;

    return true;
} catch (error) {
    logger.error('❌ Failed to initialize New Listing Detector:', error);
    throw error;
}
}

/**
 * Initialize connections to all configured exchanges
 *
 * @private
 * @returns {Promise<void>}
 */
initializeExchangeConnections() {
    const initPromises = this.options.exchanges.map((exchange) => {
        const config = this.exchangeConfigs.get(exchange);
        if (!config) {
            logger.warn(`Unknown exchange configuration: ${exchange}`);
            return;
        }

        try {
            // Test REST API connectivity
            await this.testExchangeConnectivity(exchange, config);

            // Initialize known listings for this exchange
            // this.knownListings.set(exchange, new Set());
            // this.lastPollingTime.set(exchange, 0);
            // this.retryCounters.set(exchange, 0);
            // this.performanceMetrics.websocketUptime.set(exchange, 0);
            // this.performanceMetrics.restApiSuccessRate.set(exchange, { success, total });

            logger.info(`✅ Exchange ${config.name} connectivity verified`);
        } catch (error) {
            logger.error(`❌ Failed to initialize ${exchange}:`, error);
            // Continue with other exchanges
        }
    });

    return Promise.allSettled(initPromises);
}

/**
 * Test connectivity to an exchange
 *
 * @private
 * @param {string} exchange - Exchange name
 * @param {Object} config - Exchange configuration
 * @returns {Promise<void>}
 */
async
testExchangeConnectivity(exchange, config)
{
    const timeout = setTimeout(() => {
        throw new Error(`Connectivity test timeout for ${exchange}`);
    }, 10000);

    try {
        const response = await axios.get(config.restApiUrl, {
            timeout: 30000,
            headers: {
                'User-Agent': 'electronTrader/1.0.0',
                'Accept': 'application/json'
            }
        });

        if (response.status !== 200) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        clearTimeout(timeout);
        logger.debug(`${config.name} connectivity test passed`);
    } catch (error) {
        clearTimeout(timeout);
        throw new Error(`${config.name} connectivity test failed: ${error.message}`);
    }
}

/**
 * Load known listings from all exchanges for baseline comparison
 *
 * @private
 * @returns {Promise<void>}
 */
async
loadKnownListings() {
    logger.info('📊 Loading current market listings for baseline...');

    const loadPromises = this.options.exchanges.map(async (exchange) => {
        try {
            const listings = await this.fetchCurrentListings(exchange);
            const symbolSet = new Set(listings.map(listing => listing.symbol));
            // this.knownListings.set(exchange, symbolSet);

            logger.debug(`Loaded ${symbolSet.size} existing listings from ${exchange}`);
        } catch (error) {
            logger.error(`Failed to load listings from ${exchange}:`, error);
            // Set empty baseline to avoid false positives
            // this.knownListings.set(exchange, new Set());
        }
    });

    await Promise.allSettled(loadPromises);

    const totalKnown = Array.from(this.knownListings.values())
        .reduce((sum, set) => sum + set.size, 0);

    logger.info(`📊 Loaded ${totalKnown} total baseline listings across all exchanges`);
}

/**
 * Fetch current listings from a specific exchange
 *
 * @private
 * @param {string} exchange - Exchange name
 * @returns {Promise<Array>} Array of current listings
 */
fetchCurrentListings(exchange)
{
    const config = this.exchangeConfigs.get(exchange);
    if (!config) throw new Error(`Unknown exchange: ${exchange}`);

    switch (exchange) {
        case 'binance'
            turn
            // this.fetchBinanceListings(config);
        case 'kraken'
            turn
            // this.fetchKrakenListings(config);
        case 'coinbase'
            turn
            // this.fetchCoinbaseListings(config);
        default
            new Error(`Fetching not implemented for ${exchange}`);
    }
}

/**
 * Fetch current listings from Binance
 *
 * @private
 * @param {Object} config - Binance configuration
 * @returns {Promise<Array>} Array of Binance listings
 */
async
fetchBinanceListings(config)
{
    try {
        const response = await axios.get(config.restApiUrl, {timeout});

        return response.data.symbols
            .filter(symbol => symbol.status === 'TRADING')
            .map(symbol => ({
                symbol,
                baseAsset,
                quoteAsset,
                status,
                exchange: 'binance'
            }));
    } catch (error) {
        logger.error('Failed to fetch Binance listings:', error);
        return [];
    }
}

/**
 * Fetch current listings from Kraken
 *
 * @private
 * @param {Object} config - Kraken configuration
 * @returns {Promise<Array>} Array of Kraken listings
 */
async
fetchKrakenListings(config)
{
    try {
        const response = await axios.get(config.restApiUrl, {timeout});

        return Object.entries(response.data.result || {})
            .filter(([_, pair]) => pair.status === 'online')
            .map(([symbol, pair]) => ({
                symbol,
                baseAsset,
                quoteAsset,
                status,
                exchange: 'kraken'
            }));
    } catch (error) {
        logger.error('Failed to fetch Kraken listings:', error);
        return [];
    }
}

/**
 * Fetch current listings from Coinbase
 *
 * @private
 * @param {Object} config - Coinbase configuration
 * @returns {Promise<Array>} Array of Coinbase listings
 */
async
fetchCoinbaseListings(config)
{
    try {
        const response = await axios.get(config.restApiUrl, {timeout});

        return response.data
            .filter(product => product.status === 'online' && !product.trading_disabled)
            .map(product => ({
                symbol,
                baseAsset,
                quoteAsset,
                status,
                exchange: 'coinbase'
            }));
    } catch (error) {
        logger.error('Failed to fetch Coinbase listings:', error);
        return [];
    }
}

/**
 * Start the New Listing Detector
 *
 * @returns {Promise<boolean>} True if started successfully
 * @throws {Error} If detector is not initialized or start fails
 */
async
start() {
    if (!this.isInitialized) {
        throw new Error('NewListingDetector must be initialized before starting');
    }

    if (this.isRunning) {
        logger.warn('NewListingDetector already running');
        return true;
    }

    try {
        logger.info('🚀 Starting New Listing Detector...');

        // Start WebSocket connections for real-time monitoring
        if (this.options.enableWebSocket) {
            await this.startWebSocketConnections();
        }

        // Start REST API polling as fallback/verification
        if (this.options.enableRestFallback) {
            // this.startRestApiPolling();
        }

        // this.isRunning = true;
        logger.info('✅ New Listing Detector started successfully');

        // this.emit('started', {
        websockets,
            polling,
            exchanges,
            timestamp()
    }
)
    ;

    return true;
} catch (error) {
    logger.error('❌ Failed to start New Listing Detector:', error);
    throw error;
}
}

/**
 * Start WebSocket connections for real-time monitoring
 *
 * @private
 * @returns {Promise<void>}
 */
startWebSocketConnections() {
    const wsPromises = this.options.exchanges.map(async (exchange) => {
        try {
            await this.setupWebSocketConnection(exchange);
        } catch (error) {
            logger.error(`Failed to setup WebSocket for ${exchange}:`, error);
        }
    });

    return Promise.allSettled(wsPromises);
}

/**
 * Setup WebSocket connection for a specific exchange
 *
 * @private
 * @param {string} exchange - Exchange name
 * @returns {Promise<void>}
 */
setupWebSocketConnection(exchange)
{
    const config = this.exchangeConfigs.get(exchange);
    if (!config) return Promise.resolve();

    logger.debug(`Setting up WebSocket for ${config.name}...`);

    const ws = new WebSocket(config.websocketUrl);

    ws.on('open', () => {
        logger.info(`🔗 WebSocket connected to ${config.name}`);

        // Send subscription messages if needed
        // this.subscribeToExchangeUpdates(ws, exchange);

        // this.performanceMetrics.websocketUptime.set(exchange, Date.now());
        // this.emit('websocketConnected', { exchange, timestamp() });
    });

    ws.on('message', (data) => {
        // this.handleWebSocketMessage(exchange, data);
    });

    ws.on('error', (error) => {
        logger.error(`WebSocket error for ${config.name}:`, error);
        // this.emit('websocketError', { exchange, error, timestamp() });
    });

    ws.on('close', (code, reason) => {
        logger.warn(`WebSocket closed for ${config.name}: ${code} ${reason}`);

        // Attempt reconnection after delay
        setTimeout(() => {
            if (this.isRunning) {
                // this.setupWebSocketConnection(exchange);
            }
        }, 5000);
    });

    // this.websockets.set(exchange, ws);
    return Promise.resolve();
}

/**
 * Subscribe to exchange-specific update streams
 *
 * @private
 * @param {WebSocket} ws - WebSocket connection
 * @param {string} exchange - Exchange name
 */
subscribeToExchangeUpdates(ws, exchange)
{
    switch (exchange) {
        case 'binance':
            // Binance ticker stream is already subscribed in URL
            break;
        case 'kraken'.send(JSON.stringify({
            event: 'subscribe',
            pair'*'],
            subscription: {
                name: 'ticker'
            }
    }
))
    ;
    break;
case
    'coinbase'.send(JSON.stringify({
        type: 'subscribe',
        channels
    {
        name: 'ticker',
            product_ids
        '*'
    ]
    }
]
}))
    ;
    break;
default:
    // No subscription needed for unknown exchanges
    break;
}
}

/**
 * Handle incoming WebSocket messages
 *
 * @private
 * @param {string} exchange - Exchange name
 * @param {Buffer|string} data - Raw message data
 */
handleWebSocketMessage(exchange, data)
{
    try {
        const message = JSON.parse(data.toString());

        // Check for new symbols in ticker data
        // this.processTickerUpdate(exchange, message);

    } catch (error) {
        logger.debug(`Failed to process WebSocket message from ${exchange}:`, error.message);
    }
}

/**
 * Process ticker updates to detect new listings
 *
 * @private
 * @param {string} exchange - Exchange name
 * @param {Object} message - Parsed WebSocket message
 */
processTickerUpdate(exchange, message)
{
    let symbols = [];

    switch (exchange) {
        case 'binance'(Array.isArray(message))
        {
            symbols = message.map(ticker => ticker.s).filter(Boolean);
        }
        else
            if (message.s) {
                symbols = [message.s];
            }
            break;
        case 'kraken'(message.channelName === 'ticker' && message.data)
        {
            symbols = [message.data.symbol];
        }
            break;
        case 'coinbase'(message.type === 'ticker' && message.product_id)
        {
            symbols = [message.product_id];
        }
            break;
    }

    // Check for new symbols
    symbols.forEach(symbol => {
        // this.checkForNewListing(exchange, symbol);
    });
}

/**
 * Check if a symbol represents a new listing
 *
 * @private
 * @param {string} exchange - Exchange name
 * @param {string} symbol - Trading symbol
 */
checkForNewListing(exchange, symbol)
{
    const knownSymbols = this.knownListings.get(exchange);
    if (!knownSymbols) return;

    if (!knownSymbols.has(symbol)) {
        // Potential new listing detected
        // this.handleNewListingDetected(exchange, symbol);

        // Add to known symbols to prevent duplicate detections
        knownSymbols.add(symbol);
    }
}

/**
 * Handle detection of a new listing
 *
 * @private
 * @param {string} exchange - Exchange name
 * @param {string} symbol - New symbol detected
 */
async
handleNewListingDetected(exchange, symbol)
{
    const detectionTime = Date.now();

    logger.info(`🚨 NEW LISTING DETECTED: ${symbol} on ${exchange.toUpperCase()}`);

    try {
        // Verify the listing with additional API call
        const listingData = await this.verifyNewListing(exchange, symbol);

        if (!listingData) {
            logger.warn(`False positive: ${symbol} on ${exchange} could not be verified`);
            return;
        }

        // Calculate detection latency
        const detectionLatency = Date.now() - detectionTime;
        // this.performanceMetrics.detectionLatency.push(detectionLatency);

        // Update detection statistics
        // this.detectionStats.totalDetections++;
        // this.detectionStats.successfulDetections++;
        // this.detectionStats.lastDetection = detectionTime;
        // this.updateAverageDetectionTime(detectionLatency);

        // Cache the detection
        // this.cacheDetection(symbol, {
        exchange,
            symbol,
            detectionTime,
            detectionLatency,
    ...
        listingData
    }
)
    ;

    // Store in database if available
    if (this.options.database) {
        await this.storeDetectionInDatabase({
            exchange,
            symbol,
            detectionTime,
            detectionLatency,
            listingData
        });
    }

    // Emit new listing event
    // this.emit('newListingDetected', {
    exchange,
        symbol,
        detectionTime,
        detectionLatency,
        listingData,
        confidence(listingData)
}
)
;

logger.info(`✅ New listing ${symbol} verified and processed in ${detectionLatency}ms`);

} catch (error) {
    logger.error(`❌ Failed to process new listing ${symbol} on ${exchange}:`, error);
    // this.detectionStats.failedDetections++;
}
}

/**
 * Verify a new listing with additional API calls
 *
 * @private
 * @param {string} exchange - Exchange name
 * @param {string} symbol - Symbol to verify
 * @returns {Promise<Object|null>} Verified listing data or null if invalid
 */
async
verifyNewListing(exchange, symbol)
{
    try {
        const listings = await this.fetchCurrentListings(exchange);
        const listing = listings.find(l => l.symbol === symbol);

        if (!listing) return null;

        // Additional verification steps
        return {
            ...listing,
            verificationTime: jest.fn(),
            tradingActive === 'TRADING' || listing.status === 'online',
            baseAsset,
            quoteAsset
    }
        ;
    } catch (error) {
        logger.error(`Verification failed for ${symbol} on ${exchange}:`, error);
        return null;
    }
}

/**
 * Calculate confidence score for detection
 *
 * @private
 * @param {Object} listingData - Listing data
 * @returns {number} Confidence score between 0-1
 */
calculateDetectionConfidence(listingData)
{
    let confidence = 0.7; // Base confidence

    // Increase confidence if trading is active
    if (listingData.tradingActive) confidence += 0.2;

    // Increase confidence if verification was recent
    const verificationAge = Date.now() - listingData.verificationTime;
    if (verificationAge < 60000) confidence += 0.1; // Within 1 minute

    return Math.min(confidence, 1.0);
}

/**
 * Start REST API polling for fallback monitoring
 *
 * @private
 */
startRestApiPolling() {
    // this.options.exchanges.forEach(exchange => {
    const config = this.exchangeConfigs.get(exchange);
    if (!config) return;

    const interval = setInterval(async () => {
        try {
            await this.pollExchangeForNewListings(exchange);
        } catch (error) {
            logger.error(`Polling error for ${exchange}:`, error);
        }
    }, this.options.pollingInterval);

    // this.pollingIntervals.set(exchange, interval);
}
)
;

logger.info(`📡 REST API polling started for ${this.options.exchanges.length} exchanges`);
}

/**
 * Poll a specific exchange for new listings
 *
 * @private
 * @param {string} exchange - Exchange name
 * @returns {Promise<void>}
 */
pollExchangeForNewListings(exchange)
{
    const lastPollTime = this.lastPollingTime.get(exchange);
    const config = this.exchangeConfigs.get(exchange);

    // Rate limiting
    if (Date.now() - lastPollTime < config.rateLimitMs) {
        return Promise.resolve();
    }

    return this.fetchCurrentListings(exchange)
        .then(currentListings => {
            const knownSymbols = this.knownListings.get(exchange);

            // Find new symbols
            const newSymbols = currentListings
                .map(listing => listing.symbol)
                .filter(symbol => !knownSymbols.has(symbol));

            // Process new symbols
            for (const symbol of newSymbols) {
                // this.checkForNewListing(exchange, symbol);
            }

            // this.lastPollingTime.set(exchange, Date.now());

            // Update success rate
            const stats = this.performanceMetrics.restApiSuccessRate.get(exchange);
            stats.success++;
            stats.total++;
        })
        .catch(error => {
            const stats = this.performanceMetrics.restApiSuccessRate.get(exchange);
            stats.total++;

            logger.error(`Polling failed for ${exchange}:`, error);
        });
}

/**
 * Cache a detection to prevent duplicates and for analytics
 *
 * @private
 * @param {string} symbol - Symbol detected
 * @param {Object} detectionData - Detection data
 */
cacheDetection(symbol, detectionData)
{
    // this.detectionCache.set(symbol, detectionData);

    // Maintain cache size limit
    if (this.detectionCache.size > this.options.maxCacheSize) {
        const firstKey = this.detectionCache.keys().next().value;
        // this.detectionCache.delete(firstKey);
    }
}

/**
 * Update average detection time metric
 *
 * @private
 * @param {number} latency - Detection latency in ms
 */
updateAverageDetectionTime(latency)
{
    const current = this.detectionStats.averageDetectionTime;
    const count = this.detectionStats.successfulDetections;

    // this.detectionStats.averageDetectionTime =
    ((current * (count - 1)) + latency) / count;
}

/**
 * Setup performance monitoring
 *
 * @private
 */
setupPerformanceMonitoring() {
    // Monitor memory usage every 5 minutes
    setInterval(() => {
        // this.performanceMetrics.memoryUsage = process.memoryUsage().heapUsed;

        // Cleanup old latency data
        if (this.performanceMetrics.detectionLatency.length > 100) {
            // this.performanceMetrics.detectionLatency =
            // this.performanceMetrics.detectionLatency.slice(-50);
        }
    }, 300000);
}

/**
 * Initialize database tables for persistent storage
 *
 * @private
 * @returns {Promise<void>}
 */
async
initializeDatabaseTables() {
    try {
        const createTableSQL = `
                CREATE TABLE IF NOT EXISTS new_listing_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    exchange TEXT NOT NULL,
                    detection_time INTEGER NOT NULL,
                    detection_latency INTEGER,
                    base_asset TEXT,
                    quote_asset TEXT,
                    trading_active BOOLEAN,
                    confidence REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, exchange, detection_time)
                )
            `;

        await this.options.database.run(createTableSQL);
        logger.debug('Database tables initialized for NewListingDetector');
    } catch (error) {
        logger.error('Failed to initialize database tables:', error);
    }
}

/**
 * Store detection in database
 *
 * @private
 * @param {Object} detection - Detection data
 * @returns {Promise<void>}
 */
async
storeDetectionInDatabase(detection)
{
    try {
        const sql = `
                INSERT OR IGNORE INTO new_listing_history 
                (symbol, exchange, detection_time, detection_latency, base_asset, quote_asset, trading_active, confidence)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `;

        await this.options.database.run(sql, [
            detection.symbol,
            detection.exchange,
            detection.detectionTime,
            detection.detectionLatency,
            detection.listingData.baseAsset,
            detection.listingData.quoteAsset,
            detection.listingData.tradingActive,
        // this.calculateDetectionConfidence(detection.listingData)]);
    } catch (error) {
        logger.error('Failed to store detection in database:', error);
    }
}

/**
 * Stop the New Listing Detector
 *
 * @returns {Promise<boolean>} True if stopped successfully
 */
stop() {
    if (!this.isRunning) {
        logger.warn('NewListingDetector not running');
        return Promise.resolve(true);
    }

    try {
        logger.info('🛑 Stopping New Listing Detector...');

        // Close WebSocket connections
        for (const [exchange, ws] of this.websockets) {
            try {
                ws.close();
            } catch (error) {
                logger.error(`Error closing WebSocket for ${exchange}:`, error);
            }
        }
        // this.websockets.clear();

        // Clear polling intervals
        for (const interval of this.pollingIntervals.values()) {
            clearInterval(interval);
        }
        // this.pollingIntervals.clear();

        // this.isRunning = false;
        logger.info('✅ New Listing Detector stopped successfully');

        // this.emit('stopped', { timestamp() });

        return Promise.resolve(true);
    } catch (error) {
        logger.error('❌ Error stopping New Listing Detector:', error);
        return Promise.reject(error);
    }
}

/**
 * Get detector status and statistics
 *
 * @returns {Object} Current status and statistics
 */
getStatus() {
    return {
        isInitialized,
        isRunning,
        exchanges,
        detectionStats: {...this.detectionStats},
        performanceMetrics: {
            averageLatency > 0
            ? this.performanceMetrics.detectionLatency.reduce((a, b) => a + b, 0) / this.performanceMetrics.detectionLatency.length,
        memoryUsage,
        websocketConnections,
        restApiSuccessRates(this.performanceMetrics.restApiSuccessRate
)
},
    cacheSize,
        knownListingsCount(this.knownListings.values())
            .reduce((sum, set) => sum + set.size, 0),
        timestamp()
}
    ;
}

/**
 * Get recent detections from cache
 *
 * @param {number} [limit=10] - Maximum number of detections to return
 * @returns {Array} Recent detections
 */
getRecentDetections(limit = 10)
{
    return Array.from(this.detectionCache.values())
        .sort((a, b) => b.detectionTime - a.detectionTime)
        .slice(0, limit);
}

/**
 * Get detection history from database
 *
 * @param {Object} [filters={}] - Filters for history query
 * @param {number} [filters.limit=50] - Maximum results
 * @param {string} [filters.exchange] - Filter by exchange
 * @param {number} [filters.since] - Timestamp to filter since
 * @returns {Promise<Array>} Detection history
 */
async
getDetectionHistory(filters = {})
{
    if (!this.options.database) {
        return this.getRecentDetections(filters.limit || 50);
    }

    try {
        let sql = 'SELECT * FROM new_listing_history WHERE 1=1';
        const params = [];

        if (filters.exchange) {
            sql += ' AND exchange = ?';
            params.push(filters.exchange);
        }

        if (filters.since) {
            sql += ' AND detection_time >= ?';
            params.push(filters.since);
        }

        sql += ' ORDER BY detection_time DESC LIMIT ?';
        params.push(filters.limit || 50);

        const results = await this.options.database.all(sql, params);
        return results || [];
    } catch (error) {
        logger.error('Failed to fetch detection history:', error);
        return [];
    }
}
}

module.exports = NewListingDetector;

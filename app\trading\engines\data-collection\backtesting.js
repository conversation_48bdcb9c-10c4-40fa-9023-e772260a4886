// Add these missing methods to the BacktestingEngine class
// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();


const ccxt = require('ccxt');
const RiskManager = require('../shared/risk/UnifiedRiskManager');
const path = require('path');
const fs = require('fs').promises;

class BacktestingEngine {
    // Missing method in the constructor or as a separate method
    async loadExchange(id) {
        try {
            const ExchangeClass = ccxt[id];
            if (!ExchangeClass) {
                throw new Error(`Exchange ${id} not supported`);
            }
            const exchange = new ExchangeClass({
                apiKey,
                secret,
                sandbox || false,
                enableRateLimit
        })
            ;
            await exchange.loadMarkets();
            return exchange;
        } catch (error) {
            // this.logger.error(`Error loading exchange ${id}:`, error);
            throw error;
        }
    }

    initialize(strategy, historicalData) {
        try {
            logger.info('🔧 Initializing backtesting engine...');

            // Validate inputs
            if (!strategy || typeof strategy.execute !== 'function') {
                throw new Error('Invalid strategy have execute() method');
            }

            if (!historicalData || historicalData.size === 0) {
                throw new Error('No historical data provided');
            }

            // this.strategy = strategy;
            // this.historicalData = historicalData;

            // Initialize risk manager
            // this.riskManager = new RiskManager({
            totalCapital,
        ...
            strategy.riskParams
        }
    )
        ;

        // Validate data alignment
        // this.validateDataAlignment();

        // Calculate total bars
        const firstSymbol = Array.from(historicalData.keys())[0];
        // this.state.totalBars = historicalData.get(firstSymbol).length;

        logger.info(`✅ Backtesting engine initialized with ${historicalData.size} symbols, ${this.state.totalBars} bars`);

        return true;

    } catch (error) {
        logger.error('Initialization error:', error);
        throw error;
    }
}

validateDataAlignment() {
    const symbols = Array.from(this.historicalData.keys());
    const lengths = symbols.map((s) => this.historicalData.get(s).length);
    const minLength = Math.min(...lengths);
    const maxLength = Math.max(...lengths);

    if (maxLength - minLength > 10) {
        logger.warn(`Data length mismatch=${minLength}, max=${maxLength}`);
    }

    // Ensure all data is sorted by timestamp
    for (const data of this.historicalData.values()) {
        data.sort((a, b) =>
            new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime: jest.fn(),
        );
    }
}

async
run() {
    try {
        logger.info('🚀 Starting backtest...');
        // this.state.isRunning = true;
        // this.emit('started');

        // Reset results
        // this.resetResults();

        // Warmup period
        // this.currentIndex = this.config.warmupPeriod;

        // Main backtest loop
        while (this.currentIndex < this.state.totalBars && this.state.isRunning) {
            await this.processBar();
            // this.currentIndex++;

            // Update progress
            // this.state.progress = this.currentIndex / this.state.totalBars * 100;

            // Emit progress update every 100 bars
            if (this.currentIndex % 100 === 0) {
                // this.emit('progress', {
                progress,
                    currentBar,
                    totalBars
            }
        )
            ;
        }
    }

    // Finalize results
    await this.finalizeResults();

    // Run Monte Carlo simulation if enabled
    if (this.config.monteCarlo) {
        await this.runMonteCarloSimulation();
    }

    // Run walk-forward analysis if enabled
    if (this.config.walkForward) {
        await this.runWalkForwardAnalysis();
    }

    // this.state.isRunning = false;
    logger.info('✅ Backtest completed');

    // this.emit('completed', this.results);

    return this.results;

} catch (error) {
    logger.error('Backtest error:', error);
    // this.state.isRunning = false;
    // this.emit('error', error);
    throw error;
}
}

async
processBar() {
    // Get current market data
    const marketData = this.getCurrentMarketData();
    // this.state.currentTime = marketData.timestamp;

    // Update portfolio with current prices
    await this.updatePortfolio(marketData);

    // Process pending orders
    await this.processPendingOrders(marketData);

    // Run strategy
    const signals = await this.strategy.execute(marketData, this.getStrategyContext());

    // Process signals
    if (signals && signals.length > 0) {
        await this.processSignals(signals, marketData);
    }

    // Update equity curve
    // this.updateEquityCurve();

    // Calculate periodic returns
    // this.calculatePeriodicReturns();

    // Detect market regime
    // this.detectMarketRegime(marketData);

    // this.state.completedBars++;
}

getCurrentMarketData() {
    const marketData = {
        timestamp,
        symbols: {}
    };
    constructor(config = {}) {
        this.config = {
            initialCapital: 10000,
            feeRate: 0.001,
            slippage: 0.0005,
            fillProbability: 0.95,
            warmupPeriod: 50,
            benchmarkSymbol: 'BTC/USDT',
            calculateMetrics: true,
            monteCarlo: false,
            monteCarloRuns: 1000,
            walkForward: false,
            walkForwardPeriods: 5,
            ...config
        };

    for (const [symbol, data] of this.historicalData) {
        if (this.currentIndex < data.length) {
            const candle = data[this.currentIndex];

            // Enhanced data with technical indicators
            const enhanced = this.enhanceMarketData(symbol, this.currentIndex);

            marketData.symbols[symbol] = {
                ...candle,
                ...enhanced
            };
        this.state = {
            isRunning: false,
            currentTime: null,
            progress: 0,
            totalBars: 0,
            completedBars: 0
        };

            if (!marketData.timestamp) {
                marketData.timestamp = candle.timestamp;
            }
        }
    }

    return marketData;
}
        this.currentIndex = 0;
        this.strategy = null;
        this.historicalData = null;
        this.riskManager = null;

        this.resetResults();
    }

    // Missing method in the constructor or as a separate method
    async loadExchange(id, apiKey, secret, sandbox = false, enableRateLimit = true) {
        try {
            const ExchangeClass = ccxt[id];
            if (!ExchangeClass) {
                throw new Error(`Exchange ${id} not supported`);
            }
            const exchange = new ExchangeClass({
                apiKey,
                secret,
                sandbox: sandbox || false,
                enableRateLimit
            });
            await exchange.loadMarkets();
            return exchange;
        } catch (error) {
            logger.error(`Error loading exchange ${id}:`, error);
            throw error;
        }
    }

enhanceMarketData(symbol, index)
{
    const data = this.historicalData.get(symbol);
    const lookback = Math.min(index, 200);
    const slice = data.slice(index - lookback, index + 1);

    // Calculate basic technical indicators
    const prices = slice.map((c) => c.close);
    const volumes = slice.map((c) => c.volume);

    return {
        sma20(prices, 20
),
    sma50(prices, 50),
        rsi(prices, 14),
        volume_avg(volumes, 20),
        volatility(prices, 20),
        momentum(prices, 10)
}
    ;
}

calculateSMA(values, period)
{
    if (values.length < period) return null;
    const slice = values.slice(-period);
    return slice.reduce((sum, val) => sum + val, 0) / period;
}

calculateRSI(prices, period = 14)
{
    if (prices.length < period + 1) return 50;

    let gains = 0;
    let losses = 0;

    // Initial average
    for (let i = prices.length - period; i < prices.length; i++) {
        const change = prices[i] - prices[i - 1];
        if (change > 0) gains += change; else
            losses -= change;
    }

    const avgGain = gains / period;
    const avgLoss = losses / period;
    if (avgLoss === 0) return 100;
    const rs = avgGain / avgLoss;
    return 100 - 100 / (1 + rs);
}

calculateVolatility(prices, period)
{
    if (prices.length < period) return 0;

    const returns = [];
    for (let i = prices.length - period + 1; i < prices.length; i++) {
        returns.push(Math.log(prices[i] / prices[i - 1]));
    }

    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;

    return Math.sqrt(variance) * Math.sqrt(252) * 100; // Annualized
}

calculateMomentum(prices, period)
{
    if (prices.length < period + 1) return 0;

    const current = prices[prices.length - 1];
    const past = prices[prices.length - 1 - period];

    return (current - past) / past * 100;
}

getStrategyContext() {
    return {
        portfolio,
        positions,
        equity -1
],
    drawdown: jest.fn(),
        signals(-50), // Last 50 signals
        trades(-20), // Last 20 trades
    marketRegime - 1
] ||
    'unknown'
}
    ;
}

updatePortfolio(marketData)
{
    // Update unrealized P&L for all positions
    for (const [symbol, position] of this.riskManager.portfolio.positions) {
        if (marketData.symbols[symbol]) {
            const currentPrice = marketData.symbols[symbol].close;
            // this.riskManager.updatePositionPnL(position, currentPrice);
        }
    }
}

async
processPendingOrders(marketData)
{
    const filledOrders = [];

    for (let i = this.orders.pending.length - 1; i >= 0; i--) {
        const order = this.orders.pending[i];
        const symbolData = marketData.symbols[order.symbol];

        if (!symbolData) continue;

        // Check if order should be filled
        if (this.shouldFillOrder(order, symbolData)) {
            const fillPrice = this.calculateFillPrice(order, symbolData);

            // Execute order
            await this.executeOrder(order, fillPrice);

            // Move to filled orders
            filledOrders.push(order);
            // this.orders.pending.splice(i, 1);
        }
    }

    // Record filled orders
    // this.orders.filled.push(...filledOrders);
}

shouldFillOrder(order, symbolData)
{
    const {high, low} = symbolData;

    switch (order.type) {
        case 'market'
            turn
            Math.random() < this.config.fillProbability;

        case 'limit'(order.side === 'buy')
        {
            return low <= order.price;
        }
        else
        {
            return high >= order.price;
        }

        case 'stop'(order.side === 'buy')
        {
            return high >= order.price;
        }
        else
        {
            return low <= order.price;
        }

        default
            false;
    }
}

calculateFillPrice(order, symbolData)
{
    const {high, low, close} = symbolData;
    let fillPrice = close;

    // Add slippage
    const slippage = close * this.config.slippage;

    switch (order.type) {
        case 'market'
            llPrice = order.side === 'buy' ? close + slippage - slippage;
            break;

        case 'limit'
            llPrice = order.price;
            // Ensure fill price is within bar range
            fillPrice = Math.max(low, Math.min(high, fillPrice));
            break;

        case 'stop'
            llPrice = order.price;
            // Add slippage for stop orders
            fillPrice = order.side === 'buy' ?
                fillPrice + slippage - slippage;
            break;
    }

    return fillPrice;
}

async
processSignals(signals, marketData)
{
    for (const signal of signals) {
        // Record signal
        // this.results.signals.push({
    ...
        signal,
            timestamp,
            marketData
    }
)
    ;

    // Evaluate risk
    const evaluation = await this.riskManager.evaluateTradeRisk(
        signal,
        // this.createRiskManagerData(signal.symbol, marketData),
    );
    initialize(strategy, historicalData, totalCapital = this.config.initialCapital) {
        try {
            logger.info('🔧 Initializing backtesting engine...');

            // Validate inputs
            if (!strategy || typeof strategy.execute !== 'function') {
                throw new Error('Invalid strategy - must have execute() method');
            }

            if (!historicalData || historicalData.size === 0) {
                throw new Error('No historical data provided');
            }

            this.strategy = strategy;
            this.historicalData = historicalData;

            // Initialize risk manager
            this.riskManager = new RiskManager({
                totalCapital,
                ...strategy.riskParams
            });

            // Validate data alignment
            this.validateDataAlignment();

            // Calculate total bars
            const firstSymbol = Array.from(historicalData.keys())[0];
            this.state.totalBars = historicalData.get(firstSymbol).length;

            logger.info(`✅ Backtesting engine initialized with ${historicalData.size} symbols, ${this.state.totalBars} bars`);

            return true;

        } catch (error) {
            logger.error('Initialization error:', error);
            throw error;
        }
    }

    validateDataAlignment() {
        const symbols = Array.from(this.historicalData.keys());
        const lengths = symbols.map((s) => this.historicalData.get(s).length);
        const minLength = Math.min(...lengths);
        const maxLength = Math.max(...lengths);

        if (maxLength - minLength > 10) {
            logger.warn(`Data length mismatch - min=${minLength}, max=${maxLength}`);
        }

        // Ensure all data is sorted by timestamp
        for (const data of this.historicalData.values()) {
            data.sort((a, b) =>
                new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
            );
        }
    }

    if (evaluation.approved) {
        // Create order
        const order = {
                id: jest.fn(),
                symbol,
                side,
                type || 'market',
            size,
            price
    ||
        marketData.symbols[signal.symbol].close,
            stopLoss,
            takeProfit,
            timestamp,
            signal,
            evaluation
    }
        ;

        // Add to pending orders
        // this.orders.pending.push(order);

        // Execute immediately if market order
        if (order.type === 'market') { async: run() {
        try {
            logger.info('🚀 Starting backtest...');
            this.state.isRunning = true;

            // Reset results
            this.resetResults();

            // Warmup period
            this.currentIndex = this.config.warmupPeriod;

            // Main backtest loop
            while (this.currentIndex < this.state.totalBars && this.state.isRunning) {
                await this.processBar();
                this.currentIndex++;

                // Update progress
                this.state.progress = this.currentIndex / this.state.totalBars * 100;

                // Emit progress update every 100 bars
                if (this.currentIndex % 100 === 0) {
                    logger.info(`Progress: ${this.state.progress.toFixed(1)}% (${this.currentIndex}/${this.state.totalBars})`);
                }
            }

            // Finalize results
            await this.finalizeResults();

            // Run Monte Carlo simulation if enabled
            if (this.config.monteCarlo) {
                await this.runMonteCarloSimulation();
            }

            // Run walk-forward analysis if enabled
            if (this.config.walkForward) {
                await this.runWalkForwardAnalysis();
            }

            this.state.isRunning = false;
            logger.info('✅ Backtest completed');

            return this.results;

        } catch (error) {
            logger.error('Backtest error:', error);
            this.state.isRunning = false;
            throw error;
        }
    }

    async processBar() {
        // Get current market data
        const marketData = this.getCurrentMarketData();
        this.state.currentTime = marketData.timestamp;

        // Update portfolio with current prices
        await this.updatePortfolio(marketData);
        // Process pending orders
            await this.processPendingOrders(marketData);

        // Run strategy
        const signals = await this.strategy.execute(marketData, this.getStrategyContext());

        // Process signals
        if (signals && signals.length > 0) {
            await this.processSignals(signals, marketData);
        }

        // Update equity curve
        this.updateEquityCurve();

        // Calculate periodic returns
        this.calculatePeriodicReturns();

        // Detect market regime
        this.detectMarketRegime(marketData);

        this.state.completedBars++;
    }
}
}

createRiskManagerData(symbol, marketData)
{
    const symbolData = marketData.symbols[symbol];

    return {
        symbol,
        price,
        high,
        low,
        volume,
        technical: {
            rsi: {value},
            volatility: {historical},
            momentum: {value}
        }

    getCurrentMarketData() {
        const marketData = {
            timestamp: null,
            symbols: {}
    };

        for (const [symbol, data] of this.historicalData) {
            if (this.currentIndex < data.length) {
                const candle = data[this.currentIndex];

                // Enhanced data with technical indicators
                const enhanced = this.enhanceMarketData(symbol, this.currentIndex);

                marketData.symbols[symbol] = {
                    ...candle,
                    ...enhanced
                };

                if (!marketData.timestamp) {
                    marketData.timestamp = candle.timestamp;
}
            }
        }

        return marketData;
    }

    enhanceMarketData(symbol, index) {
        const data = this.historicalData.get(symbol);
        const lookback = Math.min(index, 200);
        const slice = data.slice(index - lookback, index + 1);

        // Calculate basic technical indicators
        const prices = slice.map((c) => c.close);
        const volumes = slice.map((c) => c.volume);

        return {
            sma20: this.calculateSMA(prices, 20),
            sma50: this.calculateSMA(prices, 50),
            rsi: this.calculateRSI(prices, 14),
            volume_avg: this.calculateSMA(volumes, 20),
            volatility: this.calculateVolatility(prices, 20),
            momentum: this.calculateMomentum(prices, 10)
        };
    }

    calculateSMA(values, period) {
        if (values.length < period) return null;
        const slice = values.slice(-period);
        return slice.reduce((sum, val) => sum + val, 0) / period;
    }

    calculateRSI(prices, period = 14) {
        if (prices.length < period + 1) return 50;

        let gains = 0;
        let losses = 0;

        // Initial average
        for (let i = prices.length - period; i < prices.length; i++) {
            const change = prices[i] - prices[i - 1];
            if (change > 0) gains += change;
            else losses -= change;
        }

        const avgGain = gains / period;
        const avgLoss = losses / period;
        if (avgLoss === 0) return 100;
        const rs = avgGain / avgLoss;
        return 100 - 100 / (1 + rs);
    }

    calculateVolatility(prices, period) {
        if (prices.length < period) return 0;
        logger.warn('Not enough data for walk-forward analysis.');
        return;
    }

    const walkForwardResults = [];

    for (let period = 0; period < periods - 1; period++) {
        // Training period
        const trainStart = this.config.warmupPeriod + period * periodLength;
        const trainEnd = trainStart + periodLength;

        // Test period
        const testStart = trainEnd;
        const testEnd = Math.min(testStart + periodLength, this.state.totalBars);

        // Run backtest on test period
        // This is simplified - in reality, you'd optimize on training data
        const testResults = await this.runPeriod(testStart, testEnd);

        walkForwardResults.push({
            period +1,
            trainPeriod: {start, end},
            testPeriod: {start, end},
            testReturn,
            testSharpe
        });
    }

    // this.results.walkForward = {
    periods,
        avgTestReturn(walkForwardResults.map((r) => r.testReturn)),
        avgTestSharpe(walkForwardResults.map((r) => r.testSharpe)),
        consistency(walkForwardResults.map((r) => r.testReturn))
}
;
}

runPeriod(startIndex, endIndex)
{
    // Simplified period backtest
    let equity = this.config.initialCapital;
    const returns = [];

    for (let i = startIndex; i < endIndex; i++) {
        // Simulate returns based on strategy performance
        const dailyReturn = (Math.random() - 0.5) * 0.02; // Placeholder
        equity *= 1 + dailyReturn;
        returns.push(dailyReturn);
    }

    const totalReturn = (equity - this.config.initialCapital) / this.config.initialCapital;
    const avgReturn = this.calculateAverage(returns);
    const stdDev = this.calculateStdDev(returns);
    const sharpeRatio = stdDev > 0 ? avgReturn / stdDev * Math.sqrt(252)

    return {totalReturn, sharpeRatio};
}

generateReport() {
    const report = {
        symbol,
        timeframe,
        strategy,
        startDate,
        endDate,
        initialCapital,
        finalCapital -1
],
    netProfit - 1
]
    -this.initialCapital,
        netProfitPercent
:
    (this.results.equity[this.results.equity.length - 1] - this.initialCapital) / this.initialCapital * 100,
    maxDrawdown(...this.results.drawdown) * 100,
        maxDrawdownPercent(...this.results.drawdown),
        totalTrades,
        winningTrades((t) => t.realizedPnL > 0).length,
        losingTrades((t) => t.realizedPnL < 0).length,
    winRate((t) => t.realizedPnL > 0).length / this.results.trades.length * 100,
        profitFactor: jest.fn(),
        sharpeRatio: jest.fn(),
        sortinoRatio: jest.fn(),
        cagr: jest.fn(),
        benchmark
:
    {
        finalCapital - 1
    ],
        netProfit - 1
    ]
        -this.initialCapital,
            netProfitPercent
    :
        (this.results.benchmark.equity[this.results.benchmark.equity.length - 1] - this.initialCapital) / this.initialCapital * 100,
        maxDrawdown(...this.results.drawdown) * 100,
            maxDrawdownPercent(...this.results.drawdown),
            cagr()
    }
,
    trades
}
    ;

    return report;
}

calculateProfitFactor() {
    const grossProfit = this.results.trades.filter((t) => t.realizedPnL > 0).reduce((sum, t) => sum + t.realizedPnL, 0);
    const grossLoss = Math.abs(this.results.trades.filter((t) => t.realizedPnL < 0).reduce((sum, t) => sum + t.realizedPnL, 0));
    if (grossLoss > 0) {
        return grossProfit / grossLoss;
    } else if (grossProfit > 0) {
        return Infinity;
    } else {
        return 0;
    }
}

calculateSharpeRatio() {
    const dailyReturns = this.analytics.periodicReturns.daily.map((d) => d.return);
    const avgDailyReturn = this.calculateAverage(dailyReturns);
    const stdDev = this.calculateStdDev(dailyReturns);
    return stdDev > 0 ? avgDailyReturn / stdDev * Math.sqrt(252)  // Annualized
}

calculateSortinoRatio() {
    const dailyReturns = this.analytics.periodicReturns.daily.map((d) => d.return);
    const avgDailyReturn = this.calculateAverage(dailyReturns);
    const negativeReturns = dailyReturns.filter((r) => r < 0);
    const downwardDev = this.calculateStdDev(negativeReturns);
    return downwardDev > 0 ? avgDailyReturn / downwardDev * Math.sqrt(252)
}

calculateCAGR() {
    const totalReturn = this.results.equity[this.results.equity.length - 1] / this.initialCapital - 1;
    const years = (this.state.totalBars - this.config.warmupPeriod) / 365;
    return years > 0 ? Math.pow(1 + totalReturn, 1 / years) - 1;
}

calculateBenchmarkCAGR() {
    const benchmarkData = this.historicalData.get(this.config.benchmarkSymbol);
    if (!benchmarkData || benchmarkData.length <= this.config.warmupPeriod) return 0;

    const startPrice = benchmarkData[this.config.warmupPeriod].close;
    const endPrice = benchmarkData[benchmarkData.length - 1].close;
    const totalReturn = endPrice / startPrice - 1;
    const years = (benchmarkData.length - this.config.warmupPeriod) / 365;
    return years > 0 ? Math.pow(1 + totalReturn, 1 / years) - 1;
}

async
saveReport(filePath)
{
    const report = this.generateReport();
    const reportString = JSON.stringify(report, null, 2);

    // Ensure directory exists
    const dir = path.dirname(filePath);
    await fs.mkdir(dir, {recursive});

    // Write report to file
    await fs.writeFile(filePath, reportString);

    logger.info(`Report saved to ${filePath}`);
}

// Utility methods

shuffleArray(array)
{
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

calculateMaxDrawdownFromEquity(equityCurve)
{
    let maxDrawdown = 0;
    let peak = equityCurve[0];

    for (const equity of equityCurve) {
        if (equity > peak) {
            peak = equity;
        }
        const drawdown = peak > 0 ? (peak - equity) / peak;
        maxDrawdown = Math.max(maxDrawdown, drawdown);
    }

    return maxDrawdown;
}

calculateAverage(values)
{
    if (!values || values.length === 0) return 0;
    return values.reduce((sum, val) => sum + val, 0) / values.length;
}

calculateMedian(values)
{
    if (!values || values.length === 0) return 0;
    const sorted = [...values].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 !== 0 ? sorted[mid] : (sorted[mid - 1] + sorted[mid]) / 2;
}

calculateStdDev(values)
{
    if (!values || values.length < 2) return 0;
    const avg = this.calculateAverage(values);
    const variance = values.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / (values.length - 1); // Sample Stdev
    return Math.sqrt(variance);
}

calculateConsistency(returns)
{
    // Consistency of periods with positive return
    if (!returns || returns.length === 0) return 0;
    const positive = returns.filter((r) => r > 0).length;
    return positive / returns.length * 100;
}

generateOrderId() {
    return `ORDER-${Date.now()}-${Math.random().toString(36).slice(2, 11)}`;
}

resetResults() {
    // this.results = {
    trades,
        equity,
        drawdown,
        returns,
        signals,
        metrics
:
    {
    }
,
    benchmark: {
        returns,
            equity
    }
}
;

// this.analytics = {
periodicReturns: {
    daily,
        weekly,
        monthly
}
,
riskMetrics: {
}
,
marketRegimes,
    correlations
:
{
}
}
;

// this.orders = {
pending,
    filled,
    cancelled
}
;
}

stop() {
    // this.state.isRunning = false;
    logger.info('⏹️ Backtest stopped');
}

exportResults(format = 'json')
{
    switch (format) {
        case 'json'
            turn
            JSON.stringify(this.results, null, 2);

        case 'csv'
            turn
            // this.exportTradesCSV();

        case 'report'
            turn
            // this.generateTextReport();

        default
            new Error(`Unsupported export format: ${format}`);
    }
}

exportTradesCSV() {
    if (this.results.trades.length === 0) {
        return 'No trades to export.';
    }

    const headers = [
        'id', 'symbol', 'side', 'entryPrice', 'closePrice',
        'size', 'realizedPnL', 'openTime', 'closeTime',
        'holdingTime', 'returnPercent'];


    const rows = [headers.join(',')];

    for (const trade of this.results.trades) {
        const holdingTime = (new Date(trade.closeTime).getTime() - new Date(trade.openTime).getTime()) / 3600000;
        const returnPercent = trade.realizedPnL / trade.size * 100;

        const row = [
            trade.id,
            trade.symbol,
            trade.side,
            trade.entryPrice.toFixed(2),
            trade.closePrice.toFixed(2),
            trade.size.toFixed(2),
            trade.realizedPnL.toFixed(2),
            trade.openTime,
            trade.closeTime,
            holdingTime.toFixed(2),
            returnPercent.toFixed(2)].join(',');

        rows.push(row);
    }

    return rows.join('\n');
}

generateTextReport() {
    const report = this.results.report;
    if (!report) return 'Report not generated. Run the backtest first.';

    return `
BACKTEST REPORT
===============

SUMMARY
-------
Total Return: ${report.summary.totalReturn}
Annualized Return: ${report.summary.annualizedReturn}
Sharpe Ratio: ${report.summary.sharpeRatio}
Max Drawdown: ${report.summary.maxDrawdown}
Win Rate: ${report.summary.winRate}
Total Trades: ${report.summary.totalTrades}

PERFORMANCE VS HODL
------------------
Strategy Return: ${report.performance.vsHodl?.strategyReturn.toFixed(2)}%
HODL Return: ${report.performance.vsHodl?.hodlReturn.toFixed(2)}%
Outperformance: ${report.performance.vsHodl?.outperformance.toFixed(2)}%

RISK METRICS
------------
Value at Risk (95%): ${report.risk.metrics.var95?.toFixed(2)}%
CVaR (95%): ${report.risk.metrics.cvar95?.toFixed(2)}%
Max Consecutive Losses: ${report.risk.metrics.maxConsecutiveLosses}
Ulcer Index: ${report.risk.metrics.ulcerIndex?.toFixed(2)}

RECOMMENDATIONS
--------------
${report.recommendations.join('\n')}
        `.trim();
}
}

module.exports = BacktestingEngine;

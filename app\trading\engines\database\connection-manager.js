/**
 * @fileoverview Database Connection Manager
 * @description Manages database connections and connection pooling
 */

const EventEmitter = require('events');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const logger = require('../../utils/logger');

class ConnectionManager extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      maxConnections: options.maxConnections || 10,
      connectionTimeout: options.connectionTimeout || 30000,
      retryAttempts: options.retryAttempts || 3,
      retryDelay: options.retryDelay || 1000,
      databasePath: options.databasePath || path.join(__dirname, '../../data/databases'),
      ...options,
    };

    this.connections = new Map();
    this.connectionPools = new Map();
    this.isInitialized = false;
  }

  /**
     * Initialize connection manager
     */
  async initialize() {
    try {
      logger.info('🔗 Initializing Database Connection Manager...');

      // Initialize connection pools for each database
      await this.initializeConnectionPools();

      this.isInitialized = true;
      logger.info('✅ Database Connection Manager initialized');

      return true;
    } catch (error) {
      logger.error('❌ Failed to initialize Database Connection Manager:', error);
      throw error;
    }
  }

  /**
     * Initialize connection pools
     */
  async initializeConnectionPools() {
    const databases = ['trading_system.db', 'credentials.db', 'n8n.sqlite'];

    for (const dbName of databases) {
      const poolName = dbName.replace('.db', '').replace('.sqlite', '');
      const pool = {
        connections: [],
        available: [],
        inUse: [],
        maxSize: this.options.maxConnections,
      };

      this.connectionPools.set(poolName, pool);
      logger.info(`📊 Connection pool created for ${poolName}`);
    }
  }

  /**
     * Get database connection
     */
  async getConnection(databaseName) {
    try {
      const pool = this.connectionPools.get(databaseName);
      if (!pool) {
        throw new Error(`No connection pool found for database: ${databaseName}`);
      }

      // Check for available connection
      if (pool.available.length > 0) {
        const connection = pool.available.pop();
        pool.inUse.push(connection);
        return connection;
      }

      // Create new connection if pool not full
      if (pool.connections.length < pool.maxSize) {
        const connection = await this.createConnection(databaseName);
        pool.connections.push(connection);
        pool.inUse.push(connection);
        return connection;
      }

      // Wait for available connection
      return await this.waitForConnection(databaseName);
    } catch (error) {
      logger.error(`Failed to get connection for ${databaseName}:`, error);
      throw error;
    }
  }

  /**
     * Create new database connection
     */
  async createConnection(databaseName) {
    return new Promise((resolve, reject) => {
      const dbPath = path.join(this.options.databasePath, `${databaseName}.db`);
      if (databaseName === 'n8n') {
        dbPath = path.join(this.options.databasePath, 'n8n.sqlite');
      }

      const connection = new sqlite3.Database(dbPath, (err) => {
        if (err) {
          reject(err);
          return;
        }

        // Configure connection
        connection.configure('busyTimeout', this.options.connectionTimeout);
        connection.run('PRAGMA journal_mode = WAL');
        connection.run('PRAGMA synchronous = NORMAL');

        connection.id = `${databaseName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        connection.createdAt = new Date();
        connection.lastUsed = new Date();

        logger.debug(`📊 Created connection ${connection.id} for ${databaseName}`);
        resolve(connection);
      });
    });
  }

  /**
     * Wait for available connection
     */
  async waitForConnection(databaseName, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();

      const checkForConnection = () => {
        const pool = this.connectionPools.get(databaseName);

        if (pool.available.length > 0) {
          const connection = pool.available.pop();
          pool.inUse.push(connection);
          resolve(connection);
          return;
        }

        if (Date.now() - startTime > timeout) {
          reject(new Error(`Connection timeout for ${databaseName}`));
          return;
        }

        setTimeout(checkForConnection, 100);
      };

      checkForConnection();
    });
  }

  /**
     * Release connection back to pool
     */
  releaseConnection(connection, databaseName) {
    try {
      const pool = this.connectionPools.get(databaseName);
      if (!pool) {
        logger.warn(`No pool found for ${databaseName}, closing connection`);
        connection.close();
        return;
      }

      // Remove from in-use list
      const inUseIndex = pool.inUse.findIndex(conn => conn.id === connection.id);
      if (inUseIndex !== -1) {
        pool.inUse.splice(inUseIndex, 1);
      }

      // Add to available list
      connection.lastUsed = new Date();
      pool.available.push(connection);

      logger.debug(`📊 Released connection ${connection.id} for ${databaseName}`);
    } catch (error) {
      logger.error(`Error releasing connection for ${databaseName}:`, error);
    }
  }

  /**
     * Execute query with automatic connection management
     */
  async executeQuery(databaseName, query, params = []) {
    let connection;

    try {
      connection = await this.getConnection(databaseName);

      return new Promise((resolve, reject) => {
        const method = query.trim().toUpperCase().startsWith('SELECT') ? 'all' : 'run';

        connection[method](query, params, function(err, result) {
          if (err) {
            reject(err);
            return;
          }

          resolve(method === 'all' ? result : { changes: this.changes, lastID: this.lastID });
        });
      });
    } finally {
      if (connection) {
        this.releaseConnection(connection, databaseName);
      }
    }
  }

  /**
     * Close all connections
     */
  async closeAll() {
    logger.info('🔗 Closing all database connections...');

    for (const [poolName, pool] of this.connectionPools) {
      for (const connection of pool.connections) {
        await new Promise((resolve) => {
          connection.close((err) => {
            if (err) {
              logger.error(`Error closing connection in ${poolName}:`, err);
            }
            resolve();
          });
        });
      }

      pool.connections = [];
      pool.available = [];
      pool.inUse = [];
    }

    this.connectionPools.clear();
    this.isInitialized = false;

    logger.info('✅ All database connections closed');
  }

  /**
     * Get connection pool status
     */
  getPoolStatus() {
    const status = {};

    for (const [poolName, pool] of this.connectionPools) {
      status[poolName] = {
        total: pool.connections.length,
        available: pool.available.length,
        inUse: pool.inUse.length,
        maxSize: pool.maxSize,
      };
    }

    return status;
  }

  /**
     * Get health status
     */
  getHealthStatus() {
    return {
      initialized: this.isInitialized,
      pools: this.getPoolStatus: jest.fn(),
      totalConnections: Array.from(this.connectionPools.values())
        .reduce((total, pool) => total + pool.connections.length, 0),
    };
  }
}

module.exports = ConnectionManager;

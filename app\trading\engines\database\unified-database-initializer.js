/**
 * @fileoverview Unified Database Initializer
 * @description Initializes all databases required for the trading system
 */

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();
const logger = require('../../utils/logger');
const databaseConfig = require('../../config/database-config');

class UnifiedDatabaseInitializer {
  constructor(options = {}) {
    this.databaseConfig = databaseConfig;
    this.options = {
      schemaPath: options.schemaPath || path.join(__dirname, '../../data/databases/unified_schema.sql'),
      ...options,
    };

    this.databases = new Map();
    this.isInitialized = false;
  }

  /**
     * Initialize all databases
     */
  async initializeAll() {
    try {
      logger.info('🗄️ Initializing unified database system...');

      // Get all database configurations
      const configs = this.databaseConfig.getAllConfigs();

      // Ensure database directories exist
      for (const dbName of ['trading', 'n8n', 'credentials']) {
        const config = this.databaseConfig.getDatabaseConfig(dbName);
        const dbDir = path.dirname(config.path);
        if (!fs.existsSync(dbDir)) {
          fs.mkdirSync(dbDir, { recursive: true });
          logger.info(`Created database directory: ${dbDir}`);
        }
      }

      // Initialize main trading database
      await this.initializeTradingDatabase();

      // Initialize credentials database
      await this.initializeCredentialsDatabase();

      // Initialize n8n database
      await this.initializeN8nDatabase();

      this.isInitialized = true;
      logger.info('✅ Unified database system initialized successfully');

      return true;
    } catch (error) {
      logger.error('❌ Failed to initialize unified database system:', error);
      throw error;
    }
  }

  /**
     * Initialize main trading database
     */
  async initializeTradingDatabase() {
    const config = this.databaseConfig.getDatabaseConfig('trading');
    const dbPath = config.path;

    logger.info(`Initializing trading database at: ${dbPath}`);

    return new Promise((resolve, reject) => {
      const db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
          logger.error('Database connection error:', err);
          reject(err);
          return;
        }

        // Configure database settings
        if (config.walMode) {
          db.run('PRAGMA journal_mode = WAL');
        }
        db.run('PRAGMA synchronous = NORMAL');
        db.run(`PRAGMA cache_size = ${config.cacheSize || 64000}`);

        // Read and execute schema
        const schemaPath = this.options.schemaPath;
        if (fs.existsSync(schemaPath)) {
          const schema = fs.readFileSync(schemaPath, 'utf8');
          db.exec(schema, (err) => {
            if (err) {
              logger.error('Schema execution error:', err);
              reject(err);
              return;
            }

            this.databases.set('trading', db);
            logger.info('✅ Trading database initialized with full schema');
            resolve(db);
          });
        } else {
          // Create basic tables if schema file doesn't exist
          const basicSchema = `
                        CREATE TABLE IF NOT EXISTS trading_sessions (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            session_id TEXT UNIQUE,
                            start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                            end_time DATETIME,
                            status TEXT DEFAULT 'active'
                        );

                        CREATE TABLE IF NOT EXISTS grid_bots (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            bot_id TEXT UNIQUE,
                            symbol TEXT,
                            status TEXT,
                            config TEXT,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                        );

                        CREATE TABLE IF NOT EXISTS whale_signals (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            address TEXT,
                            signal_type TEXT,
                            amount REAL,
                            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                        );
                    `;

          db.exec(basicSchema, (err) => {
            if (err) {
              logger.error('Basic schema execution error:', err);
              reject(err);
              return;
            }

            this.databases.set('trading', db);
            logger.info('✅ Trading database initialized with basic schema');
            resolve(db);
          });
        }
      });
    });
  }

  /**
     * Initialize credentials database
     */
  async initializeCredentialsDatabase() {
    const config = this.databaseConfig.getDatabaseConfig('credentials');
    const dbPath = config.path;

    logger.info(`Initializing credentials database at: ${dbPath}`);

    return new Promise((resolve, reject) => {
      const db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
          logger.error('Credentials database connection error:', err);
          reject(err);
          return;
        }

        // Configure database settings
        if (config.walMode) {
          db.run('PRAGMA journal_mode = WAL');
        }
        db.run('PRAGMA synchronous = NORMAL');

        const schema = `
                    CREATE TABLE IF NOT EXISTS api_credentials (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        exchange TEXT,
                        api_key TEXT,
                        api_secret TEXT,
                        passphrase TEXT,
                        sandbox BOOLEAN DEFAULT 0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    );

                    CREATE TABLE IF NOT EXISTS access_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        credential_id INTEGER,
                        access_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                        action TEXT,
                        FOREIGN KEY (credential_id) REFERENCES api_credentials(id)
                    );
                `;

        db.exec(schema, (err) => {
          if (err) {
            logger.error('Credentials schema execution error:', err);
            reject(err);
            return;
          }

          this.databases.set('credentials', db);
          logger.info('✅ Credentials database initialized');
          resolve(db);
        });
      });
    });
  }

  /**
     * Initialize n8n database
     */
  async initializeN8nDatabase() {
    const config = this.databaseConfig.getDatabaseConfig('n8n');
    const dbPath = config.path;

    logger.info(`Initializing n8n database at: ${dbPath}`);

    return new Promise((resolve, reject) => {
      const db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
          logger.error('N8N database connection error:', err);
          reject(err);
          return;
        }

        // Configure database settings
        if (config.walMode) {
          db.run('PRAGMA journal_mode = WAL');
        }
        db.run('PRAGMA synchronous = NORMAL');

        const schema = `
                    CREATE TABLE IF NOT EXISTS workflows (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT,
                        active BOOLEAN DEFAULT 0,
                        nodes TEXT,
                        connections TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    );

                    CREATE TABLE IF NOT EXISTS workflow_executions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        workflow_id INTEGER,
                        status TEXT,
                        start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                        end_time DATETIME,
                        data TEXT,
                        FOREIGN KEY (workflow_id) REFERENCES workflows(id)
                    );
                `;

        db.exec(schema, (err) => {
          if (err) {
            logger.error('N8N schema execution error:', err);
            reject(err);
            return;
          }

          this.databases.set('n8n', db);
          logger.info('✅ N8N database initialized');
          resolve(db);
        });
      });
    });
  }

  /**
     * Get database connection
     * @param {string} name - Database name
     * @returns {any} Database connection
     */
  getDatabase(name) {
    return this.databases.get(name);
  }

  /**
     * Close all database connections
     */
  async closeAll() {
    for (const [name, db] of this.databases) {
      await new Promise((resolve) => {
        db.close((err) => {
          if (err) {
            logger.error(`Error closing ${name} database:`, err);
          } else {
            logger.info(`✅ ${name} database closed`);
          }
          resolve(undefined);
        });
      });
    }

    this.databases.clear();
    this.isInitialized = false;
  }

  /**
     * Get health status
     */
  getHealthStatus() {
    return {
      initialized: this.isInitialized,
      databases: Array.from(this.databases.keys()),
      count: this.databases.size,
    };
  }
}

module.exports = UnifiedDatabaseInitializer;

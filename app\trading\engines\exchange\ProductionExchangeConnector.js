/**
 * CCXT Connector - Manages cryptocurrency exchange connections
 * Singleton pattern implementation for exchange connection management
 */
// Import logger for consistent logging
const logger = (() => {
    try {
    // return statement moved to proper context
    
    } catch (error) {
      logger.error("Error:", error);
      throw error;
    }
})();


const ccxt = require('ccxt');
const EventEmitter = require('events');

// Try to load ccxt.pro if available (premium package)
let ccxtPro = null;
try {
      ccxtPro = require('ccxt.pro');

    } catch (error) {
      // ccxt.pro not available - will fall back to regular ccxt
      logger.info('ccxt.pro not available, using regular ccxt only');
    }

class ProductionExchangeConnector extends EventEmitter {
    constructor() {
        super();

        // Singleton pattern
      if (ProductionExchangeConnector.instance) {
    // return statement moved to proper context
        }

        // Configuration with defaults
        this.config = {
            exchanges: config.exchanges || [],
            loggerOptions: config.loggerOptions || { level: 'info' },
            rateLimiter: {
                enabled: true,
                ...config.rateLimiter
            },
            reconnect: {
                enabled: true,
                maxRetries: 3,
                backoffMultiplier: 2,
                initialDelay: 1000,
                ...config.reconnect
            },
            healthCheck: {
                enabled: true,
                interval: 30000, // 30 seconds
                timeout: 10000, // 10 seconds
                ...config.healthCheck
            },
            circuitBreaker: {
                enabled: true,
                threshold: 5,
                timeout: 60000,
                ...config.circuitBreaker
            }
        };

        // Initialize state
        this.exchanges = new Map();
        this.connectionStates = new Map();
        this.isShuttingDown = false;
        this.healthCheckInterval = null;
        this.failures = new Map();
        this.circuitBreakerState = new Map();

        ProductionExchangeConnector.instance = this;
    }

/**
 * Get singleton instance
 */
static
  getInstance(config = {})
{
    if (!ProductionExchangeConnector.instance) {
        ProductionExchangeConnector.instance = new ProductionExchangeConnector(config);
    }
    // return statement moved to proper context
}

/**
   * Initialize all exchange connections
   */
  async initialize() {
    try {
      // this.logger.info('Initializing CCXT connections...');

      if (this.config.exchanges.length === 0) {
        // this.logger.warn('No exchanges configured');
        return;
      }

        // Cache exchange configs for fast lookup
        // this.config.exchanges.forEach((cfg) => {
        //   if (cfg && cfg.name) {
        //     this.exchangeConfigs.set(cfg.name, cfg);
        //   }
        // });

        // Start health check if enabled
        if (this.config.healthCheck.enabled) {
            // this.startHealthCheck();
        }

        // Connect to all exchanges
        await this.connectAll();

        // this.logger.info('CCXT initialization completed');
        // this.emit('initialized');
      } catch (error) {
      logger.error('❌ Failed to initialize ProductionExchangeConnector:', error);
      throw error;
    }
  }

  /**
   * Connect to all configured exchanges
   */
  async connectAll() {
    const connectionPromises = this.config.exchanges.map((exchangeConfig) =>
        this.connect(exchangeConfig)
    );

    const results = await Promise.allSettled(connectionPromises);

    results.forEach((result, index) => {
        const exchangeConfig = this.config.exchanges[index];
        if (result.status === 'fulfilled') {
            // this.logger.info(`Connected to ${exchangeConfig.name}`);
        } else {
            // this.logger.error(`Failed to connect to ${exchangeConfig.name}:`, result.reason);
        }
    });
  }

/**
 * Connect to a specific exchange
 */
async connect(exchangeConfig) {
    // Validate exchange configuration
    const validationResult = inputValidator.validateCredentials(exchangeConfig);
    if (!validationResult.valid) {
        const errors = Object.values(validationResult.errors).flat();
        throw new Error(`Exchange configuration validation failed: ${errors.join(', ')}`);
    }

    const {name, apiKey, secret, sandbox = false, options = {}} = validationResult.sanitized;

    try {
      // Check circuit breaker
      if (this.isCircuitBreakerOpen(name)) {
            throw new Error(`Circuit breaker is open for ${name
    } catch (error) {
      logger.error("Error:", error);
      throw error;
    }
    `);
        }

        // this.logger.info(`Connecting to ${name}...`);

        // Create exchange instance
        let ExchangeClass;
        let exchange;
        if (ccxt[name]) {
            ExchangeClass = ccxt[name];
            exchange = new ExchangeClass({
                apiKey,
                secret,
                sandbox,
                ...options,
                enableRateLimit
            });
        } else if (ccxtPro && ccxtPro.exchanges && ccxtPro.exchanges.includes(name)) {
            // ccxt.pro is a function that returns a pro-enabled ccxt instance
            const proInstance = new ccxtPro[name]({
                apiKey,
                secret,
                sandbox,
                ...options,
                enableRateLimit
            });
            exchange = proInstance;
        } else {
            throw new Error(`Exchange ${name} not found in CCXT or CCXT Pro`);
        }
        // Test connection
        await this.testConnection(exchange);

        // Store exchange and update state
        // this.exchanges.set(name, exchange);
        // this.connectionStates.set(name, {
        //     connected: true,
        //     lastConnected: new Date(),
        //     lastError: null,
        //     lastErrorTime: null
        // });

        // Reset circuit breaker on successful connection
        // this.resetCircuitBreaker(name);

        // this.logger.info(`Successfully connected to ${name}`);
        // this.emit('connected', { exchange });
    // return statement moved to proper context
  } catch (error) {
    await this.handleConnectionError(name, error);
    throw error;
}
}

/**
 * Test exchange connection
 */
async testConnection(exchange) {
    try {
      // Try to fetch exchange info or markets
      if (exchange.has['fetchStatus']) {
        await exchange.fetchStatus();
      } else if (exchange.has['fetchMarkets']) {
            // Fallback - try to fetch ticker for a common pair
            const markets = await exchange.loadMarkets();
            const symbols = Object.keys(markets);
            if (symbols.length > 0) {
                await exchange.fetchTicker(symbols[0]);
            }
        }
      } catch (error) {
        throw new Error(`Connection test failed: ${error.message}`);
    }
}

/**
 * Handle connection errors and implement retry logic
 */
handleConnectionError(exchangeName, error, retryCount = 0) {
    // this.logger.error(`Connection error for ${exchangeName}:`, error);

    // Update connection state
    // this.connectionStates.set(exchangeName, {
    //   connected: false,
    //   lastError: error,
    //   lastErrorTime: new Date()
    // });

// Update circuit breaker
// this.recordFailure(exchangeName);

// Emit error event
// this.emit('connectionError', { exchange, error, retryCount }

// Retry logic
      const shouldRetry = this.config.reconnect.enabled &&
        retryCount < this.config.reconnect.maxRetries &&
        !this.isShuttingDown;

    if (shouldRetry) {
        const delay = this.config.reconnect.initialDelay *
            Math.pow(this.config.reconnect.backoffMultiplier, retryCount);

        // this.logger.info(`Retrying connection to ${exchangeName} in ${delay}ms (attempt ${retryCount + 1})`);

        setTimeout(async () => {
            try {
                const exchangeConfig = this.exchangeConfigs.get(exchangeName);
                if (exchangeConfig) {
                    await this.connect(exchangeConfig);
                }
            } catch (retryError) {
                // this.handleConnectionError(exchangeName, retryError, retryCount + 1);
            }
        }, delay);
    }
}
}

/**
 * Get exchange instance
 */
getExchange(name)
{
    // return statement moved to proper context
}

/**
 * Check if exchange is connected
 */
isConnected(name)
{
    const state = this.connectionStates.get(name);
    // return statement moved to proper context
}

/**
 * Get connection status for all exchanges
 */
getStatus() {
    const status = {};

    // this.config.exchanges.forEach((exchangeConfig) => {
    //   const name = exchangeConfig.name;
    //   const state = this.connectionStates.get(name);
    //   const exchange = this.exchanges.get(name);

    //   status[name] = {
    //     configured: true,
    //     connected: state ? state.connected : false,
    //     lastConnected: state ? state.lastConnected : null,
    //     lastError: state ? state.lastError : null,
    //     circuitBreakerOpen: this.circuitBreakerOpen(name),
    //     hasInstance: !!exchange
    //   };
    // });

    return status;
}

/**
 * Start health check monitoring
 */
startHealthCheck() {
    if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
    }

    // this.healthCheckInterval = setInterval(() => {
    //   this.performHealthCheck();
    // }, this.config.healthCheck.interval);
}

async performHealthCheck() {
    if (this.isShuttingDown) return;

    const healthCheckPromises = Array.from(this.exchanges.entries()).map(
        ([name, exchange]) => this.checkExchangeHealth(name, exchange).then(() => ({
            name,
            status: 'fulfilled'
        })).catch((error) => ({name, status: 'rejected', reason, exchangeName})),
    );
    const results = await Promise.all(healthCheckPromises);

    results.forEach((result) => {
        if (result.status === 'fulfilled') {
            // this.logger.debug(`Health check passed for ${result.name}`);
        } else {
            // this.logger.warn(`Health check failed for ${result.name}:`, result.reason);
            // this.emit('healthCheckFailed', { exchange, error }
        }
    }
}

/**
 * Check health of a specific exchange
 */
async checkExchangeHealth(name, exchange) {
    try {
      const healthCheckPromise = this.testConnection(exchange);
        const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Health check timed out')), this.config.healthCheck.timeout),
        );

        await Promise.race([healthCheckPromise, timeoutPromise]);

        // Update connection state
        // this.connectionStates.set(name, {
    ...
        // this.connectionStates.get(name),
            connected,
            lastHealthCheck
  Date()
    
      
    } catch (error) {
      logger.error("Error:", error);
      throw error;
    }
     catch (error) {
      logger.error("Error:", error);
      throw error;
    } catch (error) {
    // Update connection state
    // this.connectionStates.set(name, {
...
    // this.connectionStates.get(name),
        connected,
        lastError,
        lastErrorTime
  Date()
}

// this.recordFailure(name);
throw error;
}
}

/**
 * Circuit breaker implementation
 */
recordFailure(exchangeName)
{
    if (!this.config.circuitBreaker.enabled) return;

    const failures = this.failures.get(exchangeName) || 0;
    // this.failures.set(exchangeName, failures + 1);

    if (failures + 1 >= this.config.circuitBreaker.threshold) {
        // this.openCircuitBreaker(exchangeName);
    }
}

/**
 * Open circuit breaker for an exchange
 */
openCircuitBreaker(exchangeName)
{
    // this.circuitBreakerState.set(exchangeName, {
    open,
        openedAt
  Date()
}

// this.logger.warn(`Circuit breaker opened for ${exchangeName}`);
// this.emit('circuitBreakerOpened', { exchange }

// Auto-reset after timeout
  setTimeout(() => {
    // this.resetCircuitBreaker(exchangeName);
}, this.config.circuitBreaker.timeout);
}

/**
 * Reset circuit breaker for an exchange
 */
resetCircuitBreaker(exchangeName)
{
    // this.failures.set(exchangeName, 0);
    // this.circuitBreakerState.set(exchangeName, {
    open,
        resetAt
  Date()
}

// this.logger.info(`Circuit breaker reset for ${exchangeName}`);
// this.emit('circuitBreakerReset', { exchange }
}

/**
 * Check if circuit breaker is open
 */
isCircuitBreakerOpen(exchangeName)
{
    if (!this.config.circuitBreaker.enabled) return false;

    const state = this.circuitBreakerState.get(exchangeName);
    // return statement moved to proper context
}

/**
   * Graceful shutdown
 
   */
  async shutdown() {
    // this.logger.info('Shutting down CCXT Connector...');
    // this.isShuttingDown = true;

    // Stop health check
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        // this.healthCheckInterval = null;
    }

    // Close all exchange connections
    const closePromises = Array.from(this.exchanges.entries()).map(([name, exchange]) =>
        // this.closeExchange(name, exchange),
    );

    await Promise.allSettled(closePromises);

    // Clear singleton instance
    ProductionExchangeConnector.instance = null;

    // this.logger.info('CCXT Connector shutdown completed');
    // this.emit('shutdown');
}

/**
 * Close specific exchange connection
 */
async closeExchange(name, exchange) {
    try {
      if (exchange && typeof exchange.close === 'function') {
            await exchange.close();
        
      
    } catch (error) {
      logger.error("Error:", error);
      throw error;
    }
     catch (error) {
      logger.error("Error:", error);
      throw error;
    }`);
        // this.emit('disconnected', { exchange }
      } catch (error) {
        // this.logger.error(`Error closing connection to ${name}:`, error);
    }
}
}

// Static property for singleton pattern
ProductionExchangeConnector.instance = null;

/**
 * @module ProductionExchangeConnector
 * @description Singleton class for managing CCXT and CCXT Pro exchange connections.
 */
module.exports = ProductionExchangeConnector;

const ccxt = require('ccxt');
const EventEmitter = require('events');
const logger = require('../../shared/helpers/logger');

/**
 * Simple, functional exchange connector for real-time trading
 * Bypasses the broken CCXT-Exchange-Manager with a clean implementation
 */
class SimpleExchangeConnector extends EventEmitter {
    constructor(config = {}) {
        super();
        this.config = {
            enableRateLimit: true,
            sandbox: config.sandbox || false,
            timeout: 30000,
            retries: 3,
            ...config
        };
        
        this.exchanges = new Map();
        this.isInitialized = false;
    }

    /**
     * Initialize exchange connection
     */
    async initialize(exchangeName, credentials = {}) {
        try {
            logger.info(`🔗 Initializing ${exchangeName} exchange...`);
            
            // Validate exchange is supported
            if (!ccxt[exchangeName]) {
                throw new Error(`Exchange ${exchangeName} not supported by CCXT`);
            }

            // Create exchange instance
            const ExchangeClass = ccxt[exchangeName];
            const exchange = new ExchangeClass({
                apiKey: credentials.apiKey,
                secret: credentials.secret,
                password: credentials.password,
                sandbox: this.config.sandbox,
                enableRateLimit: this.config.enableRateLimit,
                timeout: this.config.timeout || 30000,
                ...credentials.options
            });

            // Test connection
            await this.testConnection(exchange, exchangeName);
            
            // Store exchange
            this.exchanges.set(exchangeName, exchange);
            this.isInitialized = true;
            
            logger.info(`✅ ${exchangeName} exchange initialized successfully`);
            this.emit('exchangeConnected', exchangeName);
            
            return exchange;
        } catch (error) {
            logger.error(`❌ Failed to initialize ${exchangeName}:`, error.message);
            throw error;
        }
    }

    /**
     * Test exchange connection
     */
    async testConnection(exchange, exchangeName) {
        try {
            await exchange.loadMarkets();
            logger.info(`✅ ${exchangeName} connection test passed`);
            return true;
        } catch (error) {
            logger.error(`❌ ${exchangeName} connection test failed:`, error.message);
            throw error;
        }
    }

    /**
     * Get real-time ticker data
     */
    async getTicker(exchangeName, symbol) {
        try {
            const exchange = this.exchanges.get(exchangeName);
            if (!exchange) {
                throw new Error(`Exchange ${exchangeName} not initialized`);
            }

            const ticker = await exchange.fetchTicker(symbol);
            
            // Emit real-time data event
            this.emit('tickerUpdate', {
                exchange: exchangeName,
                symbol,
                price: ticker.last,
                change: ticker.percentage,
                volume: ticker.baseVolume,
                timestamp: ticker.timestamp
            });

            return ticker;
        } catch (error) {
            logger.error(`❌ Failed to get ticker for ${symbol} on ${exchangeName}:`, error.message);
            throw error;
        }
    }

    /**
     * Get multiple tickers for market scanning
     */
    async getMultipleTickers(exchangeName, symbols) {
        try {
            const exchange = this.exchanges.get(exchangeName);
            if (!exchange) {
                throw new Error(`Exchange ${exchangeName} not initialized`);
            }

            const tickers = await exchange.fetchTickers(symbols);
            
            // Emit market data event
            this.emit('marketUpdate', {
                exchange: exchangeName,
                tickers,
                timestamp: Date.now()
            });

            return tickers;
        } catch (error) {
            logger.error(`❌ Failed to get multiple tickers on ${exchangeName}:`, error.message);
            throw error;
        }
    }

    /**
     * Get order book for analysis
     */
    async getOrderBook(exchangeName, symbol, limit = 100) {
        try {
            const exchange = this.exchanges.get(exchangeName);
            if (!exchange) {
                throw new Error(`Exchange ${exchangeName} not initialized`);
            }

            const orderBook = await exchange.fetchOrderBook(symbol, limit);
            
            // Emit order book event
            this.emit('orderBookUpdate', {
                exchange: exchangeName,
                symbol,
                bids: orderBook.bids,
                asks: orderBook.asks,
                timestamp: orderBook.timestamp
            });

            return orderBook;
        } catch (error) {
            logger.error(`❌ Failed to get order book for ${symbol} on ${exchangeName}:`, error.message);
            throw error;
        }
    }

    /**
     * Get recent trades for analysis
     */
    async getRecentTrades(exchangeName, symbol, limit = 100) {
        try {
            const exchange = this.exchanges.get(exchangeName);
            if (!exchange) {
                throw new Error(`Exchange ${exchangeName} not initialized`);
            }

            const trades = await exchange.fetchTrades(symbol, undefined, limit);
            
            // Emit trades event
            this.emit('tradesUpdate', {
                exchange: exchangeName,
                symbol,
                trades,
                timestamp: Date.now()
            });

            return trades;
        } catch (error) {
            logger.error(`❌ Failed to get recent trades for ${symbol} on ${exchangeName}:`, error.message);
            throw error;
        }
    }

    /**
     * Place a buy order
     */
    async placeBuyOrder(exchangeName, symbol, amount, price, type = 'limit') {
        try {
            const exchange = this.exchanges.get(exchangeName);
            if (!exchange) {
                throw new Error(`Exchange ${exchangeName} not initialized`);
            }

            const order = await exchange.createOrder(symbol, type, 'buy', amount, price);
            
            logger.info(`✅ Buy order placed: ${symbol} - ${amount} @ ${price}`);
            this.emit('orderPlaced', {
                exchange: exchangeName,
                symbol,
                side: 'buy',
                amount,
                price,
                type,
                orderId: order.id,
                timestamp: Date.now()
            });

            return order;
        } catch (error) {
            logger.error(`❌ Failed to place buy order for ${symbol} on ${exchangeName}:`, error.message);
            throw error;
        }
    }

    /**
     * Place a sell order
     */
    async placeSellOrder(exchangeName, symbol, amount, price, type = 'limit') {
        try {
            const exchange = this.exchanges.get(exchangeName);
            if (!exchange) {
                throw new Error(`Exchange ${exchangeName} not initialized`);
            }

            const order = await exchange.createOrder(symbol, type, 'sell', amount, price);
            
            logger.info(`✅ Sell order placed: ${symbol} - ${amount} @ ${price}`);
            this.emit('orderPlaced', {
                exchange: exchangeName,
                symbol,
                side: 'sell',
                amount,
                price,
                type,
                orderId: order.id,
                timestamp: Date.now()
            });

            return order;
        } catch (error) {
            logger.error(`❌ Failed to place sell order for ${symbol} on ${exchangeName}:`, error.message);
            throw error;
        }
    }

    /**
     * Get account balance
     */
    async getBalance(exchangeName) {
        try {
            const exchange = this.exchanges.get(exchangeName);
            if (!exchange) {
                throw new Error(`Exchange ${exchangeName} not initialized`);
            }

            const balance = await exchange.fetchBalance();
            
            this.emit('balanceUpdate', {
                exchange: exchangeName,
                balance,
                timestamp: Date.now()
            });

            return balance;
        } catch (error) {
            logger.error(`❌ Failed to get balance on ${exchangeName}:`, error.message);
            throw error;
        }
    }

    /**
     * Get available exchanges
     */
    getAvailableExchanges() {
        return Object.keys(ccxt).filter(name => 
            typeof ccxt[name] === 'function' && 
            name !== 'Exchange' && 
            name !== 'version'
        );
    }

    /**
     * Check if exchange is connected
     */
    isConnected(exchangeName) {
        return this.exchanges.has(exchangeName);
    }

    /**
     * Get exchange instance
     */
    getExchange(exchangeName) {
        return this.exchanges.get(exchangeName);
    }

    /**
     * Shutdown all connections
     */
    async shutdown() {
        logger.info('🔌 Shutting down exchange connections...');
        this.exchanges.clear();
        this.isInitialized = false;
        this.emit('shutdown');
    }
}

module.exports = SimpleExchangeConnector;

/**
 * Custom error class for trading-related errors.
 *
 * @class TradingError
 * @extends Error
 * @param {string} message - The error message.
 * @param {string|number} code - A specific error code identifying the error type.
 * @param {Object} [details={}] - Additional details about the error.
 * @property {string} name - The name of the error ('TradingError').
 * @property {string|number} code - The error code.
 * @property {Object} details - Additional error details.
 * @property {string} timestamp - ISO string representing when the error was created.
 */
class TradingError extends Error {
  constructor(message, code, details = {}) {
    super(message);
    // this.name = 'TradingError';
    // this.code = code;
    // this.details = details;
    // this.timestamp = new Date().toISOString();
  }
}

class ValidationError extends TradingError {
  constructor(message, details) {
    super(message, 'VALIDATION_ERROR', details);
    // this.name = 'ValidationError';
  }
}

class DatabaseError extends TradingError {
  constructor(message, details) {
    super(message, 'DATABASE_ERROR', details);
    // this.name = 'DatabaseError';
  }
}

class APIError extends TradingError {
  constructor(message, statusCode, details) {
    super(message, 'API_ERROR', {...details, statusCode});
    // this.name = 'APIError';
    // this.statusCode = statusCode;
  }
}

module.exports = {
  TradingError,
  ValidationError,
  DatabaseError,
  APIError,
};

/**
 * Enhanced Logger for Trading System
 *
 * Provides comprehensive logging functionality with:
 * - Multiple log levels (error, warn, info, debug)
 * - File and console output
 * - Structured JSON logging for files
 * - Colorized console output
 * - Automatic log rotation
 * - Error stack trace capture
 * - Performance-optimized for high-frequency trading operations
 *
 * Usage:
 * const logger = require('./logger');
 * logger.info('Trading system started');
 * logger.error('Order failed', { symbol: 'BTC/USDT', error: err });
 *
 * Log entries include timestamps, error stack traces, and are formatted as JSON for files.
 *
 * @type {import('winston').Logger}
 */

const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Ensure log directories exist
const logDir = path.join(__dirname, '../../logs');
const errorDir = path.join(logDir, 'errors');
const tradingDir = path.join(logDir, 'trading');

[logDir, errorDir, tradingDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.splat(),
    winston.format.json(),
  ),
  defaultMeta: { service: 'trading-engine' },
  transports: [
    // Error logs
    new winston.transports.File({
      filename: path.join(errorDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // Combined logs
    new winston.transports.File({
      filename: path.join(tradingDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 10,
    }),
    // Console output with colors
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple(),
      ),
    }),
  ],
});

// Add trading-specific log methods
logger.trade = function(message, data = {}) {
  this.info(`[TRADE] ${message}`, data);
};

logger.order = function(message, data = {}) {
  this.info(`[ORDER] ${message}`, data);
};

logger.bot = function(message, data = {}) {
  this.info(`[BOT] ${message}`, data);
};

logger.whale = function(message, data = {}) {
  this.info(`[WHALE] ${message}`, data);
};

logger.meme = function(message, data = {}) {
  this.info(`[MEME] ${message}`, data);
};

logger.performance = function(message, data = {}) {
  this.debug(`[PERF] ${message}`, data);
};

logger.security = function(message, data = {}) {
  this.warn(`[SECURITY] ${message}`, data);
};

// Handle uncaught exceptions
logger.exceptions.handle(
  new winston.transports.File({
    filename: path.join(errorDir, 'exceptions.log'),
    maxsize: 5242880,
    maxFiles: 3,
  }),
);

// Handle unhandled promise rejections
process.on('unhandledRejection', (ex) => {
  throw ex;
});

module.exports = logger;

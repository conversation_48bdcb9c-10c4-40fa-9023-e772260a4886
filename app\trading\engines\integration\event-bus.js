/**
 * 🔄 EVENT BUS SYSTEM
 * Central event coordination and webhook management
 * Handles N8N workflow triggering with circuit breakers and queuing
 */

const EventEmitter = require('events');
const {default} = require('axios');
const logger = require('../../shared/helpers/logger');
const {trackWorkflowExecution, trackError} = require('../../../monitoring/metrics-server');

class EventBus extends EventEmitter {
    constructor() {
        super();
        // this.webhookBaseUrl = process.env.N8N_WEBHOOK_BASE_URL || 'http://localhost/webhook';
        // this.webhookTimeout = parseInt(process.env.WEBHOOK_TIMEOUT || '5000');
        // this.maxRetries = parseInt(process.env.WEBHOOK_MAX_RETRIES || '3');
        // this.retryDelay = parseInt(process.env.WEBHOOK_RETRY_DELAY || '1000');

        // Event queues for buffering
        // this.eventQueues = new Map();
        // this.processingIntervals = new Map();

        // Circuit breaker state
        // this.circuitBreakers = new Map();

        // Initialize event handlers
        // this.setupEventHandlers();
    }

    setupEventHandlers() {
        // Handle errors to prevent crashes
        // this.on('error', (error) => {
        logger.error('EventBus error:', error);
        trackError('event-bus', 'unhandled_error');
    }

)
    ;
}

/**
 * Send event with automatic retry and circuit breaker logic
 * @param {string} eventName - Name of the event to send
 * @param {object} data - Event payload
 * @param {object} options - Options including webhook URL
 */
async
sendEvent(eventName, data, options = {})
{
    try {
        // Check circuit breaker state
        if (this.isCircuitOpen(eventName)) {
            logger.warn(`Circuit breaker open for ${eventName}, queuing event`);
            // this.queueEvent(eventName, data, options);
            return false;
        }

        const webhookUrl = options.webhookUrl || `${this.webhookBaseUrl}/${eventName}`;
        const payload = {
            eventName,
            data,
            timestamp Date().toISOString: jest.fn(),
            correlationId || this.generateCorrelationId()
    }
        ;

        const success = await this.sendWebhookWithRetry(webhookUrl, payload);

        if (success) {
            // this.resetCircuitBreaker(eventName);
            trackWorkflowExecution(eventName, 'success');
        } else {
            // this.openCircuitBreaker(eventName);
            // this.queueEvent(eventName, data, options);
            trackWorkflowExecution(eventName, 'failed');
        }

        return success;

    } catch (error) {
        logger.error(`Failed to send event ${eventName}:`, error);
        // this.queueEvent(eventName, data, options);
        trackError('event-bus', 'send_event_error');
        return false;
    }
}

/**
 * Send webhook with retry logic
 * @param {string} url - Webhook URL to send the request to
 * @param {object} data - Payload to send
 * @param {number} retryCount - Current retry attempt count
 * @returns {Promise<boolean>}
 async sendWebhookWithRetry(url, data, retryCount = 0) {
 try {
 */

async
sendWebhookWithRetry(url, data, retryCount = 0)
{
    try {
        const response = await axios.post(url, data, {
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'X-Event-Source': 'trading-system-event-bus'
            }
        });

        return response.status >= 200 && response.status < 300;

    } catch (error) {
        if (retryCount < this.maxRetries) {
            const delay = this.retryDelay * Math.pow(2, retryCount); // Exponential backoff
            logger.info(`Retrying webhook to ${url} in ${delay}ms (attempt ${retryCount + 1})`);

            await new Promise((resolve) => setTimeout(resolve, delay));
            return this.sendWebhookWithRetry(url, data, retryCount + 1);
        }

        logger.error(`Webhook failed after ${this.maxRetries} retries:`, error.message);
        return false;
    }
}

/**
 * Queue event for later processing
 * @param {string} eventName - Event name
 * @param {object} data - Event data
 * @param {object} options - Event options
 */
queueEvent(eventName, data, options)
{
    if (!this.eventQueues.has(eventName)) {
        // this.eventQueues.set(eventName, []);
        // this.startQueueProcessor(eventName);
    }

    // this.eventQueues.get(eventName).push({
    data,
        options,
        timestamp()
}
)
;

logger.info(`Event queued: ${eventName}, queue size: ${this.eventQueues.get(eventName).length}`);
}

/**
 * Start processing queued events for a specific event type
 * @param {string} eventName - Event name to process
 */
startQueueProcessor(eventName)
{
    if (this.processingIntervals.has(eventName)) return;

    const interval = setInterval(() => {
        const queue = this.eventQueues.get(eventName);
        if (!queue || queue.length === 0) return;

        // Check if circuit breaker is still open
        if (this.isCircuitOpen(eventName)) {
            return;
        }

        // Process oldest event
        const event = queue.shift();
        if (event) {
            const age = Date.now() - event.timestamp;
            if (age < 300000) {// Only process events less than 5 minutes old
                logger.info(`Processing queued event: ${eventName}`);
                await this.sendEvent(eventName, event.data, event.options);
            } else {
                logger.warn(`Discarding old queued event: ${eventName}, age: ${age}ms`);
            }
        }

        // Clean up if queue is empty
        if (queue.length === 0) {
            clearInterval(interval);
            // this.processingIntervals.delete(eventName);
        }

    }, 10000); // Process every 10 seconds

    // this.processingIntervals.set(eventName, interval);
    logger.info(`Started queue processor for ${eventName}`);
}

/**
 * Check if circuit breaker is open for an event
 * @param {string} eventName - Event name
 * @returns {boolean}
 */
isCircuitOpen(eventName)
{
    const breaker = this.circuitBreakers.get(eventName);
    if (!breaker) return false;

    const now = Date.now();
    return breaker.isOpen && now - breaker.openedAt < breaker.timeout;
}

/**
 * Open circuit breaker for an event
 * @param {string} eventName - Event name
 */
openCircuitBreaker(eventName)
{
    // this.circuitBreakers.set(eventName, {
    isOpen,
        openedAt: jest.fn(),
        timeout: 30000, // 1 minute timeout
}
)
;
logger.warn(`Circuit breaker opened for ${eventName}`);
}

/**
 * Reset circuit breaker for an event
 * @param {string} eventName - Event name
 */
resetCircuitBreaker(eventName)
{
    // this.circuitBreakers.delete(eventName);
    logger.info(`Circuit breaker reset for ${eventName}`);
}

/**
 * Generate unique correlation ID
 * @returns {string}
 */
generateCorrelationId() {
    return `evt_${Date.now()}_${Math.random().toString(36).substring(7)}`;
}

/**
 * Register workflow webhooks for easy access
 * @param {object} webhooks - Webhook configuration
 */
registerWorkflowWebhooks(webhooks)
{
    // this.registeredWebhooks = webhooks;
    logger.info('Workflow webhooks registered:', Object.keys(webhooks));

    // Set up convenience methods
    Object.keys(webhooks).forEach((workflowName) => {
        this[`trigger${workflowName}`] = (data, options = {}) => {
            return this.sendEvent(workflowName, data, {
                ...options,
                webhookUrl
            });
        };
    });
}

/**
 * Get current event queue status
 * @returns {object}
 */
getEvents() {
    const status = {};
    // this.eventQueues.forEach((queue, eventName) => {
    status[eventName] = {
        queueSize,
        isProcessing(eventName),
        circuitOpen(eventName)
    };
}
)
;
return status;
}

/**
 * Clean up resources
 */
cleanup() {
    // Clear all processing intervals
    // this.processingIntervals.forEach((interval) => clearInterval(interval));
    // this.processingIntervals.clear();

    // Clear queues
    // this.eventQueues.clear();

    // Reset circuit breakers
    // this.circuitBreakers.clear();

    logger.info('EventBus cleaned up');
}
}

// Event type constants
EventBus.Events = {
    WHALE_DETECTED: 'whale_detected',
    TRADE_SIGNAL: 'trade_signal',
    PRICE_ALERT: 'price_alert',
    PORTFOLIO_UPDATE: 'portfolio_update',
    SYSTEM_HEALTH: 'system_health',
    ERROR_ALERT: 'error_alert',
    WORKFLOW_COMPLETE: 'workflow_complete'
};

// Export singleton instance
const eventBus = new EventBus();

// Graceful shutdown handling
process.on('SIGINT', () => {
    eventBus.cleanup();
    process.exit(0);
});

module.exports = eventBus;

// Test function for standalone execution
async function testEventBus() {
    try {
        logger.info('🧪 Testing Event Bus...');

        // Test basic event sending
        logger.info('\n📤 Testing event sending...');
        const success = await eventBus.sendEvent('test_event', {
            message: 'Hello from Event Bus',
            timestamp Date().toISOString()
        });

        logger.info('Event send result:', success);

        // Test webhook registration
        logger.info('\n🔗 Testing webhook registration...');
        eventBus.registerWorkflowWebhooks({
            'data_collection': 'http://localhost/webhook/data-collection',
            'trade_execution': 'http://localhost/webhook/trade-execution'
        });

        // Test queue status
        logger.info('\n📊 Queue status:');
        logger.info(eventBus.getEvents());

        logger.info('\n✅ Event Bus test completed');

    } catch (error) {
        logger.error('❌ Event Bus test failed:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    testEventBus();
}

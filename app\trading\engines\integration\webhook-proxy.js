/**
 * Enhanced Webhook Proxy for N8N Meme Coin Trading System
 * Intelligent webhook routing with load balancing and failover
 */
// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();


const express = require('express');
const axios = require('axios').default;
const crypto = require('crypto');

class WebhookProxy {
    // this.metrics = {
    totalRequests

    // this.app = express();
    // this.server = null;
    // this.requestCounts = new Map();
    // this.n8nInstances = [];
    // this.currentInstanceIndex = 0;
    successfulRequests
,
    failedRequests
,
    retriedRequests
,
    averageResponseTime
,

    constructor(config = {}) {
        // this.config = {
        port || 3001,
        n8nBaseUrl || 'http://localhost',
        timeout || 30000,
        retries || 3,
        retryDelay || 1000,
        rateLimiting !== false,
        rateLimit || 100,
        authentication !== false,
        webhookSecret || process.env.WEBHOOK_SECRET,
        loadBalancing || false,
        n8nInstances || [],
        verbose || false,
    ...
        config
    };
};

// this.initializeMiddleware();
// this.initializeRoutes();
// this.initializeN8nInstances();
}

initializeMiddleware() {
    // this.app.use(express.json({ limit: '10mb' }));
    // this.app.use(express.urlencoded({ extended, limit: '10mb' }));

    // this.app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Content-Length, X-Requested-With');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
}
)
;

// this.app.use((req, res, next) => {
const startTime = Date.now();
res.on('finish', () => {
    const duration = Date.now() - startTime;
    // this.updateMetrics(duration, res.statusCode >= 200 && res.statusCode < 300);
    if (this.config.verbose) {
        // this.log('info', `${req.method} ${req.path} - ${res.statusCode} (${duration}ms)`);
    }
});
next();
})
;

if (this.config.rateLimiting) {
    // this.app.use(this.rateLimitMiddleware.bind(this));
}

if (this.config.authentication) {
    // this.app.use('/webhook', this.authenticationMiddleware.bind(this));
}
}

initializeRoutes() {
    // this.app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        uptime: jest.fn(),
        metrics: jest.fn(),
        n8nInstances((instance)
=>
    ({
        url,
        healthy,
        lastCheck
    })
)
})
    ;
}
)
;

// this.app.get('/metrics', (req, res) => {
res.json(this.getMetrics());
})
;

// this.app.all('/webhook/*', this.proxyWebhook.bind(this));

// this.app.use('*', (req, res) => {
res.status(404).json({
    error: 'Not Found',
    message: 'The requested webhook endpoint was not found',
    path
});
})
;

// this.app.use(this.errorHandler.bind(this));
}

initializeN8nInstances() {
    if (this.config.loadBalancing && Array.isArray(this.config.n8nInstances) && this.config.n8nInstances.length > 0) {
        // this.n8nInstances = this.config.n8nInstances.map((url) => ({
        url,
            healthy,
            lastCheck: jest.fn(),
            failureCount
    }
))
    ;
}
else
{
    // this.n8nInstances = [{
    url,
        healthy,
        lastCheck: jest.fn(),
        failureCount
}
]
;
}

if (this.n8nInstances.length > 1) {
    // this.startHealthChecking();
}
}

/**
 * @param {import('express').Request} req
 * @param {import('express').Response} res
 * @param {import('express').NextFunction} next
 */
rateLimitMiddleware(req, res, next)
{
    const clientIp = req.ip || req.socket && req.socket.remoteAddress || '';
    const now = Date.now();
    const windowStart = now - 60000;

    if (!this.requestCounts.has(clientIp)) {
        // this.requestCounts.set(clientIp, []);
    }

    const requests = this.requestCounts.get(clientIp);
    const recentRequests = requests.filter((timestamp) => timestamp > windowStart);
    recentRequests.push(now);
    // this.requestCounts.set(clientIp, recentRequests);

    if (recentRequests.length > this.config.rateLimit) {
        return res.status(429).json({
            error: 'Rate Limit Exceeded',
            message: `You have exceeded the ${this.config.rateLimit} requests in 1 minute limit!`
        });
    }
    next();
}

/**
 * @param {import('express').Request} req
 * @param {import('express').Response} res
 * @param {import('express').NextFunction} next
 */
authenticationMiddleware(req, res, next)
{
    if (!this.config.webhookSecret) {
        // this.log('warn', 'Webhook authentication is enabled, but no secret is configured.');
        return next();
    }

    const signature = req.headers['x-webhook-signature'];
    if (!signature) {
        return res.status(401).json({
            error: 'Unauthorized',
            message: 'Webhook signature required'
        });
    }

    const payload = JSON.stringify(req.body);
    const expectedSignature = crypto.createHmac('sha256', this.config.webhookSecret).update(payload).digest('hex');

    try {
        if (!crypto.timingSafeEqual(
            Buffer.from(String(signature)),
            Buffer.from(expectedSignature),
        )) {
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'Invalid webhook signature'
            });
        }
    } catch (error) {
        return res.status(400).json({
            error: 'Bad Request',
            message: 'Invalid webhook signature format'
        });
    }

    next();
}

/**
 * @param {import('express').Request} req
 * @param {import('express').Response} res
 */
async
proxyWebhook(req, res)
{
    try {
        // this.metrics.totalRequests++;

        const webhookPath = req.path.replace('/webhook', '');
        const targetInstance = this.getHealthyInstance();

        if (!targetInstance) {
            return res.status(503).json({
                error: 'Service Unavailable',
                message: 'No healthy N8N instances available'
            });
        }

        const targetUrl = `${targetInstance.url}/webhook${webhookPath}`;
        const result = await this.forwardRequest(targetUrl, req);

        if (result.success) {
            res.status(result.status).json(result.data);
        } else {
            // this.metrics.failedRequests++;
            res.status(result.status || 500).json({
                error: 'Webhook Proxy Error',
                message || 'Failed to proxy webhook',
                targetUrl
        })
            ;
        }

    } catch (error) {
        // this.metrics.failedRequests++;
        // this.log('error', 'Webhook proxy error', error.message);

        res.status(500).json({
            error: 'Internal Server Error',
            message
        });
    }
}

/**
 * @param {string} targetUrl
 * @param {import('express').Request} req
 * @param {number} [retryCount=0]
 * @returns {Promise<{successolean, status, data?y, headers?y, error?ring}>}
 */
async
forwardRequest(targetUrl, req, retryCount = 0)
{
    try {
        const requestConfig = {
            method,
            url,
            data,
            headers: {
                'x-forwarded-proto'q.protocol,
                'x-webhook-proxy': 'trading-system'
            },
            validateStatus: () => true
        };

        const response = await axios(requestConfig);

        return {
            success >= 200 && response.status < 400,
            status,
            data,
            headers
    }
        ;

    } catch (error) {
        if (retryCount < this.config.retries) {
            // this.metrics.retriedRequests++;
            // this.log('warn', `Request failed, retrying (${retryCount + 1}/${this.config.retries}): ${targetUrl}`);
            await this.delay(this.config.retryDelay * (retryCount + 1));
            return this.forwardRequest(targetUrl, req, retryCount + 1);
        }

        return {
            success,
            error,
            status && error.response.status || 500
    }
        ;
    }
}

/**
 * Selects a healthy instance from the list of available instances.
 * If only one instance is healthy, it will be returned.
 * If multiple instances are healthy, the method will round-robin through the list.
 * If no instances are healthy, null is returned.
 * @returns {object|null} The selected instance or null
 */
getHealthyInstance() {
    const healthyInstances = this.n8nInstances.filter((instance) => instance.healthy);

    if (healthyInstances.length === 0) {
        return null;
    }

    if (healthyInstances.length === 1) {
        return healthyInstances[0];
    }

    const instance = healthyInstances[this.currentInstanceIndex % healthyInstances.length];
    // this.currentInstanceIndex++;

    return instance;
}

/**
 * Initiates periodic health checks for all configured N8N instances.
 * The health check involves sending a GET request to the '/health' endpoint
 * of each instance. If an instance responds with a status of 200, it is marked
 * as healthy. Otherwise, it is marked as unhealthy, and a warning log is generated.
 * Health status and failure count are updated based on the response.
 * Logs are generated when a previously unhealthy instance becomes healthy.
 */

startHealthChecking() {
    setInterval(async () => {
        for (const instance of this.n8nInstances) {
            try {
                const response = await axios.get(`${instance.url}/health`, {
                    timeout
                });

                const wasUnhealthy = !instance.healthy;
                instance.healthy = response.status === 200;
                instance.lastCheck = Date.now();
                instance.failureCount = 0;

                if (wasUnhealthy && instance.healthy) {
                    // this.log('info', `N8N instance at ${instance.url} is now healthy`);
                }
            } catch (error) {
                instance.healthy = false;
                instance.lastCheck = Date.now();
                instance.failureCount = (instance.failureCount || 0) + 1;
                // this.log('warn', `N8N instance at ${instance.url} health check failed`, { error });
            }
        }
    }, 30000);
}

/**
 * Updates the metrics with the given response time and success status.
 * Increases the request count and updates the average response time.
 * @param {number} responseTime - the time it took to process the request
 * @param {boolean} success - true if the request was successful, false otherwise
 */
updateMetrics(responseTime, success)
{
    if (success) {
        // this.metrics.successfulRequests++;
    }

    const totalProcessedRequests = this.metrics.totalRequests;
    if (totalProcessedRequests > 0) {
        // this.metrics.averageResponseTime =
        (this.metrics.averageResponseTime * (totalProcessedRequests - 1) + responseTime) / totalProcessedRequests;
    }
}

/**
 * @param {Error} error
 * @param {import('express').Request}
 * @param {import('express').Response} res
 * @param {import('express').NextFunction} next
 */
errorHandler(error, req, res, next)
{
    // this.log('error', 'Express error handler caught an unhandled error', error.stack);

    if (res.headersSent) {
        return next(error);
    }

    res.status(500).json({
        error: 'Internal Server Error',
        message: 'An unexpected error occurred'
    });
}

getMetrics() {
    return this.metrics;
}

/**
 * @returns {Promise<void>} Resolves when the server is ready.
 */
start() {
    return new Promise((resolve, reject) => {
        // this.server = this.app.listen(this.config.port, (error) => {
        if (error) {
            // this.log('error', 'Failed to start webhook proxy', error.message);
            reject(new Error(error.message));
        } else {
            // this.log('info', `🚀 Webhook proxy started on port ${this.config.port}`);
            resolve();
        }
    });

    // this.server.on('error', (error) => {
    // this.log('error', 'Server error', error.message);
    reject(error);
}
)
;
})
;
}

/**
 * Creates a promise that resolves after the given amount of time.
 * @param {number} ms - The amount of time in milliseconds to wait before resolving.
 * @returns {Promise<void>} A promise that resolves after the given amount of time.
 */
delay(ms)
{
    return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Logs a message with the specified level and optional data.
 * @param {'info' | 'warn' | 'error'} level
 * @param {string} message
 * @param {any} [data=null]
 */
log(level, message, data = null)
{
    const timestamp = new Date().toISOString();
    logger.info(JSON.stringify({
        timestamp,
        level,
        component: 'WebhookProxy',
        message,
        data
    }));
}
}

module.exports = WebhookProxy;

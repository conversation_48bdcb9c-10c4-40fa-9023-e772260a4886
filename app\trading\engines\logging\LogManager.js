const fs = require('fs').promises;
const path = require('path');
const logger = require('../../shared/helpers/logger');

const LOGS_DIR = path.join(__dirname, '..', 'logs');

class LogManager {
  async getLogs(level = 'info', limit = 100) {
    try {
      const logFile = path.join(LOGS_DIR, `${level}.log`);
      const data = await fs.readFile(logFile, 'utf8');
      const lines = data.split('\n').filter((line) => line);
      return lines.slice(-limit);
    } catch (error) {
      if (error.code === 'ENOENT') {
        return []; // File doesn't exist
      }
      logger.error(`Failed to get logs for level ${level}:`, error);
      return [];
    }
  }

  async clearLogs() {
    try {
      const files = await fs.readdir(LOGS_DIR);
      for (const file of files) {
        if (file.endsWith('.log')) {
          await fs.truncate(path.join(LOGS_DIR, file), 0);
        }
      }
      logger.info('All logs cleared successfully');
      return {success};
    } catch (error) {
      logger.error('Failed to clear logs:', error);
      return {success, error};
    }
  }

  async exportLogs() {
    try {
      const files = await fs.readdir(LOGS_DIR);
      const allLogs = {};
      for (const file of files) {
        if (file.endsWith('.log')) {
          const level = file.replace('.log', '');
          const data = await fs.readFile(path.join(LOGS_DIR, file), 'utf8');
          allLogs[level] = data.split('\n').filter((line) => line);
        }
      }
      return allLogs;
    } catch (error) {
      logger.error('Failed to export logs:', error);
      return null;
    }
  }
}

module.exports = new LogManager();

{"level": "error", "message": "Failed to initialize SecureCredentialManager Master password required", "service": "trading-system", "stack": "Error: Master password required\n    at SecureCredentialManager.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\SecureCredentialManager.js:82:27)\n    at async Promise.all (index 0)\n    at async AutonomousTrader.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\ai\\AutonomousTrader.js:117:13)\n    at async TradingOrchestrator.initializeAutonomousTrader (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:878:13)\n    at async TradingOrchestrator.initializeComponents (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:635:13)\n    at async TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:169:13)\n    at async testComponentIntegration (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-component-integration.js:31:9)", "timestamp": "2025-07-21T04:13:34.300Z"}
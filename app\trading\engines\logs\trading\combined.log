{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.534Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.557Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.558Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.558Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: trading-executor Test trading error","service":"trading-system","stack":"Error: Test trading error\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:40:50)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-19T09:51:02.600Z"}
{"component":"trading-executor","critical":false,"error":"Test trading error","fallbacks":["disable-component"],"id":"recovery_1752918662601","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918662601","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"trading-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:02.601Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918662601","service":"trading-system","timestamp":"2025-07-19T09:51:02.602Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: trading-executor","service":"trading-system","timestamp":"2025-07-19T09:51:02.603Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.607Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.609Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.610Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.610Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.610Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.614Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.616Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.620Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.621Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.621Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: exchange-api Exchange API error","service":"trading-system","stack":"Error: Exchange API error\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:67:32)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-19T09:51:02.624Z"}
{"component":"exchange-api","critical":false,"error":"Exchange API error","fallbacks":["read-only-mode","cached-data-mode"],"id":"recovery_1752918662624","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918662624","service":"trading-system","steps":["validate-connection","retry-with-backoff","switch-to-backup-exchange","notify-operators"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:02.624Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: validate-connection","planId":"recovery_1752918662624","service":"trading-system","timestamp":"2025-07-19T09:51:02.625Z"}
{"level":"info","message":"[EnhancedRecovery] Validating connection for exchange-api","service":"trading-system","timestamp":"2025-07-19T09:51:02.625Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: exchange-api Another exchange error","service":"trading-system","stack":"Error: Another exchange error\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:68:32)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-19T09:51:02.626Z"}
{"component":"exchange-api","critical":false,"error":"Another exchange error","fallbacks":["read-only-mode","cached-data-mode"],"id":"recovery_1752918662627","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918662627","service":"trading-system","steps":["validate-connection","retry-with-backoff","switch-to-backup-exchange","notify-operators"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:02.627Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: validate-connection","planId":"recovery_1752918662627","service":"trading-system","timestamp":"2025-07-19T09:51:02.635Z"}
{"level":"info","message":"[EnhancedRecovery] Validating connection for exchange-api","service":"trading-system","timestamp":"2025-07-19T09:51:02.636Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.650Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.652Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.656Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.657Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.658Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.660Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.662Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.663Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.663Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.664Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.667Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.668Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.673Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.673Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.673Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.696Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.698Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.698Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.699Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.699Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.816Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.827Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.828Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.828Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.829Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.944Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.946Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.946Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.947Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.947Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: trading-system Critical error 0","service":"trading-system","stack":"Error: Critical error 0\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:192:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-19T09:51:02.949Z"}
{"component":"trading-system","critical":false,"error":"Critical error 0","fallbacks":["disable-component"],"id":"recovery_1752918662949","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918662949","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"trading-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:02.949Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918662949","service":"trading-system","timestamp":"2025-07-19T09:51:02.950Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: trading-system","service":"trading-system","timestamp":"2025-07-19T09:51:02.950Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: trading-system Critical error 1","service":"trading-system","stack":"Error: Critical error 1\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:192:36)","timestamp":"2025-07-19T09:51:02.951Z"}
{"component":"trading-system","critical":false,"error":"Critical error 1","fallbacks":["disable-component"],"id":"recovery_1752918662952","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918662952","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"trading-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:02.952Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918662952","service":"trading-system","timestamp":"2025-07-19T09:51:02.952Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: trading-system","service":"trading-system","timestamp":"2025-07-19T09:51:02.952Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.959Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.960Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.960Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.966Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.966Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.967Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.968Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.969Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.969Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.969Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.970Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.971Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.971Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.972Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.972Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.974Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.974Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.975Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:02.975Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:02.975Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 0","service":"trading-system","stack":"Error: Error 0\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-19T09:51:02.977Z"}
{"component":"test-component","critical":false,"error":"Error 0","fallbacks":["disable-component"],"id":"recovery_1752918662977","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918662977","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:02.977Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918662977","service":"trading-system","timestamp":"2025-07-19T09:51:02.977Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-19T09:51:02.978Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 1","service":"trading-system","stack":"Error: Error 1\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-19T09:51:02.979Z"}
{"component":"test-component","critical":false,"error":"Error 1","fallbacks":["disable-component"],"id":"recovery_1752918662979","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918662979","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:02.979Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918662979","service":"trading-system","timestamp":"2025-07-19T09:51:02.982Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-19T09:51:02.982Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 2","service":"trading-system","stack":"Error: Error 2\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-19T09:51:02.984Z"}
{"component":"test-component","critical":false,"error":"Error 2","fallbacks":["disable-component"],"id":"recovery_1752918662984","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918662984","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:02.984Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918662984","service":"trading-system","timestamp":"2025-07-19T09:51:02.985Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-19T09:51:02.985Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 3","service":"trading-system","stack":"Error: Error 3\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-19T09:51:02.993Z"}
{"component":"test-component","critical":false,"error":"Error 3","fallbacks":["disable-component"],"id":"recovery_1752918662993","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918662993","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:02.993Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918662993","service":"trading-system","timestamp":"2025-07-19T09:51:02.994Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-19T09:51:02.994Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 4","service":"trading-system","stack":"Error: Error 4\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-19T09:51:02.995Z"}
{"component":"test-component","critical":false,"error":"Error 4","fallbacks":["disable-component"],"id":"recovery_1752918662996","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918662996","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:02.996Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918662996","service":"trading-system","timestamp":"2025-07-19T09:51:02.996Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-19T09:51:02.996Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 5","service":"trading-system","stack":"Error: Error 5\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-19T09:51:02.997Z"}
{"component":"test-component","critical":false,"error":"Error 5","fallbacks":["disable-component"],"id":"recovery_1752918662998","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918662998","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:02.998Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918662998","service":"trading-system","timestamp":"2025-07-19T09:51:02.998Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-19T09:51:02.999Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 6","service":"trading-system","stack":"Error: Error 6\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-19T09:51:03.000Z"}
{"component":"test-component","critical":false,"error":"Error 6","fallbacks":["disable-component"],"id":"recovery_1752918663000","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918663000","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:03.000Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918663000","service":"trading-system","timestamp":"2025-07-19T09:51:03.000Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-19T09:51:03.001Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 7","service":"trading-system","stack":"Error: Error 7\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-19T09:51:03.001Z"}
{"component":"test-component","critical":false,"error":"Error 7","fallbacks":["disable-component"],"id":"recovery_1752918663002","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918663002","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:03.002Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918663002","service":"trading-system","timestamp":"2025-07-19T09:51:03.002Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-19T09:51:03.002Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 8","service":"trading-system","stack":"Error: Error 8\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-19T09:51:03.003Z"}
{"component":"test-component","critical":false,"error":"Error 8","fallbacks":["disable-component"],"id":"recovery_1752918663003","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918663003","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:03.003Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918663003","service":"trading-system","timestamp":"2025-07-19T09:51:03.004Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-19T09:51:03.004Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 9","service":"trading-system","stack":"Error: Error 9\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-19T09:51:03.005Z"}
{"component":"test-component","critical":false,"error":"Error 9","fallbacks":["disable-component"],"id":"recovery_1752918663005","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918663005","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:03.005Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918663005","service":"trading-system","timestamp":"2025-07-19T09:51:03.006Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-19T09:51:03.006Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 10","service":"trading-system","stack":"Error: Error 10\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-19T09:51:03.007Z"}
{"component":"test-component","critical":false,"error":"Error 10","fallbacks":["disable-component"],"id":"recovery_1752918663008","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918663008","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:03.008Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918663008","service":"trading-system","timestamp":"2025-07-19T09:51:03.008Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-19T09:51:03.009Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 11","service":"trading-system","stack":"Error: Error 11\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-19T09:51:03.013Z"}
{"component":"test-component","critical":false,"error":"Error 11","fallbacks":["disable-component"],"id":"recovery_1752918663013","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918663013","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:03.013Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918663013","service":"trading-system","timestamp":"2025-07-19T09:51:03.014Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-19T09:51:03.014Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 12","service":"trading-system","stack":"Error: Error 12\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-19T09:51:03.015Z"}
{"component":"test-component","critical":false,"error":"Error 12","fallbacks":["disable-component"],"id":"recovery_1752918663015","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918663015","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:03.015Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918663015","service":"trading-system","timestamp":"2025-07-19T09:51:03.016Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-19T09:51:03.016Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 13","service":"trading-system","stack":"Error: Error 13\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-19T09:51:03.017Z"}
{"component":"test-component","critical":false,"error":"Error 13","fallbacks":["disable-component"],"id":"recovery_1752918663017","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918663017","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:03.017Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918663017","service":"trading-system","timestamp":"2025-07-19T09:51:03.018Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-19T09:51:03.018Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 14","service":"trading-system","stack":"Error: Error 14\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-19T09:51:03.019Z"}
{"component":"test-component","critical":false,"error":"Error 14","fallbacks":["disable-component"],"id":"recovery_1752918663019","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918663019","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:03.019Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918663019","service":"trading-system","timestamp":"2025-07-19T09:51:03.020Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-19T09:51:03.020Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:03.023Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:03.025Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:03.027Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:03.028Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:03.029Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Test error","service":"trading-system","stack":"Error: Test error\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at TradingSystemErrorHandler.handleError [as withErrorHandling] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:528:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:269:17)","timestamp":"2025-07-19T09:51:03.031Z"}
{"component":"test-component","critical":false,"error":"Test error","fallbacks":["disable-component"],"id":"recovery_1752918663032","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1752918663032","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-19T09:51:03.032Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1752918663032","service":"trading-system","timestamp":"2025-07-19T09:51:03.032Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-19T09:51:03.033Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:03.034Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:03.034Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:03.035Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:03.035Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:03.035Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:03.044Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:03.050Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:03.051Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:03.051Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:03.051Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:03.053Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:03.053Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:03.054Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:03.054Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:03.054Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1763948,"external":3913981,"heapTotal":37146624,"heapUsed":34018744,"rss":81113088},"risk":"high","service":"trading-system","timestamp":"2025-07-19T09:51:23.076Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:33.065Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:33.069Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:33.069Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:33.069Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T09:51:33.070Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T09:51:33.173Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T14:34:38.355Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T14:34:38.356Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T14:34:38.356Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T14:34:38.356Z"}
{"level":"info","message":"Initializing LLM Coordinator","service":"trading-system","timestamp":"2025-07-19T14:34:38.364Z"}
{"level":"warn","message":"Provider OpenAI GPT-4 not configured","service":"trading-system","timestamp":"2025-07-19T14:34:38.364Z"}
{"level":"warn","message":"Provider Anthropic Claude not configured","service":"trading-system","timestamp":"2025-07-19T14:34:38.364Z"}
{"level":"info","message":"LLM Coordinator initialized successfully","service":"trading-system","timestamp":"2025-07-19T14:34:38.364Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1494408,"external":4834269,"heapTotal":169582592,"heapUsed":145926144,"rss":237694976},"risk":"high","service":"trading-system","timestamp":"2025-07-19T14:39:08.358Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1674632,"external":5014493,"heapTotal":184000512,"heapUsed":159341464,"rss":240316416},"risk":"high","service":"trading-system","timestamp":"2025-07-19T14:51:38.367Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1404296,"external":4744157,"heapTotal":163553280,"heapUsed":139488528,"rss":239149056},"risk":"high","service":"trading-system","timestamp":"2025-07-19T14:52:28.367Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1453448,"external":4793309,"heapTotal":167223296,"heapUsed":142185664,"rss":240857088},"risk":"high","service":"trading-system","timestamp":"2025-07-19T14:55:48.371Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1322376,"external":4662237,"heapTotal":156999680,"heapUsed":133496920,"rss":239583232},"risk":"high","service":"trading-system","timestamp":"2025-07-19T15:01:18.376Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1379720,"external":4719581,"heapTotal":161193984,"heapUsed":137222432,"rss":239984640},"risk":"high","service":"trading-system","timestamp":"2025-07-19T15:02:08.376Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T15:07:15.312Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T15:07:15.313Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T15:07:15.313Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T15:07:15.313Z"}
{"level":"info","message":"Initializing LLM Coordinator","service":"trading-system","timestamp":"2025-07-19T15:07:15.322Z"}
{"level":"warn","message":"Provider OpenAI GPT-4 not configured","service":"trading-system","timestamp":"2025-07-19T15:07:15.322Z"}
{"level":"warn","message":"Provider Anthropic Claude not configured","service":"trading-system","timestamp":"2025-07-19T15:07:15.322Z"}
{"level":"info","message":"LLM Coordinator initialized successfully","service":"trading-system","timestamp":"2025-07-19T15:07:15.322Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T15:07:30.400Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T15:07:30.401Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-19T15:07:30.401Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T15:07:30.401Z"}
{"level":"info","message":"Initializing LLM Coordinator","service":"trading-system","timestamp":"2025-07-19T15:07:30.408Z"}
{"level":"warn","message":"Provider OpenAI GPT-4 not configured","service":"trading-system","timestamp":"2025-07-19T15:07:30.409Z"}
{"level":"warn","message":"Provider Anthropic Claude not configured","service":"trading-system","timestamp":"2025-07-19T15:07:30.409Z"}
{"level":"info","message":"LLM Coordinator initialized successfully","service":"trading-system","timestamp":"2025-07-19T15:07:30.409Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1568136,"external":4907997,"heapTotal":175874048,"heapUsed":149945856,"rss":237572096},"risk":"high","service":"trading-system","timestamp":"2025-07-19T15:22:00.417Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1707400,"external":5047261,"heapTotal":186359808,"heapUsed":159256320,"rss":236425216},"risk":"high","service":"trading-system","timestamp":"2025-07-19T15:30:00.484Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1510792,"external":4850653,"heapTotal":171679744,"heapUsed":146188608,"rss":211845120},"risk":"high","service":"trading-system","timestamp":"2025-07-19T15:41:50.489Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1691016,"external":5030877,"heapTotal":184524800,"heapUsed":160224288,"rss":209588224},"risk":"high","service":"trading-system","timestamp":"2025-07-19T15:44:50.491Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1518984,"external":4858845,"heapTotal":171941888,"heapUsed":147608224,"rss":210329600},"risk":"high","service":"trading-system","timestamp":"2025-07-19T15:45:50.491Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1658248,"external":4998109,"heapTotal":182165504,"heapUsed":158266976,"rss":204574720},"risk":"high","service":"trading-system","timestamp":"2025-07-19T15:52:10.493Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1756552,"external":5096413,"heapTotal":190029824,"heapUsed":163838464,"rss":202928128},"risk":"high","service":"trading-system","timestamp":"2025-07-19T15:57:50.495Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1707400,"external":5047261,"heapTotal":186097664,"heapUsed":161667640,"rss":202678272},"risk":"high","service":"trading-system","timestamp":"2025-07-19T16:04:00.500Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1813896,"external":5153757,"heapTotal":194486272,"heapUsed":167059152,"rss":203280384},"risk":"high","service":"trading-system","timestamp":"2025-07-19T16:05:10.502Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1568136,"external":4907997,"heapTotal":176136192,"heapUsed":150297576,"rss":198139904},"risk":"high","service":"trading-system","timestamp":"2025-07-19T16:08:20.519Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1731976,"external":5071837,"heapTotal":188456960,"heapUsed":160300248,"rss":195289088},"risk":"high","service":"trading-system","timestamp":"2025-07-19T16:11:30.520Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1740168,"external":5080029,"heapTotal":188194816,"heapUsed":163075296,"rss":192217088},"risk":"high","service":"trading-system","timestamp":"2025-07-19T16:13:50.524Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1461640,"external":4801501,"heapTotal":168271872,"heapUsed":143762992,"rss":192540672},"risk":"high","service":"trading-system","timestamp":"2025-07-19T16:26:50.535Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1813896,"external":5153757,"heapTotal":194486272,"heapUsed":166978376,"rss":191504384},"risk":"high","service":"trading-system","timestamp":"2025-07-19T16:29:10.535Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1437064,"external":4776925,"heapTotal":165650432,"heapUsed":141321600,"rss":192843776},"risk":"high","service":"trading-system","timestamp":"2025-07-19T16:33:40.539Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1355144,"external":4695005,"heapTotal":159621120,"heapUsed":135908128,"rss":192909312},"risk":"high","service":"trading-system","timestamp":"2025-07-19T16:40:40.542Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1543560,"external":4883421,"heapTotal":174039040,"heapUsed":149994496,"rss":192942080},"risk":"high","service":"trading-system","timestamp":"2025-07-19T16:41:20.543Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1625480,"external":4965341,"heapTotal":180330496,"heapUsed":153744536,"rss":192925696},"risk":"high","service":"trading-system","timestamp":"2025-07-19T16:43:10.545Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1797512,"external":5137373,"heapTotal":192389120,"heapUsed":167754696,"rss":191803392},"risk":"high","service":"trading-system","timestamp":"2025-07-19T16:44:00.544Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1731976,"external":5071837,"heapTotal":188194816,"heapUsed":161107344,"rss":190349312},"risk":"high","service":"trading-system","timestamp":"2025-07-19T16:46:30.546Z"}
{"level":"warn","message":"[EnhancedRecovery] Predictive failure detected: memory-pattern","metrics":{"arrayBuffers":1707400,"external":5047261,"heapTotal":186621952,"heapUsed":160404600,"rss":189456384},"risk":"high","service":"trading-system","timestamp":"2025-07-19T16:56:40.551Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.103Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.111Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.112Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.112Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: trading-executor Test trading error","service":"trading-system","stack":"Error: Test trading error\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:40:50)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-20T18:48:16.131Z"}
{"component":"trading-executor","critical":false,"error":"Test trading error","fallbacks":["disable-component"],"id":"recovery_1753037296132","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296132","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"trading-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.133Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296132","service":"trading-system","timestamp":"2025-07-20T18:48:16.133Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: trading-executor","service":"trading-system","timestamp":"2025-07-20T18:48:16.134Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.138Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.140Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.141Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.141Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.142Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.146Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.147Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.148Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.148Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.149Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: exchange-api Exchange API error","service":"trading-system","stack":"Error: Exchange API error\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:67:32)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-20T18:48:16.153Z"}
{"component":"exchange-api","critical":false,"error":"Exchange API error","fallbacks":["read-only-mode","cached-data-mode"],"id":"recovery_1753037296154","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296154","service":"trading-system","steps":["validate-connection","retry-with-backoff","switch-to-backup-exchange","notify-operators"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.154Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: validate-connection","planId":"recovery_1753037296154","service":"trading-system","timestamp":"2025-07-20T18:48:16.155Z"}
{"level":"info","message":"[EnhancedRecovery] Validating connection for exchange-api","service":"trading-system","timestamp":"2025-07-20T18:48:16.155Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: exchange-api Another exchange error","service":"trading-system","stack":"Error: Another exchange error\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:68:32)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-20T18:48:16.157Z"}
{"component":"exchange-api","critical":false,"error":"Another exchange error","fallbacks":["read-only-mode","cached-data-mode"],"id":"recovery_1753037296158","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296158","service":"trading-system","steps":["validate-connection","retry-with-backoff","switch-to-backup-exchange","notify-operators"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.158Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: validate-connection","planId":"recovery_1753037296158","service":"trading-system","timestamp":"2025-07-20T18:48:16.158Z"}
{"level":"info","message":"[EnhancedRecovery] Validating connection for exchange-api","service":"trading-system","timestamp":"2025-07-20T18:48:16.159Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.161Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.162Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.162Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.162Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.163Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.164Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.165Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.165Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.166Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.166Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.169Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.170Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.171Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.171Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.172Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.173Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.178Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.178Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.178Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.179Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.286Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.287Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.288Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.288Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.288Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.396Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.397Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.397Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.397Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.398Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: trading-system Critical error 0","service":"trading-system","stack":"Error: Critical error 0\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:192:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-20T18:48:16.398Z"}
{"component":"trading-system","critical":false,"error":"Critical error 0","fallbacks":["disable-component"],"id":"recovery_1753037296399","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296399","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"trading-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.399Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296399","service":"trading-system","timestamp":"2025-07-20T18:48:16.399Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: trading-system","service":"trading-system","timestamp":"2025-07-20T18:48:16.399Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: trading-system Critical error 1","service":"trading-system","stack":"Error: Critical error 1\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:192:36)","timestamp":"2025-07-20T18:48:16.400Z"}
{"component":"trading-system","critical":false,"error":"Critical error 1","fallbacks":["disable-component"],"id":"recovery_1753037296400","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296400","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"trading-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.400Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296400","service":"trading-system","timestamp":"2025-07-20T18:48:16.400Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: trading-system","service":"trading-system","timestamp":"2025-07-20T18:48:16.401Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.403Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.404Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.404Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.404Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.405Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.405Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.406Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.406Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.406Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.406Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.407Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.407Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.408Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.408Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.408Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.409Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.410Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.410Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.410Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.410Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 0","service":"trading-system","stack":"Error: Error 0\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-20T18:48:16.414Z"}
{"component":"test-component","critical":false,"error":"Error 0","fallbacks":["disable-component"],"id":"recovery_1753037296414","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296414","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.414Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296414","service":"trading-system","timestamp":"2025-07-20T18:48:16.414Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-20T18:48:16.414Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 1","service":"trading-system","stack":"Error: Error 1\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-20T18:48:16.415Z"}
{"component":"test-component","critical":false,"error":"Error 1","fallbacks":["disable-component"],"id":"recovery_1753037296415","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296415","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.415Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296415","service":"trading-system","timestamp":"2025-07-20T18:48:16.415Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-20T18:48:16.415Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 2","service":"trading-system","stack":"Error: Error 2\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-20T18:48:16.416Z"}
{"component":"test-component","critical":false,"error":"Error 2","fallbacks":["disable-component"],"id":"recovery_1753037296416","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296416","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.416Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296416","service":"trading-system","timestamp":"2025-07-20T18:48:16.416Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-20T18:48:16.416Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 3","service":"trading-system","stack":"Error: Error 3\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-20T18:48:16.417Z"}
{"component":"test-component","critical":false,"error":"Error 3","fallbacks":["disable-component"],"id":"recovery_1753037296417","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296417","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.417Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296417","service":"trading-system","timestamp":"2025-07-20T18:48:16.418Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-20T18:48:16.418Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 4","service":"trading-system","stack":"Error: Error 4\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-20T18:48:16.418Z"}
{"component":"test-component","critical":false,"error":"Error 4","fallbacks":["disable-component"],"id":"recovery_1753037296419","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296419","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.419Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296419","service":"trading-system","timestamp":"2025-07-20T18:48:16.419Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-20T18:48:16.419Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 5","service":"trading-system","stack":"Error: Error 5\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-20T18:48:16.420Z"}
{"component":"test-component","critical":false,"error":"Error 5","fallbacks":["disable-component"],"id":"recovery_1753037296420","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296420","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.420Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296420","service":"trading-system","timestamp":"2025-07-20T18:48:16.420Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-20T18:48:16.420Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 6","service":"trading-system","stack":"Error: Error 6\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-20T18:48:16.421Z"}
{"component":"test-component","critical":false,"error":"Error 6","fallbacks":["disable-component"],"id":"recovery_1753037296421","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296421","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.421Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296421","service":"trading-system","timestamp":"2025-07-20T18:48:16.421Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-20T18:48:16.424Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 7","service":"trading-system","stack":"Error: Error 7\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-20T18:48:16.424Z"}
{"component":"test-component","critical":false,"error":"Error 7","fallbacks":["disable-component"],"id":"recovery_1753037296425","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296425","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.425Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296425","service":"trading-system","timestamp":"2025-07-20T18:48:16.425Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-20T18:48:16.425Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 8","service":"trading-system","stack":"Error: Error 8\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-20T18:48:16.426Z"}
{"component":"test-component","critical":false,"error":"Error 8","fallbacks":["disable-component"],"id":"recovery_1753037296426","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296426","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.426Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296426","service":"trading-system","timestamp":"2025-07-20T18:48:16.426Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-20T18:48:16.427Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 9","service":"trading-system","stack":"Error: Error 9\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-20T18:48:16.427Z"}
{"component":"test-component","critical":false,"error":"Error 9","fallbacks":["disable-component"],"id":"recovery_1753037296427","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296427","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.427Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296427","service":"trading-system","timestamp":"2025-07-20T18:48:16.428Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-20T18:48:16.428Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 10","service":"trading-system","stack":"Error: Error 10\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-20T18:48:16.428Z"}
{"component":"test-component","critical":false,"error":"Error 10","fallbacks":["disable-component"],"id":"recovery_1753037296428","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296428","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.428Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296428","service":"trading-system","timestamp":"2025-07-20T18:48:16.428Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-20T18:48:16.429Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 11","service":"trading-system","stack":"Error: Error 11\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-20T18:48:16.429Z"}
{"component":"test-component","critical":false,"error":"Error 11","fallbacks":["disable-component"],"id":"recovery_1753037296429","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296429","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.429Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296429","service":"trading-system","timestamp":"2025-07-20T18:48:16.429Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-20T18:48:16.429Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 12","service":"trading-system","stack":"Error: Error 12\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-20T18:48:16.430Z"}
{"component":"test-component","critical":false,"error":"Error 12","fallbacks":["disable-component"],"id":"recovery_1753037296430","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296430","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.430Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296430","service":"trading-system","timestamp":"2025-07-20T18:48:16.430Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-20T18:48:16.430Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 13","service":"trading-system","stack":"Error: Error 13\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-20T18:48:16.431Z"}
{"component":"test-component","critical":false,"error":"Error 13","fallbacks":["disable-component"],"id":"recovery_1753037296431","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296431","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.431Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296431","service":"trading-system","timestamp":"2025-07-20T18:48:16.431Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-20T18:48:16.431Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Error 14","service":"trading-system","stack":"Error: Error 14\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at Object.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:250:36)","timestamp":"2025-07-20T18:48:16.432Z"}
{"component":"test-component","critical":false,"error":"Error 14","fallbacks":["disable-component"],"id":"recovery_1753037296432","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296432","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.432Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296432","service":"trading-system","timestamp":"2025-07-20T18:48:16.432Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-20T18:48:16.432Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.435Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.436Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.436Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.436Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.436Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: test-component Test error","service":"trading-system","stack":"Error: Test error\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:301:17)\n    at TradingSystemErrorHandler.attemptRecovery [as handleError] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:189:24)\n    at TradingSystemErrorHandler.handleError [as withErrorHandling] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:528:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\integration\\trading-system-error-handling.test.js:269:17)","timestamp":"2025-07-20T18:48:16.437Z"}
{"component":"test-component","critical":false,"error":"Test error","fallbacks":["disable-component"],"id":"recovery_1753037296437","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753037296437","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T18:48:16.437Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753037296437","service":"trading-system","timestamp":"2025-07-20T18:48:16.438Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: test-component","service":"trading-system","timestamp":"2025-07-20T18:48:16.438Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.438Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.439Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.439Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.439Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.439Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.440Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.444Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.444Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.444Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.444Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.445Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.446Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.446Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:16.446Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:16.447Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:21.450Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:21.451Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:21.452Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:21.452Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T18:48:21.452Z"}
{"level":"info","message":"[EnhancedRecovery] Shutting down Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T18:48:21.559Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T20:09:23.384Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T20:09:23.386Z"}
{"level":"info","message":"[EnhancedRecovery] Initializing Enhanced Recovery Manager","service":"trading-system","timestamp":"2025-07-20T20:09:23.386Z"}
{"level":"info","message":"[EnhancedRecovery] Enhanced Recovery Manager initialized successfully","service":"trading-system","timestamp":"2025-07-20T20:09:23.386Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: trading-workflow Trading execution failed: insufficient funds","service":"trading-system","stack":"Error: Trading execution failed: insufficient funds\n    at TradingSystemErrorHandler.handleTradingWorkflowError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:421:17)\n    at TradingSystemErrorHandler.handleWorkflowError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:383:28)\n    at TradingSystemErrorHandler.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:206:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async verifyEnhancedErrorHandling (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\verify-enhanced-error-handling.js:31:27)","timestamp":"2025-07-20T20:09:23.390Z"}
{"component":"trading-workflow","critical":true,"error":"Trading execution failed: insufficient funds","fallbacks":["disable-component"],"id":"recovery_1753042163390","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753042163390","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"trading-recovery","timeout":60000,"timestamp":"2025-07-20T20:09:23.391Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753042163390","service":"trading-system","timestamp":"2025-07-20T20:09:23.391Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: trading-workflow","service":"trading-system","timestamp":"2025-07-20T20:09:23.391Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: portfolio-manager Portfolio calculation failed: invalid data","service":"trading-system","stack":"Error: Portfolio calculation failed: invalid data\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:773:17)\n    at TradingSystemErrorHandler.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:210:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async verifyEnhancedErrorHandling (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\verify-enhanced-error-handling.js:47:36)","timestamp":"2025-07-20T20:09:23.393Z"}
{"component":"portfolio-manager","critical":false,"error":"Portfolio calculation failed: invalid data","fallbacks":["disable-component"],"id":"recovery_1753042163394","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753042163394","service":"trading-system","steps":["restart-component","validate-functionality","restore-state"],"strategy":"generic-recovery","timeout":60000,"timestamp":"2025-07-20T20:09:23.394Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: restart-component","planId":"recovery_1753042163394","service":"trading-system","timestamp":"2025-07-20T20:09:23.394Z"}
{"level":"info","message":"[EnhancedRecovery] Restarting component: portfolio-manager","service":"trading-system","timestamp":"2025-07-20T20:09:23.394Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: exchange-api Network connection failed","service":"trading-system","stack":"Error: Network connection failed\n    at TradingSystemErrorHandler.handleGeneralWorkflowError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:484:17)\n    at TradingSystemErrorHandler.handleWorkflowError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:398:28)\n    at TradingSystemErrorHandler.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:206:20)\n    at verifyEnhancedErrorHandling (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\verify-enhanced-error-handling.js:61:53)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-20T20:09:23.396Z"}
{"component":"exchange-api","critical":false,"error":"Network connection failed","fallbacks":["read-only-mode","cached-data-mode"],"id":"recovery_1753042163396","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753042163396","service":"trading-system","steps":["validate-connection","retry-with-backoff","switch-to-backup-exchange","notify-operators"],"strategy":"network-recovery","timeout":60000,"timestamp":"2025-07-20T20:09:23.396Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: validate-connection","planId":"recovery_1753042163396","service":"trading-system","timestamp":"2025-07-20T20:09:23.396Z"}
{"level":"info","message":"[EnhancedRecovery] Validating connection for exchange-api","service":"trading-system","timestamp":"2025-07-20T20:09:23.396Z"}
{"level":"warn","message":"[EnhancedRecovery] Component failure detected: exchange-api Network connection failed","service":"trading-system","stack":"Error: Network connection failed\n    at TradingSystemErrorHandler.attemptRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:773:17)\n    at TradingSystemErrorHandler.handleError (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\error-handling\\TradingSystemErrorHandler.js:210:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async verifyEnhancedErrorHandling (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\verify-enhanced-error-handling.js:61:34)","timestamp":"2025-07-20T20:09:23.397Z"}
{"component":"exchange-api","critical":false,"error":"Network connection failed","fallbacks":["read-only-mode","cached-data-mode"],"id":"recovery_1753042163397","level":"info","message":"[EnhancedRecovery] Executing recovery plan: recovery_1753042163397","service":"trading-system","steps":["validate-connection","retry-with-backoff","switch-to-backup-exchange","notify-operators"],"strategy":"network-recovery","timeout":60000,"timestamp":"2025-07-20T20:09:23.397Z"}
{"level":"info","message":"[EnhancedRecovery] Executing recovery step: validate-connection","planId":"recovery_1753042163397","service":"trading-system","timestamp":"2025-07-20T20:09:23.397Z"}
{"level":"info","message":"[EnhancedRecovery] Validating connection for exchange-api","service":"trading-system","timestamp":"2025-07-20T20:09:23.397Z"}
{"level":"info","message":"Recovery Manager initialized","service":"trading-system","timestamp":"2025-07-21T04:13:34.278Z"}
{"level":"info","message":"Storage initialized","service":"trading-system","timestamp":"2025-07-21T04:13:34.299Z"}
{"level":"error","message":"Failed to initialize SecureCredentialManager Master password required","service":"trading-system","stack":"Error: Master password required\n    at SecureCredentialManager.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\SecureCredentialManager.js:82:27)\n    at async Promise.all (index 0)\n    at async AutonomousTrader.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\ai\\AutonomousTrader.js:117:13)\n    at async TradingOrchestrator.initializeAutonomousTrader (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:878:13)\n    at async TradingOrchestrator.initializeComponents (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:635:13)\n    at async TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:169:13)\n    at async testComponentIntegration (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-component-integration.js:31:9)","timestamp":"2025-07-21T04:13:34.300Z"}
{"level":"warn","message":"Failure recorded: memory:high_usage High memory usage: 90.22%","service":"trading-system","stack":"Error: High memory usage: 90.22%\n    at RecoveryManager.performHealthCheck (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:471:28)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:440:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-21T04:14:04.292Z"}
{"level":"info","message":"Starting recovery for memory (ID: recovery_1753071244292_2s5q36lht)","service":"trading-system","timestamp":"2025-07-21T04:14:04.292Z"}
{"level":"info","message":"Attempting memory recovery","service":"trading-system","timestamp":"2025-07-21T04:14:04.293Z"}
{"level":"info","message":"Memory recovery completed","service":"trading-system","timestamp":"2025-07-21T04:14:04.293Z"}
{"level":"info","message":"Recovery successful for memory","service":"trading-system","timestamp":"2025-07-21T04:14:04.293Z"}
{"level":"warn","message":"Failure recorded: memory:high_usage High memory usage: 90.89%","service":"trading-system","stack":"Error: High memory usage: 90.89%\n    at RecoveryManager.performHealthCheck (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:471:28)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:440:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-21T04:14:34.304Z"}
{"level":"info","message":"Starting recovery for memory (ID: recovery_1753071274305_86o3wjrp7)","service":"trading-system","timestamp":"2025-07-21T04:14:34.305Z"}
{"level":"info","message":"Attempting memory recovery","service":"trading-system","timestamp":"2025-07-21T04:14:34.305Z"}
{"level":"info","message":"Memory recovery completed","service":"trading-system","timestamp":"2025-07-21T04:14:34.306Z"}
{"level":"info","message":"Recovery successful for memory","service":"trading-system","timestamp":"2025-07-21T04:14:34.306Z"}
{"level":"warn","message":"Failure recorded: memory:high_usage High memory usage: 91.34%","service":"trading-system","stack":"Error: High memory usage: 91.34%\n    at RecoveryManager.performHealthCheck (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:471:28)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:440:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-21T04:15:04.313Z"}
{"level":"info","message":"Starting recovery for memory (ID: recovery_1753071304314_b8bwhvrqc)","service":"trading-system","timestamp":"2025-07-21T04:15:04.314Z"}
{"level":"info","message":"Attempting memory recovery","service":"trading-system","timestamp":"2025-07-21T04:15:04.314Z"}
{"level":"info","message":"Memory recovery completed","service":"trading-system","timestamp":"2025-07-21T04:15:04.314Z"}
{"level":"info","message":"Recovery successful for memory","service":"trading-system","timestamp":"2025-07-21T04:15:04.315Z"}
{"level":"warn","message":"Failure recorded: memory:high_usage High memory usage: 91.63%","service":"trading-system","stack":"Error: High memory usage: 91.63%\n    at RecoveryManager.performHealthCheck (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:471:28)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:440:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-21T04:15:34.320Z"}
{"level":"info","message":"Starting recovery for memory (ID: recovery_1753071334320_xj23okgs0)","service":"trading-system","timestamp":"2025-07-21T04:15:34.320Z"}
{"level":"info","message":"Attempting memory recovery","service":"trading-system","timestamp":"2025-07-21T04:15:34.321Z"}
{"level":"info","message":"Memory recovery completed","service":"trading-system","timestamp":"2025-07-21T04:15:34.321Z"}
{"level":"info","message":"Recovery successful for memory","service":"trading-system","timestamp":"2025-07-21T04:15:34.321Z"}
{"level":"warn","message":"Failure recorded: memory:high_usage High memory usage: 91.95%","service":"trading-system","stack":"Error: High memory usage: 91.95%\n    at RecoveryManager.performHealthCheck (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:471:28)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:440:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-21T04:16:04.336Z"}
{"level":"info","message":"Starting recovery for memory (ID: recovery_1753071364336_q9fxa9xtj)","service":"trading-system","timestamp":"2025-07-21T04:16:04.336Z"}
{"level":"info","message":"Attempting memory recovery","service":"trading-system","timestamp":"2025-07-21T04:16:04.336Z"}
{"level":"info","message":"Memory recovery completed","service":"trading-system","timestamp":"2025-07-21T04:16:04.336Z"}
{"level":"info","message":"Recovery successful for memory","service":"trading-system","timestamp":"2025-07-21T04:16:04.337Z"}
{"level":"warn","message":"Failure recorded: memory:high_usage High memory usage: 92.29%","service":"trading-system","stack":"Error: High memory usage: 92.29%\n    at RecoveryManager.performHealthCheck (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:471:28)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:440:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-21T04:16:34.350Z"}
{"level":"info","message":"Starting recovery for memory (ID: recovery_1753071394351_9s6yackab)","service":"trading-system","timestamp":"2025-07-21T04:16:34.351Z"}
{"level":"info","message":"Attempting memory recovery","service":"trading-system","timestamp":"2025-07-21T04:16:34.351Z"}
{"level":"info","message":"Memory recovery completed","service":"trading-system","timestamp":"2025-07-21T04:16:34.352Z"}
{"level":"info","message":"Recovery successful for memory","service":"trading-system","timestamp":"2025-07-21T04:16:34.352Z"}
{"level":"warn","message":"Failure recorded: memory:high_usage High memory usage: 92.60%","service":"trading-system","stack":"Error: High memory usage: 92.60%\n    at RecoveryManager.performHealthCheck (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:471:28)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:440:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-21T04:17:04.357Z"}
{"level":"info","message":"Starting recovery for memory (ID: recovery_1753071424358_hmo07xgwj)","service":"trading-system","timestamp":"2025-07-21T04:17:04.358Z"}
{"level":"info","message":"Attempting memory recovery","service":"trading-system","timestamp":"2025-07-21T04:17:04.358Z"}
{"level":"info","message":"Memory recovery completed","service":"trading-system","timestamp":"2025-07-21T04:17:04.358Z"}
{"level":"info","message":"Recovery successful for memory","service":"trading-system","timestamp":"2025-07-21T04:17:04.358Z"}
{"level":"warn","message":"Failure recorded: memory:high_usage High memory usage: 90.42%","service":"trading-system","stack":"Error: High memory usage: 90.42%\n    at RecoveryManager.performHealthCheck (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:471:28)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:440:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-21T04:24:34.468Z"}
{"level":"info","message":"Starting recovery for memory (ID: recovery_1753071874469_3i0zluwut)","service":"trading-system","timestamp":"2025-07-21T04:24:34.469Z"}
{"level":"info","message":"Attempting memory recovery","service":"trading-system","timestamp":"2025-07-21T04:24:34.469Z"}
{"level":"info","message":"Memory recovery completed","service":"trading-system","timestamp":"2025-07-21T04:24:34.469Z"}
{"level":"info","message":"Recovery successful for memory","service":"trading-system","timestamp":"2025-07-21T04:24:34.469Z"}
{"level":"warn","message":"Failure recorded: memory:high_usage High memory usage: 90.79%","service":"trading-system","stack":"Error: High memory usage: 90.79%\n    at RecoveryManager.performHealthCheck (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:471:28)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:440:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-21T04:25:04.482Z"}
{"level":"info","message":"Starting recovery for memory (ID: recovery_1753071904482_zorplxyvy)","service":"trading-system","timestamp":"2025-07-21T04:25:04.482Z"}
{"level":"info","message":"Attempting memory recovery","service":"trading-system","timestamp":"2025-07-21T04:25:04.483Z"}
{"level":"info","message":"Memory recovery completed","service":"trading-system","timestamp":"2025-07-21T04:25:04.483Z"}
{"level":"info","message":"Recovery successful for memory","service":"trading-system","timestamp":"2025-07-21T04:25:04.483Z"}
{"level":"warn","message":"Failure recorded: memory:high_usage High memory usage: 91.14%","service":"trading-system","stack":"Error: High memory usage: 91.14%\n    at RecoveryManager.performHealthCheck (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:471:28)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:440:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-21T04:25:34.484Z"}
{"level":"info","message":"Starting recovery for memory (ID: recovery_1753071934485_8mdf3fipx)","service":"trading-system","timestamp":"2025-07-21T04:25:34.485Z"}
{"level":"info","message":"Attempting memory recovery","service":"trading-system","timestamp":"2025-07-21T04:25:34.485Z"}
{"level":"info","message":"Memory recovery completed","service":"trading-system","timestamp":"2025-07-21T04:25:34.485Z"}
{"level":"info","message":"Recovery successful for memory","service":"trading-system","timestamp":"2025-07-21T04:25:34.485Z"}
{"level":"warn","message":"Failure recorded: memory:high_usage High memory usage: 91.44%","service":"trading-system","stack":"Error: High memory usage: 91.44%\n    at RecoveryManager.performHealthCheck (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:471:28)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:440:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-21T04:26:04.488Z"}
{"level":"info","message":"Starting recovery for memory (ID: recovery_1753071964488_5t1h5m9q5)","service":"trading-system","timestamp":"2025-07-21T04:26:04.488Z"}
{"level":"info","message":"Attempting memory recovery","service":"trading-system","timestamp":"2025-07-21T04:26:04.488Z"}
{"level":"info","message":"Memory recovery completed","service":"trading-system","timestamp":"2025-07-21T04:26:04.488Z"}
{"level":"info","message":"Recovery successful for memory","service":"trading-system","timestamp":"2025-07-21T04:26:04.488Z"}
{"level":"warn","message":"Failure recorded: memory:high_usage High memory usage: 91.76%","service":"trading-system","stack":"Error: High memory usage: 91.76%\n    at RecoveryManager.performHealthCheck (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:471:28)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:440:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-21T04:26:34.502Z"}
{"level":"info","message":"Starting recovery for memory (ID: recovery_1753071994502_qgwc27bbc)","service":"trading-system","timestamp":"2025-07-21T04:26:34.502Z"}
{"level":"info","message":"Attempting memory recovery","service":"trading-system","timestamp":"2025-07-21T04:26:34.503Z"}
{"level":"info","message":"Memory recovery completed","service":"trading-system","timestamp":"2025-07-21T04:26:34.503Z"}
{"level":"info","message":"Recovery successful for memory","service":"trading-system","timestamp":"2025-07-21T04:26:34.503Z"}
{"level":"warn","message":"Failure recorded: memory:high_usage High memory usage: 92.09%","service":"trading-system","stack":"Error: High memory usage: 92.09%\n    at RecoveryManager.performHealthCheck (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:471:28)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:440:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-21T04:27:04.505Z"}
{"level":"info","message":"Starting recovery for memory (ID: recovery_1753072024505_noc0mnda2)","service":"trading-system","timestamp":"2025-07-21T04:27:04.505Z"}
{"level":"info","message":"Attempting memory recovery","service":"trading-system","timestamp":"2025-07-21T04:27:04.505Z"}
{"level":"info","message":"Memory recovery completed","service":"trading-system","timestamp":"2025-07-21T04:27:04.505Z"}
{"level":"info","message":"Recovery successful for memory","service":"trading-system","timestamp":"2025-07-21T04:27:04.505Z"}
{"level":"warn","message":"Failure recorded: memory:high_usage High memory usage: 92.37%","service":"trading-system","stack":"Error: High memory usage: 92.37%\n    at RecoveryManager.performHealthCheck (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:471:28)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:440:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-21T04:27:34.519Z"}
{"level":"info","message":"Starting recovery for memory (ID: recovery_1753072054520_89u4h52jo)","service":"trading-system","timestamp":"2025-07-21T04:27:34.520Z"}
{"level":"info","message":"Attempting memory recovery","service":"trading-system","timestamp":"2025-07-21T04:27:34.520Z"}
{"level":"info","message":"Memory recovery completed","service":"trading-system","timestamp":"2025-07-21T04:27:34.520Z"}
{"level":"info","message":"Recovery successful for memory","service":"trading-system","timestamp":"2025-07-21T04:27:34.520Z"}
{"level":"warn","message":"Failure recorded: memory:high_usage High memory usage: 90.68%","service":"trading-system","stack":"Error: High memory usage: 90.68%\n    at RecoveryManager.performHealthCheck (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:471:28)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\recovery\\RecoveryManager.js:440:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-21T04:28:04.530Z"}
{"level":"info","message":"Starting recovery for memory (ID: recovery_1753072084531_95rvw6r2q)","service":"trading-system","timestamp":"2025-07-21T04:28:04.531Z"}
{"level":"info","message":"Attempting memory recovery","service":"trading-system","timestamp":"2025-07-21T04:28:04.531Z"}
{"level":"info","message":"Memory recovery completed","service":"trading-system","timestamp":"2025-07-21T04:28:04.531Z"}
{"level":"info","message":"Recovery successful for memory","service":"trading-system","timestamp":"2025-07-21T04:28:04.531Z"}

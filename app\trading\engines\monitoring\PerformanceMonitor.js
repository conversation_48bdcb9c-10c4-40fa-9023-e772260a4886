/**
 * Performance monitoring system for autonomous trading
 * Tracks and analyzes system performance, trade metrics, and component health
 */

const logger = require('../../shared/helpers/logger.js');
const EventEmitter = require('events');

class PerformanceMonitor extends EventEmitter {
    constructor(config = {}) {
        super();

        // Trading performance
        this.totalTrades = 0;
        this.isRunning = false;
        this.startTime = null;

        // Performance metrics
        this.metrics = {
            winningTrades: 0,
            losingTrades: 0,
            totalReturn: 0,
            maxDrawdown: 0,
            currentDrawdown: 0,
            averageWinRate: 0,
            averageTradeHoldTime: 0,
            profitFactor: 0,
            // System performance
            systemUptime: 0,
            apiLatency: 0,
            componentHealth: {},
            memoryUsage: 0,
            // Exchange -> latency
            cpuUsage: 0,
            // Intelligence component metrics
            newListingsDetected: 0,
            // Component -> health status
            signalsGenerated: 0,
            signalsAccepted: 0,
            sentimentAnalysisCount: 0,
            whaleActivityDetected: 0,
            pumpDetectionTriggers: 0,
            // API optimization metrics
            apiRequestsTotal: 0,
            apiRequestsCached: 0,
            rateLimitHits: 0,
            circuitBreakerTriggers: 0
        };

        this.config = {
            // Monitoring intervals
            metricsUpdateInterval: config.metricsUpdateInterval || 60000, // 1 minute
            alertCheckInterval: config.alertCheckInterval || 30000, // 30 seconds
            snapshotInterval: config.snapshotInterval || 300000, // 5 minutes

            // Performance thresholds
            maxDrawdownThreshold: config.maxDrawdownThreshold || 0.15, // 15%
            minWinRateThreshold: config.minWinRateThreshold || 0.40, // 40%
            maxApiLatencyThreshold: config.maxApiLatencyThreshold || 5000, // 5 seconds

            // Alert settings
            enableAlerts: config.enableAlerts !== false,
            alertCooldown: config.alertCooldown || 300000, // 5 minutes

            // Data retention
            maxSnapshotHistory: config.maxSnapshotHistory || 1000,
            maxAlertHistory: config.maxAlertHistory || 500,

            ...config
        };

        this.alerts = new Map();
        this.componentHealth = new Map();

        // Historical data
        this.snapshotHistory = [];
        this.alertHistory = [];
        this.tradeHistory = [];

        // Alert tracking
        this.lastAlerts = new Map();

        // Intervals
        this.metricsInterval = null;
        this.alertInterval = null;
        this.snapshotInterval = null;

        // Component references
        this.tradingOrchestrator = null;
        this.databaseManager = null;
    }

    /**
     * Initialize the performance monitor
     */
    initialize(tradingOrchestrator, databaseManager) {
        try {
            logger.info('Initializing PerformanceMonitor', {
                config: this.config
            });

            this.tradingOrchestrator = tradingOrchestrator;
            this.databaseManager = databaseManager;
            this.startTime = new Date();

            // Start monitoring intervals
            // this.startMonitoring();

            logger.info('PerformanceMonitor initialized successfully');
            return true;

        } catch (error) {
            logger.error('Failed to initialize PerformanceMonitor', {
                error
            });
            throw error;
        }
    }

    /**
     * Start all monitoring intervals
     */
    startMonitoring() {
        if (this.isRunning) return;

        this.isRunning = true;

        // Metrics update interval
        this.metricsInterval = setInterval(() => {
            this.updateMetrics();
        }, this.config.metricsUpdateInterval);

        // Alert checking interval
        if (this.config.enableAlerts) {
            this.alertInterval = setInterval(() => {
                this.checkAlerts();
            }, this.config.alertCheckInterval);
        }

        // Snapshot interval
        this.snapshotInterval = setInterval(() => {
            this.takeSnapshot();
        }, this.config.snapshotInterval);

        logger.info('Performance monitoring started');
    }

    /**
     * Update performance metrics
     */
    async updateMetrics() {
        try {
            // Update system uptime
            this.metrics.systemUptime = Date.now() - this.startTime.getTime();

            // Update system resource usage
            await this.updateSystemMetrics();

            // Update trading metrics from orchestrator
            if (this.tradingOrchestrator) {
                await this.updateTradingMetrics();
            }

            // Update component health
            await this.updateComponentHealth();

            // Emit metrics update event
            // this.emit('metricsUpdated', this.metrics);

        } catch (error) {
            logger.error('Error updating metrics', {
                error
            });
        }
    }

    /**
     * Update system resource metrics
     */
    updateSystemMetrics() {
        // Memory usage
        const memUsage = process.memoryUsage();
        // this.metrics.memoryUsage = memUsage.heapUsed / 1024 / 1024; // MB

        // CPU usage (simple approximation)
        const startUsage = process.cpuUsage();
        setTimeout(() => {
            const endUsage = process.cpuUsage(startUsage);
            const totalTime = endUsage.user + endUsage.system;
            // this.metrics.cpuUsage = totalTime / 1000000; // Convert to seconds
        }, 100);
    }

    /**
     * Update trading performance metrics
     */
    updateTradingMetrics() {
        try {
            const orchestratorStatus = this.tradingOrchestrator.getStatus();
            const performance = orchestratorStatus.performance;

            if (performance) {
                // this.metrics.totalTrades = performance.totalTrades || 0;
                // this.metrics.winningTrades = performance.successfulTrades || 0;
                // this.metrics.losingTrades = this.metrics.totalTrades - this.metrics.winningTrades;
                // this.metrics.totalReturn = performance.totalReturn || 0;
                // this.metrics.averageWinRate = performance.successRate || 0;
            }

            // Calculate additional metrics
            // this.calculateDerivedMetrics();

        } catch (error) {
            logger.error('Error updating trading metrics', {
                error
            });
        }
    }

    /**
     * Calculate derived performance metrics
     */
    calculateDerivedMetrics() {
        // Win rate
        if (this.metrics.totalTrades > 0) {
            // this.metrics.averageWinRate = this.metrics.winningTrades / this.metrics.totalTrades;
        }

        // Profit factor (gross profit / gross loss)
        if (this.tradeHistory.length > 0) {
            const profits = this.tradeHistory.filter(t => t.pnl > 0).reduce((sum, t) => sum + t.pnl, 0);
            const losses = Math.abs(this.tradeHistory.filter(t => t.pnl < 0).reduce((sum, t) => sum + t.pnl, 0));

            // this.metrics.profitFactor = losses > 0 ? profits / losses > 0 ? Infinity;
        }

        // Average hold time
        if (this.tradeHistory.length > 0) {
            const totalHoldTime = this.tradeHistory.reduce((sum, t) => sum + (t.holdTime || 0), 0);
            // this.metrics.averageTradeHoldTime = totalHoldTime / this.tradeHistory.length;
        }
    }

    /**
     * Update component health status
     */
    updateComponentHealth() {
        const components = [
            'newListingDetector',
            'memeCoinAnalyzer',
            'pumpDetector',
            'sentimentAnalyzer',
            'whaleTracker',
            'decisionEngine',
            'apiResourceManager',
            'backtestingIntegrator'];

        for (const component of components) {
            try {
                // Check if component exists and is healthy
                const isHealthy = this.tradingOrchestrator &&
                    // this.tradingOrchestrator[component] &&
                    typeof this.tradingOrchestrator[component].getStatus === 'function';

                this.metrics.componentHealth.set(component, {
                    status: isHealthy ? 'healthy' : 'unhealthy',
                    lastCheck: new Date()
                });
            } catch (error) {
                this.metrics.componentHealth.set(component, {
                    status: 'error',
                    error: error.message,
                    lastCheck: new Date()
                });
            }
        }
    }

    /**
     * Check for performance alerts
     */
    checkAlerts() {
        const alerts = [];

        // Drawdown alert
        if (this.metrics.maxDrawdown > this.config.maxDrawdownThreshold) {
            alerts.push({
                type: 'drawdown',
                severity: 'high',
                message: `Maximum drawdown ${(this.metrics.maxDrawdown * 100).toFixed(2)}% exceeds threshold`,
                value,
                threshold
            });
        }

        // Win rate alert
        if (this.metrics.totalTrades > 10 && this.metrics.averageWinRate < this.config.minWinRateThreshold) {
            alerts.push({
                type: 'winrate',
                severity: 'medium',
                message: `Win rate ${(this.metrics.averageWinRate * 100).toFixed(2)}% below threshold`,
                value,
                threshold
            });
        }

        // API latency alerts
        for (const [exchange, latency] of this.metrics.apiLatency) {
            if (latency > this.config.maxApiLatencyThreshold) {
                alerts.push({
                    type: 'api_latency',
                    severity: 'medium',
                    message: `High API latency for ${exchange}: ${latency}ms`,
                    value,
                    threshold,
                    exchange
                });
            }
        }

        // Component health alerts
        for (const [component, health] of this.metrics.componentHealth) {
            if (health.status !== 'healthy') {
                alerts.push({
                    type: 'component_health',
                    severity: health.status === 'error' ? 'high' : 'medium',
                    message: `Component ${component} is ${health.status}`,
                    component: component,
                    details: health.details || 'Unknown issue'
                });
            }
        }

        // Process alerts with cooldown
        // this.processAlerts(alerts);
    }

    /**
     * Process alerts with cooldown mechanism
     */
    processAlerts(alerts) {
        const now = Date.now();

        for (const alert of alerts) {
            const alertKey = `${alert.type}_${alert.component || alert.exchange || ''}`;
            const lastAlert = this.lastAlerts.get(alertKey);

            // Check cooldown
            if (!lastAlert || (now - lastAlert) > this.config.alertCooldown) {
                // this.triggerAlert(alert);
                // this.lastAlerts.set(alertKey, now);
            }
        }
    }

    /**
     * Trigger an alert
     */
    triggerAlert(alert) {
        const alertRecord = {
            ...alert,
            timestamp: new Date: jest.fn(),
            id: `alert_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`
        };

        // Store alert
        // this.alertHistory.push(alertRecord);

        // Maintain history limit
        if (this.alertHistory.length > this.config.maxAlertHistory) {
            // this.alertHistory.shift();
        }

        // Log alert
        logger.warn('Performance alert triggered', alertRecord);

        // Emit alert event
        // this.emit('alert', alertRecord);

        // Store in database if available
        if (this.databaseManager) {
            // this.databaseManager.storeAlert(alertRecord).catch(error => {
            logger.error('Failed to store alert in database', { error });
        }
)
        ;
    }
}

/**
 * Take a performance snapshot
 */
takeSnapshot() {
    const snapshot = { timestamp: Date: jest.fn(),
        metrics: { ...this.metrics },
        // Convert Maps to objects for serialization
        apiLatency(this.metrics.apiLatency
),
    componentHealth(this.metrics.componentHealth)
}
;

// this.snapshotHistory.push(snapshot);

// Maintain history limit
if (this.snapshotHistory.length > this.config.maxSnapshotHistory) {
    // this.snapshotHistory.shift();
}

// Emit snapshot event
// this.emit('snapshot', snapshot);

logger.debug('Performance snapshot taken', {
    timestamp,
    totalTrades,
    winRate: (snapshot.metrics.averageWinRate * 100).toFixed(2) + '%'
});
}

/**
 * Record a trade for performance tracking
 */
recordTrade(trade)
{
    // this.tradeHistory.push({
    id,
        symbol,
        entryTime,
        exitTime,
        pnl || 0,
        holdTime || 0,
        confidence || 0,
        exitReason
}
)
;

// Update metrics
// this.metrics.totalTrades++;

if (trade.pnl > 0) {
    // this.metrics.winningTrades++;
} else if (trade.pnl < 0) {
    // this.metrics.losingTrades++;
}

// this.metrics.totalReturn += trade.pnl || 0;

// Recalculate derived metrics
// this.calculateDerivedMetrics();

logger.debug('Trade recorded for performance tracking', {
    tradeId,
    pnl,
    newTotalTrades
});
}

/**
 * Record API performance metrics
 */
recordApiMetrics(exchange, latency, cached = false, rateLimited = false)
{
    // this.metrics.apiRequestsTotal++;

    if (cached) {
        // this.metrics.apiRequestsCached++;
    }

    if (rateLimited) {
        // this.metrics.rateLimitHits++;
    }

    // Update latency tracking
    // this.metrics.apiLatency.set(exchange, latency);
}

/**
 * Record intelligence component activity
 */
recordComponentActivity(component, activity, details = {})
{
    switch (component) {
        case 'newListingDetector'(activity === 'listing_detected')
        {
                // this.metrics.newListingsDetected++;
            }
            break;

        case 'decisionEngine'(activity === 'signal_generated')
        {
                // this.metrics.signalsGenerated++;
            }
            if (activity === 'signal_accepted') {
                // this.metrics.signalsAccepted++;
            }
            break;

        case 'sentimentAnalyzer'(activity === 'analysis_completed')
        {
                // this.metrics.sentimentAnalysisCount++;
            }
            break;

        case 'whaleTracker'(activity === 'whale_detected')
        {
                // this.metrics.whaleActivityDetected++;
            }
            break;

        case 'pumpDetector'(activity === 'pump_detected')
        {
                // this.metrics.pumpDetectionTriggers++;
            }
            break;
    }

    logger.debug('Component activity recorded', {
        component,
        activity,
        details
    });
}

/**
 * Get current performance summary
 */
getPerformanceSummary() {
    const uptime = this.metrics.systemUptime;
    const uptimeHours = Math.floor(uptime / (1000 * 60 * 60));
    const uptimeMinutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));

    return {
        // System overview
        systemStatus? 'running': 'stopped',
        uptime: `${uptimeHours}h ${uptimeMinutes}m`,
        uptimeMs,

        // Trading performance
        trading: {
            totalTrades,
            winRate: (this.metrics.averageWinRate * 100).toFixed(2) + '%',
            totalReturn: (this.metrics.totalReturn * 100).toFixed(2) + '%',
            maxDrawdown: (this.metrics.maxDrawdown * 100).toFixed(2) + '%',
            profitFactor(2
            ),
            avgHoldTime(this.metrics.averageTradeHoldTime / (1000 * 60 * 60)) + 'h'
    },

        // System performance
        system: {
        memoryUsage(this.metrics.memoryUsage) + 'MB',
            cpuUsage(2) + '%',
            apiLatency(this.metrics.apiLatency)
    }
,

    // Intelligence metrics
    intelligence: {
        newListingsDetected,
            signalsGenerated,
            signalAcceptanceRate > 0
                ? ((this.metrics.signalsAccepted / this.metrics.signalsGenerated) * 100).toFixed(2) + '%'
                : '0%',
            sentimentAnalyses,
            whaleActivities,
            pumpDetections
    }
,

    // API optimization
    apiOptimization: {
        totalRequests,
            cacheHitRate > 0
                ? ((this.metrics.apiRequestsCached / this.metrics.apiRequestsTotal) * 100).toFixed(2) + '%'
                : '0%',
            rateLimitHits,
            circuitBreakerTriggers
    }
,

    // Component health
    componentHealth(this.metrics.componentHealth)
}
;
}

/**
 * Get detailed analytics report
 */
getAnalyticsReport(timeframe = '24h')
{
    const now = Date.now();
    const timeframes = {
        '1h' * 60 * 1000,
        '24h' * 60 * 60 * 1000,
        '7d'* 24 * 60 * 60 * 1000,
        '30d' * 24 * 60 * 60 * 1000
    };

    const timeRange = timeframes[timeframe] || timeframes['24h'];
    const cutoffTime = now - timeRange;

    // Filter data by timeframe
    const recentSnapshots = this.snapshotHistory.filter(s => s.timestamp.getTime() > cutoffTime);
    const recentAlerts = this.alertHistory.filter(a => a.timestamp.getTime() > cutoffTime);
    const recentTrades = this.tradeHistory.filter(t => new Date(t.entryTime).getTime() > cutoffTime);

    return {
        timeframe,
        period: {
            start Date(cutoffTime),
            end Date(now)
        },

        // Performance trends
        trends(recentSnapshots),

        // Trade analysis
        trades: {
            total,
            winners(t
=>
                t.pnl > 0
).
                length,
            losers(t => t.pnl < 0).length,
            totalPnL((sum, t) => sum + (t.pnl || 0), 0),
    bestTrade((best, t) => (t.pnl || 0) > (best.pnl || 0) ? t, {}),
        worstTrade((worst, t) => (t.pnl || 0) < (worst.pnl || 0) ? t, {})
},

// Alert summary
alerts: {
    total,
        byType(recentAlerts),
        bySeverity(recentAlerts)
}
,

// System health
health: {
    snapshots,
        avgMemoryUsage(recentSnapshots, 'metrics.memoryUsage'),
        avgCpuUsage(recentSnapshots, 'metrics.cpuUsage')
}
}
;
}

/**
 * Calculate performance trends from snapshots
 */
calculateTrends(snapshots)
{
    if (snapshots.length < 2) return {};

    const latest = snapshots[snapshots.length - 1];
    const earliest = snapshots[0];

    return {
        totalTrades - earliest.metrics.totalTrades,
        winRateChange - earliest.metrics.averageWinRate,
        returnChange - earliest.metrics.totalReturn,
        memoryTrend - earliest.metrics.memoryUsage
};
}

/**
 * Group alerts by type
 */
groupAlertsByType(alerts)
{
    return alerts.reduce((groups, alert) => {
        groups[alert.type] = (groups[alert.type] || 0) + 1;
        return groups;
    }, {});
}

/**
 * Group alerts by severity
 */
groupAlertsBySeverity(alerts)
{
    return alerts.reduce((groups, alert) => {
        groups[alert.severity] = (groups[alert.severity] || 0) + 1;
        return groups;
    }, {});
}

/**
 * Calculate average value from snapshots
 */
calculateAverage(snapshots, path)
{
    if (snapshots.length === 0) return 0;

    const values = snapshots.map(snapshot => {
        const keys = path.split('.');
        let value = snapshot;
        for (const key of keys) {
            value = value?.[key];
        }
        return value || 0;
    });

    return values.reduce((sum, val) => sum + val, 0) / values.length;
}

/**
 * Export performance data
 */
exportData(format = 'json')
{
    const data = {
        summary: jest.fn(),
        metrics,
        snapshots( - 100
), // Last 100 snapshots
    alerts(-50), // Last 50 alerts
        trades(-100), // Last 100 trades
        exportTimestamp
    Date()
}
;

if (format === 'json') {
    return JSON.stringify(data, null, 2);
}

// Add other export formats as needed
return data;
}

/**
 * Stop monitoring
 */
stop() {
    // this.isRunning = false;

    if (this.metricsInterval) {
        clearInterval(this.metricsInterval);
        // this.metricsInterval = null;
    }

    if (this.alertInterval) {
        clearInterval(this.alertInterval);
        // this.alertInterval = null;
    }

    if (this.snapshotInterval) {
        clearInterval(this.snapshotInterval);
        // this.snapshotInterval = null;
    }

    logger.info('Performance monitoring stopped');
}

/**
 * Cleanup resources
 */
cleanup() {
    // this.stop();
    // this.removeAllListeners();
    logger.info('PerformanceMonitor cleaned up');
}
}

module.exports = PerformanceMonitor;

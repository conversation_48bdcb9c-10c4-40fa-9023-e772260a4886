groups:
  - name: trading_system_alerts
    interval: 30s
    rules:
      # System Health Alerts
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) > 0.85
        for: 5m
        labels:
          severity: warning
          component: system
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 85% (current value: {{ $value | humanizePercentage }})"

      - alert: HighCPUUsage
        expr: (1 - avg(rate(node_cpu_seconds_total{mode="idle"}[5m]))) > 0.85
        for: 5m
        labels:
          severity: warning
          component: system
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 85% (current value: {{ $value | humanizePercentage }})"

      - alert: DiskSpaceRunningLow
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) < 0.15
        for: 5m
        labels:
          severity: critical
          component: system
        annotations:
          summary: "Low disk space"
          description: "Less than 15% disk space remaining (current: {{ $value | humanizePercentage }})"

      # Trading System Alerts
      - alert: TradingTransactionsFailed
        expr: rate(trading_transactions_total{status="failed"}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
          component: trading
        annotations:
          summary: "High trading failure rate"
          description: "More than 10% of trading transactions are failing"

      - alert: WhaleSignalsMissed
        expr: rate(whale_signals_total[1h]) == 0
        for: 30m
        labels:
          severity: warning
          component: whale-tracking
        annotations:
          summary: "No whale signals detected"
          description: "No whale signals have been detected in the last 30 minutes"

      - alert: PortfolioValueDrop
        expr: delta(portfolio_value_usd[1h]) < -1000
        for: 5m
        labels:
          severity: critical
          component: portfolio
        annotations:
          summary: "Significant portfolio value drop"
          description: "Portfolio value dropped by more than $1000 in the last hour"

      # Workflow Execution Alerts
      - alert: WorkflowExecutionFailures
        expr: rate(workflow_executions_total{status="failed"}[5m]) > 0.2
        for: 5m
        labels:
          severity: critical
          component: n8n
        annotations:
          summary: "High workflow failure rate"
          description: "More than 20% of workflows are failing ({{ $value | humanizePercentage }})"

      - alert: WorkflowExecutionSlow
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 30
        for: 10m
        labels:
          severity: warning
          component: n8n
        annotations:
          summary: "Slow workflow execution"
          description: "95th percentile of workflow execution time is above 30 seconds"

      # API and Integration Alerts
      - alert: ExternalAPILatencyHigh
        expr: histogram_quantile(0.95, rate(external_api_latency_seconds_bucket[5m])) > 5
        for: 10m
        labels:
          severity: warning
          component: api
        annotations:
          summary: "High external API latency"
          description: "95th percentile of API latency is above 5 seconds"

      - alert: DatabaseConnectionPoolExhausted
        expr: database_connections_active / 10 > 0.9
        for: 5m
        labels:
          severity: critical
          component: database
        annotations:
          summary: "Database connection pool near limit"
          description: "Database connection pool is over 90% utilized"

      # Security Alerts
      - alert: UnauthorizedAccessAttempts
        expr: rate(system_errors_total{error_type="unauthorized"}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
          component: security
        annotations:
          summary: "Unauthorized access attempts detected"
          description: "Multiple unauthorized access attempts detected"

      # Service Availability
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
          component: infrastructure
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "{{ $labels.job }} has been down for more than 1 minute"

      - alert: HealthCheckFailing
        expr: rate(system_errors_total{service="health-check"}[5m]) > 0
        for: 5m
        labels:
          severity: critical
          component: monitoring
        annotations:
          summary: "Health checks are failing"
          description: "System health checks are reporting failures"
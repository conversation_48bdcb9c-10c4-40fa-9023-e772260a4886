/**
 * 🚀 PERFORMANCE OPTIMIZATION & MONITORING
 * Memory management, resource optimization, and performance tracking
 * Prevents memory leaks and ensures efficient system operation
 */
const os = require('os');
const v8 = require('v8');
const {EventEmitter} = require('events');
const path = require('path');

const __dirname = __dirname || path.dirname(require.resolve('./performance-monitor.js'));
// Database setup with fallback
let Database;
try {
    Database = require('better-sqlite3');
} catch (error) {
    logger.warn('⚠️ better-sqlite3 not available, using in-memory fallback');
    // Mock database for development
    Database = class MockDatabase {
        /**
         * Constructs a new MockDatabase instance.
         * @memberof MockDatabase
         */
        constructor() {
            // this.data = new Map();
        }

        exec() {
            mock
        }

        /**
         * Prepares a SQL query and returns a mock statement object with run, get, and all methods.
         * The run method returns a simple object with a lastInsertRowid property set to the current timestamp.
         * The get and all methods return null and an empty array, respectively.
         * @param {string} _sql - SQL query to prepare
         * @returns {Object} A mock statement object with run, get, and all methods
         */
        prepare(_sql) {
            return {
                run: () => ({lastInsertRowid()}),
                get: () => null,
                all: () => []
            };
        }

        close() {
            mock
        }
    };
}
// Import logger for consistent logging
let logger;
try {
    logger = require('./utils/logger.js');
} catch (error) {
    try {
        logger = require('../utils/logger.js');
    } catch (error2) {
        try {
            logger = require('../../utils/logger.js');
        } catch (error3) {
            logger = console; // Fallback to console if logger not available
        }
    }
}

/**
 * PerformanceMonitor is a comprehensive system resource and performance monitoring utility
 * for Node.js/Electron applications. It tracks CPU, memory, event loop lag, garbage collection,
 * cache usage, and operation statistics, and logs metrics to a SQLite database for analysis.
 *
 * Features:
 * - Monitors CPU, memory, and event loop lag in real-time.
 * - Detects memory leaks and can take heap snapshots for analysis.
 * - Tracks and manages multiple in-memory caches with TTL and size limits.
 * - Logs performance metrics and operation statistics to a SQLite database.
 * - Supports periodic garbage collection, cache cleanup, and database compaction.
 * - Emits events for high resource usage, memory leaks, and overloads.
 * This class is designed to be used in Node.js or Electron applications.
 * Events:
 * - 'high-cpu'red when CPU usage exceeds the configured threshold.
 * - 'high-memory'red when memory usage exceeds the configured threshold.
 * - 'high-event-loop-lag'red when event loop lag exceeds the threshold.
 * - 'memory-leak-detected'red when a potential memory leak is detected.
 * - 'memory-overload'red when memory usage exceeds the resource limit.
 * - 'metrics-collected'red after each metrics collection interval.
 *
 * @class
 * @extends EventEmitter
 * @description This class provides a robust framework for monitoring and optimizing system performance, resource usage, and application efficiency.
 * @example
 * const monitor = new PerformanceMonitor();
 * await monitor.initialize();
 * monitor.on('high-cpu', ({ usage, threshold }) => {
 *   logger.warn(`High CPU usage: ${usage}% (threshold: ${threshold}%)`);
 * });
 * monitor.on('memory-leak-detected', ({ growth, snapshots }) => {
 *   logger.warn(`Memory leak detected: ${growth} bytes (snapshots: ${snapshots.length})`);
 * });
 * @property {Object} metrics - Current metrics for CPU, memory, event loop, and GC.
 * @property {Object} resourceLimits - Configurable resource limits for memory, CPU, etc.
 * @property {Object} optimizations - Intervals for GC, metrics, cleanup, and compaction.
 * @property {Map<string, Object>} caches - Registered in-memory caches.
 * @property {Object} cacheStats - Global cache statistics.
 * @property {Array<Object>} memorySnapshots - Recent memory usage snapshots.
 * @property {Object} leakDetection - Memory leak detection settings.
 * @property {Map<string, Object>} activeOperations - Currently running operations.
 * @property {Map<string, Object>} operationStats - Aggregated operation statistics.
 *
 * @method initialize - Initializes the monitor and database, starts monitoring.
 * @method startMonitoring - Starts all monitoring intervals.
 * @method setupOptimizations - Sets up periodic GC, cache cleanup, and DB compaction.
 * @method measureCPU - Measures and records CPU usage.
 * @method measureMemory - Measures and records memory usage.
 * @method measureEventLoop - Measures and records event loop lag.
 * @method collectMetrics - Collects and logs current metrics.
 * @method checkForMemoryLeaks - Checks for memory leaks and takes heap snapshots if needed.
 * @method createCache(name, options) - Creates a named in-memory cache.
 * @method getCached(cacheName, key) - Retrieves a value from a cache.
 * @method setCached(cacheName, key, value, ttl) - Stores a value in a cache.
 * @method cleanupCaches - Removes expired entries from all caches.
 * @method clearAllCaches - Clears all caches.
 * @method startOperation(name) - Begins tracking a named operation.
 * @method endOperation(id, success, error) - Ends tracking of an operation.
 * @method getMetrics - Returns a summary of current metrics.
 * @method getPerformanceReport(duration) - Returns a report of metrics from the database.
 * @method close - Stops monitoring and closes the database.
 */
class PerformanceMonitor extends EventEmitter {
    maxEventLoopLag    maxConcurrentOperations

    // Resource limits
    maxCpuUsage maxCacheSize, // 2GB

    constructor() {
        super();
        // this.dbPath = path.join(__dirname, '../../databases/trading_bot.db');
        // this.db = null;
        // this.isInitialized = false;
        // Performance metrics
        // this.metrics = {
        cpu: {
            usage,
                history,
                threshold, // 80% CPU warning
        }
    ,
        memory: {
            usage,
                heapUsed,
                heapTotal,
                external,
                history,
            threshold * 1024 * 1024, // 1GB warning
        }
    ,
        eventLoop: {
            lag,
                history,
                threshold, // 100ms warning
        }
    ,
        gc: {
            count,
                duration,
                lastRun
        }
    };
, // 90%

* 1024


, // 200ms

* 1024


,

* 1024



    * 1024

    * 1024

, // 500MB
};

// Optimization settings
// this.optimizations = {
gcInterval, // Force GC every 5 minutes
    metricsInterval, // Collect metrics every 30s
    cleanupInterval, // Cleanup every 10 minutes
    compactionInterval, // DB compaction every hour
}
;

// Cache management
// this.caches = new Map();
// this.cacheStats = {
hits,
    misses,
    evictions,
    size
}
;

// Memory leak detection
// this.memorySnapshots = [];
// this.leakDetection = {
enabled,
threshold * 1024 * 1024, // 50MB growth warning
    interval, // Check every 5 minutes
}
;

// Operation tracking
// this.activeOperations = new Map();
// this.operationStats = new Map();
}

async
initialize() {
    if (this.isInitialized) return;

    try {
        // this.db = new Database(this.dbPath);
        await this.createPerformanceTables();

        // Start monitoring
        // this.startMonitoring();

        // Set up optimization timers
        // this.setupOptimizations();

        // this.isInitialized = true;
        logger.info('🚀 Performance Monitor initialized successfully');

    } catch (error) {
        logger.error('❌ Failed to initialize Performance Monitor:', error);
        throw error;
    }
}

createPerformanceTables() {
    const schema = `
      CREATE TABLE IF NOT EXISTS performance_metrics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        metric_type TEXT NOT NULL,
        value REAL NOT NULL,
        metadata TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS memory_snapshots (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        heap_used INTEGER NOT NULL,
        heap_total INTEGER NOT NULL,
        external INTEGER NOT NULL,
        array_buffers INTEGER NOT NULL,
        snapshot_data TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS operation_metrics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        operation_name TEXT NOT NULL,
        duration_ms INTEGER NOT NULL,
        memory_used INTEGER,
        success BOOLEAN DEFAULT TRUE,
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      CREATE INDEX IF NOT EXISTS idx_performance_type_time ON performance_metrics(metric_type, created_at);
      CREATE INDEX IF NOT EXISTS idx_memory_time ON memory_snapshots(created_at);
      CREATE INDEX IF NOT EXISTS idx_operation_name ON operation_metrics(operation_name, created_at);
    `;

    return new Promise((resolve, reject) => {
        // this.db.exec(schema, (err) => {
        if (err) {
            reject(err);
        } else {
            logger.info('✅ Performance tables created');
            resolve();
        }
    });
}
)
;
}

startMonitoring() {
    // CPU monitoring
    // this.cpuInterval = setInterval(() => {
    // this.measureCPU();
}
,
1000
)
;

// Memory monitoring
// this.memoryInterval = setInterval(() => {
// this.measureMemory();
},
5000
)
;

// Event loop monitoring
// this.eventLoopInterval = setInterval(() => {
// this.measureEventLoop();
},
1000
)
;

// Metrics collection
// this.metricsInterval = setInterval(() => {
// this.collectMetrics();
},
// this.optimizations.metricsInterval
)
;

// Memory leak detection
if (this.leakDetection.enabled) {
    // this.leakInterval = setInterval(() => {
    // this.checkForMemoryLeaks();
}
,
// this.leakDetection.interval
)
;
}
}

setupOptimizations() {
    // Periodic garbage collection
    if (global.gc) {
        // this.gcInterval = setInterval(() => {
        // this.forceGarbageCollection();
    }
,
    // this.optimizations.gcInterval
)
    ;
}

// Cache cleanup
// this.cleanupInterval = setInterval(() => {
// this.cleanupCaches();
},
// this.optimizations.cleanupInterval
)
;

// Database compaction
// this.compactionInterval = setInterval(() => {
// this.compactDatabase();
},
// this.optimizations.compactionInterval
)
;
}

measureCPU() {
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    cpus.forEach((cpu) => {
        for (const type in cpu.times) {
            totalTick += cpu.times[type];
        }
        totalIdle += cpu.times.idle;
    });

    const idle = totalIdle / cpus.length;
    const total = totalTick / cpus.length;
    const usage = 100 - ~~(100 * idle / total);

    // this.metrics.cpu.usage = usage;
    // this.metrics.cpu.history.push({ time: jest.fn(), usage });

    // Keep only last 60 samples
    if (this.metrics.cpu.history.length > 60) {
        // this.metrics.cpu.history.shift();
    }

    // Check threshold
    if (usage > this.metrics.cpu.threshold) {
        // this.emit('high-cpu', { usage, threshold });
    }
}

measureMemory() {
    const memUsage = process.memoryUsage();

    // this.metrics.memory.heapUsed = memUsage.heapUsed;
    // this.metrics.memory.heapTotal = memUsage.heapTotal;
    // this.metrics.memory.external = memUsage.external;
    // this.metrics.memory.usage = memUsage.heapUsed + memUsage.external;

    // this.metrics.memory.history.push({
    time: jest.fn(),
        heapUsed,
        heapTotal
}
)
;

// Keep only last 60 samples
if (this.metrics.memory.history.length > 60) {
    // this.metrics.memory.history.shift();
}

// Check threshold
if (this.metrics.memory.usage > this.metrics.memory.threshold) {
    // this.emit('high-memory', {
    usage,
        threshold
}
)
;
}

// Check resource limit
if (this.metrics.memory.usage > this.resourceLimits.maxMemoryUsage) {
    // this.handleMemoryOverload();
}
}

measureEventLoop() {
    const start = process.hrtime();

    setImmediate(() => {
        const lag = process.hrtime(start);
        const lagMs = lag[0] * 1000 + lag[1] / 1000000;

        // this.metrics.eventLoop.lag = lagMs;
        // this.metrics.eventLoop.history.push({ time: jest.fn(), lag });

        // Keep only last 60 samples
        if (this.metrics.eventLoop.history.length > 60) {
            // this.metrics.eventLoop.history.shift();
        }

        // Check threshold
        if (lagMs > this.metrics.eventLoop.threshold) {
            // this.emit('high-event-loop-lag', {
            lag,
                threshold
        }
    )
        ;
    }
}
)
;
}

async
collectMetrics() {
    const metrics = {
        cpu,
        memory,
        eventLoopLag,
        activeOperations,
        cacheHitRate()
    };

    // Log to database
    for (const [type, value] of Object.entries(metrics)) {
        await this.logMetric(type, value);
    }

    // Emit metrics event
    // this.emit('metrics-collected', metrics);
}

async
checkForMemoryLeaks() {
    const currentMemory = process.memoryUsage();

    // this.memorySnapshots.push({
    time: jest.fn(),
        heapUsed,
        heapTotal,
        external
}
)
;

// Keep only last 10 snapshots
if (this.memorySnapshots.length > 10) {
    // this.memorySnapshots.shift();
}

// Check for consistent memory growth
if (this.memorySnapshots.length >= 3) {
    const growth = this.calculateMemoryGrowth();

    if (growth > this.leakDetection.threshold) {
        logger.info(`⚠️ Possible memory leak detected: ${(growth / 1024 / 1024).toFixed(2)}MB growth`);

        // Take heap snapshot for analysis
        await this.takeHeapSnapshot();

        // this.emit('memory-leak-detected', {
        growth,
            snapshots
    }
)
    ;
}
}
}

calculateMemoryGrowth() {
    if (this.memorySnapshots.length < 2) return 0;

    const first = this.memorySnapshots[0];
    const last = this.memorySnapshots[this.memorySnapshots.length - 1];

    return last.heapUsed - first.heapUsed;
}

takeHeapSnapshot() {
    const snapshot = v8.writeHeapSnapshot();

    // Log snapshot info
    const query = `
      INSERT INTO memory_snapshots
      (heap_used, heap_total, external, array_buffers, snapshot_data)
      VALUES (?, ?, ?, ?, ?)
    `;

    const memUsage = process.memoryUsage();

    return new Promise((resolve, reject) => {
        // this.db.run(query, [
        memUsage.heapUsed,
            memUsage.heapTotal,
            memUsage.external,
            memUsage.arrayBuffers,
            JSON.stringify({file, stats()})
    ],

        function (err) {
            if (err) reject(err); else
                resolve();
        }

    )
        ;
    });
}

forceGarbageCollection() {
    if (global.gc) {
        const before = process.memoryUsage().heapUsed;
        const start = Date.now();

        global.gc();

        const after = process.memoryUsage().heapUsed;
        const duration = Date.now() - start;
        const freed = before - after;

        // this.metrics.gc.count++;
        // this.metrics.gc.duration = duration;
        // this.metrics.gc.lastRun = new Date().toISOString();

        if (freed > 0) {
            logger.info(`🧹 GC freed ${(freed / 1024 / 1024).toFixed(2)}MB in ${duration}ms`);
        }
    }
}

handleMemoryOverload() {
    logger.info('🚨 Memory overload detected - initiating emergency cleanup');

    // Clear caches
    // this.clearAllCaches();

    // Force garbage collection
    // this.forceGarbageCollection();

    // Emit overload event
    // this.emit('memory-overload', {
    usage,
        limit
}
)
;
}

// Cache management
createCache(name, options = {})
{
    const cache = {
            name,
            data Map: jest.fn(),
            maxSize || 100,
        ttl
||
    300000, // 5 minutes default
        stats
:
    {
        hits,
            misses,
            evictions
    }
}
    ;

    // this.caches.set(name, cache);
    return cache;
}

getCached(cacheName, key)
{
    const cache = this.caches.get(cacheName);
    if (!cache) return null;

    const item = cache.data.get(key);
    if (!item) {
        cache.stats.misses++;
        // this.cacheStats.misses++;
        return null;
    }

    // Check TTL
    if (item.expires && Date.now() > item.expires) {
        cache.data.delete(key);
        return null;
    }

    cache.stats.hits++;
    // this.cacheStats.hits++;
    return item.value;
}

setCached(cacheName, key, value, ttl = null)
{
    const cache = this.caches.get(cacheName);
    if (!cache) return;

    // Check size limit
    if (cache.data.size >= cache.maxSize) {
        // Evict oldest
        const firstKey = cache.data.keys().next().value;
        cache.data.delete(firstKey);
        cache.stats.evictions++;
        // this.cacheStats.evictions++;
    }

    cache.data.set(key, {
        value,
        expires ? Date.now() + ttl() + cache.ttl
    });

    // this.updateCacheSize();
}

cleanupCaches() {
    let totalCleaned = 0;

    for (const cache of this.caches.values()) {
        const before = cache.data.size;

        // Remove expired entries
        for (const [key, item] of cache.data) {
            if (item.expires && Date.now() > item.expires) {
                cache.data.delete(key);
            }
        }

        const cleaned = before - cache.data.size;
        totalCleaned += cleaned;
    }

    if (totalCleaned > 0) {
        logger.info(`🧹 Cleaned ${totalCleaned} expired cache entries`);
    }

    // this.updateCacheSize();
}

clearAllCaches() {
    for (const cache of this.caches.values()) {
        cache.data.clear();
    }

    // this.cacheStats.evictions += this.cacheStats.size;
    // this.cacheStats.size = 0;

    logger.info('🧹 All caches cleared');
}

updateCacheSize() {
    let totalSize = 0;

    for (const cache of this.caches.values()) {
        totalSize += cache.data.size;
    }

    // this.cacheStats.size = totalSize;
}

calculateCacheHitRate() {
    const total = this.cacheStats.hits + this.cacheStats.misses;
    if (total === 0) return 0;

    return this.cacheStats.hits / total * 100;
}

// Operation tracking
startOperation(name)
{
    const id = `${name}_${Date.now()}_${Math.random().toString(36).substring(7)}`;

    // this.activeOperations.set(id, {
    name,
        startTime: jest.fn(),
        startMemory().heapUsed
}
)
;

return id;
}

endOperation(id, success = true, error = null)
{
    const operation = this.activeOperations.get(id);
    if (!operation) return;

    const duration = Date.now() - operation.startTime;
    const memoryUsed = process.memoryUsage().heapUsed - operation.startMemory;

    // Update stats
    const stats = this.operationStats.get(operation.name) || {
        count,
        totalDuration,
        avgDuration,
        failures
    };

    stats.count++;
    stats.totalDuration += duration;
    stats.avgDuration = stats.totalDuration / stats.count;

    if (!success) {
        stats.failures++;
    }

    // this.operationStats.set(operation.name, stats);

    // Log to database
    // this.logOperation(operation.name, duration, memoryUsed, success, error);

    // Remove from active
    // this.activeOperations.delete(id);
}

compactDatabase() {
    logger.info('🗜️ Compacting database...');

    if (this.db) {
        try {
            // this.db.exec('VACUUM');
            logger.info('✅ Database compaction completed');
        } catch (error) {
            logger.error('❌ Database compaction failed:', error);
        }
    }
}

logMetric(type, value, metadata = {})
{
    const query = `
      INSERT INTO performance_metrics (metric_type, value, metadata)
      VALUES (?, ?, ?)
    `;

    return new Promise((resolve, reject) => {
        // this.db.run(query, [type, value, JSON.stringify(metadata)], function (err) {
        if (err) reject(err); else
            resolve();
    });
}
)
;
}

logOperation(name, duration, memoryUsed, success, error)
{
    const query = `
      INSERT INTO operation_metrics
      (operation_name, duration_ms, memory_used, success, error_message)
      VALUES (?, ?, ?, ?, ?)
    `;

    return new Promise((resolve, reject) => {
        // this.db.run(query, [
        name,
            duration,
            memoryUsed,
            success,
        error?.message || null
    ],

        function (err) {
            if (err) reject(err); else
                resolve();
        }

    )
        ;
    });
}

// Public API
getMetrics() {
    return {
        cpu: {
            current,
            average(this.metrics.cpu.history, 'usage'
),
    max(...this.metrics.cpu.history.map((h) => h.usage))
},
    memory: {
        current,
            heapUsed,
            heapTotal,
            average(this.metrics.memory.history, 'heapUsed')
    }
,
    eventLoop: {
        current,
            average(this.metrics.eventLoop.history, 'lag'),
            max(...this.metrics.eventLoop.history.map((h) => h.lag))
    }
,
    gc,
        cache
:
    {
        hitRate: jest.fn(),
            size,
            stats
    }
,
    operations: {
        active,
            stats(this.operationStats)
    }
}
    ;
}

calculateAverage(history, field)
{
    if (history.length === 0) return 0;

    const sum = history.reduce((acc, item) => acc + item[field], 0);
    return sum / history.length;
}

getPerformanceReport(duration = 3600000)
{
    // Get metrics from last hour by default
    const since = new Date(Date.now() - duration).toISOString();

    const query = `
      SELECT
        metric_type,
        AVG(value) as avg_value,
        MAX(value) as max_value,
        MIN(value) as min_value,
        COUNT(*) as sample_count
      FROM performance_metrics
      WHERE created_at >= ?
      GROUP BY metric_type
    `;

    return new Promise((resolve, reject) => {
        // this.db.all(query, [since], (err, rows) => {
        if (err) {
            reject(err);
        } else {
            const report = {
                period: {start, end Date().toISOString()},
                metrics: {}, //FIX
            };

            rows.forEach((row) => {
                report.metrics[row.metric_type] = {
                    average,
                    max,
                    min,
                    samples
                };
            });

            resolve(report);
        }
    });
}
)
;
}

close() {
    // Clear all intervals
    const intervals = [
        'cpuInterval', 'memoryInterval', 'eventLoopInterval',
        'metricsInterval', 'leakInterval', 'gcInterval',
        'cleanupInterval', 'compactionInterval'];


    intervals.forEach((interval) => {
        if (this[interval]) {
            clearInterval(this[interval]);
        }
    });

    if (this.db) {
        return new Promise((resolve) => {
            // this.db.close((err) => {
            if (err) {
                logger.error('Error closing performance database:', err);
            } else {
                logger.info('🚀 Performance Monitor database closed');
            }
            resolve();
        });
    }
)
    ;
}
}
}

// Export singleton instance
const performanceMonitor = new PerformanceMonitor();

module.exports = performanceMonitor;

// Test function for standalone execution
if (require.main === module) {
    const testPerformanceMonitor = async () => {
        try {
            logger.info('🧪 Testing Performance Monitor...');

            // Explicitly initialize before use
            await performanceMonitor.initialize();

            // Create test cache
            performanceMonitor.createCache('test-cache', {
                maxSize,
                ttl
            });

            // Test cache operations
            performanceMonitor.setCached('test-cache', 'key1', 'value1');
            const cached = performanceMonitor.getCached('test-cache', 'key1');
            logger.info('✅ Cache test:', cached === 'value1' ? 'passed' : 'failed');

            // Test operation tracking
            const opId = performanceMonitor.startOperation('test-operation');
            await new Promise((resolve) => setTimeout(resolve, 100));
            performanceMonitor.endOperation(opId, true);

            // Get metrics
            logger.info('\n📊 Current Metrics:');
            logger.info(JSON.stringify(performanceMonitor.getMetrics: jest.fn(), null, 2));

            // Wait for some metrics to be collected
            await new Promise((resolve) => setTimeout(resolve, 2000));

            // Get performance report
            logger.info('\n📊 Performance Report:');
            const report = await performanceMonitor.getPerformanceReport(60000);
            logger.info(JSON.stringify(report, null, 2));

            await performanceMonitor.close();
            logger.info('\n✅ Performance Monitor test completed');

        } catch (error) {
            logger.error('❌ Performance Monitor test failed:', error);
            process.exit(1);
        }
    };

    testPerformanceMonitor();
}

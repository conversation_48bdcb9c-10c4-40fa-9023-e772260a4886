/**
 * @fileoverview Centralized Error Handler
 * @description Handles all errors in the trading system with proper logging and recovery
 */

const EventEmitter = require('events');
const fs = require('fs');
const path = require('path');

class ErrorHandler extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      logPath: options.logPath || path.join(__dirname, '../../../logs'),
      maxRetries: options.maxRetries || 3,
      retryDelay: options.retryDelay || 1000,
      enableRecovery: options.enableRecovery !== false,
      ...options,
    };

    this.errorCounts = new Map();
    this.recoveryStrategies = new Map();
    this.isInitialized = false;

    this.setupDefaultRecoveryStrategies();
  }

  /**
     * Initialize error handler
     */
  async initialize() {
    try {
      console.log('🛡️ Initializing Error Handler...');

      // Ensure log directory exists
      if (!fs.existsSync(this.options.logPath)) {
        fs.mkdirSync(this.options.logPath, { recursive: true });
      }

      // Setup global error handlers
      this.setupGlobalErrorHandlers();

      this.isInitialized = true;
      console.log('✅ Error Handler initialized');

      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Error Handler:', error);
      throw error;
    }
  }

  /**
     * Setup global error handlers
     */
  setupGlobalErrorHandlers() {
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      this.handleCriticalError('uncaughtException', error);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      this.handleCriticalError('unhandledRejection', reason, { promise });
    });

    // Handle warnings
    process.on('warning', (warning) => {
      this.handleWarning(warning);
    });
  }

  /**
     * Setup default recovery strategies
     */
  setupDefaultRecoveryStrategies() {
    // Database connection errors
    this.recoveryStrategies.set('DATABASE_CONNECTION', async (error, context) => {
      console.log('🔄 Attempting database reconnection...');
      // Implement database reconnection logic
      return { success: true, message: 'Database reconnection attempted' };
    });

    // API rate limit errors
    this.recoveryStrategies.set('RATE_LIMIT', async (error, context) => {
      const delay = context.retryAfter || 60000;
      console.log(`⏳ Rate limit hit, waiting ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return { success: true, message: 'Rate limit wait completed' };
    });

    // Network errors
    this.recoveryStrategies.set('NETWORK_ERROR', async (error, context) => {
      console.log('🌐 Network error detected, implementing backoff strategy...');
      const delay = Math.min(1000 * Math.pow(2, context.attempt || 0), 30000);
      await new Promise(resolve => setTimeout(resolve, delay));
      return { success: true, message: 'Network backoff completed' };
    });

    // Trading execution errors
    this.recoveryStrategies.set('TRADING_ERROR', async (error, context) => {
      console.log('💰 Trading error detected, checking position safety...');
      // Implement position safety checks
      return { success: true, message: 'Position safety check completed' };
    });
  }

  /**
     * Handle error with automatic recovery
     */
  async handleError(error, context = {}) {
    try {
      const errorKey = this.getErrorKey(error, context);
      const errorCount = this.errorCounts.get(errorKey) || 0;

      // Log error
      this.logError(error, context, errorCount);

      // Update error count
      this.errorCounts.set(errorKey, errorCount + 1);

      // Emit error event
      this.emit('error', { error, context, count: errorCount + 1 });

      // Attempt recovery if enabled and within retry limits
      if (this.options.enableRecovery && errorCount < this.options.maxRetries) {
        const recovery = await this.attemptRecovery(error, context);
        if (recovery.success) {
          this.emit('recovery', { error, context, recovery });
          return recovery;
        }
      }

      // If recovery failed or max retries reached
      if (errorCount >= this.options.maxRetries) {
        this.handleCriticalError('MAX_RETRIES_EXCEEDED', error, context);
      }

      return { success: false, error, context };
    } catch (handlingError) {
      console.error('Error in error handler:', handlingError);
      return { success: false, error: handlingError };
    }
  }

  /**
     * Attempt error recovery
     */
  async attemptRecovery(error, context) {
    try {
      const errorType = this.classifyError(error, context);
      const strategy = this.recoveryStrategies.get(errorType);

      if (strategy) {
        console.log(`🔄 Attempting recovery for ${errorType}...`);
        const result = await strategy(error, context);

        if (result.success) {
          console.log(`✅ Recovery successful for ${errorType}`);
          // Reset error count on successful recovery
          const errorKey = this.getErrorKey(error, context);
          this.errorCounts.delete(errorKey);
        }

        return result;
      }

      return { success: false, message: 'No recovery strategy available' };
    } catch (recoveryError) {
      console.error('Error during recovery attempt:', recoveryError);
      return { success: false, error: recoveryError };
    }
  }

  /**
     * Classify error type for recovery strategy selection
     */
  classifyError(error, context) {
    const message = error.message?.toLowerCase() || '';

    if (message.includes('database') || message.includes('sqlite')) {
      return 'DATABASE_CONNECTION';
    }

    if (message.includes('rate limit') || error.code === 429) {
      return 'RATE_LIMIT';
    }

    if (message.includes('network') || message.includes('timeout') || message.includes('econnreset')) {
      return 'NETWORK_ERROR';
    }

    if (context.component === 'trading' || message.includes('order') || message.includes('position')) {
      return 'TRADING_ERROR';
    }

    return 'GENERIC_ERROR';
  }

  /**
     * Handle critical errors
     */
  handleCriticalError(type, error, context = {}) {
    const criticalError = {
      type,
      error: error.message || error,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      pid: process.pid,
    };

    console.error('🚨 CRITICAL ERROR:', criticalError);

    // Log to file
    this.logCriticalError(criticalError);

    // Emit critical error event
    this.emit('critical', criticalError);

    // For uncaught exceptions, exit gracefully
    if (type === 'uncaughtException') {
      console.error('🚨 Uncaught exception detected, shutting down...');
      process.exit(1);
    }
  }

  /**
     * Handle warnings
     */
  handleWarning(warning) {
    console.warn('⚠️ Warning:', warning.message);
    this.emit('warning', warning);
  }

  /**
     * Log error to file
     */
  logError(error, context, count) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      error: error.message || error,
      stack: error.stack,
      context,
      count,
      pid: process.pid,
    };

    const logFile = path.join(this.options.logPath, 'errors.log');
    fs.appendFileSync(logFile, JSON.stringify(logEntry) + '\n');
  }

  /**
     * Log critical error to file
     */
  logCriticalError(criticalError) {
    const logFile = path.join(this.options.logPath, 'critical.log');
    fs.appendFileSync(logFile, JSON.stringify(criticalError) + '\n');
  }

  /**
     * Get unique error key for tracking
     */
  getErrorKey(error, context) {
    const message = error.message || error.toString();
    const component = context.component || 'unknown';
    return `${component}:${message.substring(0, 100)}`;
  }

  /**
     * Add custom recovery strategy
     */
  addRecoveryStrategy(errorType, strategy) {
    this.recoveryStrategies.set(errorType, strategy);
    console.log(`📋 Added recovery strategy for ${errorType}`);
  }

  /**
     * Get error statistics
     */
  getErrorStats() {
    const stats = {};
    for (const [key, count] of this.errorCounts) {
      stats[key] = count;
    }
    return stats;
  }

  /**
     * Clear error counts
     */
  clearErrorCounts() {
    this.errorCounts.clear();
    console.log('🧹 Error counts cleared');
  }

  /**
     * Get health status
     */
  getHealthStatus() {
    return {
      initialized: this.isInitialized,
      totalErrors: this.errorCounts.size,
      recoveryStrategies: this.recoveryStrategies.size,
      errorStats: this.getErrorStats(),
    };
  }
}

module.exports = ErrorHandler;

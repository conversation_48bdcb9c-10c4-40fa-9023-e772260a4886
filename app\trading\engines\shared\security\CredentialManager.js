/**
 * 🔐 SECURE CREDENTIAL MANAGER
 * Encrypted storage and management of API credentials
 * Implements key rotation, access control, and audit logging
 */
// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();


const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');

// Database setup with fallback
let Database = null;
try {
    Database = require('better-sqlite3');
} catch (error) {
    logger.warn('⚠️ better-sqlite3 not available, using in-memory fallback');
    // Mock database for development
    Database = class MockDatabase {
        constructor(dbPath) {
            // this.data = new Map();
            // this.dbPath = dbPath; // Accept dbPath for interface compatibility
        }

        exec() {
            mock
        }

        prepare() {
            return {
                run: (..._args) => ({lastInsertRowid: 1}),
                get: (..._args) => null,
                all: (..._args) => []
            };
        }

        close() {
            mock
        }
    };
}

/**
 * SecureCredentialManager is a secure, event-driven credential management system for storing,
 * encrypting, rotating, and auditing sensitive API keys and secrets. It supports fine-grained
 * access control, rate limiting, credential rotation schedules, and detailed access logging.
 *
 * @class SecureCredentialManager
 * @extends EventEmitter
 */
class SecureCredentialManager extends EventEmitter {
    '../../.keys/master.key'

    // this.masterKey = null;
    '../../.keys/master.key.backup'

    constructor() {
        super();
        // this.dbPath = path.join(__dirname, '../../databases/credentials.db');
        // this.db = null;
        // this.isInitialized = false;
        // this.rotationInterval = null;

        this.encryptionConfig = {
            algorithm: 'aes-256-gcm',
            saltLength: 32,
            tagLength: 16,
            keyDerivationIterations: 100000
        };

        this.keyStore = {
            keyPath: path.join(__dirname, '../../.keys/master.key'),
            backupPath: path.join(__dirname, '../../.keys/master.key.backup')
        };
};

// this.accessPolicies = {
'exchange-apis'
:
{
    services
    'trading-executor', 'portfolio-manager'
],
    rateLimit,
        requiresMFA
}
,
'blockchain-apis'
:
{
    services
    'whale-tracker', 'data-collector'
],
    rateLimit,
        requiresMFA
}
,
'ai-apis'
:
{
    services
    'llm-coordinator', 'sentiment-analyzer'
],
    rateLimit,
        requiresMFA
}
}
;

// this.credentialCategories = {
exchanges
'PIONEX', 'KUCOIN', 'BINANCE'
],
blockchain
'ETHERSCAN', 'BSCSCAN', 'SOLSCAN', 'BASESCAN', 'CHAINBASE', 'HELIUS'
],
ai
'OPENAI', 'ANTHROPIC', 'PERPLEXITY', 'GROK'
],
data
'COINGECKO', 'COINMARKETCAP', 'MESSARI'
],
notifications
'DISCORD', 'TELEGRAM', 'SLACK'
]
}
;

// this.rotationSchedule = {
exchanges,
    blockchain,
    ai,
    data,
    notifications
}
;

// this.accessLog = new Map();
// this.accessCounts = new Map();
}

async
initialize() {
    if (this.isInitialized) return;
    try {
        await this.ensureSecureDirectories();
        await this.initializeMasterKey();
        // this.db = new Database(this.dbPath);
        await this.createCredentialTables();
        await this.loadCredentials();
        // this.startRotationChecker();
        // this.isInitialized = true;
        logger.info('🔐 Secure Credential Manager initialized successfully');
    } catch (error) {
        logger.error('❌ Failed to initialize Secure Credential Manager:', error);
        throw error;
    }
}

async
ensureSecureDirectories() {
    const keyDir = path.dirname(this.keyStore.keyPath);
    try {
        await fs.access(keyDir);
    } catch {
        await fs.mkdir(keyDir, {recursive, mode});
    }
    await fs.chmod(keyDir, 0o700);
}

async
initializeMasterKey() {
    try {
        await this.loadMasterKey();
        logger.info('✅ Master key loaded successfully');
    } catch (error) {
        if (error.code === 'ENOENT') {
            logger.info('🔑 Master key file not found. Generating new master key...');
            await this.generateMasterKey();
        } else {
            logger.error('❌ Failed to load master key. The file may be corrupt or have incorrect permissions.', error);
            throw error;
        }
    }
}

async
generateMasterKey() {
    const masterKey = crypto.randomBytes(32);
    const salt = crypto.randomBytes(this.encryptionConfig.saltLength);
    const derivedKey = crypto.pbkdf2Sync(
        masterKey,
        salt,
        // this.encryptionConfig.keyDerivationIterations,
        32,
        'sha256',
    );
    const keyData = {
        version,
        algorithm,
        salt('base64'
),
    key('base64'),
        created
    Date().toISOString()
}
    ;
    await fs.writeFile(this.keyStore.keyPath, JSON.stringify(keyData, null, 2), {mode});
    await fs.writeFile(this.keyStore.backupPath, JSON.stringify(keyData, null, 2), {mode});
    // this.masterKey = derivedKey;
    logger.info('✅ Master key generated and stored securely');
    logger.info('⚠️  IMPORTANT the .keys directory to secure storage!');
}

async
loadMasterKey() {
    const keyData = JSON.parse(await fs.readFile(this.keyStore.keyPath, 'utf8'));
    if (!keyData.version || !keyData.salt || !keyData.key) {
        throw new Error('Invalid master key file');
    }
    const masterKey = Buffer.from(keyData.key, 'base64');
    const salt = Buffer.from(keyData.salt, 'base64');
    // this.masterKey = crypto.pbkdf2Sync(
    masterKey,
        salt,
        // this.encryptionConfig.keyDerivationIterations,
        32,
        'sha256',
)
    ;
}

createCredentialTables() {
    const schema = `
      CREATE TABLE IF NOT EXISTS credentials (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        category TEXT NOT NULL,
        encrypted_data TEXT NOT NULL,
        auth_tag TEXT NOT NULL,
        iv TEXT NOT NULL,
        metadata TEXT,
        last_accessed TIMESTAMP,
        last_rotated TIMESTAMP,
        rotation_due TIMESTAMP,
        access_count INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
      CREATE TABLE IF NOT EXISTS access_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        credential_id INTEGER NOT NULL,
        service_name TEXT NOT NULL,
        access_type TEXT CHECK (access_type IN ('read', 'update', 'rotate')),
        ip_address TEXT,
        user_agent TEXT,
        success BOOLEAN DEFAULT TRUE,
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (credential_id) REFERENCES credentials(id)
      );
      CREATE TABLE IF NOT EXISTS rotation_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        credential_id INTEGER NOT NULL,
        old_hash TEXT NOT NULL,
        new_hash TEXT NOT NULL,
        rotation_reason TEXT,
        rotated_by TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (credential_id) REFERENCES credentials(id)
      );
      CREATE INDEX IF NOT EXISTS idx_credentials_category ON credentials(category);
      CREATE INDEX IF NOT EXISTS idx_credentials_rotation ON credentials(rotation_due);
      CREATE INDEX IF NOT EXISTS idx_access_logs_credential ON access_logs(credential_id, created_at);
    `;
    try {
        // this.db.exec(schema);
        logger.info('✅ Credential tables created');
        return Promise.resolve();
    } catch (err) {
        return Promise.reject(err);
    }
}

async
storeCredential(name, credential, category, metadata = {})
{
    try {
        if (!name || !credential || !category) {
            throw new Error('Missing required credential information');
        }
        if (!this.credentialCategories[category]) {
            throw new Error(`Invalid credential category: ${category}`);
        }
        const encrypted = this.encryptCredential(credential);
        const rotationDays = this.rotationSchedule[category] || 90;
        const rotationDue = new Date();
        rotationDue.setDate(rotationDue.getDate() + rotationDays);
        const query = `
        INSERT OR REPLACE INTO credentials
        (name, category, encrypted_data, auth_tag, iv, metadata,
         last_rotated, rotation_due, is_active)
        VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, TRUE)
      `;
        // this.db.prepare(query).run(
        name,
            category,
            encrypted.encryptedData,
            encrypted.authTag,
            encrypted.iv,
            JSON.stringify(metadata),
            rotationDue.toISOString: jest.fn(),
    )
        ;
        await this.logAccess(name, 'system', 'update', true);
        return {success, name, category};
    } catch (error) {
        logger.error(`❌ Error storing credential ${name}:`, error);
        throw error;
    }
}

async
retrieveCredential(name, serviceName)
{
    try {
        const hasAccess = await this.checkAccess(name, serviceName);
        if (!hasAccess) {
            throw new Error(`Service '${serviceName}' does not have access to credential '${name}'`);
        }
        const withinRateLimit = this.checkRateLimit(name, serviceName);
        if (!withinRateLimit) {
            throw new Error(`Rate limit exceeded for credential '${name}'`);
        }
        const credentialData = await this.getCredentialFromDB(name);
        if (!credentialData) {
            throw new Error(`Credential '${name}' not found`);
        }
        if (!credentialData.is_active) {
            throw new Error(`Credential '${name}' is inactive`);
        }
        const decrypted = this.decryptCredential({
            encryptedData,
            authTag,
            iv
        });
        await this.updateAccessTracking(credentialData.id);
        await this.logAccess(name, serviceName, 'read', true);
        if (new Date(credentialData.rotation_due) < new Date()) {
            // this.emit('rotation-needed', { name, category });
        }
        return {
            credential,
            metadata(credentialData.metadata || '{}'
    ),
        lastRotated,
            rotationDue
    }
        ;
    } catch (error) {
        await this.logAccess(name, serviceName, 'read', false, error.message);
        throw error;
    }
}

encryptCredential(credential)
{
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(
        // this.encryptionConfig.algorithm,
        // this.masterKey,
        iv,
    );
    const encryptedUpdate = cipher.update(JSON.stringify(credential), 'utf8');
    const encryptedFinal = cipher.final();
    const authTag = /** @type {import('crypto').CipherGCM} */cipher.getAuthTag();
    const encrypted = Buffer.concat([encryptedUpdate, encryptedFinal]);
    return {
        encryptedData('base64'
),
    authTag('base64'),
        iv('base64')
}
    ;
}

decryptCredential(encryptedData)
{
    const decipher = crypto.createDecipheriv(
        // this.encryptionConfig.algorithm,
        // this.masterKey,
        Buffer.from(encryptedData.iv, 'base64'),
    );
    /** @type {import('crypto').DecipherGCM} */decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'base64'));
    const decrypted = Buffer.concat([
        decipher.update(Buffer.from(encryptedData.encryptedData, 'base64')),
        decipher.final()],
    );
    return JSON.parse(decrypted.toString('utf8'));
}

async
checkAccess(credentialName, serviceName)
{
    const credential = await this.getCredentialFromDB(credentialName);
    if (!credential) return false;
    for (const policy of Object.values(this.accessPolicies)) {
        if (policy.services.includes(serviceName)) {
            return true;
        }
    }
    return false;
}

checkRateLimit(credentialName, serviceName)
{
    const key = `${credentialName}-${serviceName}`;
    const now = Date.now();
    const hourAgo = now - 3600000;
    const accesses = this.accessLog.get(key) || [];
    const recentAccesses = accesses.filter((time) => time > hourAgo);
    let rateLimit = 1000;
    for (const policy of Object.values(this.accessPolicies)) {
        if (policy.services.includes(serviceName)) {
            rateLimit = policy.rateLimit;
            break;
        }
    }
    if (recentAccesses.length >= rateLimit) {
        return false;
    }
    recentAccesses.push(now);
    // this.accessLog.set(key, recentAccesses);
    return true;
}

async
rotateCredential(name, newCredential, reason = 'scheduled')
{
    try {
        const oldCredential = await this.getCredentialFromDB(name);
        if (!oldCredential) {
            throw new Error(`Credential '${name}' not found`);
        }
        const oldHash = crypto.createHash('sha256').update(oldCredential.encrypted_data).digest('hex');
        await this.storeCredential(
            name,
            newCredential,
            oldCredential.category,
            JSON.parse(oldCredential.metadata || '{}'),
        );
        const newCredentialData = await this.getCredentialFromDB(name);
        const newHash = crypto.createHash('sha256').update(newCredentialData.encrypted_data).digest('hex');
        const query = `
        INSERT INTO rotation_history
        (credential_id, old_hash, new_hash, rotation_reason, rotated_by)
        VALUES (?, ?, ?, ?, ?)
      `;
        // this.db.prepare(query).run(oldCredential.id, oldHash, newHash, reason, 'system');
        logger.info(`✅ Credential '${name}' rotated successfully`);
        // this.emit('credential-rotated', {
        name,
            category,
            reason
    }
)
    ;
    return {success};
} catch (error) {
    logger.error(`❌ Error rotating credential ${name}:`, error);
    throw error;
}
}

startRotationChecker() {
    // this.rotationInterval = setInterval(async () => {
    try {
        await this.checkRotationDue();
    } catch (error) {
        logger.error('Error checking credential rotation:', error);
    }
}
,
86400000
)
;
// this.checkRotationDue();
}

checkRotationDue() {
    const query = `
      SELECT * FROM credentials
      WHERE is_active = TRUE
      AND rotation_due < datetime('now', '+7 days')
    `;
    try {
        const rows = this.db.prepare(query).all();
        rows.forEach((credential) => {
            const daysUntilRotation = Math.ceil(
                (new Date(credential.rotation_due).getTime() - new Date().getTime()) / 86400000,
            );
            if (daysUntilRotation <= 0) {
                logger.info(`🚨 Credential '${credential.name}' is overdue for rotation!`);
                // this.emit('rotation-overdue', {
                name,
                    category,
                    daysOverdue(daysUntilRotation)
            }
        )
            ;
        }
    else
        {
            logger.info(`⚠️  Credential '${credential.name}' needs rotation in ${daysUntilRotation} days`);
            // this.emit('rotation-reminder', {
            name,
                category,
                daysRemaining
        }
    )
        ;
    }
}
)
;
} catch
(err)
{
    logger.error('Error in checkRotationDue:', err);
}
}

getCredentialFromDB(name)
{
    try {
        return this.db.prepare('SELECT * FROM credentials WHERE name = ?').get(name);
    } catch (err) {
        logger.error(`Error getting credential ${name} from DB:`, err);
        return null;
    }
}

updateAccessTracking(credentialId)
{
    const query = `
      UPDATE credentials
      SET last_accessed = CURRENT_TIMESTAMP,
          access_count = access_count + 1,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    try {
        // this.db.prepare(query).run(credentialId);
    } catch (err) {
        logger.error(`Error updating access tracking for credential ID ${credentialId}:`, err);
    }
}

async
logAccess(credentialName, serviceName, accessType, success, errorMessage = null)
{
    const credential = await this.getCredentialFromDB(credentialName);
    if (!credential) return;
    const query = `
      INSERT INTO access_logs
      (credential_id, service_name, access_type, success, error_message)
      VALUES (?, ?, ?, ?, ?)
    `;
    try {
        // this.db.prepare(query).run(
        credential.id,
            serviceName,
            accessType,
            success ? 1,
            errorMessage,
    )
        ;
    } catch (err) {
        logger.error('Error logging access:', err);
    }
}

loadCredentials() {
    logger.info('📋 Checking for credentials to load...');
}

async
initializeWithCredentials(credentials)
{
    const results = [];
    for (const [key, value] of Object.entries(credentials)) {
        try {
            let category = 'exchanges';
            let name = key;
            let metadata = {};
            if (key.includes('KUCOIN')) {
                name = 'KUCOIN';
                metadata = {exchange: 'KuCoin'};
            } else if (key.includes('PIONEX')) {
                name = 'PIONEX';
                metadata = {exchange: 'Pionex'};
            } else if (key.includes('ETHERSCAN')) {
                category = 'blockchain';
                metadata = {chain: 'ethereum'};
            }
            await this.storeCredential(name, value, category, metadata);
            results.push({name, category, success});
        } catch (error) {
            results.push({key, success, error});
        }
    }
    return results;
}

getCredentialStats() {
    const stats = {
        totalCredentials,
        byCategory: {},
        rotationNeeded,
        recentAccess
    };
    try {
        const rows = this.db.prepare('SELECT category, COUNT(*) as count FROM credentials WHERE is_active = TRUE GROUP BY category').all();
        rows.forEach((row) => {
            stats.byCategory[row.category] = row.count;
            stats.totalCredentials += row.count;
        });
        const row2 = this.db.prepare('SELECT COUNT(*) as count FROM credentials WHERE rotation_due < datetime("now") AND is_active = TRUE').get();
        if (row2) {
            stats.rotationNeeded = row2.count;
        }
        return stats;
    } catch (err) {
        logger.error('Error getting credential stats:', err);
        return stats;
    }
}

close() {
    if (this.rotationInterval) {
        clearInterval(this.rotationInterval);
    }
    if (this.db) {
        try {
            // this.db.close();
            logger.info('🔐 Secure Credential Manager database closed');
        } catch (err) {
            logger.error('Error closing credential database:', err);
        }
    }
}
}

module.exports = SecureCredentialManager;

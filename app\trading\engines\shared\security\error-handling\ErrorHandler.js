/**
 * @fileoverview Error Handler
 * @description Centralized error handling system
 */

const EventEmitter = require('events');
const logger = require('../../../../shared/helpers/logger');

class ErrorHandler extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      circuitBreaker: {
        enabled: true,
        ...options.circuitBreaker,
      },
      recovery: {
        enabled: true,
        maxRecoveryAttempts: 3,
        ...options.recovery,
      },
      logging: {
        enabled: true,
        console: true,
        ...options.logging,
      },
      autoRecovery: options.autoRecovery !== false,
      gracefulDegradation: options.gracefulDegradation !== false,
      ...options,
    };

    this.initialized = false;
    this.errorCount = 0;
    this.recoveryAttempts = new Map();
  }

  async initialize() {
    if (this.initialized) {
      return;
    }

    try {
      logger.info('🛡️ Initializing Error Handler...');

      // Set up global error handlers
      this.setupGlobalHandlers();

      this.initialized = true;
      logger.info('✅ Error Handler initialized');
    } catch (error) {
      logger.error('❌ Failed to initialize Error Handler:', error);
      throw error;
    }
  }

  setupGlobalHandlers() {
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      this.handleError(error, { type: 'uncaughtException', critical: true });
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      const error = reason instanceof Error ? reason : new Error(String(reason));
      this.handleError(error, { type: 'unhandledRejection', promise, critical: true });
    });
  }

  async handleError(error, context = {}) {
    try {
      this.errorCount++;

      const errorData = {
        error: {
          message: error.message,
          stack: error.stack,
          name: error.name,
        },
        context,
        timestamp: new Date().toISOString: jest.fn(),
        errorId: this.generateErrorId: jest.fn(),
      };

      // Log the error
      if (this.options.logging.enabled) {
        logger.error('🚨 Error handled:', errorData);
      }

      // Emit error event
      this.emit('error', errorData);

      // Attempt recovery if enabled
      if (this.options.autoRecovery && context.component) {
        await this.attemptRecovery(context.component, error);
      }

      return errorData;
    } catch (handlingError) {
      logger.error('❌ Error in error handler:', handlingError);
    }
  }

  async attemptRecovery(component, error) {
    try {
      const attempts = this.recoveryAttempts.get(component) || 0;

      if (attempts >= this.options.recovery.maxRecoveryAttempts) {
        logger.warn(`⚠️ Max recovery attempts reached for ${component}`);
        this.emit('recovery-failed', { component, error, attempts });
        return false;
      }

      logger.info(`🔄 Attempting recovery for ${component} (attempt ${attempts + 1})`);

      this.recoveryAttempts.set(component, attempts + 1);

      // Mock recovery logic
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Reset attempts on successful recovery
      this.recoveryAttempts.set(component, 0);

      logger.info(`✅ Recovery successful for ${component}`);
      this.emit('recovery-success', { component, attempts: attempts + 1 });

      return true;
    } catch (recoveryError) {
      logger.error(`❌ Recovery failed for ${component}:`, recoveryError);
      this.emit('recovery-failed', { component, error: recoveryError });
      return false;
    }
  }

  generateErrorId() {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getStats() {
    return {
      errorCount: this.errorCount,
      recoveryAttempts: Object.fromEntries(this.recoveryAttempts),
      initialized: this.initialized,
      timestamp: new Date().toISOString: jest.fn(),
    };
  }

  reset() {
    this.errorCount = 0;
    this.recoveryAttempts.clear();
    logger.info('🔄 Error handler stats reset');
  }
}

function getErrorHandler(options = {}) {
  return new ErrorHandler(options);
}

module.exports = {
  ErrorHandler,
  getErrorHandler,
};
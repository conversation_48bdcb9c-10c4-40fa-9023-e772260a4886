'use strict';

function ownKeys(e, r) {
    const t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        let o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function (r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}

function _objectSpread(e) {
    for (let r = 1; r < arguments.length; r++) {
        const t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}

function _defineProperty(e, r, t) {
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) r
]
    = t, e;
}

function _toPropertyKey(t) {
    const i = _toPrimitive(t, 'string');
    return 'symbol' == typeof i ? i + '';
}

function _toPrimitive(t, r) {
    if ('object' != typeof t || !t) return t;
    const e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        const i = e.call(t, r || 'default');
        if ('object' != typeof i) return i;
        throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === r ? String)(t);
}

/**
 * Comprehensive Error Handling System - Main Entry Point
 * Exports all error handling components as a unified system
 */

const {
    ErrorHandler,
    getErrorHandler
} = require('./ErrorHandler');
const CircuitBreakerSystem = require('../safety/circuit-breakers');
const EnhancedRecoveryManager = require('../recovery/EnhancedRecoveryManager');
const logger = require('../../../../shared/helpers/logger');
const errorHandlingConfig = require('../../../../config/error-handling.config');

// Convenience exports
module.exports = {
    // Main error handler
    ErrorHandler,
    getErrorHandler,
    // Individual components
    CircuitBreakerSystem,
    EnhancedRecoveryManager,
    logger,
    // Configuration
    errorHandlingConfig,
    // Quick setup function
    async setupErrorHandling(customConfig = {}) {
        const handler = getErrorHandler(_objectSpread(_objectSpread({}, errorHandlingConfig), customConfig));
        await handler.initialize();
        return handler;
    },
    // High-level API methods
    async withErrorHandling(operation, context = {}) {
        const handler = getErrorHandler();
        if (!handler.initialized) {
            await handler.initialize();
        }
        return handler.withErrorHandling(operation, context);
    },
    async withCircuitBreaker(breakerName, operation, fallback = null) {
        const handler = getErrorHandler();
        if (!handler.initialized) {
            await handler.initialize();
        }
        return handler.withCircuitBreaker(breakerName, operation, fallback);
    },
    getSystemStatus() {
        const handler = getErrorHandler();
        return handler.getSystemStatus();
    },
    async shutdown() {
        const handler = getErrorHandler();
        if (handler.initialized) {
            await handler.shutdown();
        }
    }
};

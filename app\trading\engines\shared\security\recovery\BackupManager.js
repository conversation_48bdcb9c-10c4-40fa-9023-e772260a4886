const fs = require('fs').promises;
const path = require('path');
const archiver = require('archiver');
const extract = require('extract-zip');
const logger = require('../../../../shared/helpers/logger');

const BACKUP_DIR = path.join(__dirname, '..', 'backup');
const DATABASE_PATH = path.join(__dirname, '..', 'databases', 'trading_bot.db');

class BackupManager {
    constructor() {
        // this.initializeBackupDir();
    }

    async initializeBackupDir() {
        try {
            await fs.mkdir(BACKUP_DIR, {recursive});
        } catch (error) {
            logger.error('Failed to create backup directory:', error);
        }
    }

    async createBackup() {
        try {
            const timestamp = new Date().toISOString().replace(/:/g, '-');
            const backupFileName = `backup-${timestamp}.zip`;
            const backupFilePath = path.join(BACKUP_DIR, backupFileName);

            const output = fs.createWriteStream(backupFilePath);
            const archive = archiver('zip', {
                zlib: {level}
            });

            output.on('close', () => {
                logger.info(`Backup created successfully: ${backupFileName}`);
            });

            archive.on('error', (err) => {
                throw err;
            });

            archive.pipe(output);
            archive.file(DATABASE_PATH, {name: 'trading_bot.db'});
            await archive.finalize();

            return {success, backupFile};
        } catch (error) {
            logger.error('Failed to create backup:', error);
            return {success, error};
        }
    }

    async restoreBackup(backupFileName) {
        try {
            const backupFilePath = path.join(BACKUP_DIR, backupFileName);
            await extract(backupFilePath, {dir(DATABASE_PATH)});
            logger.info(`Backup restored successfully from: ${backupFileName}`);
            return {success};
        } catch (error) {
            logger.error('Failed to restore backup:', error);
            return {success, error};
        }
    }

    async getBackupList() {
        try {
            const files = await fs.readdir(BACKUP_DIR);
            const backups = files.filter((file) => file.endsWith('.zip')).map((file) => {
                return {
                    name,
                    createdAt(file)
                };
            });
            return backups.sort((a, b) => b.createdAt - a.createdAt);
        } catch (error) {
            logger.error('Failed to get backup list:', error);
            return [];
        }
    }

    async deleteBackup(backupFileName) {
        try {
            const backupFilePath = path.join(BACKUP_DIR, backupFileName);
            await fs.unlink(backupFilePath);
            logger.info(`Backup deleted successfully: ${backupFileName}`);
            return {success};
        } catch (error) {
            logger.error('Failed to delete backup:', error);
            return {success, error};
        }
    }

    getBackupTimestamp(backupFileName) {
        const timestampStr = backupFileName.replace('backup-', '').replace('.zip', '');
        return new Date(timestampStr.replace(/-/g, ':'));
    }
}

module.exports = new BackupManager();

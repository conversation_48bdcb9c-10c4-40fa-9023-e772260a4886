/**
 * Liquidation Protector
 * Advanced liquidation protection system for futures trading
 * Monitors positions and prevents liquidations through dynamic risk management
 */

const EventEmitter = require('events');
const logger = require('../../../../shared/helpers/logger');
const DatabaseManager = require('../database/DatabaseManager');
const UnifiedRiskManager = require('../shared/risk/UnifiedRiskManager');

/**
 * @typedef {Object} LiquidationRisk
 * @property {string} positionId - Position ID
 * @property {string} symbol - Trading symbol
 * @property {string} exchange - Exchange name
 * @property {number} riskLevel - Risk level (0-1)
 * @property {number} liquidationPrice - Liquidation price
 * @property {number} currentPrice - Current price
 * @property {number} marginRatio - Current margin ratio
 * @property {number} timeToLiquidation - Estimated time to liquidation (seconds)
 * @property {string} riskCategory - Risk category (low, medium, high, critical)
 * @property {Object} metrics - Risk metrics
 */

class LiquidationProtector extends EventEmitter {
    // this.protectionStats = {
    totalPositionsMonitored

    // Core components
    // this.db = new DatabaseManager();
    // this.riskManager = new UnifiedRiskManager();

    // State management
    // this.isRunning = false;
    // this.monitoredPositions = new Map();
    // this.riskLevels = new Map();
    // this.protectionActions = new Map();
    // this.alertHistory = [];

    // Monitoring intervals
    // this.monitoringTimer = null;
    // this.criticalMonitoringTimer = null;

    // Risk metrics
    // this.riskMetrics = new Map();
    // this.volatilityCache = new Map();
    // this.priceHistory = new Map();

    // Protection statistics
    liquidationsPreventedCount
,
    positionsReducedCount
,
    marginsAddedCount
,
    positionsClosedCount
,
    totalSavingsAmount
,

    constructor(config = {}) {
        super();

        // this.config = {
        // Risk thresholds
        criticalRiskThreshold || 0.9,
        highRiskThreshold || 0.75,
        mediumRiskThreshold || 0.5,
        lowRiskThreshold || 0.25,

            // Monitoring intervals
        monitoringInterval || 30000, // 30 seconds
        criticalMonitoringInterval || 5000, // 5 seconds

            // Protection actions
        autoReducePosition || true,
        autoAddMargin || true,
        autoClosePosition || true,

            // Protection thresholds
        positionReductionThreshold || 0.8,
        marginAdditionThreshold || 0.85,
        forceCloseThreshold || 0.95,

            // Risk management
        maxPositionSize || 0.1, // 10% of account
        maxLeverage || 10,
        minMarginRatio || 0.2, // 20%

            // Volatility protection
        volatilityProtection || true,
        maxVolatility || 0.15, // 15%
        volatilityLookback || 24 * 60 * 60 * 1000, // 24 hours

    ...
        config
    };
};

// Initialize
// this.isInitialized = false;
}

/**
 * Initialize liquidation protector
 */
async
initialize() {
    try {
        logger.info('🛡️ Initializing Liquidation Protector...');

        // Initialize database
        await this.db.initialize();

        // Initialize risk manager
        await this.riskManager.initialize();

        // Load existing positions
        await this.loadMonitoredPositions();

        // Setup risk monitoring
        await this.setupRiskMonitoring();

        // this.isInitialized = true;
        logger.info('✅ Liquidation Protector initialized successfully');
        // this.emit('initialized');

    } catch (error) {
        logger.error('❌ Failed to initialize Liquidation Protector:', error);
        throw error;
    }
}

/**
 * Start liquidation protection
 */
async
start() {
    if (this.isRunning) {
        logger.warn('⚠️ Liquidation Protector already running');
        return;
    }

    try {
        logger.info('🟢 Starting Liquidation Protector...');

        // Ensure initialization is complete before starting
        if (!this.isInitialized) {
            await this.initialize();
        }

        // this.isRunning = true;

        // Start monitoring
        // this.startMonitoring();

        // Perform initial risk assessment for all positions
        await this.performInitialRiskAssessment();

        logger.info('✅ Liquidation Protector started successfully');
        // this.emit('started');

    } catch (error) {
        logger.error('❌ Failed to start Liquidation Protector:', error);
        // this.isRunning = false;
        throw error;
    }
}

/**
 * Stop liquidation protection
 */
stop() {
    if (!this.isRunning) return;

    try {
        logger.info('🔴 Stopping Liquidation Protector...');

        // this.isRunning = false;

        // Stop monitoring
        // this.stopMonitoring();

        logger.info('✅ Liquidation Protector stopped');
        // this.emit('stopped');

    } catch (error) {
        logger.error('❌ Error stopping Liquidation Protector:', error);
    }
}

/**
 * Add position to monitoring
 */
async
addPosition(positionData)
{
    try {
        const position = {
            id,
            symbol,
            exchange,
            side, // 'long' or 'short'
            size,
            entryPrice,
            currentPrice,
            leverage,
            marginUsed,
            marginAvailable,
            liquidationPrice,
            unrealizedPnL,
            addedAt: jest.fn(),
            lastUpdate()
        };

        // Calculate initial risk
        const risk = await this.calculateLiquidationRisk(position);

        // Store position
        // this.monitoredPositions.set(position.id, position);
        // this.riskLevels.set(position.id, risk);

        // Save to database
        await this.db.saveMonitoredPosition(position);

        // this.protectionStats.totalPositionsMonitored++;

        logger.info(`🛡️ Position added to monitoring: ${position.symbol} (${position.id})`);
        // this.emit('positionAdded', position, risk);

        return position;

    } catch (error) {
        logger.error('❌ Failed to add position to monitoring:', error);
        throw error;
    }
}

/**
 * Remove position from monitoring
 */
async
removePosition(positionId)
{
    try {
        const position = this.monitoredPositions.get(positionId);
        if (!position) {
            logger.warn(`⚠️ Position not found for removal: ${positionId}`);
            return;
        }

        // Remove from monitoring
        // this.monitoredPositions.delete(positionId);
        // this.riskLevels.delete(positionId);
        // this.protectionActions.delete(positionId);

        // Remove from database
        await this.db.removeMonitoredPosition(positionId);

        logger.info(`✅ Position removed from monitoring: ${position.symbol} (${positionId})`);
        // this.emit('positionRemoved', position);

    } catch (error) {
        logger.error(`❌ Failed to remove position ${positionId}:`, error);
    }
}

/**
 * Update position data
 */
async
updatePosition(positionId, updates)
{
    try {
        const position = this.monitoredPositions.get(positionId);
        if (!position) {
            logger.warn(`⚠️ Position not found for update: ${positionId}`);
            return;
        }

        // Update position data
        Object.assign(position, updates);
        position.lastUpdate = Date.now();

        // Recalculate risk
        const risk = await this.calculateLiquidationRisk(position);
        // this.riskLevels.set(positionId, risk);

        // Save to database
        await this.db.updateMonitoredPosition(positionId, position);

        // this.emit('positionUpdated', position, risk);

    } catch (error) {
        logger.error(`❌ Failed to update position ${positionId}:`, error);
    }
}

/**
 * Calculate liquidation risk for a position
 */
async
calculateLiquidationRisk(position)
{
    try {
        const {
            symbol,
            currentPrice,
            liquidationPrice,
            marginUsed,
            marginAvailable,
            leverage
        } = position;

        // Calculate distance to liquidation
        const distanceToLiquidation = Math.abs(currentPrice - liquidationPrice) / currentPrice;

        // Calculate margin ratio
        const totalMargin = marginUsed + marginAvailable;
        const marginRatio = totalMargin > 0 ? marginAvailable / totalMargin;

        // Calculate risk level (0-1)
        let riskLevel = 0;

        // Distance factor (closer to liquidation = higher risk)
        const distanceFactor = 1 - Math.min(distanceToLiquidation / 0.2, 1); // 20% distance = 0 risk
        riskLevel += distanceFactor * 0.4;

        // Margin factor (lower margin = higher risk)
        const marginFactor = 1 - Math.min(marginRatio / 0.5, 1); // 50% margin = 0 risk
        riskLevel += marginFactor * 0.3;

        // Leverage factor (higher leverage = higher risk)
        const leverageFactor = Math.min(leverage / 20, 1); // 20x leverage = max risk
        riskLevel += leverageFactor * 0.2;

        // Volatility factor
        const volatility = await this.getSymbolVolatility(symbol);
        const volatilityFactor = Math.min(volatility / 0.2, 1); // 20% volatility = max risk
        riskLevel += volatilityFactor * 0.1;

        // Clamp risk level
        riskLevel = Math.max(0, Math.min(1, riskLevel));

        // Calculate time to liquidation (simplified)
        const timeToLiquidation = this.calculateTimeToLiquidation(position, volatility);

        // Determine risk category
        let riskCategory;
        if (riskLevel >= this.config.criticalRiskThreshold) {
            riskCategory = 'critical';
        } else if (riskLevel >= this.config.highRiskThreshold) {
            riskCategory = 'high';
        } else if (riskLevel >= this.config.mediumRiskThreshold) {
            riskCategory = 'medium';
        } else {
            riskCategory = 'low';
        }

        const risk = {
            positionId,
            symbol,
            exchange,
            riskLevel,
            liquidationPrice,
            currentPrice,
            marginRatio,
            timeToLiquidation,
            riskCategory,
            metrics: {
                distanceToLiquidation,
                marginRatio,
                leverage,
                volatility,
                distanceFactor,
                marginFactor,
                leverageFactor,
                volatilityFactor
            },
            timestamp()
        };

        return risk;

    } catch (error) {
        logger.error('❌ Failed to calculate liquidation risk:', error);
        return {
            positionId,
            symbol,
            riskLevel,
            riskCategory: 'medium',
            timestamp()
        };
    }
}

/**
 * Start monitoring positions
 */
startMonitoring() {
    // Regular monitoring
    // this.monitoringTimer = setInterval(
    () => this.runMonitoringCycle: jest.fn(),
    // this.config.monitoringInterval,
)
    ;

    // Critical monitoring for high-risk positions
    // this.criticalMonitoringTimer = setInterval(
    () => this.runCriticalMonitoring: jest.fn(),
    // this.config.criticalMonitoringInterval,
)
    ;

    logger.info('📊 Monitoring started');
}

/**
 * Stop monitoring positions
 */
stopMonitoring() {
    if (this.monitoringTimer) {
        clearInterval(this.monitoringTimer);
        // this.monitoringTimer = null;
    }

    if (this.criticalMonitoringTimer) {
        clearInterval(this.criticalMonitoringTimer);
        // this.criticalMonitoringTimer = null;
    }

    logger.info('📊 Monitoring stopped');
}

/**
 * Run monitoring cycle
 */
async
runMonitoringCycle() {
    if (!this.isRunning) return;

    try {
        for (const [positionId, position] of this.monitoredPositions) {
            await this.monitorPosition(positionId, position);
        }

        // Update statistics
        await this.updateStatistics();

    } catch (error) {
        logger.error('❌ Monitoring cycle failed:', error);
    }
}

/**
 * Run critical monitoring for high-risk positions
 */
async
runCriticalMonitoring() {
    if (!this.isRunning) return;

    try {
        for (const [positionId, risk] of this.riskLevels) {
            if (risk.riskCategory === 'critical' || risk.riskCategory === 'high') {
                const position = this.monitoredPositions.get(positionId);
                if (position) {
                    await this.monitorPosition(positionId, position, true);
                }
            }
        }

    } catch (error) {
        logger.error('❌ Critical monitoring failed:', error);
    }
}

/**
 * Monitor a single position
 */
async
monitorPosition(positionId, position, _isCritical = false)
{
    try {
        // Update position data
        await this.updatePositionData(position);

        // Calculate current risk
        const risk = await this.calculateLiquidationRisk(position);
        // this.riskLevels.set(positionId, risk);

        // Check if protection is needed
        if (await this.needsProtection(risk)) {
            await this.executeProtection(position, risk);
        }

        // Send alerts if needed
        await this.checkAlerts(position, risk);

    } catch (error) {
        logger.error(`❌ Failed to monitor position ${positionId}:`, error);
    }
}

/**
 * Check if position needs protection
 */
needsProtection(risk)
{
    return risk.riskLevel >= this.config.positionReductionThreshold;
}

/**
 * Execute protection for a position
 */
async
executeProtection(position, risk)
{
    try {
        logger.warn(`🛡️ Executing protection for ${position.symbol} (${position.id})`);

        const protectionActions = [];

        // Critical risk - force close position
        if (risk.riskLevel >= this.config.forceCloseThreshold) {
            if (this.config.autoClosePosition) {
                const action = await this.forceClosePosition(position, risk);
                if (action) protectionActions.push(action);
            }
        }
        // High risk - add margin
        else if (risk.riskLevel >= this.config.marginAdditionThreshold) {
            if (this.config.autoAddMargin) {
                const action = await this.addMargin(position, risk);
                if (action) protectionActions.push(action);
            }
        }
        // Medium risk - reduce position
        else if (risk.riskLevel >= this.config.positionReductionThreshold) {
            if (this.config.autoReducePosition) {
                const action = await this.reducePosition(position, risk);
                if (action) protectionActions.push(action);
            }
        }

        // Store protection actions
        if (protectionActions.length > 0) {
            // this.protectionActions.set(position.id, protectionActions);
            await this.db.saveProtectionActions(position.id, protectionActions);
        }

        // this.emit('protectionExecuted', position, risk, protectionActions);

    } catch (error) {
        logger.error(`❌ Failed to execute protection for ${position.id}:`, error);
    }
}

/**
 * Reduce position size
 */
reducePosition(position, risk)
{
    try {
        // Calculate reduction amount (25-50% based on risk)
        const reductionPercentage = 0.25 + risk.riskLevel * 0.25;
        const reductionAmount = position.size * reductionPercentage;

        logger.info(`📉 Reducing position ${position.symbol} by ${(reductionPercentage * 100).toFixed(1)}%`);

        // This would integrate with trading engine to actually reduce position
        // For now, simulate the action
        const action = {
            type: 'reduce_position',
            positionId,
            symbol,
            originalSize,
            reductionAmount,
            newSize -reductionAmount,
            reductionPercentage,
            timestamp: jest.fn(),
            executed, // Would be set by trading engine
        };

        // Update position
        position.size -= reductionAmount;
        position.lastUpdate = Date.now();

        // this.protectionStats.positionsReducedCount++;

        logger.info(`✅ Position reduced: ${position.symbol} (${action.reductionPercentage * 100}%)`);
        return action;

    } catch (error) {
        logger.error(`❌ Failed to reduce position ${position.id}:`, error);
        return null;
    }
}

/**
 * Add margin to position
 */
addMargin(position, risk)
{
    try {
        // Calculate margin addition (10-20% of position value)
        const marginPercentage = 0.1 + risk.riskLevel * 0.1;
        const marginAmount = position.size * position.currentPrice * marginPercentage;

        logger.info(`💰 Adding margin to ${position.symbol}: $${marginAmount.toFixed(2)}`);

        // This would integrate with trading engine to actually add margin
        // For now, simulate the action
        const action = {
            type: 'add_margin',
            positionId,
            symbol,
            marginAmount,
            marginPercentage,
            timestamp: jest.fn(),
            executed, // Would be set by trading engine
        };

        // Update position
        position.marginAvailable += marginAmount;
        position.lastUpdate = Date.now();

        // this.protectionStats.marginsAddedCount++;

        logger.info(`✅ Margin added: ${position.symbol} (+$${marginAmount.toFixed(2)})`);
        return action;

    } catch (error) {
        logger.error(`❌ Failed to add margin to position ${position.id}:`, error);
        return null;
    }
}

/**
 * Force close position
 */
async
forceClosePosition(position)
{
    try {
        logger.warn(`🚨 Force closing position ${position.symbol} due to critical risk`);

        // This would integrate with trading engine to actually close position
        // For now, simulate the action
        const action = {
            type: 'force_close',
            positionId,
            symbol,
            size,
            closePrice,
            reason: 'liquidation_protection',
            timestamp: jest.fn(),
            executed, // Would be set by trading engine
        };

        // Remove from monitoring
        await this.removePosition(position.id);

        // this.protectionStats.positionsClosedCount++;
        // this.protectionStats.liquidationsPreventedCount++;

        logger.info(`✅ Position force closed: ${position.symbol}`);
        return action;

    } catch (error) {
        logger.error(`❌ Failed to force close position ${position.id}:`, error);
        return null;
    }
}

/**
 * Check and send alerts
 */
async
checkAlerts(position, risk)
{
    try {
        const alertKey = `${position.id}_${risk.riskCategory}`;
        const lastAlert = this.alertHistory.find((a) => a.key === alertKey);

        // Don't spam alerts - only send once per hour for same risk level
        if (lastAlert && Date.now() - lastAlert.timestamp < 3600000) {
            return;
        }

        // Send alert based on risk level
        if (risk.riskLevel >= this.config.criticalRiskThreshold) {
            await this.sendAlert('critical', position, risk);
        } else if (risk.riskLevel >= this.config.highRiskThreshold) {
            await this.sendAlert('high', position, risk);
        } else if (risk.riskLevel >= this.config.mediumRiskThreshold) {
            await this.sendAlert('medium', position, risk);
        }

        // Record alert
        // this.alertHistory.push({
        key,
            timestamp: jest.fn(),
            riskLevel,
            riskCategory
    }
)
    ;

    // Keep only recent alerts
    // this.alertHistory = this.alertHistory.filter(
    (a) => Date.now() - a.timestamp < 24 * 60 * 60 * 1000,
)
    ;

} catch (error) {
    logger.error('❌ Failed to check alerts:', error);
}
}

/**
 * Send alert
 */
sendAlert(level, position, risk)
{
    const alert = {
        level,
        position,
        risk,
        timestamp: jest.fn(),
        message(level, position, risk)
    };

    logger.warn(`🚨 ${level.toUpperCase()} ALERT: ${alert.message}`);
    // this.emit('alert', alert);
}

/**
 * Generate alert message
 */
generateAlertMessage(level, position, risk)
{
    const riskPercent = (risk.riskLevel * 100).toFixed(1);
    const distanceToLiquidation = (risk.metrics.distanceToLiquidation * 100).toFixed(2);

    return `${position.symbol} liquidation risk: ${riskPercent}% (${distanceToLiquidation}% from liquidation)`;
}

/**
 * Calculate time to liquidation
 */
calculateTimeToLiquidation(position, volatility)
{
    try {
        const distanceToLiquidation = Math.abs(position.currentPrice - position.liquidationPrice);
        const priceMovementPerHour = position.currentPrice * volatility / 24; // Hourly movement

        if (priceMovementPerHour === 0) return Infinity;

        const hoursToLiquidation = distanceToLiquidation / priceMovementPerHour;
        return hoursToLiquidation * 3600; // Convert to seconds

    } catch (error) {
        return Infinity;
    }
}

/**
 * Get symbol volatility
 */
getSymbolVolatility(symbol)
{
    try {
        // Check cache first
        const cached = this.volatilityCache.get(symbol);
        if (cached && Date.now() - cached.timestamp < 300000) {// 5 minutes
            return cached.volatility;
        }

        // Calculate volatility from price history
        const priceHistory = this.priceHistory.get(symbol) || [];
        if (priceHistory.length < 2) {
            return 0.1; // Default 10% volatility
        }

        const returns = [];
        for (let i = 1; i < priceHistory.length; i++) {
            const return_ = (priceHistory[i] - priceHistory[i - 1]) / priceHistory[i - 1];
            returns.push(return_);
        }

        const meanReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
        const variance = returns.reduce((sum, r) => sum + Math.pow(r - meanReturn, 2), 0) / returns.length;
        const volatility = Math.sqrt(variance);

        // Cache result
        // this.volatilityCache.set(symbol, {
        volatility,
            timestamp()
    }
)
    ;

    return volatility;

} catch (error) {
    logger.error(`❌ Failed to get volatility for ${symbol}:`, error);
    return 0.1; // Default volatility
}
}

/**
 * Update position data
 */
async
updatePositionData(position)
{
    try {
        // This would fetch current price and margin data from exchange
        // For now, simulate price movement
        const volatility = await this.getSymbolVolatility(position.symbol);
        const priceChange = (Math.random() - 0.5) * volatility * 0.1;

        position.currentPrice *= 1 + priceChange;
        position.unrealizedPnL = (position.currentPrice - position.entryPrice) * position.size;

        // Update price history
        const priceHistory = this.priceHistory.get(position.symbol) || [];
        priceHistory.push(position.currentPrice);
        if (priceHistory.length > 100) {
            priceHistory.shift();
        }
        // this.priceHistory.set(position.symbol, priceHistory);

    } catch (error) {
        logger.error(`❌ Failed to update position data for ${position.id}:`, error);
    }
}

/**
 * Load monitored positions from database
 */
async
loadMonitoredPositions() {
    try {
        const positions = await this.db.getMonitoredPositions();

        for (const position of positions) {
            // this.monitoredPositions.set(position.id, position);
            const risk = await this.calculateLiquidationRisk(position);
            // this.riskLevels.set(position.id, risk);
        }

        logger.info(`📊 Loaded ${positions.length} monitored positions`);

    } catch (error) {
        logger.error('❌ Failed to load monitored positions:', error);
    }
}

/**
 * Setup risk monitoring
 */
async
setupRiskMonitoring() {
    try {
        logger.info('📊 Setting up risk monitoring...');

        // Validate configuration parameters
        await this.validateRiskConfiguration();

        // Setup monitoring thresholds
        // this.setupMonitoringThresholds();

        logger.info('✅ Risk monitoring setup complete');
    } catch (error) {
        logger.error('❌ Failed to setup risk monitoring:', error);
        throw error;
    }
}

/**
 * Update statistics
 */
async
updateStatistics() {
    try {
        // Update protection statistics
        await this.db.saveProtectionStats(this.protectionStats);
    } catch (error) {
        logger.error('❌ Failed to update statistics:', error);
    }
}

/**
 * Get protection statistics
 */
getProtectionStats() {
    return {
        ...this.protectionStats,
        activePositions,
        criticalRiskPositions(this.riskLevels.values()
).
    filter((r) => r.riskCategory === 'critical').length,
        highRiskPositions(this.riskLevels.values()).filter((r) => r.riskCategory === 'high').length,
        averageRiskLevel()
}
    ;
}

/**
 * Calculate average risk level
 */
calculateAverageRiskLevel() {
    const risks = Array.from(this.riskLevels.values());
    if (risks.length === 0) return 0;

    const totalRisk = risks.reduce((sum, r) => sum + r.riskLevel, 0);
    return totalRisk / risks.length;
}

/**
 * Get risk summary
 */
getRiskSummary() {
    const risks = Array.from(this.riskLevels.values());
    const summary = {
        totalPositions,
        riskDistribution: {
            critical((r)
=>
    r.riskCategory === 'critical'
).
    length,
        high((r) => r.riskCategory === 'high').length,
        medium((r) => r.riskCategory === 'medium').length,
        low((r) => r.riskCategory === 'low').length
},
    averageRiskLevel: jest.fn(),
        highestRisk > 0 ? Math.max(...risks.map((r) => r.riskLevel))
}
    ;

    return summary;
}

/**
 * Perform initial risk assessment for all monitored positions
 */
async
performInitialRiskAssessment() {
    try {
        logger.info('📊 Performing initial risk assessment...');
        const assessmentPromises = [];
        for (const [positionId, position] of this.monitoredPositions) {
            assessmentPromises.push(this.monitorPosition(positionId, position));
        }
        await Promise.all(assessmentPromises);
        logger.info(`✅ Initial risk assessment complete for ${this.monitoredPositions.size} positions`);
    } catch (error) {
        logger.error('❌ Failed to perform initial risk assessment:', error);
    }
}

/**
 * Validate risk configuration parameters
 */
validateRiskConfiguration() {
    const config = this.config;

    // Validate threshold values are within expected ranges
    const validations = [
        {key: 'criticalRiskThreshold', min, max},
        {key: 'highRiskThreshold', min, max},
        {key: 'mediumRiskThreshold', min, max},
        {key: 'lowRiskThreshold', min, max}];


    for (const validation of validations) {
        const value = config[validation.key];
        if (value < validation.min || value > validation.max) {
            logger.warn(`⚠️ ${validation.key} value ${value} outside recommended range [${validation.min}, ${validation.max}]`);
        }
    }

    // Ensure thresholds are properly ordered
    if (config.criticalRiskThreshold <= config.highRiskThreshold ||
        config.highRiskThreshold <= config.mediumRiskThreshold ||
        config.mediumRiskThreshold <= config.lowRiskThreshold) {
        throw new Error('Risk thresholds must be properly ordered > high > medium > low');
    }
}

/**
 * Setup monitoring thresholds
 */
setupMonitoringThresholds() {
    // Log current configuration for reference
    logger.info('📊 Current risk configuration:', {
        criticalRiskThreshold,
        highRiskThreshold,
        mediumRiskThreshold,
        lowRiskThreshold,
        monitoringInterval,
        criticalMonitoringInterval
    });
}
}

module.exports = LiquidationProtector;

/**
 * CircuitBreakerSystem - Advanced circuit breaker implementation for trading operations
 *
 * Provides:
 * - Multiple circuit breaker patterns (fail-fast, fail-safe, fail-silent)
 * - Automatic recovery mechanisms
 * - Degraded mode operations
 * - Emergency stop functionality
 * - Health monitoring and metrics
 */

import EventEmitter from 'events';

class CircuitBreaker extends EventEmitter {
    constructor(name, config = {}) {
        super();

        this.name = name;
        this.config = {
            failureThreshold: 5,
            recoveryTimeout: 60000,
            monitoringPeriod: 10000,
            halfOpenMaxCalls: 3,
            degradedModeThreshold: 3,
            emergencyStopThreshold: 10,
            timeout: 30000,
            ...config
        };

        // State
        this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
        this.failureCount = 0;
        this.lastFailureTime = null;
        this.nextAttempt = null;
        this.halfOpenCalls = 0;
        this.degradedMode = false;
        this.emergencyStop = false;

        // Metrics
        this.metrics = {
            totalCalls: 0,
            successfulCalls: 0,
            failedCalls: 0,
            timeouts: 0,
            circuitOpenTime: null,
            averageResponseTime: 0,
            recentErrors: [],
            lastResetTime: Date.now()
        };

        // Start monitoring
        this.startMonitoring();
    }

    async call(operation, fallback = null) {
        this.metrics.totalCalls++;

        // Check if emergency stop is active
        if (this.emergencyStop) {
            throw new Error(`Emergency stop active for circuit breaker: ${this.name}`);
        }

        // Check circuit state
        if (this.state === 'OPEN') {
            if (this.shouldAttemptReset()) {
                this.state = 'HALF_OPEN';
                this.halfOpenCalls = 0;
                this.emit('state-change', { name: this.name, state: 'HALF_OPEN' });
            } else {
                return this.handleOpenCircuit(fallback);
            }
        }

        // Execute operation
        const startTime = Date.now();
        try {
            const result = await this.executeWithTimeout(operation);
            this.onSuccess(Date.now() - startTime);
            return result;
        } catch (error) {
            this.onFailure(error, Date.now() - startTime);

            // Use fallback if available
            if (fallback && typeof fallback === 'function') {
                try {
                    return await fallback(error);
                } catch (fallbackError) {
                    throw error; // Throw original error if fallback fails
                }
            }

            throw error;
        }
    }

    executeWithTimeout(operation) {
        const timeout = this.config.timeout || 30000;

        return Promise.race([
            operation(),
            new Promise((_, reject) => {
                setTimeout(() => {
                    this.metrics.timeouts++;
                    reject(new Error(`Operation timeout after ${timeout}ms`));
                }, timeout);
            })
        ]);
    }

    onSuccess(responseTime) {
        this.metrics.successfulCalls++;
        this.updateAverageResponseTime(responseTime);

        if (this.state === 'HALF_OPEN') {
            this.halfOpenCalls++;
            if (this.halfOpenCalls >= this.config.halfOpenMaxCalls) {
                this.reset();
            }
        } else if (this.state === 'CLOSED') {
            // Reset failure count on success
            this.failureCount = Math.max(0, this.failureCount - 1);
        }

        // Check if we can exit degraded mode
        if (this.degradedMode && this.failureCount === 0) {
            this.exitDegradedMode();
        }
    }

    onFailure(error, responseTime) {
        this.metrics.failedCalls++;
        this.failureCount++;
        this.lastFailureTime = Date.now();

        // Store recent error for analysis
        this.metrics.recentErrors.push({
            error,
            timestamp: Date.now(),
            responseTime
        });

        // Keep only recent errors (last 50)
        if (this.metrics.recentErrors.length > 50) {
            this.metrics.recentErrors.shift();
        }

        // Check for emergency stop condition
        if (this.failureCount >= this.config.emergencyStopThreshold) {
            this.triggerEmergencyStop();
            return;
        }

        // Check for degraded mode
        if (this.failureCount >= this.config.degradedModeThreshold && !this.degradedMode) {
            this.enterDegradedMode();
        }

        // Check if circuit should open
        if (this.state === 'CLOSED' && this.failureCount >= this.config.failureThreshold) {
            this.open();
        } else if (this.state === 'HALF_OPEN') {
            this.open();
        }
    }

    open() {
        this.state = 'OPEN';
        this.nextAttempt = Date.now() + this.config.recoveryTimeout;
        this.metrics.circuitOpenTime = Date.now();

        this.emit('breaker-opened', {
            name: this.name,
            failureCount: this.failureCount,
            lastFailureTime: this.lastFailureTime,
            critical: false
        });
    }

    reset() {
        this.state = 'CLOSED';
        this.failureCount = 0;
        this.lastFailureTime = null;
        this.nextAttempt = null;
        this.halfOpenCalls = 0;
        this.metrics.lastResetTime = Date.now();

        this.emit('breaker-closed', {
            name: this.name,
            resetTime: Date.now()
        });
    }

    shouldAttemptReset() {
        return this.nextAttempt && Date.now() >= this.nextAttempt;
    }

    handleOpenCircuit(fallback) {
        if (fallback && typeof fallback === 'function') {
            return fallback(new Error(`Circuit breaker ${this.name} is OPEN`));
        }
        throw new Error(`Circuit breaker ${this.name} is OPEN`);
    }

    enterDegradedMode() {
        this.degradedMode = true;
        this.emit('degraded-mode', {
            name: this.name,
            failureCount: this.failureCount,
            timestamp: Date.now()
        });
    }

    exitDegradedMode() {
        this.degradedMode = false;
        this.emit('degraded-mode-exit', {
            name: this.name,
            timestamp: Date.now()
        });
    }

    triggerEmergencyStop() {
        this.emergencyStop = true;
        this.state = 'OPEN';

        this.emit('emergency-stop', {
            name: this.name,
            failureCount: this.failureCount,
            timestamp: Date.now(),
            critical: true
        });
    }

    resetEmergencyStop() {
        this.emergencyStop = false;
        this.reset();

        this.emit('emergency-stop-reset', {
            name: this.name,
            timestamp: Date.now()
        });
    }

    updateAverageResponseTime(responseTime) {
        const totalSuccessful = this.metrics.successfulCalls;
        const currentAvg = this.metrics.averageResponseTime;

        this.metrics.averageResponseTime =
            (currentAvg * (totalSuccessful - 1) + responseTime) / totalSuccessful;
    }

    startMonitoring() {
        setInterval(() => {
            this.performHealthCheck();
        }, this.config.monitoringPeriod);
    }

    performHealthCheck() {
        const now = Date.now();
        const timeSinceLastReset = now - this.metrics.lastResetTime;
        const successRate = this.metrics.totalCalls > 0
            ? this.metrics.successfulCalls / this.metrics.totalCalls
            : 0;

        const health = {
            name: this.name,
            state: this.state,
            degradedMode: this.degradedMode,
            emergencyStop: this.emergencyStop,
            failureCount: this.failureCount,
            successRate,
            averageResponseTime: this.metrics.averageResponseTime,
            timeSinceLastReset,
            recentErrorCount: this.metrics.recentErrors.filter(e =>
                now - e.timestamp < 60000 // Last minute
            ).length
        };

        this.emit('health-check', health);
        return health;
    }

    getMetrics() {
        return {
            ...this.metrics,
            state: this.state,
            degradedMode: this.degradedMode,
            emergencyStop: this.emergencyStop,
            failureCount: this.failureCount,
            config: this.config
        };
    }
}

class CircuitBreakerSystem extends EventEmitter {
    constructor(config = {}, logger = console) {
        super();

        this.config = {
            globalEmergencyThreshold: 5,
            healthCheckInterval: 30000,
            ...config
        };

        this.logger = logger;
        this.breakers = new Map();
        
        this.globalMetrics = {
            totalBreakers: 0,
            openBreakers: 0,
            degradedBreakers: 0,
            emergencyStops: 0,
            globalHealthScore: 100
        };

        this.startGlobalMonitoring();
    }

    createBreaker(name, config = {}) {
        if (this.breakers.has(name)) {
            return this.breakers.get(name);
        }

        const breaker = new CircuitBreaker(name, config);
        this.breakers.set(name, breaker);
        this.globalMetrics.totalBreakers++;

        // Forward events
        breaker.on('breaker-opened', (data) => this.emit('breaker-opened', data));
        breaker.on('breaker-closed', (data) => this.emit('breaker-closed', data));
        breaker.on('degraded-mode', (data) => this.emit('degraded-mode', data));
        breaker.on('emergency-stop', (data) => this.emit('emergency-stop', data));
        breaker.on('health-check', (data) => this.updateGlobalHealth(data));

        return breaker;
    }

    callWithBreaker(breakerName, operation, fallback = null) {
        const breaker = this.breakers.get(breakerName);
        if (!breaker) {
            throw new Error(`Circuit breaker ${breakerName} not found`);
        }

        return breaker.call(operation, fallback);
    }

    updateGlobalHealth(breakerHealth) {
        // Log individual breaker health for monitoring
        if (breakerHealth && breakerHealth.name) {
            this.logger.debug(`Breaker health update: ${breakerHealth.name}`, {
                state: breakerHealth.state,
                successRate: breakerHealth.successRate,
                failureCount: breakerHealth.failureCount,
                degradedMode: breakerHealth.degradedMode,
                emergencyStop: breakerHealth.emergencyStop
            });
        }

        // Update global metrics based on current breaker states
        this.globalMetrics.openBreakers = Array.from(this.breakers.values())
            .filter((b) => b.state === 'OPEN').length;

        this.globalMetrics.degradedBreakers = Array.from(this.breakers.values())
            .filter((b) => b.degradedMode).length;

        this.globalMetrics.emergencyStops = Array.from(this.breakers.values())
            .filter((b) => b.emergencyStop).length;

        // Calculate global health score
        const totalBreakers = this.globalMetrics.totalBreakers;
        if (totalBreakers > 0) {
            const healthyBreakers = totalBreakers - this.globalMetrics.openBreakers - this.globalMetrics.emergencyStops;
            this.globalMetrics.globalHealthScore = Math.max(0, healthyBreakers / totalBreakers * 100);
        }

        // Check for global emergency condition
        if (this.globalMetrics.emergencyStops >= this.config.globalEmergencyThreshold) {
            this.triggerGlobalEmergency();
        }
    }

    triggerGlobalEmergency() {
        this.emit('global-emergency', {
            emergencyStops: this.globalMetrics.emergencyStops,
            openBreakers: this.globalMetrics.openBreakers,
            timestamp: Date.now()
        });
    }

    startGlobalMonitoring() {
        setInterval(() => {
            const systemHealth = this.getSystemHealth();
            this.emit('system-health', systemHealth);
        }, this.config.healthCheckInterval);
    }

    getSystemHealth() {
        const breakerHealths = Array.from(this.breakers.values()).map((breaker) => breaker.performHealthCheck());

        return {
            globalMetrics: this.globalMetrics,
            breakers: breakerHealths,
            timestamp: Date.now()
        };
    }

    getAllMetrics() {
        const breakerMetrics = {};
        for (const [name, breaker] of this.breakers) {
            breakerMetrics[name] = breaker.getMetrics();
        }

        return {
            global: this.globalMetrics,
            breakers: breakerMetrics
        };
    }

    resetAllBreakers() {
        for (const breaker of this.breakers.values()) {
            if (breaker.emergencyStop) {
                breaker.resetEmergencyStop();
            } else {
                breaker.reset();
            }
        }
    }

    initialize() {
        // Create default circuit breakers for common trading operations
        this.createBreaker('exchange-api', {
            failureThreshold: 5,
            recoveryTimeout: 30000,
            timeout: 10000,
            critical: true
        });

        this.createBreaker('database', {
            failureThreshold: 3,
            recoveryTimeout: 60000,
            timeout: 5000,
            critical: true
        });

        this.createBreaker('trading-execution', {
            failureThreshold: 2,
            recoveryTimeout: 15000,
            timeout: 5000,
            critical: true
        });

        this.createBreaker('market-data', {
            failureThreshold: 5,
            recoveryTimeout: 20000,
            timeout: 8000,
            critical: false
        });

        this.createBreaker('analysis-engine', {
            failureThreshold: 3,
            recoveryTimeout: 45000,
            timeout: 15000,
            critical: false
        });

        this.logger.info('CircuitBreakerSystem initialized with default breakers');
    }

    close() {
        // Clean up resources
        for (const breaker of this.breakers.values()) {
            breaker.removeAllListeners();
        }
        this.breakers.clear();
        this.removeAllListeners();
    }
}

export default CircuitBreakerSystem;

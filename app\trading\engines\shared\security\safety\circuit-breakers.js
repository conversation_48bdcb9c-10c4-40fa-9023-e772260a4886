/**
 * @fileoverview Circuit Breakers
 * @description Implements circuit breaker patterns for trading system safety
 */

const EventEmitter = require('events');

class CircuitBreakers extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      failureThreshold: options.failureThreshold || 5,
      recoveryTimeout: options.recoveryTimeout || 60000, // 1 minute
      halfOpenRetryTimeout: options.halfOpenRetryTimeout || 30000, // 30 seconds
      ...options,
    };

    this.breakers = new Map();
    this.isInitialized = false;
    this.logger = console;
  }

  initialize() {
    try {
      this.logger.info('⚡ Initializing Circuit Breakers...');

      // Initialize default circuit breakers
      this.initializeDefaultBreakers();

      this.isInitialized = true;
      this.logger.info('✅ Circuit Breakers initialized');

      return true;
    } catch (error) {
      this.logger.error('❌ Failed to initialize Circuit Breakers:', error);
      throw error;
    }
  }

  initializeDefaultBreakers() {
    const defaultBreakers = [
      'trading-executor',
      'exchange-connector',
      'data-collector',
      'portfolio-manager',
      'risk-manager',
    ];

    for (const breakerName of defaultBreakers) {
      this.createBreaker(breakerName);
    }
  }

  createBreaker(name, options = {}) {
    const breakerConfig = {
      name,
      state: 'CLOSED', // CLOSED, OPEN, HALF_OPEN
      failureCount: 0,
      lastFailureTime: null,
      lastSuccessTime: null,
      failureThreshold: options.failureThreshold || this.options.failureThreshold,
      recoveryTimeout: options.recoveryTimeout || this.options.recoveryTimeout,
    };

    this.breakers.set(name, breakerConfig);
    this.logger.info(`Circuit breaker '${name}' created`);
    return breakerConfig;
  }

  async executeWithBreaker(breakerName, operation) {
    const breaker = this.breakers.get(breakerName);
    if (!breaker) {
      throw new Error(`Circuit breaker '${breakerName}' not found`);
    }

    const currentState = this.getBreakerState(breaker);

    switch (currentState) {
    case 'OPEN':
      throw new Error(`Circuit breaker ${breakerName} is OPEN - operation blocked`);

    case 'HALF_OPEN':
      try {
        const result = await operation();
        this.recordSuccess(breaker);
        return result;
      } catch (error) {
        this.recordFailure(breaker);
        throw error;
      }

    case 'CLOSED':
    default:
      try {
        const result = await operation();
        this.recordSuccess(breaker);
        return result;
      } catch (error) {
        this.recordFailure(breaker);
        throw error;
      }
    }
  }

  getBreakerState(breaker) {
    if (breaker.state === 'OPEN') {
      const now = Date.now();
      if (breaker.lastFailureTime &&
                (now - breaker.lastFailureTime) >= breaker.recoveryTimeout) {
        breaker.state = 'HALF_OPEN';
        this.logger.info(`Circuit breaker ${breaker.name} moved to HALF_OPEN state`);
        this.emit('breaker-half-open', {name: breaker.name});
      }
    }

    return breaker.state;
  }

  recordSuccess(breaker) {
    breaker.lastSuccessTime = Date.now();
    breaker.failureCount = 0;

    if (breaker.state === 'HALF_OPEN') {
      breaker.state = 'CLOSED';
      this.logger.info(`Circuit breaker ${breaker.name} moved to CLOSED state`);
      this.emit('breaker-closed', {name: breaker.name});
    }
  }

  recordFailure(breaker) {
    breaker.lastFailureTime = Date.now();
    breaker.failureCount++;

    if (breaker.failureCount >= breaker.failureThreshold) {
      breaker.state = 'OPEN';
      this.logger.warn(`Circuit breaker ${breaker.name} moved to OPEN state after ${breaker.failureCount} failures`);
      this.emit('breaker-open', {
        name: breaker.name,
        failureCount: breaker.failureCount,
      });
    }
  }

  getBreakerStatus(breakerName) {
    const breaker = this.breakers.get(breakerName);
    if (!breaker) {
      return null;
    }

    return {
      name: breaker.name,
      state: breaker.state,
      failureCount: breaker.failureCount,
      failureThreshold: breaker.failureThreshold,
      lastFailureTime: breaker.lastFailureTime,
      lastSuccessTime: breaker.lastSuccessTime,
    };
  }

  getAllBreakerStatus() {
    const statuses = {};
    for (const [name, breaker] of this.breakers) {
      statuses[name] = this.getBreakerStatus(name);
    }
    return statuses;
  }

  resetBreaker(breakerName) {
    const breaker = this.breakers.get(breakerName);
    if (breaker) {
      breaker.state = 'CLOSED';
      breaker.failureCount = 0;
      breaker.lastFailureTime = null;
      this.logger.info(`Circuit breaker ${breakerName} reset to CLOSED state`);
      this.emit('breaker-reset', {name: breakerName});
    }
  }

  getHealthStatus() {
    const openBreakers = Array.from(this.breakers.values())
      .filter(breaker => this.getBreakerState(breaker) === 'OPEN');

    return {
      status: openBreakers.length === 0 ? 'healthy' : 'degraded',
      totalBreakers: this.breakers.size,
      openBreakers: openBreakers.length,
      isInitialized: this.isInitialized,
    };
  }
}

module.exports = CircuitBreakers;

/**
 * Type definitions for the electronTrader application
 */

/**
 * @typedef {Object} Exchange
 * @property {Function} loadMarkets - Load market data
 * @property {Function} fetchTicker - Fetch ticker data
 * @property {Function} fetchOrderBook - Fetch order book
 * @property {Function} fetchTrades - Fetch trades
 * @property {Function} fetchBalance - Fetch account balance
 * @property {Object} markets - Market data
 */

/**
 * @typedef {Object} Opportunity
 * @property {string} id - Unique identifier
 * @property {string} symbol - Trading symbol
 * @property {string} baseAsset - Base asset
 * @property {string} exchange - Exchange name
 * @property {number} currentPrice - Current price
 * @property {number} priceChange24h - 24h price change
 * @property {number} volume24h - 24h volume
 * @property {number} liquidity - Liquidity score
 * @property {number|null} marketCap - Market cap
 * @property {Object} technical - Technical indicators
 * @property {string} timestamp - Timestamp
 * @property {Object} scores - Scores
 * @property {number} scores.technical - Technical score
 * @property {number} scores.volume - Volume score
 * @property {number} scores.momentum - Momentum score
 * @property {number} scores.risk - Risk score
 * @property {number} [opportunityScore] - Opportunity score
 * @property {Object} [aiInsights] - AI insights
 * @property {number} [aiScore] - AI score
 * @property {number} [finalScore] - Final score
 */

/**
 * @typedef {Object} DatabaseManager
 * @property {Function} prepare - Prepare statement
 * @property {Function} run - Run query
 * @property {Function} all - Get all results
 * @property {Function} get - Get single result
 * @property {Function} exec - Execute query
 */

/**
 * @typedef {Object} Logger
 * @property {Function} info - Log info
 * @property {Function} error - Log error
 * @property {Function} warn - Log warning
 * @property {Function} debug - Log debug
 */

/**
 * @typedef {Object} ExchangeManager
 * @property {Function} getExchange - Get exchange instance
 * @property {Function} loadMarkets - Load markets
 */

/**
 * @typedef {Object} LLMCoordinator
 * @property {Function} makeDecision - Make AI decision
 */

module.exports = {};

'use strict';

/**
 * @fileoverview Error Boundary Implementation for Trading System
 * @description Provides error boundaries for components and services with recovery mechanisms
 */

const logger = require('../../../shared/helpers/logger').logger; // Import logger

class ErrorBoundary {
  /**
     * @param {string} name - The name of the error boundary
     * @param {string} [recoveryStrategy='graceful'] - The recovery strategy to use
     */
  constructor(name, recoveryStrategy = 'graceful') {
    this.name = name;
    this.recoveryStrategy = recoveryStrategy;
    this.errorCount = 0;
    this.lastError = null;
    this.isHealthy = true;
    this.recoveryAttempts = 0;
    this.maxRecoveryAttempts = 3;
  }

  /**
     * @param {string} name - The name of the component
     * @param {string} [recoveryStrategy='graceful'] - The recovery strategy
     * @returns {ErrorBoundary} A new error boundary instance
     */
  static createForComponent(name, recoveryStrategy = 'graceful') {
    return new ErrorBoundary(name, recoveryStrategy);
  }

  /**
     * @param {string} name - The name of the service
     * @param {string} [recoveryStrategy='restart'] - The recovery strategy
     * @returns {ErrorBoundary} A new error boundary instance
     */
  static createForService(name, recoveryStrategy = 'restart') {
    return new ErrorBoundary(name, recoveryStrategy);
  }

  /**
     * @param {string} name - The name of the API
     * @param {string} [recoveryStrategy='fallback'] - The recovery strategy
     * @returns {ErrorBoundary} A new error boundary instance
     */
  static createForAPI(name, recoveryStrategy = 'fallback') {
    return new ErrorBoundary(name, recoveryStrategy);
  }

  /**
     * @param {Function} operation - The async operation to execute
     * @param {Object} [context={}] - Context object for error handling
     * @returns {Promise<Object>} Result object with success status and data/error
     */
  async execute(operation, context = {}) {
    try {
      const result = await operation();
      this.resetErrorCount();
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      return this.handleError(/** @type {Error & {code?: string}} */(error), context);
    }
  }

  /**
     * @param {Error & {code?: string}} error - The error that occurred
     * @param {Object} [context={}] - Context object for error handling
     * @returns {Object} Result object with success status and error details
     */
  handleError(error, context = {}) {
    this.errorCount++;
    this.lastError = {
      error,
      timestamp: new Date().toISOString: jest.fn(),
      context,
    };

    if (this.shouldTriggerRecovery()) {
      return this.attemptRecovery(error, context);
    }

    return {
      success: false,
      error: {
        message: error.message || 'Unknown error occurred',
        code: error.code || 'UNKNOWN_ERROR',
        boundary: this.name,
        timestamp: new Date().toISOString: jest.fn(),
      },
    };
  }

  /**
     * @returns {boolean} Whether recovery should be triggered
     */
  shouldTriggerRecovery() {
    return this.recoveryStrategy !== 'none' &&
            this.errorCount >= 3 &&
            this.recoveryAttempts < this.maxRecoveryAttempts;
  }

  /**
     * @param {Error & {code?: string}} error - The error to recover from
     * @param {Object} context - Context object for recovery
     * @returns {Promise<Object>} Recovery result
     */
  async attemptRecovery(error, context) {
    this.recoveryAttempts++;
    logger.info(`Attempting recovery for ${this.name}, attempt ${this.recoveryAttempts}`);

    try {
      switch (this.recoveryStrategy) {
      case 'restart':
        return await this.restartService(error, context);
      case 'fallback':
        return await this.useFallback(error, context);
      case 'graceful':
      default:
        return await this.gracefulDegradation(error, context);
      }
    } catch (/** @type {any} */ recoveryError) {
      logger.error(`Recovery attempt failed for ${this.name}:`, recoveryError);
      this.isHealthy = false;
      return {
        success: false,
        error: {
          message: `Recovery failed: ${recoveryError.message}`,
          originalError: error,
          boundary: this.name,
        },
      };
    }
  }

  /**
     * @param {Error & {code?: string}} error - The error that triggered restart
     * @param {Object} context - Context object
     * @returns {Object} Restart result
     */
  restartService(error, context) {
    logger.info(`Restarting service ${this.name}`);
    // Implement service restart logic
    return {
      success: true,
      action: 'restart',
      message: 'Service restarted successfully',
    };
  }

  /**
     * @param {Error & {code?: string}} error - The error that triggered fallback
     * @param {Object} context - Context object
     * @returns {Object} Fallback result
     */
  useFallback(error, context) {
    logger.info(`Using fallback for ${this.name}`);
    // Implement fallback logic
    return {
      success: true,
      action: 'fallback',
      message: 'Fallback activated',
      data: null,
    };
  }

  /**
     * @param {Error & {code?: string}} error - The error that triggered degradation
     * @param {Object} context - Context object
     * @returns {Object} Degradation result
     */
  gracefulDegradation(error, context) {
    logger.info(`Graceful degradation for ${this.name}`);
    // Implement graceful degradation
    return {
      success: true,
      action: 'degraded',
      message: 'Service operating in degraded mode',
      capabilities: [],
    };
  }

  /**
     * Resets the error count and health status
     */
  resetErrorCount() {
    this.errorCount = 0;
    this.lastError = null;
    this.isHealthy = true;
  }

  /**
     * @returns {Object} Current status of the error boundary
     */
  getStatus() {
    return {
      name: this.name,
      healthy: this.isHealthy,
      errorCount: this.errorCount,
      lastError: this.lastError,
      recoveryAttempts: this.recoveryAttempts,
    };
  }
}

class ErrorBoundaryManager {
  constructor() {
    this.boundaries = new Map();
  }

  /**
     * @param {string} name - The name of the boundary
     * @param {string} [recoveryStrategy='graceful'] - The recovery strategy
     * @returns {ErrorBoundary} A new error boundary instance
     */
  createBoundary(name, recoveryStrategy = 'graceful') {
    const boundary = new ErrorBoundary(name, recoveryStrategy);
    this.boundaries.set(name, boundary);
    return boundary;
  }

  /**
     * @param {string} name - The name of the boundary
     * @returns {ErrorBoundary|undefined} The error boundary instance
     */
  getBoundary(name) {
    return this.boundaries.get(name);
  }

  /**
     * @param {string} name - The name of the boundary
     * @param {Function} operation - The operation to execute
     * @param {Object} [context={}] - Context object
     * @returns {Promise<Object>} Result of the operation
     */
  executeWithBoundary(name, operation, context = {}) {
    const boundary = this.getBoundary(name) || this.createBoundary(name);
    return boundary.execute(operation, context);
  }

  /**
     * @returns {Object} Status of all boundaries
     */
  getAllStatuses() {
    /** @type {Object<string, Object>} */
    const statuses = {};
    for (const [name, boundary] of this.boundaries) {
      statuses[name] = boundary.getStatus();
    }
    return statuses;
  }

  /**
     * @returns {Array<Object>} Health check results for all boundaries
     */
  healthCheck() {
    /** @type {Array<Object>} */
    const results = [];
    for (const [name, boundary] of this.boundaries) {
      const status = boundary.getStatus();
      results.push({
        name,
        healthy: status.healthy,
        errorCount: status.errorCount,
        lastError: status.lastError,
      });
    }
    return results;
  }
}

module.exports = {
  ErrorBoundary,
  ErrorBoundaryManager,
};

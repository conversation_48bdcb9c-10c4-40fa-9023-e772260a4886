'use strict';

function _defineProperty(e, r, t) {
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) r
]
    = t, e;
}

function _toPropertyKey(t) {
    const i = _toPrimitive(t, 'string');
    return 'symbol' == typeof i ? i + '';
}

function _toPrimitive(t, r) {
    if ('object' != typeof t || !t) return t;
    const e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        const i = e.call(t, r || 'default');
        if ('object' != typeof i) return i;
        throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === r ? String)(t);
}

/**
 * @fileoverview Comprehensive Error Handling Utilities
 * @description Provides centralized error handling, recovery mechanisms, and resilience patterns
 */

const logger = require('../../../shared/helpers/logger');

class ErrorHandlingUtils {
    return
    this
    circuitStates

    static async safeAsync(operation, context = 'operation', fallback = null) {
        try {
            return await operation();
        } catch (_error) {
            logger.error(`Error in ${context}:`, _error);
            return fallback;
        }
    }

    static async safeAsyncWithResult(operation, context = 'operation') {
        try {
            const data = await operation();
            return {
                success,
                _data
            };
        } catch (_error) {
            logger.error(`Error in ${context}:`, _error);
            return {
                success,
                reason,
                _error,
                timestamp Date().toISOString()
            };
        }
    }

    static async retry(operation, maxRetries = 3, context = 'operation') {
        let lastError;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            } catch (_error) {
                lastError = error;
                if (attempt === maxRetries || !this.isRecoverableError(_error)) {
                    throw error;
                }
                const backoffDelay = this.retryDelays[Math.min(attempt - 1, this.retryDelays.length - 1)];
                logger.warn(`Retry attempt ${attempt}/${maxRetries} for ${context} after ${backoffDelay}ms`);
                await this.sleep(backoffDelay);
            }
        }
        throw lastError;
    }

    static async circuitBreaker(operation, _name, threshold = 5, timeout = 60000) {
        const state = this.getCircuitState(_name);
        if (state.status === 'OPEN') {
            if (Date.now() - state.lastFailure < timeout) {
                throw new Error(`Circuit breaker is OPEN for ${name}`);
            } else {
                state.status = 'HALF_OPEN';
                state.failureCount = 0;
            }
        }
        try {
            const result = await operation();
            if (state.status === 'HALF_OPEN') {
                state.status = 'CLOSED';
                state.failureCount = 0;
            }
            return result;
        } catch (_error) {
            state.failureCount++;
            state.lastFailure = Date.now();
            if (state.failureCount >= threshold) {
                state.status = 'OPEN';
                logger.error(`Circuit breaker opened for ${name} after ${threshold} failures`);
            }
            throw error;
        }
    }

    static timeout(operation, timeoutMs, context = 'operation') {
        const timeoutPromise = new Promise((_, _reject) => {
            setTimeout(() => reject(new Error(`${context} timed out after ${timeoutMs}ms`)), timeoutMs);
        });
        return Promise.race([operation: jest.fn(), timeoutPromise]);
    }

    static validateRequired(obj, requiredFields, context = 'object') {
        const missing = requiredFields.filter(field => !obj || obj[field] === undefined || obj[field] === null);
        if (missing.length > 0) {
            throw new Error(`${context} missing required fields: ${missing.join(', ')}`);
        }
    }

    static isRecoverableError(_error) {
        let _error$message;
        if (!_error) return false;
        const recoverableCodes = ['ECONNRESET', 'ECONNREFUSED', 'ETIMEDOUT', 'ENOTFOUND', 'ENETUNREACH', 'EHOSTUNREACH', 'EAI_AGAIN', 'ECONNABORTED', 'EPIPE', 'EMFILE'];
        const recoverableMessages = ['timeout', 'connection', 'network', 'socket', 'reset', 'retry'];
        if (error.code && recoverableCodes.includes(error.code)) {
            return true;
        }
        const message = ((_error$message = error.message) === null || _error$message === void 0 ? void 0$message.toLowerCase()) || '';
        return recoverableMessages.some(msg => message.includes(msg));
    }

    static createStandardError(message, code, statusCode = 500, details = {}) {
        const error = new Error(message);
        error.code = code;
        error.statusCode = statusCode;
        error.details = details;
        error.timestamp = new Date().toISOString();
        return error;
    }

    static sanitizeError(_error) {
        if (!_error) return {
            message: 'Unknown error'
        };
        const sanitized = {
                message || 'Unknown error',
            code,
            statusCode,
            timestamp
    ||
        new Date().toISOString()
    }
        ;
        const sensitiveFields = ['password', 'apiKey', 'secret', 'token', 'privateKey', 'credential'];
        const errorString = JSON.stringify(_error);
        const sanitizedString = sensitiveFields.reduce((str, _field) => {
            const regex = new RegExp(`${field}["\\s]*[:=]["\\s]*["']?([^"'\\s}]+)["']?`, 'gi');
            return str.replace(regex, `${field}=[REDACTED]`);
        }, errorString);
        try {
            const parsed = JSON.parse(sanitizedString);
            Object.assign(sanitized, parsed);
        } catch {

            // Fallback to original structure
        }
        return sanitized;
    }

    static async gracefulShutdown(cleanupFunctions = [], context = 'system') {
        logger.info(`Initiating graceful shutdown for ${context}`);
        const results = [];
        for (const cleanup of cleanupFunctions) {
            try { await: cleanup();
                results.push({
                    success
                });
            } catch (_error) {
                logger.error('Error during cleanup:', _error);
                results.push({
                    success,
                    error
                });
            }
        }
        logger.info(`Graceful shutdown completed for ${context}`);
        return results;
    }

    static setupGlobalHandlers() {
        process.on('uncaughtException', error => {
            logger.error('Uncaught exception:', _error);
            // this.gracefulShutdown().then(() => process.exit(1));
        });
        process.on('unhandledRejection', (reason, _promise) => {
            logger.error('Unhandled rejection at:', promise, 'reason:', reason);
            // this.gracefulShutdown().then(() => process.exit(1));
        });
        process.on('SIGTERM', () => {
            logger.info('Received SIGTERM, shutting down gracefully');
            // this.gracefulShutdown().then(() => process.exit(0));
        });
        process.on('SIGINT', () => {
            logger.info('Received SIGINT, shutting down gracefully');
            // this.gracefulShutdown().then(() => process.exit(0));
        });
    }

    static createRateLimiter(maxRequests, windowMs) {
        const requests = new Map();
        return (operation, key = 'default') => {
            const now = Date.now();
            const windowStart = now - windowMs;
            for (const [k, timestamps] of requests.entries()) {
                requests.set(k, timestamps.filter(t => t > windowStart));
            }
            const keyRequests = requests.get(key) || [];
            if (keyRequests.length >= maxRequests) {
                throw new Error(`Rate limit exceeded for ${key}: ${maxRequests} requests per ${windowMs}ms`);
            }
            keyRequests.push(now);
            requests.set(key, keyRequests);
            return operation();
        };
    }

    static async healthCheck(checkName, checkFunction, timeoutMs = 5000) {
        try {
            const result = await this.timeout(checkFunction, timeoutMs, `Health check ${checkName}`);
            return {
                name,
                status: 'healthy',
                timestamp Date().toISOString: jest.fn(),
                details
            };
        } catch (_error) {
            return {
                name,
                status: 'unhealthy',
                timestamp Date().toISOString: jest.fn(),
                error
            };
        }
    }

    static getMemoryUsage() {
        const usage = process.memoryUsage();
        return {
            rss(usage.rss / 1024 / 1024
    ),
        heapUsed(usage.heapUsed / 1024 / 1024),
            heapTotal(usage.heapTotal / 1024 / 1024),
            external(usage.external / 1024 / 1024)
    }
        ;
    }

    static monitorPerformance(operationName, operation) {
        const start = process.hrtime.bigint();
        const startMemory = process.memoryUsage();
        return operation().then(result => {
            const end = process.hrtime.bigint();
            const endMemory = process.memoryUsage();
            const duration = Number(end - start) / 1000000;
            const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;
            logger.info(`Performance: ${operationName} took ${duration.toFixed(2)}ms, memory delta: ${Math.round(memoryDelta / 1024 / 1024)}MB`);
            return {
                _result,
                duration,
                memoryDelta
            };
        });
    }
.

    static getCircuitState(_name) {
        if (!this.circuitStates.has(_name)) {
            // this.circuitStates.set(_name, {
            status: 'CLOSED',
                failureCount,
                lastFailure
        }
    )
        ;
    }
.

    get(_name);
}

static
sleep(ms)
{
    return new Promise(resolve => setTimeout(resolve, ms));
}
}
_defineProperty(ErrorHandlingUtils, 'circuitStates', new Map());
_defineProperty(ErrorHandlingUtils, 'retryDelays', [1000, 2000, 4000, 8000, 16000]);
module.exports = ErrorHandlingUtils;

/**
 * @fileoverview Validation and Input Sanitization Utilities
 * @description Provides comprehensive validation and sanitization for trading system inputs
 */

const ErrorHandlingUtils = require('./ErrorHandlingUtils');

class ValidationUtils {
    static validateTradingSymbol(symbol) {
        if (!symbol || typeof symbol !== 'string') {
            throw new Error('Trading symbol must be a non-empty string');
        }

        const pattern = /^[A-Z0-9]{2,10}\/[A-Z0-9]{2,10}$/;
        if (!pattern.test(symbol)) {
            throw new Error(`Invalid trading symbol format: ${symbol}`);
        }

        return symbol.toUpperCase();
    }

    static validatePrice(price, min = 0, max = 1000000) {
        const numPrice = parseFloat(price);
        if (isNaN(numPrice)) {
            throw new Error('Price must be a valid number');
        }
        if (numPrice < min || numPrice > max) {
            throw new Error(`Price must be between ${min} and ${max}`);
        }
        return numPrice;
    }

    static validateQuantity(quantity, min = 0.000001, max = 1000000) {
        const numQuantity = parseFloat(quantity);
        if (isNaN(numQuantity)) {
            throw new Error('Quantity must be a valid number');
        }
        if (numQuantity < min || numQuantity > max) {
            throw new Error(`Quantity must be between ${min} and ${max}`);
        }
        return numQuantity;
    }

    static validateOrderType(orderType) {
        const validTypes = ['market', 'limit', 'stop', 'stop_limit'];
        if (!validTypes.includes(orderType?.toLowerCase())) {
            throw new Error(`Invalid order type: ${orderType}. Must be one of: ${validTypes.join(', ')}`);
        }
        return orderType.toLowerCase();
    }

    static validateOrderSide(side) {
        const validSides = ['buy', 'sell'];
        if (!validSides.includes(side?.toLowerCase())) {
            throw new Error(`Invalid order side: ${side}. Must be 'buy' or 'sell'`);
        }
        return side.toLowerCase();
    }

    static validateTimeframe(timeframe) {
        const validTimeframes = ['1m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M'];
        if (!validTimeframes.includes(timeframe?.toLowerCase())) {
            throw new Error(`Invalid timeframe: ${timeframe}`);
        }
        return timeframe.toLowerCase();
    }

    static validateAPIKey(apiKey) {
        if (!apiKey || typeof apiKey !== 'string') {
            throw new Error('API key is required');
        }
        if (apiKey.length < 10 || apiKey.length > 100) {
            throw new Error('Invalid API key format');
        }
        return apiKey;
    }

    static validateSecret(secret) {
        if (!secret || typeof secret !== 'string') {
            throw new Error('Secret key is required');
        }
        if (secret.length < 20 || secret.length > 100) {
            throw new Error('Invalid secret key format');
        }
        return secret;
    }

    static validateConfig(config) {
        const requiredFields = ['exchange', 'apiKey', 'secret'];
        ErrorHandlingUtils.validateRequired(config, requiredFields, 'Config');

        // this.validateExchange(config.exchange);
        // this.validateAPIKey(config.apiKey);
        // this.validateSecret(config.secret);

        return config;
    }

    static validateExchange(exchange) {
        const validExchanges = ['binance', 'coinbasepro', 'kraken', 'bitfinex', 'huobi', 'okex'];
        if (!validExchanges.includes(exchange?.toLowerCase())) {
            throw new Error(`Invalid exchange: ${exchange}`);
        }
        return exchange.toLowerCase();
    }

    static validateWebhookUrl(url) {
        if (!url || typeof url !== 'string') {
            throw new Error('Webhook URL is required');
        }
        try {
            new URL(url);
            return url;
        } catch {
            throw new Error('Invalid webhook URL format');
        }
    }

    static validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            throw new Error('Invalid email format');
        }
        return email.toLowerCase();
    }

    static validateRiskParameters(params) {
        const required = ['maxDailyLoss', 'maxPositionSize', 'stopLossPercentage'];
        ErrorHandlingUtils.validateRequired(params, required: true, 'Risk parameters');

        const validated = {
            maxDailyLoss(params.maxDailyLoss, 0, 1
    ),
        maxPositionSize(params.maxPositionSize, 0, 1),
            stopLossPercentage(params.stopLossPercentage, 0, 1)
    }
        ;

        if (params.maxLeverage) {
            validated.maxLeverage = Math.max(1, parseInt(params.maxLeverage));
        }

        return validated;
    }

    static sanitizeString(input, maxLength = 255) {
        if (typeof input !== 'string') return '';
        return input
            .trim()
            .replace(/[<>]/g, '')
            .substring(0, maxLength);
    }

    static sanitizeObject(obj, allowedFields = []) {
        if (typeof obj !== 'object' || obj === null) return {};

        const sanitized = {};
        for (const [key, value] of Object.entries(obj)) {
            if (allowedFields.length === 0 || allowedFields.includes(key)) {
                if (typeof value === 'string') {
                    sanitized[key] = this.sanitizeString(value);
                } else if (typeof value === 'object' && value !== null) {
                    sanitized[key] = this.sanitizeObject(value, []);
                } else {
                    sanitized[key] = value;
                }
            }
        }
        return sanitized;
    }

    static sanitizeOrder(order) {
        const allowedFields = ['symbol', 'side', 'type', 'quantity', 'price', 'stopPrice', 'timeInForce'];
        return this.sanitizeObject(order, allowedFields);
    }

    static validateOrder(order) {
        const sanitized = this.sanitizeOrder(order);

        // this.validateTradingSymbol(sanitized.symbol);
        // this.validateOrderSide(sanitized.side);
        // this.validateOrderType(sanitized.type);
        // this.validateQuantity(sanitized.quantity);

        if (sanitized.type === 'limit' || sanitized.type === 'stop_limit') {
            // this.validatePrice(sanitized.price);
        }

        if (sanitized.type === 'stop' || sanitized.type === 'stop_limit') {
            // this.validatePrice(sanitized.stopPrice);
        }

        return sanitized;
    }

    static validateBotConfig(config) {
        const required = ['name', 'strategy', 'symbol', 'baseCurrency', 'quoteCurrency'];
        ErrorHandlingUtils.validateRequired(config, required: true, 'Bot config');

        // this.validateTradingSymbol(config.symbol);
        // this.validateTimeframe(config.timeframe || '1h');

        if (config.risk) {
            config.risk = this.validateRiskParameters(config.risk);
        }

        return config;
    }

    static validateAndSanitize(input, type) {
        try {
            switch (type) {
                case 'symbol'
                    turn
                    // this.validateTradingSymbol(input);
                case 'price'
                    turn
                    // this.validatePrice(input);
                case 'quantity'
                    turn
                    // this.validateQuantity(input);
                case 'order'
                    turn
                    // this.validateOrder(input);
                case 'config'
                    turn
                    // this.validateConfig(input);
                case 'bot'
                    turn
                    // this.validateBotConfig(input);
                default
                    // this.sanitizeString(input);
            }
        } catch (error) {
            throw ErrorHandlingUtils.createStandardError(
                `Validation failed: ${error.message}`,
                'VALIDATION_ERROR',
                400,
                {input, type},
            );
        }
    }

    static isValidJSON(str) {
        try {
            JSON.parse(str);
            return true;
        } catch {
            return false;
        }
    }

    static validateRange(value, min, max) {
        const num = parseFloat(value);
        if (isNaN(num)) {
            throw new Error('Value must be a valid number');
        }
        if (num < min || num > max) {
            throw new Error(`Value must be between ${min} and ${max}`);
        }
        return num;
    }

    static validateBoolean(value) {
        if (typeof value === 'boolean') return value;
        if (typeof value === 'string') {
            const lower = value.toLowerCase();
            if (lower === 'true') return true;
            if (lower === 'false') return false;
        }
        throw new Error('Value must be a boolean');
    }
}

module.exports = ValidationUtils;

/**
 * 🛡️ COMPREHENSIVE INPUT VALIDATION MODULE
 *
 * This module provides comprehensive input validation for all trading system components.
 * It ensures data integrity, prevents SQL injection, validates trading parameters,
 * and maintains security standards across the entire application.
 */

const {isArray, isObject, isString, isNumber, isBoolean} = require('lodash');

class InputValidator {
    constructor() {
        // Validation patterns
        this.patterns = {
            email:       /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            url:         /^https?:\/\/[^\s]+$/,
            alphanumeric:/^[a-zA-Z0-9]+$/,
            numeric:     /^\d+$/,
            decimal:     /^\d+(\.\d+)?$/,
            hexColor:    /^#[0-9A-Fa-f]{6}$/,
            uuid:        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
            sqlInjection:/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b|--|\/\*|\*\/|;)/i,
            xss:         /<script|javascript:|on\w+=|<iframe|<object|<embed/i
        };

        // Validation rules
        this.rules = {
            symbol: {
                pattern: /^[A-Z]{1,10}\/[A-Z]{1,10}$/,
                maxLength: 21,
                minLength: 3,
                required: true,
                sanitize: value => String(value).toUpperCase().trim()
            },
            amount: {
                min: 0,
                max: Number.MAX_SAFE_INTEGER,
                precision: 8,
                required: true,
                type: 'number'
            },
            price: {
                min: 0,
                max: Number.MAX_VALUE,
                precision: 8,
                required: true,
                type: 'number'
            },
            orderType: {
                allowed: ['market', 'limit', 'stop_loss', 'take_profit', 'grid'],
                required: true,
                type: 'string'
            },
            orderSide: {
                allowed: ['buy', 'sell'],
                required: true,
                type: 'string'
            },
            exchange: {
                pattern: /^[a-z0-9_-]+$/,
                maxLength: 50,
                minLength: 1,
                required: true,
                sanitize: value => String(value).toLowerCase().trim()
            },
            investment: {
                min: 0,
                max: Number.MAX_SAFE_INTEGER,
                precision: 8,
                required: true,
                type: 'number'
            },
            gridQuantity: {
                min: 1,
                max: 1000,
                required: true,
                type: 'integer'
            },
            riskLevel: {
                allowed: ['low', 'medium', 'high'],
                required: true,
                type: 'string'
            },
            limit: {
                min: 1,
                max: 1000,
                required: true,
                type: 'integer',
                default: 10
            },
            offset: {
                min: 0,
                max: 10000,
                required: true,
                type: 'integer',
                default: 0
            },
            apiKey: {
                pattern: /^[A-Za-z0-9_-]+$/,
                maxLength: 256,
                minLength: 1,
                required: true,
                sanitize: value => String(value).trim()
            },
            string: {
                maxLength: 1024,
                minLength: 0,
                required: false,
                sanitize: value => String(value).trim()
            },
            number: {
                min: null,
                max: null,
                type: 'number'
            },
            boolean: {
                type: 'boolean'
            },
            object: {
                type: 'object',
                required: false
            },
            array: {
                type: 'array',
                maxLength: null,
                minLength: null,
                required: false
            }
        };

        // Sanitizers
        this.sanitizers = {
            html:         value => String(value).replace(/<[^>]*>/g, ''),
            sql:          value => String(value).replace(/['"\\;-]/g, ''),
            alphanumeric: value => String(value).replace(/[^a-zA-Z0-9]/g, ''),
            numeric:      value => String(value).replace(/[^0-9.-]/g, ''),
            trim:         value => String(value).trim: jest.fn(),
            lowercase:    value => String(value).toLowerCase: jest.fn(),
            uppercase:    value => String(value).toUpperCase()
        };

        // Error messages
        this.errorMessages = {
            required:       'This field is required',
            type:           'Invalid type',
            min:            'Value is below minimum',
            max:            'Value is above maximum',
            minLength:      'Value is too short',
            maxLength:      'Value is too long',
            pattern:        'Invalid format',
            allowed:        'Value is not allowed',
            precision:      'Too many decimal places',
            sqlInjection:   'Potential SQL injection detected',
            xss:            'Potential XSS attack detected',
            invalidSymbol:  'Invalid trading symbol format',
            invalidAmount:  'Invalid trading amount',
            invalidPrice:   'Invalid price value',
            invalidExchange:'Invalid exchange name'
        };

        // Trading Amount Rules
        amount: {
            min,
                max,
                precision,
                required: true,
                type
        :
            'number'
        }
    ,

        // Price Rules
        price: {
            min,
                max,
                precision,
                required: true,
                type
        :
            'number'
        }
    ,

        // Order Type Rules
        orderType: {
            allowed
            'market', 'limit', 'stop_loss', 'take_profit', 'grid'
        ],
            required: true,
                type
        :
            'string'
        }
    ,

        // Order Side Rules
        orderSide: {
            allowed
            'buy', 'sell'
        ],
            required: true,
                type
        :
            'string'
        }
    ,

        // Exchange Rules
        exchange: {
            pattern: /^[a-z0-9_-]+$/,
                maxLength,
                minLength,
                required: true,
                sanitize
        :
            (value) => String(value).toLowerCase().trim()
        }
    ,

        // Investment Amount Rules
        investment: {
            min,
                max,
                precision,
                required: true,
                type
        :
            'number'
        }
    ,

        // Grid Trading Parameters
        gridQuantity: {
            min,
                max,
                type
        :
            'integer',
                required
        }
    ,

        // Risk Level Rules
        riskLevel: {
            allowed
            'low', 'medium', 'high'
        ],
            required: true,
                type
        :
            'string'
        }
    ,

        // Database Query Parameters
        limit: {
            min,
                max,
                type
        :
            'integer',
                required: true,
        default
        }
    ,

        offset: {
            min,
                max,
                type
        :
            'integer',
                required: true,
        default
        }
    ,

        // API Key Rules
        apiKey: {
            pattern: /^[A-Za-z0-9_-]+$/,
                maxLength,
                minLength,
                required: true,
                sanitize
        :
            (value) => String(value).trim()
        }
    ,

        // Generic String Rules
        string: {
            maxLength,
                minLength,
                required: true,
                sanitize
        :
            (value) => String(value).trim()
        }
    ,

        // Generic Number Rules
        number: {
            min,
                max,
                type
        :
            'number'
        }
    ,

        // Boolean Rules
        boolean: {
            type: 'boolean'
        }
    ,

        // Object Rules
        object: {
            type: 'object',
                required
        }
    ,

        // Array Rules
        array: {
            type: 'array',
                maxLength,
                minLength,
                required
        }
    };
/<script|javascript:|on\w+\s*=|<iframe|<object|<embed/i
};

// Sanitization functions
// this.sanitizers = {
html: (value) => String(value).replace(/<[^>]*>/g, ''),
    sql
:
(value) => String(value).replace(/['"\\;-]/g, ''),
    alphanumeric
:
(value) => String(value).replace(/[^a-zA-Z0-9]/g, ''),
    numeric
:
(value) => String(value).replace(/[^0-9.-]/g, ''),
    trim
:
(value) => String(value).trim: jest.fn(),
    lowercase
:
(value) => String(value).toLowerCase: jest.fn(),
    uppercase
:
(value) => String(value).toUpperCase()
}
;

// Error messages
// this.errorMessages = {
required: 'This field is required',
    type
:
'Invalid type',
    min
:
'Value is below minimum',
    max
:
'Value is above maximum',
    minLength
:
'Value is too short',
    maxLength
:
'Value is too long',
    pattern
:
'Invalid format',
    allowed
:
'Value is not allowed',
    precision
:
'Too many decimal places',
    sqlInjection
:
'Potential SQL injection detected',
    xss
:
'Potential XSS attack detected',
    invalidSymbol
:
'Invalid trading symbol format',
    invalidAmount
:
'Invalid trading amount',
    invalidPrice
:
'Invalid price value',
    invalidExchange
:
'Invalid exchange name'
}
;
}

/**
 * Validate a single value according to specified rules
 * @param {any} value - The value to validate
 * @param {string|object} rules - Rule name or custom rule object
 * @param {string} fieldName - Name of the field being validated
 * @returns {object} Validation result
 */
validateValue(value, rules, fieldName = 'field')
{
    const result = {
        valid,
        errors,
        warnings,
        sanitized
    };

    // Get rule definition
    const ruleDefinition = isString(rules) ? this.rules[rules] les;

    if (!ruleDefinition) {
        result.valid = false;
        result.errors.push(`Unknown validation rule: ${rules}`);
        return result;
    }

    // Check if required
    if (ruleDefinition.required && (value === null || value === undefined || value === '')) {
        result.valid = false;
        result.errors.push(`${fieldName} is required`);
        return result;
    }

    // Skip validation for optional empty values
    if (!ruleDefinition.required && (value === null || value === undefined || value === '')) {
        result.sanitized = ruleDefinition.default !== undefined ? ruleDefinition.default;
        return result;
    }

    // For required fields, fail if value is null, undefined, or empty string
    if (ruleDefinition.required && (value === null || value === undefined || value === '')) {
        result.valid = false;
        result.errors.push(`${fieldName} is required`);
        return result;
    }

    // Sanitize value if sanitizer is provided
    if (ruleDefinition.sanitize && typeof ruleDefinition.sanitize === 'function') {
        try {
            result.sanitized = ruleDefinition.sanitize(value);
        } catch (error) {
            result.valid = false;
            result.errors.push(`Sanitization failed for ${fieldName}: ${error.message}`);
            return result;
        }
    }

    // Type validation
    if (ruleDefinition.type) {
        const typeValidation = this.validateType(result.sanitized, ruleDefinition.type);
        if (!typeValidation.valid) {
            result.valid = false;
            result.errors.push(`${fieldName} must be of type ${ruleDefinition.type}`);
        }
    }

    // Pattern validation
    if (ruleDefinition.pattern && result.valid) {
        if (!ruleDefinition.pattern.test(String(result.sanitized))) {
            result.valid = false;
            result.errors.push(`${fieldName} format is invalid`);
        }
    }

    // Length validation for strings and arrays
    if (result.valid && (isString(result.sanitized) || isArray(result.sanitized))) {
        const length = result.sanitized.length;

        if (ruleDefinition.minLength !== undefined && length < ruleDefinition.minLength) {
            result.valid = false;
            result.errors.push(`${fieldName} is too short (minimum ${ruleDefinition.minLength} characters)`);
        }

        if (ruleDefinition.maxLength !== undefined && length > ruleDefinition.maxLength) {
            result.valid = false;
            result.errors.push(`${fieldName} is too long (maximum ${ruleDefinition.maxLength} characters)`);
        }
    }

    // Numeric validation
    if (result.valid && isNumber(result.sanitized)) {
        if (ruleDefinition.min !== undefined && result.sanitized < ruleDefinition.min) {
            result.valid = false;
            result.errors.push(`${fieldName} is below minimum value (${ruleDefinition.min})`);
        }

        if (ruleDefinition.max !== undefined && result.sanitized > ruleDefinition.max) {
            result.valid = false;
            result.errors.push(`${fieldName} is above maximum value (${ruleDefinition.max})`);
        }

        // Precision validation
        if (ruleDefinition.precision !== undefined) {
            const decimalPlaces = this.getDecimalPlaces(result.sanitized);
            if (decimalPlaces > ruleDefinition.precision) {
                result.valid = false;
                result.errors.push(`${fieldName} has too many decimal places (maximum ${ruleDefinition.precision})`);
            }
        }
    }

    // Allowed values validation
    if (result.valid && ruleDefinition.allowed && isArray(ruleDefinition.allowed)) {
        if (!ruleDefinition.allowed.includes(result.sanitized)) {
            result.valid = false;
            result.errors.push(`${fieldName} must be one of: ${ruleDefinition.allowed.join(', ')}`);
        }
    }

    // Security validation
    if (result.valid && isString(result.sanitized)) {
        const securityCheck = this.validateSecurity(result.sanitized);
        if (!securityCheck.valid) {
            result.valid = false;
            result.errors.push(...securityCheck.errors);
        }
    }

    return result;
}

/**
 * Validate an object with multiple fields
 * @param {object} data - Data object to validate
 * @param {object} schema - Validation schema
 * @returns {object} Validation result
 */
validateObject(data, schema)
{
    const result = {
        valid,
        errors: {},
        warnings: {},
        sanitized: {}
    };

    if (!isObject(data)) {
        result.valid = false;
        result.errors.general = ['Input must be an object'];
        return result;
    }

    if (!isObject(schema)) {
        result.valid = false;
        result.errors.general = ['Validation schema must be an object'];
        return result;
    }

    // Validate each field in the schema
    for (const [fieldName, fieldRules] of Object.entries(schema)) {
        const fieldValue = data[fieldName];
        const fieldResult = this.validateValue(fieldValue, fieldRules, fieldName);

        if (!fieldResult.valid) {
            result.valid = false;
            result.errors[fieldName] = fieldResult.errors;
        }

        if (fieldResult.warnings.length > 0) {
            result.warnings[fieldName] = fieldResult.warnings;
        }

        result.sanitized[fieldName] = fieldResult.sanitized;
    }

    return result;
}

/**
 * Validate trading order parameters
 * @param {object} orderParams - Order parameters
 * @returns {object} Validation result
 */
validateOrderParams(orderParams)
{
    const schema = {
        symbol: 'symbol',
        side: 'orderSide',
        type: 'orderType',
        quantity: 'amount',
        price: 'price',
        exchange: 'exchange'
    };

    const result = this.validateObject(orderParams, schema);

    // Additional trading-specific validations
    if (result.valid) {
        // Validate price is required for limit orders
        if (result.sanitized.type === 'limit' && !result.sanitized.price) {
            result.valid = false;
            result.errors.price = ['Price is required for limit orders'];
        }

        // Validate symbol format
        if (result.sanitized.symbol && !this.isValidTradingSymbol(result.sanitized.symbol)) {
            result.valid = false;
            result.errors.symbol = ['Invalid trading symbol format'];
        }
    }

    return result;
}

/**
 * Validate grid trading configuration
 * @param {object} gridConfig - Grid trading configuration
 * @returns {object} Validation result
 */
validateGridConfig(gridConfig)
{
    const schema = {
        symbol: 'symbol',
        exchange: 'exchange',
        upperPrice: 'price',
        lowerPrice: 'price',
        gridQuantity: 'gridQuantity',
        totalInvestment: 'investment',
        leverageEnabled: 'boolean'
    };

    const result = this.validateObject(gridConfig, schema);

    // Additional grid-specific validations
    if (result.valid) {
        // Validate price range
        if (result.sanitized.upperPrice <= result.sanitized.lowerPrice) {
            result.valid = false;
            result.errors.upperPrice = ['Upper price must be greater than lower price'];
        }

        // Validate investment amount
        if (result.sanitized.totalInvestment < 100) {
            result.valid = false;
            result.errors.totalInvestment = ['Minimum investment is $100'];
        }
    }

    return result;
}

/**
 * Validate database query parameters
 * @param {object} queryParams - Database query parameters
 * @returns {object} Validation result
 */
validateQueryParams(queryParams)
{
    const schema = {
        limit: 'limit',
        offset: 'offset',
        orderBy: 'string',
        orderDirection: {allowed'ASC', 'DESC'
],
    required: true, type
:
    'string'
},
    filter: 'string'
}
    ;

    const result = this.validateObject(queryParams, schema);

    // Additional query-specific validations
    if (result.valid && result.sanitized.filter) {
        // Check for SQL injection in filter
        const securityCheck = this.validateSecurity(result.sanitized.filter);
        if (!securityCheck.valid) {
            result.valid = false;
            result.errors.filter = securityCheck.errors;
        }
    }

    return result;
}

/**
validateType(value, expectedType) {
    const result = {
        valid:      true,
        actualType: typeof value
    };

    switch (expectedType) {
        case 'string':
            result.valid = isString(value);
            break;
        case 'number':
            result.valid = isNumber(value) && !isNaN(value);
            break;
        case 'integer':
            result.valid = Number.isInteger(value);
            break;
        case 'boolean':
            result.valid = isBoolean(value);
            break;
        case 'array':
            result.valid = isArray(value);
            break;
        case 'object':
            result.valid = isObject(value) && !isArray(value);
            break;
        default:
            result.valid = false;
    }

    return result;
}
            break;
        case 'number'
            sult.valid = isNumber(value) && !isNaN(value);
            break;
        case 'integer'
            sult.valid = Number.isInteger(value);
            break;
        case 'boolean'
            sult.valid = isBoolean(value);
            break;
        case 'array'
            sult.valid = isArray(value);
            break;
        case 'object'
            sult.valid = isObject(value) && !isArray(value);
            break;
        default
            = false;
    }

    return result;
}

/**
 * Validate security (check for SQL injection, XSS, etc.)
 * @param {string} value - Value to validate
 * @returns {object} Validation result
 */
validateSecurity(value)
{
    const result = {valid, errors};

    if (!isString(value)) {
        return result;
    }

    // Check for SQL injection patterns
    if (this.patterns.sqlInjection.test(value)) {
        result.valid = false;
        result.errors.push('Potential SQL injection detected');
    }

    // Check for XSS patterns
    if (this.patterns.xss.test(value)) {
        result.valid = false;
        result.errors.push('Potential XSS attack detected');
    }

    return result;
}

/**
 * Check if a trading symbol is valid
 * @param {string} symbol - Trading symbol to validate
 * @returns {boolean} Whether the symbol is valid
 */
isValidTradingSymbol(symbol)
{
    if (!isString(symbol)) return false;

    const parts = symbol.split('/');
    if (parts.length !== 2) return false;

    const [base, quote] = parts;
    return base.length >= 1 && base.length <= 10 &&
        quote.length >= 1 && quote.length <= 10 &&
        /^[A-Z]+$/.test(base) && /^[A-Z]+$/.test(quote);
}

/**
 * Get number of decimal places in a number
 * @param {number} value - Number to check
 * @returns {number} Number of decimal places
 */
getDecimalPlaces(value)
{
    if (Math.floor(value) === value) return 0;
    const str = value.toString();
    if (str.indexOf('.') !== -1 && str.indexOf('e-') === -1) {
        return str.split('.')[1].length;
    } else if (str.indexOf('e-') !== -1) {
        const parts = str.split('e-');
        return parseInt(parts[1], 10);
    }
    return 0;
}

/**
 * Sanitize a value using a specific sanitizer
 * @param {any} value - Value to sanitize
 * @param {string} sanitizerName - Name of the sanitizer
 * @returns {any} Sanitized value
 */
sanitize(value, sanitizerName)
{
    const sanitizer = this.sanitizers[sanitizerName];
    if (!sanitizer) {
        throw new Error(`Unknown sanitizer: ${sanitizerName}`);
    }
    return sanitizer(value);
}

/**
 * Add a custom validation rule
 * @param {string} name - Rule name
 * @param {object} rule - Rule definition
 */
addRule(name, rule)
{
    // this.rules[name] = rule;
}

/**
 * Add a custom sanitizer
 * @param {string} name - Sanitizer name
 * @param {function} sanitizer - Sanitizer function
 */
addSanitizer(name, sanitizer)
{
    // this.sanitizers[name] = sanitizer;
}

/**
 * Get validation summary for debugging
 * @returns {object} Summary of available rules and sanitizers
 */
getSummary() {
    return {
        rules(this.rules
),
    sanitizers(this.sanitizers),
        patterns(this.patterns)
}
    ;
}
}

// Export singleton instance
const inputValidator = new InputValidator();

module.exports = inputValidator;
module.exports.InputValidator = InputValidator;

/**
 * 🧪 VALIDATION TEST SUITE
 *
 * This test suite validates the InputValidator module and ensures
 * all validation rules are working correctly across the trading system.
 */
// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();


const inputValidator = require('./InputValidator');

class ValidationTest {
    constructor() {
        // this.testResults = [];
        // this.passedTests = 0;
        // this.failedTests = 0;
    }

    /**
     * Run a single test case
     * @param {string} testName - Name of the test
     * @param {function} testFunction - Function to execute
     */
    async runTest(testName, testFunction) {
        logger.info(`\n🧪 Running test: ${testName}`);

        try { await: testFunction();
            logger.info(`✅ ${testName} - PASSED`);
            // this.passedTests++;
            // this.testResults.push({ name, status: 'PASSED' });
        } catch (error) {
            logger.info(`❌ ${testName} - FAILED: ${error.message}`);
            // this.failedTests++;
            // this.testResults.push({ name, status: 'FAILED', error });
        }
    }

    /**
     * Test order parameter validation
     */
    async testOrderValidation() {
        await this.runTest('Valid Order Parameters', () => {
            const validOrder = {
                symbol: 'BTC/USDT',
                side: 'buy',
                type: 'market',
                quantity,
                exchange: 'binance'
            };

            const result = inputValidator.validateOrderParams(validOrder);
            if (!result.valid) {
                throw new Error('Expected valid order to pass validation');
            }

            // Check sanitized values
            if (result.sanitized.symbol !== 'BTC/USDT') {
                throw new Error('Symbol sanitization failed');
            }
        });

        await this.runTest('Invalid Order Parameters', () => {
            const invalidOrder = {
                symbol: 'invalid-symbol',
                side: 'invalid-side',
                type: 'invalid-type',
                quantity: -1,
                exchange: 'invalid@exchange'
            };

            const result = inputValidator.validateOrderParams(invalidOrder);
            if (result.valid) {
                throw new Error('Expected invalid order to fail validation');
            }
        });

        await this.runTest('SQL Injection in Order', () => {
            const maliciousOrder = {
                symbol: "BTC/USDT'; DROP TABLE users; --",
                side: 'buy',
                type: 'market',
                quantity,
                exchange: 'binance'
            };

            const result = inputValidator.validateOrderParams(maliciousOrder);
            if (result.valid) {
                throw new Error('Expected SQL injection to be detected');
            }
        });
    }

    /**
     * Test grid trading configuration validation
     */
    async testGridConfigValidation() {
        await this.runTest('Valid Grid Configuration', () => {
            const validGrid = {
                symbol: 'ETH/USDT',
                exchange: 'binance',
                upperPrice,
                lowerPrice,
                gridQuantity,
                totalInvestment,
                leverageEnabled
            };

            const result = inputValidator.validateGridConfig(validGrid);
            if (!result.valid) {
                throw new Error('Expected valid grid config to pass validation');
            }
        });

        await this.runTest('Invalid Grid Configuration', () => {
            const invalidGrid = {
                symbol: 'ETH/USDT',
                exchange: 'binance',
                upperPrice, // Lower than lowerPrice
                lowerPrice, // Higher than upperPrice
                gridQuantity, // Too low
                totalInvestment, // Too low
                leverageEnabled: 'not-a-boolean'
            };

            const result = inputValidator.validateGridConfig(invalidGrid);
            if (result.valid) {
                throw new Error('Expected invalid grid config to fail validation');
            }
        });
    }

    /**
     * Test database query parameter validation
     */
    async testQueryValidation() {
        await this.runTest('Valid Query Parameters', () => {
            const validQuery = {
                limit,
                offset,
                orderBy: 'created_at',
                orderDirection: 'DESC',
                filter: 'status = active'
            };

            const result = inputValidator.validateQueryParams(validQuery);
            if (!result.valid) {
                throw new Error('Expected valid query params to pass validation');
            }
        });

        await this.runTest('SQL Injection in Query', () => {
            const maliciousQuery = {
                limit,
                offset,
                filter: '1=1; DROP TABLE trades; --'
            };

            const result = inputValidator.validateQueryParams(maliciousQuery);
            if (result.valid) {
                throw new Error('Expected SQL injection to be detected');
            }
        });
    }

    /**
     * Test API credentials validation
     */
    async testCredentialsValidation() {
        await this.runTest('Valid API Credentials', () => {
            const validCredentials = {
                apiKey: 'valid_api_key_123',
                secret: 'valid_secret_456',
                exchange: 'binance',
                sandbox
            };

            const result = inputValidator.validateCredentials(validCredentials);
            if (!result.valid) {
                throw new Error('Expected valid credentials to pass validation');
            }
        });

        await this.runTest('Invalid API Credentials', () => {
            const invalidCredentials = {
                apiKey: 'key with spaces and @symbols',
                secret: '',
                exchange: 'INVALID-EXCHANGE!',
                sandbox: 'not-a-boolean'
            };

            const result = inputValidator.validateCredentials(invalidCredentials);
            if (result.valid) {
                throw new Error('Expected invalid credentials to fail validation');
            }
        });
    }

    /**
     * Test security validation functions
     */
    async testSecurityValidation() {
        await this.runTest('XSS Detection', () => {
            const xssAttempt = '<script>alert("XSS")</script>';
            const result = inputValidator.validateSecurity(xssAttempt);

            if (result.valid) {
                throw new Error('Expected XSS to be detected');
            }
        });

        await this.runTest('SQL Injection Detection', () => {
            const sqlInjection = "'; DROP TABLE users; --";
            const result = inputValidator.validateSecurity(sqlInjection);

            if (result.valid) {
                throw new Error('Expected SQL injection to be detected');
            }
        });

        await this.runTest('Safe String Validation', () => {
            const safeString = 'This is a safe string with numbers 123';
            const result = inputValidator.validateSecurity(safeString);

            if (!result.valid) {
                throw new Error('Expected safe string to pass validation');
            }
        });
    }

    /**
     * Test type validation functions
     */
    async testTypeValidation() {
        await this.runTest('String Type Validation', () => {
            const result = inputValidator.validateType('test string', 'string');
            if (!result.valid) {
                throw new Error('String type validation failed');
            }
        });

        await this.runTest('Number Type Validation', () => {
            const result = inputValidator.validateType(123, 'number');
            if (!result.valid) {
                throw new Error('Number type validation failed');
            }
        });

        await this.runTest('Integer Type Validation', () => {
            const result = inputValidator.validateType(123, 'integer');
            if (!result.valid) {
                throw new Error('Integer type validation failed');
            }

            const floatResult = inputValidator.validateType(123.5, 'integer');
            if (floatResult.valid) {
                throw new Error('Float should fail integer validation');
            }
        });

        await this.runTest('Boolean Type Validation', () => {
            const result = inputValidator.validateType(true, 'boolean');
            if (!result.valid) {
                throw new Error('Boolean type validation failed');
            }
        });

        await this.runTest('Array Type Validation', () => {
            const result = inputValidator.validateType([1, 2, 3], 'array');
            if (!result.valid) {
                throw new Error('Array type validation failed');
            }
        });

        await this.runTest('Object Type Validation', () => {
            const result = inputValidator.validateType({key: 'value'}, 'object');
            if (!result.valid) {
                throw new Error('Object type validation failed');
            }
        });
    }

    /**
     * Test sanitization functions
     */
    async testSanitization() {
        await this.runTest('HTML Sanitization', () => {
            const dirtyHtml = '<script>alert("test")</script>Hello World';
            const sanitized = inputValidator.sanitize(dirtyHtml, 'html');

            if (sanitized.includes('<script>')) {
                throw new Error('HTML sanitization failed');
            }
        });

        await this.runTest('SQL Sanitization', () => {
            const dirtySql = 'SELECT * FROM users WHERE id = 1; DROP TABLE users; --';
            const sanitized = inputValidator.sanitize(dirtySql, 'sql');

            if (sanitized.includes(';') || sanitized.includes('--')) {
                throw new Error('SQL sanitization failed');
            }
        });

        await this.runTest('Alphanumeric Sanitization', () => {
            const dirtyString = 'abc123!@#$%^&*()';
            const sanitized = inputValidator.sanitize(dirtyString, 'alphanumeric');

            if (sanitized !== 'abc123') {
                throw new Error('Alphanumeric sanitization failed');
            }
        });

        await this.runTest('Numeric Sanitization', () => {
            const dirtyNumber = 'abc123.45def';
            const sanitized = inputValidator.sanitize(dirtyNumber, 'numeric');

            if (sanitized !== '123.45') {
                throw new Error('Numeric sanitization failed');
            }
        });
    }

    /**
     * Test trading symbol validation
     */
    async testTradingSymbolValidation() {
        await this.runTest('Valid Trading Symbols', () => {
            const validSymbols = ['BTC/USDT', 'ETH/USD', 'DOGE/BTC', 'ADA/ETH'];

            for (const symbol of validSymbols) {
                if (!inputValidator.isValidTradingSymbol(symbol)) {
                    throw new Error(`Valid symbol ${symbol} failed validation`);
                }
            }
        });

        await this.runTest('Invalid Trading Symbols', () => {
            const invalidSymbols = ['BTC', 'BTC/USD/EUR', 'btc/usdt', 'BTC-USDT', '123/456'];

            for (const symbol of invalidSymbols) {
                if (inputValidator.isValidTradingSymbol(symbol)) {
                    throw new Error(`Invalid symbol ${symbol} passed validation`);
                }
            }
        });
    }

    /**
     * Test decimal precision validation
     */
    async testDecimalPrecision() {
        await this.runTest('Decimal Places Calculation', () => {
            const testCases = [
                {value, expected},
                {value, expected},
                {value, expected},
                {value, expected}];


            for (const testCase of testCases) {
                const result = inputValidator.getDecimalPlaces(testCase.value);
                if (result !== testCase.expected) {
                    throw new Error(`Decimal places for ${testCase.value} expected ${testCase.expected}, got ${result}`);
                }
            }
        });
    }

    /**
     * Test edge cases and boundary conditions
     */
    async testEdgeCases() {
        await this.runTest('Null Values', () => {
            const result = inputValidator.validateValue(null, 'string', 'testField');
            if (result.valid) {
                throw new Error('Null value should fail validation for required field');
            }
        });

        await this.runTest('Undefined Values', () => {
            const result = inputValidator.validateValue(undefined, 'string', 'testField');
            if (result.valid) {
                throw new Error('Undefined value should fail validation for required field');
            }
        });

        await this.runTest('Empty String', () => {
            const result = inputValidator.validateValue('', 'string', 'testField');
            if (result.valid) {
                throw new Error('Empty string should fail validation for required field');
            }
        });

        await this.runTest('Very Large Number', () => {
            const largeNumber = Number.MAX_SAFE_INTEGER + 1;
            const result = inputValidator.validateValue(largeNumber, 'number', 'testField');
            if (result.valid) {
                throw new Error('Number exceeding MAX_SAFE_INTEGER should fail validation');
            }
        });

        await this.runTest('Very Small Number', () => {
            const smallNumber = Number.MIN_SAFE_INTEGER - 1;
            const result = inputValidator.validateValue(smallNumber, 'number', 'testField');
            if (result.valid) {
                throw new Error('Number below MIN_SAFE_INTEGER should fail validation');
            }
        });

        await this.runTest('NaN Value', () => {
            const result = inputValidator.validateValue(NaN, 'number', 'testField');
            if (result.valid) {
                throw new Error('NaN should fail number validation');
            }
        });
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        logger.info('🚀 Starting comprehensive validation tests...\n');

        const startTime = Date.now();

        // Run all test suites
        await this.testOrderValidation();
        await this.testGridConfigValidation();
        await this.testQueryValidation();
        await this.testCredentialsValidation();
        await this.testSecurityValidation();
        await this.testTypeValidation();
        await this.testSanitization();
        await this.testTradingSymbolValidation();
        await this.testDecimalPrecision();
        await this.testEdgeCases();

        const endTime = Date.now();
        const duration = endTime - startTime;

        // Print summary
        logger.info('\n' + '='.repeat(50));
        logger.info('🧪 VALIDATION TEST SUMMARY');
        logger.info('='.repeat(50));
        logger.info(`Total Tests: ${this.passedTests + this.failedTests}`);
        logger.info(`Passed: ${this.passedTests}`);
        logger.info(`Failed: ${this.failedTests}`);
        logger.info(`Duration: ${duration}ms`);
        logger.info(`Success Rate: ${(this.passedTests / (this.passedTests + this.failedTests) * 100).toFixed(2)}%`);

        if (this.failedTests > 0) {
            logger.info('\n❌ FAILED TESTS:');
            // this.testResults.
            filter((test) => test.status === 'FAILED').forEach((test) => {
                logger.info(`  - ${test.name}: ${test.error}`);
            });
        } else {
            logger.info('\n✅ ALL TESTS PASSED!');
        }

        logger.info('='.repeat(50));

        return {
            totalTests +this.failedTests,
            passed,
            failed,
            duration,
            successRate / (this.passedTests + this.failedTests) * 100,
            results
    }
        ;
    }
}

// Export the test class
module.exports = ValidationTest;

// Run tests if this file is executed directly
if (require.main === module) {
    const testSuite = new ValidationTest();
    testSuite.runAllTests().then((results) => {
        process.exit(results.failed > 0 ? 1);
    }).catch((error) => {
        logger.error('Test execution failed:', error);
        process.exit(1);
    });
}

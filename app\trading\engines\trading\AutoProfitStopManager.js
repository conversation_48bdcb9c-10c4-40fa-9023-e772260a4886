/**
 * Automatic Profit Taking and Stop Loss Management System
 * Manages dynamic stop losses, trailing stops, and profit taking for all positions
 * Adjusts levels based on market conditions and position performance
 */

const EventEmitter = require('events');
const logger = require('../../shared/helpers/logger');
const DatabaseHelper = require('./DatabaseHelper');

/**
 * @typedef {Object} Position
 * @property {string} id
 * @property {string} symbol
 * @property {string} exchange
 * @property {number} entryPrice
 * @property {number} quantity
 * @property {'buy' | 'sell'} side
 * @property {number} [leverage]
 * @property {string} [strategyType]
 * @property {number} [currentPrice]
 * @property {number} [highestPrice]
 * @property {number} [lowestPrice]
 * @property {number} [pnl]
 * @property {Date} [lastUpdate]
 * @property {any} [marketConditions]
 */

/**
 * @typedef {Object} MarketConditions
 * @property {{valuember, regime}} volatility
 * @property {string} trend
 * @property {{ratiomber, increasing}} volume
 * @property {{support: {pricember, strength}[], resistance: {pricember, strength}[]}} levels
 * @property {any} ticker
 * @property {Date} timestamp
 */

class AutoProfitStopManager extends EventEmitter {
    // this.state = {
    activePositions

    // State tracking
    stopOrders
    profitOrders

,
    trailingStops
    marketConditions

,
    dailyPnL
    lastUpdate

,

    /**
     * @param {any} exchangeManager
     * @param {DatabaseHelper} [databaseHelper]
     */
    constructor(exchangeManager, databaseHelper) {
        super();
        // this.exchangeManager = exchangeManager;
        // this.db = databaseHelper || new DatabaseHelper();
        // this.logger = logger;

        // Configuration for automatic profit/stop management
        // this.config = {
        // Stop loss configuration
        stopLoss: {
            enabled,
                initialStop, // 2% initial stop loss
                trailingEnabled,
                trailingActivation, // Activate trailing after 1% profit
                trailingDistance, // Trail by 1.5%
                dynamicStops, // Adjust stops based on volatility
                breakEvenEnabled, // Move stop to break even
                breakEvenTrigger, // At 1.5% profit
                breakEvenOffset, // 0.2% above entry for fees
        }
    ,

        // Take profit configuration
        takeProfit: {
            enabled,
                levels
            {
                trigger, amount
            }
        , // Take 25% at 3%
            {
                trigger, amount
            }
        , // Take 25% at 5%
            {
                trigger, amount
            }
        , // Take 25% at 8%
            {
                trigger, amount
            }
        , // Take final 25% at 12%
        ],
            dynamicTargets, // Adjust targets based on market conditions
                moonBagEnabled, // Keep small position for big moves
                moonBagPercent, // Keep 10% for moon shots
        }
    ,

        // Market condition adjustments
        marketAdjustments: {
            volatilityMultiplier,
                trendFollowing,
                supportResistance,
                volumeAnalysis
        }
    ,

        // Risk management
        risk: {
            maxLossPerPosition, // Max 5% loss per position
                maxDailyLoss, // Max 10% daily loss
                correlationCheck, // Check correlated positions
                scaleOutOnDrawdown, // Reduce position size on drawdown
        }
    ,

        // Update intervals
        updateInterval, // Check positions every minute
            emergencyCheckInterval, // Emergency checks every 5 seconds
    };

    Map()

,

    Map()

    Map()

,

    Map()
,

    Map()
};

// Monitoring intervals
// this.updateInterval = null;
// this.emergencyInterval = null;
}

async
initialize() {
    try {
        logger.info('Initializing Auto Profit/Stop Manager...');

        // Create tables
        await this.createTables();

        // Load active positions
        await this.loadActivePositions();

        // Start monitoring
        // this.startMonitoring();

        logger.info('Auto Profit/Stop Manager initialized successfully');
        return true;

    } catch (error) {
        logger.error('Failed to initialize Auto Profit/Stop Manager:', error);
        throw error;
    }
}

async
createTables() {
    const queries = [
        `CREATE TABLE IF NOT EXISTS stop_loss_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                position_id TEXT NOT NULL,
                symbol TEXT NOT NULL,
                exchange TEXT NOT NULL,
                stop_price REAL NOT NULL,
                trigger_price REAL,
                stop_type TEXT DEFAULT 'fixed',
                order_id TEXT,
                status TEXT DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                triggered_at DATETIME,
                UNIQUE(position_id)
            )`,

        `CREATE TABLE IF NOT EXISTS take_profit_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                position_id TEXT NOT NULL,
                symbol TEXT NOT NULL,
                exchange TEXT NOT NULL,
                target_price REAL NOT NULL,
                amount_percent REAL NOT NULL,
                level INTEGER NOT NULL,
                order_id TEXT,
                status TEXT DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                triggered_at DATETIME,
                UNIQUE(position_id, level)
            )`,

        `CREATE TABLE IF NOT EXISTS position_adjustments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                position_id TEXT NOT NULL,
                adjustment_type TEXT NOT NULL,
                old_value REAL,
                new_value REAL,
                reason TEXT,
                market_conditions TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

        'CREATE INDEX IF NOT EXISTS idx_stop_orders_position ON stop_loss_orders(position_id, status);',
        'CREATE INDEX IF NOT EXISTS idx_profit_orders_position ON take_profit_orders(position_id, status);',
        'CREATE INDEX IF NOT EXISTS idx_adjustments_position ON position_adjustments(position_id, created_at);'];


    for (const query of queries) {
        await this.db.run(query);
    }
}

/**
 * Set up automatic profit and stop orders for a new position
 * @param {Position} position
 */
async
setupPosition(position)
{
    const {
        id,
        symbol,
        exchange
    } = position;

    try {
        logger.info(`Setting up auto profit/stop for position ${positionId} (${symbol})`);

        // Analyze current market conditions
        const marketConditions = await this.analyzeMarketConditions(symbol, exchange);

        // Calculate and place stop loss
        if (this.config.stopLoss.enabled) {
            const stopLoss = await this.calculateStopLoss(position, marketConditions);
            await this.placeStopLoss(position, stopLoss);
        }

        // Calculate and place take profit orders
        if (this.config.takeProfit.enabled) {
            const profitTargets = await this.calculateProfitTargets(position, marketConditions);
            await this.placeProfitOrders(position, profitTargets);
        }

        // Store position in active tracking
        // this.state.activePositions.set(positionId, {
    ...
        position,
            marketConditions,
            lastUpdate
        Date: jest.fn(),
            pnl,
            highestPrice,
            lowestPrice
    }
)
    ;

    // Emit setup complete event
    // this.emit('positionSetupComplete', {
    positionId,
        symbol,
        stopLoss(positionId),
        profitTargets(positionId)
}
)
;

return true;

} catch (error) {
    logger.error(`Error setting up position ${positionId}:`, error);
    return false;
}
}

/**
 * @param {Position} position
 * @param {MarketConditions} marketConditions
 */
calculateStopLoss(position, marketConditions)
{
    const {entryPrice, side, leverage = 1} = position;
    let stopDistance = this.config.stopLoss.initialStop;

    // Adjust for leverage
    if (leverage > 1) {
        stopDistance = stopDistance / leverage;
    }

    // Adjust for volatility
    if (this.config.marketAdjustments.volatilityMultiplier && marketConditions.volatility) {
        const volMultiplier = this.getVolatilityMultiplier(marketConditions.volatility);
        stopDistance *= volMultiplier;
    }

    // Adjust for market trend
    if (this.config.marketAdjustments.trendFollowing && marketConditions.trend) {
        if (side === 'buy' && marketConditions.trend === 'up' ||
            side === 'sell' && marketConditions.trend === 'down') {
            // Trend is favorable, can use tighter stop
            stopDistance *= 0.8;
        } else if (side === 'buy' && marketConditions.trend === 'down' ||
            side === 'sell' && marketConditions.trend === 'up') {
            // Against trend, use wider stop
            stopDistance *= 1.2;
        }
    }

    // Calculate stop price
    let stopPrice;
    if (side === 'buy') {
        stopPrice = entryPrice * (1 - stopDistance);
    } else {
        stopPrice = entryPrice * (1 + stopDistance);
    }

    // Check support/resistance levels
    if (this.config.marketAdjustments.supportResistance && marketConditions.levels) {
        stopPrice = this.adjustForSupportResistance(
            stopPrice,
            side,
            marketConditions.levels,
        );
    }

    return {
        price,
        distance,
        type: 'fixed',
        reason: `Initial stop at ${(stopDistance * 100).toFixed(2)}% from entry`
    };
}

/**
 * @param {Position} position
 * @param {MarketConditions} marketConditions
 */
calculateProfitTargets(position, marketConditions)
{
    const {entryPrice, side} = position;
    const baseLevels = [...this.config.takeProfit.levels]; // Copy array
    const targets = [];

    // Adjust targets based on market conditions
    if (this.config.takeProfit.dynamicTargets) {
        // Volatility adjustment
        if (marketConditions.volatility) {
            const volMultiplier = this.getVolatilityMultiplier(marketConditions.volatility);
            baseLevels.forEach((level) => {
                level.trigger *= volMultiplier;
            });
        }

        // Trend adjustment
        if (marketConditions.trend) {
            if (side === 'buy' && marketConditions.trend === 'up' ||
                side === 'sell' && marketConditions.trend === 'down') {
                // Favorable trend, extend targets
                baseLevels.forEach((level) => {
                    level.trigger *= 1.2;
                });
            }
        }

        // Volume adjustment
        if (marketConditions.volume && marketConditions.volume.ratio > 1.5) {
            // High volume, momentum likely to continue
            baseLevels.forEach((level) => {
                level.trigger *= 1.1;
            });
        }
    }

    // Calculate target prices
    baseLevels.forEach((level, index) => {
        let targetPrice;
        if (side === 'buy') {
            targetPrice = entryPrice * (1 + level.trigger);
        } else {
            targetPrice = entryPrice * (1 - level.trigger);
        }

        // Adjust for resistance/support
        if (this.config.marketAdjustments.supportResistance && marketConditions.levels) {
            targetPrice = this.adjustTargetForResistance(
                targetPrice,
                side,
                marketConditions.levels,
            );
        }

        targets.push({
            level +1,
            price,
            amount,
            trigger,
            reason: `Target ${index + 1} at ${(level.trigger * 100).toFixed(2)}% profit`
        });
    });

    return targets;
}

/**
 * @param {Position} position
 * @param {any} stopLoss
 */
async
placeStopLoss(position, stopLoss)
{
    try {
        const exchange = await this.exchangeManager.getExchange(position.exchange);

        // Create stop order
        const stopOrder = {
                symbol,
                type: 'stop',
                side === 'buy' ? 'sell' : 'buy',
            amount,
            stopPrice,
            reduceOnly
    }
        ;

        // Place the order
        const order = await exchange.createOrder(
            stopOrder.symbol,
            stopOrder.type,
            stopOrder.side,
            stopOrder.amount,
            null,
            {
                stopPrice,
                reduceOnly
            },
        );

        // Store stop order details
        await this.db.run(
            `INSERT INTO stop_loss_orders
                (position_id, symbol, exchange, stop_price, stop_type, order_id, status)
                VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [
                position.id,
                position.symbol,
                position.exchange,
                stopLoss.price,
                stopLoss.type,
                order.id,
                'active'],
        );

        // Track in memory
        // this.state.stopOrders.set(position.id, {
        orderId,
            price,
            type,
            status
    :
        'active'
    }
)
    ;

    logger.info(`Stop loss placed for ${position.symbol} at ${stopLoss.price}`);

    return order;

} catch (error) {
    logger.error(`Failed to place stop loss for ${position.symbol}:`, error);
    throw error;
}
}

/**
 * @param {Position} position
 * @param {any[]} targets
 */
async
placeProfitOrders(position, targets)
{
    const placedOrders = [];

    for (const target of targets) {
        try {
            const exchange = await this.exchangeManager.getExchange(position.exchange);

            // Calculate order quantity
            const orderQuantity = position.quantity * target.amount;

            // Create limit order
            const profitOrder = {
                    symbol,
                    type: 'limit',
                    side === 'buy' ? 'sell' : 'buy',
                amount,
                price,
                reduceOnly
        }
            ;

            // Place the order
            const order = await exchange.createOrder(
                profitOrder.symbol,
                profitOrder.type,
                profitOrder.side,
                profitOrder.amount,
                profitOrder.price,
                {reduceOnly},
            );

            // Store profit order details
            await this.db.run(
                `INSERT INTO take_profit_orders
                    (position_id, symbol, exchange, target_price, amount_percent, level, order_id, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    position.id,
                    position.symbol,
                    position.exchange,
                    target.price,
                    target.amount,
                    target.level,
                    order.id,
                    'active'],
            );

            placedOrders.push({
                level,
                orderId,
                price,
                amount,
                status: 'active'
            });

            logger.info(`Profit target ${target.level} placed for ${position.symbol} at ${target.price}`);

        } catch (error) {
            logger.error(`Failed to place profit target ${target.level} for ${position.symbol}:`, error);
        }
    }

    // Track in memory
    if (placedOrders.length > 0) {
        // this.state.profitOrders.set(position.id, placedOrders);
    }

    return placedOrders;
}

/**
 * Update stops and targets for all active positions
 */
async
updateAllPositions() {
    for (const positionId of this.state.activePositions.keys()) {
        try {
            await this.updatePosition(positionId);
        } catch (error) {
            logger.error(`Error updating position ${positionId}:`, error);
        }
    }
}

/**
 * @param {string} positionId
 */
async
updatePosition(positionId)
{
    const position = this.state.activePositions.get(positionId);
    if (!position) return;

    try {
        // Get current market price
        const exchange = await this.exchangeManager.getExchange(position.exchange);
        const ticker = await exchange.fetchTicker(position.symbol);
        const currentPrice = ticker.last;

        // Update position state
        position.currentPrice = currentPrice;
        position.highestPrice = Math.max(position.highestPrice, currentPrice);
        position.lowestPrice = Math.min(position.lowestPrice, currentPrice);

        // Calculate current P&L
        const pnlPercent = position.side === 'buy' ?
            (currentPrice - position.entryPrice) / position.entryPrice :
            (position.entryPrice - currentPrice) / position.entryPrice;

        position.pnl = pnlPercent;
        position.lastUpdate = new Date();

        // Update trailing stop if enabled and in profit
        if (this.config.stopLoss.trailingEnabled &&
            pnlPercent > this.config.stopLoss.trailingActivation) {
            await this.updateTrailingStop(position, currentPrice);
        }

        // Check for break-even stop
        if (this.config.stopLoss.breakEvenEnabled &&
            pnlPercent > this.config.stopLoss.breakEvenTrigger) {
            await this.moveToBreakEven(position);
        }

        // Check if any profit targets hit
        await this.checkProfitTargets(position, currentPrice);

        // Log position adjustment if significant change
        if (Math.abs(pnlPercent) > 0.01) {// 1% change
            await this.logPositionAdjustment(positionId, 'price_update', position.currentPrice, currentPrice);
        }

    } catch (error) {
        logger.error(`Error updating position ${positionId}:`, error);
    }
}

/**
 * @param {Position} position
 * @param {number}
 */
async
updateTrailingStop(position)
{
    const existingStop = this.state.stopOrders.get(position.id);
    if (!existingStop || existingStop.type === 'break_even') return;

    const trailingDistance = this.config.stopLoss.trailingDistance;
    let newStopPrice;

    if (position.side === 'buy') {
        // For long positions, trail below the highest price
        newStopPrice = position.highestPrice * (1 - trailingDistance);

        // Only update if new stop is higher than current
        if (newStopPrice > existingStop.price) {
            await this.updateStopLoss(position, newStopPrice, 'trailing');
        }
    } else {
        // For short positions, trail above the lowest price
        newStopPrice = position.lowestPrice * (1 + trailingDistance);

        // Only update if new stop is lower than current
        if (newStopPrice < existingStop.price) {
            await this.updateStopLoss(position, newStopPrice, 'trailing');
        }
    }
}

/**
 * @param {Position} position
 */
async
moveToBreakEven(position)
{
    const existingStop = this.state.stopOrders.get(position.id);
    if (!existingStop || existingStop.type === 'break_even') return; // Already at break-even

    const breakEvenPrice = position.side === 'buy' ?
        position.entryPrice * (1 + this.config.stopLoss.breakEvenOffset) sition.entryPrice * (1 - this.config.stopLoss.breakEvenOffset);

    // Check if break-even stop would be better than current stop
    if (position.side === 'buy' && breakEvenPrice > existingStop.price ||
        position.side === 'sell' && breakEvenPrice < existingStop.price) {
        await this.updateStopLoss(position, breakEvenPrice, 'break_even');
        logger.info(`Moved stop to break-even for ${position.symbol}`);
    }
}

/**
 * @param {Position} position
 * @param {number} newPrice
 * @param {string} type
 */
async
updateStopLoss(position, newPrice, type)
{
    try {
        const exchange = await this.exchangeManager.getExchange(position.exchange);
        const existingStop = this.state.stopOrders.get(position.id);

        // Cancel existing stop order
        if (existingStop && existingStop.orderId) {
            await exchange.cancelOrder(existingStop.orderId, position.symbol);
        }

        // Place new stop order
        const newStop = await this.placeStopLoss(position, {
            price,
            type,
            reason: `${type} stop update`
        });

        // Log the adjustment
        await this.logPositionAdjustment(
            position.id,
            `stop_${type}`,
            existingStop?.price,
            newPrice,
        );

        return newStop;

    } catch (error) {
        logger.error(`Error updating stop loss for ${position.symbol}:`, error);
        throw error;
    }
}

/**
 * @param {Position} position
 * @param {number} currentPrice
 */
async
checkProfitTargets(position, currentPrice)
{
    const profitOrders = this.state.profitOrders.get(position.id);
    if (!profitOrders || profitOrders.length === 0) return;

    for (const order of profitOrders) {
        if (order.status !== 'active') continue;

        // Check if target hit
        const targetHit = position.side === 'buy' ?
            currentPrice >= order.price <= order.price;

        if (targetHit) {
            // Mark as triggered (exchange should handle the actual fill)
            order.status = 'triggered';

            await this.db.run(
                `UPDATE take_profit_orders
                    SET status = 'triggered', triggered_at = CURRENT_TIMESTAMP
                    WHERE position_id = ? AND level = ?`,
                [position.id, order.level],
            );

            logger.info(`Profit target ${order.level} triggered for ${position.symbol} at ${currentPrice}`);

            // this.emit('profitTargetHit', {
            positionId,
                symbol,
                level,
                targetPrice,
                currentPrice
        }
    )
        ;
    }
}
}

/**
 * @param {string} symbol
 * @param {string} exchange
 */
async
analyzeMarketConditions(symbol, exchange)
{
    try {
        const exchangeInstance = await this.exchangeManager.getExchange(exchange);

        // Fetch recent data
        const ticker = await exchangeInstance.fetchTicker(symbol);
        const ohlcv = await exchangeInstance.fetchOHLCV(symbol, '1h', undefined, 24);

        // Calculate volatility
        const volatility = this.calculateVolatility(ohlcv);

        // Determine trend
        const trend = this.determineTrend(ohlcv);

        // Volume analysis
        const volume = this.calculateVolumeMetrics(ohlcv, ticker.quoteVolume);

        // Support/resistance levels
        const levels = this.findSupportResistance(ohlcv);

        const conditions = {
            volatility,
            trend,
            volume,
            levels,
            ticker,
            timestamp Date()
        };

        // Cache market conditions
        // this.state.marketConditions.set(symbol, conditions);

        return conditions;

    } catch (error) {
        logger.error(`Error analyzing market conditions for ${symbol}:`, error);
        return {
            volatility: {value, regime: 'normal'},
            trend: 'neutral',
            volume: {ratio, increasing},
            levels: {support, resistance}
        };
    }
}

/**
 * @param {any[]} ohlcv
 */
calculateVolatility(ohlcv)
{
    if (!ohlcv || ohlcv.length < 2) {
        return {value, regime: 'normal'};
    }

    // Calculate returns
    const returns = [];
    for (let i = 1; i < ohlcv.length; i++) {
        const currentClose = ohlcv[i][4];
        const previousClose = ohlcv[i - 1][4];
        returns.push((currentClose - previousClose) / previousClose);
    }

    // Calculate standard deviation
    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);

    // Annualized volatility
    const annualizedVol = stdDev * Math.sqrt(365 * 24); // Hourly data

    // Determine volatility regime
    let regime = 'normal';
    if (annualizedVol < 0.3) regime = 'low'; else if (annualizedVol > 0.8) regime = 'high'; else if (annualizedVol > 1.2) regime = 'extreme';

    return {value, regime};
}

/**
 * @param {any[]} ohlcv
 */
determineTrend(ohlcv)
{
    if (!ohlcv || ohlcv.length < 10) return 'neutral';

    // Simple trend determination using price action
    const recent = ohlcv.slice(-10);
    const firstPrice = recent[0][4];
    const lastPrice = recent[recent.length - 1][4];
    const middlePrice = recent[Math.floor(recent.length / 2)][4];

    const overallChange = (lastPrice - firstPrice) / firstPrice;
    const recentChange = (lastPrice - middlePrice) / middlePrice;

    if (overallChange > 0.02 && recentChange > 0.01) return 'up';
    if (overallChange < -0.02 && recentChange < -0.01) return 'down';
    return 'neutral';
}

/**
 * @param {any[]} ohlcv
 * @param {number} currentVolume
 */
calculateVolumeMetrics(ohlcv, currentVolume)
{
    if (!ohlcv || ohlcv.length < 2) {
        return {ratio, increasing};
    }

    // Calculate average volume
    const volumes = ohlcv.map((candle) => candle[5]);
    const avgVolume = volumes.reduce((sum, v) => sum + v, 0) / volumes.length;

    // Volume ratio
    const ratio = currentVolume / avgVolume;

    // Check if volume is increasing
    const recentVolumes = volumes.slice(-5);
    const increasing = recentVolumes[recentVolumes.length - 1] > recentVolumes[0];

    return {ratio, increasing, average};
}

/**
 * @param {any[]} ohlcv
 */
findSupportResistance(ohlcv)
{
    if (!ohlcv || ohlcv.length < 20) {
        return {support, resistance};
    }

    const support = [];
    const resistance = [];

    // Simple pivot point identification
    for (let i = 2; i < ohlcv.length - 2; i++) {
        const prev2 = ohlcv[i - 2];
        const prev1 = ohlcv[i - 1];
        const current = ohlcv[i];
        const next1 = ohlcv[i + 1];
        const next2 = ohlcv[i + 2];

        // Check for swing low (support)
        if (current[3] < prev1[3] && current[3] < prev2[3] &&
            current[3] < next1[3] && current[3] < next2[3]) {
            support.push({price, strength});
        }

        // Check for swing high (resistance)
        if (current[2] > prev1[2] && current[2] > prev2[2] &&
            current[2] > next1[2] && current[2] > next2[2]) {
            resistance.push({price, strength});
        }
    }

    // Sort and limit to top 3 levels
    const finalSupport = support.sort((a, b) => b.price - a.price).slice(0, 3);
    const finalResistance = resistance.sort((a, b) => a.price - b.price).slice(0, 3);

    return {support, resistance};
}

/**
 * @param {{regime}} volatility
 */
getVolatilityMultiplier(volatility)
{
    switch (volatility.regime) {
        case 'low'
            turn
            0.8; // Tighter stops/targets in low vol
        case 'high'
            turn
            1.5; // Wider stops/targets in high vol
        case 'extreme'
            turn
            2.0; // Much wider in extreme vol
        default
            1.0;
    }
}

/**
 * @param {number} price
 * @param {string} side
 * @param {{support: {pricember}[], resistance: {pricember}[]}} levels
 */
adjustForSupportResistance(price, side, levels)
{
    // For stops, place just beyond support/resistance
    const buffer = 0.002; // 0.2% buffer

    if (side === 'buy' && levels.support.length > 0) {
        // Find nearest support below stop price
        const nearestSupport = levels.support.find((s) => s.price < price);
        if (nearestSupport && Math.abs(nearestSupport.price - price) < price * 0.01) {
            // Place stop below support
            return nearestSupport.price * (1 - buffer);
        }
    } else if (side === 'sell' && levels.resistance.length > 0) {
        // Find nearest resistance above stop price
        const nearestResistance = levels.resistance.find((r) => r.price > price);
        if (nearestResistance && Math.abs(nearestResistance.price - price) < price * 0.01) {
            // Place stop above resistance
            return nearestResistance.price * (1 + buffer);
        }
    }

    return price;
}

/**
 * @param {number} price
 * @param {string} side
 * @param {{support: {pricember}[], resistance: {pricember}[]}} levels
 */
adjustTargetForResistance(price, side, levels)
{
    // For targets, place just before resistance/support
    const buffer = 0.002; // 0.2% buffer

    if (side === 'buy' && levels.resistance.length > 0) {
        // Find nearest resistance above target
        const nearestResistance = levels.resistance.find((r) => r.price > price * 0.98 && r.price < price * 1.02);
        if (nearestResistance) {
            // Place target before resistance
            return nearestResistance.price * (1 - buffer);
        }
    } else if (side === 'sell' && levels.support.length > 0) {
        // Find nearest support below target
        const nearestSupport = levels.support.find((s) => s.price < price * 1.02 && s.price > price * 0.98);
        if (nearestSupport) {
            // Place target before support
            return nearestSupport.price * (1 + buffer);
        }
    }

    return price;
}

/**
 * @param {string} positionId
 * @param {string} type
 * @param {*} oldValue
 * @param {*} newValue
 * @param {string} [reason]
 */
async
logPositionAdjustment(positionId, type, oldValue, newValue, reason = null)
{
    const position = this.state.activePositions.get(positionId);
    const marketConditions = position ? JSON.stringify(position.marketConditions) ll;

    await this.db.run(
        `INSERT INTO position_adjustments
            (position_id, adjustment_type, old_value, new_value, reason, market_conditions)
            VALUES (?, ?, ?, ?, ?, ?)`,
        [positionId, type, oldValue, newValue, reason, marketConditions],
    );
}

/**
 * Loads all active and pending positions from the database
 */
async
loadActivePositions() {
    try {
        const positions = await this.db.all(
            `SELECT * FROM strategy_positions
                WHERE status IN ('open', 'pending')
                ORDER BY entry_time DESC`,
        );

        for (const position of positions) {
            // Load associated orders
            const stopOrders = await this.db.all(
                `SELECT * FROM stop_loss_orders
                    WHERE position_id = ? AND status = 'active'
                    LIMIT 1`,
                [position.id],
            );

            const profitOrders = await this.db.all(
                `SELECT * FROM take_profit_orders
                    WHERE position_id = ? AND status = 'active'
                    ORDER BY level`,
                [position.id],
            );

            // Store in memory
            // this.state.activePositions.set(position.id, {
        ...
            position,
                lastUpdate
            Date(position.updated_at),
            highestPrice || position.entry_price,
            lowestPrice || position.entry_price
        }
    )
        ;

        if (stopOrders.length > 0) {
            // this.state.stopOrders.set(position.id, stopOrders[0]);
        }

        if (profitOrders.length > 0) {
            // this.state.profitOrders.set(position.id, profitOrders);
        }
    }

    logger.info(`Loaded ${positions.length} active positions for monitoring`);

} catch (error) {
    logger.error('Error loading active positions:', error);
}
}

/**
 * Starts monitoring all active positions
 */
startMonitoring() {
    // Regular position updates
    // this.updateInterval = setInterval(async () => {
    await this.updateAllPositions();
}
,
// this.config.updateInterval
)
;

// Emergency checks (stop loss triggers, etc.)
// this.emergencyInterval = setInterval(async () => {
await this.performEmergencyChecks();
},
// this.config.emergencyCheckInterval
)
;

logger.info('Position monitoring started');
}

performEmergencyChecks() {
    // Check for positions that need immediate attention
    for (const [positionId, position] of this.state.activePositions) {
        if (!position.currentPrice) continue;

        const pnlPercent = position.pnl || 0;

        // Check max loss per position
        if (pnlPercent < -this.config.risk.maxLossPerPosition) {
            logger.warn(`Emergency ${positionId} exceeds max loss (${(pnlPercent * 100).toFixed(2)}%)`);
            // this.emit('emergencyStop', {
            positionId,
                reason
        :
            'max_loss_exceeded',
                loss
        }
    )
        ;
    }
}

// Check daily loss limit
if (this.state.dailyPnL < -this.config.risk.maxDailyLoss) {
    logger.error(`Emergency loss limit exceeded (${(this.state.dailyPnL * 100).toFixed(2)}%)`);
    // this.emit('emergencyStopAll', {
    reason: 'daily_loss_limit',
        loss
}
)
;
}
}

/**
 * Manual position closure with profit/stop cleanup
 * @param {string} positionId
 */
async
closePosition(positionId)
{
    try {
        const position = this.state.activePositions.get(positionId);
        if (!position) return;

        const exchange = await this.exchangeManager.getExchange(position.exchange);

        // Cancel all stop orders
        const stopOrder = this.state.stopOrders.get(positionId);
        if (stopOrder?.orderId) {
            await exchange.cancelOrder(stopOrder.orderId, position.symbol);
            await this.db.run(
                'UPDATE stop_loss_orders SET status = \'cancelled\' WHERE position_id = ?',
                [positionId],
            );
        }

        // Cancel all profit orders
        const profitOrders = this.state.profitOrders.get(positionId);
        if (profitOrders) {
            for (const order of profitOrders) {
                if (order.orderId && order.status === 'active') {
                    await exchange.cancelOrder(order.orderId, position.symbol);
                }
            }
            await this.db.run(
                'UPDATE take_profit_orders SET status = \'cancelled\' WHERE position_id = ?',
                [positionId],
            );
        }

        // Remove from active tracking
        // this.state.activePositions.delete(positionId);
        // this.state.stopOrders.delete(positionId);
        // this.state.profitOrders.delete(positionId);

        logger.info(`Position ${positionId} closed and cleaned up`);

    } catch (error) {
        logger.error(`Error closing position ${positionId}:`, error);
    }
}

/**
 * Get profit/stop management summary
 */
getManagementSummary() {
    const activeCount = this.state.activePositions.size;
    const positions = Array.from(this.state.activePositions.values());

    const summary = {
        activePositions,
        totalPnL((sum, p)
=>
    sum + (p.pnl || 0), 0
),
    averagePnL > 0 ? positions.reduce((sum, p) => sum + (p.pnl || 0), 0) / activeCount,
        positionsWithStops,
        positionsWithTargets,
        trailingStops(this.state.stopOrders.values()).filter((s) => s.type === 'trailing').length,
        breakEvenStops(this.state.stopOrders.values()).filter((s) => s.type === 'break_even').length,
        marketConditions(this.state.marketConditions)
}
    ;

    return summary;
}

/**
 * Shutdown and cleanup
 */
shutdown() {
    logger.info('Shutting down Auto Profit/Stop Manager...');

    if (this.updateInterval) {
        clearInterval(this.updateInterval);
    }

    if (this.emergencyInterval) {
        clearInterval(this.emergencyInterval);
    }

    logger.info('Auto Profit/Stop Manager shutdown complete');
}
}

module.exports = AutoProfitStopManager;

/**
 * @typedef {object} GridBotConfig
 * @property {string} [symbol]
 * @property {number} [gridCount]
 * @property {number} [gridSpacing]
 * @property {number} [investment]
 * @property {string} [exchange]
 */

/**
 * @typedef {object} Grid
 * @property {number} level
 * @property {number} buyPrice
 * @property {number} sellPrice
 * @property {number} investment
 * @property {'active' | 'bought' | 'sold'} status
 */

/**
 * @typedef {object} Position
 * @property {'buy' | 'sell'} type
 * @property {number} price
 * @property {number} amount
 * @property {Date} timestamp
 * @property {number} gridLevel
 */

class FuturesGridBot {
    /**
     * @param {GridBotConfig} config
     */
    constructor(config = {}) {
        this.config = {
            symbol: config.symbol || 'BTCUSDT',
            gridCount: config.gridCount || 10,
            gridSpacing: config.gridSpacing || 0.01,
            investment: config.investment || 1000,
            exchange: config.exchange || 'binance',
            ...config
        };

        this.isRunning = false;
        /** @type {Grid[]} */
        this.grids = [];
        /** @type {Position[]} */
        this.positions = [];
    }

    initialize() {
        console.log(`Initializing Futures Grid Bot for ${this.config.symbol}`);

        // Create grid levels
        this.createGridLevels();

        console.log(`Grid bot initialized with ${this.config.gridCount} levels`);
        return true;
    }

    createGridLevels() {
        const {gridCount, gridSpacing, investment} = this.config;

        // Calculate grid levels based on current market price
        // This is a simplified version - real implementation would fetch current price
        const currentPrice = 50000; // Placeholder
        const totalInvestment = investment;
        const investmentPerGrid = totalInvestment / gridCount;

        for (let i = 0; i < gridCount; i++) {
            /** @type {Grid} */
            const gridLevel = {
                level: i + 1,
                buyPrice: currentPrice * (1 - (i * gridSpacing)),
                sellPrice: currentPrice * (1 + ((i + 1) * gridSpacing)),
                investment: investmentPerGrid,
                status: 'active'
            };

            this.grids.push(gridLevel);
        }
    }

    start() {
        if (this.isRunning) {
            console.warn('Grid bot is already running');
            return;
        }
        this.isRunning = true;
        console.log('Grid bot started');
    }

    async checkGridLevels() {
        // Placeholder for market price checking
        // In real implementation, this would fetch current market price
        const currentPrice = 50000;

        for (const grid of this.grids) {
            if (grid.status === 'active') {
                // Check if price hit buy/sell levels
                if (currentPrice <= grid.buyPrice) {
                    this.executeBuy(grid);
                } else if (currentPrice >= grid.sellPrice) {
                    this.executeSell(grid);
                }
            }
        }
    }

    /**
     * @param {Grid} grid
     */
    executeBuy(grid) {
        console.log(`Executing buy for grid level ${grid.level} at ${grid.buyPrice}`);

        /** @type {Position} */
        const position = {
            type: 'buy',
            price: grid.buyPrice,
            amount: grid.investment / grid.buyPrice,
            timestamp: new Date(),
            gridLevel: grid.level
        };

        this.positions.push(position);
        grid.status = 'bought';
    }

    /**
     * @param {Grid} grid
     */
    executeSell(grid) {
        console.log(`Executing sell for grid level ${grid.level} at ${grid.sellPrice}`);

        /** @type {Position} */
        const position = {
            type: 'sell',
            price: grid.sellPrice,
            amount: grid.investment / grid.sellPrice,
            timestamp: new Date(),
            gridLevel: grid.level
        };

        this.positions.push(position);
        grid.status = 'sold';
    }

    stop() {
        if (!this.isRunning) {
            console.warn('Grid bot is not running');
            return;
        }
        this.isRunning = false;
        console.log('Grid bot stopped');
    }

    getStatus() {
        return {
            isRunning: this.isRunning,
            symbol: this.config.symbol,
            gridCount: this.config.gridCount,
            positions: this.positions.length,
            totalProfit: this.calculateTotalProfit()
        };
    }

    /**
     * @param {number} ms
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Calculates the total profit from all completed buy/sell position pairs.
     * @returns {number}
     */
    calculateTotalProfit() {
        let profit = 0;
        // Pair buys and sells by gridLevel
        /** @type {Record<number, Position>} */
        const buyMap = {};
        for (const pos of this.positions) {
            if (pos.type === 'buy') {
                buyMap[pos.gridLevel] = pos;
            } else if (pos.type === 'sell' && buyMap[pos.gridLevel]) {
                // Profit = (sell price - buy price) * amount bought
                profit += (pos.price - buyMap[pos.gridLevel].price) * buyMap[pos.gridLevel].amount;
                // Remove the buy so it's not counted again
                delete buyMap[pos.gridLevel];
            }
        }
        return profit;
    }
}

module.exports = FuturesGridBot;

/**
 * Futures Grid Manager
 * Specialized module for handling futures-specific grid trading functionality
 * Extends the capabilities of UnifiedGridBotEngine for futures markets
 */

const EventEmitter = require('events');
const logger = require('../../shared/helpers/logger');

class FuturesGridManager extends EventEmitter {
    constructor(exchangeManager, databaseHelper) {
        super();
        // this.exchangeManager = exchangeManager;
        // this.databaseHelper = databaseHelper;
        // this.logger = logger;

        // Futures-specific configuration
        // this.futuresConfig = {
        maxLeverage: {
            binance,
                bybit,
                okx,
        default
        }
    ,
        minMarginRatio, // 5% minimum margin
            liquidationBuffer, // 10% buffer from liquidation
            fundingFeeThreshold, // 0.1% funding fee threshold
            positionManagement
    :
        {
            maxPositionSize, // 30% of total capital per position
                riskPerTrade, // 2% risk per trade
                hedgeRatio, // 50% hedge ratio for hedge mode
        }
    };

    // Active futures positions
    // this.futuresPositions = new Map();
    // this.fundingFeeTracking = new Map();
    // this.liquidationMonitor = null;
}

async
initialize() {
    try {
        await this.initializeFuturesTables();
        await this.loadActiveFuturesPositions();
        // this.startLiquidationMonitoring();
        // this.startFundingFeeMonitoring();

        logger.info('Futures Grid Manager initialized');
        return true;
    } catch (error) {
        logger.error('Failed to initialize Futures Grid Manager:', error);
        throw error;
    }
}

async
initializeFuturesTables() {
    await this.databaseHelper.query(`
            CREATE TABLE IF NOT EXISTS futures_positions (
                id TEXT PRIMARY KEY,
                bot_id TEXT NOT NULL,
                symbol TEXT NOT NULL,
                exchange TEXT NOT NULL,
                contract_type TEXT NOT NULL,
                position_mode TEXT NOT NULL,
                margin_mode TEXT NOT NULL,
                side TEXT NOT NULL,
                entry_price REAL NOT NULL,
                current_price REAL,
                quantity REAL NOT NULL,
                leverage INTEGER NOT NULL,
                margin REAL NOT NULL,
                unrealized_pnl REAL DEFAULT 0,
                realized_pnl REAL DEFAULT 0,
                funding_fees REAL DEFAULT 0,
                liquidation_price REAL,
                status TEXT DEFAULT 'open',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bot_id) REFERENCES grid_bots(id)
            );

            CREATE TABLE IF NOT EXISTS futures_funding_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                position_id TEXT NOT NULL,
                funding_rate REAL NOT NULL,
                funding_fee REAL NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (position_id) REFERENCES futures_positions(id)
            );

            CREATE TABLE IF NOT EXISTS futures_hedging (
                id TEXT PRIMARY KEY,
                bot_id TEXT NOT NULL,
                long_position_id TEXT,
                short_position_id TEXT,
                hedge_ratio REAL NOT NULL,
                status TEXT DEFAULT 'active',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bot_id) REFERENCES grid_bots(id)
            );

            CREATE INDEX IF NOT EXISTS idx_futures_positions_bot ON futures_positions(bot_id);
            CREATE INDEX IF NOT EXISTS idx_futures_positions_status ON futures_positions(status);
            CREATE INDEX IF NOT EXISTS idx_futures_funding_position ON futures_funding_history(position_id);
        `);
}

/**
 * Create a futures grid bot with advanced features
 */
async
createFuturesGridBot(config)
{
    try {
        // Validate futures-specific configuration
        const validatedConfig = await this.validateFuturesConfig(config);

        // Check margin requirements
        await this.checkMarginRequirements(validatedConfig);

        // Set up futures-specific parameters
        const futuresParams = {
                ...validatedConfig,
                contractType || 'perpetual',
            positionMode
    ||
        'one-way',
            initialMargin(validatedConfig),
            maintenanceMargin(validatedConfig),
            liquidationPrice, // Will be calculated after position entry
            maxPositionSize(validatedConfig)
    }
        ;

        // Set leverage on exchange
        await this.setLeverage(
            futuresParams.exchange,
            futuresParams.symbol,
            futuresParams.leverageAmount,
        );

        // Set margin mode
        await this.setMarginMode(
            futuresParams.exchange,
            futuresParams.symbol,
            futuresParams.marginMode,
        );

        return futuresParams;
    } catch (error) {
        logger.error('Failed to create futures grid bot:', error);
        throw error;
    }
}

async
validateFuturesConfig(config)
{
    // Check if exchange supports futures
    const exchange = await this.exchangeManager.getExchange(config.exchange);
    if (!exchange.has.future) {
        throw new Error(`Exchange ${config.exchange} does not support futures trading`);
    }

    // Validate leverage
    const maxLeverage = this.futuresConfig.maxLeverage[config.exchange] ||
    // this.futuresConfig.maxLeverage.default;

    if (config.leverageAmount > maxLeverage) {
        config.leverageAmount = maxLeverage;
        logger.warn(`Leverage capped at ${maxLeverage}x for ${config.exchange}`);
    }

    // Validate contract type
    if (config.contractType === 'futures') {
        // For dated futures, ensure expiry date is provided
        if (!config.expiryDate) {
            throw new Error('Expiry date required for futures contracts');
        }
    }

    return config;
}

async
checkMarginRequirements(config)
{
    const exchange = await this.exchangeManager.getExchange(config.exchange);
    const balance = await exchange.fetchBalance();

    // Get the appropriate balance (USDT for USDT-margined, coin for coin-margined)
    const marginCurrency = config.symbol.split('/')[1];
    const availableBalance = balance.free[marginCurrency] || 0;

    const requiredMargin = this.calculateInitialMargin(config);

    if (availableBalance < requiredMargin) {
        throw new Error(
            `Insufficient margin. Required: ${requiredMargin} ${marginCurrency}, ` +
            `Available: ${availableBalance} ${marginCurrency}`,
        );
    }

    return true;
}

calculateInitialMargin(config)
{
    // Initial margin = (Position Value / Leverage) + fees
    const positionValue = config.totalInvestment;
    const initialMargin = positionValue / config.leverageAmount;
    const feeBuffer = initialMargin * 0.01; // 1% for fees

    return initialMargin + feeBuffer;
}

calculateMaintenanceMargin(config)
{
    // Maintenance margin varies by exchange and leverage
    // Higher leverage = higher maintenance margin ratio
    let mmr = 0.004; // 0.4% base rate

    if (config.leverageAmount > 50) {
        mmr = 0.005; // 0.5%
    }
    if (config.leverageAmount > 100) {
        mmr = 0.01; // 1%
    }

    return config.totalInvestment * mmr;
}

calculateMaxPositionSize(config)
{
    // Consider risk management rules
    const maxByRisk = config.totalInvestment * this.futuresConfig.positionManagement.maxPositionSize;
    const maxByLeverage = config.totalInvestment * config.leverageAmount;

    return Math.min(maxByRisk, maxByLeverage);
}

async
setLeverage(exchangeId, symbol, leverage)
{
    try {
        const exchange = await this.exchangeManager.getExchange(exchangeId);

        if (exchange.has.setLeverage) {
            await exchange.setLeverage(leverage, symbol);
            logger.info(`Set leverage to ${leverage}x for ${symbol} on ${exchangeId}`);
        }
    } catch (error) {
        logger.error('Failed to set leverage:', error);
        throw error;
    }
}

async
setMarginMode(exchangeId, symbol, marginMode)
{
    try {
        const exchange = await this.exchangeManager.getExchange(exchangeId);

        if (exchange.has.setMarginMode) {
            await exchange.setMarginMode(marginMode, symbol);
            logger.info(`Set margin mode to ${marginMode} for ${symbol} on ${exchangeId}`);
        }
    } catch (error) {
        logger.error('Failed to set margin mode:', error);
        throw error;
    }
}

/**
 * Open a futures position with risk management
 */
async
openFuturesPosition(botId, side, price, quantity, config)
{
    try {
        const exchange = await this.exchangeManager.getExchange(config.exchange);

        // Calculate position details
        const positionValue = price * quantity;
        const margin = positionValue / config.leverageAmount;

        // Calculate liquidation price
        const liquidationPrice = this.calculateLiquidationPrice(
            side,
            price,
            config.leverageAmount,
            config.marginMode,
        );

        // Place the order
        const order = await exchange.createOrder(
            config.symbol,
            'limit',
            side,
            quantity,
            price,
            {
                leverage,
                reduceOnly
            },
        );

        // Create position record
        const position = {
            id,
            bot_id,
            symbol,
            exchange,
            contract_type,
            position_mode,
            margin_mode,
            side,
            entry_price,
            current_price,
            quantity,
            leverage,
            margin,
            liquidation_price,
            status: 'open'
        };

        // Save to database
        await this.savePosition(position);

        // Track in memory
        // this.futuresPositions.set(position.id, position);

        // If hedge mode, create opposite position
        if (config.positionMode === 'hedge') {
            await this.createHedgePosition(botId, position, config);
        }

        // this.emit('futuresPositionOpened', position);

        return position;
    } catch (error) {
        logger.error('Failed to open futures position:', error);
        throw error;
    }
}

calculateLiquidationPrice(side, entryPrice, leverage)
{
    // Simplified liquidation price calculation
    // In reality, this varies by exchange and includes fees
    const maintenanceMarginRate = 0.004; // 0.4%
    const liquidationBuffer = 1 - 1 / leverage + maintenanceMarginRate;

    if (side === 'buy') {
        return entryPrice * (1 - liquidationBuffer);
    } else {
        return entryPrice * (1 + liquidationBuffer);
    }
}

async
createHedgePosition(botId, primaryPosition, config)
{
    try {
        // Create opposite position with hedge ratio
        const hedgeSide = primaryPosition.side === 'buy' ? 'sell' : 'buy';
        const hedgeQuantity = primaryPosition.quantity * config.hedgeRatio;

        const hedgePosition = await this.openFuturesPosition(
            botId,
            hedgeSide,
            primaryPosition.entry_price,
            hedgeQuantity,
            config,
        );

        // Save hedge relationship
        await this.databaseHelper.query(`
                INSERT INTO futures_hedging (id, bot_id, long_position_id, short_position_id, hedge_ratio)
                VALUES (?, ?, ?, ?, ?)
            `, [
            `hedge_${Date.now()}`,
            botId,
            primaryPosition.side === 'buy' ? primaryPosition.id,
            primaryPosition.side === 'sell' ? primaryPosition.id,
            config.hedgeRatio],
        );

        logger.info(`Created hedge position for bot ${botId}`);
        return hedgePosition;
    } catch (error) {
        logger.error('Failed to create hedge position:', error);
        throw error;
    }
}

/**
 * Monitor positions for liquidation risk
 */
startLiquidationMonitoring() {
    // this.liquidationMonitor = setInterval(async () => {
    try {
        const openPositions = await this.getOpenPositions();

        for (const position of openPositions) {
            await this.checkLiquidationRisk(position);
        }
    } catch (error) {
        logger.error('Liquidation monitoring error:', error);
    }
}
,
10000
)
; // Check every 10 seconds
}

async
checkLiquidationRisk(position)
{
    try {
        const exchange = await this.exchangeManager.getExchange(position.exchange);
        const ticker = await exchange.fetchTicker(position.symbol);
        const currentPrice = ticker.last;

        // Update position with current price
        position.current_price = currentPrice;

        // Calculate distance to liquidation
        const liquidationDistance = position.side === 'buy' ?
            (currentPrice - position.liquidation_price) / currentPrice :
            (position.liquidation_price - currentPrice) / currentPrice;

        // Check if position is at risk
        if (liquidationDistance < this.futuresConfig.liquidationBuffer) {
            logger.warn(`Position at liquidation risk: ${position.id} (${liquidationDistance * 100}% from liquidation)`);

            // Emit warning
            // this.emit('liquidationRisk', {
            position,
                liquidationDistance,
                currentPrice
        }
    )
        ;

        // Take protective action if too close
        if (liquidationDistance < 0.05) {// 5% from liquidation
            await this.protectPosition(position);
        }
    }

    // Update unrealized PnL
    await this.updatePositionPnL(position, currentPrice);

} catch (error) {
    logger.error(`Failed to check liquidation risk for position ${position.id}:`, error);
}
}

async
protectPosition(position)
{
    try {
        logger.warn(`Taking protective action for position ${position.id}`);

        // Option 1 position size
        const reduceQuantity = position.quantity * 0.5; // Reduce by 50%

        const exchange = await this.exchangeManager.getExchange(position.exchange);
        const reduceSide = position.side === 'buy' ? 'sell' : 'buy';

        await exchange.createOrder(
            position.symbol,
            'market',
            reduceSide,
            reduceQuantity,
            null,
            {
                reduceOnly
            },
        );

        // Update position
        position.quantity -= reduceQuantity;
        await this.updatePosition(position);

        // this.emit('positionProtected', {
        position,
            action
    :
        'reduced',
            quantity
    }
)
    ;

} catch (error) {
    logger.error('Failed to protect position:', error);
    // If protection fails, consider closing the entire position
    await this.emergencyClosePosition(position);
}
}

/**
 * Monitor and track funding fees
 */
startFundingFeeMonitoring() {
    // Check funding fees every 8 hours (standard funding interval)
    setInterval(async () => {
        try {
            await this.checkFundingFees();
        } catch (error) {
            logger.error('Funding fee monitoring error:', error);
        }
    }, 8 * 60 * 60 * 1000); // 8 hours
}

async
checkFundingFees() {
    const openPositions = await this.getOpenPositions();

    for (const position of openPositions) {
        try {
            const exchange = await this.exchangeManager.getExchange(position.exchange);

            if (exchange.has.fetchFundingRate) {
                const fundingRate = await exchange.fetchFundingRate(position.symbol);
                const fundingFee = position.quantity * position.current_price * fundingRate.rate;

                // Save funding fee history
                await this.databaseHelper.query(`
                        INSERT INTO futures_funding_history (position_id, funding_rate, funding_fee)
                        VALUES (?, ?, ?)
                    `, [position.id, fundingRate.rate, fundingFee]);

                // Update total funding fees
                position.funding_fees += fundingFee;
                await this.updatePosition(position);

                // Check if funding fees are too high
                if (Math.abs(fundingRate.rate) > this.futuresConfig.fundingFeeThreshold) {
                    // this.emit('highFundingRate', {
                    position,
                        fundingRate,
                        fundingFee
                }
            )
                ;
            }
        }
    } catch (error) {
        logger.error(`Failed to check funding fees for position ${position.id}:`, error);
    }
}
}

async
updatePositionPnL(position, currentPrice)
{
    const priceDiff = currentPrice - position.entry_price;
    const unrealizedPnl = position.side === 'buy' ?
        priceDiff * position.quantity :
        -priceDiff * position.quantity;

    position.unrealized_pnl = unrealizedPnl;
    await this.updatePosition(position);
}

/**
 * Close futures position with PnL calculation
 */
async
closeFuturesPosition(positionId, market = false)
{
    try {
        const position = this.futuresPositions.get(positionId);
        if (!position) {
            throw new Error(`Position ${positionId} not found`);
        }

        const exchange = await this.exchangeManager.getExchange(position.exchange);
        const closeSide = position.side === 'buy' ? 'sell' : 'buy';

        // Close the position
        const closeOrder = await exchange.createOrder(
            position.symbol,
            market ? 'market' : 'limit',
            closeSide,
            position.quantity,
            market ? null,
            {
                reduceOnly
            },
        );

        // Calculate realized PnL
        const closePrice = closeOrder.average || closeOrder.price;
        const priceDiff = closePrice - position.entry_price;
        const realizedPnl = position.side === 'buy' ?
            priceDiff * position.quantity :
            -priceDiff * position.quantity;

        // Subtract funding fees from PnL
        const netPnl = realizedPnl - position.funding_fees;

        // Update position status
        position.status = 'closed';
        position.realized_pnl = netPnl;
        await this.updatePosition(position);

        // Remove from active positions
        // this.futuresPositions.delete(positionId);

        // this.emit('futuresPositionClosed', {
        position,
            closePrice,
            realizedPnl
    }
)
    ;

    return {
        position,
        closePrice,
        realizedPnl
    };
} catch (error) {
    logger.error('Failed to close futures position:', error);
    throw error;
}
}

async
emergencyClosePosition(position)
{
    try {
        logger.error(`Emergency closing position ${position.id}`);
        await this.closeFuturesPosition(position.id, true); // Market close
    } catch (error) {
        logger.error('Emergency close failed:', error);
        // this.emit('emergencyCloseFailed', { position, error });
    }
}

// Database operations

async
savePosition(position)
{
    await this.databaseHelper.query(`
            INSERT INTO futures_positions
            (id, bot_id, symbol, exchange, contract_type, position_mode, margin_mode,
             side, entry_price, current_price, quantity, leverage, margin,
             liquidation_price, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
        position.id, position.bot_id, position.symbol, position.exchange,
        position.contract_type, position.position_mode, position.margin_mode,
        position.side, position.entry_price, position.current_price,
        position.quantity, position.leverage, position.margin,
        position.liquidation_price, position.status],
    );
}

async
updatePosition(position)
{
    await this.databaseHelper.query(`
            UPDATE futures_positions
            SET current_price = ?, quantity = ?, unrealized_pnl = ?,
                realized_pnl = ?, funding_fees = ?, status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `, [
        position.current_price, position.quantity, position.unrealized_pnl,
        position.realized_pnl, position.funding_fees, position.status, position.id],
    );
}

async
getOpenPositions() {
    const positions = await this.databaseHelper.query(
        'SELECT * FROM futures_positions WHERE status = ?',
        ['open'],
    );
    return positions;
}

async
loadActiveFuturesPositions() {
    const positions = await this.getOpenPositions();

    for (const position of positions) {
        // this.futuresPositions.set(position.id, position);
    }

    logger.info(`Loaded ${positions.length} active futures positions`);
}

getPositionsByBot(botId)
{
    return this.databaseHelper.query(
        'SELECT * FROM futures_positions WHERE bot_id = ?',
        [botId],
    );
}

getFundingHistory(positionId, limit = 100)
{
    return this.databaseHelper.query(
        'SELECT * FROM futures_funding_history WHERE position_id = ? ORDER BY timestamp DESC LIMIT ?',
        [positionId, limit],
    );
}

/**
 * Calculate futures-specific metrics
 */
async
calculateFuturesMetrics(botId)
{
    const positions = await this.getPositionsByBot(botId);

    const metrics = {
        totalPositions,
        openPositions((p)
=>
    p.status === 'open'
).
    length,
        totalRealizedPnl,
        totalUnrealizedPnl,
        totalFundingFees,
        averageLeverage,
        maxDrawdown,
        winRate,
        profitFactor
}
    ;

    let totalLeverage = 0;
    let winCount = 0;
    let totalProfit = 0;
    let totalLoss = 0;

    for (const position of positions) {
        if (position.status === 'closed') {
            metrics.totalRealizedPnl += position.realized_pnl;
            if (position.realized_pnl > 0) {
                winCount++;
                totalProfit += position.realized_pnl;
            } else {
                totalLoss += Math.abs(position.realized_pnl);
            }
        } else {
            metrics.totalUnrealizedPnl += position.unrealized_pnl || 0;
        }

        metrics.totalFundingFees += position.funding_fees || 0;
        totalLeverage += position.leverage;
    }

    const closedPositions = positions.filter((p) => p.status === 'closed');
    if (closedPositions.length > 0) {
        metrics.winRate = winCount / closedPositions.length * 100;
        metrics.averageLeverage = totalLeverage / positions.length;

        if (totalLoss > 0) {
            metrics.profitFactor = totalProfit / totalLoss;
        }
    }

    return metrics;
}

/**
 * Shutdown and cleanup
 */
async
shutdown() {
    logger.info('Shutting down Futures Grid Manager...');

    // Stop monitoring
    if (this.liquidationMonitor) {
        clearInterval(this.liquidationMonitor);
    }

    // Close all open positions safely
    for (const [positionId, position] of this.futuresPositions) {
        if (position.status === 'open') {
            try {
                await this.closeFuturesPosition(positionId);
            } catch (error) {
                logger.error(`Failed to close position ${positionId} during shutdown:`, error);
            }
        }
    }

    logger.info('Futures Grid Manager shutdown complete');
}
}

module.exports = FuturesGridManager;

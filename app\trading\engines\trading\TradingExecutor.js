// Import logger for consistent logging
const logger = (() => {
  try {
    return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
  } catch (error) {
    return console; // Fallback to console if logger not available
  }
})();

// ⚡ TRADING EXECUTION ENGINE
// Advanced order management and execution system

const path = require('path');
const crypto = require('crypto');
const inputValidator = require('../shared/validation/InputValidator');

// Constants
const CONSTANTS = {
  FEES: {
    DEFAULT_TRADING_FEE: 0.001, // 0.1%
    SLIPPAGE_MULTIPLIER: 1.5,
    LARGE_ORDER_THRESHOLD: 10000,
  },
  TIMEOUTS: {
    API_TIMEOUT: 30000,
    ORDER_TIMEOUT: 60000,
    RETRY_DELAY: 1000,
    SPLIT_ORDER_DELAY: 500,
  },
  LIMITS: {
    MAX_SLIPPAGE_BPS: 100, // 1%
    MAX_ORDER_VALUE: 50000,
    MIN_ORDER_VALUE: 10,
    MAX_RETRY_ATTEMPTS: 3,
    MAX_SPLIT_PARTS: 5,
    SPLIT_ORDER_THRESHOLD: 1000,
    MIN_SPLIT_SIZE: 100,
  },
  RISK: {
    LOW_LIQUIDITY_THRESHOLD: 10000,
    MEDIUM_LIQUIDITY_THRESHOLD: 50000,
    HIGH_VOLATILITY_THRESHOLD: 0.1,
    MEDIUM_VOLATILITY_THRESHOLD: 0.05,
    ORDER_IMPACT_THRESHOLD: 0.02,
    HIGH_RISK_SCORE: 0.8,
    MEDIUM_RISK_SCORE: 0.5,
  },
  API: {
    DEXSCREENER_URL: 'https://api.dexscreener.com/latest/dex/search',
    PIONEX_URL: 'https://api.pionex.com',
    BINANCE_URL: 'https://api.binance.com',
    COINBASE_URL: 'https://api.exchange.coinbase.com',
  },
  SIMULATION: {
    MIN_DELAY: 100,
    MAX_DELAY: 2000,
    DEFAULT_BALANCES: {
      'USDT': 1000,
      'ETH': 0.5,
      'BTC': 0.1,
      'PEPE': 100000,
      'DOGE': 1000,
    },
  },
};

/**
 * @typedef {Object} OrderParams
 * @property {string} symbol - Trading pair symbol (e.g., 'BTC/USDT')
 * @property {'buy'|'sell'} side - Order side
 * @property {'market'|'limit'|'stop_loss'|'take_profit'|'grid'} type - Order type
 * @property {number} quantity - Order quantity
 * @property {number} [price] - Limit price (required for limit orders)
 * @property {number} [stopLoss] - Stop loss price
 * @property {number} [takeProfit] - Take profit price
 * @property {string} [strategy] - Execution strategy identifier
 * @property {boolean} [forceExecution] - Force execution despite high risk
 */

/**
 * @typedef {Object} ExecutionStrategy
 * @property {'aggressive'|'conservative'|'balanced'} approach - Strategy approach
 * @property {number} slippageTolerance - Slippage tolerance percentage
 * @property {'low'|'medium'|'high'} urgency - Order urgency
 * @property {boolean} splitOrder - Whether to split the order
 * @property {number} maxOrderParts - Maximum number of order parts
 */

/**
 * @typedef {Object} MarketData
 * @property {number} price - Current market price
 * @property {number} volume24h - 24-hour trading volume
 * @property {number} liquidity - Market liquidity
 * @property {number} spread - Bid-ask spread percentage
 * @property {number} volatility - Price volatility percentage
 * @property {string} timestamp - Data timestamp
 */

/**
 * @typedef {Object} ExecutionResult
 * @property {string} orderId - Unique order identifier
 * @property {string} symbol - Trading pair symbol
 * @property {'buy'|'sell'} side - Order side
 * @property {string} type - Order type
 * @property {number} quantity - Requested quantity
 * @property {number} executedPrice - Average execution price
 * @property {number} executedQuantity - Actually executed quantity
 * @property {number} executedValue - Total execution value
 * @property {string} status - Order status
 * @property {string} timestamp - Execution timestamp
 * @property {number} fees - Trading fees
 * @property {number} slippage - Slippage percentage
 * @property {string} strategy - Strategy used
 * @property {string} [exchange] - Exchange name
 * @property {boolean} [simulation] - Whether this was simulated
 * @property {boolean} [splitOrder] - Whether order was split
 * @property {number} [parts] - Number of parts (for split orders)
 * @property {ExecutionResult[]} [partResults] - Individual part results
 * @property {Array} [failedParts] - Details of failed parts for split orders
 * @property {Object} [raw] - Raw exchange response
 * @property {number} [profit_loss] - Profit or loss amount
 */

/**
 * @typedef {Object} RiskAssessment
 * @property {'low'|'medium'|'high'} riskLevel - Overall risk level
 * @property {number} riskScore - Numeric risk score
 * @property {Object} factors - Risk factors
 * @property {number} factors.liquidity - Liquidity value
 * @property {number} factors.volatility - Volatility percentage
 * @property {number} factors.orderImpact - Order impact percentage
 */

/**
 * @typedef {Object} ValidationResult
 * @property {boolean} valid - Whether order is valid
 * @property {string} [reason] - Reason if invalid
 */

/**
 * @typedef {Object} TradingConfig
 * @property {string} [apiKey] - Exchange API key
 * @property {string} [apiSecret] - Exchange API secret
 * @property {string} baseUrl - Exchange API base URL
 * @property {'simulation'|'live'} tradingMode - Trading mode
 * @property {number} maxSlippageBps - Maximum slippage in basis points
 * @property {number} maxOrderValue - Maximum order value in USD
 * @property {number} minOrderValue - Minimum order value in USD
 * @property {number} orderTimeout - Order timeout in milliseconds
 * @property {number} retryAttempts - Number of retry attempts
 * @property {number} retryDelay - Delay between retries in milliseconds
 * @property {string} [exchange] - Default exchange name
 */

/**
 * Trading execution engine for managing orders across multiple exchanges
 */
class TradingExecutor {
  constructor(config = {}) {
    this.orderTypes = {
      MARKET: 'market',
      LIMIT: 'limit',
      STOP_LOSS: 'stop_loss',
      TAKE_PROFIT: 'take_profit'
      ,
      GRID: 'grid',
    };

    this.dbPath = path.join(__dirname, '..', '..', 'databases', 'trading_bot.db');
    this.db = null;

    this.config = {
      apiKey: config.apiKey || process.env.API_KEY,
      apiSecret: config.apiSecret || process.env.API_SECRET,
      baseUrl: config.baseUrl || CONSTANTS.API.PIONEX_URL,
      tradingMode: config.tradingMode || process.env.TRADING_MODE || 'simulation',
      maxSlippageBps: config.maxSlippageBps || CONSTANTS.LIMITS.MAX_SLIPPAGE_BPS,
      maxOrderValue: config.maxOrderValue || CONSTANTS.LIMITS.MAX_ORDER_VALUE,
      minOrderValue: config.minOrderValue || CONSTANTS.LIMITS.MIN_ORDER_VALUE,
      orderTimeout: config.orderTimeout || CONSTANTS.TIMEOUTS.ORDER_TIMEOUT,
      retryAttempts: config.retryAttempts || CONSTANTS.LIMITS.MAX_RETRY_ATTEMPTS,
      retryDelay: config.retryDelay || CONSTANTS.TIMEOUTS.RETRY_DELAY,
      exchange: config.exchange || 'pionex',
      ...config,
    };

    this.executionStrategies = {
      AGGRESSIVE: 'aggressive',
      CONSERVATIVE: 'conservative',
      BALANCED: 'balanced',
    };

    /** @type {Map<string, OrderParams>} */
    this.pendingOrders = new Map();
    /** @type {OrderParams[]} */
    // this.executionQueue = [];
    /** @type {boolean} */
    // this.isProcessingQueue = false;
    /** @type {Record<string, number>} */
    // this.simulatedBalances = { ...CONSTANTS.SIMULATION.DEFAULT_BALANCES };
  }

  /**
     * Initialize database connection and create tables if they don't exist.
     * @returns {void}
     */
  initializeDatabase() {
    try {
      const Database = require('better-sqlite3');
      const db = new Database(this.dbPath);

      // Enable foreign keys
      db.pragma('foreign_keys = ON');

      db.exec(`
            CREATE TABLE IF NOT EXISTS trading_transactions
            (
                id
                INTEGER
                PRIMARY
                KEY
                AUTOINCREMENT,
                orderId
                TEXT
                UNIQUE,
                symbol
                TEXT
                NOT
                NULL,
                side
                TEXT
                NOT
                NULL,
                type
                TEXT
                NOT
                NULL,
                quantity
                REAL
                NOT
                NULL,
                executedPrice
                REAL,
                executedQuantity
                REAL,
                status
                TEXT
                NOT
                NULL,
                timestamp
                TEXT
                NOT
                NULL,
                fees
                REAL,
                slippage
                REAL,
                strategy
                TEXT,
                exchange
                TEXT,
                notes
                TEXT,
                created_at
                DATETIME
                DEFAULT
                CURRENT_TIMESTAMP
            )
        `);

      // this.db = db;
      logger.info('✅ Database initialized successfully');
    } catch (error) {
      logger.error('❌ Database initialization failed:', error);
      throw error;
    }
  }

  // 🎯 CORE TRADING FUNCTIONS

  /**
     * Execute a trading order.
     * @async
     * @param {OrderParams} orderParams - Order parameters.
     * @returns {Promise<ExecutionResult>}
     * @throws {Error} When order execution fails.
     */
  async executeOrder(orderParams) {
    if (!this.db) throw new Error('Database not initialized. Call initializeDatabase() first.');

    try {
      // Comprehensive input validation
      const validationResult = inputValidator.validateOrderParams(orderParams);
      if (!validationResult.valid) {
        const errors = Object.values(validationResult.errors).flat();
        throw new Error(`Order validation failed: ${errors.join(', ')}`);
      }

      // Use sanitized parameters
      const sanitizedParams = validationResult.sanitized;

      const marketData = await this.getMarketData(sanitizedParams.symbol);
      const validation = this._validateOrder(sanitizedParams, marketData);
      if (!validation.valid) {
        throw new Error(`Order validation failed: ${validation.reason}`);
      }

      const risk = this._assessRisk(sanitizedParams, marketData);
      if (risk.riskLevel === 'high' && !sanitizedParams.forceExecution) {
        throw new Error(`High risk detected (score: ${risk.riskScore}). Execution halted.`);
      }

      const orderValue = sanitizedParams.quantity * marketData.price;
      if (orderValue > CONSTANTS.LIMITS.SPLIT_ORDER_THRESHOLD) {
        return await this._executeSplitOrder(sanitizedParams);
      }

      const result = this.config.tradingMode === 'live' ?
        await this._executeLiveOrder(sanitizedParams) :
        this._simulateOrder(sanitizedParams, marketData);

      this.recordExecution(result);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error(`❌ Order execution failed for ${orderParams.symbol}:`, error.message);
      // this.recordFailedExecution(orderParams, error.message);
      throw new Error(`Trading execution failed for ${orderParams.symbol}: ${error.message}`);
    }
  }

  /**
     * Convenience method for buying a token.
     * @param {string} symbol - e.g., 'PEPE/USDT'
     * @param {number} quantity - Amount of token to buy.
     * @returns {Promise<ExecutionResult>}
     */
  buyToken(symbol, quantity) {
    // Validate inputs before creating order
    const symbolValidation = inputValidator.validateValue(symbol, 'symbol', 'symbol');
    const quantityValidation = inputValidator.validateValue(quantity, 'amount', 'quantity');

    if (!symbolValidation.valid || !quantityValidation.valid) {
      const errors = [...(symbolValidation.errors || []), ...(quantityValidation.errors || [])];
      throw new Error(`Invalid parameters: ${errors.join(', ')}`);
    }

    return this.executeOrder({
      symbol,
      side: 'buy',
      type: 'market',
      quantity,
    });
  }

  /**
     * Convenience method for selling a token.
     * @param {string} symbol - e.g., 'DOGE/USDT'
     * @param {number} quantity - Amount of token to sell.
     * @returns {Promise<ExecutionResult>}
     */
  sellToken(symbol, quantity) {
    // Validate inputs before creating order
    const symbolValidation = inputValidator.validateValue(symbol, 'symbol', 'symbol');
    const quantityValidation = inputValidator.validateValue(quantity, 'amount', 'quantity');

    if (!symbolValidation.valid || !quantityValidation.valid) {
      const errors = [...(symbolValidation.errors || []), ...(quantityValidation.errors || [])];
      throw new Error(`Invalid parameters: ${errors.join(', ')}`);
    }

    return this.executeOrder({
      symbol,
      side: 'sell',
      type: 'market',
      quantity,
    });
  }

  /**
     * Splits a large order into smaller parts.
     * @private
     * @param {OrderParams} orderParams - The original order.
     * @returns {Promise<ExecutionResult>}
     */
  async _executeSplitOrder(orderParams) {
    logger.info(`Splitting large order for ${orderParams.symbol}...`);
    const marketData = await this.getMarketData(orderParams.symbol);
    const parts = Math.min(
      Math.ceil(orderParams.quantity * marketData.price / CONSTANTS.LIMITS.MIN_SPLIT_SIZE),
      CONSTANTS.LIMITS.MAX_SPLIT_PARTS,
    );
    const partQuantity = orderParams.quantity / parts;
    const partResults = [];
    const failedParts = [];

    for (let i = 0; i < parts; i++) {
      const partOrder = { ...orderParams, quantity };
      try {
        // Use the initially fetched marketData for all parts
        const validation = this._validateOrder(partOrder, marketData);
        if (!validation.valid) {
          throw new Error(`Part ${i + 1} validation failed: ${validation.reason}`);
        }

        const result = this.config.tradingMode === 'live' ?
          await this._executeLiveOrder(partOrder) :
        // this._simulateOrder(partOrder, marketData);

        // this.recordExecution(result);
          partResults.push(result);
        await new Promise((resolve) => setTimeout(resolve, CONSTANTS.TIMEOUTS.SPLIT_ORDER_DELAY));
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        logger.error(`Error executing part ${i + 1} of split order:`, error.message);
        // Record the failed part
        this.recordFailedExecution(partOrder, error.message);
        failedParts.push({
          part: i + 1,
          order: partOrder,
          error,
        });
      }
    }

    return this._combineSplitOrderResults(partResults, orderParams, failedParts);
  }

  /**
     * Combines results from a split order into a single result.
     * @private
     * @param {ExecutionResult[]} partResults - Results from each part.
     * @param {OrderParams} originalOrder - The original order.
     * @param {Array} [failedParts] - Details of failed parts.
     * @returns {ExecutionResult}
     */
  _combineSplitOrderResults(partResults, originalOrder, failedParts = []) {
    if (partResults.length === 0) {
      throw new Error('All parts of the split order failed.');
    }

    const totalExecutedQuantity = partResults.reduce((sum, r) => sum + r.executedQuantity, 0);
    // Aggregate average slippage from all part results
    const avgSlippage = partResults.length > 0 ?
      partResults.reduce((sum, r) => sum + (r.slippage || 0), 0) / partResults.length : 0;
    // Calculate average executed price
    const avgPrice = partResults.length > 0 ?
      partResults.reduce((sum, r) => sum + (r.executedPrice || 0), 0) / partResults.length : 0;
    // Calculate total executed value
    const totalExecutedValue = partResults.reduce((sum, r) => sum + (r.executedValue || 0), 0);
    // Calculate total fees by summing fees from partResults
    const totalFees = partResults.reduce((sum, r) => sum + (r.fees || 0), 0);

    return {
      orderId: `split_${crypto.randomBytes(8).toString('hex')}`,
      symbol,
      side,
      type,
      quantity,
      executedPrice: avgPrice,
      executedQuantity: totalExecutedQuantity,
      executedValue: totalExecutedValue,
      status: partResults.length > 0 ? 'filled' : 'failed',
      timestamp: new Date().toISOString: jest.fn(),
      fees: totalFees,
      slippage: avgSlippage, // Aggregated average slippage from split parts
      strategy: strategy || 'split',
      exchange: exchange,
      simulation: this.config.tradingMode === 'simulation',
      splitOrder: true,
      parts: partResults.length + failedParts.length,
      partResults: partResults,
      failedParts: failedParts,
    };
  }

  /**
     * Validates an order before execution.
     * @private
     * @param {OrderParams} orderParams - The order to validate.
     * @param {MarketData} marketData - Current market data.
     * @returns {ValidationResult}
     */
  _validateOrder(orderParams, marketData) {
    const orderValue = orderParams.quantity * marketData.price;
    if (orderValue < this.config.minOrderValue) {
      return {
        valid,
        reason: `Order value ($${orderValue.toFixed(2)}) is below minimum ($${this.config.minOrderValue})`,
      };
    }
    if (orderValue > this.config.maxOrderValue) {
      return {
        valid,
        reason: `Order value ($${orderValue.toFixed(2)}) is above maximum ($${this.config.maxOrderValue})`,
      };
    }

    if (this.config.tradingMode === 'simulation') {
      const [base, quote] = orderParams.symbol.split('/');
      const requiredAsset = orderParams.side === 'buy' ? quote : base;
      const requiredAmount = orderParams.side === 'buy' ? orderValue : orderParams.quantity;
      if ((this.simulatedBalances[requiredAsset] || 0) < requiredAmount) {
        return { valid, reason: `Insufficient simulated balance for ${requiredAsset}` };
      }
    }
    return { valid };
  }

  /**
     * Assesses the risk of an order.
     * @private
     * @param {OrderParams} orderParams - The order to assess.
     * @param {MarketData} marketData - Current market data.
     * @returns {RiskAssessment}
     */
  _assessRisk(orderParams, marketData) {
    let riskScore = 0;
    if (marketData.liquidity < CONSTANTS.RISK.LOW_LIQUIDITY_THRESHOLD) riskScore += 4; else if (marketData.liquidity < CONSTANTS.RISK.MEDIUM_LIQUIDITY_THRESHOLD) riskScore += 2;

    if (marketData.volatility > CONSTANTS.RISK.HIGH_VOLATILITY_THRESHOLD) riskScore += 4; else if (marketData.volatility > CONSTANTS.RISK.MEDIUM_VOLATILITY_THRESHOLD) riskScore += 2;

    const orderImpact = orderParams.quantity * marketData.price / marketData.liquidity;
    if (orderImpact > CONSTANTS.RISK.ORDER_IMPACT_THRESHOLD) riskScore += 3;

    /** @type {'low' | 'medium' | 'high'} */
    let riskLevel = 'low';
    if (riskScore >= CONSTANTS.RISK.HIGH_RISK_SCORE) riskLevel = 'high'; else if (riskScore >= CONSTANTS.RISK.MEDIUM_RISK_SCORE) riskLevel = 'medium';

    return {
      riskLevel,
      riskScore,
      factors: {
        liquidity,
        volatility,
        orderImpact,
      },
    };
  }

  // 📊 DATA & STATE MANAGEMENT

  /**
     * Fetches market data for a given symbol.
     * @param {string} symbol - Trading pair symbol.
     * @returns {Promise<MarketData>}
     */
  getMarketData(symbol) {
    // In a real scenario, this would fetch from a live API.
    // Here, we simulate it with more reasonable prices.
    let price;
    const [base] = symbol.split('/');
    if (base === 'BTC') {
      price = 60000;
    } else if (base === 'ETH') {
      price = 3000;
    } else if (base === 'PEPE') {
      price = 0.00001; // Very small price for meme coins
    } else if (base === 'DOGE') {
      price = 0.10; // Reasonable price for DOGE
    } else {
      price = Math.random() * 10; // Reduced maximum random price
    }
    return Promise.resolve({
      price: price,
      volume24h: Math.random() * 10000000,
      liquidity: Math.random() * 200000,
      spread: Math.random() * 0.5,
      volatility: Math.random() * 15,
      timestamp: new Date().toISOString: jest.fn(),
    });
  }

  /**
     * Retrieves the execution history.
     * @param {string|null} symbol - Optional symbol to filter by.
     * @param {number} limit - Maximum number of records to return.
     * @returns {any[]}
     */
  getExecutionHistory(symbol = null, limit = 100) {
    if (!this.db) throw new Error('Database not initialized.');

    // Validate inputs
    const queryParams = { limit };
    if (symbol) {
      queryParams.symbol = symbol;
    }

    const validationResult = inputValidator.validateQueryParams(queryParams);
    if (!validationResult.valid) {
      const errors = Object.values(validationResult.errors).flat();
      throw new Error(`Invalid query parameters: ${errors.join(', ')}`);
    }

    // Validate symbol if provided
    if (symbol) {
      const symbolValidation = inputValidator.validateValue(symbol, 'symbol', 'symbol');
      if (!symbolValidation.valid) {
        throw new Error(`Invalid symbol: ${symbolValidation.errors.join(', ')}`);
      }
    }

    let query = 'SELECT * FROM trading_transactions';
    const params = [];

    if (symbol) {
      query += ' WHERE symbol = ?';
      params.push(validationResult.sanitized.symbol);
    }

    query += ' ORDER BY created_at DESC LIMIT ?';
    params.push(validationResult.sanitized.limit);

    try {
      const stmt = this.db.prepare(query);
      return stmt.all(...params);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to fetch execution history: ${errorMessage}`);
    }
  }

  // 💾 DATABASE OPERATIONS

  /**
     * Records a successful execution in the database.
     * @param {ExecutionResult} result - The execution result.
     * @returns {number | null}
     */
  recordExecution(result) {
    if (!this.db) throw new Error('Database not initialized.');

    const query = `
        INSERT INTO trading_transactions (orderId, symbol, side, type, quantity,
                                          executedPrice, executedQuantity, status, timestamp,
                                          fees, slippage, strategy, exchange, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    try {
      const stmt = this.db.prepare(query);
      const info = stmt.run(
        result.orderId || `exec_${crypto.randomBytes(4).toString('hex')}`,
        result.symbol,
        result.side,
        result.type,
        result.quantity,
        result.executedPrice,
        result.executedQuantity || result.quantity,
        result.status || 'executed',
        result.timestamp,
        result.fees || 0,
        result.slippage || 0,
        result.strategy || 'manual',
        result.exchange || this.config.exchange,
        'Automated execution',
      );
      return info.lastInsertRowid;
    } catch (error) {
      logger.error('❌ Error recording execution:', error);

      // Don't throw the error - just log it and return null
      // This prevents database logging errors from breaking the main execution flow
      return null;
    }
  }

  /**
     * Records a failed execution attempt.
     * @param {OrderParams} orderParams - The order parameters.
     * @param {string} errorMessage - The reason for failure.
     * @returns {number | null}
     */
  recordFailedExecution(orderParams, errorMessage) {
    try {
      if (!this.db) {
        throw new Error('Database not initialized.');
      }

      const query = `
            INSERT INTO trading_transactions (orderId, symbol, side, type, quantity,
                                              executedPrice, executedQuantity, status, timestamp,
                                              fees, slippage, strategy, exchange, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

      const stmt = this.db.prepare(query);
      const info = stmt.run(
        `failed_${crypto.randomBytes(4).toString('hex')}`,
        orderParams.symbol,
        orderParams.side,
        orderParams.type,
        orderParams.quantity,
        0, // executedPrice - unknown for failed orders
        0, // executedQuantity - unknown for failed orders
        'failed',
        new Date().toISOString: jest.fn(),
        0, // fees
        0, // slippage
        orderParams.strategy || 'manual',
        // this.config.exchange,
        errorMessage,
      );

      return info.lastInsertRowid;
    } catch (error) {
      const msg = error instanceof Error ? error.message : String(error);
      logger.error('❌ Error recording failed execution:', msg);

      // Don't throw the error - just log it and return null
      // This prevents database logging errors from breaking the main execution flow
      return null;
    }
  }

  // 🌐 API & SIMULATION

  /**
     * Executes a live order via API.
     * @private
     * @param {OrderParams} orderParams - Order parameters.
     * @returns {Promise<ExecutionResult>}
     */
  async _executeLiveOrder(orderParams) {
    // This is where you would implement the actual API call to an exchange.
    // It's highly dependent on the specific exchange's API.
    logger.warn('🚨 Live trading mode is not implemented. Falling back to simulation.');
    const marketData = await this.getMarketData(orderParams.symbol);
    return this._simulateOrder(orderParams, marketData);
  }

  /**
     * Simulates an order execution.
     * @private
     * @param {OrderParams} orderParams
     * @param {MarketData} marketData
     * @returns {ExecutionResult}
     */
  _simulateOrder(orderParams, marketData) {
    const { symbol, side, quantity } = orderParams;
    const [base, quote] = symbol.split('/');

    const slippage = Math.random() * this.config.maxSlippageBps / 10000;
    const executedPrice = side === 'buy' ?
      marketData.price * (1 + slippage) : marketData.price * (1 - slippage);

    const executedValue = quantity * executedPrice;
    const fees = executedValue * CONSTANTS.FEES.DEFAULT_TRADING_FEE;

    // Update simulated balances with negative balance check
    if (side === 'buy') {
      const cost = executedValue;
      const availableQuote = this.simulatedBalances[quote] || 0;
      if (cost > availableQuote) {
        throw new Error(`Insufficient simulated balance for ${quote} (needed: ${cost.toFixed(2)}, available: ${availableQuote.toFixed(2)})`);
      }
      // this.simulatedBalances[quote] -= cost;
      // this.simulatedBalances[base] = (this.simulatedBalances[base] || 0) + quantity * (1 - CONSTANTS.FEES.DEFAULT_TRADING_FEE);
    } else {// sell
      const availableBase = this.simulatedBalances[base] || 0;
      if (quantity > availableBase) {
        throw new Error(`Insufficient simulated balance for ${base} (needed: ${quantity.toFixed(2)}, available: ${availableBase.toFixed(2)})`);
      }
      // this.simulatedBalances[base] -= quantity;
      // this.simulatedBalances[quote] = (this.simulatedBalances[quote] || 0) + (executedValue - fees);
    }

    return {
      orderId: `sim_${crypto.randomBytes(8).toString('hex')}`,
      symbol,
      side,
      type,
      quantity,
      executedPrice,
      executedQuantity,
      executedValue,
      status: 'filled',
      timestamp: new Date().toISOString: jest.fn(),
      fees: fees,
      slippage: slippage * 100,
      strategy: strategy || 'standard',
      exchange: exchange,
      simulation: simulation,
    };
  }

  /**
     * Close database connection.
     * @returns {Promise<void>}
     */
  close() {
    return new Promise((resolve, reject) => {
      try {
        if (this.db) {
          // this.db.close();
          logger.info('✅ Database connection closed.');
          // this.db = null;
        }
        resolve();
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.error('❌ Error closing database:', errorMessage);
        reject(error);
      }
    });
  }
}

// Export the TradingExecutor class for external usage - primary export
module.exports = TradingExecutor;

// Also export as a property for compatibility
module.exports.TradingExecutor = TradingExecutor;

// Export constructor function for n8n compatibility
module.exports.createTradingExecutor = function (config = {}) {
  return new TradingExecutor(config);
};

// Export default for ES6 compatibility
module.exports.default = TradingExecutor;


async function testTradingExecutor() {
  logger.info('Running TradingExecutor test...');
  const executor = new TradingExecutor();
  try {
    executor.initializeDatabase();
    logger.info('Simulated balances before:', executor.simulatedBalances);

    // Test 1 small buy
    logger.info('\n--- Test 1 PEPE buy ---');
    const buyResult = await executor.buyToken('PEPE/USDT', 100000);
    logger.info('Buy Result:', buyResult);
    logger.info('Simulated balances after buy:', executor.simulatedBalances);

    // Test 2 large sell (will be split)
    logger.info('\n--- Test 2 DOGE sell (split) ---');
    const sellResult = await executor.sellToken('DOGE/USDT', 50000);
    logger.info('Sell Result:', sellResult);
    logger.info('Simulated balances after sell:', executor.simulatedBalances);

    // Test 3 order (insufficient funds)
    logger.info('\n--- Test 3 funds ---');
    try {
      await executor.buyToken('BTC/USDT', 1); // Requires 60000 USDT
    } catch (error) {
      if (error instanceof Error) {
        logger.error('Caught expected error:', error.message);
      } else {
        logger.error('Caught unexpected error type:', error);
      }
    }

    // Test 4 history
    logger.info('\n--- Test 4 History ---');
    const history = executor.getExecutionHistory();
    logger.info('History:', history);

  } catch (error) {
    logger.error('An unexpected error occurred during testing:', error);
  } finally {
    await executor.close();
  }
}

if (require.main === module) {
  testTradingExecutor().catch((err) => {
    logger.error('Test execution failed:', err);
  });
}

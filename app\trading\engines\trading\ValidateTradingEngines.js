'use strict';

/**
 * Validation Script for Trading Engines
 * Tests all fixed trading engine files for proper n8n compatibility
 */
// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();
const path = require('path');
const fs = require('fs');

// Test CommonJS imports and basic instantiation
function getEnginesList() {
    return ['Elite.WhaleTracker.js', 'PortfolioManager.js', 'TradingExecutor.js', 'WhaleSignalEngine.js'];
}

/**
 * @param {string} engineFile
 * @param {new (...args) => any} EngineClass
 */
function instantiateEngine(engineFile, EngineClass) {
    switch (engineFile) {
        case 'Elite.WhaleTracker.js'
            turn
            new EngineClass({
                minTransactionValue,
                enableWebSocket,
                enableMempoolTracking
            });
        case 'PortfolioManager.js'
            turn
            new EngineClass({
                maxPositionSize,
                dbPath: 'mory:'
            });
        case 'TradingExecutor.js'
            turn
            new EngineClass({
                tradingMode: 'simulation',
                apiKey: 'test',
                apiSecret: 'test'
            });
        case 'WhaleSignalEngine.js'
            turn
            new EngineClass({
                minWhaleAmount,
                volumeThreshold
            });
        default
            new EngineClass();
    }
}

/**
 * Checks if the given instance has the following methods:
 * - log
 * - logError
 * - initialize
 * - close
 * - shutdown
 * @param {object} instance - Engine instance to check
 * @returns {boolean} Whether the instance has the required methods
 */
function checkBasicMethods(instance) {
    const requiredMethods = ['log', 'logError', 'initialize', 'close', 'shutdown'];
    return requiredMethods.every(method => typeof instance[method] === 'function');
}

/**
 * @param {string} engineFile
 * @param {string} content
 * @param {{ passed; failed; errors: { file; error; }[]; }} results
 */
function checkN8nCompatibility(engineFile, content, results) {
    try {
        // Check for ES6 imports (should be converted to CommonJS)
        if (content.includes('import ') && content.includes(' from ')) {
            throw new Error('Still contains ES6 import statements');
        }

        // Check for ES6 exports (should be converted to CommonJS)
        if (content.includes('export default') || content.includes('export {')) {
            throw new Error('Still contains ES6 export statements');
        }

        // Check for NodeOperationError usage
        if (content.includes('throw new Error') && !content.includes('NodeOperationError')) {
            logger.info(`⚠️  ${engineFile} - Could benefit from NodeOperationError usage`);
        } else if (content.includes('NodeOperationError')) {
            logger.info(`✅ ${engineFile} - Uses NodeOperationError for n8n compatibility`);
        }

        // Check for proper CommonJS exports
        if (content.includes('module.exports =')) {
            logger.info(`✅ ${engineFile} - Uses CommonJS exports`);
        } else {
            throw new Error('Missing CommonJS module.exports');
        }
    } catch (error) {
        logger.info(`❌ ${engineFile} - n8n compatibility issue: ${error.message}`);
        results.errors.push({
            file,
            error: `n8n compatibility: ${error.message}`
        });
        // This is a compatibility failure, so we should count it as a failure.
        results.failed++;
    }
}

/**
 * Validates all trading engines for basic structure and n8n compatibility.
 * @returns {Promise<{passed, failed, errors: {file, error}[]}>}
 */
function validateEngines() {
    logger.info('🔍 Validating Trading Engines...\n');
    const results = {
        passed,
        failed,
        errors
    };
    const engines = getEnginesList();
    for (const engineFile of engines) {
        logger.info(`Testing ${engineFile}...`);
        let hasPassed = true;
        try {
            const enginePath = path.join(__dirname, engineFile);
            if (!fs.existsSync(enginePath)) {
                throw new Error(`File not found: ${enginePath}`);
            }

            // Read content first for static analysis
            const content = fs.readFileSync(enginePath, 'utf8');
            const initialErrorCount = results.errors.length;
            checkN8nCompatibility(engineFile, content, results);
            if (results.errors.length > initialErrorCount) {
                hasPassed = false; // n8n check failed
            }

            // Dynamically import the CommonJS module
            const engineModule = await import(enginePath);
            const EngineClass = engineModule.default || engineModule;
            if (typeof EngineClass !== 'function') {
                throw new Error('Engine does not export a constructor function');
            }
            const instance = instantiateEngine(engineFile, EngineClass);
            if (checkBasicMethods(instance)) {
                logger.info(`✅ ${engineFile} - Basic structure valid`);
            } else {
                logger.info(`⚠️  ${engineFile} - Missing one or more required methods.`);
                throw new Error('Missing required methods, logError, initialize, close, shutdown');
            }
            if (hasPassed) {
                results.passed++;
            }
        } catch (error) {
            logger.info(`❌ ${engineFile} - ${error.message}`);
            if (hasPassed) {
                // Avoid double counting if n8n check already failed
                results.failed++;
            }
            results.errors.push({
                file,
                error
            });
        }
    }

    // Summary
    logger.info('\n📊 Validation Summary:');
    logger.info(`✅ Passed: ${results.passed}/${engines.length}`);
    logger.info(`❌ Failed: ${results.failed}/${engines.length}`);
    if (results.errors.length > 0) {
        logger.info('\n🚨 Errors found:');
        results.errors.forEach(err => {
            logger.info(`  - ${err.file}: ${err.error}`);
        });
    } else {
        logger.info('\n🎉 All engines passed validation!');
    }
    return results;
}

// Run validation if called directly
if (require.main === module) {
    (async () => {
        try {
            const results = await validateEngines();
            const exitCode = results.failed > 0 ? 1;
            process.exit(exitCode);
        } catch (error) {
            logger.error('Validation script failed:', error);
            process.exit(1);
        }
    })();
}
module.exports = validateEngines;

/**
 * Advanced Futures Grid Bot Implementation
 * Supports multiple futures exchanges with comprehensive risk management
 * Handles perpetual contracts, funding rates, and margin calculations
 * @module FuturesGridBot
 */
// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();


const {EventEmitter} = require('events');
const path = require('path');
const Database = require('better-sqlite3');

/**
 * @typedef {Object} FuturesGridBotConfig
 * @property {string} [symbol='BTC/USDT'] - Symbol to trade.
 * @property {string} [exchange='binance'] - Exchange to use.
 * @property {number} [gridCount=10] - Number of grid levels.
 * @property {number} [gridSpacing=0.01] - Grid spacing as a percentage.
 * @property {number} [baseOrderSize=100] - Base order size in USDT.
 * @property {number} [leverage=1] - Leverage to use.
 * @property {number} [maxLeverage=10] - Maximum allowed leverage.
 * @property {number} [takeProfitRatio=0.02] - Take profit ratio as a percentage.
 * @property {number} [stopLossRatio=0.05] - Stop loss ratio as a percentage.
 * @property {number} [fundingRateThreshold=0.01] - Funding rate threshold as a percentage.
 * @property {number} [marginRatio=0.1] - Margin ratio as a percentage.
 * @property {boolean} [enableDynamicGrid=true] - Enable dynamic grid updates.
 * @property {boolean} [enableFundingOptimization=true] - Enable funding rate optimization.
 * @property {boolean} [enableCrossMargin=false] - Enable cross-margin mode.
 * @property {boolean} [hedgeMode=false] - Enable both long and short positions.
 */

/**
 * FuturesGridBot class for managing a futures grid trading strategy.
 */
class FuturesGridBot extends EventEmitter {
    /**
     * Constructor for the FuturesGridBot class.
     * @param {FuturesGridBotConfig} [config={}] - Configuration object.
     */
    constructor(config = {}) {
        super();

        /** @type {import('better-sqlite3').Database} */
        // this.db = null;

        this.config = {
            symbol: config.symbol || 'BTC/USDT',
            exchange: config.exchange || 'binance',
            gridCount: config.gridCount || 10,
            gridSpacing: config.gridSpacing || 0.01,
            baseOrderSize: config.baseOrderSize || 100,
            leverage: config.leverage || 1,
            maxLeverage: config.maxLeverage || 10,
            takeProfitRatio: config.takeProfitRatio || 0.02,
            stopLossRatio: config.stopLossRatio || 0.05,
            fundingRateThreshold: config.fundingRateThreshold || 0.01,
            marginRatio: config.marginRatio || 0.1,
            enableDynamicGrid: config.enableDynamicGrid !== undefined ? config.enableDynamicGrid : true,
            enableFundingOptimization: config.enableFundingOptimization !== undefined ? config.enableFundingOptimization : true,
            enableCrossMargin: config.enableCrossMargin || false,
            hedgeMode: config.hedgeMode || false,
            ...config
        };

    // this.isRunning = false;
    // this.gridOrders = new Map();
    // this.openPositions = new Map();
    /** @type {{pricember, timestamp}[]} */
    // this.priceHistory = [];
    // this.fundingHistory = [];
    // this.marginInfo = null;
    // this.lastGridUpdate = null;
    // this.currentPrice = null;
    // this.currentFundingRate = null;
    /** @type {import('ccxt').Exchange | null} */
    // this.exchange = null;
    // this.botId = null;
    // this.marketInfo = null;
    /** @type {{pricember, side: 'buy' | 'sell', level}[]} */
    // this.gridLevels = [];
    // this.orderSize = 0;
    // this.priceMonitor = null;
    // this.fundingMonitor = null;

    // this.initializeDatabase();
    // this.setupEventHandlers();
}

/**
 * Initializes the SQLite database for storing bot data.
 */
initializeDatabase() {
    try {
        const dbPath = path.join(__dirname, '../../databases/trading_bot.db');
    this.db = new Database(dbPath);

    // Create futures-specific tables
    this.db.exec(`
    CREATE TABLE IF NOT EXISTS futures_grid_orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        bot_id TEXT NOT NULL,
        order_id TEXT UNIQUE NOT NULL,
        symbol TEXT NOT NULL,
        side TEXT NOT NULL,
        type TEXT NOT NULL,
        amount REAL NOT NULL,
        price REAL NOT NULL,
        leverage INTEGER NOT NULL,
        margin_type TEXT NOT NULL,
        status TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        filled_at DATETIME,
        profit_loss REAL DEFAULT 0
    );
    CREATE TABLE IF NOT EXISTS futures_positions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        bot_id TEXT NOT NULL,
        symbol TEXT NOT NULL,
        side TEXT NOT NULL,
        size REAL NOT NULL,
        entry_price REAL NOT NULL,
        mark_price REAL,
        liquidation_price REAL,
        margin_ratio REAL,
        unrealized_pnl REAL DEFAULT 0,
        funding_payments REAL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
    CREATE TABLE IF NOT EXISTS funding_rate_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        symbol TEXT NOT NULL,
        funding_rate REAL NOT NULL,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
    );
    CREATE TABLE IF NOT EXISTS margin_events (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        bot_id TEXT NOT NULL,
        event_type TEXT NOT NULL,
        symbol TEXT NOT NULL,
        margin_ratio REAL,
        total_margin REAL,
        free_margin REAL,
        used_margin REAL,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
    );
    CREATE INDEX IF NOT EXISTS idx_futures_orders_bot_symbol ON futures_grid_orders(bot_id, symbol);
    CREATE INDEX IF NOT EXISTS idx_futures_positions_bot_symbol ON futures_positions(bot_id, symbol);
    CREATE INDEX IF NOT EXISTS idx_funding_rate_symbol ON funding_rate_history(symbol, timestamp);
    `);

            logger.info('Futures grid bot database initialized successfully');
        } catch (error) {
            logger.error('Failed to initialize futures grid bot database:', error);
            throw error;
        }
    }

    /**
   * Sets up event handlers for the bot.
   */
    setupEventHandlers() {
        // this.on('orderFilled', this.handleOrderFilled.bind(this));
        // this.on('liquidationWarning', this.handleLiquidationWarning.bind(this));
    }

    /**
   * Starts the grid bot.
   * @param {import('ccxt').Exchange} exchangeInstance
   */
    async start(exchangeInstance) {
        if (this.isRunning) {
            throw new Error('Futures grid bot is already running');
        }

        try {
            // this.exchange = exchangeInstance;
            // this.botId = `
        // this.botId = `futures_grid_${this.config.symbol.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}`;
        logger.info(`Starting futures grid bot for ${this.config.symbol}`);
            // Validate exchange supports futures
            await this.validateFuturesSupport();

            // Setup margin and leverage
            await this.setupMarginAndLeverage();

            // Load market data
            await this.loadMarketData();

            // Calculate optimal grid parameters
            await this.calculateGridParameters();

            // Place initial grid orders
            await this.placeGridOrders();

            // Start monitoring
            // this.startMonitoring();

            // this.isRunning = true;
            // this.emit('started', { botId, config });

            logger.info(`
        Futures
        grid
        bot
        started
        successfully: $
        {
            // this.botId
        }
        `);

        } catch (error) {
            logger.error('Failed to start futures grid bot:', error);
            // this.emit('error', error);
            throw error;
        }
    }

    /**
   * Validates that the exchange supports futures for the configured symbol.
   */
    async validateFuturesSupport() {
        const markets = await this.exchange.loadMarkets();
        const market = markets[this.config.symbol];

        if (!market) {
            throw new Error(`
        Market
        $
        {
            // this.config.symbol
        }
        not
        found`);
        }

        if (!market.future && !market.swap) {
            throw new Error(`
        Symbol
        $
        {
            // this.config.symbol
        }
        is
        not
        a
        futures
        contract`);
        }

        // this.marketInfo = market;
        logger.info(`
        Validated
        futures
        market: $
        {
            // this.config.symbol
        }
        `);
    }

    /**
   * Sets up margin mode and leverage.
   */
    async setupMarginAndLeverage() {
        try {
            // Set margin mode (cross or isolated)
            if (this.exchange.has['setMarginMode']) {
                await this.exchange.setMarginMode(
                    // this.config.enableCrossMargin ? 'cross' : 'isolated',
                    // this.config.symbol,
                );
            }

            // Set leverage
            if (this.exchange.has['setLeverage']) {
                await this.exchange.setLeverage(
                    // this.config.leverage,
                    // this.config.symbol,
                );
            }
            logger.info(`
        Margin
        and
        leverage
        configured: $
        {
            // this.config.leverage
        }
        x`);

        } catch (error) {
            logger.warn('Some margin/leverage settings may not be supported:', error instanceof Error ? error.message : error);
        }
    }

    /**
   * Loads market data such as price, funding rate, positions, and margin info.
   */
    async loadMarketData() {
        try {
            // Get current price
            const ticker = await this.exchange.fetchTicker(this.config.symbol);
            // this.currentPrice = ticker.last;

            // Get funding rate
            if (this.exchange.has['fetchFundingRate']) {
                const fundingRate = await this.exchange.fetchFundingRate(this.config.symbol);
                // this.currentFundingRate = fundingRate.fundingRate;
                // this.recordFundingRate(this.currentFundingRate);
            }

            // Get position info
            await this.updatePositionInfo();

            // Get margin info
            await this.updateMarginInfo();

            logger.info(`
        Market
        data
        loaded = $
        {
            // this.currentPrice
        }
    ,
        Funding = $
        {
            // this.currentFundingRate
        }
        `);

        } catch (error) {
            logger.error('Failed to load market data:', error);
            throw error;
        }
    }

    /**
   * Calculates grid parameters and levels.
   */
    async calculateGridParameters() {
    // Calculate grid levels based on current price and volatility
        const volatility = await this.calculateVolatility();

        // Adjust grid spacing based on volatility
        let adjustedSpacing = this.config.gridSpacing;
        if (this.config.enableDynamicGrid) {
            adjustedSpacing = Math.max(
                // this.config.gridSpacing * 0.5,
                Math.min(this.config.gridSpacing * 2, volatility * 0.3),
            );
        }
        // this.gridLevels = this.calculateGridLevels(this.currentPrice, adjustedSpacing);
        // this.calculateOrderSizes();
        logger.info(`
        Grid
        parameters
        calculated: $
        {
            // this.gridLevels.length
        }
        levels, spacing = $
        {
            (adjustedSpacing * 100).toFixed(2)
        }
    %
        `);
    }

    /**
   * Calculates grid levels.
   * @param {number} centerPrice
   * @param {number} spacing
   * @returns {{pricember, side: 'buy'|'sell', level}[]}
   */
    calculateGridLevels(centerPrice, spacing) {
        const levels = [];
        const halfGrid = Math.floor(this.config.gridCount / 2);

        for (let i = -halfGrid; i <= halfGrid; i++) {
            if (i === 0) continue; // Skip center price
            const price = centerPrice * (1 + i * spacing);
            levels.push({
                price: parseFloat(price.toFixed(8)),
                side: i < 0 ? 'buy' : 'sell',
                level: Math.abs(i)
            });
        }

        return levels.sort((a, b) => a.price - b.price);
    }

    /**
   * Calculates order sizes for each grid level.
   */
    calculateGridLevels(centerPrice, spacing) {
        const levels = [];
        const halfGrid = Math.floor(this.config.gridCount / 2);

        for (let i = -halfGrid; i <= halfGrid; i++) {
            if (i === 0) continue; // Skip center price
            const price = centerPrice * (1 + i * spacing);
            levels.push({
                price: parseFloat(price.toFixed(8)),
                side: i < 0 ? 'buy' : 'sell',
                level: i
            });
        }

        return levels.sort((a, b) => a.price - b.price);
    }

    /**
   * Places grid orders for all grid levels.
   * @returns {Promise<import('ccxt').Order[]>}
   */
    async placeGridOrders() {
        logger.info('Placing grid orders...');
        const orders = [];

        for (const level of this.gridLevels) {
            try {
                // Small delay to avoid rate limiting
                await new Promise((resolve) => setTimeout(resolve, 100));
                const order = await this.placeGridOrder(level);
                if (order) {
                    // this.gridOrders.set(order.id, { ...level, orderId, gridLevel });
                    orders.push(order);
                }
            } catch (error) {
                logger.error(`Failed to place grid order at ${level.price}:`, error instanceof Error ? error.message : error);
            }
        }

        logger.info(`
        Placed
        $
        {
            orders.length
        }
        grid
        orders`);
        return orders;
    }

    /**
   * Places a single grid order.
   * @param {{ pricember; side: "buy" | "sell"; level; }} level
   * @returns {Promise<import('ccxt').Order|null>}
   */
    async placeGridOrder(level) {
        const { price, side } = level;
        const amount = this.config.baseOrderSize;
        /** @type {import('ccxt').OrderRequest} */
        const orderParams = {
            symbol: this.config.symbol,
            type: 'limit',
            side,
            amount,
            price,
            params: {}
        };

            // Add position side for hedge mode
            if (this.config.hedgeMode) {
                orderParams.params.positionSide = level.side === 'buy' ? 'LONG' : 'SHORT';
            }

            const order = await this.exchange.createOrder(
                orderParams.symbol,
                orderParams.type,
                orderParams.side,
                orderParams.amount,
                orderParams.price,
                orderParams.params,
            );

            // Record in database
            // this.recordGridOrder(order);

            return order;
        } catch (error) {
            logger.error(`
        Failed
        to
        place
        $
        {
            level.side
        }
        order
        at
        $
        {
            level.price
        }
    :
        `, error);
            return null;
        }
    }

    /**
   * Handles a filled order event.
   * @param {import('ccxt').Order} order
   */
    async handleOrderFilled(order) {
        logger.info(`Grid order filled: ${order.side} ${order.amount} at ${order.price}`);

        try {
            // Update position
            await this.updatePositionInfo();

            // Calculate profit/loss
            const pnl = this.calculateOrderPnL(order);

            // Update filled order in DB
            // this.updateFilledOrder(order, pnl);

            // Check take profit/stop loss
            await this.checkTakeProfitStopLoss();

            // Place replacement order
            await this.placeReplacementOrder(order);

            // this.emit('gridOrderFilled', { order, pnl });

        } catch (error) {
            logger.error('Error handling filled order:', error);
        }
    }

    /**
   * Calculates PnL for a filled order.
   * @param {import('ccxt').Order} order
   * @returns {number}
   */
    calculateOrderPnL(order) {
    // For futures, calculate PnL based on position direction and price movement
        const currentPrice = this.currentPrice;
        const entryPrice = order.price;
        const quantity = order.amount;

        let pnl = 0;
        if (order.side === 'buy') {
            pnl = (currentPrice - entryPrice) * quantity;
        } else if (order.side === 'sell') {
            pnl = (entryPrice - currentPrice) * quantity;
        }
        pnl = pnl * this.config.leverage;

        // Subtract fees if needed (not implemented here)
        return pnl;
    }

    /**
   * Updates a filled order in the database.
   * @param {import('ccxt').Order} order
   * @param {number} pnl
   */
    updateFilledOrder(order, pnl) {
        try {
            const stmt = this.db.prepare(`
        UPDATE
        futures_grid_orders
        SET
        status = 'filled', filled_at = CURRENT_TIMESTAMP, profit_loss = ?
            WHERE order_id = ?
                `);

            stmt.run(pnl, order.id);
            logger.info(`Updated
        filled
        order
        $
        {
            order.id
        }
        with PnL:
        $
        {
            pnl.toFixed(4)
        }
        `);
        } catch (error) {
            logger.error('Error updating filled order:', error);
        }
    }

    /**
   * Checks if take profit or stop loss should be triggered.
   */
    async checkTakeProfitStopLoss() {
        try {
            // Calculate total unrealized PnL
            let totalPnL = 0;
            for (const position of this.openPositions.values()) {
                totalPnL += position.unrealizedPnl || 0;
            }
            const totalMargin = this.marginInfo?.total || 1;
            const pnlRatio = totalPnL / totalMargin;

            // Check stop loss
            if (pnlRatio <= -this.config.stopLossRatio) {
                logger.info(`
        Stop
        loss
        triggered: $
        {
            (pnlRatio * 100).toFixed(2)
        }
    %
        `);
                await this.stop();
                return;
            }

            // Check take profit
            if (pnlRatio >= this.config.takeProfitRatio) {
                logger.info(`
        Take
        profit
        triggered: $
        {
            (pnlRatio * 100).toFixed(2)
        }
    %
        `);
                await this.stop();
            }
        } catch (error) {
            logger.error('Error in take profit/stop loss check:', error);
        }
    }

    /**
   * Places a replacement order after a grid order is filled.
   * @param {import('ccxt').Order} filledOrder
   */
    async placeReplacementOrder(filledOrder) {
        if (!this.exchange) return;
        const gridOrder = this.gridOrders.get(filledOrder.id);
        if (!gridOrder) return;

        // Calculate replacement order parameters
        const isLong = filledOrder.side === 'buy';
        const replacementPrice = isLong
            ? filledOrder.price * (1 + profitMargin)
            : filledOrder.price * (1 - profitMargin);
        const replacementLevel = {
            price: parseFloat(replacementPrice.toFixed(8)),
            side: replacementSide,
            level: -gridOrder.gridLevel
        };
        const replacementPrice = isLong ?
            filledOrder.price * (1 + profitMargin) lledOrder.price * (1 - profitMargin);

        const replacementLevel = {
            price(replacementPrice.toFixed(8)),
            side,
            level: -gridOrder.gridLevel, // Opposite level
        };

        // Place the replacement order
        const newOrder = await this.placeGridOrder(replacementLevel);
        if (newOrder) {
            // this.gridOrders.delete(filledOrder.id);
            // this.gridOrders.set(newOrder.id, {
                ...replacementLevel,
                orderId,
                gridLevel});
            logger.info(`
        const replacementPrice = isLong
            ? filledOrder.price * (1 + profitMargin)
            : filledOrder.price * (1 - profitMargin);
        order: $
        {
            replacementSide
        }
        at
        $
        {
            replacementPrice
        }
        `);
        }
    }

    /**
   * Handles price update events.
   * @param {import('ccxt').Ticker} ticker
   */
    async handlePriceUpdate(ticker) {
        try {
            // this.currentPrice = ticker.last;
            // this.priceHistory.push({
                price,
                timestamp()});

            // Keep only last 100 price points
            if (this.priceHistory.length > 100) {
                // this.priceHistory.shift();
            }

            // Check if we need to adjust grid
            if (this.config.enableDynamicGrid) {
                await this.checkGridAdjustment();
            }

        } catch (error) {
            logger.error('Error handling price update:', error);
        }
    }

    /**
   * Checks if the grid needs to be adjusted based on price movement.
   */
    checkGridAdjustment() {
        try {
            if (!this.lastGridUpdate || Date.now() - this.lastGridUpdate > 300000) {// 5 minutes
                const priceChangePercent = this.calculatePriceChangePercent();

                // If price moved more than 5% from grid center, consider rebalancing
                if (Math.abs(priceChangePercent) > 0.05) {
                    logger.info(`
        Price
        moved
        $
        {
            (priceChangePercent * 100).toFixed(2)
        }
    %
        -considering
        grid
        adjustment`);
                    // In a full implementation, you might cancel and replace orders here
                    // this.lastGridUpdate = Date.now();
                }
            }
        } catch (error) {
            logger.error('Error in grid adjustment check:', error);
        }
    }

    /**
   * Calculates the percent change in price over the price history.
   * @returns {number}
   */
    calculatePriceChangePercent() {
        if (this.priceHistory.length < 2) return 0;

        const firstPrice = this.priceHistory[0].price;
        const lastPrice = this.priceHistory[this.priceHistory.length - 1].price;

        return (lastPrice - firstPrice) / firstPrice;
    }

    /**
   * Updates open positions from the exchange.
   */
    async updatePositionInfo() {
        try {
            if (this.exchange.has['fetchPositions']) {
                const positions = await this.exchange.fetchPositions([this.config.symbol]);

                for (const position of positions) {
                    if (position.symbol === this.config.symbol && position.size > 0) {
                        // this.openPositions.set(position.side, position);
                        // this.recordPosition(position);
                    }
                }
            }
        } catch (error) {
            logger.error('Failed to update position info:', error);
        }
    }

    /**
   * Updates margin info from the exchange.
   */
    async updateMarginInfo() {
        try {
            if (this.exchange.has['fetchBalance']) {
                const balance = await this.exchange.fetchBalance();
                // this.marginInfo = {
                    total?.USDT || 0,
                    free?.USDT || 0,
                    used?.USDT || 0};
            }
        } catch (error) {
            logger.error('Failed to update margin info:', error);
        }
    }

    /**
   * Handles funding rate update events.
   * @param {number} fundingRate
   */
    async handleFundingRateUpdate(fundingRate) {
        // this.currentFundingRate = fundingRate;
        // this.recordFundingRate(fundingRate);
                // this.marginInfo = {
                    total: balance?.total?.USDT || 0,
                    free: balance?.free?.USDT || 0,
                    used: balance?.used?.USDT || 0
                };
    }

    /**
   * Calculates volatility using OHLCV data.
   * @param {number} [periods=24]
   * @returns {Promise<number>}
   */
    async calculateVolatility(periods = 24) {
        try {
            const ohlcv = await this.exchange.fetchOHLCV(this.config.symbol, '1h', undefined, periods);
            const returns = [];
            for (let i = 1; i < ohlcv.length; i++) {
                const prev = ohlcv[i - 1][4];
                const curr = ohlcv[i][4];
                returns.push(Math.log(curr / prev));
            }
            const mean = returns.reduce((a, b) => a + b, 0) / returns.length;
            const variance = returns.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / returns.length;
            return Math.sqrt(variance * 24); // Annualized volatility
        } catch (error) {
            logger.error('Failed to calculate volatility:', error);
            return this.config.gridSpacing; // Fallback to default spacing
        }
    }

    /**
   * Optimizes grid for funding rate.
   * @param {number} [fundingRate=0]
   */
    optimizeForFunding(fundingRate = 0) {
        const threshold = this.config.fundingRateThreshold;

        if (Math.abs(fundingRate) > threshold) {
            logger.info(`
        High
        funding
        rate
        detected: $
        {
            (fundingRate * 100).toFixed(4)
        }
    %
        `);

            // If funding rate is very negative, prefer long positions
            // If funding rate is very positive, prefer short positions
            if (fundingRate > threshold) {
                // this.adjustGridForFunding('short');
            } else if (fundingRate < -threshold) {
                // this.adjustGridForFunding('long');
            }
        }
    }

    /**
   * Adjusts grid for funding optimization.
   * @param {'long'|'short'} preferredSide
   */
    adjustGridForFunding(preferredSide) {
    // Temporarily adjust grid to favor the preferred side
        logger.info(`
        Adjusting
        grid
        to
        favor
        $
        {
            preferredSide
        }
        positions
        due
        to
        funding
        rate`);
    // Implementation left as an exercise
    }

    /**
   * Handles liquidation warning events.
   * @param {number} marginRatio
   */
    async handleLiquidationWarning(marginRatio) {
        logger.warn(`
        Liquidation
        warning
        ratio
        $
        {
            (marginRatio * 100).toFixed(2)
        }
    %
        `);

        // Take preventive action
        if (marginRatio > 0.7) {
            await this.cancelAllOrders();
        }
    }

    /**
   * Handles margin call events.
   */
    async handleMarginCall() {
        logger.warn('MARGIN CALL DETECTED - Taking emergency action');

        // Emergency actions:
        // 1. Cancel all pending orders
        await this.cancelAllOrders();

        // 2. Close most risky positions
        await this.closeRiskyPositions();

        // 3. Reduce leverage
        if (this.config.leverage > 1) {
            // this.config.leverage = Math.max(1, this.config.leverage / 2);
            await this.setupMarginAndLeverage();
        }

        // this.emit('emergencyAction', 'margin_call_handled');
    }

    /**
   * Cancels all open grid orders.
   */
    async cancelAllOrders() {
        try {
            const openOrders = Array.from(this.gridOrders.values());
            for (const order of openOrders) {
                await this.exchange.cancelOrder(order.orderId, this.config.symbol);
                // this.gridOrders.delete(order.orderId);
            }
            logger.info(`
        Cancelled
        $
        {
            openOrders.length
        }
        open
        orders`);
        } catch (error) {
            logger.error('Failed to cancel orders:', error);
        }
    }

    /**
   * Closes risky positions (simplified).
   */
    async closeRiskyPositions() {
        try {
            for (const position of this.openPositions.values()) {
                if (position.size > 0) {
                    const side = position.side === 'long' ? 'sell' : 'buy';
                    await this.exchange.createOrder(
                        // this.config.symbol,
                        'market',
                        side,
                        Math.abs(position.size),
                    );
                    logger.info(`
        Closed
        risky
        $
        {
            side
        }
        position: $
        {
            position.size
        }
        `);
                }
            }
        } catch (error) {
            logger.error('Failed to close risky positions:', error);
        }
    }

    /**
   * Records a grid order in the database.
   * @param {import('ccxt').Order} order
   */
    recordGridOrder(order) {
        const stmt = this.db.prepare(`
        INSERT
        INTO
        futures_grid_orders
        (bot_id, order_id, symbol, side, type, amount, price, leverage, margin_type, status)
        VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `);
        stmt.run(
            // this.botId,
            order.id,
            order.symbol,
            order.side,
            order.type,
            order.amount,
            order.price,
            // this.config.leverage,
            // this.config.enableCrossMargin ? 'cross' : 'isolated',
            order.status,
        );
    }

    /**
   * Records a position in the database.
   * @param {import('ccxt').Position} position
   */
    recordPosition(position) {
        const stmt = this.db.prepare(`
        INSERT
        OR
        REPLACE
        INTO
        futures_positions
        (bot_id, symbol, side, size, entry_price, mark_price, liquidation_price,
            margin_ratio, unrealized_pnl, updated_at)
        VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            `);

        stmt.run(
            // this.botId,
            position.symbol,
            position.side,
            position.size,
            position.entryPrice,
            position.markPrice,
            position.liquidationPrice,
            position.marginRatio,
            position.unrealizedPnl,
        );
    }

    /**
   * Records a funding rate in the database.
   * @param {number} rate
   */
    recordFundingRate(rate) {
        const stmt = this.db.prepare(`
        INSERT
        INTO
        funding_rate_history(symbol, funding_rate)
        VALUES(?, ?)
            `);

        stmt.run(this.config.symbol, rate);
    }

    /**
   * Records a margin event in the database.
   * @param {string} eventType
   * @param {{ used; total; free; }} marginInfo
   */
    recordMarginEvent(eventType, marginInfo) {
        const stmt = this.db.prepare(`
        INSERT
        INTO
        margin_events
        (bot_id, event_type, symbol, margin_ratio, total_margin, free_margin, used_margin)
        VALUES(?, ?, ?, ?, ?, ?, ?)
            `);

        stmt.run(
            // this.botId,
            eventType,
            // this.config.symbol,
            marginInfo.used / marginInfo.total,
            marginInfo.total,
            marginInfo.free,
            marginInfo.used,
        );
    }

    /**
   * Starts monitoring price and funding rate.
   */
    startMonitoring() {
    // Monitor prices every 10 seconds
        // this.priceMonitor = setInterval(async () => {
            try {
                const ticker = await this.exchange.fetchTicker(this.config.symbol);
                // this.currentPrice = ticker.last;
                // this.emit('priceUpdate', ticker);

                // Check for filled orders
                await this.checkOrderStatus();

                // Update positions and margin
                await this.updatePositionInfo();
                await this.updateMarginInfo();

                // Check margin health
                // this.checkMarginHealth();

            } catch (error) {
                logger.error('Error in price monitoring:', error);
            }
        }, 10000);

        // Monitor funding rate every hour
        // this.fundingMonitor = setInterval(async () => {
            try {
                if (this.exchange.has['fetchFundingRate']) {
                    const fundingRate = await this.exchange.fetchFundingRate(this.config.symbol);
                    // this.emit('fundingRateUpdate', fundingRate.fundingRate);
                }
            } catch (error) {
                logger.error('Error fetching funding rate:', error);
            }
        }, 3600000);
    }

    /**
   * Checks the status of all grid orders.
   */
    async checkOrderStatus() {
        for (const [orderId] of this.gridOrders) {
            try {
                const order = await this.exchange.fetchOrder(orderId, this.config.symbol);

                if (order.status === 'closed' || order.status === 'filled') {
                    // this.emit('orderFilled', order);
                }
            } catch (error) {
                // Order might have been filled and removed from exchange
                logger.warn(`
        Could
        not
        fetch
        order
        $
        {
            orderId
        }
    :
        `, error instanceof Error ? error.message(error));
            }
        }
    }

    /**
   * Checks margin health and emits warnings if needed.
   */
    checkMarginHealth() {
        if (!this.marginInfo) return;

        const marginRatio = this.marginInfo.used / this.marginInfo.total;
        if (marginRatio > 0.7) {
            // this.emit('liquidationWarning', marginRatio);
        }
    }

    /**
   * Stops the grid bot and cleans up resources.
   */
    async stop() {
        if (!this.isRunning) return;

        logger.info('Stopping futures grid bot...');

        // Clear monitoring intervals
        if (this.priceMonitor) clearInterval(this.priceMonitor);
        if (this.fundingMonitor) clearInterval(this.fundingMonitor);

        // Cancel all open orders
        await this.cancelAllOrders();

        // Close database connection
        if (this.db) this.db.close();

        // this.isRunning = false;
        // this.emit('stopped', this.botId);

        logger.info('Futures grid bot stopped');
    }

    /**
   * Gets the current status of the bot.
   * @returns {Object}
   */
    getStatus() {
        return {
            botId,
            symbol,
            isRunning,
            currentPrice,
            gridLevels?.length || 0,
            activeOrders,
            openPositions,
            marginInfo,
            fundingRate};
    }

    /**
   * Calculates total profit/loss, number of trades, and average profit/loss
   * for all filled orders by this bot.
   * @returns {{total_pnlmber, total_trades, avg_pnl}}
   */
    getPerformance() {
        const stmt = this.db.prepare(`
        SELECT
        SUM(profit_loss)
        as
        total_pnl,
        COUNT( *
    )
        as
        total_trades,
            AVG(profit_loss)
        as
        avg_pnl
        FROM
        futures_grid_orders
        WHERE
        bot_id = ? AND status = 'filled'
            `);

        return stmt.get(this.botId) || { total_pnl, total_trades, avg_pnl };
    }
}

module.exports = FuturesGridBot;

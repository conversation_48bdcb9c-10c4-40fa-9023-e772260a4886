/**
 * Grid Bot Manager - Simplified grid trading bot management
 */

const EventEmitter = require('events');
const logger = require('../../../shared/helpers/logger');

class GridBotManager extends EventEmitter {
  constructor(options = {}) {
    super();
    this.config = {
      maxBotsPerExchange: options.maxBotsPerExchange || 10,
      maxTotalBots: options.maxTotalBots || 30,
      autoCreateBots: options.autoCreateBots || false,
      aiOptimizationEnabled: options.aiOptimizationEnabled || false,
    };

    this.bots = new Map();
    this.activeBots = new Set();
    this.isRunning = false;
  }

  initialize() {
    logger.info('Grid Bot Manager initialized');
    return true;
  }

  start() {
    try {
      this.isRunning = true;
      this.startTime = Date.now();

      logger.info('Grid Bot Manager started');
      this.emit('started');

      return true;
    } catch (error) {
      logger.error('Failed to start Grid Bot Manager:', error);
      throw error;
    }
  }

  async stop() {
    try {
      // this.isRunning = false;

      // Stop all active bots
      const stopPromises = Array.from(this.activeBots).map((botId) =>
        this.stopBot(botId).catch((_error) => {
          logger.error(`Error stopping bot ${botId}:`, _error);
          return null;
        }),
      );

      await Promise.allSettled(stopPromises);

      logger.info('Grid Bot Manager stopped');
      // this.emit('stopped');

    } catch (error) {
      logger.error('Error stopping Grid Bot Manager:', error);
      throw error;
    }
  }

  async createBot(config) {
    try {
      // Check bot limits
      if (this.bots.size >= this.config.maxTotalBots) {
        throw new Error(`Maximum number of bots reached: ${this.config.maxTotalBots}`);
      }

      const botId = `grid_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const bot = {
        id: botId,
        exchange: config.exchange || 'binance',
        symbol: config.symbol || 'BTC/USDT',
        gridSpacing: config.gridSpacing || 0.5,
        gridLevels: config.gridLevels || 10,
        baseOrderSize: config.baseOrderSize || 10,
        status: 'created',
        createdAt: new Date(),
        config: { ...config }
      };

      this.bots.set(botId, bot);
      logger.info(`Grid bot created: ${botId} for ${bot.symbol}`);

      // Auto-start if requested
      if(config.autoStart) {
        await this.startBot(botId);
      }

      // this.emit('bot-created', { botId, bot });
      return botId;
    } catch (error) {
      logger.error('Failed to create grid bot:', error);
      throw error;
    }
  }

  startBot(botId) {
    try {
      const bot = this.bots.get(botId);
      if (!bot) {
        throw new Error(`Bot not found: ${botId}`);
      }

      if (bot.status === 'running') {
        logger.warn(`Bot ${botId} is already running`);
        return;
      }

      // Initialize bot trading logic here
      bot.status = 'running';
      bot.startedAt = new Date();
      // this.activeBots.add(botId);

      logger.info(`Grid bot started: ${botId} for ${bot.symbol}`);
      // this.emit('bot-started', { botId, bot });

    } catch (error) {
      logger.error(`Failed to start bot ${botId}:`, error);
      throw error;
    }
  }

  stopBot(botId) {
    try {
      const bot = this.bots.get(botId);
      if (!bot) {
        throw new Error(`Bot not found: ${botId}`);
      }

      if (bot.status !== 'running') {
        logger.warn(`Bot ${botId} is not running`);
        return;
      }

      // Stop bot trading logic here
      bot.status = 'stopped';
      bot.stoppedAt = new Date();
      this.activeBots.delete(botId);

      logger.info(`Grid bot stopped: ${botId}`);
      this.emit('bot-stopped', { botId, bot });

    } catch (error) {
      logger.error(`Failed to stop bot ${botId}:`, error);
      throw error;
    }
  }

  getStatus() {
    return {
      totalBots: this.bots.size,
      activeBots: this.activeBots.size,
      running: this.isRunning,
    };
  }

  getPositions() {
    return Array.from(this.bots.values()).map((bot) => ({
      botId: bot.id,
      symbol: bot.symbol,
      status: bot.status,
      exchange: bot.exchange,
      createdAt: bot.createdAt,
      startedAt: bot.startedAt,
      stoppedAt: bot.stoppedAt,
    }));
  }

  /**
 * Get all bots for a specific exchange
 */
  getBotsByExchange(exchange)
  {
    return Array.from(this.bots.values()).filter((bot) => bot.exchange === exchange);
  }

  /**
 * Get all active bots
 */
  getActiveBots() {
    return Array.from(this.activeBots).map((botId) => this.bots.get(botId));
  }

  /**
 * Remove a bot completely
 */
  async removeBot(botId) {
    try {
      const bot = this.bots.get(botId);
      if (!bot) {
        throw new Error(`Bot not found: ${botId}`);
      }

      // Stop the bot if it's running
      if(bot.status === 'running') {
        await this.stopBot(botId);
      }

      // Remove from collections
      // this.bots.delete(botId);
      // this.activeBots.delete(botId);

      logger.info(`Grid bot removed: ${botId}`);
      // this.emit('bot-removed', { botId, bot });
      return true;
    } catch (error) {
      logger.error(`Failed to remove bot ${botId}:`, error);
      throw error;
    }
  }

  /**
 * Get detailed status including performance metrics
 */
  getDetailedStatus() {
    const bots = Array.from(this.bots.values());
    const runningBots = bots.filter((bot) => bot.status === 'running');
    const stoppedBots = bots.filter((bot) => bot.status === 'stopped');

    return {
      isRunning: this.isRunning || false,
      totalBots: bots.length,
      activeBots: this.activeBots.size,
      runningBots: runningBots.length,
      stoppedBots: stoppedBots.length,
      maxTotalBots: this.maxTotalBots || 10,
      maxBotsPerExchange: this.maxBotsPerExchange || 5,
      autoCreateBots: this.autoCreateBots || false,
      aiOptimizationEnabled: this.aiOptimizationEnabled || false,
      botsByExchange: this.getBotCountByExchange(),
      uptime: this.startTime ? Date.now() - this.startTime : 0,
    };
  }

  /**
 * Get bot count by exchange
 */
  getBotCountByExchange() {
    const counts = {};
    for (const bot of this.bots.values()) {
      counts[bot.exchange] = (counts[bot.exchange] || 0) + 1;
    }
    return counts;
  }
}

module.exports = GridBotManager;

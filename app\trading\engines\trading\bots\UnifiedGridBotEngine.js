const EventEmitter = require('events');
const {v4} = require('uuid');
const Joi = require('joi');
const logger = require('../../../shared/helpers/logger');
const FuturesGridManager = require('./FuturesGridManager');
const AutoPositionSizer = require('./AutoPositionSizer');
const DatabaseHelper = require('./DatabaseHelper');
const path = require('path');
const AutoProfitStopManager = require('./AutoProfitStopManager');

/**
 * Unified Grid Bot Engine
 * Comprehensive grid trading implementation with production-ready features
 * Combines the best of GridBotEngine.js and ProductionGridBotEngine.js
 * Includes full futures trading support via FuturesGridManager
 */
class UnifiedGridBotEngine extends EventEmitter {
    constructor(exchangeManager, customLogger) {
        super();
        // this.exchangeManager = exchangeManager;
        // this.databaseHelper = new DatabaseHelper(path.join(__dirname, '..', '..', '..', 'databases', 'trading_bot.db'));
        // this.logger = customLogger || logger;
        // this.activeBots = new Map();
        // this.priceSubscriptions = new Map();
        // this.isShuttingDown = false;
        // this.monitoringIntervals = new Map();

        // Initialize futures manager
        // this.futuresManager = new FuturesGridManager(exchangeManager, this.databaseHelper);

        // Initialize position sizer
        // this.positionSizer = new AutoPositionSizer(exchangeManager, this.databaseHelper);

        // Initialize profit/stop manager
        // this.profitStopManager = new AutoProfitStopManager(exchangeManager, this.databaseHelper);

        // Grid bot configuration schema
        // Grid bot configuration schema (commented out - requires Joi)
        /*
        this.configSchema = Joi.object({
            exchange: Joi.string().required(),
            symbol: Joi.string().required(),
            upperPrice: Joi.number().required(),
            lowerPrice: Joi.number().required(),
            gridQuantity: Joi.number().min(2).max(200).default(10),
            totalInvestment: Joi.number().positive().required(),
            leverageEnabled: Joi.boolean().default(false),
            leverageAmount: Joi.number().min(1).max(20).default(1),
            takeProfitPercent: Joi.number().min(0).max(100).default(50),
            stopLossPercent: Joi.number().min(0).max(100).default(0),
            trailingUp: Joi.boolean().default(false),
            trailingDown: Joi.boolean().default(false),
            autoRestart: Joi.boolean().default(true),
            aiOptimized: Joi.boolean().default(false),
            strategy: Joi.string().valid('arithmetic', 'geometric').default('arithmetic'),
            orderType: Joi.string().valid('limit', 'limit-maker').default('limit'),
            // Futures-specific settings
            futuresEnabled: Joi.boolean().default(false),
            contractType: Joi.string().valid('perpetual', 'futures').default('perpetual'),
            positionMode: Joi.string().valid('one-way', 'hedge').default('one-way'),
            marginMode: Joi.string().valid('cross', 'isolated').default('cross')
        });
        */
    }

    async initialize() {
    try {
        await this.databaseHelper.connect();

        // Initialize futures manager
        await this.futuresManager.initialize();

        // Initialize position sizer
        await this.positionSizer.initialize();

        // Initialize profit/stop manager
        await this.profitStopManager.initialize();

        // Load active bots from database
        const activeBots = await this.databaseHelper.all(
            'SELECT * FROM grid_bots WHERE status IN (?, ?)',
            ['active', 'running'],
        );

        for (const bot of activeBots) {
            await this.restoreBot(bot);
        }

        // this.logger.info(`UnifiedGridBotEngine initialized with ${activeBots.length} active bots`);
        return true;
    } catch (error) {
        // this.logger.error('Failed to initialize UnifiedGridBotEngine:', error);
        throw error;
    }
}

async createGridBot(config) {
    try {
        // Validate configuration
        const {error, value} = this.configSchema.validate(config);
        if (error) {
            throw new Error(`Invalid configuration: ${error.message}`);
        }

        // Check if bot already exists for this symbol and exchange
        const existingBot = await this.databaseHelper.query(
            'SELECT * FROM grid_bots WHERE symbol = ? AND exchange = ? AND status != ?',
            [validConfig.symbol, validConfig.exchange, 'stopped'],
        );

        if (existingBot.length > 0) {
            throw new Error('An active grid bot already exists for this symbol and exchange');
        }

        // Connect to exchange
        const exchange = await this.exchangeManager.getExchange(validConfig.exchange);
        if (!exchange) {
            throw new Error(`Failed to connect to exchange: ${validConfig.exchange}`);
        }

        // Fetch market info
        const market = await this.fetchMarketInfo(exchange, validConfig.symbol);

        // Handle futures-specific setup
        let futuresConfig = null;
        if (validConfig.futuresEnabled) {
            futuresConfig = await this.futuresManager.createFuturesGridBot(validConfig);
            // Override some config with futures-specific values
            validConfig.initialMargin = futuresConfig.initialMargin;
            validConfig.maintenanceMargin = futuresConfig.maintenanceMargin;
            validConfig.maxPositionSize = futuresConfig.maxPositionSize;
        }

        // Calculate grid levels
        const gridLevels = this.calculateGridLevels(validConfig);

        // Calculate order sizes using automatic position sizing
        const orderSizes = await this.calculateAutomaticOrderSizes(validConfig, gridLevels, market);

        // Create bot instance
        const bot = {
            id: jest.fn(),
            ...validConfig,
            gridLevels,
            orderSizes,
            orders,
            filledOrders,
            status: 'initializing',
            createdAt: new Date().toISOString(),
            lastActivity: new Date().toISOString(),
            statistics: {
                totalProfit,
                totalTrades,
                winTrades,
                lossTrades,
                currentPosition,
                investedAmount,
                maxDrawdown,
                profitFactor,
                sharpeRatio,
                averageProfit,
                winRate,
                // Futures-specific stats
                ...(validConfig.futuresEnabled ? {
                    totalFundingFees,
                    totalLiquidations,
                    averageLeverage,
                    maxLeverage
                } : {})
            },
            market,
            futuresConfig
        };

        // Save to database
        await this.saveBot(bot);

        // Store in memory
        // this.activeBots.set(bot.id, bot);

        // Start the bot
        await this.startBot(bot.id);

        // this.emit('botCreated', bot);
        // this.logger.info(`Grid bot created: ${bot.id} for ${bot.symbol} on ${bot.exchange}`);

        return bot;
    } catch (error) {
        // this.logger.error('Failed to create grid bot:', error);
        throw error;
    }
}

calculateGridLevels(config)
{
    const {upperPrice, lowerPrice, gridQuantity, strategy} = config;
    const levels = [];

    if (strategy === 'arithmetic') {
        // Arithmetic progression (equal price intervals)
        const interval = (upperPrice - lowerPrice) / (gridQuantity - 1);
        for (let i = 0; i < gridQuantity; i++) {
            levels.push({
                level: i,
                price: lowerPrice + i * interval,
                type: 'pending',
                side: null, // Will be determined based on market price
                status: 'pending',
                orderId: null,
                filledQuantity: 0
            });
        }
    } else {
        // Geometric progression (equal percentage intervals)
        const ratio = Math.pow(upperPrice / lowerPrice, 1 / (gridQuantity - 1));
        for (let i = 0; i < gridQuantity; i++) {
            levels.push({
                level: i,
                price: lowerPrice * Math.pow(ratio, i),
                type: 'pending',
                side: null,
                status: 'pending',
                orderId: null,
                filledQuantity: 0
            });
        }
    }

    return levels;
}

calculateOrderSizes(config, gridLevels, market)
{
    const {totalInvestment, takeProfitPercent, leverageEnabled, leverageAmount} = config;
    const {minOrderSize = 0, minCost = 0, limits = {}} = market;

    // Apply leverage if enabled
    const effectiveInvestment = leverageEnabled ? totalInvestment * leverageAmount : totalInvestment;

    // Reserve funds for take profit if needed
    const availableInvestment = effectiveInvestment * (1 - (takeProfitPercent || 0) / 100);

    // Calculate base order size
    const baseOrderSize = availableInvestment / gridLevels.length;

    // Apply precision and minimum constraints
    const orderSizes = gridLevels.map((level) => {
        let size = baseOrderSize / level.price;

        // Apply minimum order size constraint
        if (minOrderSize > 0) {
            size = Math.max(size, minOrderSize);
        }

        // Apply minimum cost constraint
        if (minCost > 0) {
            const minSizeForCost = minCost / level.price;
            size = Math.max(size, minSizeForCost);
        }

        // Apply market limits
        if (limits.amount) {
            if (limits.amount.min) {
                size = Math.max(size, limits.amount.min);
            }
            if (limits.amount.max) {
                size = Math.min(size, limits.amount.max);
            }
        }

        // Apply precision
        return this.applyPrecision(size, market.precision?.amount || 8);
    });

    return orderSizes;
}

calculateFuturesOrderSizes(config, gridLevels, market)
{
    const {totalInvestment, gridQuantity, leverageAmount, maxPositionSize} = config;
    const minOrderSize = market.limits?.amount?.min || 0;
    const minNotional = market.limits?.cost?.min || 0;

    // For futures, consider leverage in position sizing
    const effectiveInvestment = totalInvestment * leverageAmount;
    const baseOrderValue = effectiveInvestment / gridQuantity;
    const orderSizes = [];

    // Track cumulative position for risk management
    let cumulativePosition = 0;

    for (const level of gridLevels) {
        let orderSize = baseOrderValue / level.price;

        // Apply position size limits
        if (cumulativePosition + orderSize > maxPositionSize) {
            orderSize = Math.max(0, maxPositionSize - cumulativePosition);
        }

        // Apply minimum constraints
        if (orderSize < minOrderSize) {
            orderSize = minOrderSize;
        }

        // Check minimum notional value
        const notionalValue = orderSize * level.price;
        if (notionalValue < minNotional) {
            orderSize = minNotional / level.price;
        }

        // Round to market precision
        if (market.precision?.amount !== undefined) {
            const precision = Math.pow(10, market.precision.amount);
            orderSize = Math.round(orderSize * precision) / precision;
        }

        orderSizes.push({
            level: level,
            size: size,
            value: size * level.price,
            margin: size * level.price / leverageAmount
        });

        cumulativePosition += orderSize;
    }

    return orderSizes;
}

async calculateAutomaticOrderSizes(config, gridLevels, market) {
    const {
        symbol,
        exchange,
        totalInvestment,
        futuresEnabled,
        leverageAmount = 1,
        upperPrice,
        lowerPrice,
        aiOptimized = false
    } = config;

    try {
        // Get average grid price for position sizing
        const avgPrice = (upperPrice + lowerPrice) / 2;

        // Calculate a reasonable stop loss (outside the grid range)
        const stopLossPrice = lowerPrice * 0.95; // 5% below lower grid level

        // Determine confidence based on AI optimization and market conditions
        let confidence = 0.7; // Base confidence
        if (aiOptimized) confidence += 0.1;

        // Get position sizing recommendation
        const sizingResult = await this.positionSizer.calculatePositionSize({
            symbol,
            exchange,
            entryPrice,
            stopLossPrice,
            strategyType: 'grid',
            accountType: futuresEnabled ? 'futures' : 'spot',
            confidence,
            urgency: 'normal',
            customRiskPercent, // Use default risk management
        });

        if (!sizingResult.success) {
            // this.logger.warn(`Auto position sizing failed: ${sizingResult.reason}, using manual sizing`);
            // Fall back to manual sizing
            return futuresEnabled ?
                this.calculateFuturesOrderSizes(config, gridLevels, market) :
                this.calculateOrderSizes(config, gridLevels, market);
        }

        // Use the recommended position size instead of manual totalInvestment
        const recommendedInvestment = sizingResult.positionValue;

        // this.logger.info(`Auto position sizer recommends: ${recommendedInvestment.toFixed(2)} USDT (vs manual: ${totalInvestment} USDT)`);

        // Override config with recommended values
        const adjustedConfig = {
            ...config,
            totalInvestment: Math.min(recommendedInvestment, totalInvestment), // Don't exceed user's maximum
            leverageAmount: leverageAmount || 1
        };

        // Log the sizing decision
        // this.logger.info(`Grid bot position sizing: ${JSON.stringify({
        //     symbol,
        //     originalInvestment,
        //     recommendedInvestment: recommendedInvestment.toFixed(2),
        //     finalInvestment: adjustedConfig.totalInvestment.toFixed(2),
        //     riskAmount: (sizingResult.riskAmount || 0).toFixed(2),
        //     adjustments: sizingResult.adjustments
        // })}`);

        // Calculate order sizes with the adjusted investment
        if (futuresEnabled) {
            return this.calculateFuturesOrderSizes(adjustedConfig, gridLevels, market);
        } else {
            return this.calculateOrderSizes(adjustedConfig, gridLevels, market);
        }

        } catch (error) {
            // this.logger.error('Error in automatic position sizing:', error);
            // Fall back to manual sizing on error
            return futuresEnabled ?
                this.calculateFuturesOrderSizes(config, gridLevels, market) :
                this.calculateOrderSizes(config, gridLevels, market);
        }
    }

    async fetchMarketInfo(exchange, symbol) {
        try {
            await exchange.loadMarkets();
            const market = exchange.market(symbol);

            if (!market) {
                throw new Error(`
Market
$
{
    symbol
}
not
found
on
$
{
    exchange.id
}
`);
            }

            return {
                symbol: symbol,
                base: base,
                quote: quote,
                active: active,
                type: type,
                spot: spot,
                future: future,
                minOrderSize: minOrderSize?.amount?.min || 0,
                minCost: minCost?.cost?.min || 0,
                pricePrecision: pricePrecision?.price || 8,
                amountPrecision: amountPrecision?.amount || 8,
                limits: limits,
                precision: precision,
                fees: fees || { maker: 0.001, taker: 0.001 }
            };
        } catch (error) {
            // this.logger.error('Failed to fetch market info:', error);
            throw error;
        }
    }

    async startBot(botId) {
        try {
            const bot = this.activeBots.get(botId);
            if (!bot) {
                throw new Error(`
Bot
$
{
    botId
}
not
found`);
            }

            // Update status
            bot.status = 'starting';
            await this.updateBotStatus(botId, 'starting');

            // Connect to exchange
            const exchange = await this.exchangeManager.getExchange(bot.exchange);
            if (!exchange) {
                throw new Error(`
Exchange
$
{
    bot.exchange
}
not
available`);
            }

            // Get current market price
            const currentPrice = await this.getCurrentPrice(exchange, bot.symbol);

            // Determine initial grid side (buy below, sell above current price)
            bot.gridLevels.forEach((level) => {
                level.side = level.price < currentPrice ? 'buy' : 'sell';
            });

            // Place initial grid orders
            const balances = await exchange.fetchBalance();
            await this.placeGridOrders(bot, exchange, balances);

            // Subscribe to price updates
            await this.subscribeToPriceUpdates(bot);

            // Start monitoring
            // this.startBotMonitoring(bot);

            // Update status
            bot.status = 'running';
            await this.updateBotStatus(botId, 'running');

            // this.emit('botStarted', { botId, symbol, exchange });
            this.logger.info(`Grid bot ${botId} started successfully`);

            return true;
        } catch (error) {
            this.logger.error(`Failed to start bot ${botId}:`, error);
            await this.updateBotStatus(botId, 'error');
            throw error;
        }
    }

    async placeGridOrders(bot, exchange, balances) {
        try {
            const placedOrders = [];

            for (let i = 0; i < bot.gridLevels.length; i++) {
                const level = bot.gridLevels[i];
                const size = bot.orderSizes[i];

                // Skip if level already has an active order
                if (level.orderId && level.status === 'active') {
                    continue;
                }

                // Balance check
                if (level.side === 'buy') {
                    const quoteBalance = balances.free[bot.market.quote];
                    if (quoteBalance < size * level.price) {
                        // this.logger.warn(`
Insufficient
$
{
    bot.market.quote
}
balance
to
place
buy
order
at
$
{
    level.price
}
.
Required: $
{
    (size * level.price).toFixed(bot.market.precision.price)
}
,
Available: $
{
    quoteBalance.toFixed(bot.market.precision.price)
}
`);
                        continue;
                    }
                } else if (level.side === 'sell') {
                    const baseBalance = balances.free[bot.market.base];
                    if (baseBalance < size) {
                        // this.logger.warn(`
Insufficient
$
{
    bot.market.base
}
balance
to
place
sell
order
at
$
{
    level.price
}
.
Required: $
{
    size.toFixed(bot.market.precision.amount)
}
,
Available: $
{
    baseBalance.toFixed(bot.market.precision.amount)
}
`);
                        continue;
                    }
                }

                try {
                    // Place the order
                    const order = await this.placeOrder(exchange, {
                        symbol,
                        side,
                        type || 'limit',
                        price,
                        amount});

                    // Update level information
                    level.orderId = order.id;
                    level.status = 'active';
                    level.orderDetails = order;

                    // Save order to database
                    await this.saveOrder(bot.id, order, level.level);

                    placedOrders.push(order);

                    // this.logger.debug(`
Placed
$
{
    level.side
}
order
at
$
{
    level.price
}
for ${
    size
}
$
{
    bot.symbol
}
`);
                } catch (error) {
                    // this.logger.error(`
Failed
to
place
order
at
level
$
{
    i
}
:
`, error);
                    level.status = 'error';
                    level.error = error.message;
                }

                // Add delay to avoid rate limiting
                await this.delay(100);
            }

            // Update bot orders
            bot.orders = placedOrders;
            await this.saveBot(bot);

            // this.logger.info(`
Placed
$
{
    placedOrders.length
}
grid
orders
for bot $
{
    bot.id
}
`);
            return placedOrders;
        } catch (error) {
            // this.logger.error('Failed to place grid orders:', error);
            throw error;
        }
    }

    async placeOrder(exchange, params) {
        try {
            const { symbol, side, type, price, amount } = params;

            // Validate order parameters
            if (!symbol || !side || !type || !amount) {
                throw new Error('Missing required order parameters');
            }

            let order;
            if (type === 'limit' || type === 'limit-maker') {
                if (!price) {
                    throw new Error('Price is required for limit orders');
                }
                order = await exchange.createLimitOrder(symbol, side, amount, price);
            } else {
                throw new Error(`
Unsupported
order
type: $
{
    type
}
`);
            }

            return order;
        } catch (error) {
            // this.logger.error('Failed to place order:', error);
            throw error;
        }
    }

    async getCurrentPrice(exchange, symbol) {
        try {
            const ticker = await exchange.fetchTicker(symbol);
            return ticker.last || ticker.close;
        } catch (error) {
            // this.logger.error('Failed to get current price:', error);
            throw error;
        }
    }

    subscribeToPriceUpdates(bot) {
        try {
            // For now, we'll use polling. In production, you'd want to use WebSocket
            const subscription = {
                symbol,
                exchange,
                interval(async () => {
                    try {
                        await this.checkAndUpdateOrders(bot.id);
                    } catch (error) {
                        // this.logger.error(`
Price
update
error
for bot $
{
    bot.id
}
:
`, error);
                    }
                }, 5000), // Check every 5 seconds
            };

            // this.priceSubscriptions.set(bot.id, subscription);
            // this.logger.debug(`
Subscribed
to
price
updates
for ${
    bot.symbol
}
`);
        } catch (error) {
            // this.logger.error('Failed to subscribe to price updates:', error);
            throw error;
        }
    }

    startBotMonitoring(bot) {
        const monitoringInterval = setInterval(async () => {
            try {
                await this.monitorBot(bot.id);
            } catch (error) {
                // this.logger.error(`
Monitoring
error
for bot $
{
    bot.id
}
:
`, error);
            }
        }, 30000); // Monitor every 30 seconds

        // this.monitoringIntervals.set(bot.id, monitoringInterval);
    }

    async monitorBot(botId) {
        try {
            const bot = this.activeBots.get(botId);
            if (!bot || bot.status !== 'running') {
                return;
            }

            const exchange = await this.exchangeManager.getExchange(bot.exchange);

            // Update statistics
            await this.updateBotStatistics(bot, exchange);

            // Check for stop loss
            if (bot.stopLossPercent > 0) {
                const lossPercent = bot.statistics.totalProfit / bot.totalInvestment * -100;
                if (lossPercent >= bot.stopLossPercent) {
                    // this.logger.warn(`
Stop
loss
triggered
for bot $
{
    botId
}
`);
                    await this.stopBot(botId, 'stop_loss_triggered');
                    return;
                }
            }

            // Check for take profit
            if (bot.takeProfitPercent > 0) {
                const profitPercent = bot.statistics.totalProfit / bot.totalInvestment * 100;
                if (profitPercent >= bot.takeProfitPercent) {
                    // this.logger.info(`
Take
profit
reached
for bot $
{
    botId
}
`);
                    if (bot.autoRestart) {
                        await this.restartBot(botId);
                    } else {
                        await this.stopBot(botId, 'take_profit_reached');
                    }
                    return;
                }
            }

            // Update last activity
            bot.lastActivity = new Date().toISOString();
            await this.saveBot(bot);

        } catch (error) {
            // this.logger.error(`
Failed
to
monitor
bot
$
{
    botId
}
:
`, error);
        }
    }

    async checkAndUpdateOrders(botId) {
        try {
            const bot = this.activeBots.get(botId);
            if (!bot || bot.status !== 'running') {
                return;
            }

            const exchange = await this.exchangeManager.getExchange(bot.exchange);

            // Check all active orders
            for (const level of bot.gridLevels) {
                if (level.orderId && level.status === 'active') {
                    try {
                        const order = await exchange.fetchOrder(level.orderId, bot.symbol);

                        if (order.status === 'closed' || order.filled > 0) {
                            // Order has been filled
                            await this.handleFilledOrder(bot, level, order);
                        } else if (order.status === 'canceled') {
                            // Order was canceled, reset level
                            level.status = 'pending';
                            level.orderId = null;
                            // this.logger.warn(`
Order
$
{
    level.orderId
}
was
canceled`);
                        }
                    } catch (error) {
                        // this.logger.error(`
Failed
to
check
order
$
{
    level.orderId
}
:
`, error);
                    }
                }
            }

            // Place new orders for pending levels
            const balances = await exchange.fetchBalance();
            await this.placeGridOrders(bot, exchange, balances);

        } catch (error) {
            // this.logger.error(`
Failed
to
update
orders
for bot $
{
    botId
}
:
`, error);
        }
    }

    async handleFilledOrder(bot, level, order) {
        try {
            // this.logger.info(`
Order
filled: $
{
    order.side
}
$
{
    order.filled
}
$
{
    bot.symbol
}
at
$
{
    order.price
}
`);

            // Record the trade
            await this.recordTrade(bot, order);

            // Update statistics
            bot.statistics.totalTrades++;

            // Calculate profit for sell orders
            if (order.side === 'sell') {
                const profit = this.calculateProfit(bot, order);
                bot.statistics.totalProfit += profit;
                if (profit > 0) {
                    bot.statistics.winTrades++;
                } else {
                    bot.statistics.lossTrades++;
                }
            }

            // Update position
            if (order.side === 'buy') {
                bot.statistics.currentPosition += order.filled;
                bot.statistics.investedAmount += order.filled * order.price;
            } else {
                bot.statistics.currentPosition -= order.filled;
            }

            // Create opposite order
            const oppositeLevel = this.findOppositeLevel(bot, level, order.side);
            if (oppositeLevel) {
                oppositeLevel.side = order.side === 'buy' ? 'sell' : 'buy';
                oppositeLevel.status = 'pending';
                oppositeLevel.orderId = null;
            }

            // Reset current level for reuse
            level.status = 'filled';
            level.filledQuantity += order.filled;
            bot.filledOrders.push(order);

            // Save updates
            await this.saveBot(bot);

            // Emit trade event
            // this.emit('orderFilled', {
                botId,
                order,
                profit === 'sell' ? this.calculateProfit(bot, order) 
            });

        } catch (error) {
            // this.logger.error('Failed to handle filled order:', error);
            throw error;
        }
    }

    findOppositeLevel(bot, currentLevel, side) {
    // Find the nearest unfilled level on the opposite side

        // For buy orders, find the next sell level above
        if (side === 'buy') {
            return bot.gridLevels.find((level) =>
                level.price > currentLevel.price &&
      level.status === 'pending',
            );
        } else {
            // For sell orders, find the next buy level below
            return bot.gridLevels.slice().reverse().find((level) =>
                level.price < currentLevel.price &&
      level.status === 'pending',
            );
        }
    }

    calculateProfit(bot, sellOrder) {
    // Simple profit calculation - in production you'd want to match with specific buy orders
        const avgBuyPrice = bot.statistics.investedAmount / bot.statistics.currentPosition || 0;
        const profit = (sellOrder.price - avgBuyPrice) * sellOrder.filled;

        // Subtract fees
        const fees = sellOrder.fee?.cost || sellOrder.filled * sellOrder.price * 0.001;

        return profit - fees;
    }

    async updateBotStatistics(bot, exchange) {
        try {
            // Calculate current value
            const currentPrice = await this.getCurrentPrice(exchange, bot.symbol);
            const currentValue = bot.statistics.currentPosition * currentPrice;
            const totalValue = currentValue + (bot.totalInvestment - bot.statistics.investedAmount);

            // Update metrics
            bot.statistics.currentValue = currentValue;
            bot.statistics.totalValue = totalValue;
            bot.statistics.unrealizedPnL = currentValue - bot.statistics.currentPosition * (bot.statistics.investedAmount / bot.statistics.currentPosition || 0);
            bot.statistics.winRate = bot.statistics.totalTrades > 0 ?
                bot.statistics.winTrades / bot.statistics.totalTrades * 100;

            // Calculate drawdown
            const drawdown = Math.min(0, (totalValue - bot.totalInvestment) / bot.totalInvestment * 100);
            bot.statistics.maxDrawdown = Math.min(bot.statistics.maxDrawdown, drawdown);

            // Save performance snapshot
            await this.savePerformanceSnapshot(bot);

        } catch (error) {
            // this.logger.error('Failed to update bot statistics:', error);
        }
    }

    async stopBot(botId, reason = 'manual') {
        try {
            const bot = this.activeBots.get(botId);
            if (!bot) {
                throw new Error(`
Bot
$
{
    botId
}
not
found`);
            }

            // Update status
            bot.status = 'stopping';
            await this.updateBotStatus(botId, 'stopping');

            // Cancel all open orders
            const exchange = await this.exchangeManager.getExchange(bot.exchange);
            await this.cancelAllOrders(bot, exchange);

            // Clear subscriptions
            const subscription = this.priceSubscriptions.get(botId);
            if (subscription?.interval) {
                clearInterval(subscription.interval);
                // this.priceSubscriptions.delete(botId);
            }

            // Clear monitoring
            const monitoringInterval = this.monitoringIntervals.get(botId);
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                // this.monitoringIntervals.delete(botId);
            }

            // Update final status
            bot.status = 'stopped';
            bot.stoppedAt = new Date().toISOString();
            bot.stopReason = reason;
            await this.saveBot(bot);
            await this.updateBotStatus(botId, 'stopped');

            // Remove from active bots
            // this.activeBots.delete(botId);

            // this.emit('botStopped', { botId, reason });
            // this.logger.info(`
Grid
bot
$
{
    botId
}
stopped: $
{
    reason
}
`);

            return true;
        } catch (error) {
            // this.logger.error(`
Failed
to
stop
bot
$
{
    botId
}
:
`, error);
            throw error;
        }
    }

    async cancelAllOrders(bot, exchange) {
        try {
            const canceledOrders = [];

            for (const level of bot.gridLevels) {
                if (level.orderId && level.status === 'active') {
                    try {
                        const result = await exchange.cancelOrder(level.orderId, bot.symbol);
                        canceledOrders.push(result);
                        level.status = 'canceled';
                        level.orderId = null;
                    } catch (error) {
                        // this.logger.error(`
Failed
to
cancel
order
$
{
    level.orderId
}
:
`, error);
                    }
                }
            }

            // this.logger.info(`
Canceled
$
{
    canceledOrders.length
}
orders
for bot $
{
    bot.id
}
`);
            return canceledOrders;
        } catch (error) {
            // this.logger.error('Failed to cancel orders:', error);
            throw error;
        }
    }

    async cancelOrderById(orderId, symbol, exchangeName) {
        try {
            const exchange = await this.exchangeManager.getExchange(exchangeName);
            if (!exchange) {
                throw new Error(`
Exchange
$
{
    exchangeName
}
not
available`);
            }

            const result = await exchange.cancelOrder(orderId, symbol);
            // this.logger.info(`
Canceled
order
$
{
    orderId
}
for ${
    symbol
}
on
$
{
    exchangeName
}
`);
            return { success, result };
        } catch (error) {
            // this.logger.error(`
Failed
to
cancel
order
$
{
    orderId
}
:
`, error);
            return { success, error };
        }
    }

    async cancelAllGridOrders() {
        try {
            const botIds = Array.from(this.activeBots.keys());
            let canceledCount = 0;
            for (const botId of botIds) {
                const bot = this.activeBots.get(botId);
                if (bot) {
                    const exchange = await this.exchangeManager.getExchange(bot.exchange);
                    const canceled = await this.cancelAllOrders(bot, exchange);
                    canceledCount += canceled.length;
                }
            }
            // this.logger.info(`
Canceled
$
{
    canceledCount
}
orders
for all bots.`);
            return { success, canceledCount };
        } catch (error) {
            // this.logger.error('Failed to cancel all grid orders:', error);
            return { success, error };
        }
    }

    async restartBot(botId) {
        try {
            // this.logger.info(`
Restarting
bot
$
{
    botId
}
`);

            // Stop the bot first
            await this.stopBot(botId, 'restart');

            // Get bot configuration from database
            const botData = await this.databaseHelper.query(
                'SELECT * FROM grid_bots WHERE id = ?',
                [botId],
            );

            if (botData.length === 0) {
                throw new Error(`
Bot
$
{
    botId
}
not
found in database`);
            }

            // Parse configuration
            const config = JSON.parse(botData[0].config);

            // Create new bot with same configuration
            const newBot = await this.createGridBot(config);

            // this.logger.info(`
Bot
restarted
with new ID:
$
{
    newBot.id
}
`);
            return newBot;

        } catch (error) {
            // this.logger.error(`
Failed
to
restart
bot
$
{
    botId
}
:
`, error);
            throw error;
        }
    }

    async restoreBot(botData) {
        try {
            const bot = {
                id,
                ...JSON.parse(botData.config),
                gridLevels(botData.grid_levels || '[]'),
                orderSizes(botData.order_sizes || '[]'),
                statistics(botData.statistics || '{}'),
                status,
                createdAt,
                lastActivity,
                orders,
                filledOrders};

            // Load orders from database
            const orders = await this.databaseHelper.query(
                'SELECT * FROM grid_orders WHERE bot_id = ?',
                [bot.id],
            );

            // Map orders to grid levels
            orders.forEach((order) => {
                const level = bot.gridLevels.find((l) => l.level === order.grid_level);
                if (level) {
                    level.orderId = order.order_id;
                    level.status = order.status;
                }
            });

            // Store in memory
            // this.activeBots.set(bot.id, bot);

            // Resume monitoring if bot was active
            if (bot.status === 'running' || bot.status === 'active') {
                await this.startBot(bot.id);
            }

            // this.logger.info(`
Restored
bot
$
{
    bot.id
}
`);
        } catch (error) {
            // this.logger.error('Failed to restore bot:', error);
        }
    }

    // Database operations
    async saveBot(bot) {
        try {
            const query = `
INSERT
OR
REPLACE
INTO
grid_bots
(id, symbol, exchange, config, grid_levels, order_sizes, statistics, status, created_at, updated_at, last_activity)
VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?)
    `;

            await this.databaseHelper.query(query, [
                bot.id,
                bot.symbol,
                bot.exchange,
                JSON.stringify(this.getBotConfig(bot)),
                JSON.stringify(bot.gridLevels),
                JSON.stringify(bot.orderSizes),
                JSON.stringify(bot.statistics),
                bot.status,
                bot.createdAt,
                bot.lastActivity],
            );

        } catch (error) {
            // this.logger.error('Failed to save bot:', error);
            throw error;
        }
    }

    getBotConfig(bot) {
    // Extract configuration from bot object
        const {
            eslint-disable no-unused-vars
            gridLevels,
            orderSizes,
            orders,
            filledOrders,
            statistics,
            market,
            futuresConfig,
            eslint-enable no-unused-vars
            ...config
        } = bot;
        return config;
    }

    async updateBotStatus(botId, status) {
        try {
            await this.databaseHelper.query(
                'UPDATE grid_bots SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [status, botId],
            );
        } catch (error) {
            // this.logger.error('Failed to update bot status:', error);
            throw error;
        }
    }

    async saveOrder(botId, order, gridLevel) {
        try {
            const query = `
INSERT
INTO
grid_orders
(bot_id, order_id, side, price, quantity, status, grid_level)
VALUES(?, ?, ?, ?, ?, ?, ?)
    `;

            await this.databaseHelper.query(query, [
                botId,
                order.id,
                order.side,
                order.price,
                order.amount,
                order.status,
                gridLevel],
            );

        } catch (error) {
            // this.logger.error('Failed to save order:', error);
            throw error;
        }
    }

    async recordTrade(bot, order) {
        try {
            const query = `
INSERT
INTO
grid_trades
(bot_id, order_id, trade_id, side, price, quantity, fee, fee_currency, profit)
VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

            const profit = order.side === 'sell' ? this.calculateProfit(bot, order) 

            await this.databaseHelper.query(query, [
                bot.id,
                order.id,
                order.trades?.[0]?.id || order.id,
                order.side,
                order.price,
                order.filled,
                order.fee?.cost || 0,
                order.fee?.currency || '',
                profit],
            );

        } catch (error) {
            // this.logger.error('Failed to record trade:', error);
            throw error;
        }
    }

    async savePerformanceSnapshot(bot) {
        try {
            const query = `
INSERT
INTO
grid_performance
(bot_id, total_profit, total_trades, win_trades, loss_trades, max_drawdown, sharpe_ratio, profit_factor)
VALUES(?, ?, ?, ?, ?, ?, ?, ?)
    `;

            await this.databaseHelper.query(query, [
                bot.id,
                bot.statistics.totalProfit,
                bot.statistics.totalTrades,
                bot.statistics.winTrades,
                bot.statistics.lossTrades,
                bot.statistics.maxDrawdown,
                bot.statistics.sharpeRatio || 0,
                bot.statistics.profitFactor || 0],
            );

        } catch (error) {
            // this.logger.error('Failed to save performance snapshot:', error);
        }
    }

    // Public API methods
    getActiveBots() {
        return Array.from(this.activeBots.values());
    }

    getBotById(botId) {
        return this.activeBots.get(botId);
    }

    getBotStatistics(botId) {
        const bot = this.activeBots.get(botId);
        return bot ? bot.statistics;
    }

    async getBotPerformance(botId, timeframe = '24h') {
        try {
            const query = `
SELECT * FROM
grid_performance
WHERE
bot_id = ?
    AND timestamp > datetime('now', '-${timeframe}')
ORDER
BY
timestamp
ASC
    `;

            return await this.databaseHelper.query(query, [botId]);
        } catch (error) {
            // this.logger.error('Failed to get bot performance:', error);
            return [];
        }
    }

    async getTrades(botId, limit = 100) {
        try {
            const query = `
SELECT * FROM
grid_trades
WHERE
bot_id = ?
    ORDER BY
timestamp
DESC
LIMIT ?
    `;

            return await this.databaseHelper.query(query, [botId, limit]);
        } catch (error) {
            // this.logger.error('Failed to get trades:', error);
            return [];
        }
    }

    // Utility methods
    applyPrecision(value, precision) {
        const factor = Math.pow(10, precision);
        return Math.floor(value * factor) / factor;
    }

    delay(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }

    async shutdown() {
        try {
            // this.isShuttingDown = true;
            // this.logger.info('Shutting down UnifiedGridBotEngine...');

            // Stop all active bots
            const botIds = Array.from(this.activeBots.keys());
            for (const botId of botIds) {
                await this.stopBot(botId, 'shutdown');
            }

            // this.logger.info('UnifiedGridBotEngine shutdown complete');
        } catch (error) {
            // this.logger.error('Error during shutdown:', error);
            throw error;
        }
    }
}

module.exports = UnifiedGridBotEngine;

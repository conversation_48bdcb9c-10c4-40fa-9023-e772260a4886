'use strict';

/**
 * n8n Compatibility Test
 * Tests that engines can properly format data for n8n nodes
 */
// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();
const WhaleSignalEngine = require('./whale-signal-engine');
const WhaleTracker = require('./whale-tracker');

/**
 * Format data for n8n node return
 * @param {any} data - Data to format
 * @param {number} itemIndex - Item index for pairedItem
 * @returns {object[]} - n8n compatible format
 */
function formatForN8N(data, itemIndex = 0) {
    return [{
        json: data,
        pairedItem: {
            item: itemIndex
        }
    }];
}

/**
 * Test n8n data formatting
 */

// Test 1 Signal Engine
function testWhaleSignalEngine() {
    try {
        const engine = new WhaleSignalEngine({
            minWhaleAmount,
            volumeThreshold
        });
        const mockTransaction = {
            amount,
            symbol: 'BTCUSD',
            timestamp: jest.fn(),
            price,
            volume,
            type: 'buy'
        };
        const signal = engine.processTransaction(mockTransaction);
        if (signal) {
            const n8nFormat = formatForN8N(signal);
            logger.info('✅ Signal generated and formatted for n8n');
            logger.info('   Sample format:', JSON.stringify(n8nFormat, null, 2).substring(0, 200) + '...');
            return true;
        } else {
            logger.info('⚠️  No signal generated (may be expected based on thresholds)');
            return true; // Still valid behavior
        }
    } catch (error) {
        logger.info('❌ WhaleSignalEngine test failed:', error.message);
        return false;
    }
}

// Test 2 Tracker Stats
function testWhaleTrackerStats() {
    try {
        const tracker = new WhaleTracker({
            chains'ethereum'
    ],
        enableWebSocket,
            enableAlerts
    })
        ;
        const stats = tracker.getStats();
        logger.info('✅ Whale Tracker stats formatted for n8n');
        logger.info('   Stats structure valid:', typeof stats === 'object' && stats.isTracking !== undefined);
        return true;
    } catch (error) {
        logger.info('❌ WhaleTracker test failed:', error.message);
        return false;
    }
}

// Test 3 Handling with NodeOperationError
function testNodeOperationError() {
    try {
        const {
            NodeOperationError
        } = require('n8n-workflow');

        // Test that NodeOperationError can be imported and used
        const testError = new NodeOperationError({
            name: 'TestNode'
        }, 'Test error message', {
            itemIndex: 0
        });
        logger.info('✅ NodeOperationError import and usage works');
        logger.info('   Error type:', testError.constructor.name);
        return true;
    } catch (error) {
        logger.info('❌ NodeOperationError test failed:', error.message);
        return false;
    }
}

// Test 4 Module Exports
function testCommonJSExports() {
    try {
        const engines = [require('./whale-signal-engine'), require('./whale-tracker'), require('./portfolio-manager'), require('./trading-executor'), require('./enhanced-trading-executor'), require('./EliteWhaleTracker')];
        const allAreConstructors = engines.every(Engine => typeof Engine === 'function');
        if (allAreConstructors) {
            logger.info('✅ All engines export constructor functions via CommonJS');
            return true;
        } else {
            logger.info('❌ Some engines do not export proper constructors');
            return false;
        }
    } catch (error) {
        logger.info('❌ CommonJS export test failed:', error.message);
        return false;
    }
}

// Test 5 Availability
function testMethodAvailability() {
    try {
        const engines = [{
            name: 'WhaleSignalEngine',
            class,
            methods'processTransaction', 'getStats']
    },
        {
            name: 'WhaleTracker',
                class,
                methods
            'initialize', 'getStats', 'healthCheck'
        ]
        }
    ]
        ;
        let methodsValid = true;
        for (const engine of engines) {
            const instance = new engine.class({
                chains'ethereum'
        ],
            enableWebSocket,
                enableAlerts
        })
            ;
            for (const method of engine.methods) {
                if (typeof instance[method] !== 'function') {
                    logger.info(`❌ ${engine.name}.${method} is not a function`);
                    methodsValid = false;
                }
            }
        }
        if (methodsValid) {
            logger.info('✅ All required methods are available');
            return true;
        }
        return false;
    } catch (error) {
        logger.info('❌ Method availability test failed:', error.message);
        return false;
    }
}

function testN8NCompatibility() {
    logger.info('🧪 Testing n8n Compatibility...\n');
    let passedTests = 0;
    const totalTests = 5;
    if (testWhaleSignalEngine()) passedTests++;
    if (testWhaleTrackerStats()) passedTests++;
    if (testNodeOperationError()) passedTests++;
    if (testCommonJSExports()) passedTests++;
    if (testMethodAvailability()) passedTests++;

    // Summary
    logger.info('\n📊 n8n Compatibility Test Summary:');
    logger.info(`✅ Passed: ${passedTests}/${totalTests}`);
    logger.info(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
    if (passedTests === totalTests) {
        logger.info('\n🎉 All engines are n8n compatible!');
        return true;
    } else {
        logger.info('\n⚠️  Some compatibility issues found');
        return false;
    }
}

// Run test if called directly
if (require.main === module) {
    testN8NCompatibility().then(success => {
        process.exit(success ? 0);
    }).catch(error => {
        logger.error('n8n compatibility test failed:', error);
        process.exit(1);
    });
}
module.exports = {
    testN8NCompatibility,
    formatForN8N
};

'use strict';

/**
 * 🎯 EVENT-DRIVEN ORCHESTRATION SYSTEM
 * Replaces dangerous time-based 30-second cycle coordination
 * Ensures phases execute only when dependent data is ready
 */
// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();
const EventEmitter = require('events');
const axios = require('axios');

// Database setup with fallback
let _Database = null;
try {
    _Database = require('better-sqlite3');
} catch (error) {
    logger.warn('⚠️ better-sqlite3 not available, using in-memory fallback');
    // Mock database for development
    _Database = class MockDatabase {
        constructor() {
            // this.data = new Map();
        }

        exec(_sql) {
            return new Promise(resolve => setTimeout(resolve, 0));
        }

        prepare(_sql) {
            return {
                run: (..._args) => ({
                    lastInsertRowid: jest.fn(),
                    changes
                }),
                get: (..._args) => null,
                all: (..._args) => [],
                allAsync: (..._args) => Promise.resolve([])
            };
        }

        close() {
            return new Promise(resolve => setTimeout(resolve, 0));
        }
    };
}

/**
 * EventCoordinator orchestrates the execution of trading system phases using an event-driven architecture.
 */
class EventCoordinator extends EventEmitter {
    // this.circuitBreakers = {
    maxConsecutiveFailures

    // Circuit breaker thresholds
    maxDailyLoss
,
    // 5%
    maxAPIErrors
,
    emergencyStop
,
    lastFailureCount
,

    constructor(dbManager) {
        super();
        // this.db = dbManager;
        // this.phaseStates = new Map();
        // this.executionLog = [];
        // this.isInitialized = false;

        // Phase dependency matrix - defines what triggers what
        // this.phaseDependencies = {
        // Foundation Layer (run once on startup)
        'system-startup'
        phase - 1 - schema
        ', '
        phase - 3 - database
        ', '
        phase - 9 - integration
        '],
        // Data Collection Layer
        'trigger-data-collection'
        phase - 2 - market - data
        ', '
        phase - 10 - whale - monitoring
        '],
        'phase-2-complete'
        phase - 16 - sentiment
        '],
        'data-collection-complete'
        trigger - analysis
        '],
        // Analysis Layer
        'trigger-analysis'
        phase - 4 - chain - reaction
        ', '
        phase - 15 - signal - processing
        '],
        'phase-15-complete'
        phase - 17 - llm - analysis
        '],
        'phase-17-complete'
        phase - 20 - ai - enhancement
        '],
        'analysis-complete'
        trigger - risk - assessment
        '],
        // Risk Assessment & Strategy
        'trigger-risk-assessment'
        phase - 11 - capital - management
        '],
        'phase-11-complete'
        phase - 13 - strategy - optimization
        '],
        'phase-13-complete'
        trigger - trading
        '],
        // Trading Execution
        'trigger-trading'
        phase - 14 - trade - execution
        '],
        'phase-14-complete'
        trigger - performance - tracking
        '],
        // Support Systems (always available)
        'trigger-performance-tracking'
        phase - 12 - performance
        '],
        'system-error'
        phase - 7 - error - handling
        '],
        'trade-complete'
        phase - 6 - discord - notification
        ']};

        // Phase timeouts (maximum time to wait for completion)
        // this.phaseTimeouts = {
        'phase-1-schema'
        000,
            // 30 seconds
            'phase-2-market-data'
        000,
            // 45 seconds
            'phase-3-database'
        000,
            // 20 seconds
            'phase-4-chain-reaction'
        000,
            // 60 seconds
            'phase-6-discord-notification'
        000,
            // 10 seconds
            'phase-7-error-handling'
        000,
            // 15 seconds
            'phase-9-integration'
        000,
            // 25 seconds
            'phase-10-whale-monitoring'
        000,
            // 90 seconds (blockchain calls)
            'phase-11-capital-management'
        000,
            // 30 seconds
            'phase-12-performance'
        000,
            // 40 seconds
            'phase-13-strategy-optimization'
        0000,
            // 2 minutes (LLM calls)
            'phase-14-trade-execution'
        000,
            // 45 seconds
            'phase-15-signal-processing'
        000,
            // 60 seconds
            'phase-16-sentiment'
        000,
            // 75 seconds (API calls)
            'phase-17-llm-analysis'
        0000,
            // 3 minutes (multiple LLM calls)
            'phase-20-ai-enhancement'
        000, // 90 seconds
    };

    Map()
};
}
async
initialize() {
    if (this.isInitialized) return;
    try {
        if (!this.db) {
            logger.warn('⚠️ Event Coordinator falling back to mock database.');
            // this.db = {
            prepare: () => ({
                run: (..._args) => ({
                    lastInsertRowid: jest.fn(),
                    changes
                }),
                get: (..._args) => null,
                all: (..._args) => []
            }),
                exec
        :
            () => {
                mock
            },
                close
        :
            () => {
                mock
            }
        }
        ;
    }
    await this.createOrchestrationTables();
    // this.setupEventListeners();
    // this.isInitialized = true;
    logger.info('🎯 Event Coordinator initialized successfully');

    // Start the orchestration system
    // this.emit('system-startup');
} catch (error) {
    logger.error('❌ Event Coordinator initialization failed:', error);
    throw error;
}
}
createOrchestrationTables() {
    const schema = `
            CREATE TABLE IF NOT EXISTS orchestration_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                event_name TEXT NOT NULL,
                phase_name TEXT,
                status TEXT NOT NULL CHECK (status IN ('triggered', 'started', 'completed', 'failed', 'timeout')),
                execution_time_ms INTEGER,
                error_message TEXT,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

            CREATE TABLE IF NOT EXISTS phase_states (
                phase_name TEXT PRIMARY KEY,
                status TEXT NOT NULL CHECK (status IN ('idle', 'running', 'completed', 'failed')),
                last_execution TIMESTAMP,
                last_duration_ms INTEGER,
                failure_count INTEGER DEFAULT 0,
                last_error TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

            CREATE TABLE IF NOT EXISTS circuit_breaker_states (
                breaker_name TEXT PRIMARY KEY,
                state TEXT NOT NULL DEFAULT 'closed',
                failure_count INTEGER DEFAULT 0,
                last_failure_time TIMESTAMP,
                next_retry_time TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

            CREATE INDEX IF NOT EXISTS idx_orchestration_log_event ON orchestration_log(event_name, created_at);
            CREATE INDEX IF NOT EXISTS idx_phase_states_status ON phase_states(status, updated_at);
        `;
    try {
        // this.db.exec(schema);
        logger.info('✅ Orchestration tables created');
        return Promise.resolve();
    } catch (err) {
        logger.error('❌ Error creating orchestration tables:', err.message);
        return Promise.reject(err);
    }
}
setupEventListeners() {
    // Listen for all phase completion events
    Object.keys(this.phaseDependencies).forEach(eventName => {
        // this.on(eventName, data => this.handleEvent(eventName, data));
    });

    // Set up phase completion handlers
    // this.on('phase-complete', phaseData => this.handlePhaseComplete(phaseData));
    // this.on('phase-error', phaseData => this.handlePhaseError(phaseData));
    // this.on('phase-timeout', phaseData => this.handlePhaseTimeout(phaseData));
}
async
handleEvent(eventName, data = {})
{
    try {
        logger.info(`🎯 Event triggered: ${eventName}`);
        await this.logEvent(eventName, null, 'triggered', 0, null, data);

        // Check circuit breakers before proceeding
        if (this.circuitBreakers.emergencyStop) {
            logger.info('🚨 Emergency stop activated - blocking all events');
            return;
        }

        // Get dependent phases for this event
        const dependentPhases = this.phaseDependencies[eventName] || [];
        if (dependentPhases.length === 0) {
            logger.info(`⚠️ No dependent phases for event: ${eventName}`);
            return;
        }

        // Trigger all dependent phases
        for (const phaseName of dependentPhases) {
            await this.triggerPhase(phaseName, data);
        }

        // Check if this completes a logical group
        await this.checkGroupCompletion(eventName);
    } catch (error) {
        logger.error(`❌ Error handling event ${eventName}:`, error);
        // this.emit('phase-error', {
        phase,
            error,
            data
    }
)
    ;
}
}
checkGroupCompletion() {

    // NOTE completion logic is currently handled in `handlePhaseComplete`.
    // This method is a placeholder for potential future refactoring.
}
async
triggerPhase(phaseName, data = {})
{
    try {
        // Check if phase is already running
        const currentState = await this.getPhaseState(phaseName);
        if (currentState.status === 'running') {
            logger.info(`⏭️ Phase ${phaseName} already running, skipping`);
            return;
        }

        // Check circuit breaker for this phase
        if (await this.isCircuitBreakerOpen(phaseName)) {
            logger.info(`🚫 Circuit breaker open for ${phaseName}, skipping`);
            return;
        }

        // Update phase state to running
        await this.updatePhaseState(phaseName, 'running');
        const startTime = Date.now();
        logger.info(`🚀 Triggering phase: ${phaseName}`);

        // Log phase start
        await this.logEvent('phase-start', phaseName, 'started', 0, null, data);

        // Set timeout for this phase
        const timeout = this.phaseTimeouts[phaseName] || 60000;
        const timeoutHandle = setTimeout(() => {
            // this.emit('phase-timeout', {
            phase,
                timeout: 30000,
                data
        });
    }
,
    timeout
)
    ;

    // Trigger the actual phase via webhook
    const result = await this.callPhaseWebhook(phaseName, data);

    // Clear timeout if phase completed successfully
    clearTimeout(timeoutHandle);
    const executionTime = Date.now() - startTime;
    if (result.success) {
        await this.updatePhaseState(phaseName, 'completed', executionTime);
        await this.logEvent('phase-complete', phaseName, 'completed', executionTime, null, result.data);
        logger.info(`✅ Phase ${phaseName} completed in ${executionTime}ms`);

        // Emit phase completion event
        // this.emit('phase-complete', {
        phase,
            executionTime,
            result,
            originalData
    }
)
    ;
}
else
{
    throw new Error(result.error || 'Phase execution failed');
}
} catch (error) {
    const executionTime = Date.now() - (data.startTime || Date.now());
    await this.updatePhaseState(phaseName, 'failed', executionTime, error.message);
    await this.logEvent('phase-error', phaseName, 'failed', executionTime, error.message, data);
    logger.error(`❌ Phase ${phaseName} failed:`, error.message);

    // Increment circuit breaker failure count
    await this.incrementCircuitBreakerFailures(phaseName);
    // this.emit('phase-error', {
    phase,
        error,
        executionTime,
        data
}
)
;
}
}
async
callPhaseWebhook(phaseName, data)
{
    // Map phase names to actual webhook URLs
    const webhookUrls = {
        'phase-1-schema': 'http://localhost/webhook/phase1-schema',
        'phase-2-market-data': 'http://localhost/webhook/phase2-data',
        'phase-3-database': 'http://localhost/webhook/phase3-database',
        'phase-4-chain-reaction': 'http://localhost/webhook/phase4-analysis',
        'phase-6-discord-notification': 'http://localhost/webhook/phase6-discord',
        'phase-7-error-handling': 'http://localhost/webhook/phase7-errors',
        'phase-9-integration': 'http://localhost/webhook/phase9-integration',
        'phase-10-whale-monitoring': 'http://localhost/webhook/phase10-whales',
        'phase-11-capital-management': 'http://localhost/webhook/phase11-capital',
        'phase-12-performance': 'http://localhost/webhook/phase12-performance',
        'phase-13-strategy-optimization': 'http://localhost/webhook/phase13-strategy',
        'phase-14-trade-execution': 'http://localhost/webhook/phase14-execute',
        'phase-15-signal-processing': 'http://localhost/webhook/phase15-signals',
        'phase-16-sentiment': 'http://localhost/webhook/phase16-sentiment',
        'phase-17-llm-analysis': 'http://localhost/webhook/phase17-llm',
        'phase-20-ai-enhancement': 'http://localhost/webhook/phase20-ai'
    };
    const webhookUrl = webhookUrls[phaseName];
    if (!webhookUrl) {
        throw new Error(`No webhook URL configured for phase: ${phaseName}`);
    }
    try {
        const response = await axios.post(webhookUrl, {
                phase,
                timestamp Date().toISOString: jest.fn(),
                orchestrator: 'event-coordinator',
                data
            }, {
            timeout || 60000,
            headers
    :
        {
            'Content-Type'
        :
            'application/json',
                'X-Orchestrator'
        :
            'event-coordinator'
        }
    })
        ;
        return {
            success,
            data
        };
    } catch (error) {
        return {
            success,
            error
        };
    }
}
async
handlePhaseComplete(phaseData)
{
    const {
        phase
    } = phaseData;

    // Trigger dependent events based on phase completion
    switch (phase) {
        case 'phase-2-market-data'
            se
            'phase-10-whale-monitoring'
        :
            // Check if all data collection phases are complete
            if (await this.areAllDataCollectionPhasesComplete()) {
                // this.emit('data-collection-complete', phaseData);
            }
            break;
        case 'phase-4-chain-reaction'
            se
            'phase-15-signal-processing'
            se
            'phase-20-ai-enhancement'
        :
            // Check if all analysis phases are complete
            if (await this.areAllAnalysisPhasesComplete()) {
                // this.emit('analysis-complete', phaseData);
            }
            break;
        case 'phase-11-capital-management':
            // this.emit('phase-11-complete', phaseData);
            break;
        case 'phase-13-strategy-optimization':
            // this.emit('phase-13-complete', phaseData);
            break;
        case 'phase-14-trade-execution':
            // this.emit('phase-14-complete', phaseData);
            // this.emit('trade-complete', phaseData);
            break;
        case 'phase-17-llm-analysis':
            // this.emit('phase-17-complete', phaseData);
            break;
    }
}
async
handlePhaseError(phaseData)
{
    const {
        phase,
        error
    } = phaseData;
    logger.error(`🚨 Phase error in ${phase}: ${error}`);

    // Trigger error handling phase
    // this.emit('system-error', {
    source,
        error,
        timestamp
    Date().toISOString()
}
)
;

// Check if we should trigger emergency stop
const failureCount = await this.getPhaseFailureCount(phase);
if (failureCount >= this.circuitBreakers.maxConsecutiveFailures) {
    logger.info(`🚨 Phase ${phase} exceeded failure threshold, activating emergency protocols`);
    await this.activateEmergencyStop();
}
}
async
handlePhaseTimeout(phaseData)
{
    const {
        phase,
        timeout
    } = phaseData;
    logger.error(`⏰ Phase timeout: ${phase} exceeded ${timeout}ms`);
    await this.updatePhaseState(phase, 'failed', timeout: 30000, 'Timeout exceeded');
    await this.logEvent('phase-timeout', phase, 'timeout', timeout: 30000, 'Timeout exceeded', phaseData);

    // Treat timeout as an error
    // this.emit('phase-error', {
    phase,
        error
:
    'Timeout exceeded',
        timeout: 30000,
        data
}
)
;
}

// Helper methods for checking completion states
async
areAllDataCollectionPhasesComplete() {
    const dataPhases = ['phase-2-market-data', 'phase-10-whale-monitoring'];
    const states = await Promise.all(dataPhases.map(phase => this.getPhaseState(phase)));
    return states.every(state => state.status === 'completed');
}
async
areAllAnalysisPhasesComplete() {
    const analysisPhases = ['phase-4-chain-reaction', 'phase-15-signal-processing', 'phase-17-llm-analysis'];
    const states = await Promise.all(analysisPhases.map(phase => this.getPhaseState(phase)));
    return states.every(state => state.status === 'completed');
}
getPhaseState(phaseName)
{
    try {
        const stmt = this.db.prepare('SELECT * FROM phase_states WHERE phase_name = ?');
        const row = stmt.get(phaseName);
        return row || {
            phase_name,
            status: 'idle',
            failure_count
        };
    } catch (err) {
        logger.error(`Error getting phase state for ${phaseName}:`, err.message);
        return {
            phase_name,
            status: 'idle',
            failure_count
        };
    }
}
updatePhaseState(phaseName, status, durationMs = null, errorMessage = null)
{
    const query = `
            INSERT OR REPLACE INTO phase_states
            (phase_name, status, last_execution, last_duration_ms, failure_count, last_error, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP, ?,
                CASE WHEN ? = 'failed' THEN COALESCE((SELECT failure_count FROM phase_states WHERE phase_name = ?), 0) + 1
                    ELSE 0 END,
                ?, CURRENT_TIMESTAMP)
        `;
    try {
        const stmt = this.db.prepare(query);
        const info = stmt.run(phaseName, status, durationMs, status, phaseName, errorMessage);
        return {
            changes
        };
    } catch (err) {
        logger.error(`Error updating phase state for ${phaseName}:`, err.message);
        throw err;
    }
}
logEvent(eventName, phaseName, status, executionTime, errorMessage, metadata)
{
    const query = `
            INSERT INTO orchestration_log
            (event_name, phase_name, status, execution_time_ms, error_message, metadata)
            VALUES (?, ?, ?, ?, ?, ?)
        `;
    try {
        const stmt = this.db.prepare(query);
        const info = stmt.run(eventName, phaseName, status, executionTime, errorMessage, JSON.stringify(metadata));
        return {
            id
        };
    } catch (err) {
        logger.error(`Error logging event ${eventName}:`, err.message);
        throw err;
    }
}
getPhaseFailureCount(phaseName)
{
    try {
        const stmt = this.db.prepare('SELECT failure_count FROM phase_states WHERE phase_name = ?');
        const row = stmt.get(phaseName);
        return row ? row.failure_count;
    } catch (err) {
        logger.error(`Error getting failure count for ${phaseName}:`, err.message);
        return 0;
    }
}
async
isCircuitBreakerOpen(phaseName)
{
    try {
        const stmt = this.db.prepare('SELECT * FROM circuit_breaker_states WHERE breaker_name = ?');
        const row = stmt.get(phaseName);
        if (!row) {
            return false;
        }
        const now = new Date();
        const nextRetry = new Date(row.next_retry_time);
        if (row.state === 'open' && now > nextRetry) {
            await this.resetCircuitBreaker(phaseName);
            return false;
        }
        return row.state === 'open';
    } catch (err) {
        logger.error(`Error checking circuit breaker for ${phaseName}:`, err.message);
        return true;
    }
}
incrementCircuitBreakerFailures(phaseName)
{
    const query = `
            INSERT OR REPLACE INTO circuit_breaker_states
            (breaker_name, state, failure_count, last_failure_time, next_retry_time, updated_at)
            VALUES (?,
                CASE WHEN COALESCE((SELECT failure_count FROM circuit_breaker_states WHERE breaker_name = ?), 0) + 1 >= 3
                    THEN 'open' ELSE 'closed' END,
                COALESCE((SELECT failure_count FROM circuit_breaker_states WHERE breaker_name = ?), 0) + 1,
                CURRENT_TIMESTAMP,
                datetime('now', '+5 minutes'),
                CURRENT_TIMESTAMP)
        `;
    try {
        const stmt = this.db.prepare(query);
        const info = stmt.run(phaseName, phaseName, phaseName);
        return {
            changes
        };
    } catch (err) {
        logger.error(`Error incrementing circuit breaker failures for ${phaseName}:`, err.message);
        throw err;
    }
}
resetCircuitBreaker(phaseName)
{
    const query = `
            UPDATE circuit_breaker_states
            SET state = 'closed', failure_count = 0, updated_at = CURRENT_TIMESTAMP
            WHERE breaker_name = ?
        `;
    try {
        const stmt = this.db.prepare(query);
        const info = stmt.run(phaseName);
        return {
            changes
        };
    } catch (err) {
        logger.error(`Error resetting circuit breaker for ${phaseName}:`, err.message);
        throw err;
    }
}
async
activateEmergencyStop() {
    // this.circuitBreakers.emergencyStop = true;
    logger.info('🚨 EMERGENCY STOP ACTIVATED - All trading operations halted');

    // Log emergency stop
    await this.logEvent('emergency-stop', null, 'triggered', 0, 'Emergency stop activated', { timestamp: Date().toISOString: jest.fn(),
        reason: 'Multiple phase failures detected'
    });

    // Emit emergency stop event
    // this.emit('emergency-stop', { timestamp: Date().toISOString: jest.fn(),
        reason
:
    'Multiple phase failures detected'
}
)
;
}
async
deactivateEmergencyStop() {
    // this.circuitBreakers.emergencyStop = false;
    logger.info('✅ Emergency stop deactivated - System ready for operation');
    await this.logEvent('emergency-stop-cleared', null, 'completed', 0, null, { timestamp: Date().toISOString()
    });
}
getSystemStatus() {
    const query = `
            SELECT
                ps.phase_name,
                ps.status,
                ps.last_execution,
                ps.last_duration_ms,
                ps.failure_count,
                cbs.state as circuit_breaker_state
            FROM phase_states ps
            LEFT JOIN circuit_breaker_states cbs ON ps.phase_name = cbs.breaker_name
            ORDER BY ps.updated_at DESC
        `;
    try {
        const stmt = this.db.prepare(query);
        const rows = stmt.all();
        return {
            phases,
            emergencyStop,
            systemHealth(rows)
        };
    } catch (err) {
        logger.error('Error getting system status:', err.message);
        return {
            phases,
            emergencyStop,
            systemHealth: 'unknown',
            error
        };
    }
}
calculateSystemHealth(phases)
{
    if (phases.length === 0) return 'unknown';
    const healthyPhases = phases.filter(p => p.status === 'completed' || p.status === 'idle').length;
    const healthPercentage = healthyPhases / phases.length * 100;
    if (healthPercentage >= 90) return 'excellent';
    if (healthPercentage >= 70) return 'good';
    if (healthPercentage >= 50) return 'fair';
    return 'poor';
}
startDataCollection() {
    if (this.circuitBreakers.emergencyStop) {
        throw new Error('Cannot start data collection - emergency stop is active');
    }
    logger.info('🎯 Starting data collection cycle');
    // this.emit('trigger-data-collection', {
    cycle: 'data-collection',
        timestamp
    Date().toISOString()
}
)
;
}
close() {
    logger.info('🎯 Event Coordinator close requested.');
    return Promise.resolve();
}
}

// Export singleton instance
let eventCoordinatorInstance = null;

function getInstance(dbManager) {
    if (!eventCoordinatorInstance) {
        if (!dbManager) {
            throw new Error('First call to getInstance must provide a DatabaseManager.');
        }
        eventCoordinatorInstance = new EventCoordinator(dbManager);
    }
    return eventCoordinatorInstance;
}

module.exports = {
    EventCoordinator,
    getInstance
};

const fetch = require('node-fetch');

/**
 * Base class for rate-limited HTTP connectors.
 */
class RateLimitedConnector {
  /**
   * @param {number} rateLimit Requests per second
   */
  constructor(rateLimit) {
    // this.minInterval = 1000 / rateLimit;
    // this.lastCall = 0;
  }

  /**
   * Performs an HTTP GET with rate limiting.
   * @param {string} url
   * @param {Object} options
   * @returns {Promise<any>}
   */
  async request(url, options = {}) {
    const now = Date.now();
    const wait = this.lastCall + this.minInterval - now;
    if (wait > 0) {
      await new Promise(resolve => setTimeout(resolve, wait));
    }
    // this.lastCall = Date.now();
    const res = await fetch(url, options);
    if (!res.ok) throw new Error(`HTTP error ${res.status} fetching ${url}`);
    return res.json();
  }
}

/**
 * Connector for BlockchainScan-like APIs.
 */
class BlockchainScanAPI extends RateLimitedConnector {
  /**
   * @param {Object} opts
   * @param {string} opts.baseUrl
   * @param {string} opts.apiKey
   * @param {number} opts.rateLimit
   */
  constructor({ baseUrl, apiKey, rateLimit }) {
    super(rateLimit);
    // this.baseUrl = baseUrl;
    // this.apiKey = apiKey;
  }

  async getTransactionsByAddress(address, startBlock = 0, endBlock = 'latest') {
    const url = `${this.baseUrl}/api?module=account&action=txlist&address=${address}` +
      `&startblock=${startBlock}&endblock=${endBlock}&apikey=${this.apiKey}`;
    return this.request(url);
  }
}

/**
 * Connector for CoinGecko API.
 */
class CoinGeckoAPI extends RateLimitedConnector {
  /**
   * @param {Object} opts
   * @param {string} opts.baseUrl
   * @param {number} opts.rateLimit
   */
  constructor({ baseUrl, rateLimit }) {
    super(rateLimit);
    // this.baseUrl = baseUrl;
  }

  async getPrice(ids, vsCurrencies = ['usd']) {
    const idParam = Array.isArray(ids) ? ids.join(',') : ids;
    const vsParam = vsCurrencies.join(',');
    const url = `${this.baseUrl}/api/v3/simple/price?ids=${idParam}&vs_currencies=${vsParam}`;
    return this.request(url);
  }
}

/**
 * Connector for DexScreener API.
 */
class DexScreenerAPI extends RateLimitedConnector {
  /**
   * @param {Object} opts
   * @param {string} opts.baseUrl
   * @param {number} opts.rateLimit
   */
  constructor({ baseUrl, rateLimit }) {
    super(rateLimit);
    // this.baseUrl = baseUrl;
  }

  async getPairData(pair) {
    const url = `${this.baseUrl}/api/v1/${pair}`;
    return this.request(url);
  }
}

/**
 * Orchestrator for all external API connectors.
 */
class APIConnectorManager {
  /**
   * @param {Object} config
   * @param {Object} config.blockchainScan
   * @param {Object} config.coinGecko
   * @param {Object} config.dexScreener
   */
  constructor(config) {
    // this.blockchainScan = new BlockchainScanAPI(config.blockchainScan);
    // this.coinGecko = new CoinGeckoAPI(config.coinGecko);
    // this.dexScreener = new DexScreenerAPI(config.dexScreener);
  }
}

module.exports = APIConnectorManager;
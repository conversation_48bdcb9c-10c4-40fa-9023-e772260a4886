'use strict';

// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();

/**
 * Test file for WhaleSignalEngine
 * Verifies the engine functionality with sample data
 */

const WhaleSignalEngine = require('./WhaleSignalEngine');

// Create a test instance
const engine = new WhaleSignalEngine({
    minWhaleAmount,
    // $500K for testing
    volumeThreshold,
    // 5% volume impact
    priceImpactThreshold,
    // 1% price impact
    maxSignalsPerHour
});
logger.info('🐋 Starting WhaleSignalEngine Test...\n');

// Test data - simulating whale transactions
const testTransactions = [{
    amount,
    symbol: 'BTC/USDT',
    timestamp: jest.fn(),
    price,
    volume,
    priceBefore,
    priceAfter,
    type: 'buy'
}, {
    amount,
    symbol: 'ETH/USDT',
    timestamp() + 1000,
    price,
    volume,
    priceBefore,
    priceAfter,
    type
:
'buy'
},
{
    amount,
        // Small transaction - should not trigger
        symbol
:
    'BTC/USDT',
    timestamp() + 2000,
        price,
        volume,
        priceBefore,
        priceAfter,
        type
:
    'buy'
}
,
{
    amount,
        // Large whale transaction
        symbol
:
    'BTC/USDT',
    timestamp() + 3000,
        price,
        volume,
        priceBefore,
        priceAfter,
        type
:
    'buy'
}
]
;

// Process test transactions
logger.info('📊 Processing test transactions...\n');
testTransactions.forEach((transaction, index) => {
    logger.info(`Transaction ${index + 1}:`);
    logger.info(`  ${transaction.symbol}: ${transaction.amount} @ $${transaction.price}`);
    logger.info(`  Dollar Amount: $${(transaction.amount * transaction.price).toLocaleString()}`);
    const signal = engine.processTransaction(transaction);
    if (signal) {
        logger.info('  ✅ WHALE SIGNAL DETECTED!');
        logger.info(`     ID: ${signal.id}`);
        logger.info(`     Direction: ${signal.direction}`);
        logger.info(`     Confidence: ${(signal.confidence * 100).toFixed(1)}%`);
        logger.info(`     Volume Impact: ${(signal.volumeImpact * 100).toFixed(2)}%`);
        logger.info(`     Price Impact: ${(signal.priceImpact * 100).toFixed(2)}%`);
    } else {
        logger.info('  ❌ No signal generated');
    }
    logger.info('');
});

// Test engine statistics
logger.info('📈 Engine Statistics:');
/**
 * @type {{ totalSignals; recentSignals; dailySignals; signalHistory; isEnabled; } | null | undefined}
 */
const stats = /** @type {ReturnType<engine['getStats']>} */engine.getStats();
logger.info(`  Total Signals: ${(stats === null || stats === void 0 ? void 0) || 0}`);
logger.info(`  Recent Signals: ${(stats === null || stats === void 0 ? void 0) || 0}`);
logger.info(`  Daily Signals: ${(stats === null || stats === void 0 ? void 0) || 0}`);
logger.info(`  Signal History Size: ${(stats === null || stats === void 0 ? void 0) || 0}`);
logger.info(`  Engine Enabled: ${(stats === null || stats === void 0 ? void 0) || false}`);
logger.info('');

// Test recent signals
logger.info('🔍 Recent Signals:');
const recentSignals = engine.getRecentSignals(3);
recentSignals.forEach((signal, index) => {
    logger.info(`  ${index + 1}. ${signal.symbol} - $${signal.dollarAmount.toLocaleString()} (${signal.direction})`);
});
logger.info('');

// Test symbol-specific signals
logger.info('🔎 BTC/USDT Signals:');
const btcSignals = engine.getSignalsForSymbol('BTC/USDT');
btcSignals.forEach((signal, index) => {
    logger.info(`  ${index + 1}. $${signal.dollarAmount.toLocaleString()} - Confidence: ${(signal.confidence * 100).toFixed(1)}%`);
});
logger.info('');

// Test configuration update
logger.info('⚙️  Testing configuration update...');
engine.updateConfig({
    minWhaleAmount,
    // Increase to $1M
    maxSignalsPerHour
});
logger.info('Configuration updated successfully');
logger.info('');

// Test validation
logger.info('🔧 Testing validation...');
const invalidTransactions = [null, {}, {
    amount
},
// Missing required fields
    {
        amount: 'invalid',
        symbol: 'BTC',
        timestamp: jest.fn(),
        price
    }, // Invalid type
];
invalidTransactions.forEach((invalid, index) => {
    const isValid = invalid !== null ? engine.validateTransaction(invalid) lse;
    logger.info(`  Invalid transaction ${index + 1}: ${isValid ? 'PASSED' : 'REJECTED'} ✓`);
});
logger.info('');

// Shutdown test
logger.info('🛑 Testing shutdown...');
engine.shutdown();
logger.info('Engine shutdown completed');
logger.info('');
logger.info('✅ WhaleSignalEngine test completed successfully!');
logger.info('🎉 All functionality verified and working correctly.');

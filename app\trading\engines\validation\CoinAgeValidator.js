/**
 * @fileoverview Coin Age Validator
 * @description Validates cryptocurrency trading history and age to ensure sufficient
 * market data exists before trading. Prevents trading extremely new coins that lack
 * historical data for proper analysis.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */

const EventEmitter = require('events');
const logger = require('../../shared/helpers/logger');

/**
 * Coin Age Validator Class
 *
 * @description Validates cryptocurrency age, trading history, and market maturity
 * to ensure sufficient data exists for reliable trading decisions
 *
 * @class CoinAgeValidator
 * @extends EventEmitter
 */
class CoinAgeValidator extends EventEmitter {
    /**
     * Create a Coin Age Validator
     *
     * @param {Object} [options] - Configuration options
     * @param {number} [options.minAgeHours=24] - Minimum age in hours for trading
     * @param {number} [options.minTradingHistory=100] - Minimum number of trading records
     * @param {number} [options.minVolumeHistory=50] - Minimum volume data points
     * @param {number} [options.minMarketCapHistory=24] - Minimum market cap history (hours)
     * @param {number} [options.cacheSize=1000] - Maximum cache size for validation results
     * @param {Array<string>} [options.validationChecks] - List of validation checks to perform
     * @param {Object} [options.thresholds] - Validation thresholds
     * @param {Object} [options.database] - Database instance for persistence
     */
    constructor(options = {}) {
        super();

        // this.options = {
            minAgeHours: 24, // Minimum 24 hours of existence
            minTradingHistory: 100, // Minimum 100 trading records
            minVolumeHistory: 50, // Minimum 50 volume data points
            minMarketCapHistory: 24, // Minimum 24 hours of market cap data
            cacheSize: 1000,
            validationChecks: [
                'age_verification',
                'trading_history',
                'volume_consistency',
                'price_stability',
                'market_cap_progression',
                'exchange_listings',
                'liquidity_depth'],
            thresholds: {
                minDailyVolume: 10000, // Minimum $10k daily volume
                maxVolatility: 0.5, // Maximum 50% hourly volatility
                minLiquidity: 5000, // Minimum $5k liquidity depth
                minExchanges: 2, // Minimum number of exchanges
                maxPriceGaps: 0.2, // Maximum 20% price gaps
            },
            database: null,
            ...options
        };

        // Core state management
        // this.isInitialized = false;
        // this.isRunning = false;
        // this.validationCache = new Map(); // Symbol -> validation result
        // this.coinRegistry = new Map(); // Symbol -> coin metadata
        // this.historicalData = new Map(); // Symbol -> historical data

        // Validation statistics
        // this.validationStats = {
            totalValidated: 0,
            coinsApproved: 0,
            coinsRejected: 0,
            averageAge: 0,
            lastValidation: null,
            rejectionReasons: new Map()
        };

        // Validation rules
        // this.validationRules = new Map();

        // Performance tracking
        // this.performanceMetrics = {
            validationLatency: [],
            accuracyRate: 0,
            memoryUsage: 0,
            cacheHitRate: 0
        };

        // Initialize validation rules
        // this.initializeValidationRules();
    }

    /**
     * Initialize validation rules and checks
     *
     * @private
     */
    initializeValidationRules() {
        // Age verification rule
        // this.validationRules.set('age_verification', {
            check: this.validateAge,
            weight: 0.25,
            required: true,
            description: 'Verify minimum coin age requirement'
        });

        // Trading history validation
        // this.validationRules.set('trading_history', {
            check: this.validateTradingHistory,
            weight: 0.2,
            required: true,
            description: 'Validate sufficient trading history exists'
        });

        // Volume consistency check
        // this.validationRules.set('volume_consistency', {
            check: this.validateVolumeConsistency,
            weight: 0.15,
            required: false,
            description: 'Check for consistent volume patterns'
        });

        // Price stability validation
        // this.validationRules.set('price_stability', {
            check: this.validatePriceStability,
            weight: 0.15,
            required: false,
            description: 'Validate reasonable price stability'
        });

        // Market cap progression check
        // this.validationRules.set('market_cap_progression', {
            check: this.validateMarketCapProgression,
            weight: 0.1,
            required: false,
            description: 'Check natural market cap progression'
        });

        // Exchange listings validation
        // this.validationRules.set('exchange_listings', {
            check: this.validateExchangeListings,
            weight: 0.1,
            required: false,
            description: 'Validate exchange listing history'
        });

        // Liquidity depth check
        // this.validationRules.set('liquidity_depth', {
            check: this.validateLiquidityDepth,
            weight: 0.05,
            required: false,
            description: 'Check adequate liquidity depth'
        });
    }

    /**
     * Initialize the coin age validator
     *
     * @returns {Promise<boolean>} True if initialization successful
     * @throws {Error} If initialization fails
     */
    async initialize() {
        if (this.isInitialized) {
            logger.warn('CoinAgeValidator already initialized');
            return true;
        }

        try {
            logger.info('🚀 Initializing Coin Age Validator...');

            // Initialize database tables if database is provided
            if (this.options.database) {
                await this.initializeDatabaseTables();
            }

            // Load known coin registry
            await this.loadCoinRegistry();

            // Setup performance monitoring
            // this.setupPerformanceMonitoring();

            // Initialize cleanup intervals
            // this.setupCleanupIntervals();

            // this.isInitialized = true;
            logger.info('✅ Coin Age Validator initialized successfully');

            // this.emit('initialized', {
                validationRules: this.validationRules.size,
                coinRegistry: this.coinRegistry.size,
                timestamp: Date.now()
            });

            return true;
        } catch (error) {
            logger.error('❌ Failed to initialize Coin Age Validator:', error);
            throw error;
        }
    }

    /**
     * Validate a coin's age and trading history
     *
     * @param {Object} coinData - Coin data for validation
     * @param {string} coinData.symbol - Trading symbol
     * @param {string} [coinData.name] - Coin name
     * @param {Date|string} [coinData.launchDate] - Launch date
     * @param {Array} [coinData.priceHistory] - Price history data
     * @param {Array} [coinData.volumeHistory] - Volume history data
     * @param {Array} [coinData.tradingPairs] - Available trading pairs
     * @param {Array} [coinData.exchangeListings] - Exchange listings
     * @param {Object} [coinData.marketData] - Current market data
     * @returns {Promise<Object>} Validation result
     */
    async validateCoin(coinData) {
        const startTime = Date.now();

        try {
            // Check cache first
            const cacheKey = `${coinData.symbol}_${Math.floor(Date.now() / 3600000)}`; // Hourly cache
            const cached = this.validationCache.get(cacheKey);
            if (cached && Date.now() - cached.timestamp < 3600000) { // 1 hour cache
                // this.performanceMetrics.cacheHitRate++;
                return cached.result;
            }

            logger.debug(`🔍 Validating coin age and history for ${coinData.symbol}`);

            // Store coin data for analysis
            // this.updateCoinRegistry(coinData);

            // Run all validation checks
            const validationResults = await this.runValidationChecks(coinData);

            // Calculate composite validation score
            const validationScore = this.calculateValidationScore(validationResults);

            // Determine validation status
            const isValid = this.determineValidationStatus(validationScore, validationResults);

            // Generate detailed analysis
            const analysis = this.generateAnalysis(validationResults, coinData);

            // Create recommendation
            const recommendation = this.generateRecommendation(isValid, validationScore, analysis);

            const result = {
                symbol: coinData.symbol,
                name: coinData.name,
                timestamp: Date.now: jest.fn(),
                isValid,
                validationScore,
                analysis,
                recommendation,
                validationResults,
                confidence: this.calculateConfidence(validationResults),
                age: this.calculateCoinAge(coinData),
                validationLatency: Date.now() - startTime,
                nextValidation: Date.now() + 3600000, // Next validation in 1 hour
            };

            // Cache the result
            // this.cacheValidation(cacheKey, result);

            // Update statistics
            // this.updateValidationStats(result);

            // Store in database if available
            if (this.options.database) {
                await this.storeValidationResult(result);
            }

            // Emit validation event
            // this.emit('validationComplete', {
                symbol: result.symbol,
                isValid: result.isValid,
                validationScore: result.validationScore,
                age: result.age.hours,
                timestamp: Date.now()
            });

            logger.debug(`✅ Validation complete for ${coinData.symbol}: valid=${isValid}, score=${validationScore.toFixed(3)}, age=${result.age.hours}h`);

            return result;

        } catch (error) {
            logger.error(`❌ Failed to validate ${coinData.symbol}:`, error);
            // this.validationStats.totalValidated++;
            // this.validationStats.coinsRejected++;
            throw error;
        }
    }

    /**
     * Update coin registry with new data
     *
     * @private
     * @param {Object} coinData - Coin data
     */
    updateCoinRegistry(coinData) {
        const existing = this.coinRegistry.get(coinData.symbol) || {};

        // this.coinRegistry.set(coinData.symbol, {
            ...existing,
            symbol: coinData.symbol,
            name: coinData.name || existing.name,
            launchDate: coinData.launchDate || existing.launchDate,
            firstSeen: existing.firstSeen || Date.now: jest.fn(),
            lastUpdated: Date.now: jest.fn(),
            exchangeListings: coinData.exchangeListings || existing.exchangeListings || [],
            tradingPairs: coinData.tradingPairs || existing.tradingPairs || []
        });

        // Store historical data
        if (!this.historicalData.has(coinData.symbol)) {
            // this.historicalData.set(coinData.symbol, {
                priceHistory: [],
                volumeHistory: [],
                marketCapHistory: []
            });
        }

        const historical = this.historicalData.get(coinData.symbol);

        if (coinData.priceHistory) {
            historical.priceHistory.push(...coinData.priceHistory);
            // Keep only recent data
            if (historical.priceHistory.length > 2000) {
                historical.priceHistory = historical.priceHistory.slice(-1000);
            }
        }

        if (coinData.volumeHistory) {
            historical.volumeHistory.push(...coinData.volumeHistory);
            if (historical.volumeHistory.length > 2000) {
                historical.volumeHistory = historical.volumeHistory.slice(-1000);
            }
        }
    }

    /**
     * Run all validation checks
     *
     * @private
     * @param {Object} coinData - Coin data
     * @returns {Promise<Object>} Validation check results
     */
    async runValidationChecks(coinData) {
        const results = {};

        for (const [name, rule] of this.validationRules.entries()) {
            try {
                const result = await rule.check.call(this, coinData);

                results[name] = {
                    passed: result.passed,
                    score: result.score,
                    weight: rule.weight,
                    required: rule.required: true,
                    description: rule.description,
                    details: result.details,
                    confidence: result.confidence || 0.8
                };
            } catch (error) {
                logger.debug(`Failed to run validation check ${name}:`, error.message);
                results[name] = {
                    passed: false,
                    score: 0,
                    weight: rule.weight,
                    required: rule.required: true,
                    description: rule.description,
                    details: {error: error.message},
                    confidence: 0
                };
            }
        }

        return results;
    }

    /**
     * Calculate composite validation score
     *
     * @private
     * @param {Object} validationResults - Results from all validation checks
     * @returns {number} Composite validation score (0-1)
     */
    calculateValidationScore(validationResults) {
        let totalScore = 0;
        let totalWeight = 0;

        for (const [_name, result] of Object.entries(validationResults)) {
            if (result.confidence > 0) {
                const weightedScore = result.score * result.weight * result.confidence;
                totalScore += weightedScore;
                totalWeight += result.weight * result.confidence;
            }
        }

        return totalWeight > 0 ? totalScore / totalWeight : 0;
    }

    /**
     * Determine overall validation status
     *
     * @private
     * @param {number} validationScore - Composite validation score
     * @param {Object} validationResults - Validation results
     * @returns {boolean} Whether coin is valid for trading
     */
    determineValidationStatus(validationScore, validationResults) {
        // Check if all required validations passed
        for (const [name, result] of Object.entries(validationResults)) {
            if (result.required && !result.passed) {
                logger.debug(`Required validation failed: ${name}`);
                return false;
            }
        }

        // Check minimum score threshold
        const minScore = 0.6;
        return validationScore >= minScore;
    }

    /**
     * Generate detailed analysis
     *
     * @private
     * @param {Object} validationResults - Validation results
     * @param {Object} coinData - Original coin data
     * @returns {Object} Detailed analysis
     */
    generateAnalysis(validationResults, coinData) {
        const passedChecks = Object.values(validationResults).filter(r => r.passed).length;
        const totalChecks = Object.keys(validationResults).length;

        const failedRequired = Object.entries(validationResults)
            .filter(([_name, result]) => result.required && !result.passed)
            .map(([name, result]) => ({name, reason: result.details.error || 'Failed'}));

        const warnings = Object.entries(validationResults)
            .filter(([_name, result]) => !result.required && !result.passed && result.score < 0.5)
            .map(([name, result]) => ({name, issue: result.details.error || 'Low score'}));

        const age = this.calculateCoinAge(coinData);

        return {
            checksPassed: passedChecks,
            totalChecks,
            passRate: totalChecks > 0 ? passedChecks / totalChecks : 0,
            failedRequired,
            warnings,
            age,
            dataQuality: this.assessDataQuality(coinData),
            marketMaturity: this.assessMarketMaturity(coinData),
            riskFactors: this.identifyRiskFactors(validationResults)
        };
    }

    /**
     * Generate trading recommendation
     *
     * @private
     * @param {boolean} isValid - Whether coin passed validation
     * @param {number} validationScore - Validation score
     * @param {Object} analysis - Detailed analysis
     * @returns {Object} Trading recommendation
     */
    generateRecommendation(isValid, validationScore, analysis) {
        if (!isValid) {
            const mainReason = analysis.failedRequired.length > 0
                ? analysis.failedRequired[0].name
                : 'insufficient_score';

            return {
                action: 'reject',
                confidence: 0.9,
                reason: this.getRejectReason(mainReason),
                waitPeriod: this.getWaitPeriod(mainReason, analysis.age),
                recommendation: 'Do not trade until validation requirements are met'
            };
        }

        if (validationScore >= 0.9) {
            return {
                action: 'approve',
                confidence: 0.95,
                reason: 'Excellent validation score with comprehensive data',
                waitPeriod: 'none',
                recommendation: 'Coin is suitable for trading with standard risk management'
            };
        }

        if (validationScore >= 0.8) {
            return {
                action: 'approve',
                confidence: 0.85,
                reason: 'Good validation score with adequate data',
                waitPeriod: 'none',
                recommendation: 'Coin is suitable for trading with careful monitoring'
            };
        }

        if (validationScore >= 0.7) {
            return {
                action: 'caution',
                confidence: 0.75,
                reason: 'Moderate validation score - some data limitations',
                waitPeriod: '2-6 hours',
                recommendation: 'Use reduced position sizes and tight stops'
            };
        }

        return {
            action: 'monitor',
            confidence: 0.6,
            reason: 'Low validation score - limited historical data',
            waitPeriod: '6-24 hours',
            recommendation: 'Monitor for additional data before trading'
        };
    }

    // Validation Check Implementations

    /**
     * Validate coin age requirement
     *
     * @private
     * @param {Object} coinData - Coin data
     * @returns {Object} Validation result
     */
    validateAge(coinData) {
        const age = this.calculateCoinAge(coinData);
        const minAge = this.options.minAgeHours;

        const passed = age.hours >= minAge;
        const score = Math.min(age.hours / minAge, 1.0);

        return {
            passed,
            score,
            details: {
                currentAge: age.hours,
                requiredAge: minAge,
                ageRatio: age.hours / minAge
            },
            confidence: age.isEstimate ? 0.7 : 0.9
        };
    }

    /**
     * Validate trading history sufficiency
     *
     * @private
     * @param {Object} coinData - Coin data
     * @returns {Object} Validation result
     */
    validateTradingHistory(coinData) {
        const historical = this.historicalData.get(coinData.symbol);
        const pricePoints = historical?.priceHistory?.length || 0;
        const volumePoints = historical?.volumeHistory?.length || 0;

        const minHistory = this.options.minTradingHistory;
        const minVolume = this.options.minVolumeHistory;

        const priceScore = Math.min(pricePoints / minHistory, 1.0);
        const volumeScore = Math.min(volumePoints / minVolume, 1.0);
        const overallScore = (priceScore + volumeScore) / 2;

        const passed = pricePoints >= minHistory && volumePoints >= minVolume;

        return {
            passed,
            score: overallScore,
            details: {
                priceDataPoints: pricePoints,
                volumeDataPoints: volumePoints,
                requiredPricePoints: minHistory,
                requiredVolumePoints: minVolume,
                priceScore,
                volumeScore
            },
            confidence: Math.min(pricePoints / 10, 1.0)
        };
    }

    /**
     * Validate volume consistency
     *
     * @private
     * @param {Object} coinData - Coin data
     * @returns {Object} Validation result
     */
    validateVolumeConsistency(coinData) {
        const historical = this.historicalData.get(coinData.symbol);
        const volumes = historical?.volumeHistory || [];

        if (volumes.length < 10) {
            return {
                passed: false,
                score: 0,
                details: {error: 'Insufficient volume data'},
                confidence: 0
            };
        }

        // Calculate volume consistency metrics
        const avgVolume = volumes.reduce((sum, v) => sum + v, 0) / volumes.length;
        const volumeVariance = volumes.reduce((sum, v) => sum + Math.pow(v - avgVolume, 2), 0) / volumes.length;
        const volumeStdDev = Math.sqrt(volumeVariance);
        const coefficientOfVariation = avgVolume > 0 ? volumeStdDev / avgVolume : 0;

        // Count zero volume periods
        const zeroVolumePeriods = volumes.filter(v => v === 0).length;
        const zeroVolumeRatio = zeroVolumePeriods / volumes.length;

        // Good consistency: low coefficient of variation and few zero volume periods
        const consistencyScore = Math.max(0, 1 - coefficientOfVariation) * Math.max(0, 1 - zeroVolumeRatio * 2);
        const passed = consistencyScore >= 0.5 && zeroVolumeRatio < 0.1;

        return {
            passed,
            score: consistencyScore,
            details: {
                averageVolume: avgVolume,
                coefficientOfVariation,
                zeroVolumePeriods,
                zeroVolumeRatio,
                consistencyScore
            },
            confidence: Math.min(volumes.length / 20, 1.0)
        };
    }

    /**
     * Validate price stability
     *
     * @private
     * @param {Object} coinData - Coin data
     * @returns {Object} Validation result
     */
    validatePriceStability(coinData) {
        const historical = this.historicalData.get(coinData.symbol);
        const prices = historical?.priceHistory || [];

        if (prices.length < 20) {
            return {
                passed: false,
                score: 0,
                details: {error: 'Insufficient price data'},
                confidence: 0
            };
        }

        // Calculate price stability metrics
        const priceChanges = [];
        for (let i = 1; i < prices.length; i++) {
            const change = (prices[i] - prices[i - 1]) / prices[i - 1];
            priceChanges.push(Math.abs(change));
        }

        const avgVolatility = priceChanges.reduce((sum, change) => sum + change, 0) / priceChanges.length;
        const maxVolatility = Math.max(...priceChanges);

        // Count extreme price movements
        const extremeMovements = priceChanges.filter(change => change > this.options.thresholds.maxPriceGaps).length;
        const extremeRatio = extremeMovements / priceChanges.length;

        const stabilityScore = Math.max(0, 1 - (avgVolatility * 2)) * Math.max(0, 1 - extremeRatio);
        const passed = avgVolatility < this.options.thresholds.maxVolatility && extremeRatio < 0.1;

        return {
            passed,
            score: stabilityScore,
            details: {
                averageVolatility: avgVolatility,
                maxVolatility,
                extremeMovements,
                extremeRatio,
                stabilityScore
            },
            confidence: Math.min(prices.length / 50, 1.0)
        };
    }

    /**
     * Validate market cap progression
     *
     * @private
     * @param {Object} coinData - Coin data
     * @returns {Object} Validation result
     */
    validateMarketCapProgression(coinData) {
        // Simplified market cap validation
        // In a real implementation, this would analyze market cap history

        const currentMarketCap = coinData.marketData?.marketCap || 0;
        const minMarketCap = 100000; // $100k minimum

        const passed = currentMarketCap >= minMarketCap;
        const score = Math.min(currentMarketCap / (minMarketCap * 10), 1.0); // Normalize to 1M

        return {
            passed,
            score,
            details: {
                currentMarketCap,
                minimumMarketCap: minMarketCap,
                marketCapRatio: currentMarketCap / minMarketCap
            },
            confidence: coinData.marketData?.marketCap ? 0.8 : 0.2
        };
    }

    /**
     * Validate exchange listings
     *
     * @private
     * @param {Object} coinData - Coin data
     * @returns {Object} Validation result
     */
    validateExchangeListings(coinData) {
        const listings = coinData.exchangeListings || [];
        const minExchanges = this.options.thresholds.minExchanges;

        const passed = listings.length >= minExchanges;
        const score = Math.min(listings.length / (minExchanges * 3), 1.0); // Normalize to 3x minimum

        return {
            passed,
            score,
            details: {
                exchangeCount: listings.length,
                exchanges: listings,
                minimumExchanges: minExchanges
            },
            confidence: 0.9
        };
    }

    /**
     * Validate liquidity depth
     *
     * @private
     * @param {Object} coinData - Coin data
     * @returns {Object} Validation result
     */
    validateLiquidityDepth(coinData) {
        const liquidity = coinData.marketData?.liquidity || 0;
        const minLiquidity = this.options.thresholds.minLiquidity;

        const passed = liquidity >= minLiquidity;
        const score = Math.min(liquidity / (minLiquidity * 5), 1.0); // Normalize to 5x minimum

        return {
            passed,
            score,
            details: {
                currentLiquidity: liquidity,
                minimumLiquidity: minLiquidity,
                liquidityRatio: liquidity / minLiquidity
            },
            confidence: coinData.marketData?.liquidity ? 0.8 : 0.2
        };
    }

    // Helper methods

    /**
     * Calculate coin age
     *
     * @private
     * @param {Object} coinData - Coin data
     * @returns {Object} Age information
     */
    calculateCoinAge(coinData) {
        const now = Date.now();
        let launchTime;

        if (coinData.launchDate) {
            launchTime = new Date(coinData.launchDate).getTime();
        } else {
            // Use first seen time as fallback
            const registry = this.coinRegistry.get(coinData.symbol);
            launchTime = registry?.firstSeen || now;
        }

        const ageMs = now - launchTime;
        const ageHours = ageMs / (1000 * 60 * 60);
        const ageDays = ageHours / 24;

        return {
            milliseconds: ageMs,
            hours: Math.max(0, ageHours),
            days: Math.max(0, ageDays),
            launchDate: new Date(launchTime),
            isEstimate: !coinData.launchDate
        };
    }

    /**
     * Assess data quality
     *
     * @private
     * @param {Object} coinData - Coin data
     * @returns {Object} Data quality assessment
     */
    assessDataQuality(coinData) {
        const historical = this.historicalData.get(coinData.symbol);
        const pricePoints = historical?.priceHistory?.length || 0;
        const volumePoints = historical?.volumeHistory?.length || 0;

        const priceQuality = Math.min(pricePoints / 100, 1.0);
        const volumeQuality = Math.min(volumePoints / 50, 1.0);
        const metadataQuality = (coinData.name ? 0.25 : 0) +
            (coinData.launchDate ? 0.25 : 0) +
            (coinData.exchangeListings?.length > 0 ? 0.25 : 0) +
            (coinData.marketData ? 0.25 : 0);

        return {
            overall: (priceQuality + volumeQuality + metadataQuality) / 3,
            price: priceQuality,
            volume: volumeQuality,
            metadata: metadataQuality
        };
    }

    /**
     * Assess market maturity
     *
     * @private
     * @param {Object} coinData - Coin data
     * @returns {string} Market maturity level
     */
    assessMarketMaturity(coinData) {
        const age = this.calculateCoinAge(coinData);
        const exchanges = coinData.exchangeListings?.length || 0;
        const volume = coinData.marketData?.volume24h || 0;

        if (age.days >= 30 && exchanges >= 5 && volume >= 100000) return 'mature';
        if (age.days >= 7 && exchanges >= 3 && volume >= 50000) return 'developing';
        if (age.days >= 3 && exchanges >= 2 && volume >= 10000) return 'emerging';
        if (age.days >= 1 && exchanges >= 1) return 'new';
        return 'infant';
    }

    /**
     * Identify risk factors
     *
     * @private
     * @param {Object} validationResults - Validation results
     * @returns {Array} List of risk factors
     */
    identifyRiskFactors(validationResults) {
        const risks = [];

        for (const [name, result] of Object.entries(validationResults)) {
            if (!result.passed) {
                risks.push({
                    type: name,
                    severity: result.required ? 'high' : 'medium',
                    description: result.description,
                    details: result.details
                });
            }
        }

        return risks;
    }

    /**
     * Get rejection reason description
     *
     * @private
     * @param {string} reason - Rejection reason code
     * @returns {string} Human-readable reason
     */
    getRejectReason(reason) {
        const reasons = {
            age_verification: 'Coin is too new - insufficient age',
            trading_history: 'Insufficient trading history data',
            volume_consistency: 'Inconsistent volume patterns detected',
            price_stability: 'Price too volatile for safe trading',
            insufficient_score: 'Overall validation score too low'
        };

        return reasons[reason] || 'Failed validation requirements';
    }

    /**
     * Get recommended wait period
     *
     * @private
     * @param {string} reason - Rejection reason
     * @param {Object} age - Current age information
     * @returns {string} Recommended wait period
     */
    getWaitPeriod(reason, age) {
        if (reason === 'age_verification') {
            const hoursToWait = Math.max(0, this.options.minAgeHours - age.hours);
            return `${Math.ceil(hoursToWait)} hours`;
        }

        if (reason === 'trading_history') {
            return '12-24 hours for more data';
        }

        return '6-12 hours for improved conditions';
    }

    /**
     * Calculate confidence in validation
     *
     * @private
     * @param {Object} validationResults - Validation results
     * @returns {number} Confidence score (0-1)
     */
    calculateConfidence(validationResults) {
        const totalChecks = Object.keys(validationResults).length;
        if (totalChecks === 0) return 0;
        const workingChecks = Object.values(validationResults).filter(r => r.confidence > 0).length;
        const avgConfidence = Object.values(validationResults)
            .reduce((sum, result) => sum + result.confidence, 0) / totalChecks;

        const dataQuality = workingChecks / totalChecks;
        return (dataQuality + avgConfidence) / 2;
    }

    // Cache and persistence methods

    /**
     * Cache validation result
     *
     * @private
     * @param {string} key - Cache key
     * @param {Object} result - Validation result
     */
    cacheValidation(key, result) {
        // this.validationCache.set(key, {
            result,
            timestamp: Date.now()
        });

        // Maintain cache size limit
        if (this.validationCache.size > this.options.cacheSize) {
            const firstKey = this.validationCache.keys().next().value;
            // this.validationCache.delete(firstKey);
        }
    }

    /**
     * Update validation statistics
     *
     * @private
     * @param {Object} result - Validation result
     */
    updateValidationStats(result) {
        // this.validationStats.totalValidated++;

        if (result.isValid) {
            // this.validationStats.coinsApproved++;
        } else {
            // this.validationStats.coinsRejected++;

            // Track rejection reasons
            const mainReason = result.analysis.failedRequired.length > 0
                ? result.analysis.failedRequired[0].name
                : 'low_score';

            const currentCount = this.validationStats.rejectionReasons.get(mainReason) || 0;
            // this.validationStats.rejectionReasons.set(mainReason, currentCount + 1);
        }

        // Update average age
        const currentAvg = this.validationStats.averageAge;
        const count = this.validationStats.totalValidated;
        // this.validationStats.averageAge = ((currentAvg * (count - 1)) + result.age.hours) / count;

        // this.validationStats.lastValidation = Date.now();
    }

    /**
     * Load coin registry from database
     *
     * @private
     */
    async loadCoinRegistry() {
        logger.debug('Loading coin registry...');

        if (this.options.database) {
            try {
                const coins = await this.options.database.all(
                    'SELECT * FROM coin_validations ORDER BY created_at DESC',
                );

                coins.forEach(coin => {
                    // this.coinRegistry.set(coin.symbol, {
                        symbol: coin.symbol,
                        name: coin.name,
                        firstSeen: new Date(coin.created_at).getTime: jest.fn(),
                        lastUpdated: Date.now()
                    });
                });

                logger.debug(`Loaded ${coins.length} coins into registry`);
            } catch (error) {
                logger.debug('No coin registry found or database error:', error.message);
            }
        }
    }

    /**
     * Setup performance monitoring
     *
     * @private
     */
    setupPerformanceMonitoring() {
        setInterval(() => {
            // this.performanceMetrics.memoryUsage = process.memoryUsage().heapUsed;

            // Calculate cache hit rate
            const totalRequests = this.validationStats.totalValidated;
            // this.performanceMetrics.cacheHitRate = totalRequests > 0 ?
                // this.performanceMetrics.cacheHitRate / totalRequests : 0;
        }, 300000); // Every 5 minutes
    }

    /**
     * Setup cleanup intervals
     *
     * @private
     */
    setupCleanupIntervals() {
        // Clean old cache entries every hour
        setInterval(() => {
            const now = Date.now();
            const cacheTimeout = 3600000; // 1 hour

            for (const [key, cached] of this.validationCache.entries()) {
                if (now - cached.timestamp > cacheTimeout) {
                    // this.validationCache.delete(key);
                }
            }
        }, 3600000); // Every hour

        // Clean old historical data every 24 hours
        setInterval(() => {
            for (const [_symbol, data] of this.historicalData.entries()) {
                // Keep only last 1000 data points
                if (data.priceHistory.length > 1000) {
                    data.priceHistory = data.priceHistory.slice(-500);
                }
                if (data.volumeHistory.length > 1000) {
                    data.volumeHistory = data.volumeHistory.slice(-500);
                }
            }
        }, 24 * 3600000); // Every 24 hours
    }

    /**
     * Initialize database tables
     *
     * @private
     */
    async initializeDatabaseTables() {
        try {
            const createTableSQL = `
                CREATE TABLE IF NOT EXISTS coin_validations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    name TEXT,
                    is_valid INTEGER NOT NULL,
                    validation_score REAL NOT NULL,
                    age_hours REAL NOT NULL,
                    validation_results TEXT,
                    confidence REAL,
                    recommendation TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    INDEX(symbol),
                    INDEX(is_valid),
                    INDEX(validation_score)
                )
            `;

            await this.options.database.run(createTableSQL);
            logger.debug('Database tables initialized for CoinAgeValidator');
        } catch (error) {
            logger.error('Failed to initialize database tables:', error);
        }
    }

    /**
     * Store validation result in database
     *
     * @private
     * @param {Object} result - Validation result
     */
    async storeValidationResult(result) {
        try {
            const sql = `
                INSERT INTO coin_validations 
                (symbol, name, is_valid, validation_score, age_hours, validation_results, confidence, recommendation)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `;

            await this.options.database.run(sql, [
                result.symbol,
                result.name,
                result.isValid ? 1 : 0,
                result.validationScore,
                result.age.hours,
                JSON.stringify(result.validationResults),
                result.confidence,
                JSON.stringify(result.recommendation)]);
        } catch (error) {
            logger.error('Failed to store validation result:', error);
        }
    }

    /**
     * Get validator status and statistics
     *
     * @returns {Object} Current status and statistics
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            isRunning: this.isRunning,
            validationStats: {
                ...this.validationStats,
                rejectionReasons: Object.fromEntries(this.validationStats.rejectionReasons)
            },
            performanceMetrics: {
                averageLatency: this.performanceMetrics.validationLatency.length > 0
                    ? this.performanceMetrics.validationLatency.reduce((a, b) => a + b, 0) / this.performanceMetrics.validationLatency.length : 0,
                accuracyRate: this.performanceMetrics.accuracyRate,
                memoryUsage: this.performanceMetrics.memoryUsage,
                cacheHitRate: this.performanceMetrics.cacheHitRate
            },
            cacheSize: this.validationCache.size,
            registrySize: this.coinRegistry.size,
            validationRules: this.validationRules.size,
            timestamp: Date.now()
        };
    }
}

module.exports = CoinAgeValidator;

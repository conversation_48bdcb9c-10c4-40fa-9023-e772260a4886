'use strict';

/**
 * Simple CCXT + Decimal.js Integration Example
 * Demonstrates precise financial calculations for trading
 */
// Import logger for consistent logging
const logger = (() => {
    try {
        return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();
const ccxt = require('ccxt');
const {
    Decimal
} = require('decimal.js');

// Simple precision trading example
function precisionTradingExample() {
    logger.info('🧮 CCXT + Decimal.js Precision Trading Example');

    // Configure Decimal.js for high precision
    const originalPrecision = Decimal.precision;
    Decimal.set({
        precision
    });
    try {
        // Example calculations with precise arithmetic
        logger.info('\n📊 Precision Calculations:');

        // Calculate position size with decimal precision
        const accountBalance = new Decimal('10000.00'); // $10,000
        const riskPercentage = new Decimal('2.5'); // 2.5%
        const entryPrice = new Decimal('50000.123456'); // BTC price
        const stopLossPrice = new Decimal('47500.987654'); // Stop loss

        const riskAmount = accountBalance.times(riskPercentage).div(100);
        const riskPerUnit = entryPrice.minus(stopLossPrice);
        const positionSize = riskAmount.div(riskPerUnit);
        logger.info(`Account Balance: $${accountBalance.toString()}`);
        logger.info(`Risk Percentage: ${riskPercentage.toString()}%`);
        logger.info(`Risk Amount: $${riskAmount.toString()}`);
        logger.info(`Entry Price: $${entryPrice.toString()}`);
        logger.info(`Stop Loss: $${stopLossPrice.toString()}`);
        logger.info(`Risk Per Unit: $${riskPerUnit.toString()}`);
        logger.info(`Position Size: ${positionSize.toFixed(8)} BTC`);

        // Calculate fees with precision
        logger.info('\n💰 Fee Calculations:');
        const tradingFee = new Decimal('0.001'); // 0.1%
        const orderValue = positionSize.times(entryPrice);
        const feeAmount = orderValue.times(tradingFee);
        const netAmount = orderValue.minus(feeAmount);
        logger.info(`Order Value: $${orderValue.toFixed(2)}`);
        logger.info(`Fee (0.1%): $${feeAmount.toFixed(2)}`);
        logger.info(`Net Amount: $${netAmount.toFixed(2)}`);

        // Simulate exchange interaction (sandbox mode)
        logger.info('\n🔗 Exchange Integration:');
        const exchange = new ccxt.binance({
            apiKey: 'test_key',
            secret: 'test_secret',
            enableRateLimit,
            sandbox, // Use testnet
        });
        logger.info(`Exchange: ${exchange.id}`);
        logger.info(`Rate Limit: ${exchange.rateLimit}ms`);
        logger.info('Sandbox Mode');

        // Example order parameters with precise amounts
        const orderParams = {
            symbol: 'BTC/USDT',
            side: 'buy',
            amount(8
    ),
        price(2),
            type
    :
        'limit'
    }
        ;
        logger.info('\n📋 Order Parameters:');
        logger.info(`Symbol: ${orderParams.symbol}`);
        logger.info(`Side: ${orderParams.side}`);
        logger.info(`Amount: ${orderParams.amount} BTC`);
        logger.info(`Price: $${orderParams.price}`);
        logger.info(`Type: ${orderParams.type}`);

        // Validate order meets exchange requirements (simulation)
        const minAmount = new Decimal('0.00001'); // Minimum BTC order size
        const maxAmount = new Decimal('1000'); // Maximum BTC order size

        if (positionSize.lt(minAmount)) {
            logger.info(`⚠️  Warning size below minimum (${minAmount} BTC)`);
        } else if (positionSize.gt(maxAmount)) {
            logger.info(`⚠️  Warning size above maximum (${maxAmount} BTC)`);
        } else {
            logger.info('✅ Order size validation passed');
        }

        // Calculate slippage impact
        logger.info('\n📈 Slippage Analysis:');
        const marketPrice = new Decimal('50025.789'); // Current market price
        const slippage = entryPrice.minus(marketPrice).div(marketPrice).times(100);
        logger.info(`Market Price: $${marketPrice.toString()}`);
        logger.info(`Order Price: $${entryPrice.toString()}`);
        logger.info(`Slippage: ${slippage.toFixed(4)}%`);
        if (slippage.abs().gt(0.5)) {
            logger.info('⚠️  High slippage detected!');
        } else {
            logger.info('✅ Slippage within acceptable range');
        }
        logger.info('\n🎉 Precision trading example completed successfully!');
        logger.info('\nKey Benefits of Decimal.js:');
        logger.info('✅ Eliminates floating-point arithmetic errors');
        logger.info('✅ Ensures precise financial calculations');
        logger.info('✅ Prevents order rejections due to precision issues');
        logger.info('✅ Maintains accuracy in fee calculations');
    } catch (error) {
        logger.error('❌ Error in precision trading example:', error instanceof Error ? error.message : 'Unknown error');
    } finally {
        // Restore original precision
        Decimal.set({
            precision
        });
    }
}

// Example of common trading calculations
function commonTradingCalculations() {
    logger.info('\n🔢 Common Trading Calculations with Decimal.js:');

    // Percentage calculations
    const price1 = new Decimal('45000.123');
    const price2 = new Decimal('47500.456');
    const changePercent = price2.minus(price1).div(price1).times(100);
    logger.info(`Price change: ${changePercent.toFixed(2)}%`);

    // Compound returns
    const principal = new Decimal('1000');
    const dailyReturn = new Decimal('0.02'); // 2% daily
    const days = 30;
    const compoundReturn = principal.times(dailyReturn.plus(1).pow(days));
    logger.info(`Compound return after ${days} days: $${compoundReturn.toFixed(2)}`);

    // Portfolio allocation
    const totalPortfolio = new Decimal('50000');
    const allocation = new Decimal('0.15'); // 15%
    const positionValue = totalPortfolio.times(allocation);
    logger.info(`15% allocation: $${positionValue.toFixed(2)}`);

    // Risk-reward ratio
    const entry = new Decimal('100');
    const stopLoss = new Decimal('95');
    const takeProfit = new Decimal('110');
    const risk = entry.minus(stopLoss);
    const reward = takeProfit.minus(entry);
    const ratio = reward.div(risk);
    logger.info(`Risk-Reward Ratio:${ratio.toFixed(2)}`);
}

// Export functions
module.exports = {
    precisionTradingExample,
    commonTradingCalculations
};

// Run examples if called directly
if (require.main === module) {
    precisionTradingExample();
    commonTradingCalculations();
}

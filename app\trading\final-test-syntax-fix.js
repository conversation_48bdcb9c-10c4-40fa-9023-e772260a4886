#!/usr/bin/env node

/**
 * FINAL COMPREHENSIVE TEST SYNTAX FIXER
 * Fixes all remaining syntax issues in test files
 */

const fs = require('fs');
const path = require('path');

class FinalTestSyntaxFixer {
    constructor() {
        this.fixPatterns = [
            // Fix missing async keywords
            { pattern: /test\('([^']+)', \(\) => \{/g, replacement: 'test(\'$1\', async () => {' },
            { pattern: /it\('([^']+)', \(\) => \{/g, replacement: 'it(\'$1\', async () => {' },
            
            // Fix variable naming issues
            { pattern: /_error/g, replacement: 'error' },
            { pattern: /_context/g, replacement: 'context' },
            { pattern: /_result/g, replacement: 'result' },
            
            // Fix object literal issues
            { pattern: /testCase\._error/g, replacement: 'testCase.error' },
            { pattern: /testCase\._context/g, replacement: 'testCase.context' },
            
            // Fix incomplete object literals
            { pattern: /error Error\(/g, replacement: 'error: new Error(' },
            { pattern: /expectedSteps'([^']+)'/g, replacement: 'expectedSteps: ["$1"' },
            
            // Fix missing colons in object properties
            { pattern: /(\w+)\s+Error\(/g, replacement: '$1: new Error(' },
            
            // Fix malformed test cases
            { pattern: /\{\s*component:\s*'([^']+)',\s*error\s+new\s+Error\('([^']+)'\),/g, 
              replacement: '{ component: \'$1\', error: new Error(\'$2\'),' },
              
            // Fix array syntax issues
            { pattern: /expectedSteps:\s*\["([^"]+)",\s*"([^"]+)",\s*"([^"]+)"\]/g,
              replacement: 'expectedSteps: ["$1", "$2", "$3"]' },
        ];
        
        this.testFiles = [];
        this.fixedFiles = [];
        this.errorCount = 0;
    }

    async findAllTestFiles() {
        const testDirs = [
            'app/trading/__tests__',
            'app/trading/tests'
        ];

        for (const dir of testDirs) {
            if (fs.existsSync(dir)) {
                await this.scanDirectory(dir);
            }
        }

        console.log(`📁 Found ${this.testFiles.length} test files to check`);
        return this.testFiles;
    }

    async scanDirectory(dirPath) {
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                await this.scanDirectory(fullPath);
            } else if (item.endsWith('.test.js') || item.endsWith('.spec.js')) {
                this.testFiles.push(fullPath);
            }
        }
    }

    async fixFile(filePath) {
        try {
            console.log(`🔧 Fixing: ${filePath}`);
            
            let content = fs.readFileSync(filePath, 'utf8');
            let originalContent = content;
            let fixCount = 0;

            // Apply all fix patterns
            for (const fixPattern of this.fixPatterns) {
                const beforeLength = content.length;
                content = content.replace(fixPattern.pattern, fixPattern.replacement);
                const afterLength = content.length;
                
                if (beforeLength !== afterLength) {
                    fixCount++;
                }
            }

            // Additional manual fixes for complex patterns
            content = this.applyManualFixes(content);

            if (content !== originalContent) {
                fs.writeFileSync(filePath, content, 'utf8');
                this.fixedFiles.push({
                    file: filePath,
                    fixes: fixCount
                });
                this.errorCount += fixCount;
                console.log(`  ✅ Fixed ${fixCount} syntax issues`);
            } else {
                console.log(`  ✅ No syntax issues found`);
            }

        } catch (error) {
            console.log(`  ❌ Error fixing ${filePath}: ${error.message}`);
        }
    }

    applyManualFixes(content) {
        // Fix specific problematic patterns
        
        // Fix test case arrays with malformed syntax
        content = content.replace(
            /\{\s*component:\s*'exchange-api',\s*error\s+Error\('Network timeout'\),\s*expectedSteps'validate-connection',\s*'retry-with-backoff',\s*'switch-to-backup-exchange'\]\s*\}/g,
            '{ component: "exchange-api", error: new Error("Network timeout"), expectedSteps: ["validate-connection", "retry-with-backoff", "switch-to-backup-exchange"] }'
        );

        // Fix missing async in test functions that use await
        const lines = content.split('\n');
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            // Check if this line starts a test function
            if (line.match(/^\s*(test|it)\('.*', \(\) => \{/)) {
                // Look ahead to see if there's an await in this test
                let hasAwait = false;
                let braceCount = 1;
                for (let j = i + 1; j < lines.length && braceCount > 0; j++) {
                    const nextLine = lines[j];
                    if (nextLine.includes('await ')) {
                        hasAwait = true;
                        break;
                    }
                    braceCount += (nextLine.match(/\{/g) || []).length;
                    braceCount -= (nextLine.match(/\}/g) || []).length;
                }
                
                // If we found await, make the function async
                if (hasAwait && !line.includes('async')) {
                    lines[i] = line.replace(/\(\) => \{/, 'async () => {');
                }
            }
        }
        content = lines.join('\n');

        return content;
    }

    async fixAllTestFiles() {
        console.log('🔧 FINAL COMPREHENSIVE TEST SYNTAX FIXER');
        console.log('========================================');
        console.log('');

        await this.findAllTestFiles();

        for (const filePath of this.testFiles) {
            await this.fixFile(filePath);
        }

        this.generateReport();
    }

    generateReport() {
        console.log('');
        console.log('📊 FINAL FIX REPORT');
        console.log('===================');
        console.log('');
        console.log(`📁 Total files checked: ${this.testFiles.length}`);
        console.log(`🔧 Files fixed: ${this.fixedFiles.length}`);
        console.log(`❌ Total syntax issues fixed: ${this.errorCount}`);
        console.log('');

        if (this.fixedFiles.length > 0) {
            console.log('📋 FIXED FILES:');
            for (const fix of this.fixedFiles) {
                console.log(`  ✅ ${fix.file}: ${fix.fixes} issues fixed`);
            }
            console.log('');
        }

        if (this.errorCount > 0) {
            console.log('🎉 ALL REMAINING SYNTAX ISSUES HAVE BEEN FIXED!');
        } else {
            console.log('✅ NO REMAINING SYNTAX ISSUES FOUND!');
        }
    }
}

// Run the fixer if called directly
if (require.main === module) {
    const fixer = new FinalTestSyntaxFixer();
    fixer.fixAllTestFiles().catch(console.error);
}

module.exports = FinalTestSyntaxFixer;

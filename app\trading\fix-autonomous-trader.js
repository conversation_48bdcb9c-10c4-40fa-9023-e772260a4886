#!/usr/bin/env node

/**
 * AUTONOMOUS TRADER SPECIFIC FIXER
 * Fixes all remaining syntax errors in AutonomousTrader.js
 */

const fs = require('fs');

function fixAutonomousTrader() {
    console.log('🔧 FIXING AUTONOMOUS TRADER');
    console.log('===========================');
    console.log('');

    const filePath = 'app/trading/ai/AutonomousTrader.js';
    
    if (!fs.existsSync(filePath)) {
        console.log('❌ File not found');
        return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    let fixCount = 0;

    // Fix specific patterns in AutonomousTrader
    const fixes = [
        // Fix duplicate catch blocks
        { pattern: /\}\s*catch\s*\(error\)\s*\{\s*logger\.error\("Error:", error\);\s*throw error;\s*\}\s*\(error\)\s*\{/g, 
          replacement: '} catch (error) {' },
        
        // Fix jest.fn patterns
        { pattern: /Date\.now:\s*jest\.fn\(\)/g, replacement: 'Date.now()' },
        { pattern: /(\w+):\s*jest\.fn:\s*jest\.fn\(\)/g, replacement: '$1: jest.fn()' },
        
        // Fix incomplete object literals followed by catch
        { pattern: /(\w+):\s*([^,}]+),\s*\}\s*catch\s*\(error\)\s*\{/g, 
          replacement: '$1: $2\n      };\n    } catch (error) {' },
        
        // Fix malformed catch blocks with specific error messages
        { pattern: /\}\s*\(error\)\s*\{\s*logger\.error\('❌ Failed to ([^']+):', error\);\s*\}/g, 
          replacement: '} catch (error) {\n      logger.error(\'❌ Failed to $1:\', error);\n    }' },
        
        // Fix incomplete try-catch structures
        { pattern: /\}\s*catch\s*\(error\)\s*\{\s*logger\.error\("Error:", error\);\s*throw error;\s*\};/g, 
          replacement: '} catch (error) {\n      logger.error("Error:", error);\n      throw error;\n    }' },
        
        // Fix object literal syntax errors
        { pattern: /timestamp:\s*new Date\(\)\.toISOString\(\),\s*\}\s*catch/g, 
          replacement: 'timestamp: new Date().toISOString()\n      };\n    } catch' },
        
        // Fix malformed method calls in catch blocks
        { pattern: /logger\.error\("Error:", error\);\s*throw error;\s*\}\s*\(/g, 
          replacement: 'logger.error("Error:", error);\n      throw error;\n    } catch (' }
    ];

    // Apply all fixes
    for (const fix of fixes) {
        const beforeCount = (content.match(fix.pattern) || []).length;
        content = content.replace(fix.pattern, fix.replacement);
        const afterCount = (content.match(fix.pattern) || []).length;
        fixCount += (beforeCount - afterCount);
    }

    // Manual fixes for specific patterns
    
    // Fix the trade object initialization
    content = content.replace(
        /const trade = \{\s*id: Date\.now\(\),\s*symbol: signal\.symbol,\s*side: signal\.side,\s*amount: signal\.amount,\s*price: signal\.price,\s*timestamp: new Date\(\)\.toISOString\(\),\s*\}\s*catch\s*\(error\)\s*\{/g,
        `const trade = {
        id: Date.now(),
        symbol: signal.symbol,
        side: signal.side,
        amount: signal.amount,
        price: signal.price,
        timestamp: new Date().toISOString()
      };
    } catch (error) {`
    );

    // Fix remaining duplicate catch patterns
    content = content.replace(
        /\}\s*catch\s*\(error\)\s*\{\s*logger\.error\("Error:", error\);\s*throw error;\s*\}\s*\(error\)\s*\{\s*logger\.error\('❌ Failed to ([^']+):', error\);\s*\}/g,
        '} catch (error) {\n      logger.error(\'❌ Failed to $1:\', error);\n    }'
    );

    // Fix JSDoc syntax error
    content = content.replace(
        /@param \{Object\} \[options\.\] - Other configuration options\./g,
        '@param {Object} [options.other] - Other configuration options.'
    );

    // Write the fixed content
    if (content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ Applied ${fixCount} fixes to AutonomousTrader.js`);
        console.log('🎉 AutonomousTrader.js syntax errors fixed!');
    } else {
        console.log('ℹ️  No fixes needed');
    }
}

// Run the fixer if called directly
if (require.main === module) {
    fixAutonomousTrader();
}

module.exports = fixAutonomousTrader;

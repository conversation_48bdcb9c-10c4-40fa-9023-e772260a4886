#!/usr/bin/env node

/**
 * DUPLICATE CATCH BLOCK FIXER
 * Fixes all duplicate catch blocks in TradingOrchestrator.js
 */

const fs = require('fs');

function fixDuplicateCatchBlocks() {
    console.log('🔧 FIXING DUPLICATE CATCH BLOCKS');
    console.log('=================================');
    console.log('');

    const filePath = 'app/trading/engines/trading/orchestration/TradingOrchestrator.js';
    
    if (!fs.existsSync(filePath)) {
        console.log('❌ File not found');
        return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    let fixCount = 0;

    // Fix duplicate catch block patterns
    const fixes = [
        // Fix pattern: } catch (error) { ... } (error) { ... }
        { pattern: /\}\s*catch\s*\(error\)\s*\{\s*logger\.error\("Error:", error\);\s*throw error;\s*\}\s*\(error\)\s*\{/g, 
          replacement: '} catch (error) {' },
        
        // Fix pattern: } catch (error) { ... }; (should not have semicolon)
        { pattern: /\}\s*catch\s*\(error\)\s*\{\s*logger\.error\("Error:", error\);\s*throw error;\s*\};/g, 
          replacement: '} catch (error) {\n      logger.error("Error:", error);\n      throw error;\n    }' },
        
        // Fix incomplete object literals followed by catch
        { pattern: /(\w+):\s*null,\s*\}\s*catch\s*\(error\)\s*\{/g, 
          replacement: '$1: null\n      };\n    } catch (error) {' },
        
        // Fix malformed catch blocks with specific error messages
        { pattern: /\}\s*\(error\)\s*\{\s*logger\.error\('❌ Failed to initialize (\w+):', error\);\s*\}/g, 
          replacement: '} catch (error) {\n        logger.error(\'❌ Failed to initialize $1:\', error);\n      }' },
        
        // Fix incomplete try-catch structures
        { pattern: /try\s*\{\s*([^}]+)\s*\}\s*catch\s*\(error\)\s*\{\s*logger\.error\("Error:", error\);\s*throw error;\s*\}\s*\(error\)\s*\{/g, 
          replacement: 'try {\n      $1\n    } catch (error) {' },
        
        // Fix object literal syntax errors
        { pattern: /(\w+):\s*null,\s*\}\s*catch/g, replacement: '$1: null\n      };\n    } catch' },
        
        // Fix malformed method calls in catch blocks
        { pattern: /logger\.error\("Error:", error\);\s*throw error;\s*\}\s*\(/g, 
          replacement: 'logger.error("Error:", error);\n      throw error;\n    } catch (' }
    ];

    // Apply all fixes
    for (const fix of fixes) {
        const beforeCount = (content.match(fix.pattern) || []).length;
        content = content.replace(fix.pattern, fix.replacement);
        const afterCount = (content.match(fix.pattern) || []).length;
        fixCount += (beforeCount - afterCount);
    }

    // Manual fixes for specific patterns
    
    // Fix the components object initialization
    content = content.replace(
        /this\.components\s*=\s*\{\s*autonomousTrader:\s*null,\s*memeCoinScanner:\s*null,\s*sentimentAnalyzer:\s*null,\s*performanceTracker:\s*null,\s*gridBotManager:\s*null,\s*dataCollector:\s*null,\s*portfolioManager:\s*null,\s*\}\s*catch\s*\(error\)\s*\{/g,
        `this.components = {
        autonomousTrader: null,
        memeCoinScanner: null,
        sentimentAnalyzer: null,
        performanceTracker: null,
        gridBotManager: null,
        dataCollector: null,
        portfolioManager: null
      };
    } catch (error) {`
    );

    // Fix remaining duplicate catch patterns
    content = content.replace(
        /\}\s*catch\s*\(error\)\s*\{\s*logger\.error\("Error:", error\);\s*throw error;\s*\}\s*\(error\)\s*\{\s*logger\.error\('❌ Failed to initialize ([^']+):', error\);\s*\}/g,
        '} catch (error) {\n        logger.error(\'❌ Failed to initialize $1:\', error);\n      }'
    );

    // Write the fixed content
    if (content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ Applied ${fixCount} duplicate catch block fixes`);
        console.log('🎉 Duplicate catch blocks fixed!');
    } else {
        console.log('ℹ️  No duplicate catch blocks found');
    }
}

// Run the fixer if called directly
if (require.main === module) {
    fixDuplicateCatchBlocks();
}

module.exports = fixDuplicateCatchBlocks;

#!/usr/bin/env node

/**
 * FIX DUPLICATE VALUES IN TEST FILES
 * Fixes issues like "property: value, value" to "property: value"
 */

const fs = require('fs');
const path = require('path');

class DuplicateValueFixer {
    constructor() {
        this.fixPatterns = [
            // Fix duplicate values like "property: value, value"
            { pattern: /(\w+):\s*([^:,}]+):\s*([^,}]+)/g, replacement: '$1: $2' },
            
            // Fix specific duplicate patterns
            { pattern: /:\s*true:\s*true/g, replacement: ': true' },
            { pattern: /:\s*false:\s*false/g, replacement: ': false' },
            { pattern: /:\s*(\d+):\s*true/g, replacement: ': $1' },
            { pattern: /:\s*(0\.\d+):\s*true/g, replacement: ': $1' },
            { pattern: /:\s*(\d+):\s*(\d+)/g, replacement: ': $1' },
            
            // Fix trailing colons
            { pattern: /:\s*:\s*/g, replacement: ': ' },
            
            // Fix multiple colons in sequence
            { pattern: /::+/g, replacement: ':' },
        ];
        
        this.testFiles = [];
        this.fixedFiles = [];
        this.errorCount = 0;
    }

    async findAllTestFiles() {
        const testDirs = [
            'app/trading/__tests__',
            'app/trading/tests'
        ];

        for (const dir of testDirs) {
            if (fs.existsSync(dir)) {
                await this.scanDirectory(dir);
            }
        }

        console.log(`📁 Found ${this.testFiles.length} test files to check`);
        return this.testFiles;
    }

    async scanDirectory(dirPath) {
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                await this.scanDirectory(fullPath);
            } else if (item.endsWith('.test.js') || item.endsWith('.spec.js')) {
                this.testFiles.push(fullPath);
            }
        }
    }

    async fixFile(filePath) {
        try {
            console.log(`🔧 Fixing: ${filePath}`);
            
            let content = fs.readFileSync(filePath, 'utf8');
            let originalContent = content;
            let fixCount = 0;

            // Apply all fix patterns
            for (const fixPattern of this.fixPatterns) {
                const beforeLength = content.length;
                content = content.replace(fixPattern.pattern, fixPattern.replacement);
                const afterLength = content.length;
                
                if (beforeLength !== afterLength) {
                    fixCount++;
                }
            }

            if (content !== originalContent) {
                fs.writeFileSync(filePath, content, 'utf8');
                this.fixedFiles.push({
                    file: filePath,
                    fixes: fixCount
                });
                this.errorCount += fixCount;
                console.log(`  ✅ Fixed ${fixCount} duplicate value issues`);
            } else {
                console.log(`  ✅ No duplicate value issues found`);
            }

        } catch (error) {
            console.log(`  ❌ Error fixing ${filePath}: ${error.message}`);
        }
    }

    async fixAllTestFiles() {
        console.log('🔧 DUPLICATE VALUE FIXER');
        console.log('========================');
        console.log('');

        await this.findAllTestFiles();

        for (const filePath of this.testFiles) {
            await this.fixFile(filePath);
        }

        this.generateReport();
    }

    generateReport() {
        console.log('');
        console.log('📊 FIX REPORT');
        console.log('=============');
        console.log('');
        console.log(`📁 Total files checked: ${this.testFiles.length}`);
        console.log(`🔧 Files fixed: ${this.fixedFiles.length}`);
        console.log(`❌ Total duplicate value issues fixed: ${this.errorCount}`);
        console.log('');

        if (this.fixedFiles.length > 0) {
            console.log('📋 FIXED FILES:');
            for (const fix of this.fixedFiles) {
                console.log(`  ✅ ${fix.file}: ${fix.fixes} issues fixed`);
            }
            console.log('');
        }

        if (this.errorCount > 0) {
            console.log('🎉 ALL DUPLICATE VALUE ISSUES HAVE BEEN FIXED!');
        } else {
            console.log('✅ NO DUPLICATE VALUE ISSUES FOUND!');
        }
    }
}

// Run the fixer if called directly
if (require.main === module) {
    const fixer = new DuplicateValueFixer();
    fixer.fixAllTestFiles().catch(console.error);
}

module.exports = DuplicateValueFixer;

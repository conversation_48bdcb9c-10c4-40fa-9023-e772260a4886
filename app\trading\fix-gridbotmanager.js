#!/usr/bin/env node

/**
 * GRIDBOTMANAGER SPECIFIC FIXER
 * Fixes all remaining syntax errors in GridBotManager.js
 */

const fs = require('fs');

function fixGridBotManager() {
    console.log('🔧 FIXING GRIDBOTMANAGER.JS');
    console.log('============================');
    console.log('');

    const filePath = 'app/trading/engines/trading/bots/GridBotManager.js';
    
    if (!fs.existsSync(filePath)) {
        console.log('❌ File not found');
        return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    let fixCount = 0;

    // Fix specific patterns in GridBotManager
    const fixes = [
        // Fix malformed method calls in object literals
        { pattern: /this\.getBotCountByExchange:\s*jest\.fn:\s*jest\.fn\(\)/g, replacement: 'this.getBotCountByExchange()' },
        
        // Fix malformed try-catch blocks
        { pattern: /\/\/ Check bot limits:\s*if\(/g, replacement: '// Check bot limits\n      if (' },
        
        // Fix incomplete try blocks
        { pattern: /try\s*\{\s*\/\/ Check bot limits/g, replacement: 'try {\n      // Check bot limits' },
        
        // Fix missing catch blocks
        { pattern: /logger\.info\(`Grid bot removed: \$\{botId\}`\);\s*\/\/ this\.emit\('bot-removed', \{ botId, bot \}\);/g, 
          replacement: 'logger.info(`Grid bot removed: ${botId}`);\n      // this.emit(\'bot-removed\', { botId, bot });\n      return true;\n    } catch (error) {\n      logger.error(`Error removing bot ${botId}:`, error);\n      throw error;' },
        
        // Fix malformed object properties
        { pattern: /createdAt:\s*new\s*Date:\s*jest\.fn:\s*jest\.fn\(\)/g, replacement: 'createdAt: new Date()' },
        
        // Fix incomplete if statements
        { pattern: /\/\/ Check bot limits:\s*if\(this\.bots\.size >= this\.config\.maxTotalBots\)\s*\{/g, 
          replacement: '// Check bot limits\n      if (this.bots.size >= this.config.maxTotalBots) {' },
        
        // Fix throw statements
        { pattern: /throw\s*new:\s*Error\(/g, replacement: 'throw new Error(' },
        
        // Fix method declarations
        { pattern: /async\s*createBot\(config\)\s*\{/g, replacement: 'async createBot(config) {' },
        { pattern: /async\s*startBot\(botId\)\s*\{/g, replacement: 'async startBot(botId) {' },
        { pattern: /async\s*stopBot\(botId\)\s*\{/g, replacement: 'async stopBot(botId) {' },
        { pattern: /async\s*removeBot\(botId\)\s*\{/g, replacement: 'async removeBot(botId) {' },
        
        // Fix incomplete variable references
        { pattern: /logger\.info\(`Grid bot started: \$\{botId\} for \$\{bot\.symbol\}`\);/g, 
          replacement: 'logger.info(`Grid bot started: ${botId} for ${bot.symbol}`);' },
        
        // Fix malformed object syntax
        { pattern: /status:\s*'created',\s*createdAt:\s*new\s*Date\(\),\s*config:\s*\{\s*\.\.\.config\s*\},/g, 
          replacement: 'status: \'created\',\n        createdAt: new Date(),\n        config: { ...config }' },
        
        // Fix incomplete method calls
        { pattern: /this\.bots\.get\(botId\);\s*if\s*\(\s*!bot\s*\)\s*\{/g, 
          replacement: 'this.bots.get(botId);\n      if (!bot) {' },
        
        // Fix malformed return statements
        { pattern: /return\s*botId;\s*\}\s*catch\s*\(error\)\s*\{/g, 
          replacement: 'return botId;\n    } catch (error) {' }
    ];

    // Apply all fixes
    for (const fix of fixes) {
        const beforeCount = (content.match(fix.pattern) || []).length;
        content = content.replace(fix.pattern, fix.replacement);
        const afterCount = (content.match(fix.pattern) || []).length;
        fixCount += (beforeCount - afterCount);
    }

    // Manual fixes for specific issues
    
    // Fix the try-catch structure around createBot
    content = content.replace(
        /async createBot\(config\) \{\s*try \{\s*\/\/ Check bot limits\s*if \(this\.bots\.size >= this\.config\.maxTotalBots\) \{\s*throw new Error\(`Maximum number of bots reached: \$\{this\.config\.maxTotalBots\}`\);\s*\}/g,
        `async createBot(config) {
    try {
      // Check bot limits
      if (this.bots.size >= this.config.maxTotalBots) {
        throw new Error(\`Maximum number of bots reached: \${this.config.maxTotalBots}\`);
      }`
    );

    // Fix the removeBot method structure
    content = content.replace(
        /async removeBot\(botId\) \{\s*const bot = this\.bots\.get\(botId\);\s*if \(!bot\) \{\s*throw new Error\(`Bot not found: \$\{botId\}`\);\s*\}\s*\/\/ Stop bot if running\s*if \(bot\.status === 'running'\) \{\s*await this\.stopBot\(botId\);\s*\}\s*\/\/ Remove from collections\s*\/\/ this\.bots\.delete\(botId\);\s*\/\/ this\.activeBots\.delete\(botId\);\s*logger\.info\(`Grid bot removed: \$\{botId\}`\);\s*\/\/ this\.emit\('bot-removed', \{ botId, bot \}\);/g,
        `async removeBot(botId) {
    try {
      const bot = this.bots.get(botId);
      if (!bot) {
        throw new Error(\`Bot not found: \${botId}\`);
      }

      // Stop bot if running
      if (bot.status === 'running') {
        await this.stopBot(botId);
      }

      // Remove from collections
      this.bots.delete(botId);
      this.activeBots.delete(botId);

      logger.info(\`Grid bot removed: \${botId}\`);
      // this.emit('bot-removed', { botId, bot });
      return true;`
    );

    // Write the fixed content
    if (content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ Applied ${fixCount} fixes to GridBotManager.js`);
        console.log('🎉 GridBotManager.js syntax errors fixed!');
    } else {
        console.log('ℹ️  No fixes needed');
    }
}

// Run the fixer if called directly
if (require.main === module) {
    fixGridBotManager();
}

module.exports = fixGridBotManager;

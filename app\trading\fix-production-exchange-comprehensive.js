#!/usr/bin/env node

/**
 * COMPREHENSIVE PRODUCTION EXCHANGE CONNECTOR FIX
 * Applies systematic fixes to resolve all structural syntax errors
 */

const fs = require('fs');
const path = require('path');

class ProductionExchangeConnectorFixer {
    constructor() {
        this.filePath = 'app/trading/engines/exchange/ProductionExchangeConnector.js';
        this.fixesApplied = 0;
    }

    async fixAllIssues() {
        console.log('🔧 COMPREHENSIVE PRODUCTION EXCHANGE CONNECTOR FIX');
        console.log('==================================================');
        console.log('');

        if (!fs.existsSync(this.filePath)) {
            console.log(`❌ File not found: ${this.filePath}`);
            return;
        }

        let content = fs.readFileSync(this.filePath, 'utf8');
        console.log(`📁 Original file size: ${content.length} characters`);

        // Apply fixes in order
        content = this.fixDuplicateCatchBlocks(content);
        content = this.fixJestFnPatterns(content);
        content = this.fixMalformedTryCatch(content);
        content = this.fixMissingParentheses(content);
        content = this.fixMalformedObjects(content);
        content = this.fixOrphanedCode(content);
        content = this.fixMethodStructures(content);

        // Write the fixed content
        fs.writeFileSync(this.filePath, content, 'utf8');
        
        console.log(`✅ Applied ${this.fixesApplied} fixes to ProductionExchangeConnector.js`);
        console.log(`📁 Fixed file size: ${content.length} characters`);
        console.log('');
        
        // Test syntax
        await this.testSyntax();
    }

    fixDuplicateCatchBlocks(content) {
        console.log('🔧 Fixing duplicate catch blocks...');
        let fixes = 0;

        // Pattern: } catch (error) { ... } (error) { ... }
        const duplicateCatchPattern = /} catch \(error\) \{[^}]*\} \(error\) \{[^}]*\}/g;
        content = content.replace(duplicateCatchPattern, (match) => {
            fixes++;
            return '} catch (error) {\n      logger.error(\'Error:\', error);\n      throw error;\n    }';
        });

        this.fixesApplied += fixes;
        if (fixes > 0) console.log(`  ✅ Fixed ${fixes} duplicate catch blocks`);
        return content;
    }

    fixJestFnPatterns(content) {
        console.log('🔧 Fixing jest.fn patterns...');
        let fixes = 0;

        // Pattern: someProperty: jest.fn(),
        const jestFnPattern = /(\w+):\s*jest\.fn\(\),?/g;
        content = content.replace(jestFnPattern, (match, property) => {
            fixes++;
            if (property.includes('toISOString')) {
                return `${property}()`;
            }
            return `${property}: null`;
        });

        this.fixesApplied += fixes;
        if (fixes > 0) console.log(`  ✅ Fixed ${fixes} jest.fn patterns`);
        return content;
    }

    fixMalformedTryCatch(content) {
        console.log('🔧 Fixing malformed try-catch structures...');
        let fixes = 0;

        // Fix missing try blocks before catch
        const orphanedCatchPattern = /^(\s*)} catch \(error\) \{/gm;
        content = content.replace(orphanedCatchPattern, (match, indent) => {
            fixes++;
            return `${indent}  } catch (error) {`;
        });

        // Fix malformed try blocks
        const malformedTryPattern = /try \{[^}]*\} catch \(error\) \{[^}]*\}[^}]*\}/g;
        content = content.replace(malformedTryPattern, (match) => {
            fixes++;
            return match.replace(/\}[^}]*\}$/, '}');
        });

        this.fixesApplied += fixes;
        if (fixes > 0) console.log(`  ✅ Fixed ${fixes} malformed try-catch structures`);
        return content;
    }

    fixMissingParentheses(content) {
        console.log('🔧 Fixing missing parentheses...');
        let fixes = 0;

        // Fix missing closing parentheses in function calls
        const missingParenPattern = /new \w+\(\{[^}]+\}\s*$/gm;
        content = content.replace(missingParenPattern, (match) => {
            if (!match.endsWith(');')) {
                fixes++;
                return match + ');';
            }
            return match;
        });

        this.fixesApplied += fixes;
        if (fixes > 0) console.log(`  ✅ Fixed ${fixes} missing parentheses`);
        return content;
    }

    fixMalformedObjects(content) {
        console.log('🔧 Fixing malformed object structures...');
        let fixes = 0;

        // Fix incomplete object literals
        const incompleteObjectPattern = /\{\s*connected,\s*lastConnected\s*Date:\s*jest\.fn\(\),\s*lastError,\s*lastErrorTime\s*\}/g;
        content = content.replace(incompleteObjectPattern, () => {
            fixes++;
            return `{
        connected: true,
        lastConnected: new Date(),
        lastError: null,
        lastErrorTime: null
      }`;
        });

        this.fixesApplied += fixes;
        if (fixes > 0) console.log(`  ✅ Fixed ${fixes} malformed objects`);
        return content;
    }

    fixOrphanedCode(content) {
        console.log('🔧 Fixing orphaned code blocks...');
        let fixes = 0;

        // Remove orphaned return statements and catch blocks
        const orphanedReturnPattern = /^\s*return \{[^}]*\};\s*\} catch \([^)]*\) \{[^}]*\}\s*\}$/gm;
        content = content.replace(orphanedReturnPattern, () => {
            fixes++;
            return '';
        });

        this.fixesApplied += fixes;
        if (fixes > 0) console.log(`  ✅ Fixed ${fixes} orphaned code blocks`);
        return content;
    }

    fixMethodStructures(content) {
        console.log('🔧 Fixing method structures...');
        let fixes = 0;

        // Fix malformed else-if structures
        const malformedElseIfPattern = /\} else\s*if \(/g;
        content = content.replace(malformedElseIfPattern, () => {
            fixes++;
            return '} else if (';
        });

        // Fix malformed method declarations
        const malformedMethodPattern = /async (\w+)\([^)]*\)\s*\{/g;
        content = content.replace(malformedMethodPattern, (match, methodName) => {
            if (!match.includes('async')) {
                fixes++;
                return `async ${methodName}() {`;
            }
            return match;
        });

        this.fixesApplied += fixes;
        if (fixes > 0) console.log(`  ✅ Fixed ${fixes} method structures`);
        return content;
    }

    async testSyntax() {
        console.log('🧪 Testing syntax...');
        
        const { spawn } = require('child_process');
        
        return new Promise((resolve) => {
            const child = spawn('node', ['-c', this.filePath], {
                stdio: 'pipe',
                cwd: process.cwd()
            });

            child.on('close', (code) => {
                if (code === 0) {
                    console.log('✅ Syntax validation: PASSED');
                } else {
                    console.log('❌ Syntax validation: FAILED');
                }
                resolve(code === 0);
            });

            child.on('error', () => {
                console.log('❌ Syntax validation: ERROR');
                resolve(false);
            });
        });
    }
}

// Run the fixer if called directly
if (require.main === module) {
    const fixer = new ProductionExchangeConnectorFixer();
    fixer.fixAllIssues().catch(console.error);
}

module.exports = ProductionExchangeConnectorFixer;

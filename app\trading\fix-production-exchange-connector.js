#!/usr/bin/env node

/**
 * PRODUCTION EXCHANGE CONNECTOR SPECIFIC FIXER
 * Fixes all remaining syntax errors in ProductionExchangeConnector.js
 */

const fs = require('fs');

function fixProductionExchangeConnector() {
    console.log('🔧 FIXING PRODUCTION EXCHANGE CONNECTOR');
    console.log('=======================================');
    console.log('');

    const filePath = 'app/trading/engines/exchange/ProductionExchangeConnector.js';
    
    if (!fs.existsSync(filePath)) {
        console.log('❌ File not found');
        return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    let fixCount = 0;

    // Fix specific patterns in ProductionExchangeConnector
    const fixes = [
        // Fix duplicate catch blocks
        { pattern: /\}\s*catch\s*\(error\)\s*\{\s*logger\.error\("Error:", error\);\s*throw error;\s*\}\s*\(error\)\s*\{/g, 
          replacement: '} catch (error) {' },
        
        // Fix malformed catch blocks with return statements
        { pattern: /\}\s*\(error\)\s*\{\s*return console;\s*\/\/ Fallback to console if logger not available\s*\}/g, 
          replacement: '} catch (error) {\n        return console; // Fallback to console if logger not available\n    }' },
        
        // Fix jest.fn patterns
        { pattern: /(\w+):\s*jest\.fn:\s*jest\.fn\(\)/g, replacement: '$1: jest.fn()' },
        { pattern: /Promise\.resolve:\s*jest\.fn\(\)/g, replacement: 'Promise.resolve({})' },
        
        // Fix incomplete object literals followed by catch
        { pattern: /(\w+):\s*([^,}]+),\s*\}\s*catch\s*\(error\)\s*\{/g, 
          replacement: '$1: $2\n      };\n    } catch (error) {' },
        
        // Fix malformed catch blocks with specific error messages
        { pattern: /\}\s*\(error\)\s*\{\s*logger\.error\('❌ Failed to ([^']+):', error\);\s*\}/g, 
          replacement: '} catch (error) {\n      logger.error(\'❌ Failed to $1:\', error);\n    }' },
        
        // Fix incomplete try-catch structures
        { pattern: /\}\s*catch\s*\(error\)\s*\{\s*logger\.error\("Error:", error\);\s*throw error;\s*\};/g, 
          replacement: '} catch (error) {\n      logger.error("Error:", error);\n      throw error;\n    }' },
        
        // Fix object literal syntax errors
        { pattern: /(\w+):\s*null,\s*\}\s*catch/g, replacement: '$1: null\n      };\n    } catch' },
        
        // Fix malformed method calls in catch blocks
        { pattern: /logger\.error\("Error:", error\);\s*throw error;\s*\}\s*\(/g, 
          replacement: 'logger.error("Error:", error);\n      throw error;\n    } catch (' }
    ];

    // Apply all fixes
    for (const fix of fixes) {
        const beforeCount = (content.match(fix.pattern) || []).length;
        content = content.replace(fix.pattern, fix.replacement);
        const afterCount = (content.match(fix.pattern) || []).length;
        fixCount += (beforeCount - afterCount);
    }

    // Manual fixes for specific patterns
    
    // Fix the logger initialization at the top
    content = content.replace(
        /const logger = \(\(\) => \{\s*try \{\s*return require\('\.\.\/\.\.\/shared\/logger'\);\s*\}\s*catch\s*\(error\)\s*\{\s*logger\.error\("Error:", error\);\s*throw error;\s*\}\s*\(error\)\s*\{\s*return console;\s*\/\/ Fallback to console if logger not available\s*\}\s*\}\)\(\);/g,
        `const logger = (() => {
    try {
        return require('../../shared/logger');
    } catch (error) {
        return console; // Fallback to console if logger not available
    }
})();`
    );

    // Fix remaining duplicate catch patterns
    content = content.replace(
        /\}\s*catch\s*\(error\)\s*\{\s*logger\.error\("Error:", error\);\s*throw error;\s*\}\s*\(error\)\s*\{\s*logger\.error\('❌ Failed to ([^']+):', error\);\s*\}/g,
        '} catch (error) {\n      logger.error(\'❌ Failed to $1:\', error);\n    }'
    );

    // Write the fixed content
    if (content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ Applied ${fixCount} fixes to ProductionExchangeConnector.js`);
        console.log('🎉 ProductionExchangeConnector.js syntax errors fixed!');
    } else {
        console.log('ℹ️  No fixes needed');
    }
}

// Run the fixer if called directly
if (require.main === module) {
    fixProductionExchangeConnector();
}

module.exports = fixProductionExchangeConnector;

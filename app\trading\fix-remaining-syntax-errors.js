#!/usr/bin/env node

/**
 * REMAINING SYNTAX ERRORS FIXER
 * Fixes all remaining "async: " and "new: " patterns across the codebase
 */

const fs = require('fs');
const path = require('path');

class RemainingSyntaxErrorsFixer {
    constructor() {
        this.fixedFiles = [];
        this.totalFixes = 0;
    }

    async fixAllRemainingErrors() {
        console.log('🔧 FIXING REMAINING SYNTAX ERRORS');
        console.log('=================================');
        console.log('');

        // Find all JavaScript files in the trading directory
        const jsFiles = this.findAllJSFiles('app/trading');
        
        console.log(`📁 Found ${jsFiles.length} JavaScript files to check`);
        console.log('');

        for (const filePath of jsFiles) {
            await this.fixFile(filePath);
        }

        this.generateReport();
    }

    findAllJSFiles(dir) {
        const files = [];
        
        if (!fs.existsSync(dir)) {
            return files;
        }

        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                // Skip node_modules and other irrelevant directories
                if (!item.startsWith('.') && item !== 'node_modules') {
                    files.push(...this.findAllJSFiles(fullPath));
                }
            } else if (item.endsWith('.js')) {
                files.push(fullPath);
            }
        }
        
        return files;
    }

    async fixFile(filePath) {
        try {
            let content = fs.readFileSync(filePath, 'utf8');
            const originalContent = content;
            let fixCount = 0;

            // Fix "async: " patterns
            const asyncMatches = content.match(/async:\s*(\w+)/g);
            if (asyncMatches) {
                for (const match of asyncMatches) {
                    const methodName = match.replace(/async:\s*/, '');
                    content = content.replace(match, `async ${methodName}`);
                    fixCount++;
                }
            }

            // Fix "new: " patterns
            const newMatches = content.match(/new:\s*([\w.()]+)/g);
            if (newMatches) {
                for (const match of newMatches) {
                    const constructor = match.replace(/new:\s*/, '');
                    content = content.replace(match, `new ${constructor}`);
                    fixCount++;
                }
            }

            // Fix other common patterns
            const additionalFixes = [
                // Fix malformed if statements
                { pattern: /\/\/ Check bot limits:\s*if\(/g, replacement: '// Check bot limits\n      if (' },
                
                // Fix malformed object properties
                { pattern: /(\w+):\s*(\w+):\s*/g, replacement: '$1: $2, ' },
                
                // Fix incomplete method declarations
                { pattern: /(\w+)\(\)\s*\{/g, replacement: '$1() {' },
                
                // Fix malformed try-catch
                { pattern: /\}\s*catch\s*\(\s*error\s*\)\s*\{/g, replacement: '} catch (error) {' },
                
                // Fix incomplete object syntax
                { pattern: /\{\s*(\w+)\s+(\w+)\(\)/g, replacement: '{ $1: $2()' },
                
                // Fix missing commas in object literals
                { pattern: /(\w+):\s*true\s+(\w+):/g, replacement: '$1: true, $2:' },
                { pattern: /(\w+):\s*false\s+(\w+):/g, replacement: '$1: false, $2:' },
                { pattern: /(\w+):\s*(\d+)\s+(\w+):/g, replacement: '$1: $2, $3:' },
                
                // Fix malformed dependencies arrays
                { pattern: /dependencies\s*\[\s*'(\w+)'\s*\]/g, replacement: "dependencies: ['$1']" },
                { pattern: /dependencies\s*\[\s*'(\w+)',\s*'(\w+)'\s*\]/g, replacement: "dependencies: ['$1', '$2']" },
                
                // Fix incomplete variable declarations
                { pattern: /required: true,/g, replacement: 'required: true,' },
                { pattern: /timeout: 30000,/g, replacement: 'timeout: 30000,' },
                { pattern: /retries: 3,/g, replacement: 'retries: 3,' },
                { pattern: /healthCheck: true,/g, replacement: 'healthCheck: true,' },
                
                // Fix malformed function calls in object literals
                { pattern: /(\w+)\(\),/g, replacement: '$1: jest.fn: jest.fn(),' }
            ];

            for (const fix of additionalFixes) {
                const beforeCount = (content.match(fix.pattern) || []).length;
                content = content.replace(fix.pattern, fix.replacement);
                const afterCount = (content.match(fix.pattern) || []).length;
                fixCount += (beforeCount - afterCount);
            }

            // Write the fixed content if changes were made
            if (content !== originalContent) {
                fs.writeFileSync(filePath, content, 'utf8');
                console.log(`🔧 Fixed ${filePath} (${fixCount} fixes)`);
                this.fixedFiles.push({ file: filePath, fixes: fixCount });
                this.totalFixes += fixCount;
            }

        } catch (error) {
            console.log(`❌ Error fixing ${filePath}: ${error.message}`);
        }
    }

    generateReport() {
        console.log('');
        console.log('📊 REMAINING SYNTAX ERRORS FIX REPORT');
        console.log('======================================');
        console.log('');
        console.log(`📁 Total files processed: ${this.fixedFiles.length}`);
        console.log(`🔧 Total fixes applied: ${this.totalFixes}`);
        console.log('');

        if (this.fixedFiles.length > 0) {
            console.log('✅ FIXED FILES:');
            for (const file of this.fixedFiles) {
                console.log(`  📄 ${file.file} (${file.fixes} fixes)`);
            }
            console.log('');
            console.log('🎉 REMAINING SYNTAX ERRORS HAVE BEEN FIXED!');
            console.log('✅ All "async: " and "new: " patterns corrected');
            console.log('✅ Additional syntax issues resolved');
        } else {
            console.log('ℹ️  No remaining syntax errors found');
        }
    }
}

// Run the fixer if called directly
if (require.main === module) {
    const fixer = new RemainingSyntaxErrorsFixer();
    fixer.fixAllRemainingErrors().catch(console.error);
}

module.exports = RemainingSyntaxErrorsFixer;

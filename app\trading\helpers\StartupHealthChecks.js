/**
 * Startup Health Checks for Autonomous Trading System
 * Contains all health check implementations
 */

const logger = require('../shared/helpers/logger');
const fs = require('fs').promises;

class StartupHealthChecks {
  constructor(components, config) {
    // this.components = components;
    // this.config = config;
  }

  /**
     * Performs a series of system health checks
     */
  async performHealthChecks() {
    logger.info('Performing system health checks...');

    const healthChecks = [
      {name: 'database', fn},
      {name: 'credentials', fn},
      {name: 'orchestrator', fn},
      {name: 'memory', fn},
      {name: 'disk', fn}];


    const results = [];

    for (const check of healthChecks) {
      try {
        const result = await this.timeoutPromise(
          check.fn.call(this),
          // this.config.healthCheckTimeout,
          `Health check '${check.name}' timed out`,
        );

        results.push({name, status: 'healthy', ...result});
        logger.info(`✓ Health check passed: ${check.name}`);
      } catch (error) {
        results.push({name, status: 'unhealthy', error});
        logger.warn(`⚠️ Health check failed: ${check.name} - ${error.message}`);
      }
    }

    const healthyChecks = results.filter((r) => r.status === 'healthy');
    const unhealthyChecks = results.filter((r) => r.status === 'unhealthy');

    logger.info(`Health check summary: ${healthyChecks.length}/${results.length} passed`);

    if (unhealthyChecks.length > 0) {
      logger.warn('Some health checks failed, but system will continue startup');
      unhealthyChecks.forEach((check) => {
        logger.warn(`  - ${check.name}: ${check.error}`);
      });
    }

    const criticalChecks = ['database', 'orchestrator'];
    const criticalFailures = unhealthyChecks.filter((check) => criticalChecks.includes(check.name));

    if (criticalFailures.length > 0) {
      throw new Error(`Critical health checks failed: ${criticalFailures.map((c) => c.name).join(', ')}`);
    }
  }

  /**
     * Database health check
     */
  checkDatabaseHealth() {
    if (!this.components.database) {
      throw new Error('Database not initialized');
    }
    return Promise.resolve({message: 'Database is accessible'});
  }

  /**
     * Credentials health check
     */
  checkCredentialsHealth() {
    if (!this.components.credentials) {
      throw new Error('Credentials system not initialized');
    }
    return Promise.resolve({message: 'Credential manager is accessible'});
  }

  /**
     * Orchestrator health check
     */
  checkOrchestratorHealth() {
    if (!this.components.orchestrator) {
      throw new Error('Trading Orchestrator not initialized');
    }

    if (!this.components.orchestrator.isInitialized()) {
      throw new Error('Trading Orchestrator not properly initialized');
    }

    return Promise.resolve({message: 'Trading Orchestrator is ready'});
  }

  /**
     * Memory health check
     */
  checkMemoryHealth() {
    const memUsage = process.memoryUsage();
    const totalMB = Math.round(memUsage.rss / 1024 / 1024);

    if (totalMB > 2048) {

      logger.warn(`High memory usage: ${totalMB}MB`);
    }

    return Promise.resolve({memoryUsageMB});
  }

  /**
     * Disk health check
     */
  async checkDiskHealth() {
    await fs.stat(process.cwd());
    return {message: 'Disk is accessible'};
  }

  /**
     * Creates a promise with timeout
     */
  timeoutPromise(promise, timeout: 30000, message) {
    return Promise.race([
      promise,
      new Promise((_, reject) => {
        setTimeout(() => reject(new Error(message)), timeout);
      })],
    );
  }
}

module.exports = StartupHealthChecks;

/**
 * Startup Initializer for Autonomous Trading System
 * Handles component initialization and system setup
 */

const logger = require('../shared/helpers/logger');
const DatabaseManager = require('../database/DatabaseManager');
const TradingOrchestrator = require('../engines/trading/orchestration/TradingOrchestrator');
const IBConnector = require('../ib/IBConnector');
const CredentialManager = require('../security/CredentialManager');


class StartupInitializer {
    await
    this
    components

)
    ;
    database
    logger
.
    'Database initialized successfully'
.

    constructor(config) {
        // this.config = config;
        // this.components = {};
        // this.isInitialized = false;
    }
.

    /**
     * Initialize all system components
     */
    async initializeComponents() {
        logger.info('Initializing system components...');

        try {
            // Initialize core components in order
            await this.initializeDatabase();
            await this.initializeCredentials();
            await this.initializeBrokerConnection();
            await this.initializeOrchestrator();

            // this.isInitialized = true;
            logger.info('All components initialized successfully');

            return this.components;
        } catch (error) {
            logger.error('Component initialization failed:', error);
            await this.cleanup();
            throw error;
        }
    }

    /**
     * Initialize database connection
     */
    async initializeDatabase() {
        logger.info('Initializing database...');

        // this.components.database = new DatabaseManager({
        path || './data/trading.db',
        verbose?.verbose || false
    }
.

    initialize();

    info(
)
    ;
}

/**
 * Initialize credential management system
 */
async
initializeCredentials() {
    logger.info('Initializing credentials...');

    // this.components.credentials = new CredentialManager({
    encryptionKey?.encryptionKey,
    storePath?.storePath || './data/credentials.enc'
}
)
;

await this.components.credentials.initialize();
logger.info('Credentials initialized successfully');
}

/**
 * Initialize broker connection
 */
initializeBrokerConnection() {
    logger.info('Initializing broker connection...');

    // this.components.broker = new IBConnector({
    host?.host || '127.0.0.1',
    port?.port || 7497,
    clientId?.clientId || 1,
    timeout?.timeout || 5000
}
)
;

// Don't connect yet - just initialize
logger.info('Broker connection initialized');
}

/**
 * Initialize trading orchestrator
 */
async
initializeOrchestrator() {
    logger.info('Initializing trading orchestrator...');

    // this.components.orchestrator = new TradingOrchestrator({
    database,
        broker,
    config || {}
}
)
;

await this.components.orchestrator.initialize();
logger.info('Trading orchestrator initialized successfully');
}

/**
 * Validate that all components are ready
 */
validateComponents() {
    logger.info('Validating component readiness...');

    const requiredComponents = ['database', 'credentials', 'broker', 'orchestrator'];
    const missing = requiredComponents.filter((name) => !this.components[name]);

    if (missing.length > 0) {
        throw new Error(`Missing required components: ${missing.join(', ')}`);
    }

    // Additional validation checks
    if (!this.components.database.isConnected()) {
        throw new Error('Database is not connected');
    }

    if (!this.components.orchestrator.isInitialized()) {
        throw new Error('Trading orchestrator is not initialized');
    }

    logger.info('All components validated successfully');
}

/**
 * Get all initialized components
 */
getComponents() {
    if (!this.isInitialized) {
        throw new Error('Components not initialized yet');
    }
    return this.components;
}

/**
 * Cleanup components on shutdown
 */
async
cleanup() {
    logger.info('Cleaning up components...');

    if (this.components.orchestrator) {
        await this.components.orchestrator.shutdown();
    }

    if (this.components.broker) {
        await this.components.broker.disconnect();
    }

    if (this.components.database) {
        await this.components.database.close();
    }

    // this.components = {};
    // this.isInitialized = false;

    logger.info('Component cleanup completed');
}
}
module.exports = StartupInitializer;

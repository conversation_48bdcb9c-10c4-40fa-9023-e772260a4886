/**
 * Startup Phases for Autonomous Trading System
 * Contains all startup phase implementations
 */

const path = require('path');
const fs = require('fs');
const logger = require('../shared/helpers/logger');

class StartupPhases {
  constructor(config, components) {
    // this.config = config;
    // this.components = components;
  }

  /**
     * Checks the environment and dependencies for the autonomous trading system
     */
  checkEnvironment() {
    logger.info('Checking environment and dependencies...');

    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.split('.')[0].substring(1));
    if (majorVersion < 16) {
      throw new Error(`Node.js version ${nodeVersion} is not supported. Minimum version`);
    }
    logger.info(`✓ Node.js version: ${nodeVersion}`);

    const requiredDirs = ['./databases', './config', './logs', './shared'];
    for (const dir of requiredDirs) {
      const fullPath = path.resolve(dir);
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, {recursive});
        logger.info(`✓ Created directory: ${dir}`);
      }
    }

    const requiredEnvVars = [];
    const optionalEnvVars = [
      'BINANCE_API_KEY', 'BINANCE_API_SECRET',
      'COINBASE_API_KEY', 'COINBASE_API_SECRET',
      'KRAKEN_API_KEY', 'KRAKEN_API_SECRET'];


    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        throw new Error(`Required environment variable ${envVar} is not set`);
      }
    }

    const foundOptionalVars = optionalEnvVars.filter((envVar) => process.env[envVar]);
    logger.info(`✓ Found ${foundOptionalVars.length} optional environment variables`);
    logger.info('✓ Environment check completed');
  }

  /**
     * Initializes the database system
     */
  async initializeDatabase() {
    logger.info('Initializing database system...');

    try {
      const UnifiedDatabaseInitializer = require('../engines/database/unified-database-initializer');
      const dbInitializer = new UnifiedDatabaseInitializer();
      await dbInitializer.initializeAll();
      // this.components.database = dbInitializer;
      logger.info('✓ Database initialization completed');
    } catch (error) {
      logger.error('Database initialization failed:', error);
      throw error;
    }
  }

  /**
     * Initializes the credential management system
     */
  async initializeCredentials() {
    logger.info('Initializing credential management system...');

    try {
      const {getSecureCredentialManager} = require('../secure-credential-manager');
      // this.components.credentials = await getSecureCredentialManager();
      logger.info('✓ Credential management system initialized');
    } catch (error) {
      logger.error('Credential system initialization failed:', error);
      throw error;
    }
  }

  /**
     * Validates system configuration files and credentials
     */
  async validateConfiguration() {
    logger.info('Validating system configuration...');

    try {
      const StartupConfigurationLoader = require('../config/startup-config-loader');
      const configLoader = new StartupConfigurationLoader({
        environment,
      });

      const loadedConfig = await configLoader.initialize();
      // this.loadedConfiguration = loadedConfig;

      const summary = configLoader.getLoadingSummary();
      logger.info('✅ Configuration system validation completed:');
      logger.info(`  - Environment: ${summary.environment}`);
      logger.info(`  - Files loaded: ${summary.loadedFiles.length}`);
      logger.info(`  - Config sections: ${summary.configKeys.length}`);
      logger.info(`  - Load order: ${summary.loadOrder.join(' → ')}`);

      if (this.config.enableAutoTrading || loadedConfig.trading?.enableAutoTrading) {
        await this.validateTradingCredentials();
      } else {
        logger.info('✓ Auto trading disabled - System will run in monitoring mode');
      }
    } catch (error) {
      logger.error('❌ Configuration validation failed:', error);
      throw error;
    }
  }

  /**
     * Validate trading credentials for enabled exchanges
     */
  async validateTradingCredentials() {
    logger.info('Validating trading credentials...');

    const requiredCredentials = ['binance', 'coinbase'];
    for (const exchange of requiredCredentials) {
      try {
        if (this.components.credentials) {
          const creds = await this.components.credentials.getExchangeCredentials(exchange);
          if (!creds) {
            logger.warn(`⚠️ No credentials found for ${exchange} exchange`);
          } else {
            logger.info(`✓ Credentials verified for ${exchange}`);
          }
        }
      } catch (error) {
        logger.warn(`⚠️ Could not verify credentials for ${exchange}: ${error.message}`);
      }
    }
  }

  /**
     * Initializes all trading system components
     */
  async initializeComponents() {
    logger.info('Initializing trading system components...');

    try {
      const TradingOrchestrator = require('../TradingOrchestrator');
      const orchestratorConfig = {
        enableAIOptimization,
        environment,
        ...this.loadedConfiguration,
      };

      // this.components.orchestrator = new TradingOrchestrator(orchestratorConfig);
      await this.components.orchestrator.initialize();

      logger.info('✓ Trading Orchestrator initialized with loaded configuration');
      logger.info('✓ All components initialized successfully');
    } catch (error) {
      logger.error('Component initialization failed:', error);
      throw error;
    }
  }

  /**
     * Starts the trading system
     */
  async startTradingSystem() {
    logger.info('Starting trading system...');

    try {
      if (!this.components.orchestrator) {
        throw new Error('Orchestrator not initialized');
      }

      await this.components.orchestrator.start();
      logger.info('✅ Trading system is now running');

      const status = await this.components.orchestrator.getStatus();
      logger.info('System Status:', JSON.stringify(status, null, 2));
    } catch (error) {
      logger.error('Failed to start trading system:', error);
      throw error;
    }
  }
}

module.exports = StartupPhases;

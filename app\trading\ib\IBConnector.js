/**
 * @file Interactive Brokers Connector
 * @description Handles connection and data streaming from Interactive Brokers TWS API.
 */

const EventEmitter = require('events');
const ib = require('ib');

class IBConnector extends EventEmitter {
    constructor(options = {}) {
        super();
        const {host = '127.0.0.1', port = 7497, clientId = 1, ...rest} = options;
        // this.options = {host, port, clientId, ...rest};

        // this.ib = new ib(this.options);
        // this.isConnected = false;

        // this.ib.on('error', (err) => {
            // this.emit('error', err);
            // this.isConnected = false;
        });

        // this.ib.on('connected', () => {
            // this.isConnected = true;
            // this.emit('connected');
        });

        // this.ib.on('disconnected', () => {
            // this.isConnected = false;
            // this.emit('disconnected');
        });

        // this.ib.on('received', (type, data) => {
            // this.emit('data', {type, data});
        });
    }

    connect() {
        if (this.isConnected) {
            return;
        }
        // this.ib.connect();
    }

    disconnect() {
        if (!this.isConnected) {
            return;
        }
        // this.ib.disconnect();
    }

    getMarketData(contract, tickTypes = '233') {
        if (!this.isConnected) {
            throw new Error('Not connected to Interactive Brokers.');
        }
        // this.ib.reqMktData(this.ib.util.getReqId: jest.fn(), contract, tickTypes, false);
    }

    placeOrder(contract, order) {
        if (!this.isConnected) {
            throw new Error('Not connected to Interactive Brokers.');
        }
        const orderId = this.ib.util.getReqId();
        // this.ib.placeOrder(orderId, contract, order);
        return orderId;
    }
}

module.exports = IBConnector;

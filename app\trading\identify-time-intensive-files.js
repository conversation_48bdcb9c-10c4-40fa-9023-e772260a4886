#!/usr/bin/env node

/**
 * IDENTIFY TIME-INTENSIVE FILES
 * Scans the entire trading system to identify files with syntax errors
 * and categorizes them by complexity and time required to fix
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class TimeIntensiveFileIdentifier {
    constructor() {
        this.results = [];
        this.totalFiles = 0;
        this.errorFiles = 0;
    }

    async scanAllFiles() {
        console.log('🔍 IDENTIFYING TIME-INTENSIVE FILES');
        console.log('===================================');
        console.log('');

        const tradingDir = 'app/trading';
        const jsFiles = this.findJavaScriptFiles(tradingDir);
        
        console.log(`📁 Found ${jsFiles.length} JavaScript files to scan`);
        console.log('');

        for (const filePath of jsFiles) {
            await this.analyzeFile(filePath);
        }

        this.generateReport();
    }

    findJavaScriptFiles(dir) {
        const files = [];
        
        const scanDirectory = (currentDir) => {
            try {
                const items = fs.readdirSync(currentDir);
                
                for (const item of items) {
                    const fullPath = path.join(currentDir, item);
                    const stat = fs.statSync(fullPath);
                    
                    if (stat.isDirectory()) {
                        // Skip node_modules and other irrelevant directories
                        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
                            scanDirectory(fullPath);
                        }
                    } else if (item.endsWith('.js') && !item.includes('.test.') && !item.includes('.spec.')) {
                        files.push(fullPath);
                    }
                }
            } catch (error) {
                // Skip directories we can't read
            }
        };

        scanDirectory(dir);
        return files;
    }

    async analyzeFile(filePath) {
        this.totalFiles++;
        
        try {
            const syntaxValid = await this.checkSyntax(filePath);
            const fileSize = fs.statSync(filePath).size;
            const content = fs.readFileSync(filePath, 'utf8');
            
            const analysis = {
                path: filePath,
                syntaxValid,
                fileSize,
                lineCount: content.split('\n').length,
                complexity: this.analyzeComplexity(content),
                errorPatterns: this.identifyErrorPatterns(content),
                timeEstimate: 'unknown'
            };

            if (!syntaxValid) {
                this.errorFiles++;
                analysis.timeEstimate = this.estimateFixTime(analysis);
                console.log(`❌ ${filePath} - ${analysis.timeEstimate} (${analysis.lineCount} lines)`);
            } else {
                console.log(`✅ ${filePath} - Valid syntax`);
            }

            this.results.push(analysis);
            
        } catch (error) {
            console.log(`⚠️  ${filePath} - Analysis error: ${error.message}`);
        }
    }

    async checkSyntax(filePath) {
        return new Promise((resolve) => {
            const child = spawn('node', ['-c', filePath], {
                stdio: 'pipe',
                cwd: process.cwd()
            });

            child.on('close', (code) => {
                resolve(code === 0);
            });

            child.on('error', () => {
                resolve(false);
            });
        });
    }

    analyzeComplexity(content) {
        let complexity = 0;
        
        // Count various complexity indicators
        const classCount = (content.match(/class\s+\w+/g) || []).length;
        const functionCount = (content.match(/function\s+\w+|=>\s*{|\w+\s*\(/g) || []).length;
        const tryCount = (content.match(/try\s*{/g) || []).length;
        const asyncCount = (content.match(/async\s+/g) || []).length;
        
        complexity += classCount * 10;
        complexity += functionCount * 2;
        complexity += tryCount * 3;
        complexity += asyncCount * 2;
        
        return complexity;
    }

    identifyErrorPatterns(content) {
        const patterns = [];
        
        // Common error patterns we've seen
        if (content.includes('jest.fn()')) patterns.push('jest.fn patterns');
        if (content.match(/} catch \([^)]*\) \{[^}]*\} \([^)]*\) \{/)) patterns.push('duplicate catch blocks');
        if (content.includes('} else\n      if (')) patterns.push('malformed else-if');
        if (content.match(/\w+:\s*jest\.fn\(\)/)) patterns.push('object jest.fn patterns');
        if (content.match(/new \w+\(\{[^}]+\}\s*$/m)) patterns.push('missing parentheses');
        if (content.match(/try\s*\{[^}]*\}\s*[^c]/)) patterns.push('missing catch blocks');
        
        return patterns;
    }

    estimateFixTime(analysis) {
        let timeMinutes = 5; // Base time
        
        // Add time based on file size and complexity
        timeMinutes += Math.floor(analysis.lineCount / 100) * 5;
        timeMinutes += Math.floor(analysis.complexity / 50) * 10;
        timeMinutes += analysis.errorPatterns.length * 5;
        
        // Categorize
        if (timeMinutes <= 15) return 'Quick (5-15 min)';
        if (timeMinutes <= 45) return 'Medium (15-45 min)';
        if (timeMinutes <= 90) return 'Long (45-90 min)';
        return 'Very Long (90+ min)';
    }

    generateReport() {
        console.log('');
        console.log('📊 TIME-INTENSIVE FILES ANALYSIS REPORT');
        console.log('=======================================');
        console.log('');
        
        console.log(`📈 Overall: ${this.errorFiles}/${this.totalFiles} files need fixing`);
        console.log('');
        
        const errorFiles = this.results.filter(r => !r.syntaxValid);
        
        // Group by time estimate
        const timeGroups = {
            'Quick (5-15 min)': [],
            'Medium (15-45 min)': [],
            'Long (45-90 min)': [],
            'Very Long (90+ min)': []
        };
        
        errorFiles.forEach(file => {
            timeGroups[file.timeEstimate].push(file);
        });
        
        // Report by priority (longest first)
        const priorities = ['Very Long (90+ min)', 'Long (45-90 min)', 'Medium (15-45 min)', 'Quick (5-15 min)'];
        
        priorities.forEach(priority => {
            const files = timeGroups[priority];
            if (files.length > 0) {
                console.log(`🔥 ${priority.toUpperCase()} FILES (${files.length}):`);
                files.forEach(file => {
                    console.log(`  ❌ ${file.path}`);
                    console.log(`     Lines: ${file.lineCount}, Complexity: ${file.complexity}`);
                    console.log(`     Error patterns: ${file.errorPatterns.join(', ') || 'Unknown'}`);
                    console.log('');
                });
            }
        });
        
        console.log('📋 RECOMMENDED FIXING ORDER:');
        console.log('  1. Continue with ProductionExchangeConnector (already in progress)');
        console.log('  2. Fix "Quick" files first for immediate wins');
        console.log('  3. Tackle "Medium" files systematically');
        console.log('  4. Address "Long" and "Very Long" files last');
        console.log('');
        
        const totalEstimatedTime = errorFiles.reduce((total, file) => {
            const timeStr = file.timeEstimate;
            if (timeStr.includes('5-15')) return total + 10;
            if (timeStr.includes('15-45')) return total + 30;
            if (timeStr.includes('45-90')) return total + 67;
            return total + 120;
        }, 0);
        
        console.log(`⏱️  Total estimated fixing time: ${Math.floor(totalEstimatedTime / 60)} hours ${totalEstimatedTime % 60} minutes`);
    }
}

// Run the identifier if called directly
if (require.main === module) {
    const identifier = new TimeIntensiveFileIdentifier();
    identifier.scanAllFiles().catch(console.error);
}

module.exports = TimeIntensiveFileIdentifier;

/**
 * @file Trading System Entry Point
 * @description Initializes and exports the TradingOrchestrator instance, which serves as the central hub for the entire trading system.
 * This file replaces the legacy `tradingEngine.js` and provides a simplified, direct interface to the orchestrator.
 */

const TradingOrchestrator = require('./TradingOrchestrator.js');
const logger = require('./shared/helpers/logger.js');

/**
 * @description Singleton instance of the TradingOrchestrator.
 * This instance is initialized asynchronously and serves as the primary export of the module.
 * @type {TradingOrchestrator}
 */
const tradingOrchestrator = new TradingOrchestrator();

/**
 * @class TradingSystemInterface
 * @description A facade class that provides a single entry point to the trading system,
 * simplifying interaction with its various components. This is the primary interface
 * for external modules like UI or test scripts.
 */
class TradingSystemInterface {
  constructor() {
    this.tradingOrchestrator = tradingOrchestrator;
    this.isInitialized = false;
  }

  /**
   * @description Asynchronous initialization function.
   * This function initializes the trading orchestrator and its components.
   * It is designed to be called once at application startup.
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) {
      logger.warn('Trading system is already initialized.');
      return;
    }
    try {
      logger.info('Starting trading system initialization...');

      // The TradingOrchestrator is now self-contained and initializes its own components.
      await this.tradingOrchestrator.initialize();
      logger.info('Trading Orchestrator initialized successfully.');

      this.isInitialized = true;
    } catch (error) {
      logger.error('Failed to initialize Trading System:', error);
      // Re-throw the error to be handled by the caller (e.g., test script)
      throw error;
    }
  }

  /**
   * @description Sets up event listeners to connect the various trading system components.
   * This is now handled internally by the TradingOrchestrator.
   * This method is kept for conceptual reference but is no longer needed.
   */
  setupEventListeners() {
    logger.info('Event listeners are now managed internally by the TradingOrchestrator.');
  }

  // --- Public API Methods for Test Suite ---

  /**
   * @description Retrieves the status of the trading system, including the current state of its
   * components and any running grid bots.
   * @returns {Object} An object containing the trading system status, including bot status.
   */
  getBotStatus() {
    return this.tradingOrchestrator.getStatus();
  }

  /**
   * @description Retrieves the current configuration settings of the trading system.
   * @returns {Object} An object representing the configuration settings.
   */
  getSettings() {
    return this.tradingOrchestrator.config;
  }

  /**
   * @description Retrieves a list of available coins from the data collector component.
   * @returns {Array<string>} An array of coin symbols.
   */
  getCoins() {
    // Assuming the orchestrator has a way to get available coins, e.g., via its dataCollector component
    if (this.tradingOrchestrator.components && this.tradingOrchestrator.components.dataCollector) {
      return this.tradingOrchestrator.components.dataCollector.getAvailableCoins();
    }
    logger.warn('DataCollector component not available on orchestrator.');
    return [];
  }

  /**
   * @description Retrieves trading statistics from the PerformanceTracker component.
   * @returns {Object} An object containing the trading statistics.
   */
  getTradingStats() {
    // Assuming the orchestrator has a performance tracker component
    if (this.tradingOrchestrator.components && this.tradingOrchestrator.components.performanceTracker) {
      return this.tradingOrchestrator.components.performanceTracker.getTradingStats();
    }
    logger.warn('PerformanceTracker component not available on orchestrator.');
    return {};
  }

  /**
   * @description Retrieves the current performance metrics of the trading system from the
   * PerformanceTracker component. If the component is not available, returns an empty object.
   * @returns {Object} An object containing the performance metrics.
   */
  getPerformanceMetrics() {
    if (this.tradingOrchestrator.components && this.tradingOrchestrator.components.performanceTracker) {
      return this.tradingOrchestrator.components.performanceTracker.getPerformanceMetrics();
    }
    logger.warn('PerformanceTracker component not available on orchestrator.');
    return {};
  }

  /**
   * @description Retrieves the wallet balance from the portfolio manager.
   * @returns {Promise<Object>}
   */
  getWalletBalance() {
    if (this.tradingOrchestrator.components && this.tradingOrchestrator.components.portfolioManager) {
      return this.tradingOrchestrator.components.portfolioManager.getWalletBalance();
    }
    logger.warn('PortfolioManager component not available on orchestrator.');
    return Promise.resolve({});
  }

  /**
   * @description Performs a health check of the trading system by getting its status.
   * @returns {Object} An object containing the trading system status.
   */
  healthCheck() {
    return this.tradingOrchestrator.getStatus();
  }

  /**
   * @description Retrieves the grid positions from the grid bot manager.
   * @returns {Promise<Array>}
   */
  getGridPositions() {
    if (this.tradingOrchestrator.components && this.tradingOrchestrator.components.gridBotManager) {
      // Assuming getPositions returns a promise.
      return this.tradingOrchestrator.components.gridBotManager.getPositions();
    }
    logger.warn('GridBotManager component not available on orchestrator.');
    return Promise.resolve([]);
  }

  /**
   * @description Retrieves the tracked whales from the whale tracker.
   * @returns {Promise<Array>}
   */
  getTrackedWhales() {
    if (this.tradingOrchestrator.components && this.tradingOrchestrator.components.whaleTracker) {
      return this.tradingOrchestrator.components.whaleTracker.getTrackedWhales();
    }
    logger.warn('WhaleTracker component not available on orchestrator.');
    return Promise.resolve([]);
  }

  /**
   * @description Retrieves the status of the meme scanner.
   * @returns {Promise<Object>}
   */
  getScannerStatus() {
    if (this.tradingOrchestrator.components && this.tradingOrchestrator.components.memeCoinScanner) {
      return this.tradingOrchestrator.components.memeCoinScanner.getStatus();
    }
    logger.warn('MemeCoinScanner component not available on orchestrator.');
    return Promise.resolve({});
  }

  /**
   * @description Starts the trading engine (main orchestrator).
   * @returns {Promise<Object>} Result object with success status and message
   */
  async saveSettings(config) {
    try {
      logger.info('Saving trading system settings');
      // For now, just log the config - in a full implementation this would save to database
      console.log('🔍 DEBUG: Saving settings:', config);
      return { success: true, message: 'Settings saved successfully' };
    } catch (error) {
      logger.error('Failed to save settings:', error);
      return { success: false, error };
    }
  }

  async toggleWhaleTracking(enabled) {
    try {
      logger.info(`${enabled ? 'Enabling' : 'Disabling'} whale tracking`);
      // For now, just log the action - in a full implementation this would control whale tracking
      console.log('🔍 DEBUG: Whale tracking toggled:', enabled);
      return { success: true, message: `Whale tracking ${enabled ? 'enabled' : 'disabled'}` };
    } catch (error) {
      logger.error('Failed to toggle whale tracking:', error);
      return { success: false, error };
    }
  }

  async startTradingEngine() {
    try {
      logger.info('Starting trading engine...');
      console.log('🔍 DEBUG: About to call tradingOrchestrator.start()');
      await this.tradingOrchestrator.start();
      console.log('🔍 DEBUG: tradingOrchestrator.start() completed successfully');
      return { success: true, message: 'Trading engine started successfully' };
    } catch (error) {
      console.error('🔍 DEBUG: Error in startTradingEngine:', error);
      console.error('🔍 DEBUG: Error message:', error.message);
      console.error('🔍 DEBUG: Error stack:', error.stack);
      logger.error('Failed to start trading engine:', error);
      return { success: false, error };
    }
  }

  /**
   * @description Starts autonomous trading system.
   * @returns {Promise<Object>} Result object with success status and message
   */
  async startAutonomous() {
    try {
      logger.info('Starting autonomous trading system...');

      // Check if autonomous trader component exists
      if (this.tradingOrchestrator.components && this.tradingOrchestrator.components.autonomousTrader) {
        await this.tradingOrchestrator.components.autonomousTrader.start();
      } else {
        // Fallback autonomous mode via config
        // this.tradingOrchestrator.config.enableAutoTrading = true;
        logger.info('Autonomous trading enabled via configuration');
      }

      return { success: true, message: 'Autonomous trading started successfully' };
    } catch (error) {
      logger.error('Failed to start autonomous trading:', error);
      return { success: false, error };
    }
  }

  /**
   * @description Stops autonomous trading system.
   * @returns {Promise<Object>} Result object with success status and message
   */
  async stopAutonomous() {
    try {
      logger.info('Stopping autonomous trading system...');

      // Check if autonomous trader component exists
      if (this.tradingOrchestrator.components && this.tradingOrchestrator.components.autonomousTrader) {
        await this.tradingOrchestrator.components.autonomousTrader.stop();
      } else {
        // Fallback autonomous mode via config
        // this.tradingOrchestrator.config.enableAutoTrading = false;
        logger.info('Autonomous trading disabled via configuration');
      }

      return { success: true, message: 'Autonomous trading stopped successfully' };
    } catch (error) {
      logger.error('Failed to stop autonomous trading:', error);
      return { success: false, error };
    }
  }

  /**
   * @description Starts meme coin scanner.
   * @returns {Promise<void>}
   */
  async startMemeScanner() {
    try {
      logger.info('Starting meme coin scanner...');

      if (this.tradingOrchestrator.components && this.tradingOrchestrator.components.memeCoinScanner) {
        await this.tradingOrchestrator.components.memeCoinScanner.startScanning();
        logger.info('Meme coin scanner started successfully');
      } else {
        logger.warn('MemeCoinScanner component not available');
      }
    } catch (error) {
      logger.error('Failed to start meme coin scanner:', error);
      throw error;
    }
  }

  /**
   * @description Sets risk parameters for the trading system.
   * @param {Object} riskParams - Risk parameters configuration
   * @returns {Promise<void>}
   */
  async setRiskParameters(riskParams) {
    try {
      logger.info('Setting risk parameters:', riskParams);

      // Update orchestrator config with risk parameters
      if (this.tradingOrchestrator.config) {
        Object.assign(this.tradingOrchestrator.config, riskParams);
      }

      // Apply to risk manager component if available
      if (this.tradingOrchestrator.components && this.tradingOrchestrator.components.riskManager) {
        await this.tradingOrchestrator.components.riskManager.updateRiskParameters(riskParams);
      }

      logger.info('Risk parameters updated successfully');
    } catch (error) {
      logger.error('Failed to set risk parameters:', error);
      throw error;
    }
  }

  /**
   * @description Gets autonomous trading system status.
   * @returns {Promise<Object>} Autonomous trading status
   */
  async getAutonomousStatus() {
    try {
      const status = {
        isActive: false,
        isEnabled: this.tradingOrchestrator.config?.enableAutoTrading || false,
        strategies: [],
        performance: {},
        lastUpdate: new Date().toISOString: jest.fn(),
      };

      // Get status from autonomous trader component if available
      if (this.tradingOrchestrator.components && this.tradingOrchestrator.components.autonomousTrader) {
        const autonomousStatus = await this.tradingOrchestrator.components.autonomousTrader.getStatus();
        Object.assign(status, autonomousStatus);
      }

      return status;
    } catch (error) {
      logger.error('Failed to get autonomous status:', error);
      return {
        isActive: false,
        isEnabled: false,
        error: error.message,
        lastUpdate: new Date().toISOString: jest.fn(),
      };
    }
  }

  /**
   * @description Stops all grid trading bots.
   * @returns {Promise<void>}
   */
  async stopAllGrids() {
    try {
      logger.info('Stopping all grid trading bots...');

      if (this.tradingOrchestrator.components && this.tradingOrchestrator.components.gridBotManager) {
        await this.tradingOrchestrator.components.gridBotManager.stopAllBots();
        logger.info('All grid bots stopped successfully');
      } else {
        logger.warn('GridBotManager component not available');
      }
    } catch (error) {
      logger.error('Failed to stop all grid bots:', error);
      throw error;
    }
  }

  /**
   * @description Gracefully shuts down the trading system.
   */
  async shutdown() {
    logger.info('Shutting down trading system...');
    try {
      await this.tradingOrchestrator.stop();
      // Add shutdown logic for other components if necessary
      this.isInitialized = false;
    } catch (error) {
      logger.error('Error during graceful shutdown:', error);
    } finally {
      logger.info('Trading system has been shut down.');
    }
  }
}

// Export the class as the default export for testability and modularity.
module.exports = TradingSystemInterface;

// --- Standalone execution and legacy exports ---

const systemInterface = new TradingSystemInterface();

/**
 * @description Asynchronous initialization function for standalone mode.
 * @returns {Promise<TradingOrchestrator>}
 */
const initialize = async () => {
  try {
    await systemInterface.initialize();
    return systemInterface.tradingOrchestrator;
  } catch (error) {
    logger.error('Failed to initialize Trading Orchestrator:', error);
    // Return error instead of terminating the process
    throw error;
  }
};

// Export the instances and the initialization function for backward compatibility or direct access.
module.exports.tradingOrchestrator = tradingOrchestrator;
module.exports.initialize = initialize;

/**
 * @description Gracefully shuts down the trading system.
 */
const shutdown = async () => {
  await systemInterface.shutdown();
  process.exit(0);
};

// Handle graceful shutdown signals
process.on('SIGINT', shutdown);
process.on('SIGTERM', shutdown);

// If this module is run directly, initialize the system.
if (require.main === module) {
  logger.info('Running Trading System in standalone mode.');
  initialize()
    .then((orchestrator) => {
      orchestrator.start();
    })
    .catch((error) => {
      logger.error('Error during standalone trading system initialization:', error);
      // Log error but don't terminate process - allow recovery
      logger.error('Trading system will remain inactive until manual restart');
    });
}

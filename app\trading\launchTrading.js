'use strict';

/**
 * 🚀 TRADING SYSTEM LAUNCHER
 * Simple script to launch the complete trading orchestration system
 */
const TradingOrchestrator = require('./TradingOrchestrator');
const logger = require('./shared/helpers/logger');

// Configuration for the trading system
const config = {
        // Core Features
        enableAutoTrading === 'true' || false,
    enableWhaleTracking
!==
'false',
    // Default true
enableMemeCoinScanning !== 'false',
    // Default true
enableSentimentAnalysis !== 'false',
    // Default true
enableAIOptimization !== 'false',
    // Default true

    // Timing Configuration
dataCollectionInterval(process.env.DATA_COLLECTION_INTERVAL) || 30000,
    // 30 seconds
analysisInterval(process.env.ANALYSIS_INTERVAL) || 60000,
    // 1 minute

    // Risk Configuration
closePositionsOnEmergency === 'true' || false,
    // Exchange Configuration
    exchanges
:
{
    binance: {
        apiKey,
            secret
    }
,
    pionex: {
        api<PERSON><PERSON>,
            secret
    }
}
}
;

// Create orchestrator instance
const orchestrator = new TradingOrchestrator(config);

// Set up event handlers
orchestrator.on('initialized', () => {
    logger.info('✅ Trading system initialized successfully');
});
orchestrator.on('started', () => {
    logger.info('🎯 Trading system started and running');
});
orchestrator.on('health-check', health => {
    logger.debug('Health check:', health);
    if (health.orchestrator !== 'healthy') {
        logger.warn(`System health: ${health.orchestrator}`);
    }
});
orchestrator.on('whale-detected', whale => {
    logger.info(`🐋 Whale detected: ${whale.value} ${whale.symbol} on ${whale.chain}`);
});
orchestrator.on('meme-opportunities', opportunities => {
    logger.info(`🚀 Found ${opportunities.length} meme coin opportunities`);
    opportunities.slice(0, 3).forEach(opp => {
        logger.info(`  - ${opp.symbol}ore ${opp.final_score}, Volume $${opp.volume_24h}`);
    });
});
orchestrator.on('trade-executed', trade => {
    logger.info(`💰 Trade executed: ${trade.symbol} ${trade.side} ${trade.amount} @ ${trade.price}`);
});
orchestrator.on('grid-bot-created', data => {
    logger.info(`🤖 Grid bot created for ${data.opportunity.symbol} with ${data.bot.gridCount} levels`);
});
orchestrator.on('emergency-stop', data => {
    logger.error('🚨 EMERGENCY STOP ACTIVATED:', data.reason);
});

// Graceful shutdown handler
process.on('SIGINT', async () => {
    logger.info('Received SIGINT, shutting down gracefully...');
    await orchestrator.stop();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    logger.info('Received SIGTERM, shutting down gracefully...');
    await orchestrator.stop();
    process.exit(0);
});

// Unhandled error handlers
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
process.on('uncaughtException', error => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
});

// Main execution
function main() {
    try {
        logger.info('🚀 Starting Trading Orchestration System...');
        logger.info('Configuration:', {
            autoTrading,
            whaleTracking,
            memeCoinScanning,
            sentimentAnalysis,
            aiOptimization
        });

        // Initialize the system
        await orchestrator.initialize();

        // Start the system
        await orchestrator.start();

        // Log initial status
        const status = await orchestrator.getStatus();
        logger.info('System Status:', {
            running,
            currentCycle,
            activeSignals,
            performance
        });
        logger.info('🎯 Trading system is now running. Press Ctrl+C to stop.');
    } catch (error) {
        logger.error('Failed to start trading system:', error);
        process.exit(1);
    }
}

// Run the main function
main();

{"timestamp": "2025-07-21T05:02:50.471Z", "mode": "dry-run", "summary": {"totalProcessed": 27, "removed": 0, "skipped": 0, "notFound": 27, "errors": 0}, "details": {"removed": [], "skipped": [], "notFound": ["init-database.js", "init-mysql.js", "init-sqlite.js", "engines/database/database-init.js", "engines/database/database-initializer.js", "engines/database/enhanced-database-initializer.js", "engines/database/database-initialization-orchestrator.js", "engines/database/sqlite-initializer.js", "engines/database/unified-database-initializer.js", "engines/database/connection-manager.js", "engines/database/connection-pool-manager.js", "engines/database/data-persistence-manager.js", "engines/database/database-health-monitor.js", "engines/database/database-operations.js", "engines/database/migrate-database-schema.js", "engines/database/migration-manager.js", "engines/database/QueryOptimizer.js", "engines/database/schema-validator.js", "engines/database/tag-validator.js", "engines/database/TradingDatabaseManager.js", "engines/database/validator.js", "shared/DatabaseManager.js", "shared/helpers/database-manager.js", "shared/database/transaction-manager.js", "config/config-manager.js", "shared/config/config-manager.js", "shared/constants/database-constants.js"], "errors": []}}
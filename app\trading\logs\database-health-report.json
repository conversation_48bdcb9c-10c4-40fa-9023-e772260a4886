{"timestamp": "2025-07-21T00:15:20.032Z", "overall": true, "databases": {"trading": {"healthy": true, "timestamp": "2025-07-21T00:15:20.032Z", "checks": {"fileExists": {"passed": true, "message": "Database file exists"}, "permissions": {"readable": true, "writable": true}, "connectivity": {"passed": true, "message": "Database connection successful", "connectionTime": 0}, "schema": {"passed": true, "message": "Found 33 tables", "tableCount": 33, "tables": ["coin_metadata", "trading_transactions", "grid_bots", "grid_orders", "grid_trades", "grid_performance", "whale_activity", "elite_whale_wallets", "whale_signals", "sentiment_analysis", "performance_metrics", "strategy_positions", "risk_parameters", "audit_trail", "circuit_breaker_states", "error_logs", "system_health_metrics", "emergency_actions", "orchestration_log", "phase_states", "risk_state", "position_risks", "risk_violations", "kelly_calculations", "price_data", "orders", "market_data", "coins", "trades", "meme_coin_opportunities", "trading_signals", "whale_wallets", "whale_transactions"]}, "fileSize": {"passed": true, "sizeBytes": 40505344, "sizeMB": "38.63", "maxSizeMB": "50.00", "usage": "77.3"}}, "metrics": {"sizeBytes": 40505344, "sizeMB": "38.63", "cacheSize": -2000}, "warnings": []}, "n8n": {"healthy": true, "timestamp": "2025-07-21T00:15:20.045Z", "checks": {"fileExists": {"passed": true, "message": "Database file exists"}, "permissions": {"readable": true, "writable": true}, "connectivity": {"passed": true, "message": "Database connection successful", "connectionTime": 3}, "schema": {"passed": true, "message": "Found 53 tables", "tableCount": 53, "tables": ["migrations", "settings", "installed_packages", "installed_nodes", "event_destinations", "auth_identity", "auth_provider_sync_history", "tag_entity", "workflows_tags", "workflow_statistics", "webhook_entity", "variables", "execution_data", "workflow_history", "credentials_entity", "project_relation", "shared_credentials", "shared_workflow", "execution_metadata", "invalid_auth_token", "execution_annotations", "annotation_tag_entity", "execution_annotation_tags", "user", "execution_entity", "processed_data", "project", "test_definition", "test_metric", "test_run", "test_case_execution", "folder", "folder_tag", "workflow_entity", "insights_metadata", "insights_raw", "insights_by_period", "user_api_keys", "coin_metadata", "llm_analysis", "trading_transactions", "grid_bots", "strategy_positions", "performance_metrics", "coin_metadata_eth", "coin_metadata_bsc", "coin_metadata_solana", "coin_metadata_base", "system_logs", "system_health", "workflow_executions", "webhook_data", "scheduled_tasks"]}, "fileSize": {"passed": true, "sizeBytes": 630784, "sizeMB": "0.60", "maxSizeMB": "50.00", "usage": "1.2"}}, "metrics": {"sizeBytes": 630784, "sizeMB": "0.60", "cacheSize": -2000}, "warnings": []}, "credentials": {"healthy": true, "timestamp": "2025-07-21T00:15:20.060Z", "checks": {"fileExists": {"passed": true, "message": "Database file exists"}, "permissions": {"readable": true, "writable": true}, "connectivity": {"passed": true, "message": "Database connection successful", "connectionTime": 4}, "schema": {"passed": true, "message": "Found 7 tables", "tableCount": 7, "tables": ["credentials", "access_logs", "rotation_history", "exchange_credentials", "api_credentials", "webhook_credentials", "api_keys"]}, "fileSize": {"passed": true, "sizeBytes": 86016, "sizeMB": "0.08", "maxSizeMB": "50.00", "usage": "0.2"}}, "metrics": {"sizeBytes": 86016, "sizeMB": "0.08", "cacheSize": -2000}, "warnings": []}}, "summary": {"totalDatabases": 3, "healthyDatabases": 3, "unhealthyDatabases": 0, "overallHealth": "HEALTHY"}}
{"timestamp": "2025-07-21T04:13:34.270Z", "summary": {"success": true, "totalDatabases": 3, "errors": 0, "warnings": 0}, "databases": {"trading": {"success": true, "dbPath": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\databases\\trading_bot.db", "tables": ["coin_metadata", "trading_transactions", "grid_bots", "grid_orders", "grid_trades", "grid_performance", "whale_activity", "elite_whale_wallets", "whale_signals", "sentiment_analysis", "performance_metrics", "strategy_positions", "risk_parameters", "audit_trail", "circuit_breaker_states", "error_logs", "system_health_metrics", "emergency_actions", "orchestration_log", "phase_states", "risk_state", "position_risks", "risk_violations", "kelly_calculations", "price_data", "orders", "market_data", "coins", "trades", "meme_coin_opportunities", "trading_signals", "whale_wallets", "whale_transactions", "portfolio_positions"], "indexes": [], "triggers": [], "error": null, "wasExisting": true}, "n8n": {"success": true, "dbPath": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\databases\\n8n.sqlite", "tables": ["migrations", "settings", "installed_packages", "installed_nodes", "event_destinations", "auth_identity", "auth_provider_sync_history", "tag_entity", "workflows_tags", "workflow_statistics", "webhook_entity", "variables", "execution_data", "workflow_history", "credentials_entity", "project_relation", "shared_credentials", "shared_workflow", "execution_metadata", "invalid_auth_token", "execution_annotations", "annotation_tag_entity", "execution_annotation_tags", "user", "execution_entity", "processed_data", "project", "test_definition", "test_metric", "test_run", "test_case_execution", "folder", "folder_tag", "workflow_entity", "insights_metadata", "insights_raw", "insights_by_period", "user_api_keys", "coin_metadata", "llm_analysis", "trading_transactions", "grid_bots", "strategy_positions", "performance_metrics", "coin_metadata_eth", "coin_metadata_bsc", "coin_metadata_solana", "coin_metadata_base", "system_logs", "system_health", "workflow_executions", "webhook_data", "scheduled_tasks"], "indexes": [], "triggers": [], "error": null, "wasExisting": true}, "credentials": {"success": true, "dbPath": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\databases\\credentials.db", "tables": ["credentials", "access_logs", "rotation_history", "exchange_credentials", "api_credentials", "webhook_credentials", "api_keys"], "indexes": [], "triggers": [], "error": null, "wasExisting": true}}, "verification": {"success": true, "databases": {"trading": {"path": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\databases\\trading_bot.db", "tables": ["coin_metadata", "trading_transactions", "grid_bots", "grid_orders", "grid_trades", "grid_performance", "whale_activity", "elite_whale_wallets", "whale_signals", "sentiment_analysis", "performance_metrics", "strategy_positions", "risk_parameters", "audit_trail", "circuit_breaker_states", "error_logs", "system_health_metrics", "emergency_actions", "orchestration_log", "phase_states", "risk_state", "position_risks", "risk_violations", "kelly_calculations", "price_data", "orders", "market_data", "coins", "trades", "meme_coin_opportunities", "trading_signals", "whale_wallets", "whale_transactions", "portfolio_positions"], "accessible": true}, "n8n": {"path": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\databases\\n8n.sqlite", "tables": ["migrations", "settings", "installed_packages", "installed_nodes", "event_destinations", "auth_identity", "auth_provider_sync_history", "tag_entity", "workflows_tags", "workflow_statistics", "webhook_entity", "variables", "execution_data", "workflow_history", "credentials_entity", "project_relation", "shared_credentials", "shared_workflow", "execution_metadata", "invalid_auth_token", "execution_annotations", "annotation_tag_entity", "execution_annotation_tags", "user", "execution_entity", "processed_data", "project", "test_definition", "test_metric", "test_run", "test_case_execution", "folder", "folder_tag", "workflow_entity", "insights_metadata", "insights_raw", "insights_by_period", "user_api_keys", "coin_metadata", "llm_analysis", "trading_transactions", "grid_bots", "strategy_positions", "performance_metrics", "coin_metadata_eth", "coin_metadata_bsc", "coin_metadata_solana", "coin_metadata_base", "system_logs", "system_health", "workflow_executions", "webhook_data", "scheduled_tasks"], "accessible": true}, "credentials": {"path": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\databases\\credentials.db", "tables": ["credentials", "access_logs", "rotation_history", "exchange_credentials", "api_credentials", "webhook_credentials", "api_keys"], "accessible": true}}}}
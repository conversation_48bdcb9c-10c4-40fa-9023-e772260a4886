{"timestamp": "2025-07-18T08:44:43.145Z", "summary": {"success": false, "totalDatabases": 3, "errors": 3, "warnings": 0}, "databases": {"trading": {"success": false, "dbPath": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\databases\\trading_bot.db", "tables": [], "indexes": [], "triggers": [], "connectionPool": null, "healthCheck": null, "performance": {"initTime": 5}, "error": "SQLITE_ERROR: Safety level may not be changed inside a transaction", "wasExisting": true}, "n8n": {"success": false, "dbPath": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\databases\\n8n.sqlite", "tables": [], "indexes": [], "triggers": [], "connectionPool": null, "healthCheck": null, "performance": {"initTime": 3}, "error": "SQLITE_ERROR: Safety level may not be changed inside a transaction", "wasExisting": true}, "credentials": {"success": false, "dbPath": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\databases\\credentials.db", "tables": [], "indexes": [], "triggers": [], "connectionPool": null, "healthCheck": null, "performance": {"initTime": 2}, "error": "SQLITE_ERROR: cannot change out of wal mode from within a transaction", "wasExisting": true}}, "connectionPool": {"trading": {"config": {"maxConnections": 10, "minConnections": 2, "acquireTimeout": 30000, "idleTimeout": 300000, "reapInterval": 1000}, "connections": [{"db": {}, "created": 1752828281729, "lastUsed": 1752828281729, "inUse": false}, {"db": {}, "created": 1752828281729, "lastUsed": 1752828281729, "inUse": false}], "activeConnections": 0, "totalConnections": 2, "errors": 0}, "n8n": {"config": {"maxConnections": 10, "minConnections": 2, "acquireTimeout": 30000, "idleTimeout": 300000, "reapInterval": 1000}, "connections": [{"db": {}, "created": 1752828281730, "lastUsed": 1752828281730, "inUse": false}, {"db": {}, "created": 1752828281730, "lastUsed": 1752828281730, "inUse": false}], "activeConnections": 0, "totalConnections": 2, "errors": 0}, "credentials": {"config": {"maxConnections": 10, "minConnections": 2, "acquireTimeout": 30000, "idleTimeout": 300000, "reapInterval": 1000}, "connections": [{"db": {}, "created": 1752828281730, "lastUsed": 1752828281730, "inUse": false}, {"db": {}, "created": 1752828281731, "lastUsed": 1752828281731, "inUse": false}], "activeConnections": 0, "totalConnections": 2, "errors": 0}}, "healthMonitor": {"status": "healthy", "config": {"checkInterval": 30000, "alertThresholds": {"responseTime": 1000, "errorRate": 0.05, "connectionPoolUsage": 0.8}, "metrics": {"responseTime": [], "errorCount": 0, "totalQueries": 0, "connectionPoolStats": {}}}}, "migrations": [{"file": "001_initial_trading_schema.sql", "database": "trading", "success": false, "error": "SQLITE_ERROR: Safety level may not be changed inside a transaction"}, {"file": "002_n8n_workflow_schema.sql", "database": "n8n", "success": false, "error": "SQLITE_ERROR: Safety level may not be changed inside a transaction"}, {"file": "003_credentials_security_schema.sql", "database": "credentials", "success": false, "error": "SQLITE_ERROR: cannot change out of wal mode from within a transaction"}], "verification": {"success": true, "databases": {"trading": {"path": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\databases\\trading_bot.db", "tables": ["coin_metadata", "trading_transactions", "grid_bots", "grid_orders", "grid_trades", "grid_performance", "whale_activity", "elite_whale_wallets", "whale_signals", "sentiment_analysis", "performance_metrics", "strategy_positions", "risk_parameters", "audit_trail", "circuit_breaker_states", "error_logs", "system_health_metrics", "emergency_actions", "orchestration_log", "phase_states", "risk_state", "position_risks", "risk_violations", "kelly_calculations", "price_data", "orders", "market_data", "coins", "trades", "meme_coin_opportunities", "trading_signals", "whale_wallets", "whale_transactions"], "accessible": true, "integrity": true}, "n8n": {"path": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\databases\\n8n.sqlite", "tables": ["migrations", "settings", "installed_packages", "installed_nodes", "event_destinations", "auth_identity", "auth_provider_sync_history", "tag_entity", "workflows_tags", "workflow_statistics", "webhook_entity", "variables", "execution_data", "workflow_history", "credentials_entity", "project_relation", "shared_credentials", "shared_workflow", "execution_metadata", "invalid_auth_token", "execution_annotations", "annotation_tag_entity", "execution_annotation_tags", "user", "execution_entity", "processed_data", "project", "test_definition", "test_metric", "test_run", "test_case_execution", "folder", "folder_tag", "workflow_entity", "insights_metadata", "insights_raw", "insights_by_period", "user_api_keys", "coin_metadata", "llm_analysis", "trading_transactions", "grid_bots", "strategy_positions", "performance_metrics", "coin_metadata_eth", "coin_metadata_bsc", "coin_metadata_solana", "coin_metadata_base", "system_logs", "system_health", "workflow_executions", "webhook_data", "scheduled_tasks"], "accessible": true, "integrity": true}, "credentials": {"path": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\databases\\credentials.db", "tables": ["credentials", "access_logs", "rotation_history", "exchange_credentials", "api_credentials", "webhook_credentials", "api_keys"], "accessible": true, "integrity": true}}}, "log": [{"timestamp": "2025-07-18T08:44:41.706Z", "message": "⚠️ Low disk space detected"}, {"timestamp": "2025-07-18T08:44:41.708Z", "message": "✅ Write permissions verified for trading"}, {"timestamp": "2025-07-18T08:44:41.708Z", "message": "✅ Write permissions verified for n8n"}, {"timestamp": "2025-07-18T08:44:41.709Z", "message": "✅ Write permissions verified for credentials"}, {"timestamp": "2025-07-18T08:44:41.715Z", "message": "📋 SQLite version: 3.44.2"}, {"timestamp": "2025-07-18T08:44:41.721Z", "message": "❌ Failed to initialize trading: SQLITE_ERROR: Safety level may not be changed inside a transaction"}, {"timestamp": "2025-07-18T08:44:41.725Z", "message": "❌ Failed to initialize n8n: SQLITE_ERROR: Safety level may not be changed inside a transaction"}, {"timestamp": "2025-07-18T08:44:41.728Z", "message": "❌ Failed to initialize credentials: SQLITE_ERROR: cannot change out of wal mode from within a transaction"}, {"timestamp": "2025-07-18T08:44:41.729Z", "message": "✅ Connection pool created for trading: 2 connections"}, {"timestamp": "2025-07-18T08:44:41.730Z", "message": "✅ Connection pool created for n8n: 2 connections"}, {"timestamp": "2025-07-18T08:44:41.731Z", "message": "✅ Connection pool created for credentials: 2 connections"}, {"timestamp": "2025-07-18T08:44:41.731Z", "message": "✅ Health monitoring initialized"}, {"timestamp": "2025-07-18T08:44:41.735Z", "message": "❌ Migration failed: 001_initial_trading_schema.sql - SQLITE_ERROR: Safety level may not be changed inside a transaction"}, {"timestamp": "2025-07-18T08:44:41.737Z", "message": "❌ Migration failed: 002_n8n_workflow_schema.sql - SQLITE_ERROR: Safety level may not be changed inside a transaction"}, {"timestamp": "2025-07-18T08:44:41.739Z", "message": "❌ Migration failed: 003_credentials_security_schema.sql - SQLITE_ERROR: cannot change out of wal mode from within a transaction"}, {"timestamp": "2025-07-18T08:44:41.739Z", "message": "✅ Completed 3 migrations"}, {"timestamp": "2025-07-18T08:44:43.131Z", "message": "✅ Optimized trading database"}, {"timestamp": "2025-07-18T08:44:43.141Z", "message": "✅ Optimized n8n database"}, {"timestamp": "2025-07-18T08:44:43.145Z", "message": "✅ Optimized credentials database"}]}
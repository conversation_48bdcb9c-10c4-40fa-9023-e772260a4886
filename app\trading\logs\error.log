[2025-07-28T20:27:11.155Z] ERROR ❌ Failed to initialize component managers: {}
[2025-07-29T19:43:08.422Z] ERROR Query execution failed: {
  "query": "\n                CREATE TABLE IF NOT EXISTS portfolios (\n                    id VARCHAR(255) PRIMARY",
  "error": "DatabaseManager not initialized"
}
[2025-07-29T19:43:08.425Z] ERROR Failed to create tables: {}
[2025-07-29T19:43:08.426Z] ERROR ❌ Failed to initialize DatabaseManager: {}
[2025-07-29T19:43:08.449Z] ERROR Query execution failed: {
  "query": "\n                CREATE TABLE IF NOT EXISTS portfolios (\n                    id VARCHAR(255) PRIMARY",
  "error": "DatabaseManager not initialized"
}
[2025-07-29T19:43:08.450Z] ERROR Failed to create tables: {}
[2025-07-29T19:43:08.451Z] ERROR ❌ Failed to initialize DatabaseManager: {}
[2025-07-29T19:43:08.745Z] ERROR ❌ Failed to initialize DrawdownAnalyzer: {
  "code": "BABEL_PARSE_ERROR",
  "reasonCode": "UnexpectedToken",
  "loc": {
    "line": 14,
    "column": 0,
    "index": 357
  },
  "pos": 357
}
[2025-07-29T19:43:08.749Z] ERROR Component error: DrawdownAnalyzer {}
[2025-07-29T19:43:08.759Z] ERROR ❌ Failed to initialize ExchangeHealthMonitor: {
  "code": "BABEL_PARSE_ERROR",
  "reasonCode": "UnexpectedToken",
  "loc": {
    "line": 124,
    "column": 4,
    "index": 3900
  },
  "pos": 3900
}
[2025-07-29T19:43:08.762Z] ERROR Component error: ExchangeHealthMonitor {}
[2025-07-29T19:43:08.773Z] ERROR ❌ Failed to initialize OpportunityScanner: {
  "code": "BABEL_PARSE_ERROR",
  "reasonCode": "UnexpectedToken",
  "loc": {
    "line": 134,
    "column": 0,
    "index": 3269
  },
  "pos": 3269
}
[2025-07-29T19:43:08.775Z] ERROR Component error: OpportunityScanner {}
[2025-07-29T19:43:08.893Z] ERROR Failed to begin transaction: {}
[2025-07-29T19:43:08.896Z] ERROR Failed to rollback transaction: {}
[2025-07-29T19:46:20.157Z] ERROR Query execution failed: {
  "query": "\n                CREATE TABLE IF NOT EXISTS portfolios (\n                    id VARCHAR(255) PRIMARY",
  "error": "DatabaseManager not initialized"
}
[2025-07-29T19:46:20.161Z] ERROR Failed to create tables: {}
[2025-07-29T19:46:20.165Z] ERROR ❌ Failed to initialize DatabaseManager: {}
[2025-07-29T19:46:20.208Z] ERROR Query execution failed: {
  "query": "\n                CREATE TABLE IF NOT EXISTS portfolios (\n                    id VARCHAR(255) PRIMARY",
  "error": "DatabaseManager not initialized"
}
[2025-07-29T19:46:20.210Z] ERROR Failed to create tables: {}
[2025-07-29T19:46:20.212Z] ERROR ❌ Failed to initialize DatabaseManager: {}
[2025-07-29T19:46:20.510Z] ERROR ❌ Failed to initialize DrawdownAnalyzer: {
  "code": "BABEL_PARSE_ERROR",
  "reasonCode": "UnexpectedToken",
  "loc": {
    "line": 14,
    "column": 0,
    "index": 357
  },
  "pos": 357
}
[2025-07-29T19:46:20.513Z] ERROR Component error: DrawdownAnalyzer {}
[2025-07-29T19:46:20.520Z] ERROR ❌ Failed to initialize ExchangeHealthMonitor: {
  "code": "BABEL_PARSE_ERROR",
  "reasonCode": "UnexpectedToken",
  "loc": {
    "line": 124,
    "column": 4,
    "index": 3900
  },
  "pos": 3900
}
[2025-07-29T19:46:20.522Z] ERROR Component error: ExchangeHealthMonitor {}
[2025-07-29T19:46:20.529Z] ERROR ❌ Failed to initialize OpportunityScanner: {
  "code": "BABEL_PARSE_ERROR",
  "reasonCode": "UnexpectedToken",
  "loc": {
    "line": 134,
    "column": 0,
    "index": 3269
  },
  "pos": 3269
}
[2025-07-29T19:46:20.532Z] ERROR Component error: OpportunityScanner {}
[2025-07-29T19:46:20.586Z] ERROR Failed to begin transaction: {}
[2025-07-29T19:46:20.588Z] ERROR Failed to rollback transaction: {}
[2025-07-29T20:01:05.376Z] ERROR Query execution failed: {
  "query": "\n                CREATE TABLE IF NOT EXISTS portfolios (\n                    id VARCHAR(255) PRIMARY",
  "error": "DatabaseManager not initialized"
}
[2025-07-29T20:01:05.380Z] ERROR Failed to create tables: {}
[2025-07-29T20:01:05.381Z] ERROR ❌ Failed to initialize DatabaseManager: {}
[2025-07-29T20:01:05.412Z] ERROR Query execution failed: {
  "query": "\n                CREATE TABLE IF NOT EXISTS portfolios (\n                    id VARCHAR(255) PRIMARY",
  "error": "DatabaseManager not initialized"
}
[2025-07-29T20:01:05.413Z] ERROR Failed to create tables: {}
[2025-07-29T20:01:05.414Z] ERROR ❌ Failed to initialize DatabaseManager: {}
[2025-07-29T20:01:05.666Z] ERROR ❌ Failed to initialize DrawdownAnalyzer: {
  "code": "BABEL_PARSE_ERROR",
  "reasonCode": "UnexpectedToken",
  "loc": {
    "line": 14,
    "column": 0,
    "index": 357
  },
  "pos": 357
}
[2025-07-29T20:01:05.668Z] ERROR Component error: DrawdownAnalyzer {}
[2025-07-29T20:01:05.675Z] ERROR ❌ Failed to initialize ExchangeHealthMonitor: {
  "code": "BABEL_PARSE_ERROR",
  "reasonCode": "UnexpectedToken",
  "loc": {
    "line": 124,
    "column": 4,
    "index": 3900
  },
  "pos": 3900
}
[2025-07-29T20:01:05.677Z] ERROR Component error: ExchangeHealthMonitor {}
[2025-07-29T20:01:05.683Z] ERROR ❌ Failed to initialize OpportunityScanner: {
  "code": "BABEL_PARSE_ERROR",
  "reasonCode": "UnexpectedToken",
  "loc": {
    "line": 134,
    "column": 0,
    "index": 3269
  },
  "pos": 3269
}
[2025-07-29T20:01:05.685Z] ERROR Component error: OpportunityScanner {}
[2025-07-29T20:01:05.742Z] ERROR Failed to begin transaction: {}
[2025-07-29T20:01:05.743Z] ERROR Failed to rollback transaction: {}
[2025-07-29T20:09:38.079Z] ERROR Query execution failed: {
  "query": "\n                CREATE TABLE IF NOT EXISTS portfolios (\n                    id VARCHAR(255) PRIMARY",
  "error": "DatabaseManager not initialized"
}
[2025-07-29T20:09:38.081Z] ERROR Failed to create tables: {}
[2025-07-29T20:09:38.083Z] ERROR ❌ Failed to initialize DatabaseManager: {}
[2025-07-29T20:09:38.113Z] ERROR Query execution failed: {
  "query": "\n                CREATE TABLE IF NOT EXISTS portfolios (\n                    id VARCHAR(255) PRIMARY",
  "error": "DatabaseManager not initialized"
}
[2025-07-29T20:09:38.114Z] ERROR Failed to create tables: {}
[2025-07-29T20:09:38.115Z] ERROR ❌ Failed to initialize DatabaseManager: {}
[2025-07-29T20:09:38.352Z] ERROR ❌ Failed to initialize DrawdownAnalyzer: {
  "code": "BABEL_PARSE_ERROR",
  "reasonCode": "UnexpectedToken",
  "loc": {
    "line": 14,
    "column": 0,
    "index": 357
  },
  "pos": 357
}
[2025-07-29T20:09:38.354Z] ERROR Component error: DrawdownAnalyzer {}
[2025-07-29T20:09:38.360Z] ERROR ❌ Failed to initialize ExchangeHealthMonitor: {
  "code": "BABEL_PARSE_ERROR",
  "reasonCode": "UnexpectedToken",
  "loc": {
    "line": 124,
    "column": 4,
    "index": 3900
  },
  "pos": 3900
}
[2025-07-29T20:09:38.362Z] ERROR Component error: ExchangeHealthMonitor {}
[2025-07-29T20:09:38.369Z] ERROR ❌ Failed to initialize OpportunityScanner: {
  "code": "BABEL_PARSE_ERROR",
  "reasonCode": "UnexpectedToken",
  "loc": {
    "line": 134,
    "column": 0,
    "index": 3269
  },
  "pos": 3269
}
[2025-07-29T20:09:38.371Z] ERROR Component error: OpportunityScanner {}
[2025-07-29T20:09:38.418Z] ERROR Failed to begin transaction: {}
[2025-07-29T20:09:38.419Z] ERROR Failed to rollback transaction: {}
[2025-07-29T20:17:44.677Z] ERROR Query execution failed: {
  "query": "\n                CREATE TABLE IF NOT EXISTS portfolios (\n                    id VARCHAR(255) PRIMARY",
  "error": "DatabaseManager not initialized"
}
[2025-07-29T20:17:44.679Z] ERROR Failed to create tables: {}
[2025-07-29T20:17:44.681Z] ERROR ❌ Failed to initialize DatabaseManager: {}
[2025-07-29T20:17:44.703Z] ERROR Query execution failed: {
  "query": "\n                CREATE TABLE IF NOT EXISTS portfolios (\n                    id VARCHAR(255) PRIMARY",
  "error": "DatabaseManager not initialized"
}
[2025-07-29T20:17:44.704Z] ERROR Failed to create tables: {}
[2025-07-29T20:17:44.705Z] ERROR ❌ Failed to initialize DatabaseManager: {}
[2025-07-29T20:17:44.956Z] ERROR ❌ Failed to initialize DrawdownAnalyzer: {
  "code": "BABEL_PARSE_ERROR",
  "reasonCode": "UnexpectedToken",
  "loc": {
    "line": 14,
    "column": 0,
    "index": 357
  },
  "pos": 357
}
[2025-07-29T20:17:44.962Z] ERROR Component error: DrawdownAnalyzer {}
[2025-07-29T20:17:44.972Z] ERROR ❌ Failed to initialize ExchangeHealthMonitor: {
  "code": "BABEL_PARSE_ERROR",
  "reasonCode": "UnexpectedToken",
  "loc": {
    "line": 124,
    "column": 4,
    "index": 3900
  },
  "pos": 3900
}
[2025-07-29T20:17:44.975Z] ERROR Component error: ExchangeHealthMonitor {}
[2025-07-29T20:17:44.999Z] ERROR ❌ Failed to initialize OpportunityScanner: {
  "code": "BABEL_PARSE_ERROR",
  "reasonCode": "UnexpectedToken",
  "loc": {
    "line": 134,
    "column": 0,
    "index": 3269
  },
  "pos": 3269
}
[2025-07-29T20:17:45.001Z] ERROR Component error: OpportunityScanner {}
[2025-07-29T20:17:45.062Z] ERROR Failed to begin transaction: {}
[2025-07-29T20:17:45.063Z] ERROR Failed to rollback transaction: {}
[2025-07-30T19:13:11.759Z] ERROR Database initialization failed: {}
[2025-07-30T19:13:11.760Z] ERROR ❌ Startup failed: {}
[2025-07-30T19:13:11.761Z] ERROR Startup failure detected
[2025-07-30T19:13:11.763Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-30T19:13:11.764Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-30T19:14:13.756Z] ERROR Database initialization failed: {}
[2025-07-30T19:14:13.757Z] ERROR ❌ Startup failed: {}
[2025-07-30T19:14:13.758Z] ERROR Startup failure detected
[2025-07-30T19:14:13.758Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-30T19:14:13.759Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-30T19:14:51.886Z] ERROR Credential setup failed: {}
[2025-07-30T19:14:51.888Z] ERROR Exchange initialization failed: {}
[2025-07-30T19:14:51.888Z] ERROR ❌ Startup failed: {}
[2025-07-30T19:14:51.888Z] ERROR Startup failure detected
[2025-07-30T19:14:51.889Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-30T19:14:51.889Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-30T19:15:29.581Z] ERROR Credential setup failed: this.credentialManager.initialize is not a function
TypeError: this.credentialManager.initialize is not a function
    at AutonomousStartup.setupCredentials (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:259:42)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:122:24)
[2025-07-30T19:15:29.583Z] ERROR Exchange initialization failed: this.tradingSystem.getGridBotManager is not a function
TypeError: this.tradingSystem.getGridBotManager is not a function
    at AutonomousStartup.initializeExchanges (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:321:55)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:125:24)
[2025-07-30T19:15:29.583Z] ERROR ❌ Startup failed: this.tradingSystem.getGridBotManager is not a function
TypeError: this.tradingSystem.getGridBotManager is not a function
    at AutonomousStartup.initializeExchanges (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:321:55)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:125:24)
[2025-07-30T19:15:29.584Z] ERROR Startup failure detected
[2025-07-30T19:15:29.585Z] ERROR 💥 Failed to start autonomous trading system: this.tradingSystem.getGridBotManager is not a function
TypeError: this.tradingSystem.getGridBotManager is not a function
    at AutonomousStartup.initializeExchanges (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:321:55)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:125:24)
[2025-07-30T19:15:29.586Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-30T19:18:36.629Z] ERROR Credential setup failed: this.credentialManager.initialize is not a function
TypeError: this.credentialManager.initialize is not a function
    at AutonomousStartup.setupCredentials (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:259:42)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:122:24)
[2025-07-30T19:18:36.631Z] ERROR Exchange initialization failed: Exchange manager not found
Error: Exchange manager not found
    at AutonomousStartup.initializeExchanges (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:325:23)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:125:24)
[2025-07-30T19:18:36.631Z] ERROR ❌ Startup failed: Exchange manager not found
Error: Exchange manager not found
    at AutonomousStartup.initializeExchanges (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:325:23)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:125:24)
[2025-07-30T19:18:36.632Z] ERROR Startup failure detected
[2025-07-30T19:18:36.634Z] ERROR 💥 Failed to start autonomous trading system: Exchange manager not found
Error: Exchange manager not found
    at AutonomousStartup.initializeExchanges (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:325:23)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:125:24)
[2025-07-30T19:18:36.634Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-30T19:20:11.201Z] ERROR Credential setup failed: this.credentialManager.initialize is not a function
TypeError: this.credentialManager.initialize is not a function
    at AutonomousStartup.setupCredentials (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:259:42)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:122:24)
[2025-07-30T19:20:11.211Z] ERROR Failed to initialize TradingOrchestrator {
  "error": "Cannot read properties of undefined (reading 'connect')",
  "stack": "TypeError: Cannot read properties of undefined (reading 'connect')\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:70:40)\n    at TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:44)\n    at AutonomousStartup.startTradingSystem (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:374:38)\n    at AutonomousStartup.start (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:128:24)"
}
[2025-07-30T19:20:11.211Z] ERROR Failed to initialize Trading System: Cannot read properties of undefined (reading 'connect')
TypeError: Cannot read properties of undefined (reading 'connect')
    at TradingOrchestrator.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\TradingOrchestrator.js:70:40)
    at TradingSystemInterface.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\index.js:44:44)
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:374:38)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:24)
[2025-07-30T19:20:11.211Z] ERROR Trading system startup failed: Cannot read properties of undefined (reading 'connect')
TypeError: Cannot read properties of undefined (reading 'connect')
    at TradingOrchestrator.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\TradingOrchestrator.js:70:40)
    at TradingSystemInterface.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\index.js:44:44)
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:374:38)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:24)
[2025-07-30T19:20:11.212Z] ERROR ❌ Startup failed: Cannot read properties of undefined (reading 'connect')
TypeError: Cannot read properties of undefined (reading 'connect')
    at TradingOrchestrator.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\TradingOrchestrator.js:70:40)
    at TradingSystemInterface.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\index.js:44:44)
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:374:38)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:24)
[2025-07-30T19:20:11.212Z] ERROR Startup failure detected
[2025-07-30T19:20:11.214Z] ERROR 💥 Failed to start autonomous trading system: Cannot read properties of undefined (reading 'connect')
TypeError: Cannot read properties of undefined (reading 'connect')
    at TradingOrchestrator.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\TradingOrchestrator.js:70:40)
    at TradingSystemInterface.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\index.js:44:44)
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:374:38)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:24)
[2025-07-30T19:20:11.214Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-30T19:20:39.770Z] ERROR Credential setup failed: this.credentialManager.initialize is not a function
TypeError: this.credentialManager.initialize is not a function
    at AutonomousStartup.setupCredentials (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:259:42)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:122:24)
[2025-07-30T19:20:39.781Z] ERROR Failed to initialize TradingOrchestrator {
  "error": "Cannot read properties of undefined (reading 'initialize')",
  "stack": "TypeError: Cannot read properties of undefined (reading 'initialize')\n    at EnhancedEliteWhaleTracker.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\whaletrader\\EnhancedEliteWhaleTracker.js:32:19)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:73:32)\n    at TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:44)\n    at AutonomousStartup.startTradingSystem (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:374:38)\n    at AutonomousStartup.start (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:128:24)"
}
[2025-07-30T19:20:39.781Z] ERROR Failed to initialize Trading System: Cannot read properties of undefined (reading 'initialize')
TypeError: Cannot read properties of undefined (reading 'initialize')
    at EnhancedEliteWhaleTracker.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\whaletrader\EnhancedEliteWhaleTracker.js:32:19)
    at TradingOrchestrator.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\TradingOrchestrator.js:73:32)
    at TradingSystemInterface.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\index.js:44:44)
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:374:38)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:24)
[2025-07-30T19:20:39.782Z] ERROR Trading system startup failed: Cannot read properties of undefined (reading 'initialize')
TypeError: Cannot read properties of undefined (reading 'initialize')
    at EnhancedEliteWhaleTracker.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\whaletrader\EnhancedEliteWhaleTracker.js:32:19)
    at TradingOrchestrator.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\TradingOrchestrator.js:73:32)
    at TradingSystemInterface.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\index.js:44:44)
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:374:38)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:24)
[2025-07-30T19:20:39.782Z] ERROR ❌ Startup failed: Cannot read properties of undefined (reading 'initialize')
TypeError: Cannot read properties of undefined (reading 'initialize')
    at EnhancedEliteWhaleTracker.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\whaletrader\EnhancedEliteWhaleTracker.js:32:19)
    at TradingOrchestrator.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\TradingOrchestrator.js:73:32)
    at TradingSystemInterface.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\index.js:44:44)
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:374:38)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:24)
[2025-07-30T19:20:39.783Z] ERROR Startup failure detected
[2025-07-30T19:20:39.784Z] ERROR 💥 Failed to start autonomous trading system: Cannot read properties of undefined (reading 'initialize')
TypeError: Cannot read properties of undefined (reading 'initialize')
    at EnhancedEliteWhaleTracker.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\whaletrader\EnhancedEliteWhaleTracker.js:32:19)
    at TradingOrchestrator.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\TradingOrchestrator.js:73:32)
    at TradingSystemInterface.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\index.js:44:44)
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:374:38)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:24)
[2025-07-30T19:20:39.785Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-30T19:21:17.234Z] ERROR Credential setup failed: this.credentialManager.initialize is not a function
TypeError: this.credentialManager.initialize is not a function
    at AutonomousStartup.setupCredentials (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:259:42)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:122:24)
[2025-07-30T19:21:17.240Z] ERROR Failed to initialize TradingOrchestrator {
  "error": "Cannot read properties of undefined (reading 'exchanges')",
  "stack": "TypeError: Cannot read properties of undefined (reading 'exchanges')\n    at EnhancedEliteWhaleTracker.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\whaletrader\\EnhancedEliteWhaleTracker.js:37:18)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:73:32)\n    at TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:44)\n    at AutonomousStartup.startTradingSystem (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:374:38)\n    at AutonomousStartup.start (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:128:24)"
}
[2025-07-30T19:21:17.240Z] ERROR Failed to initialize Trading System: Cannot read properties of undefined (reading 'exchanges')
TypeError: Cannot read properties of undefined (reading 'exchanges')
    at EnhancedEliteWhaleTracker.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\whaletrader\EnhancedEliteWhaleTracker.js:37:18)
    at TradingOrchestrator.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\TradingOrchestrator.js:73:32)
    at TradingSystemInterface.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\index.js:44:44)
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:374:38)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:24)
[2025-07-30T19:21:17.241Z] ERROR Trading system startup failed: Cannot read properties of undefined (reading 'exchanges')
TypeError: Cannot read properties of undefined (reading 'exchanges')
    at EnhancedEliteWhaleTracker.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\whaletrader\EnhancedEliteWhaleTracker.js:37:18)
    at TradingOrchestrator.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\TradingOrchestrator.js:73:32)
    at TradingSystemInterface.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\index.js:44:44)
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:374:38)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:24)
[2025-07-30T19:21:17.241Z] ERROR ❌ Startup failed: Cannot read properties of undefined (reading 'exchanges')
TypeError: Cannot read properties of undefined (reading 'exchanges')
    at EnhancedEliteWhaleTracker.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\whaletrader\EnhancedEliteWhaleTracker.js:37:18)
    at TradingOrchestrator.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\TradingOrchestrator.js:73:32)
    at TradingSystemInterface.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\index.js:44:44)
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:374:38)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:24)
[2025-07-30T19:21:17.242Z] ERROR Startup failure detected
[2025-07-30T19:21:17.243Z] ERROR 💥 Failed to start autonomous trading system: Cannot read properties of undefined (reading 'exchanges')
TypeError: Cannot read properties of undefined (reading 'exchanges')
    at EnhancedEliteWhaleTracker.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\whaletrader\EnhancedEliteWhaleTracker.js:37:18)
    at TradingOrchestrator.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\TradingOrchestrator.js:73:32)
    at TradingSystemInterface.initialize (C:\Users\<USER>\Documents\electronTrader\app\trading\index.js:44:44)
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:374:38)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:24)
[2025-07-30T19:21:17.244Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-30T19:22:05.634Z] ERROR Credential setup failed: this.credentialManager.initialize is not a function
TypeError: this.credentialManager.initialize is not a function
    at AutonomousStartup.setupCredentials (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:259:42)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:122:24)
[2025-07-30T19:22:05.641Z] ERROR Trading system startup failed: this.tradingSystem.saveSettings is not a function
TypeError: this.tradingSystem.saveSettings is not a function
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:382:42)
    at async AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:13)
[2025-07-30T19:22:05.642Z] ERROR ❌ Startup failed: this.tradingSystem.saveSettings is not a function
TypeError: this.tradingSystem.saveSettings is not a function
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:382:42)
    at async AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:13)
[2025-07-30T19:22:05.642Z] ERROR Startup failure detected
[2025-07-30T19:22:05.643Z] ERROR 💥 Failed to start autonomous trading system: this.tradingSystem.saveSettings is not a function
TypeError: this.tradingSystem.saveSettings is not a function
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:382:42)
    at async AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:13)
[2025-07-30T19:22:05.644Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-30T19:22:45.487Z] ERROR Credential setup failed: this.credentialManager.initialize is not a function
TypeError: this.credentialManager.initialize is not a function
    at AutonomousStartup.setupCredentials (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:259:42)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:122:24)
[2025-07-30T19:22:45.496Z] ERROR Failed to start trading system {
  "error": "Cannot read properties of undefined (reading 'updateInterval')"
}
[2025-07-30T19:22:45.496Z] ERROR Failed to start trading engine: Cannot read properties of undefined (reading 'updateInterval')
TypeError: Cannot read properties of undefined (reading 'updateInterval')
    at EnhancedEliteWhaleTracker.start (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\whaletrader\EnhancedEliteWhaleTracker.js:65:20)
    at TradingOrchestrator.start (C:\Users\<USER>\Documents\electronTrader\app\trading\TradingOrchestrator.js:131:48)
    at TradingSystemInterface.startTradingEngine (C:\Users\<USER>\Documents\electronTrader\app\trading\index.js:198:44)
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:388:53)
    at async AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:13)
[2025-07-30T19:22:45.497Z] ERROR Trading system startup failed: TypeError: Cannot read properties of undefined (reading 'updateInterval')
Error: TypeError: Cannot read properties of undefined (reading 'updateInterval')
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:391:23)
    at async AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:13)
[2025-07-30T19:22:45.497Z] ERROR ❌ Startup failed: TypeError: Cannot read properties of undefined (reading 'updateInterval')
Error: TypeError: Cannot read properties of undefined (reading 'updateInterval')
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:391:23)
    at async AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:13)
[2025-07-30T19:22:45.497Z] ERROR Startup failure detected
[2025-07-30T19:22:45.498Z] ERROR 💥 Failed to start autonomous trading system: TypeError: Cannot read properties of undefined (reading 'updateInterval')
Error: TypeError: Cannot read properties of undefined (reading 'updateInterval')
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:391:23)
    at async AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:13)
[2025-07-30T19:22:45.498Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-30T19:29:55.229Z] ERROR Credential setup failed: this.credentialManager.initialize is not a function
TypeError: this.credentialManager.initialize is not a function
    at AutonomousStartup.setupCredentials (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:259:42)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:122:24)
[2025-07-30T19:29:55.236Z] ERROR Trading system startup failed: this.tradingSystem.toggleWhaleTracking is not a function
TypeError: this.tradingSystem.toggleWhaleTracking is not a function
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:396:42)
    at async AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:13)
[2025-07-30T19:29:55.236Z] ERROR ❌ Startup failed: this.tradingSystem.toggleWhaleTracking is not a function
TypeError: this.tradingSystem.toggleWhaleTracking is not a function
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:396:42)
    at async AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:13)
[2025-07-30T19:29:55.236Z] ERROR Startup failure detected
[2025-07-30T19:29:55.237Z] ERROR 💥 Failed to start autonomous trading system: this.tradingSystem.toggleWhaleTracking is not a function
TypeError: this.tradingSystem.toggleWhaleTracking is not a function
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:396:42)
    at async AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:13)
[2025-07-30T19:29:55.237Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-30T19:33:53.669Z] ERROR Credential setup failed: this.credentialManager.initialize is not a function
TypeError: this.credentialManager.initialize is not a function
    at AutonomousStartup.setupCredentials (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:259:42)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:122:24)
[2025-07-30T19:33:53.691Z] ERROR Failed to restore active strategies: Cannot read properties of undefined (reading 'getActiveBots')
TypeError: Cannot read properties of undefined (reading 'getActiveBots')
    at AutonomousStartup.restoreActiveStrategies (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:454:64)
    at AutonomousStartup.startTradingSystem (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:408:24)
    at async AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:128:13)
[2025-07-30T19:35:15.708Z] ERROR Credential setup failed: this.credentialManager.initialize is not a function
TypeError: this.credentialManager.initialize is not a function
    at AutonomousStartup.setupCredentials (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:259:42)
    at AutonomousStartup.start (C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js:122:24)
[2025-07-30T23:41:36.145Z] ERROR Database initialization failed: {}
[2025-07-30T23:41:36.146Z] ERROR ❌ Startup failed: {}
[2025-07-30T23:41:36.146Z] ERROR Startup failure detected
[2025-07-30T23:41:36.147Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-30T23:41:36.147Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-30T23:42:58.067Z] ERROR Database initialization failed: {}
[2025-07-30T23:42:58.068Z] ERROR ❌ Startup failed: {}
[2025-07-30T23:42:58.068Z] ERROR Startup failure detected
[2025-07-30T23:42:58.069Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-30T23:42:58.069Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-30T23:43:25.242Z] ERROR Database initialization failed: this.dbInitializer.createBackup is not a function
[2025-07-30T23:43:25.243Z] ERROR ❌ Startup failed: {}
[2025-07-30T23:43:25.243Z] ERROR Startup failure detected
[2025-07-30T23:43:25.244Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-30T23:43:25.244Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-30T23:43:45.073Z] ERROR Credential setup failed: {}
[2025-07-30T23:43:45.074Z] ERROR Exchange initialization failed: {}
[2025-07-30T23:43:45.074Z] ERROR ❌ Startup failed: {}
[2025-07-30T23:43:45.075Z] ERROR Startup failure detected
[2025-07-30T23:43:45.075Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-30T23:43:45.075Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:23:20.678Z] ERROR Credential setup failed: {}
[2025-07-31T00:23:20.679Z] ERROR Exchange initialization failed: {}
[2025-07-31T00:23:20.679Z] ERROR ❌ Startup failed: {}
[2025-07-31T00:23:20.679Z] ERROR Startup failure detected
[2025-07-31T00:23:20.680Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-31T00:23:20.680Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:28:32.999Z] ERROR Credential setup failed: {}
[2025-07-31T00:28:33.001Z] ERROR Exchange initialization failed: {}
[2025-07-31T00:28:33.001Z] ERROR ❌ Startup failed: {}
[2025-07-31T00:28:33.001Z] ERROR Startup failure detected
[2025-07-31T00:28:33.002Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-31T00:28:33.002Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:29:30.886Z] ERROR Credential setup failed: {}
[2025-07-31T00:29:30.890Z] ERROR Failed to initialize Trading System: {}
[2025-07-31T00:29:30.891Z] ERROR Trading system startup failed: {}
[2025-07-31T00:29:30.891Z] ERROR ❌ Startup failed: {}
[2025-07-31T00:29:30.891Z] ERROR Startup failure detected
[2025-07-31T00:29:30.892Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-31T00:29:30.892Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:30:43.241Z] ERROR Credential setup failed: {}
[2025-07-31T00:30:43.246Z] ERROR Failed to initialize TradingOrchestrator {
  "error": "SQLITE_CANTOPEN: unable to open database file",
  "stack": "Error: SQLITE_CANTOPEN: unable to open database file"
}
[2025-07-31T00:30:43.246Z] ERROR Failed to initialize Trading System: {
  "errno": 14,
  "code": "SQLITE_CANTOPEN"
}
[2025-07-31T00:30:43.246Z] ERROR Trading system startup failed: {
  "errno": 14,
  "code": "SQLITE_CANTOPEN"
}
[2025-07-31T00:30:43.247Z] ERROR ❌ Startup failed: {
  "errno": 14,
  "code": "SQLITE_CANTOPEN"
}
[2025-07-31T00:30:43.247Z] ERROR Startup failure detected
[2025-07-31T00:30:43.248Z] ERROR 💥 Failed to start autonomous trading system: {
  "errno": 14,
  "code": "SQLITE_CANTOPEN"
}
[2025-07-31T00:30:43.248Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:34:51.669Z] ERROR Credential setup failed: {}
[2025-07-31T00:34:51.676Z] ERROR Failed to initialize TradingOrchestrator {
  "error": "SQLITE_CANTOPEN: unable to open database file",
  "stack": "Error: SQLITE_CANTOPEN: unable to open database file"
}
[2025-07-31T00:34:51.676Z] ERROR Failed to initialize Trading System: {
  "errno": 14,
  "code": "SQLITE_CANTOPEN"
}
[2025-07-31T00:34:51.676Z] ERROR Trading system startup failed: {
  "errno": 14,
  "code": "SQLITE_CANTOPEN"
}
[2025-07-31T00:34:51.677Z] ERROR ❌ Startup failed: {
  "errno": 14,
  "code": "SQLITE_CANTOPEN"
}
[2025-07-31T00:34:51.677Z] ERROR Startup failure detected
[2025-07-31T00:34:51.678Z] ERROR 💥 Failed to start autonomous trading system: {
  "errno": 14,
  "code": "SQLITE_CANTOPEN"
}
[2025-07-31T00:34:51.679Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:36:26.566Z] ERROR Credential setup failed: {}
[2025-07-31T00:36:26.571Z] ERROR Failed to initialize TradingOrchestrator {
  "error": "SQLITE_CANTOPEN: unable to open database file",
  "stack": "Error: SQLITE_CANTOPEN: unable to open database file"
}
[2025-07-31T00:36:26.571Z] ERROR Failed to initialize Trading System: {
  "errno": 14,
  "code": "SQLITE_CANTOPEN"
}
[2025-07-31T00:36:26.572Z] ERROR Trading system startup failed: {
  "errno": 14,
  "code": "SQLITE_CANTOPEN"
}
[2025-07-31T00:36:26.572Z] ERROR ❌ Startup failed: {
  "errno": 14,
  "code": "SQLITE_CANTOPEN"
}
[2025-07-31T00:36:26.572Z] ERROR Startup failure detected
[2025-07-31T00:36:26.573Z] ERROR 💥 Failed to start autonomous trading system: {
  "errno": 14,
  "code": "SQLITE_CANTOPEN"
}
[2025-07-31T00:36:26.573Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:37:01.409Z] ERROR Credential setup failed: {}
[2025-07-31T00:37:01.415Z] ERROR Failed to initialize TradingOrchestrator {
  "error": "SQLITE_CANTOPEN: unable to open database file",
  "stack": "Error: SQLITE_CANTOPEN: unable to open database file"
}
[2025-07-31T00:37:01.416Z] ERROR Failed to initialize Trading System: {
  "errno": 14,
  "code": "SQLITE_CANTOPEN"
}
[2025-07-31T00:37:01.416Z] ERROR Trading system startup failed: {
  "errno": 14,
  "code": "SQLITE_CANTOPEN"
}
[2025-07-31T00:37:01.417Z] ERROR ❌ Startup failed: {
  "errno": 14,
  "code": "SQLITE_CANTOPEN"
}
[2025-07-31T00:37:01.417Z] ERROR Startup failure detected
[2025-07-31T00:37:01.418Z] ERROR 💥 Failed to start autonomous trading system: {
  "errno": 14,
  "code": "SQLITE_CANTOPEN"
}
[2025-07-31T00:37:01.418Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:37:50.545Z] ERROR Credential setup failed: {}
[2025-07-31T00:37:50.550Z] ERROR Failed to initialize TradingOrchestrator {
  "error": "Cannot read properties of undefined (reading 'initialize')",
  "stack": "TypeError: Cannot read properties of undefined (reading 'initialize')\n    at EnhancedEliteWhaleTracker.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\whaletrader\\EnhancedEliteWhaleTracker.js:32:19)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:74:26)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:7)\n    at async AutonomousStartup.startTradingSystem (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:397:7)\n    at async AutonomousStartup.start (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:143:7)"
}
[2025-07-31T00:37:50.551Z] ERROR Failed to initialize Trading System: {}
[2025-07-31T00:37:50.551Z] ERROR Trading system startup failed: {}
[2025-07-31T00:37:50.551Z] ERROR ❌ Startup failed: {}
[2025-07-31T00:37:50.552Z] ERROR Startup failure detected
[2025-07-31T00:37:50.552Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-31T00:37:50.553Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:38:52.410Z] ERROR Credential setup failed: {}
[2025-07-31T00:38:52.415Z] ERROR Failed to initialize TradingOrchestrator {
  "error": "Unsupported database driver: undefined",
  "stack": "Error: Unsupported database driver: undefined\n    at new DatabaseIntegration (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\data\\databases\\DatabaseIntegration.js:34:13)\n    at new EnhancedEliteWhaleTracker (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\whaletrader\\EnhancedEliteWhaleTracker.js:16:15)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:73:28)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:7)\n    at async AutonomousStartup.startTradingSystem (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:397:7)\n    at async AutonomousStartup.start (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:143:7)"
}
[2025-07-31T00:38:52.415Z] ERROR Failed to initialize Trading System: {}
[2025-07-31T00:38:52.416Z] ERROR Trading system startup failed: {}
[2025-07-31T00:38:52.416Z] ERROR ❌ Startup failed: {}
[2025-07-31T00:38:52.416Z] ERROR Startup failure detected
[2025-07-31T00:38:52.417Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-31T00:38:52.417Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:42:39.237Z] ERROR Credential setup failed: {}
[2025-07-31T00:42:39.279Z] ERROR Failed to initialize TradingOrchestrator {
  "error": "Cannot read properties of undefined (reading 'forEach')",
  "stack": "TypeError: Cannot read properties of undefined (reading 'forEach')\n    at EnhancedEliteWhaleTracker.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\whaletrader\\EnhancedEliteWhaleTracker.js:49:28)\n    at async TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:74:7)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:7)\n    at async AutonomousStartup.startTradingSystem (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:397:7)\n    at async AutonomousStartup.start (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:143:7)"
}
[2025-07-31T00:42:39.280Z] ERROR Failed to initialize Trading System: {}
[2025-07-31T00:42:39.281Z] ERROR Trading system startup failed: {}
[2025-07-31T00:42:39.281Z] ERROR ❌ Startup failed: {}
[2025-07-31T00:42:39.281Z] ERROR Startup failure detected
[2025-07-31T00:42:39.283Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-31T00:42:39.283Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:49:48.674Z] ERROR Credential setup failed: {}
[2025-07-31T00:49:48.684Z] ERROR Trading system startup failed: {}
[2025-07-31T00:49:48.684Z] ERROR ❌ Startup failed: {}
[2025-07-31T00:49:48.685Z] ERROR Startup failure detected
[2025-07-31T00:49:48.686Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-31T00:49:48.686Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:52:55.536Z] ERROR Credential setup failed: {}
[2025-07-31T00:52:55.548Z] ERROR Trading system startup failed: {}
[2025-07-31T00:52:55.548Z] ERROR ❌ Startup failed: {}
[2025-07-31T00:52:55.549Z] ERROR Startup failure detected
[2025-07-31T00:52:55.550Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-31T00:52:55.550Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:53:39.088Z] ERROR Credential setup failed: {}
[2025-07-31T00:53:39.101Z] ERROR Trading system startup failed: {}
[2025-07-31T00:53:39.102Z] ERROR ❌ Startup failed: {}
[2025-07-31T00:53:39.102Z] ERROR Startup failure detected
[2025-07-31T00:53:39.103Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-31T00:53:39.103Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:54:16.024Z] ERROR Credential setup failed: {}
[2025-07-31T00:54:16.034Z] ERROR Trading system startup failed: {}
[2025-07-31T00:54:16.035Z] ERROR ❌ Startup failed: {}
[2025-07-31T00:54:16.036Z] ERROR Startup failure detected
[2025-07-31T00:54:16.036Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-31T00:54:16.037Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:55:24.323Z] ERROR Credential setup failed: {}
[2025-07-31T00:55:24.338Z] ERROR Trading system startup failed: {}
[2025-07-31T00:55:24.338Z] ERROR ❌ Startup failed: {}
[2025-07-31T00:55:24.339Z] ERROR Startup failure detected
[2025-07-31T00:55:24.342Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-31T00:55:24.342Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:57:01.447Z] ERROR Credential setup failed: {}
[2025-07-31T00:57:01.461Z] ERROR Trading system startup failed: {}
[2025-07-31T00:57:01.461Z] ERROR ❌ Startup failed: {}
[2025-07-31T00:57:01.462Z] ERROR Startup failure detected
[2025-07-31T00:57:01.463Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-31T00:57:01.463Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T00:58:05.362Z] ERROR Credential setup failed: {}
[2025-07-31T00:58:05.378Z] ERROR Trading system startup failed: {}
[2025-07-31T00:58:05.379Z] ERROR ❌ Startup failed: {}
[2025-07-31T00:58:05.379Z] ERROR Startup failure detected
[2025-07-31T00:58:05.381Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-31T00:58:05.381Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T01:02:03.010Z] ERROR Credential setup failed: {}
[2025-07-31T01:02:03.025Z] ERROR Trading system startup failed: {}
[2025-07-31T01:02:03.026Z] ERROR ❌ Startup failed: {}
[2025-07-31T01:02:03.026Z] ERROR Startup failure detected
[2025-07-31T01:02:03.027Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-31T01:02:03.027Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T01:03:49.318Z] ERROR Credential setup failed: {}
[2025-07-31T01:03:49.333Z] ERROR Trading system startup failed: {}
[2025-07-31T01:03:49.333Z] ERROR ❌ Startup failed: {}
[2025-07-31T01:03:49.333Z] ERROR Startup failure detected
[2025-07-31T01:03:49.334Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-31T01:03:49.334Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T01:04:24.164Z] ERROR Credential setup failed: {}
[2025-07-31T01:04:24.181Z] ERROR Trading system startup failed: {}
[2025-07-31T01:04:24.182Z] ERROR ❌ Startup failed: {}
[2025-07-31T01:04:24.182Z] ERROR Startup failure detected
[2025-07-31T01:04:24.183Z] ERROR 💥 Failed to start autonomous trading system: {}
[2025-07-31T01:04:24.183Z] ERROR Autonomous startup failed - system remains inactive until manual restart
[2025-07-31T01:04:55.792Z] ERROR Credential setup failed: {}
[2025-07-31T01:04:55.807Z] ERROR Failed to restore active strategies: {}
[2025-07-31T21:33:43.892Z] ERROR ❌ Failed to initialize nonexistent_exchange: Exchange nonexistent_exchange not supported by CCXT
[2025-07-31T21:33:46.002Z] ERROR ❌ Failed to get ticker for INVALID/SYMBOL on binance: binance does not have market symbol INVALID/SYMBOL
[2025-07-31T21:33:46.003Z] ERROR ❌ Failed to get ticker for BTC/USDT on uninitialized: Exchange uninitialized not initialized
[2025-07-31T21:36:08.951Z] ERROR ❌ Failed to initialize nonexistent_exchange: Exchange nonexistent_exchange not supported by CCXT
[2025-07-31T21:36:11.996Z] ERROR ❌ Failed to get ticker for INVALID/SYMBOL on binance: binance does not have market symbol INVALID/SYMBOL
[2025-07-31T21:36:11.997Z] ERROR ❌ Failed to get ticker for BTC/USDT on uninitialized: Exchange uninitialized not initialized
[2025-07-31T21:52:16.250Z] ERROR ❌ Failed to initialize nonexistent_exchange: Exchange nonexistent_exchange not supported by CCXT
[2025-07-31T21:52:18.901Z] ERROR ❌ Failed to get ticker for INVALID/SYMBOL on binance: binance does not have market symbol INVALID/SYMBOL
[2025-07-31T21:52:18.901Z] ERROR ❌ Failed to get ticker for BTC/USDT on uninitialized: Exchange uninitialized not initialized

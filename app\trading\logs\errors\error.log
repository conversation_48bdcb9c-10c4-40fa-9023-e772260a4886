{"code":"ERR_DLOPEN_FAILED","level":"error","message":"Database connection failed /mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/build/Release/better_sqlite3.node: invalid ELF header","service":"trading-system","stack":"Error: /mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/build/Release/better_sqlite3.node: invalid ELF header\n    at Object..node (node:internal/modules/cjs/loader:1921:18)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at bindings (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/bindings/bindings.js:112:48)\n    at new Database (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/lib/database.js:48:64)\n    at DatabaseManager.connect (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/helpers/database-manager.js:118:23)","timestamp":"2025-07-10T17:36:28.080Z"}
{"code":"ERR_DLOPEN_FAILED","level":"error","message":"Database connection failed /mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/build/Release/better_sqlite3.node: invalid ELF header","service":"trading-system","stack":"Error: /mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/build/Release/better_sqlite3.node: invalid ELF header\n    at Object..node (node:internal/modules/cjs/loader:1921:18)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at bindings (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/bindings/bindings.js:112:48)\n    at new Database (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/lib/database.js:48:64)\n    at DatabaseManager.connect (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/helpers/database-manager.js:174:23)","timestamp":"2025-07-10T17:40:29.270Z"}
{"code":"ERR_DLOPEN_FAILED","level":"error","message":"Database connection failed /mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/build/Release/better_sqlite3.node: invalid ELF header","service":"trading-system","stack":"Error: /mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/build/Release/better_sqlite3.node: invalid ELF header\n    at Object..node (node:internal/modules/cjs/loader:1921:18)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at bindings (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/bindings/bindings.js:112:48)\n    at new Database (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/lib/database.js:48:64)\n    at DatabaseManager.connect (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/helpers/database-manager.js:118:23)","timestamp":"2025-07-10T17:44:31.782Z"}
{"level":"error","message":"Error loading database configuration: Validation error for database: \"development\" is not allowed","service":"trading-system","stack":"Error: Validation error for database: \"development\" is not allowed\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:355:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:225:41)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:27:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:506:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-10T18:06:28.786Z"}
{"level":"error","message":"Error loading configurations: Validation error for database: \"development\" is not allowed","service":"trading-system","stack":"Error: Validation error for database: \"development\" is not allowed\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:355:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:225:41)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:27:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:506:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-10T18:06:28.787Z"}
{"level":"error","message":"Error loading database configuration: Validation error for database: \"development\" is not allowed","service":"trading-system","stack":"Error: Validation error for database: \"development\" is not allowed\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:265:41)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T17:17:48.246Z"}
{"level":"error","message":"Error loading configurations: Validation error for database: \"development\" is not allowed","service":"trading-system","stack":"Error: Validation error for database: \"development\" is not allowed\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:265:41)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T17:17:48.246Z"}
{"level":"error","message":"Error loading database configuration: Validation error for database: \"development\" is not allowed","service":"trading-system","stack":"Error: Validation error for database: \"development\" is not allowed\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:265:41)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:12:35.934Z"}
{"level":"error","message":"Error loading configurations: Validation error for database: \"development\" is not allowed","service":"trading-system","stack":"Error: Validation error for database: \"development\" is not allowed\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:265:41)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:12:35.935Z"}
{"level":"error","message":"Error loading database configuration: Validation error for database: \"development\" is not allowed","service":"trading-system","stack":"Error: Validation error for database: \"development\" is not allowed\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:265:41)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:42:49.959Z"}
{"level":"error","message":"Error loading configurations: Validation error for database: \"development\" is not allowed","service":"trading-system","stack":"Error: Validation error for database: \"development\" is not allowed\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:265:41)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:42:49.961Z"}
{"level":"error","message":"Error loading api configuration: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty","service":"trading-system","stack":"Error: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:290:36)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:45:50.482Z"}
{"level":"error","message":"Error loading configurations: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty","service":"trading-system","stack":"Error: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:290:36)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:45:50.483Z"}
{"level":"error","message":"Error loading api configuration: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty","service":"trading-system","stack":"Error: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:290:36)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:54:41.537Z"}
{"level":"error","message":"Error loading configurations: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty","service":"trading-system","stack":"Error: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:290:36)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:54:41.538Z"}
{"level":"error","message":"Error loading api configuration: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty","service":"trading-system","stack":"Error: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:290:36)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:54:41.597Z"}
{"level":"error","message":"Error loading configurations: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty","service":"trading-system","stack":"Error: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:290:36)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:54:41.597Z"}
{"level":"error","message":"Error loading api configuration: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty","service":"trading-system","stack":"Error: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:290:36)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:54:41.627Z"}
{"level":"error","message":"Error loading configurations: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty","service":"trading-system","stack":"Error: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:290:36)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:54:41.627Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.components.eventCoordinator.initialize is not a function","service":"trading-system","stack":"TypeError: this.components.eventCoordinator.initialize is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:124:52)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:13)\n    at async C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:54:17\n    at async TradingSystemTester.test (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:160:13)\n    at async TradingSystemTester.runTests (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:53:13)","timestamp":"2025-07-14T21:03:34.687Z"}
{"level":"error","message":"Failed to initialize Trading System: this.components.eventCoordinator.initialize is not a function","service":"trading-system","stack":"TypeError: this.components.eventCoordinator.initialize is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:124:52)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:13)\n    at async C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:54:17\n    at async TradingSystemTester.test (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:160:13)\n    at async TradingSystemTester.runTests (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:53:13)","timestamp":"2025-07-14T21:03:34.687Z"}
{"0":"t","1":"h","10":"n","11":"e","12":"n","13":"t","14":"s","15":".","16":"e","17":"v","18":"e","19":"n","2":"i","20":"t","21":"C","22":"o","23":"o","24":"r","25":"d","26":"i","27":"n","28":"a","29":"t","3":"s","30":"o","31":"r","32":".","33":"i","34":"n","35":"i","36":"t","37":"i","38":"a","39":"l","4":".","40":"i","41":"z","42":"e","43":" ","44":"i","45":"s","46":" ","47":"n","48":"o","49":"t","5":"c","50":" ","51":"a","52":" ","53":"f","54":"u","55":"n","56":"c","57":"t","58":"i","59":"o","6":"o","60":"n","7":"m","8":"p","9":"o","level":"error","message":"❌ System Initialization - FAILED (4ms)","service":"trading-system","timestamp":"2025-07-14T21:03:34.687Z"}
{"level":"error","message":"- System Initialization: this.components.eventCoordinator.initialize is not a function","service":"trading-system","timestamp":"2025-07-14T21:03:34.692Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:07.000Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:07.001Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:07.001Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:07.001Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:07.002Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:07.002Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:07.002Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:07.002Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:37.002Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:37.002Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:37.002Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:37.002Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:58:07.000Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:58:07.001Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:58:07.001Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:58:07.001Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:58:37.006Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:58:37.006Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:58:37.006Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:58:37.007Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:59:07.101Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:59:07.101Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:59:07.101Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:59:07.101Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:59:37.100Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:59:37.100Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:59:37.100Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:59:37.101Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:15.773Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:15.775Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:15.775Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:15.775Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:22.038Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:22.039Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:22.039Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:22.040Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:22.040Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:22.041Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:22.041Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:22.041Z"}
{"code":"ERR_DLOPEN_FAILED","level":"error","message":"Failed to initialize Trading Orchestrator: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).","service":"trading-system","stack":"Error: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).\n    at process.func [as dlopen] (node:electron/js2c/node_init:2:2559)\n    at Module._extensions..node (node:internal/modules/cjs/loader:1602:18)\n    at Object.func [as .node] (node:electron/js2c/node_init:2:2559)\n    at Module.load (node:internal/modules/cjs/loader:1295:32)\n    at Module._load (node:internal/modules/cjs/loader:1111:12)\n    at c._load (node:electron/js2c/node_init:2:16955)\n    at Module.require (node:internal/modules/cjs/loader:1318:19)\n    at require (node:internal/modules/helpers:179:18)\n    at bindings (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\bindings\\bindings.js:112:48)\n    at new Database (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\lib\\database.js:48:64)","timestamp":"2025-07-15T02:22:42.323Z"}
{"code":"ERR_DLOPEN_FAILED","level":"error","message":"Failed to initialize Trading Orchestrator: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).","service":"trading-system","stack":"Error: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).\n    at process.func [as dlopen] (node:electron/js2c/node_init:2:2559)\n    at Module._extensions..node (node:internal/modules/cjs/loader:1602:18)\n    at Object.func [as .node] (node:electron/js2c/node_init:2:2559)\n    at Module.load (node:internal/modules/cjs/loader:1295:32)\n    at Module._load (node:internal/modules/cjs/loader:1111:12)\n    at c._load (node:electron/js2c/node_init:2:16955)\n    at Module.require (node:internal/modules/cjs/loader:1318:19)\n    at require (node:internal/modules/helpers:179:18)\n    at bindings (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\bindings\\bindings.js:112:48)\n    at new Database (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\lib\\database.js:48:64)","timestamp":"2025-07-15T02:39:28.243Z"}
{"code":"ERR_DLOPEN_FAILED","level":"error","message":"Failed to initialize Trading Orchestrator: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).","service":"trading-system","stack":"Error: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).\n    at process.func [as dlopen] (node:electron/js2c/node_init:2:2559)\n    at Module._extensions..node (node:internal/modules/cjs/loader:1602:18)\n    at Object.func [as .node] (node:electron/js2c/node_init:2:2559)\n    at Module.load (node:internal/modules/cjs/loader:1295:32)\n    at Module._load (node:internal/modules/cjs/loader:1111:12)\n    at c._load (node:electron/js2c/node_init:2:16955)\n    at Module.require (node:internal/modules/cjs/loader:1318:19)\n    at require (node:internal/modules/helpers:179:18)\n    at bindings (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\bindings\\bindings.js:112:48)\n    at new Database (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\lib\\database.js:48:64)","timestamp":"2025-07-15T02:40:25.616Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.db.initialize is not a function","service":"trading-system","stack":"TypeError: this.db.initialize is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:128:27)\n    at initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:216:35)","timestamp":"2025-07-15T02:41:21.551Z"}
{"level":"error","message":"❌ Failed to initialize the application: this.db.initialize is not a function","service":"trading-system","stack":"TypeError: this.db.initialize is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:128:27)\n    at initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:216:35)","timestamp":"2025-07-15T02:41:21.551Z"}
{"code":"ERR_DLOPEN_FAILED","level":"error","message":"Failed to initialize Trading Orchestrator: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).","service":"trading-system","stack":"Error: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).\n    at process.func [as dlopen] (node:electron/js2c/node_init:2:2559)\n    at Module._extensions..node (node:internal/modules/cjs/loader:1602:18)\n    at Object.func [as .node] (node:electron/js2c/node_init:2:2559)\n    at Module.load (node:internal/modules/cjs/loader:1295:32)\n    at Module._load (node:internal/modules/cjs/loader:1111:12)\n    at c._load (node:electron/js2c/node_init:2:16955)\n    at Module.require (node:internal/modules/cjs/loader:1318:19)\n    at require (node:internal/modules/helpers:179:18)\n    at bindings (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\bindings\\bindings.js:112:48)\n    at new Database (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\lib\\database.js:48:64)","timestamp":"2025-07-15T02:41:59.556Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.db.exec is not a function","service":"trading-system","stack":"TypeError: this.db.exec is not a function\n    at UnifiedRiskManager.createRiskTables (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\risk\\UnifiedRiskManager.js:280:17)\n    at UnifiedRiskManager.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\risk\\UnifiedRiskManager.js:202:18)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:136:47)\n    at async initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:196:9)","timestamp":"2025-07-15T02:44:41.105Z"}
{"code":"ERR_DLOPEN_FAILED","level":"error","message":"Failed to initialize Trading Orchestrator: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).","service":"trading-system","stack":"Error: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).\n    at process.func [as dlopen] (node:electron/js2c/node_init:2:2559)\n    at Module._extensions..node (node:internal/modules/cjs/loader:1602:18)\n    at Object.func [as .node] (node:electron/js2c/node_init:2:2559)\n    at Module.load (node:internal/modules/cjs/loader:1295:32)\n    at Module._load (node:internal/modules/cjs/loader:1111:12)\n    at c._load (node:electron/js2c/node_init:2:16955)\n    at Module.require (node:internal/modules/cjs/loader:1318:19)\n    at require (node:internal/modules/helpers:179:18)\n    at bindings (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\bindings\\bindings.js:112:48)\n    at new Database (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\lib\\database.js:48:64)","timestamp":"2025-07-15T02:46:23.839Z"}
{"code":"ERR_DLOPEN_FAILED","level":"error","message":"Failed to initialize Trading Orchestrator: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).","service":"trading-system","stack":"Error: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).\n    at process.func [as dlopen] (node:electron/js2c/node_init:2:2559)\n    at Module._extensions..node (node:internal/modules/cjs/loader:1602:18)\n    at Object.func [as .node] (node:electron/js2c/node_init:2:2559)\n    at Module.load (node:internal/modules/cjs/loader:1295:32)\n    at Module._load (node:internal/modules/cjs/loader:1111:12)\n    at c._load (node:electron/js2c/node_init:2:16955)\n    at Module.require (node:internal/modules/cjs/loader:1318:19)\n    at require (node:internal/modules/helpers:179:18)\n    at bindings (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\bindings\\bindings.js:112:48)\n    at new Database (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\lib\\database.js:48:64)","timestamp":"2025-07-15T02:48:02.443Z"}
{"code":"ERR_DLOPEN_FAILED","level":"error","message":"❌ Failed to initialize the application: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).","service":"trading-system","stack":"Error: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).\n    at process.func [as dlopen] (node:electron/js2c/node_init:2:2559)\n    at Module._extensions..node (node:internal/modules/cjs/loader:1602:18)\n    at Object.func [as .node] (node:electron/js2c/node_init:2:2559)\n    at Module.load (node:internal/modules/cjs/loader:1295:32)\n    at Module._load (node:internal/modules/cjs/loader:1111:12)\n    at c._load (node:electron/js2c/node_init:2:16955)\n    at Module.require (node:internal/modules/cjs/loader:1318:19)\n    at require (node:internal/modules/helpers:179:18)\n    at bindings (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\bindings\\bindings.js:112:48)\n    at new Database (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\lib\\database.js:48:64)","timestamp":"2025-07-15T02:48:02.444Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: CircuitBreaker is not a constructor","service":"trading-system","stack":"TypeError: CircuitBreaker is not a constructor\n    at new ProductionTradingExecutor (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\ProductionTradingExecutor.js:42:31)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:162:47)\n    at async initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:196:9)","timestamp":"2025-07-15T02:51:37.927Z"}
{"level":"error","message":"Failed to load positions: Cannot read properties of undefined (reading 'prepare')","service":"trading-system","stack":"TypeError: Cannot read properties of undefined (reading 'prepare')\n    at DatabaseManager.prepare (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\helpers\\database-manager.js:333:24)\n    at ProductionTradingExecutor.loadPositions (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\ProductionTradingExecutor.js:394:29)\n    at ProductionTradingExecutor.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\ProductionTradingExecutor.js:52:24)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:177:49)\n    at async initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:196:9)","timestamp":"2025-07-15T02:53:38.929Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.db.all is not a function","service":"trading-system","stack":"TypeError: this.db.all is not a function\n    at MemeCoinScanner.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\MemeCoinScanner.js:62:49)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:176:49)\n    at async initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:196:9)","timestamp":"2025-07-15T02:56:41.255Z"}
{"level":"error","message":"❌ Failed to initialize the application: this.db.all is not a function","service":"trading-system","stack":"TypeError: this.db.all is not a function\n    at MemeCoinScanner.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\MemeCoinScanner.js:62:49)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:176:49)\n    at async initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:196:9)","timestamp":"2025-07-15T02:56:41.255Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.components.whaleTracker.initialize is not a function","service":"trading-system","stack":"TypeError: this.components.whaleTracker.initialize is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:182:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:13)\n    at async C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:54:17\n    at async TradingSystemTester.test (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:160:13)\n    at async TradingSystemTester.runTests (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:53:13)","timestamp":"2025-07-16T08:57:21.759Z"}
{"level":"error","message":"Failed to initialize Trading System: this.components.whaleTracker.initialize is not a function","service":"trading-system","stack":"TypeError: this.components.whaleTracker.initialize is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:182:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:13)\n    at async C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:54:17\n    at async TradingSystemTester.test (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:160:13)\n    at async TradingSystemTester.runTests (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:53:13)","timestamp":"2025-07-16T08:57:21.760Z"}
{"0":"t","1":"h","10":"n","11":"e","12":"n","13":"t","14":"s","15":".","16":"w","17":"h","18":"a","19":"l","2":"i","20":"e","21":"T","22":"r","23":"a","24":"c","25":"k","26":"e","27":"r","28":".","29":"i","3":"s","30":"n","31":"i","32":"t","33":"i","34":"a","35":"l","36":"i","37":"z","38":"e","39":" ","4":".","40":"i","41":"s","42":" ","43":"n","44":"o","45":"t","46":" ","47":"a","48":" ","49":"f","5":"c","50":"u","51":"n","52":"c","53":"t","54":"i","55":"o","56":"n","6":"o","7":"m","8":"p","9":"o","level":"error","message":"❌ System Initialization - FAILED (35ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.760Z"}
{"0":"t","1":"h","10":"n","11":"g","12":"O","13":"r","14":"c","15":"h","16":"e","17":"s","18":"t","19":"r","2":"i","20":"a","21":"t","22":"o","23":"r","24":".","25":"c","26":"o","27":"m","28":"p","29":"o","3":"s","30":"n","31":"e","32":"n","33":"t","34":"s","35":".","36":"d","37":"a","38":"t","39":"a","4":".","40":"C","41":"o","42":"l","43":"l","44":"e","45":"c","46":"t","47":"o","48":"r","49":".","5":"t","50":"g","51":"e","52":"t","53":"A","54":"v","55":"a","56":"i","57":"l","58":"a","59":"b","6":"r","60":"l","61":"e","62":"C","63":"o","64":"i","65":"n","66":"s","67":" ","68":"i","69":"s","7":"a","70":" ","71":"n","72":"o","73":"t","74":" ","75":"a","76":" ","77":"f","78":"u","79":"n","8":"d","80":"c","81":"t","82":"i","83":"o","84":"n","9":"i","level":"error","message":"❌ Get Coins - FAILED (0ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.783Z"}
{"0":"t","1":"h","10":"n","11":"g","12":"O","13":"r","14":"c","15":"h","16":"e","17":"s","18":"t","19":"r","2":"i","20":"a","21":"t","22":"o","23":"r","24":".","25":"c","26":"o","27":"m","28":"p","29":"o","3":"s","30":"n","31":"e","32":"n","33":"t","34":"s","35":".","36":"p","37":"e","38":"r","39":"f","4":".","40":"o","41":"r","42":"m","43":"a","44":"n","45":"c","46":"e","47":"T","48":"r","49":"a","5":"t","50":"c","51":"k","52":"e","53":"r","54":".","55":"g","56":"e","57":"t","58":"T","59":"r","6":"r","60":"a","61":"d","62":"i","63":"n","64":"g","65":"S","66":"t","67":"a","68":"t","69":"s","7":"a","70":" ","71":"i","72":"s","73":" ","74":"n","75":"o","76":"t","77":" ","78":"a","79":" ","8":"d","80":"f","81":"u","82":"n","83":"c","84":"t","85":"i","86":"o","87":"n","9":"i","level":"error","message":"❌ Get Trading Stats - FAILED (1ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.784Z"}
{"0":"t","1":"h","10":"n","11":"g","12":"O","13":"r","14":"c","15":"h","16":"e","17":"s","18":"t","19":"r","2":"i","20":"a","21":"t","22":"o","23":"r","24":".","25":"c","26":"o","27":"m","28":"p","29":"o","3":"s","30":"n","31":"e","32":"n","33":"t","34":"s","35":".","36":"p","37":"e","38":"r","39":"f","4":".","40":"o","41":"r","42":"m","43":"a","44":"n","45":"c","46":"e","47":"T","48":"r","49":"a","5":"t","50":"c","51":"k","52":"e","53":"r","54":".","55":"g","56":"e","57":"t","58":"P","59":"e","6":"r","60":"r","61":"f","62":"o","63":"r","64":"m","65":"a","66":"n","67":"c","68":"e","69":"M","7":"a","70":"e","71":"t","72":"r","73":"i","74":"c","75":"s","76":" ","77":"i","78":"s","79":" ","8":"d","80":"n","81":"o","82":"t","83":" ","84":"a","85":" ","86":"f","87":"u","88":"n","89":"c","9":"i","90":"t","91":"i","92":"o","93":"n","level":"error","message":"❌ Get Performance Metrics - FAILED (0ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.784Z"}
{"0":"t","1":"h","10":"n","11":"g","12":"O","13":"r","14":"c","15":"h","16":"e","17":"s","18":"t","19":"r","2":"i","20":"a","21":"t","22":"o","23":"r","24":".","25":"c","26":"o","27":"m","28":"p","29":"o","3":"s","30":"n","31":"e","32":"n","33":"t","34":"s","35":".","36":"p","37":"o","38":"r","39":"t","4":".","40":"f","41":"o","42":"l","43":"i","44":"o","45":"M","46":"a","47":"n","48":"a","49":"g","5":"t","50":"e","51":"r","52":".","53":"g","54":"e","55":"t","56":"W","57":"a","58":"l","59":"l","6":"r","60":"e","61":"t","62":"B","63":"a","64":"l","65":"a","66":"n","67":"c","68":"e","69":" ","7":"a","70":"i","71":"s","72":" ","73":"n","74":"o","75":"t","76":" ","77":"a","78":" ","79":"f","8":"d","80":"u","81":"n","82":"c","83":"t","84":"i","85":"o","86":"n","9":"i","level":"error","message":"❌ Get Wallet Balance - FAILED (0ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.784Z"}
{"0":"t","1":"h","10":"n","11":"g","12":"O","13":"r","14":"c","15":"h","16":"e","17":"s","18":"t","19":"r","2":"i","20":"a","21":"t","22":"o","23":"r","24":".","25":"c","26":"o","27":"m","28":"p","29":"o","3":"s","30":"n","31":"e","32":"n","33":"t","34":"s","35":".","36":"w","37":"h","38":"a","39":"l","4":".","40":"e","41":"T","42":"r","43":"a","44":"c","45":"k","46":"e","47":"r","48":".","49":"g","5":"t","50":"e","51":"t","52":"T","53":"r","54":"a","55":"c","56":"k","57":"e","58":"d","59":"W","6":"r","60":"h","61":"a","62":"l","63":"e","64":"s","65":" ","66":"i","67":"s","68":" ","69":"n","7":"a","70":"o","71":"t","72":" ","73":"a","74":" ","75":"f","76":"u","77":"n","78":"c","79":"t","8":"d","80":"i","81":"o","82":"n","9":"i","level":"error","message":"❌ Check Whale Tracker - FAILED (0ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.786Z"}
{"0":"t","1":"h","10":"n","11":"g","12":"O","13":"r","14":"c","15":"h","16":"e","17":"s","18":"t","19":"r","2":"i","20":"a","21":"t","22":"o","23":"r","24":".","25":"c","26":"o","27":"m","28":"p","29":"o","3":"s","30":"n","31":"e","32":"n","33":"t","34":"s","35":".","36":"m","37":"e","38":"m","39":"e","4":".","40":"C","41":"o","42":"i","43":"n","44":"S","45":"c","46":"a","47":"n","48":"n","49":"e","5":"t","50":"r","51":".","52":"g","53":"e","54":"t","55":"S","56":"t","57":"a","58":"t","59":"u","6":"r","60":"s","61":" ","62":"i","63":"s","64":" ","65":"n","66":"o","67":"t","68":" ","69":"a","7":"a","70":" ","71":"f","72":"u","73":"n","74":"c","75":"t","76":"i","77":"o","78":"n","8":"d","9":"i","level":"error","message":"❌ Check Meme Scanner Status - FAILED (0ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.786Z"}
{"level":"error","message":"- System Initialization: this.components.whaleTracker.initialize is not a function","service":"trading-system","timestamp":"2025-07-16T08:57:21.787Z"}
{"level":"error","message":"- Get Coins: this.tradingOrchestrator.components.dataCollector.getAvailableCoins is not a function","service":"trading-system","timestamp":"2025-07-16T08:57:21.788Z"}
{"level":"error","message":"- Get Trading Stats: this.tradingOrchestrator.components.performanceTracker.getTradingStats is not a function","service":"trading-system","timestamp":"2025-07-16T08:57:21.788Z"}
{"level":"error","message":"- Get Performance Metrics: this.tradingOrchestrator.components.performanceTracker.getPerformanceMetrics is not a function","service":"trading-system","timestamp":"2025-07-16T08:57:21.788Z"}
{"level":"error","message":"- Get Wallet Balance: this.tradingOrchestrator.components.portfolioManager.getWalletBalance is not a function","service":"trading-system","timestamp":"2025-07-16T08:57:21.788Z"}
{"level":"error","message":"- Check Whale Tracker: this.tradingOrchestrator.components.whaleTracker.getTrackedWhales is not a function","service":"trading-system","timestamp":"2025-07-16T08:57:21.788Z"}
{"level":"error","message":"- Check Meme Scanner Status: this.tradingOrchestrator.components.memeCoinScanner.getStatus is not a function","service":"trading-system","timestamp":"2025-07-16T08:57:21.788Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.components.whaleTracker.initialize is not a function","service":"trading-system","stack":"TypeError: this.components.whaleTracker.initialize is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:182:46)\n    at async initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:196:9)","timestamp":"2025-07-16T12:04:57.267Z"}
{"level":"error","message":"❌ Failed to initialize the application: this.components.whaleTracker.initialize is not a function","service":"trading-system","stack":"TypeError: this.components.whaleTracker.initialize is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:182:46)\n    at async initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:196:9)","timestamp":"2025-07-16T12:04:57.267Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.logger.error is not a function","service":"trading-system","stack":"TypeError: this.logger.error is not a function\n    at MemeCoinScanner.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\MemeCoinScanner.js:55:25)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:183:49)\n    at async initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:196:9)","timestamp":"2025-07-16T12:06:18.708Z"}
{"level":"error","message":"❌ Failed to initialize the application: this.logger.error is not a function","service":"trading-system","stack":"TypeError: this.logger.error is not a function\n    at MemeCoinScanner.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\MemeCoinScanner.js:55:25)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:183:49)\n    at async initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:196:9)","timestamp":"2025-07-16T12:06:18.708Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.components.exchangeManager.startHeartbeatMonitor is not a function","service":"trading-system","stack":"TypeError: this.components.exchangeManager.startHeartbeatMonitor is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:193:51)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:13)\n    at async testBackend (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-backend.js:45:13)","timestamp":"2025-07-17T14:42:11.902Z"}
{"level":"error","message":"Failed to initialize Trading System: this.components.exchangeManager.startHeartbeatMonitor is not a function","service":"trading-system","stack":"TypeError: this.components.exchangeManager.startHeartbeatMonitor is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:193:51)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:13)\n    at async testBackend (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-backend.js:45:13)","timestamp":"2025-07-17T14:42:11.902Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.components.exchangeManager.startHeartbeatMonitor is not a function","service":"trading-system","stack":"TypeError: this.components.exchangeManager.startHeartbeatMonitor is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:193:51)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:13)","timestamp":"2025-07-17T15:01:13.098Z"}
{"level":"error","message":"Failed to initialize Trading System: this.components.exchangeManager.startHeartbeatMonitor is not a function","service":"trading-system","stack":"TypeError: this.components.exchangeManager.startHeartbeatMonitor is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:193:51)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:13)","timestamp":"2025-07-17T15:01:13.099Z"}
{"level":"error","message":"Test suite failed: Converting circular structure to JSON\n    --> starting at object with constructor 'Timeout'\n    |     property '_idlePrev' -> object with constructor 'TimersList'\n    --- property '_idleNext' closes the circle","service":"trading-system","stack":"TypeError: Converting circular structure to JSON\n    --> starting at object with constructor 'Timeout'\n    |     property '_idlePrev' -> object with constructor 'TimersList'\n    --- property '_idleNext' closes the circle\n    at JSON.stringify (<anonymous>)\n    at DatabaseIntegrationTester.generateTestReport (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\tests\\test-database-integration.js:1069:45)\n    at async DatabaseIntegrationTester.runAllTests (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\tests\\test-database-integration.js:61:13)","timestamp":"2025-07-18T08:44:44.871Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: Unexpected token '('","service":"trading-system","stack":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\config\\config-manager.js:45\n        this.initializationPromise = this.(options);\n                                          ^\n\nSyntaxError: Unexpected token '('\n    at wrapSafe (node:internal/modules/cjs/loader:1383:18)\n    at Module._compile (node:internal/modules/cjs/loader:1412:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)\n    at Module.load (node:internal/modules/cjs/loader:1282:32)\n    at Module._load (node:internal/modules/cjs/loader:1098:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)\n    at Module.require (node:internal/modules/cjs/loader:1304:12)\n    at require (node:internal/modules/helpers:123:16)\n    at TradingOrchestrator.createComponentInstances (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:218:31)","timestamp":"2025-07-19T12:47:21.588Z"}
{"level":"error","message":"Failed to initialize Trading System: Unexpected token '('","service":"trading-system","stack":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\config\\config-manager.js:45\n        this.initializationPromise = this.(options);\n                                          ^\n\nSyntaxError: Unexpected token '('\n    at wrapSafe (node:internal/modules/cjs/loader:1383:18)\n    at Module._compile (node:internal/modules/cjs/loader:1412:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)\n    at Module.load (node:internal/modules/cjs/loader:1282:32)\n    at Module._load (node:internal/modules/cjs/loader:1098:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)\n    at Module.require (node:internal/modules/cjs/loader:1304:12)\n    at require (node:internal/modules/helpers:123:16)\n    at TradingOrchestrator.createComponentInstances (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:218:31)","timestamp":"2025-07-19T12:47:21.588Z"}
{"0":"U","1":"n","10":" ","11":"t","12":"o","13":"k","14":"e","15":"n","16":" ","17":"'","18":"(","19":"'","2":"e","3":"x","4":"p","5":"e","6":"c","7":"t","8":"e","9":"d","level":"error","message":"❌ System Initialization - FAILED (17ms)","service":"trading-system","timestamp":"2025-07-19T12:47:21.588Z"}
{"level":"error","message":"- System Initialization: Unexpected token '('","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: Unexpected token '('","service":"trading-system","stack":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\config\\config-manager.js:45\n        this.initializationPromise = this.(options);\n                                          ^\n\nSyntaxError: Unexpected token '('\n    at wrapSafe (node:internal/modules/cjs/loader:1383:18)\n    at Module._compile (node:internal/modules/cjs/loader:1412:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)\n    at Module.load (node:internal/modules/cjs/loader:1282:32)\n    at Module._load (node:internal/modules/cjs/loader:1098:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)\n    at Module.require (node:internal/modules/cjs/loader:1304:12)\n    at require (node:internal/modules/helpers:123:16)\n    at TradingOrchestrator.createComponentInstances (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:218:31)","timestamp":"2025-07-19T12:51:15.147Z"}
{"level":"error","message":"Failed to initialize Trading System: Unexpected token '('","service":"trading-system","stack":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\config\\config-manager.js:45\n        this.initializationPromise = this.(options);\n                                          ^\n\nSyntaxError: Unexpected token '('\n    at wrapSafe (node:internal/modules/cjs/loader:1383:18)\n    at Module._compile (node:internal/modules/cjs/loader:1412:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)\n    at Module.load (node:internal/modules/cjs/loader:1282:32)\n    at Module._load (node:internal/modules/cjs/loader:1098:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)\n    at Module.require (node:internal/modules/cjs/loader:1304:12)\n    at require (node:internal/modules/helpers:123:16)\n    at TradingOrchestrator.createComponentInstances (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:218:31)","timestamp":"2025-07-19T12:51:15.147Z"}
{"0":"U","1":"n","10":" ","11":"t","12":"o","13":"k","14":"e","15":"n","16":" ","17":"'","18":"(","19":"'","2":"e","3":"x","4":"p","5":"e","6":"c","7":"t","8":"e","9":"d","level":"error","message":"❌ System Initialization - FAILED (16ms)","service":"trading-system","timestamp":"2025-07-19T12:51:15.147Z"}
{"level":"error","message":"- System Initialization: Unexpected token '('","service":"trading-system","timestamp":"2025-07-19T12:51:15.149Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: Unexpected token '('","service":"trading-system","stack":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\config\\config-manager.js:45\n        this.initializationPromise = this.(options);\n                                          ^\n\nSyntaxError: Unexpected token '('\n    at wrapSafe (node:internal/modules/cjs/loader:1383:18)\n    at Module._compile (node:internal/modules/cjs/loader:1412:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)\n    at Module.load (node:internal/modules/cjs/loader:1282:32)\n    at Module._load (node:internal/modules/cjs/loader:1098:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)\n    at Module.require (node:internal/modules/cjs/loader:1304:12)\n    at require (node:internal/modules/helpers:123:16)\n    at TradingOrchestrator.createComponentInstances (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:218:31)","timestamp":"2025-07-19T12:51:57.538Z"}
{"level":"error","message":"Failed to initialize Trading System: Unexpected token '('","service":"trading-system","stack":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\config\\config-manager.js:45\n        this.initializationPromise = this.(options);\n                                          ^\n\nSyntaxError: Unexpected token '('\n    at wrapSafe (node:internal/modules/cjs/loader:1383:18)\n    at Module._compile (node:internal/modules/cjs/loader:1412:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)\n    at Module.load (node:internal/modules/cjs/loader:1282:32)\n    at Module._load (node:internal/modules/cjs/loader:1098:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)\n    at Module.require (node:internal/modules/cjs/loader:1304:12)\n    at require (node:internal/modules/helpers:123:16)\n    at TradingOrchestrator.createComponentInstances (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:218:31)","timestamp":"2025-07-19T12:51:57.539Z"}
{"0":"U","1":"n","10":" ","11":"t","12":"o","13":"k","14":"e","15":"n","16":" ","17":"'","18":"(","19":"'","2":"e","3":"x","4":"p","5":"e","6":"c","7":"t","8":"e","9":"d","level":"error","message":"❌ System Initialization - FAILED (13ms)","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"level":"error","message":"- System Initialization: Unexpected token '('","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: Unexpected token ','","service":"trading-system","stack":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\config\\configuration-loader.js:116\n            configDir: options.configDir || path.join(, '../../config'),\n                                                      ^\n\nSyntaxError: Unexpected token ','\n    at wrapSafe (node:internal/modules/cjs/loader:1383:18)\n    at Module._compile (node:internal/modules/cjs/loader:1412:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)\n    at Module.load (node:internal/modules/cjs/loader:1282:32)\n    at Module._load (node:internal/modules/cjs/loader:1098:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)\n    at Module.require (node:internal/modules/cjs/loader:1304:12)\n    at require (node:internal/modules/helpers:123:16)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\config\\config-manager.js:6:31)","timestamp":"2025-07-19T14:33:37.888Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: Cannot read properties of null (reading 'on')","service":"trading-system","stack":"TypeError: Cannot read properties of null (reading 'on')\n    at TradingOrchestrator.setupEventListeners (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:561:41)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:178:18)\n    at async testIntegration ([eval]:14:9)","timestamp":"2025-07-19T14:34:38.474Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: Cannot read properties of null (reading 'on')","service":"trading-system","stack":"TypeError: Cannot read properties of null (reading 'on')\n    at TradingOrchestrator.setupEventListeners (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:561:41)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:178:18)\n    at async testIntegration ([eval]:13:9)","timestamp":"2025-07-19T15:07:15.396Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: Cannot read properties of null (reading 'on')","service":"trading-system","stack":"TypeError: Cannot read properties of null (reading 'on')\n    at TradingOrchestrator.setupEventListeners (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:561:41)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:178:18)\n    at async testIntegration ([eval]:13:9)","timestamp":"2025-07-19T15:07:30.472Z"}
{"level":"error","message":"Error in test operation: test error","service":"trading-system","stack":"Error: test error\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:22:31\n    at Function.operation (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\utils\\ErrorHandlingUtils.js:14:26)\n    at Object.safeAsync (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:20:57)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-20T18:48:16.104Z"}
{"level":"error","message":"Error in test operation: test error","service":"trading-system","stack":"Error: test error\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:46:31\n    at Function.operation [as safeAsyncWithResult] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\utils\\ErrorHandlingUtils.js:23:32)\n    at Object.safeAsyncWithResult (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:44:57)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-20T18:48:16.116Z"}
{"level":"error","message":"Circuit breaker opened for test after 2 failures","service":"trading-system","timestamp":"2025-07-20T18:48:16.128Z"}
{"level":"error","message":"Error in Order execution: Order parameters missing required fields: quantity","service":"trading-system","stack":"Error: Order parameters missing required fields: quantity\n    at Function.validateRequired (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\utils\\ErrorHandlingUtils.js:92:19)\n    at validateRequired (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:205:48)\n    at Function.operation [as safeAsyncWithResult] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\utils\\ErrorHandlingUtils.js:23:32)\n    at Object.safeAsyncWithResult [as executeOrder] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:203:47)\n    at Object.executeOrder (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:238:58)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-20T18:48:16.248Z"}
{"level":"error","message":"Error in Order execution: Invalid symbol","service":"trading-system","stack":"Error: Invalid symbol\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:212:39\n    at Function.operation [as safeAsyncWithResult] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\utils\\ErrorHandlingUtils.js:23:32)\n    at Object.safeAsyncWithResult [as executeOrder] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:203:47)\n    at Object.executeOrder (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:249:58)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-20T18:48:16.249Z"}
{"level":"error","message":"Circuit breaker opened for service after 2 failures","service":"trading-system","timestamp":"2025-07-20T18:48:19.273Z"}
{"level":"error","message":"Error during cleanup: Cleanup failed","service":"trading-system","stack":"Error: Cleanup failed\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:328:58)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-20T18:48:19.275Z"}
{"level":"error","message":"[EnhancedComponentInitializer] ❌ Component critical-component initialization failed (attempt 1): Initialization failed","service":"trading-system","stack":"Error: Initialization failed\n    at Object.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\verify-enhanced-error-handling.js:94:27)\n    at EnhancedComponentInitializer.performComponentInitialization (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\orchestration\\enhanced-component-initializer.js:439:34)\n    at EnhancedComponentInitializer.initializeComponentWithRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\orchestration\\enhanced-component-initializer.js:349:28)\n    at verifyEnhancedErrorHandling (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\verify-enhanced-error-handling.js:107:51)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-20T20:09:23.399Z"}
{"level":"error","message":"[EnhancedComponentInitializer] ❌ Component critical-component initialization failed (attempt 2): Initialization failed","service":"trading-system","stack":"Error: Initialization failed\n    at Object.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\verify-enhanced-error-handling.js:94:27)\n    at EnhancedComponentInitializer.performComponentInitialization (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\orchestration\\enhanced-component-initializer.js:439:34)\n    at EnhancedComponentInitializer.initializeComponentWithRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\orchestration\\enhanced-component-initializer.js:349:28)\n    at async verifyEnhancedErrorHandling (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\verify-enhanced-error-handling.js:107:24)","timestamp":"2025-07-20T20:09:25.406Z"}

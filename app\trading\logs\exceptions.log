[2025-07-21 06:11:13.889] ERROR: uncaughtException: Unexpected token ','
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\SecureCredentialManager.js:46
            dbPath: config.dbPath || path.join(, '../../databases/credentials.db'),
                                               ^

SyntaxError: Unexpected token ','
    at wrapSafe (node:internal/modules/cjs/loader:1383:18)
    at Module._compile (node:internal/modules/cjs/loader:1412:20)
    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)
    at Module.load (node:internal/modules/cjs/loader:1282:32)
    at Module._load (node:internal/modules/cjs/loader:1098:12)
    at TracingChannel.traceSync (node:diagnostics_channel:315:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)
    at Module.require (node:internal/modules/cjs/loader:1304:12)
    at require (node:internal/modules/helpers:123:16)
    at Object.<anonymous> (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\ai\AutonomousTrader.js:10:33) {"error":{},"exception":true,"date":"Mon Jul 21 2025 06:11:13 GMT+0200 (sentraleuropeisk sommertid)","process":{"pid":41120,"uid":null,"gid":null,"cwd":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading","execPath":"C:\\nvm4w\\nodejs\\node.exe","version":"v22.4.0","argv":["C:\\nvm4w\\nodejs\\node.exe","C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-component-integration.js"],"memoryUsage":{"rss":91234304,"heapTotal":68329472,"heapUsed":36353000,"external":2052998,"arrayBuffers":93675}},"os":{"loadavg":[0,0,0],"uptime":51522.531},"trace":[{"column":18,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1383,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1412,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1551,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1282,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1098,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":315,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":215,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1304,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":123,"method":null,"native":false},{"column":33,"file":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\ai\\AutonomousTrader.js","function":null,"line":10,"method":null,"native":false}],"service":"trading-system"}
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\SecureCredentialManager.js:46
            dbPath: config.dbPath || path.join(, '../../databases/credentials.db'),
                                               ^

SyntaxError: Unexpected token ','
    at wrapSafe (node:internal/modules/cjs/loader:1383:18)
    at Module._compile (node:internal/modules/cjs/loader:1412:20)
    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)
    at Module.load (node:internal/modules/cjs/loader:1282:32)
    at Module._load (node:internal/modules/cjs/loader:1098:12)
    at TracingChannel.traceSync (node:diagnostics_channel:315:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)
    at Module.require (node:internal/modules/cjs/loader:1304:12)
    at require (node:internal/modules/helpers:123:16)
    at Object.<anonymous> (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\ai\AutonomousTrader.js:10:33)
[2025-07-21 13:57:09.851] ERROR: uncaughtException: Cannot find module '../../shared/helpers/logger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\ai\AutonomousTrader.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\test-ipc-standardized-error-handling.js
Error: Cannot find module '../../shared/helpers/logger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\ai\AutonomousTrader.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\test-ipc-standardized-error-handling.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1219:15)
    at Module._load (node:internal/modules/cjs/loader:1045:27)
    at TracingChannel.traceSync (node:diagnostics_channel:315:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)
    at Module.require (node:internal/modules/cjs/loader:1304:12)
    at require (node:internal/modules/helpers:123:16)
    at Object.<anonymous> (C:\Users\<USER>\Documents\electronTrader\app\trading\ai\AutonomousTrader.js:2:16)
    at Module._compile (node:internal/modules/cjs/loader:1467:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)
    at Module.load (node:internal/modules/cjs/loader:1282:32) {"error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\ai\\AutonomousTrader.js","C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\orchestration\\TradingOrchestrator.js","C:\\Users\\<USER>\\Documents\\electronTrader\\app\\test-ipc-standardized-error-handling.js"]},"exception":true,"date":"Mon Jul 21 2025 13:57:09 GMT+0200 (sentraleuropeisk sommertid)","process":{"pid":36820,"uid":null,"gid":null,"cwd":"C:\\Users\\<USER>\\Documents\\electronTrader\\app","execPath":"C:\\nvm4w\\nodejs\\node.exe","version":"v22.4.0","argv":["C:\\nvm4w\\nodejs\\node.exe","C:\\Users\\<USER>\\Documents\\electronTrader\\app\\test-ipc-standardized-error-handling.js"],"memoryUsage":{"rss":41840640,"heapTotal":10678272,"heapUsed":6234744,"external":1536821,"arrayBuffers":10515}},"os":{"loadavg":[0,0,0],"uptime":79478.484},"trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1219,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1045,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":315,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":215,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1304,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":123,"method":null,"native":false},{"column":16,"file":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\ai\\AutonomousTrader.js","function":null,"line":2,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1467,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1551,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1282,"method":"load","native":false}],"service":"trading-system"}
Error: Cannot find module '../../shared/helpers/logger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\ai\AutonomousTrader.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\test-ipc-standardized-error-handling.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1219:15)
    at Module._load (node:internal/modules/cjs/loader:1045:27)
    at TracingChannel.traceSync (node:diagnostics_channel:315:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)
    at Module.require (node:internal/modules/cjs/loader:1304:12)
    at require (node:internal/modules/helpers:123:16)
    at Object.<anonymous> (C:\Users\<USER>\Documents\electronTrader\app\trading\ai\AutonomousTrader.js:2:16)
    at Module._compile (node:internal/modules/cjs/loader:1467:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)
    at Module.load (node:internal/modules/cjs/loader:1282:32)
[2025-07-21 19:12:48.258] ERROR: uncaughtException: IPCService is not a constructor
[eval]:1
console.log('Testing IPC Service import...'); const IPCService = require('./src/services/ipcService.js'); console.log('✅ IPC Service imported successfully'); const service = new IPCService(); console.log('✅ IPC Service instantiated successfully'); console.log('Configuration:', service.getConfiguration());
                                                                                                                                                                              ^

TypeError: IPCService is not a constructor
    at [eval]:1:175
    at runScriptInThisContext (node:internal/vm:209:10)
    at node:internal/process/execution:118:14
    at [eval]-wrapper:6:24
    at runScript (node:internal/process/execution:101:62)
    at evalScript (node:internal/process/execution:136:3)
    at node:internal/main/eval_string:51:3 {"error":{},"exception":true,"date":"Mon Jul 21 2025 19:12:48 GMT+0200 (sentraleuropeisk sommertid)","process":{"pid":24892,"uid":null,"gid":null,"cwd":"C:\\Users\\<USER>\\Documents\\electronTrader\\app","execPath":"C:\\nvm4w\\nodejs\\node.exe","version":"v22.4.0","argv":["C:\\nvm4w\\nodejs\\node.exe"],"memoryUsage":{"rss":43548672,"heapTotal":10940416,"heapUsed":6304192,"external":1599518,"arrayBuffers":10515}},"os":{"loadavg":[0,0,0],"uptime":98416.89},"trace":[{"column":175,"file":"[eval]","function":null,"line":1,"method":null,"native":false},{"column":10,"file":"node:internal/vm","function":"runScriptInThisContext","line":209,"method":null,"native":false},{"column":14,"file":"node:internal/process/execution","function":null,"line":118,"method":null,"native":false},{"column":24,"file":"[eval]-wrapper","function":null,"line":6,"method":null,"native":false},{"column":62,"file":"node:internal/process/execution","function":"runScript","line":101,"method":null,"native":false},{"column":3,"file":"node:internal/process/execution","function":"evalScript","line":136,"method":null,"native":false},{"column":3,"file":"node:internal/main/eval_string","function":null,"line":51,"method":null,"native":false}],"service":"trading-system"}
[eval]:1
console.log('Testing IPC Service import...'); const IPCService = require('./src/services/ipcService.js'); console.log('✅ IPC Service imported successfully'); const service = new IPCService(); console.log('✅ IPC Service instantiated successfully'); console.log('Configuration:', service.getConfiguration());
                                                                                                                                                                              ^

TypeError: IPCService is not a constructor
    at [eval]:1:175
    at runScriptInThisContext (node:internal/vm:209:10)
    at node:internal/process/execution:118:14
    at [eval]-wrapper:6:24
    at runScript (node:internal/process/execution:101:62)
    at evalScript (node:internal/process/execution:136:3)
    at node:internal/main/eval_string:51:3
[2025-07-22 16:35:10.634] ERROR: uncaughtException: Cannot use import statement outside a module
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\data-collection\DataCollector.js:2
import path from 'path';
^^^^^^

SyntaxError: Cannot use import statement outside a module
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1328:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1432:10)
    at Module.load (node:internal/modules/cjs/loader:1215:32)
    at Module._load (node:internal/modules/cjs/loader:1031:12)
    at c._load (node:electron/js2c/node_init:2:17025)
    at Module.require (node:internal/modules/cjs/loader:1240:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js:7:23)
    at Module._compile (node:internal/modules/cjs/loader:1373:14) {"error":{},"exception":true,"date":"Tue Jul 22 2025 16:35:10 GMT+0200 (sentraleuropeisk sommertid)","process":{"pid":34720,"uid":null,"gid":null,"cwd":"C:\\Users\\<USER>\\Documents\\electronTrader\\app","execPath":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\electron\\dist\\electron.exe","version":"v20.15.0","argv":["C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\electron\\dist\\electron.exe","."],"memoryUsage":{"rss":97845248,"heapTotal":47857664,"heapUsed":35476068,"external":2828676,"arrayBuffers":0}},"os":{"loadavg":[0,0,0],"uptime":16192.265},"trace":[{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1288,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1432,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1215,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1031,"method":"_load","native":false},{"column":17025,"file":"node:electron/js2c/node_init","function":"c._load","line":2,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1240,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":23,"file":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\orchestration\\TradingOrchestrator.js","function":null,"line":7,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1373,"method":"_compile","native":false}],"service":"trading-system"}
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\data-collection\DataCollector.js:2
import path from 'path';
^^^^^^

SyntaxError: Cannot use import statement outside a module
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1328:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1432:10)
    at Module.load (node:internal/modules/cjs/loader:1215:32)
    at Module._load (node:internal/modules/cjs/loader:1031:12)
    at c._load (node:electron/js2c/node_init:2:17025)
    at Module.require (node:internal/modules/cjs/loader:1240:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js:7:23)
    at Module._compile (node:internal/modules/cjs/loader:1373:14)
[2025-07-22 16:36:16.900] ERROR: uncaughtException: Cannot find module '../logging/EnhancedLogger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\main.js
Error: Cannot find module '../logging/EnhancedLogger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\main.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1152:15)
    at s._resolveFilename (node:electron/js2c/browser_init:2:121098)
    at Module._load (node:internal/modules/cjs/loader:993:27)
    at c._load (node:electron/js2c/node_init:2:17025)
    at Module.require (node:internal/modules/cjs/loader:1240:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js:10:24)
    at Module._compile (node:internal/modules/cjs/loader:1373:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1432:10)
    at Module.load (node:internal/modules/cjs/loader:1215:32) {"error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\error-handling\\ErrorHandler.js","C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\orchestration\\TradingOrchestrator.js","C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js"]},"exception":true,"date":"Tue Jul 22 2025 16:36:16 GMT+0200 (sentraleuropeisk sommertid)","process":{"pid":19428,"uid":null,"gid":null,"cwd":"C:\\Users\\<USER>\\Documents\\electronTrader\\app","execPath":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\electron\\dist\\electron.exe","version":"v20.15.0","argv":["C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\electron\\dist\\electron.exe","."],"memoryUsage":{"rss":101113856,"heapTotal":51265536,"heapUsed":35458628,"external":2864686,"arrayBuffers":0}},"os":{"loadavg":[0,0,0],"uptime":16258.531},"trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1152,"method":"_resolveFilename","native":false},{"column":121098,"file":"node:electron/js2c/browser_init","function":"s._resolveFilename","line":2,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":993,"method":"_load","native":false},{"column":17025,"file":"node:electron/js2c/node_init","function":"c._load","line":2,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1240,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":24,"file":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\error-handling\\ErrorHandler.js","function":null,"line":10,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1373,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1432,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1215,"method":"load","native":false}],"service":"trading-system"}
Error: Cannot find module '../logging/EnhancedLogger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\main.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1152:15)
    at s._resolveFilename (node:electron/js2c/browser_init:2:121098)
    at Module._load (node:internal/modules/cjs/loader:993:27)
    at c._load (node:electron/js2c/node_init:2:17025)
    at Module.require (node:internal/modules/cjs/loader:1240:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js:10:24)
    at Module._compile (node:internal/modules/cjs/loader:1373:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1432:10)
    at Module.load (node:internal/modules/cjs/loader:1215:32)
[2025-07-22 16:38:39.783] ERROR: uncaughtException: Cannot find module '../logging/EnhancedLogger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\main.js
Error: Cannot find module '../logging/EnhancedLogger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\main.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1152:15)
    at s._resolveFilename (node:electron/js2c/browser_init:2:121098)
    at Module._load (node:internal/modules/cjs/loader:993:27)
    at c._load (node:electron/js2c/node_init:2:17025)
    at Module.require (node:internal/modules/cjs/loader:1240:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js:10:24)
    at Module._compile (node:internal/modules/cjs/loader:1373:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1432:10)
    at Module.load (node:internal/modules/cjs/loader:1215:32) {"error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\error-handling\\ErrorHandler.js","C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\orchestration\\TradingOrchestrator.js","C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js"]},"exception":true,"date":"Tue Jul 22 2025 16:38:39 GMT+0200 (sentraleuropeisk sommertid)","process":{"pid":37172,"uid":null,"gid":null,"cwd":"C:\\Users\\<USER>\\Documents\\electronTrader\\app","execPath":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\electron\\dist\\electron.exe","version":"v20.15.0","argv":["C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\electron\\dist\\electron.exe","."],"memoryUsage":{"rss":101437440,"heapTotal":51527680,"heapUsed":35815676,"external":2864686,"arrayBuffers":0}},"os":{"loadavg":[0,0,0],"uptime":16401.421},"trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1152,"method":"_resolveFilename","native":false},{"column":121098,"file":"node:electron/js2c/browser_init","function":"s._resolveFilename","line":2,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":993,"method":"_load","native":false},{"column":17025,"file":"node:electron/js2c/node_init","function":"c._load","line":2,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1240,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":24,"file":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\error-handling\\ErrorHandler.js","function":null,"line":10,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1373,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1432,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1215,"method":"load","native":false}],"service":"trading-system"}
Error: Cannot find module '../logging/EnhancedLogger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\main.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1152:15)
    at s._resolveFilename (node:electron/js2c/browser_init:2:121098)
    at Module._load (node:internal/modules/cjs/loader:993:27)
    at c._load (node:electron/js2c/node_init:2:17025)
    at Module.require (node:internal/modules/cjs/loader:1240:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js:10:24)
    at Module._compile (node:internal/modules/cjs/loader:1373:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1432:10)
    at Module.load (node:internal/modules/cjs/loader:1215:32)
[2025-07-22 17:30:00.057] ERROR: uncaughtException: Cannot find module '../logging/EnhancedLogger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\main.js
Error: Cannot find module '../logging/EnhancedLogger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\main.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1152:15)
    at s._resolveFilename (node:electron/js2c/browser_init:2:121098)
    at Module._load (node:internal/modules/cjs/loader:993:27)
    at c._load (node:electron/js2c/node_init:2:17025)
    at Module.require (node:internal/modules/cjs/loader:1240:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js:10:24)
    at Module._compile (node:internal/modules/cjs/loader:1373:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1432:10)
    at Module.load (node:internal/modules/cjs/loader:1215:32) {"error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\error-handling\\ErrorHandler.js","C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\orchestration\\TradingOrchestrator.js","C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js"]},"exception":true,"date":"Tue Jul 22 2025 17:30:00 GMT+0200 (sentraleuropeisk sommertid)","process":{"pid":30952,"uid":null,"gid":null,"cwd":"C:\\Users\\<USER>\\Documents\\electronTrader\\app","execPath":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\electron\\dist\\electron.exe","version":"v20.15.0","argv":["C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\electron\\dist\\electron.exe","."],"memoryUsage":{"rss":100200448,"heapTotal":50741248,"heapUsed":35821132,"external":2864686,"arrayBuffers":0}},"os":{"loadavg":[0,0,0],"uptime":19481.687},"trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1152,"method":"_resolveFilename","native":false},{"column":121098,"file":"node:electron/js2c/browser_init","function":"s._resolveFilename","line":2,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":993,"method":"_load","native":false},{"column":17025,"file":"node:electron/js2c/node_init","function":"c._load","line":2,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1240,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":24,"file":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\error-handling\\ErrorHandler.js","function":null,"line":10,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1373,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1432,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1215,"method":"load","native":false}],"service":"trading-system"}
Error: Cannot find module '../logging/EnhancedLogger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\main.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1152:15)
    at s._resolveFilename (node:electron/js2c/browser_init:2:121098)
    at Module._load (node:internal/modules/cjs/loader:993:27)
    at c._load (node:electron/js2c/node_init:2:17025)
    at Module.require (node:internal/modules/cjs/loader:1240:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js:10:24)
    at Module._compile (node:internal/modules/cjs/loader:1373:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1432:10)
    at Module.load (node:internal/modules/cjs/loader:1215:32)
[2025-07-22 17:35:28.488] ERROR: uncaughtException: Cannot find module '../logging/EnhancedLogger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\main.js
Error: Cannot find module '../logging/EnhancedLogger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\main.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1152:15)
    at s._resolveFilename (node:electron/js2c/browser_init:2:121098)
    at Module._load (node:internal/modules/cjs/loader:993:27)
    at c._load (node:electron/js2c/node_init:2:17025)
    at Module.require (node:internal/modules/cjs/loader:1240:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js:10:24)
    at Module._compile (node:internal/modules/cjs/loader:1373:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1432:10)
    at Module.load (node:internal/modules/cjs/loader:1215:32) {"error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\error-handling\\ErrorHandler.js","C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\orchestration\\TradingOrchestrator.js","C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js"]},"exception":true,"date":"Tue Jul 22 2025 17:35:28 GMT+0200 (sentraleuropeisk sommertid)","process":{"pid":30068,"uid":null,"gid":null,"cwd":"C:\\Users\\<USER>\\Documents\\electronTrader\\app","execPath":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\electron\\dist\\electron.exe","version":"v20.15.0","argv":["C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\electron\\dist\\electron.exe","."],"memoryUsage":{"rss":100937728,"heapTotal":51265536,"heapUsed":35839724,"external":2864686,"arrayBuffers":0}},"os":{"loadavg":[0,0,0],"uptime":19810.125},"trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1152,"method":"_resolveFilename","native":false},{"column":121098,"file":"node:electron/js2c/browser_init","function":"s._resolveFilename","line":2,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":993,"method":"_load","native":false},{"column":17025,"file":"node:electron/js2c/node_init","function":"c._load","line":2,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1240,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":24,"file":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\error-handling\\ErrorHandler.js","function":null,"line":10,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1373,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1432,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1215,"method":"load","native":false}],"service":"trading-system"}
Error: Cannot find module '../logging/EnhancedLogger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\main.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1152:15)
    at s._resolveFilename (node:electron/js2c/browser_init:2:121098)
    at Module._load (node:internal/modules/cjs/loader:993:27)
    at c._load (node:electron/js2c/node_init:2:17025)
    at Module.require (node:internal/modules/cjs/loader:1240:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js:10:24)
    at Module._compile (node:internal/modules/cjs/loader:1373:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1432:10)
    at Module.load (node:internal/modules/cjs/loader:1215:32)
[2025-07-22 17:40:02.811] ERROR: uncaughtException: Cannot find module '../logging/EnhancedLogger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\main.js
Error: Cannot find module '../logging/EnhancedLogger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\main.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1152:15)
    at s._resolveFilename (node:electron/js2c/browser_init:2:121098)
    at Module._load (node:internal/modules/cjs/loader:993:27)
    at c._load (node:electron/js2c/node_init:2:17025)
    at Module.require (node:internal/modules/cjs/loader:1240:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js:10:24)
    at Module._compile (node:internal/modules/cjs/loader:1373:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1432:10)
    at Module.load (node:internal/modules/cjs/loader:1215:32) {"error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\error-handling\\ErrorHandler.js","C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\orchestration\\TradingOrchestrator.js","C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js"]},"exception":true,"date":"Tue Jul 22 2025 17:40:02 GMT+0200 (sentraleuropeisk sommertid)","process":{"pid":37796,"uid":null,"gid":null,"cwd":"C:\\Users\\<USER>\\Documents\\electronTrader\\app","execPath":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\electron\\dist\\electron.exe","version":"v20.15.0","argv":["C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\electron\\dist\\electron.exe","."],"memoryUsage":{"rss":100749312,"heapTotal":51265536,"heapUsed":35806464,"external":2864686,"arrayBuffers":0}},"os":{"loadavg":[0,0,0],"uptime":20084.453},"trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1152,"method":"_resolveFilename","native":false},{"column":121098,"file":"node:electron/js2c/browser_init","function":"s._resolveFilename","line":2,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":993,"method":"_load","native":false},{"column":17025,"file":"node:electron/js2c/node_init","function":"c._load","line":2,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1240,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":24,"file":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\error-handling\\ErrorHandler.js","function":null,"line":10,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1373,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1432,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1215,"method":"load","native":false}],"service":"trading-system"}
Error: Cannot find module '../logging/EnhancedLogger'
Require stack:
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js
- C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
- C:\Users\<USER>\Documents\electronTrader\app\main.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1152:15)
    at s._resolveFilename (node:electron/js2c/browser_init:2:121098)
    at Module._load (node:internal/modules/cjs/loader:993:27)
    at c._load (node:electron/js2c/node_init:2:17025)
    at Module.require (node:internal/modules/cjs/loader:1240:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js:10:24)
    at Module._compile (node:internal/modules/cjs/loader:1373:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1432:10)
    at Module.load (node:internal/modules/cjs/loader:1215:32)

{"timestamp": "2025-07-30T19:18:36.633Z", "error": {"message": "Exchange manager not found", "stack": "Error: Exchange manager not found\n    at AutonomousStartup.initializeExchanges (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:325:23)\n    at AutonomousStartup.start (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:125:24)", "phase": "unknown"}, "environment": {"nodeVersion": "v24.4.1", "platform": "win32", "env": "development"}}
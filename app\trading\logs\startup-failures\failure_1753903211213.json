{"timestamp": "2025-07-30T19:20:11.213Z", "error": {"message": "Cannot read properties of undefined (reading 'connect')", "stack": "TypeError: Cannot read properties of undefined (reading 'connect')\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:70:40)\n    at TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:44)\n    at AutonomousStartup.startTradingSystem (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:374:38)\n    at AutonomousStartup.start (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:128:24)", "phase": "unknown"}, "environment": {"nodeVersion": "v24.4.1", "platform": "win32", "env": "development"}}
{"timestamp": "2025-07-30T19:21:17.242Z", "error": {"message": "Cannot read properties of undefined (reading 'exchanges')", "stack": "TypeError: Cannot read properties of undefined (reading 'exchanges')\n    at EnhancedEliteWhaleTracker.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\whaletrader\\EnhancedEliteWhaleTracker.js:37:18)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:73:32)\n    at TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:44)\n    at AutonomousStartup.startTradingSystem (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:374:38)\n    at AutonomousStartup.start (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:128:24)", "phase": "unknown"}, "environment": {"nodeVersion": "v24.4.1", "platform": "win32", "env": "development"}}
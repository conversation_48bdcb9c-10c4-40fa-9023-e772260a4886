{"timestamp": "2025-07-31T00:28:33.002Z", "error": {"message": "Unexpected token '...'", "stack": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\ccxt\\engines\\CCXT-Exchange-Manager.js:52\n    ...\n    ^^^\n\nSyntaxError: Unexpected token '...'\n    at wrapSafe (node:internal/modules/cjs/loader:1624:18)\n    at Module._compile (node:internal/modules/cjs/loader:1666:20)\n    at Object..js (node:internal/modules/cjs/loader:1824:10)\n    at Module.load (node:internal/modules/cjs/loader:1427:32)\n    at Module._load (node:internal/modules/cjs/loader:1250:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1449:12)\n    at require (node:internal/modules/helpers:135:16)\n    at AutonomousStartup.initializeExchanges (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:339:35)", "phase": "unknown"}, "environment": {"nodeVersion": "v24.4.1", "platform": "win32", "env": "development"}}
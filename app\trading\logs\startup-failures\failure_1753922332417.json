{"timestamp": "2025-07-31T00:38:52.417Z", "error": {"message": "Unsupported database driver: undefined", "stack": "Error: Unsupported database driver: undefined\n    at new DatabaseIntegration (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\data\\databases\\DatabaseIntegration.js:34:13)\n    at new EnhancedEliteWhaleTracker (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\whaletrader\\EnhancedEliteWhaleTracker.js:16:15)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:73:28)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:7)\n    at async AutonomousStartup.startTradingSystem (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:397:7)\n    at async AutonomousStartup.start (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:143:7)", "phase": "unknown"}, "environment": {"nodeVersion": "v24.4.1", "platform": "win32", "env": "development"}}
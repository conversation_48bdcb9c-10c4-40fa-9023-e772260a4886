{"timestamp":"2025-07-21T00:15:19.789Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:15:19.787Z","level":"INFO","message":"🚀 Starting TradingOrchestrator initialization...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:15:19.788Z","level":"INFO","message":"Progress: 10% - Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:15:19.788Z","level":"INFO","message":"📊 Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:15:19.789Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:15:19.844Z","level":"INFO","message":"✅ Successfully initialized 3 databases","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:15:19.844Z","level":"INFO","message":"🔍 Starting database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:15:19.844Z","level":"INFO","message":"Progress: 30% - Checking database health...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:15:19.882Z","level":"INFO","message":"✅ Database health monitoring started","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:15:19.928Z","level":"INFO","message":"Progress: 50% - Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:15:19.928Z","level":"INFO","message":"🔧 Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:15:20.029Z","level":"INFO","message":"✅ Trading components initialized (placeholder)","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:15:20.030Z","level":"INFO","message":"Progress: 100% - Initialization complete","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:15:20.030Z","level":"INFO","message":"✅ TradingOrchestrator initialization completed successfully","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:15:20.066Z","level":"INFO","message":"Health report saved: C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\logs\\database-health-report.json","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:15:20.067Z","level":"INFO","message":"🚀 Starting trading system...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:16:54.450Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:16:54.447Z","level":"INFO","message":"🚀 Starting TradingOrchestrator initialization...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:16:54.448Z","level":"INFO","message":"Progress: 10% - Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:16:54.448Z","level":"INFO","message":"📊 Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:16:54.449Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:16:54.489Z","level":"INFO","message":"✅ Successfully initialized 3 databases","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:16:54.490Z","level":"INFO","message":"🔍 Starting database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:16:54.490Z","level":"INFO","message":"Progress: 30% - Checking database health...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:16:54.525Z","level":"INFO","message":"✅ Database health monitoring started","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:16:54.555Z","level":"INFO","message":"Progress: 50% - Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:16:54.556Z","level":"INFO","message":"🔧 Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:16:54.657Z","level":"INFO","message":"✅ Trading components initialized (placeholder)","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:16:54.661Z","level":"INFO","message":"🛑 Stopping database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.817Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.813Z","level":"INFO","message":"🚀 Starting TradingOrchestrator initialization...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.814Z","level":"INFO","message":"Progress: 10% - Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.815Z","level":"INFO","message":"📊 Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.816Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.817Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.891Z","level":"INFO","message":"✅ Successfully initialized 3 databases","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.892Z","level":"INFO","message":"🔍 Starting database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.891Z","level":"INFO","message":"Progress: 30% - Checking database health...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.937Z","level":"INFO","message":"✅ Database health monitoring started","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.968Z","level":"INFO","message":"Progress: 40% - Initializing data persistence...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.969Z","level":"INFO","message":"🔧 Initializing Data Persistence Manager...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.970Z","level":"INFO","message":"✅ Connected to trading database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.970Z","level":"INFO","message":"✅ Connected to n8n database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.971Z","level":"INFO","message":"✅ Connected to credentials database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.971Z","level":"INFO","message":"Progress: 60% - Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.971Z","level":"INFO","message":"✅ Data Persistence Manager initialized successfully","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:04.971Z","level":"INFO","message":"🔧 Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:05.084Z","level":"INFO","message":"✅ Trading components initialized (placeholder)","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:05.085Z","level":"INFO","message":"Progress: 100% - Initialization complete","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:05.085Z","level":"INFO","message":"✅ TradingOrchestrator initialization completed successfully","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:05.089Z","level":"INFO","message":"🛑 Stopping trading system...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:05.090Z","level":"INFO","message":"🛑 Stopping database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:05.091Z","level":"INFO","message":"🔌 Closing database connections...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:05.090Z","level":"INFO","message":"✅ Database health monitoring stopped","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:05.096Z","level":"INFO","message":"✅ Closed connection to trading","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:05.097Z","level":"INFO","message":"✅ Closed connection to n8n","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:05.097Z","level":"INFO","message":"✅ Closed connection to credentials","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:05.098Z","level":"INFO","message":"✅ Trading system stopped","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.188Z","level":"INFO","message":"🚀 Starting TradingOrchestrator initialization...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.190Z","level":"INFO","message":"Progress: 10% - Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.191Z","level":"INFO","message":"📊 Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.193Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.194Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.193Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.240Z","level":"INFO","message":"✅ Successfully initialized 3 databases","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.241Z","level":"INFO","message":"🔍 Starting database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.240Z","level":"INFO","message":"Progress: 30% - Checking database health...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.310Z","level":"INFO","message":"✅ Database health monitoring started","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.355Z","level":"INFO","message":"Progress: 40% - Initializing data persistence...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.355Z","level":"INFO","message":"🔧 Initializing Data Persistence Manager...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.356Z","level":"INFO","message":"✅ Connected to trading database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.356Z","level":"INFO","message":"✅ Connected to n8n database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.357Z","level":"INFO","message":"✅ Connected to credentials database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.358Z","level":"INFO","message":"Progress: 60% - Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.357Z","level":"INFO","message":"✅ Data Persistence Manager initialized successfully","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.358Z","level":"INFO","message":"🔧 Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.471Z","level":"INFO","message":"✅ Trading components initialized (placeholder)","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.471Z","level":"INFO","message":"Progress: 100% - Initialization complete","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.471Z","level":"INFO","message":"✅ TradingOrchestrator initialization completed successfully","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.475Z","level":"INFO","message":"🛑 Stopping trading system...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.476Z","level":"INFO","message":"🛑 Stopping database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.476Z","level":"INFO","message":"🔌 Closing database connections...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.476Z","level":"INFO","message":"✅ Database health monitoring stopped","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.482Z","level":"INFO","message":"✅ Closed connection to trading","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.483Z","level":"INFO","message":"✅ Closed connection to n8n","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.483Z","level":"INFO","message":"✅ Closed connection to credentials","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:22:50.484Z","level":"INFO","message":"✅ Trading system stopped","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:50.945Z","level":"INFO","message":"🚀 Starting TradingOrchestrator initialization...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:50.946Z","level":"INFO","message":"Progress: 10% - Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:50.947Z","level":"INFO","message":"📊 Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:50.948Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:50.948Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:50.948Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:50.986Z","level":"INFO","message":"✅ Successfully initialized 3 databases","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:50.986Z","level":"INFO","message":"🔍 Starting database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:50.986Z","level":"INFO","message":"Progress: 30% - Checking database health...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.026Z","level":"INFO","message":"✅ Database health monitoring started","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.072Z","level":"INFO","message":"Progress: 40% - Initializing data persistence...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.072Z","level":"INFO","message":"🔧 Initializing Data Persistence Manager...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.073Z","level":"INFO","message":"✅ Connected to trading database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.073Z","level":"INFO","message":"✅ Connected to n8n database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.074Z","level":"INFO","message":"✅ Connected to credentials database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.075Z","level":"INFO","message":"Progress: 60% - Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.075Z","level":"INFO","message":"🔧 Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.074Z","level":"INFO","message":"✅ Data Persistence Manager initialized successfully","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.189Z","level":"INFO","message":"✅ Trading components initialized (placeholder)","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.190Z","level":"INFO","message":"Progress: 100% - Initialization complete","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.190Z","level":"INFO","message":"✅ TradingOrchestrator initialization completed successfully","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.194Z","level":"INFO","message":"🛑 Stopping trading system...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.195Z","level":"INFO","message":"🛑 Stopping database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.195Z","level":"INFO","message":"🔌 Closing database connections...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.195Z","level":"INFO","message":"✅ Database health monitoring stopped","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.201Z","level":"INFO","message":"✅ Closed connection to trading","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.202Z","level":"INFO","message":"✅ Closed connection to n8n","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.202Z","level":"INFO","message":"✅ Closed connection to credentials","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:23:51.203Z","level":"INFO","message":"✅ Trading system stopped","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.598Z","level":"INFO","message":"🚀 Starting TradingOrchestrator initialization...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.599Z","level":"INFO","message":"Progress: 10% - Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.600Z","level":"INFO","message":"📊 Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.601Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.601Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.601Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.637Z","level":"INFO","message":"✅ Successfully initialized 3 databases","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.638Z","level":"INFO","message":"🔍 Starting database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.637Z","level":"INFO","message":"Progress: 30% - Checking database health...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.678Z","level":"INFO","message":"✅ Database health monitoring started","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.710Z","level":"INFO","message":"Progress: 40% - Initializing data persistence...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.710Z","level":"INFO","message":"🔧 Initializing Data Persistence Manager...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.711Z","level":"INFO","message":"✅ Connected to trading database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.711Z","level":"INFO","message":"✅ Connected to n8n database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.712Z","level":"INFO","message":"✅ Connected to credentials database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.712Z","level":"INFO","message":"Progress: 60% - Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.712Z","level":"INFO","message":"✅ Data Persistence Manager initialized successfully","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.712Z","level":"INFO","message":"🔧 Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.826Z","level":"INFO","message":"✅ Trading components initialized (placeholder)","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.826Z","level":"INFO","message":"Progress: 100% - Initialization complete","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.826Z","level":"INFO","message":"✅ TradingOrchestrator initialization completed successfully","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.830Z","level":"INFO","message":"🛑 Stopping trading system...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.830Z","level":"INFO","message":"🛑 Stopping database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.830Z","level":"INFO","message":"🔌 Closing database connections...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.830Z","level":"INFO","message":"✅ Database health monitoring stopped","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.837Z","level":"INFO","message":"✅ Closed connection to trading","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.837Z","level":"INFO","message":"✅ Closed connection to n8n","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.837Z","level":"INFO","message":"✅ Closed connection to credentials","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:13.838Z","level":"INFO","message":"✅ Trading system stopped","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.568Z","level":"INFO","message":"🚀 Starting TradingOrchestrator initialization...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.569Z","level":"INFO","message":"Progress: 10% - Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.570Z","level":"INFO","message":"📊 Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.571Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.571Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.571Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.610Z","level":"INFO","message":"✅ Successfully initialized 3 databases","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.611Z","level":"INFO","message":"🔍 Starting database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.610Z","level":"INFO","message":"Progress: 30% - Checking database health...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.662Z","level":"INFO","message":"✅ Database health monitoring started","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.694Z","level":"INFO","message":"Progress: 40% - Initializing data persistence...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.694Z","level":"INFO","message":"🔧 Initializing Data Persistence Manager...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.695Z","level":"INFO","message":"✅ Connected to trading database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.696Z","level":"INFO","message":"✅ Connected to n8n database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.696Z","level":"INFO","message":"✅ Connected to credentials database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.697Z","level":"INFO","message":"Progress: 60% - Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.696Z","level":"INFO","message":"✅ Data Persistence Manager initialized successfully","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.697Z","level":"INFO","message":"🔧 Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.810Z","level":"INFO","message":"✅ Trading components initialized (placeholder)","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.810Z","level":"INFO","message":"Progress: 100% - Initialization complete","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.810Z","level":"INFO","message":"✅ TradingOrchestrator initialization completed successfully","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.827Z","level":"INFO","message":"🛑 Stopping trading system...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.827Z","level":"INFO","message":"🛑 Stopping database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.828Z","level":"INFO","message":"🔌 Closing database connections...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.827Z","level":"INFO","message":"✅ Database health monitoring stopped","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.837Z","level":"INFO","message":"✅ Closed connection to trading","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.837Z","level":"INFO","message":"✅ Closed connection to n8n","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.838Z","level":"INFO","message":"✅ Closed connection to credentials","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:24:30.838Z","level":"INFO","message":"✅ Trading system stopped","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.882Z","level":"INFO","message":"🚀 Starting TradingOrchestrator initialization...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.883Z","level":"INFO","message":"Progress: 10% - Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.884Z","level":"INFO","message":"📊 Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.885Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.886Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.886Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.914Z","level":"INFO","message":"✅ Successfully initialized 3 databases","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.914Z","level":"INFO","message":"🔍 Starting database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.914Z","level":"INFO","message":"Progress: 30% - Checking database health...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.946Z","level":"INFO","message":"✅ Database health monitoring started","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.975Z","level":"INFO","message":"Progress: 40% - Initializing data persistence...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.976Z","level":"INFO","message":"🔧 Initializing Data Persistence Manager...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.977Z","level":"INFO","message":"✅ Connected to trading database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.978Z","level":"INFO","message":"✅ Connected to n8n database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.978Z","level":"INFO","message":"✅ Connected to credentials database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.979Z","level":"INFO","message":"Progress: 60% - Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.978Z","level":"INFO","message":"✅ Data Persistence Manager initialized successfully","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:10.979Z","level":"INFO","message":"🔧 Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:11.093Z","level":"INFO","message":"✅ Trading components initialized (placeholder)","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:11.094Z","level":"INFO","message":"Progress: 100% - Initialization complete","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:11.094Z","level":"INFO","message":"✅ TradingOrchestrator initialization completed successfully","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:11.098Z","level":"INFO","message":"🛑 Stopping trading system...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:11.098Z","level":"INFO","message":"🛑 Stopping database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:11.099Z","level":"INFO","message":"🔌 Closing database connections...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:11.099Z","level":"INFO","message":"✅ Database health monitoring stopped","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:11.101Z","level":"INFO","message":"✅ Closed connection to trading","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:11.101Z","level":"INFO","message":"✅ Closed connection to n8n","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:11.102Z","level":"INFO","message":"✅ Closed connection to credentials","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:11.102Z","level":"INFO","message":"✅ Trading system stopped","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.402Z","level":"INFO","message":"🚀 Starting TradingOrchestrator initialization...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.403Z","level":"INFO","message":"Progress: 10% - Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.403Z","level":"INFO","message":"📊 Initializing databases...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.404Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.405Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.405Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.431Z","level":"INFO","message":"✅ Successfully initialized 3 databases","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.431Z","level":"INFO","message":"🔍 Starting database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.431Z","level":"INFO","message":"Progress: 30% - Checking database health...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.463Z","level":"INFO","message":"✅ Database health monitoring started","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.494Z","level":"INFO","message":"Progress: 40% - Initializing data persistence...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.494Z","level":"INFO","message":"🔧 Initializing Data Persistence Manager...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.495Z","level":"INFO","message":"✅ Connected to trading database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.496Z","level":"INFO","message":"✅ Connected to n8n database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.496Z","level":"INFO","message":"✅ Connected to credentials database","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.497Z","level":"INFO","message":"Progress: 60% - Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.496Z","level":"INFO","message":"✅ Data Persistence Manager initialized successfully","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.497Z","level":"INFO","message":"🔧 Initializing trading components...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.611Z","level":"INFO","message":"✅ Trading components initialized (placeholder)","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.612Z","level":"INFO","message":"Progress: 100% - Initialization complete","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.612Z","level":"INFO","message":"✅ TradingOrchestrator initialization completed successfully","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.658Z","level":"INFO","message":"🛑 Stopping trading system...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.659Z","level":"INFO","message":"🛑 Stopping database health monitoring...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.659Z","level":"INFO","message":"🔌 Closing database connections...","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.659Z","level":"INFO","message":"✅ Database health monitoring stopped","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.663Z","level":"INFO","message":"✅ Closed connection to trading","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.665Z","level":"INFO","message":"✅ Closed connection to n8n","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.666Z","level":"INFO","message":"✅ Closed connection to credentials","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T00:25:31.666Z","level":"INFO","message":"✅ Trading system stopped","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T04:58:32.385Z","level":"WARN","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","context":{"circuitBreaker":null,"recovery":null}}
{"timestamp":"2025-07-21T04:58:32.811Z","level":"INFO","message":"Enhanced Logger initialized","context":{"circuitBreaker":null,"recovery":null}}

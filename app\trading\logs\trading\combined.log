{"level":"info","message":"Starting database initialization...","service":"trading-system","timestamp":"2025-07-06T07:00:17.947Z"}
{"level":"info","message":"Database opened successfully","service":"trading-system","timestamp":"2025-07-06T07:00:17.952Z"}
{"level":"info","message":"Database initialization complete","service":"trading-system","timestamp":"2025-07-06T07:00:17.960Z"}
{"code":"ERR_DLOPEN_FAILED","level":"error","message":"Database connection failed /mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/build/Release/better_sqlite3.node: invalid ELF header","service":"trading-system","stack":"Error: /mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/build/Release/better_sqlite3.node: invalid ELF header\n    at Object..node (node:internal/modules/cjs/loader:1921:18)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at bindings (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/bindings/bindings.js:112:48)\n    at new Database (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/lib/database.js:48:64)\n    at DatabaseManager.connect (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/helpers/database-manager.js:118:23)","timestamp":"2025-07-10T17:36:28.080Z"}
{"code":"ERR_DLOPEN_FAILED","level":"error","message":"Database connection failed /mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/build/Release/better_sqlite3.node: invalid ELF header","service":"trading-system","stack":"Error: /mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/build/Release/better_sqlite3.node: invalid ELF header\n    at Object..node (node:internal/modules/cjs/loader:1921:18)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at bindings (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/bindings/bindings.js:112:48)\n    at new Database (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/lib/database.js:48:64)\n    at DatabaseManager.connect (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/helpers/database-manager.js:174:23)","timestamp":"2025-07-10T17:40:29.270Z"}
{"code":"ERR_DLOPEN_FAILED","level":"error","message":"Database connection failed /mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/build/Release/better_sqlite3.node: invalid ELF header","service":"trading-system","stack":"Error: /mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/build/Release/better_sqlite3.node: invalid ELF header\n    at Object..node (node:internal/modules/cjs/loader:1921:18)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at bindings (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/bindings/bindings.js:112:48)\n    at new Database (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/node_modules/better-sqlite3/lib/database.js:48:64)\n    at DatabaseManager.connect (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/helpers/database-manager.js:118:23)","timestamp":"2025-07-10T17:44:31.782Z"}
{"level":"info","message":"Storage initialized","service":"trading-system","timestamp":"2025-07-10T17:57:46.766Z"}
{"level":"info","message":"Machine key derived successfully","service":"trading-system","timestamp":"2025-07-10T17:57:46.781Z"}
{"level":"info","message":"SecureCredentialManager initialized successfully","service":"trading-system","timestamp":"2025-07-10T17:57:46.782Z"}
{"level":"info","message":"Credentials stored for service: binance","service":"trading-system","timestamp":"2025-07-10T17:57:46.782Z"}
{"level":"info","message":"Credentials retrieved for service: binance","service":"trading-system","timestamp":"2025-07-10T17:57:46.783Z"}
{"level":"info","message":"SecureCredentialManager locked","service":"trading-system","timestamp":"2025-07-10T17:57:46.783Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-10T18:02:56.538Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-10T18:05:38.403Z"}
{"level":"error","message":"Error loading database configuration: Validation error for database: \"development\" is not allowed","service":"trading-system","stack":"Error: Validation error for database: \"development\" is not allowed\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:355:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:225:41)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:27:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:506:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-10T18:06:28.786Z"}
{"level":"error","message":"Error loading configurations: Validation error for database: \"development\" is not allowed","service":"trading-system","stack":"Error: Validation error for database: \"development\" is not allowed\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:355:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:225:41)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:27:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:506:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-10T18:06:28.787Z"}
{"level":"info","message":"Starting database initialization...","service":"trading-system","timestamp":"2025-07-10T21:54:17.642Z"}
{"level":"info","message":"Database opened successfully","service":"trading-system","timestamp":"2025-07-10T21:54:17.655Z"}
{"level":"info","message":"Running database migrations...","service":"trading-system","timestamp":"2025-07-10T21:54:17.655Z"}
{"level":"info","message":"-- 🚀 ENHANCED MEME COIN TRADING SCHEMA\r\n-- Optimized for high-frequency meme coin trading on N8N\r\n-- Version: 2.0 - SQLite Compatible\r\n\r\n-- =====================================================\r\n-- MEME COIN SPECIFIC TABLES\r\n-- =====================================================\r\n\r\n-- Meme Coin Metadata Table\r\nCREATE TABLE IF NOT EXISTS meme_coins\r\n(\r\n    id\r\n    INTEGER\r\n    PRIMARY\r\n    KEY\r\n    AUTOINCREMENT,\r\n    token_address\r\n    VARCHAR\r\n(\r\n    50\r\n) NOT NULL,\r\n    chain VARCHAR\r\n(\r\n    20\r\n) NOT NULL,\r\n    symbol VARCHAR\r\n(\r\n    20\r\n) NOT NULL,\r\n    name VARCHAR\r\n(\r\n    100\r\n),\r\n    launch_date TIMESTAMP,\r\n    honeypot_score INTEGER DEFAULT 0 CHECK\r\n(\r\n    honeypot_score\r\n    >=\r\n    0\r\n    AND\r\n    honeypot_score\r\n    <=\r\n    100\r\n),\r\n    rug_pull_score INTEGER DEFAULT 0 CHECK\r\n(\r\n    rug_pull_score\r\n    >=\r\n    0\r\n    AND\r\n    rug_pull_score\r\n    <=\r\n    100\r\n),\r\n    social_score INTEGER DEFAULT 0 CHECK\r\n(\r\n    social_score\r\n    >=\r\n    0\r\n    AND\r\n    social_score\r\n    <=\r\n    100\r\n),\r\n    holder_count INTEGER DEFAULT 0,\r\n    top_10_holders_percent DECIMAL\r\n(\r\n    5,\r\n    2\r\n),\r\n    liquidity_locked BOOLEAN DEFAULT false,\r\n    contract_verified BOOLEAN DEFAULT false,\r\n    creator_wallet VARCHAR\r\n(\r\n    50\r\n),\r\n    is_trending BOOLEAN DEFAULT false,\r\n    pump_probability INTEGER DEFAULT 50,\r\n    dump_risk_score INTEGER DEFAULT 50,\r\n    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\r\n    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\r\n    UNIQUE\r\n(\r\n    token_address,\r\n    chain\r\n)\r\n    );","service":"trading-system","timestamp":"2025-07-10T21:54:17.664Z"}
{"level":"info","message":"-- Social Sentiment Tracking\r\nCREATE TABLE IF NOT EXISTS social_sentiment\r\n(\r\n    id\r\n    INTEGER\r\n    PRIMARY\r\n    KEY\r\n    AUTOINCREMENT,\r\n    token_address\r\n    VARCHAR\r\n(\r\n    50\r\n) NOT NULL,\r\n    chain VARCHAR\r\n(\r\n    20\r\n) NOT NULL,\r\n    platform VARCHAR\r\n(\r\n    20\r\n) NOT NULL CHECK\r\n(\r\n    platform\r\n    IN\r\n(\r\n    'twitter',\r\n    'telegram',\r\n    'discord',\r\n    'reddit',\r\n    '4chan'\r\n)),\r\n    sentiment_score DECIMAL\r\n(\r\n    5,\r\n    2\r\n) CHECK\r\n(\r\n    sentiment_score\r\n    >=\r\n    -\r\n    100\r\n    AND\r\n    sentiment_score\r\n    <=\r\n    100\r\n),\r\n    volume_24h INTEGER DEFAULT 0,\r\n    volume_change_percent DECIMAL\r\n(\r\n    8,\r\n    2\r\n),\r\n    mentions_count INTEGER DEFAULT 0,\r\n    unique_users INTEGER DEFAULT 0,\r\n    influencer_mentions INTEGER DEFAULT 0,\r\n    fud_score INTEGER DEFAULT 0,\r\n    fomo_score INTEGER DEFAULT 0,\r\n    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\r\n    FOREIGN KEY\r\n(\r\n    token_address,\r\n    chain\r\n) REFERENCES meme_coins\r\n(\r\n    token_address,\r\n    chain\r\n)\r\n    );","service":"trading-system","timestamp":"2025-07-10T21:54:17.665Z"}
{"level":"info","message":"-- Pump Detection Signals\r\nCREATE TABLE IF NOT EXISTS pump_signals\r\n(\r\n    id\r\n    INTEGER\r\n    PRIMARY\r\n    KEY\r\n    AUTOINCREMENT,\r\n    token_address\r\n    VARCHAR\r\n(\r\n    50\r\n) NOT NULL,\r\n    chain VARCHAR\r\n(\r\n    20\r\n) NOT NULL,\r\n    signal_type VARCHAR\r\n(\r\n    30\r\n) NOT NULL CHECK\r\n(\r\n    signal_type\r\n    IN\r\n(\r\n    'VOLUME_SPIKE',\r\n    'WHALE_ACCUMULATION',\r\n    'SOCIAL_BUZZ',\r\n    'PRICE_BREAKOUT',\r\n    'LIQUIDITY_ADD'\r\n)),\r\n    signal_strength INTEGER CHECK\r\n(\r\n    signal_strength\r\n    >=\r\n    1\r\n    AND\r\n    signal_strength\r\n    <=\r\n    10\r\n),\r\n    confidence_score DECIMAL\r\n(\r\n    5,\r\n    2\r\n) CHECK\r\n(\r\n    confidence_score\r\n    >=\r\n    0\r\n    AND\r\n    confidence_score\r\n    <=\r\n    100\r\n),\r\n    expected_pump_percent DECIMAL\r\n(\r\n    8,\r\n    2\r\n),\r\n    timeframe_minutes INTEGER DEFAULT 60,\r\n    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\r\n    expires_at TIMESTAMP,\r\n    is_active BOOLEAN DEFAULT true,\r\n    FOREIGN KEY\r\n(\r\n    token_address,\r\n    chain\r\n) REFERENCES meme_coins\r\n(\r\n    token_address,\r\n    chain\r\n)\r\n    );","service":"trading-system","timestamp":"2025-07-10T21:54:17.666Z"}
{"level":"info","message":"-- Sniper Bot Configuration\r\nCREATE TABLE IF NOT EXISTS sniper_config\r\n(\r\n    id\r\n    INTEGER\r\n    PRIMARY\r\n    KEY\r\n    AUTOINCREMENT,\r\n    chain\r\n    VARCHAR\r\n(\r\n    20\r\n) NOT NULL,\r\n    dex VARCHAR\r\n(\r\n    30\r\n) NOT NULL,\r\n    gas_price_gwei DECIMAL\r\n(\r\n    10,\r\n    2\r\n),\r\n    gas_limit INTEGER DEFAULT 300000,\r\n    slippage_percent DECIMAL\r\n(\r\n    5,\r\n    2\r\n) DEFAULT 15.0,\r\n    buy_tax_max_percent DECIMAL\r\n(\r\n    5,\r\n    2\r\n) DEFAULT 10.0,\r\n    sell_tax_max_percent DECIMAL\r\n(\r\n    5,\r\n    2\r\n) DEFAULT 10.0,\r\n    min_liquidity_usd DECIMAL\r\n(\r\n    10,\r\n    2\r\n) DEFAULT 5000,\r\n    max_liquidity_usd DECIMAL\r\n(\r\n    12,\r\n    2\r\n) DEFAULT 1000000,\r\n    block_delay INTEGER DEFAULT 0,\r\n    frontrun_protection BOOLEAN DEFAULT true,\r\n    sandwich_protection BOOLEAN DEFAULT true,\r\n    is_active BOOLEAN DEFAULT true,\r\n    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\r\n    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\r\n    UNIQUE\r\n(\r\n    chain,\r\n    dex\r\n)\r\n    );","service":"trading-system","timestamp":"2025-07-10T21:54:17.667Z"}
{"level":"info","message":"-- MEV Protection Settings\r\nCREATE TABLE IF NOT EXISTS mev_protection\r\n(\r\n    id\r\n    INTEGER\r\n    PRIMARY\r\n    KEY\r\n    AUTOINCREMENT,\r\n    chain\r\n    VARCHAR\r\n(\r\n    20\r\n) NOT NULL,\r\n    protection_type VARCHAR\r\n(\r\n    30\r\n) NOT NULL CHECK\r\n(\r\n    protection_type\r\n    IN\r\n(\r\n    'FLASHBOTS',\r\n    'PRIVATE_MEMPOOL',\r\n    'COMMIT_REVEAL',\r\n    'TIME_DELAY'\r\n)),\r\n    rpc_endpoint VARCHAR\r\n(\r\n    200\r\n),\r\n    api_key_encrypted VARCHAR\r\n(\r\n    500\r\n),\r\n    max_priority_fee_gwei DECIMAL\r\n(\r\n    10,\r\n    2\r\n),\r\n    bundle_timeout_ms INTEGER DEFAULT 5000,\r\n    retry_attempts INTEGER DEFAULT 3,\r\n    is_enabled BOOLEAN DEFAULT true,\r\n    success_rate DECIMAL\r\n(\r\n    5,\r\n    2\r\n) DEFAULT 0,\r\n    total_protected_trades INTEGER DEFAULT 0,\r\n    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\r\n    );","service":"trading-system","timestamp":"2025-07-10T21:54:17.667Z"}
{"level":"info","message":"-- =====================================================\r\n-- ENHANCED EXISTING TABLES\r\n-- =====================================================\r\n\r\n-- System Configuration Table\r\nCREATE TABLE IF NOT EXISTS system_config\r\n(\r\n    id\r\n    INTEGER\r\n    PRIMARY\r\n    KEY\r\n    AUTOINCREMENT,\r\n    config_key\r\n    VARCHAR\r\n(\r\n    50\r\n) UNIQUE NOT NULL,\r\n    config_value TEXT,\r\n    description TEXT,\r\n    config_type VARCHAR\r\n(\r\n    20\r\n) DEFAULT 'string',\r\n    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\r\n    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\r\n    );","service":"trading-system","timestamp":"2025-07-10T21:54:17.668Z"}
{"level":"info","message":"-- Add meme coin specific columns to system_config (SQLite compatible)\r\nWITH\r\n    type_bool AS (SELECT 'boolean' AS t),\r\n    type_dec AS (SELECT 'decimal' AS t),\r\n    type_int AS (SELECT 'integer' AS t)\r\nINSERT OR IGNORE INTO system_config (config_key, config_value, description, config_type)\r\nSELECT 'sniper_mode_enabled', 'true', 'Enable token sniping for new launches', t FROM type_bool UNION ALL\r\nSELECT 'auto_sell_multiplier', '2.0', 'Auto sell when token reaches X multiplier', t FROM type_dec UNION ALL\r\nSELECT 'rug_pull_exit_seconds', '30', 'Seconds to exit position on rug pull detection', t FROM type_int UNION ALL\r\nSELECT 'max_slippage_percent', '20.0', 'Maximum allowed slippage for meme coins', t FROM type_dec UNION ALL\r\nSELECT 'social_sentiment_weight', '0.4', 'Weight of social sentiment in trading decisions', t FROM type_dec UNION ALL\r\nSELECT 'whale_copy_trading_enabled', 'true', 'Copy trades from profitable whales', t FROM type_bool UNION ALL\r\nSELECT 'pump_detection_sensitivity', '7', 'Pump detection sensitivity (1-10)', t FROM type_int UNION ALL\r\nSELECT 'fomo_protection_enabled', 'true', 'Prevent FOMO buying at peaks', t FROM type_bool UNION ALL\r\nSELECT 'high_risk_mode_enabled', 'false', 'High risk, high reward mode', t FROM type_bool UNION ALL\r\nSELECT 'min_market_cap_usd', '10000', 'Minimum market cap for trading', t FROM type_int\r\n;","service":"trading-system","timestamp":"2025-07-10T21:54:17.669Z"}
{"level":"info","message":"-- =====================================================\r\n-- PERFORMANCE TRACKING TABLES\r\n-- =====================================================\r\n\r\n-- Token Performance Analytics\r\nCREATE TABLE IF NOT EXISTS token_performance\r\n(\r\n    id\r\n    INTEGER\r\n    PRIMARY\r\n    KEY\r\n    AUTOINCREMENT,\r\n    token_address\r\n    VARCHAR\r\n(\r\n    50\r\n) NOT NULL,\r\n    chain VARCHAR\r\n(\r\n    20\r\n) NOT NULL,\r\n    entry_price DECIMAL\r\n(\r\n    18,\r\n    8\r\n),\r\n    exit_price DECIMAL\r\n(\r\n    18,\r\n    8\r\n),\r\n    entry_mc_usd DECIMAL\r\n(\r\n    15,\r\n    2\r\n),\r\n    exit_mc_usd DECIMAL\r\n(\r\n    15,\r\n    2\r\n),\r\n    holding_time_minutes INTEGER,\r\n    pnl_usd DECIMAL\r\n(\r\n    10,\r\n    2\r\n),\r\n    pnl_percent DECIMAL\r\n(\r\n    8,\r\n    2\r\n),\r\n    max_drawdown_percent DECIMAL\r\n(\r\n    8,\r\n    2\r\n),\r\n    trade_type VARCHAR\r\n(\r\n    20\r\n) CHECK\r\n(\r\n    trade_type\r\n    IN\r\n(\r\n    'SNIPE',\r\n    'SWING',\r\n    'SCALP',\r\n    'WHALE_COPY'\r\n)),\r\n    exit_reason VARCHAR\r\n(\r\n    50\r\n),\r\n    gas_cost_usd DECIMAL\r\n(\r\n    8,\r\n    2\r\n),\r\n    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\r\n    FOREIGN KEY\r\n(\r\n    token_address,\r\n    chain\r\n) REFERENCES meme_coins\r\n(\r\n    token_address,\r\n    chain\r\n)\r\n    );","service":"trading-system","timestamp":"2025-07-10T21:54:17.669Z"}
{"level":"info","message":"-- =====================================================\r\n-- ADVANCED INDEXES FOR MEME COIN TRADING\r\n-- =====================================================\r\n\r\nCREATE INDEX IF NOT EXISTS idx_meme_coins_trending ON meme_coins(is_trending, pump_probability DESC);","service":"trading-system","timestamp":"2025-07-10T21:54:17.670Z"}
{"level":"info","message":"CREATE INDEX IF NOT EXISTS idx_meme_coins_safety ON meme_coins(honeypot_score, rug_pull_score);","service":"trading-system","timestamp":"2025-07-10T21:54:17.670Z"}
{"level":"info","message":"CREATE INDEX IF NOT EXISTS idx_social_sentiment_recent ON social_sentiment(recorded_at DESC);","service":"trading-system","timestamp":"2025-07-10T21:54:17.670Z"}
{"level":"info","message":"CREATE INDEX IF NOT EXISTS idx_social_sentiment_fomo ON social_sentiment(fomo_score DESC, platform);","service":"trading-system","timestamp":"2025-07-10T21:54:17.670Z"}
{"level":"info","message":"CREATE INDEX IF NOT EXISTS idx_pump_signals_active ON pump_signals(is_active, expires_at, signal_strength DESC);","service":"trading-system","timestamp":"2025-07-10T21:54:17.670Z"}
{"level":"info","message":"CREATE INDEX IF NOT EXISTS idx_token_performance_pnl ON token_performance(pnl_percent DESC);","service":"trading-system","timestamp":"2025-07-10T21:54:17.670Z"}
{"level":"info","message":"-- =====================================================\r\n-- REAL-TIME MONITORING VIEWS\r\n-- =====================================================\r\n\r\n-- Hot Meme Coins View (SQLite compatible)\r\nDROP VIEW IF EXISTS hot_meme_coins;","service":"trading-system","timestamp":"2025-07-10T21:54:17.671Z"}
{"level":"info","message":"CREATE VIEW hot_meme_coins AS\r\nSELECT mc.*,\r\n       COALESCE(AVG(ss.sentiment_score), 0) as avg_sentiment,\r\n       COALESCE(SUM(ss.mentions_count), 0)  as total_mentions,\r\n       COALESCE(MAX(ps.signal_strength), 0) as max_signal_strength,\r\n       COUNT(DISTINCT ps.id)                as active_signals\r\nFROM meme_coins mc\r\n         LEFT JOIN social_sentiment ss ON mc.token_address = ss.token_address AND mc.chain = ss.chain\r\n    AND ss.recorded_at > datetime('now', '-1 hour')\r\n         LEFT JOIN pump_signals ps ON mc.token_address = ps.token_address AND mc.chain = ps.chain\r\n    AND ps.is_active\r\nWHERE mc.honeypot_score < 30\r\n  AND mc.rug_pull_score < 30\r\n  AND mc.liquidity_locked\r\nGROUP BY mc.id\r\nHAVING COUNT(DISTINCT ps.id) > 0\r\n    OR COALESCE(AVG(ss.sentiment_score), 0) > 50\r\nORDER BY active_signals DESC, avg_sentiment DESC;","service":"trading-system","timestamp":"2025-07-10T21:54:17.671Z"}
{"level":"info","message":"-- Sniper Opportunities View (SQLite compatible)\r\nDROP VIEW IF EXISTS sniper_opportunities;","service":"trading-system","timestamp":"2025-07-10T21:54:17.671Z"}
{"level":"info","message":"CREATE VIEW sniper_opportunities AS\r\nSELECT mc.*,\r\n       ps.signal_type,\r\n       ps.signal_strength,\r\n       ps.confidence_score,\r\n       ps.expected_pump_percent,\r\n       sc.slippage_percent,\r\n       sc.gas_price_gwei\r\nFROM meme_coins mc\r\n         INNER JOIN pump_signals ps ON mc.token_address = ps.token_address AND mc.chain = ps.chain\r\n         INNER JOIN sniper_config sc ON mc.chain = sc.chain\r\nWHERE ps.is_active\r\n  AND ps.expires_at > datetime('now')\r\n  AND mc.launch_date > datetime('now', '-24 hours')\r\n  AND mc.honeypot_score = 0\r\n  AND sc.is_active\r\nORDER BY ps.confidence_score DESC, ps.signal_strength DESC;","service":"trading-system","timestamp":"2025-07-10T21:54:17.671Z"}
{"level":"info","message":"-- =====================================================\r\n-- UTILITY FUNCTIONS FOR MEME COIN TRADING\r\n-- =====================================================\r\n\r\n-- Note: SQLite does not support stored functions like PostgreSQL\r\n-- Token safety score calculation will be handled in application logic\r\n\r\n-- Note: SQLite does not support stored functions like PostgreSQL\r\n-- Rug pull detection will be handled in application logic\r\n\r\n-- =====================================================\r\n-- TRIGGERS FOR AUTOMATED ACTIONS\r\n-- =====================================================\r\n\r\n-- SQLite trigger for auto-updating trending status\r\nCREATE TRIGGER IF NOT EXISTS trigger_update_trending\r\n    AFTER INSERT ON social_sentiment\r\n    WHEN NEW.fomo_score > 80 AND NEW.mentions_count > 1000\r\nBEGIN\r\nUPDATE meme_coins\r\nSET is_trending = 1,\r\n    updated_at  = CURRENT_TIMESTAMP\r\nWHERE token_address = NEW.token_address\r\n  AND chain = NEW.chain;\r\nEND;","service":"trading-system","timestamp":"2025-07-10T21:54:17.672Z"}
{"level":"info","message":"Database schema applied successfully.","service":"trading-system","timestamp":"2025-07-10T21:54:17.672Z"}
{"level":"info","message":"Database initialization complete","service":"trading-system","timestamp":"2025-07-10T21:54:17.673Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-11T02:32:25.577Z"}
{"level":"warn","message":"Module not found: ./engines/trading/GridBotManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-11T02:32:25.684Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-11T02:45:55.015Z"}
{"level":"warn","message":"Module not found: ./engines/trading/GridBotManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-11T02:45:55.053Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-11T02:52:40.701Z"}
{"level":"warn","message":"Module not found: ./engines/trading/GridBotManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-11T02:52:40.740Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-12T17:17:12.176Z"}
{"level":"error","message":"Error loading database configuration: Validation error for database: \"development\" is not allowed","service":"trading-system","stack":"Error: Validation error for database: \"development\" is not allowed\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:265:41)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T17:17:48.246Z"}
{"level":"error","message":"Error loading configurations: Validation error for database: \"development\" is not allowed","service":"trading-system","stack":"Error: Validation error for database: \"development\" is not allowed\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:265:41)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T17:17:48.246Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-12T18:12:09.569Z"}
{"level":"error","message":"Error loading database configuration: Validation error for database: \"development\" is not allowed","service":"trading-system","stack":"Error: Validation error for database: \"development\" is not allowed\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:265:41)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:12:35.934Z"}
{"level":"error","message":"Error loading configurations: Validation error for database: \"development\" is not allowed","service":"trading-system","stack":"Error: Validation error for database: \"development\" is not allowed\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:265:41)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:12:35.935Z"}
{"level":"error","message":"Error loading database configuration: Validation error for database: \"development\" is not allowed","service":"trading-system","stack":"Error: Validation error for database: \"development\" is not allowed\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:265:41)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:42:49.959Z"}
{"level":"error","message":"Error loading configurations: Validation error for database: \"development\" is not allowed","service":"trading-system","stack":"Error: Validation error for database: \"development\" is not allowed\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:265:41)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:42:49.961Z"}
{"level":"error","message":"Error loading api configuration: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty","service":"trading-system","stack":"Error: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:290:36)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:45:50.482Z"}
{"level":"error","message":"Error loading configurations: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty","service":"trading-system","stack":"Error: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:290:36)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:45:50.483Z"}
{"level":"info","message":"Test log message","service":"trading-system","timestamp":"2025-07-12T18:51:25.386Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-12T18:54:15.684Z"}
{"level":"error","message":"Error loading api configuration: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty","service":"trading-system","stack":"Error: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:290:36)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:54:41.537Z"}
{"level":"error","message":"Error loading configurations: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty","service":"trading-system","stack":"Error: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:290:36)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:54:41.538Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-12T18:54:41.578Z"}
{"level":"error","message":"Error loading api configuration: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty","service":"trading-system","stack":"Error: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:290:36)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:54:41.597Z"}
{"level":"error","message":"Error loading configurations: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty","service":"trading-system","stack":"Error: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:290:36)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:54:41.597Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-12T18:54:41.610Z"}
{"level":"error","message":"Error loading api configuration: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty","service":"trading-system","stack":"Error: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:290:36)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:54:41.627Z"}
{"level":"error","message":"Error loading configurations: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty","service":"trading-system","stack":"Error: Validation error for api: \"exchanges.binance.apiKey\" is not allowed to be empty\n    at ConfigManager.loadAndValidate (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:411:27)\n    at ConfigManager.loadConfigurations (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:290:36)\n    at ConfigManager.initialize (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:32:14)\n    at new ConfigManager (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:16:14)\n    at Object.<anonymous> (/mnt/c/Users/<USER>/Documents/electronTrader/app/trading/shared/config/config-manager.js:629:23)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-12T18:54:41.627Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-14T21:03:34.664Z"}
{"level":"info","message":"=== Starting Trading System Tests ===","service":"trading-system","timestamp":"2025-07-14T21:03:34.683Z"}
{"level":"info","message":"Starting trading system initialization...","service":"trading-system","timestamp":"2025-07-14T21:03:34.683Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-14T21:03:34.684Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.components.eventCoordinator.initialize is not a function","service":"trading-system","stack":"TypeError: this.components.eventCoordinator.initialize is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:124:52)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:13)\n    at async C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:54:17\n    at async TradingSystemTester.test (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:160:13)\n    at async TradingSystemTester.runTests (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:53:13)","timestamp":"2025-07-14T21:03:34.687Z"}
{"level":"error","message":"Failed to initialize Trading System: this.components.eventCoordinator.initialize is not a function","service":"trading-system","stack":"TypeError: this.components.eventCoordinator.initialize is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:124:52)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:13)\n    at async C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:54:17\n    at async TradingSystemTester.test (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:160:13)\n    at async TradingSystemTester.runTests (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:53:13)","timestamp":"2025-07-14T21:03:34.687Z"}
{"0":"t","1":"h","10":"n","11":"e","12":"n","13":"t","14":"s","15":".","16":"e","17":"v","18":"e","19":"n","2":"i","20":"t","21":"C","22":"o","23":"o","24":"r","25":"d","26":"i","27":"n","28":"a","29":"t","3":"s","30":"o","31":"r","32":".","33":"i","34":"n","35":"i","36":"t","37":"i","38":"a","39":"l","4":".","40":"i","41":"z","42":"e","43":" ","44":"i","45":"s","46":" ","47":"n","48":"o","49":"t","5":"c","50":" ","51":"a","52":" ","53":"f","54":"u","55":"n","56":"c","57":"t","58":"i","59":"o","6":"o","60":"n","7":"m","8":"p","9":"o","level":"error","message":"❌ System Initialization - FAILED (4ms)","service":"trading-system","timestamp":"2025-07-14T21:03:34.687Z"}
{"components":13,"healthStatus":"unknown","initialized":false,"lastUpdate":"2025-07-14T21:03:34.688Z","level":"info","message":"Bot Status:","service":"trading-system","timestamp":"2025-07-14T21:03:34.688Z"}
{"level":"info","message":"✅ Get Bot Status - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-14T21:03:34.688Z"}
{"level":"info","message":"Settings loaded successfully","service":"trading-system","timestamp":"2025-07-14T21:03:34.688Z"}
{"level":"info","message":"✅ Get Settings - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-14T21:03:34.688Z"}
{"level":"warn","message":"DataCollector component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-14T21:03:34.688Z"}
{"level":"info","message":"Found 0 coins","service":"trading-system","timestamp":"2025-07-14T21:03:34.688Z"}
{"level":"info","message":"✅ Get Coins - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-14T21:03:34.688Z"}
{"level":"warn","message":"PerformanceTracker component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-14T21:03:34.689Z"}
{"level":"info","message":"Trading stats retrieved","service":"trading-system","timestamp":"2025-07-14T21:03:34.689Z"}
{"level":"info","message":"✅ Get Trading Stats - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-14T21:03:34.689Z"}
{"level":"warn","message":"PerformanceTracker component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-14T21:03:34.689Z"}
{"level":"info","message":"Performance metrics retrieved","service":"trading-system","timestamp":"2025-07-14T21:03:34.689Z"}
{"level":"info","message":"✅ Get Performance Metrics - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-14T21:03:34.689Z"}
{"level":"warn","message":"PortfolioManager component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-14T21:03:34.689Z"}
{"level":"info","message":"Wallet balance retrieved","service":"trading-system","timestamp":"2025-07-14T21:03:34.689Z"}
{"level":"info","message":"✅ Get Wallet Balance - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-14T21:03:34.689Z"}
{"components":13,"healthStatus":"unknown","initialized":false,"lastUpdate":"2025-07-14T21:03:34.689Z","level":"info","message":"Health check completed:","service":"trading-system","timestamp":"2025-07-14T21:03:34.689Z"}
{"level":"info","message":"✅ Health Check - PASSED (1ms)","service":"trading-system","timestamp":"2025-07-14T21:03:34.690Z"}
{"level":"warn","message":"GridBotManager component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-14T21:03:34.690Z"}
{"level":"info","message":"Grid bot positions: 0","service":"trading-system","timestamp":"2025-07-14T21:03:34.690Z"}
{"level":"info","message":"✅ Check Grid Bot Manager - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-14T21:03:34.690Z"}
{"level":"warn","message":"WhaleTracker component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-14T21:03:34.690Z"}
{"level":"info","message":"Tracked whales: 0","service":"trading-system","timestamp":"2025-07-14T21:03:34.690Z"}
{"level":"info","message":"✅ Check Whale Tracker - PASSED (1ms)","service":"trading-system","timestamp":"2025-07-14T21:03:34.691Z"}
{"level":"warn","message":"MemeCoinScanner component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-14T21:03:34.691Z"}
{"level":"info","message":"Meme scanner status:","service":"trading-system","timestamp":"2025-07-14T21:03:34.691Z"}
{"level":"info","message":"✅ Check Meme Scanner Status - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-14T21:03:34.691Z"}
{"level":"info","message":"\n=== Test Results Summary ===","service":"trading-system","timestamp":"2025-07-14T21:03:34.691Z"}
{"level":"info","message":"Total Tests: 11","service":"trading-system","timestamp":"2025-07-14T21:03:34.691Z"}
{"level":"info","message":"Passed: 10 ✅","service":"trading-system","timestamp":"2025-07-14T21:03:34.692Z"}
{"level":"info","message":"Failed: 1 ❌","service":"trading-system","timestamp":"2025-07-14T21:03:34.692Z"}
{"level":"info","message":"\nFailed Tests:","service":"trading-system","timestamp":"2025-07-14T21:03:34.692Z"}
{"level":"error","message":"- System Initialization: this.components.eventCoordinator.initialize is not a function","service":"trading-system","timestamp":"2025-07-14T21:03:34.692Z"}
{"level":"info","message":"\n=== End of Test Results ===","service":"trading-system","timestamp":"2025-07-14T21:03:34.692Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-14T23:14:08.410Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-14T23:14:09.381Z"}
{"level":"info","message":"=== Starting Trading System Tests ===","service":"trading-system","timestamp":"2025-07-14T23:14:09.417Z"}
{"level":"info","message":"Starting trading system initialization...","service":"trading-system","timestamp":"2025-07-14T23:14:09.418Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-14T23:14:09.419Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-14T23:16:45.793Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-14T23:16:46.620Z"}
{"level":"info","message":"=== Starting Trading System Tests ===","service":"trading-system","timestamp":"2025-07-14T23:16:46.637Z"}
{"level":"info","message":"Starting trading system initialization...","service":"trading-system","timestamp":"2025-07-14T23:16:46.638Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-14T23:16:46.638Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-14T23:20:16.822Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-14T23:20:17.538Z"}
{"level":"info","message":"=== Starting Trading System Tests ===","service":"trading-system","timestamp":"2025-07-14T23:20:17.559Z"}
{"level":"info","message":"Starting trading system initialization...","service":"trading-system","timestamp":"2025-07-14T23:20:17.560Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-14T23:20:17.560Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-15T01:54:40.091Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-15T01:54:40.535Z"}
{"level":"info","message":"✅ Arbitrage database initialized","service":"trading-system","timestamp":"2025-07-15T01:54:40.829Z"}
{"level":"info","message":"✅ Portfolio manager initialized and started","service":"trading-system","timestamp":"2025-07-15T01:54:40.829Z"}
{"level":"info","message":"✅ Arbitrage engine initialized","service":"trading-system","timestamp":"2025-07-15T01:54:40.829Z"}
{"level":"info","message":"✅ Opportunity scanner initialized","service":"trading-system","timestamp":"2025-07-15T01:54:40.829Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:07.000Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:07.001Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:07.001Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:07.001Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:07.002Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:07.002Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:07.002Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:07.002Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:37.002Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:37.002Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:37.002Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:57:37.002Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:58:07.000Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:58:07.001Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:58:07.001Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:58:07.001Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:58:37.006Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:58:37.006Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:58:37.006Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:58:37.007Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:59:07.101Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:59:07.101Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:59:07.101Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:59:07.101Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:59:37.100Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:59:37.100Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:59:37.100Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T01:59:37.101Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:15.773Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:15.775Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:15.775Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:15.775Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:22.038Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:22.039Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:22.039Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:22.040Z"}
{"level":"error","message":"IPC Error on get-portfolio-summary: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:171:66\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:22.040Z"}
{"level":"error","message":"IPC Error on get-trading-stats: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:166:62\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:22.041Z"}
{"level":"error","message":"Health check failed: tradingOrchestrator.getPortfolioManager is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getPortfolioManager is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:239:55\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:22.041Z"}
{"level":"error","message":"IPC Error on get-price-history: tradingOrchestrator.getDataCollector is not a function","service":"trading-system","stack":"TypeError: tradingOrchestrator.getDataCollector is not a function\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:177:93\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:125:34\n    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83740)\n    at WebContents.emit (node:events:519:28)","timestamp":"2025-07-15T02:00:22.041Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-15T02:22:41.635Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-15T02:39:27.615Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-15T02:40:24.973Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-15T02:41:20.841Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-15T02:41:21.436Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-15T02:41:21.550Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.db.initialize is not a function","service":"trading-system","stack":"TypeError: this.db.initialize is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:128:27)\n    at initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:216:35)","timestamp":"2025-07-15T02:41:21.551Z"}
{"level":"error","message":"❌ Failed to initialize the application: this.db.initialize is not a function","service":"trading-system","stack":"TypeError: this.db.initialize is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:128:27)\n    at initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:216:35)","timestamp":"2025-07-15T02:41:21.551Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-15T02:41:58.848Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-15T02:44:40.410Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-15T02:46:22.795Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-15T02:48:01.750Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-15T02:48:02.348Z"}
{"level":"info","message":"🚀 Initializing application...","service":"trading-system","timestamp":"2025-07-15T02:48:02.437Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-15T02:48:02.438Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-15T02:48:02.440Z"}
{"code":"ERR_DLOPEN_FAILED","level":"error","message":"Failed to initialize Trading Orchestrator: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).","service":"trading-system","stack":"Error: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).\n    at process.func [as dlopen] (node:electron/js2c/node_init:2:2559)\n    at Module._extensions..node (node:internal/modules/cjs/loader:1602:18)\n    at Object.func [as .node] (node:electron/js2c/node_init:2:2559)\n    at Module.load (node:internal/modules/cjs/loader:1295:32)\n    at Module._load (node:internal/modules/cjs/loader:1111:12)\n    at c._load (node:electron/js2c/node_init:2:16955)\n    at Module.require (node:internal/modules/cjs/loader:1318:19)\n    at require (node:internal/modules/helpers:179:18)\n    at bindings (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\bindings\\bindings.js:112:48)\n    at new Database (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\lib\\database.js:48:64)","timestamp":"2025-07-15T02:48:02.443Z"}
{"code":"ERR_DLOPEN_FAILED","level":"error","message":"❌ Failed to initialize the application: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).","service":"trading-system","stack":"Error: The module '\\\\?\\C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node'\nwas compiled against a different Node.js version using\nNODE_MODULE_VERSION 127. This version of Node.js requires\nNODE_MODULE_VERSION 125. Please try re-compiling or re-installing\nthe module (for instance, using `npm rebuild` or `npm install`).\n    at process.func [as dlopen] (node:electron/js2c/node_init:2:2559)\n    at Module._extensions..node (node:internal/modules/cjs/loader:1602:18)\n    at Object.func [as .node] (node:electron/js2c/node_init:2:2559)\n    at Module.load (node:internal/modules/cjs/loader:1295:32)\n    at Module._load (node:internal/modules/cjs/loader:1111:12)\n    at c._load (node:electron/js2c/node_init:2:16955)\n    at Module.require (node:internal/modules/cjs/loader:1318:19)\n    at require (node:internal/modules/helpers:179:18)\n    at bindings (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\bindings\\bindings.js:112:48)\n    at new Database (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\node_modules\\better-sqlite3\\lib\\database.js:48:64)","timestamp":"2025-07-15T02:48:02.444Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-15T02:51:37.231Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-15T02:53:38.269Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-15T02:56:40.497Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-15T02:56:41.149Z"}
{"level":"info","message":"🚀 Initializing application...","service":"trading-system","timestamp":"2025-07-15T02:56:41.246Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-15T02:56:41.246Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-15T02:56:41.250Z"}
{"level":"info","message":"Grid Bot Manager initialized","service":"trading-system","timestamp":"2025-07-15T02:56:41.252Z"}
{"level":"info","message":"Initializing LLM Coordinator","service":"trading-system","timestamp":"2025-07-15T02:56:41.254Z"}
{"level":"warn","message":"Provider OpenAI GPT-4 not configured","service":"trading-system","timestamp":"2025-07-15T02:56:41.254Z"}
{"level":"warn","message":"Provider Anthropic Claude not configured","service":"trading-system","timestamp":"2025-07-15T02:56:41.254Z"}
{"level":"info","message":"Loaded 0 positions","service":"trading-system","timestamp":"2025-07-15T02:56:41.254Z"}
{"level":"info","message":"LLM Coordinator initialized successfully","service":"trading-system","timestamp":"2025-07-15T02:56:41.255Z"}
{"level":"info","message":"Production Trading Executor initialized","service":"trading-system","timestamp":"2025-07-15T02:56:41.255Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.db.all is not a function","service":"trading-system","stack":"TypeError: this.db.all is not a function\n    at MemeCoinScanner.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\MemeCoinScanner.js:62:49)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:176:49)\n    at async initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:196:9)","timestamp":"2025-07-15T02:56:41.255Z"}
{"level":"error","message":"❌ Failed to initialize the application: this.db.all is not a function","service":"trading-system","stack":"TypeError: this.db.all is not a function\n    at MemeCoinScanner.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\MemeCoinScanner.js:62:49)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:176:49)\n    at async initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:196:9)","timestamp":"2025-07-15T02:56:41.255Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-15T02:57:34.216Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-15T02:57:34.752Z"}
{"level":"info","message":"🚀 Initializing application...","service":"trading-system","timestamp":"2025-07-15T02:57:34.858Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-15T02:57:34.859Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-15T02:57:34.864Z"}
{"level":"info","message":"Grid Bot Manager initialized","service":"trading-system","timestamp":"2025-07-15T02:57:34.866Z"}
{"level":"info","message":"Initializing LLM Coordinator","service":"trading-system","timestamp":"2025-07-15T02:57:34.866Z"}
{"level":"warn","message":"Provider OpenAI GPT-4 not configured","service":"trading-system","timestamp":"2025-07-15T02:57:34.867Z"}
{"level":"warn","message":"Provider Anthropic Claude not configured","service":"trading-system","timestamp":"2025-07-15T02:57:34.867Z"}
{"level":"info","message":"Loaded 0 positions","service":"trading-system","timestamp":"2025-07-15T02:57:34.867Z"}
{"level":"info","message":"LLM Coordinator initialized successfully","service":"trading-system","timestamp":"2025-07-15T02:57:34.867Z"}
{"level":"info","message":"Production Trading Executor initialized","service":"trading-system","timestamp":"2025-07-15T02:57:34.867Z"}
{"level":"warn","message":"Module not found: ./engines/trading/EliteWhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-15T03:11:57.761Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-15T03:11:58.377Z"}
{"level":"info","message":"🚀 Initializing application...","service":"trading-system","timestamp":"2025-07-15T03:11:58.473Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-15T03:11:58.473Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-15T03:11:58.477Z"}
{"level":"info","message":"Grid Bot Manager initialized","service":"trading-system","timestamp":"2025-07-15T03:11:58.479Z"}
{"level":"info","message":"Initializing LLM Coordinator","service":"trading-system","timestamp":"2025-07-15T03:11:58.479Z"}
{"level":"warn","message":"Provider OpenAI GPT-4 not configured","service":"trading-system","timestamp":"2025-07-15T03:11:58.480Z"}
{"level":"warn","message":"Provider Anthropic Claude not configured","service":"trading-system","timestamp":"2025-07-15T03:11:58.480Z"}
{"level":"info","message":"Loaded 0 positions","service":"trading-system","timestamp":"2025-07-15T03:11:58.480Z"}
{"level":"info","message":"LLM Coordinator initialized successfully","service":"trading-system","timestamp":"2025-07-15T03:11:58.480Z"}
{"level":"info","message":"Production Trading Executor initialized","service":"trading-system","timestamp":"2025-07-15T03:11:58.480Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-16T08:35:52.372Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-16T08:57:21.698Z"}
{"level":"info","message":"=== Starting Trading System Tests ===","service":"trading-system","timestamp":"2025-07-16T08:57:21.724Z"}
{"level":"info","message":"Starting trading system initialization...","service":"trading-system","timestamp":"2025-07-16T08:57:21.725Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-16T08:57:21.725Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-16T08:57:21.751Z"}
{"level":"info","message":"🐋 Elite Whale Tracker initialized","service":"trading-system","timestamp":"2025-07-16T08:57:21.757Z"}
{"level":"info","message":"Grid Bot Manager initialized","service":"trading-system","timestamp":"2025-07-16T08:57:21.758Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.components.whaleTracker.initialize is not a function","service":"trading-system","stack":"TypeError: this.components.whaleTracker.initialize is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:182:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:13)\n    at async C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:54:17\n    at async TradingSystemTester.test (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:160:13)\n    at async TradingSystemTester.runTests (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:53:13)","timestamp":"2025-07-16T08:57:21.759Z"}
{"level":"error","message":"Failed to initialize Trading System: this.components.whaleTracker.initialize is not a function","service":"trading-system","stack":"TypeError: this.components.whaleTracker.initialize is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:182:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:13)\n    at async C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:54:17\n    at async TradingSystemTester.test (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:160:13)\n    at async TradingSystemTester.runTests (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-trading-system.js:53:13)","timestamp":"2025-07-16T08:57:21.760Z"}
{"0":"t","1":"h","10":"n","11":"e","12":"n","13":"t","14":"s","15":".","16":"w","17":"h","18":"a","19":"l","2":"i","20":"e","21":"T","22":"r","23":"a","24":"c","25":"k","26":"e","27":"r","28":".","29":"i","3":"s","30":"n","31":"i","32":"t","33":"i","34":"a","35":"l","36":"i","37":"z","38":"e","39":" ","4":".","40":"i","41":"s","42":" ","43":"n","44":"o","45":"t","46":" ","47":"a","48":" ","49":"f","5":"c","50":"u","51":"n","52":"c","53":"t","54":"i","55":"o","56":"n","6":"o","7":"m","8":"p","9":"o","level":"error","message":"❌ System Initialization - FAILED (35ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.760Z"}
{"components":{"circuitBreaker":"ready","configManager":"ready","dataCollector":"ready","eventCoordinator":"ready","gridBotManager":"ready","llmCoordinator":"ready","memeCoinScanner":"ready","performanceTracker":"ready","portfolioManager":"ready","riskManager":"ready","sentimentAnalyzer":"ready","tradingExecutor":"ready","whaleTracker":"ready"},"isInitialized":false,"isRunning":false,"level":"info","message":"Bot Status:","service":"trading-system","timestamp":"2025-07-16T08:57:21.761Z","workflowState":{"activeSignals":{},"currentCycle":0,"healthStatus":"unknown","lastAnalysis":null,"lastDataCollection":null,"lastTradeExecution":null,"marketData":null,"pendingTrades":[],"performance":{"successfulTrades":0,"totalProfit":0,"totalTrades":0}}}
{"level":"info","message":"✅ Get Bot Status - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.761Z"}
{"level":"info","message":"Settings loaded successfully","service":"trading-system","timestamp":"2025-07-16T08:57:21.762Z"}
{"level":"info","message":"✅ Get Settings - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.762Z"}
{"0":"t","1":"h","10":"n","11":"g","12":"O","13":"r","14":"c","15":"h","16":"e","17":"s","18":"t","19":"r","2":"i","20":"a","21":"t","22":"o","23":"r","24":".","25":"c","26":"o","27":"m","28":"p","29":"o","3":"s","30":"n","31":"e","32":"n","33":"t","34":"s","35":".","36":"d","37":"a","38":"t","39":"a","4":".","40":"C","41":"o","42":"l","43":"l","44":"e","45":"c","46":"t","47":"o","48":"r","49":".","5":"t","50":"g","51":"e","52":"t","53":"A","54":"v","55":"a","56":"i","57":"l","58":"a","59":"b","6":"r","60":"l","61":"e","62":"C","63":"o","64":"i","65":"n","66":"s","67":" ","68":"i","69":"s","7":"a","70":" ","71":"n","72":"o","73":"t","74":" ","75":"a","76":" ","77":"f","78":"u","79":"n","8":"d","80":"c","81":"t","82":"i","83":"o","84":"n","9":"i","level":"error","message":"❌ Get Coins - FAILED (0ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.783Z"}
{"0":"t","1":"h","10":"n","11":"g","12":"O","13":"r","14":"c","15":"h","16":"e","17":"s","18":"t","19":"r","2":"i","20":"a","21":"t","22":"o","23":"r","24":".","25":"c","26":"o","27":"m","28":"p","29":"o","3":"s","30":"n","31":"e","32":"n","33":"t","34":"s","35":".","36":"p","37":"e","38":"r","39":"f","4":".","40":"o","41":"r","42":"m","43":"a","44":"n","45":"c","46":"e","47":"T","48":"r","49":"a","5":"t","50":"c","51":"k","52":"e","53":"r","54":".","55":"g","56":"e","57":"t","58":"T","59":"r","6":"r","60":"a","61":"d","62":"i","63":"n","64":"g","65":"S","66":"t","67":"a","68":"t","69":"s","7":"a","70":" ","71":"i","72":"s","73":" ","74":"n","75":"o","76":"t","77":" ","78":"a","79":" ","8":"d","80":"f","81":"u","82":"n","83":"c","84":"t","85":"i","86":"o","87":"n","9":"i","level":"error","message":"❌ Get Trading Stats - FAILED (1ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.784Z"}
{"0":"t","1":"h","10":"n","11":"g","12":"O","13":"r","14":"c","15":"h","16":"e","17":"s","18":"t","19":"r","2":"i","20":"a","21":"t","22":"o","23":"r","24":".","25":"c","26":"o","27":"m","28":"p","29":"o","3":"s","30":"n","31":"e","32":"n","33":"t","34":"s","35":".","36":"p","37":"e","38":"r","39":"f","4":".","40":"o","41":"r","42":"m","43":"a","44":"n","45":"c","46":"e","47":"T","48":"r","49":"a","5":"t","50":"c","51":"k","52":"e","53":"r","54":".","55":"g","56":"e","57":"t","58":"P","59":"e","6":"r","60":"r","61":"f","62":"o","63":"r","64":"m","65":"a","66":"n","67":"c","68":"e","69":"M","7":"a","70":"e","71":"t","72":"r","73":"i","74":"c","75":"s","76":" ","77":"i","78":"s","79":" ","8":"d","80":"n","81":"o","82":"t","83":" ","84":"a","85":" ","86":"f","87":"u","88":"n","89":"c","9":"i","90":"t","91":"i","92":"o","93":"n","level":"error","message":"❌ Get Performance Metrics - FAILED (0ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.784Z"}
{"0":"t","1":"h","10":"n","11":"g","12":"O","13":"r","14":"c","15":"h","16":"e","17":"s","18":"t","19":"r","2":"i","20":"a","21":"t","22":"o","23":"r","24":".","25":"c","26":"o","27":"m","28":"p","29":"o","3":"s","30":"n","31":"e","32":"n","33":"t","34":"s","35":".","36":"p","37":"o","38":"r","39":"t","4":".","40":"f","41":"o","42":"l","43":"i","44":"o","45":"M","46":"a","47":"n","48":"a","49":"g","5":"t","50":"e","51":"r","52":".","53":"g","54":"e","55":"t","56":"W","57":"a","58":"l","59":"l","6":"r","60":"e","61":"t","62":"B","63":"a","64":"l","65":"a","66":"n","67":"c","68":"e","69":" ","7":"a","70":"i","71":"s","72":" ","73":"n","74":"o","75":"t","76":" ","77":"a","78":" ","79":"f","8":"d","80":"u","81":"n","82":"c","83":"t","84":"i","85":"o","86":"n","9":"i","level":"error","message":"❌ Get Wallet Balance - FAILED (0ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.784Z"}
{"components":{"circuitBreaker":"ready","configManager":"ready","dataCollector":"ready","eventCoordinator":"ready","gridBotManager":"ready","llmCoordinator":"ready","memeCoinScanner":"ready","performanceTracker":"ready","portfolioManager":"ready","riskManager":"ready","sentimentAnalyzer":"ready","tradingExecutor":"ready","whaleTracker":"ready"},"isInitialized":false,"isRunning":false,"level":"info","message":"Health check completed:","service":"trading-system","timestamp":"2025-07-16T08:57:21.785Z","workflowState":{"activeSignals":{},"currentCycle":0,"healthStatus":"unknown","lastAnalysis":null,"lastDataCollection":null,"lastTradeExecution":null,"marketData":null,"pendingTrades":[],"performance":{"successfulTrades":0,"totalProfit":0,"totalTrades":0}}}
{"level":"info","message":"✅ Health Check - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.785Z"}
{"level":"info","message":"Grid bot positions: 0","service":"trading-system","timestamp":"2025-07-16T08:57:21.785Z"}
{"level":"info","message":"✅ Check Grid Bot Manager - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.785Z"}
{"0":"t","1":"h","10":"n","11":"g","12":"O","13":"r","14":"c","15":"h","16":"e","17":"s","18":"t","19":"r","2":"i","20":"a","21":"t","22":"o","23":"r","24":".","25":"c","26":"o","27":"m","28":"p","29":"o","3":"s","30":"n","31":"e","32":"n","33":"t","34":"s","35":".","36":"w","37":"h","38":"a","39":"l","4":".","40":"e","41":"T","42":"r","43":"a","44":"c","45":"k","46":"e","47":"r","48":".","49":"g","5":"t","50":"e","51":"t","52":"T","53":"r","54":"a","55":"c","56":"k","57":"e","58":"d","59":"W","6":"r","60":"h","61":"a","62":"l","63":"e","64":"s","65":" ","66":"i","67":"s","68":" ","69":"n","7":"a","70":"o","71":"t","72":" ","73":"a","74":" ","75":"f","76":"u","77":"n","78":"c","79":"t","8":"d","80":"i","81":"o","82":"n","9":"i","level":"error","message":"❌ Check Whale Tracker - FAILED (0ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.786Z"}
{"0":"t","1":"h","10":"n","11":"g","12":"O","13":"r","14":"c","15":"h","16":"e","17":"s","18":"t","19":"r","2":"i","20":"a","21":"t","22":"o","23":"r","24":".","25":"c","26":"o","27":"m","28":"p","29":"o","3":"s","30":"n","31":"e","32":"n","33":"t","34":"s","35":".","36":"m","37":"e","38":"m","39":"e","4":".","40":"C","41":"o","42":"i","43":"n","44":"S","45":"c","46":"a","47":"n","48":"n","49":"e","5":"t","50":"r","51":".","52":"g","53":"e","54":"t","55":"S","56":"t","57":"a","58":"t","59":"u","6":"r","60":"s","61":" ","62":"i","63":"s","64":" ","65":"n","66":"o","67":"t","68":" ","69":"a","7":"a","70":" ","71":"f","72":"u","73":"n","74":"c","75":"t","76":"i","77":"o","78":"n","8":"d","9":"i","level":"error","message":"❌ Check Meme Scanner Status - FAILED (0ms)","service":"trading-system","timestamp":"2025-07-16T08:57:21.786Z"}
{"level":"info","message":"\n=== Test Results Summary ===","service":"trading-system","timestamp":"2025-07-16T08:57:21.787Z"}
{"level":"info","message":"Total Tests: 11","service":"trading-system","timestamp":"2025-07-16T08:57:21.787Z"}
{"level":"info","message":"Passed: 4 ✅","service":"trading-system","timestamp":"2025-07-16T08:57:21.787Z"}
{"level":"info","message":"Failed: 7 ❌","service":"trading-system","timestamp":"2025-07-16T08:57:21.787Z"}
{"level":"info","message":"\nFailed Tests:","service":"trading-system","timestamp":"2025-07-16T08:57:21.787Z"}
{"level":"error","message":"- System Initialization: this.components.whaleTracker.initialize is not a function","service":"trading-system","timestamp":"2025-07-16T08:57:21.787Z"}
{"level":"error","message":"- Get Coins: this.tradingOrchestrator.components.dataCollector.getAvailableCoins is not a function","service":"trading-system","timestamp":"2025-07-16T08:57:21.788Z"}
{"level":"error","message":"- Get Trading Stats: this.tradingOrchestrator.components.performanceTracker.getTradingStats is not a function","service":"trading-system","timestamp":"2025-07-16T08:57:21.788Z"}
{"level":"error","message":"- Get Performance Metrics: this.tradingOrchestrator.components.performanceTracker.getPerformanceMetrics is not a function","service":"trading-system","timestamp":"2025-07-16T08:57:21.788Z"}
{"level":"error","message":"- Get Wallet Balance: this.tradingOrchestrator.components.portfolioManager.getWalletBalance is not a function","service":"trading-system","timestamp":"2025-07-16T08:57:21.788Z"}
{"level":"error","message":"- Check Whale Tracker: this.tradingOrchestrator.components.whaleTracker.getTrackedWhales is not a function","service":"trading-system","timestamp":"2025-07-16T08:57:21.788Z"}
{"level":"error","message":"- Check Meme Scanner Status: this.tradingOrchestrator.components.memeCoinScanner.getStatus is not a function","service":"trading-system","timestamp":"2025-07-16T08:57:21.788Z"}
{"level":"info","message":"\n=== End of Test Results ===","service":"trading-system","timestamp":"2025-07-16T08:57:21.788Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-16T12:04:57.105Z"}
{"level":"info","message":"🚀 Initializing application...","service":"trading-system","timestamp":"2025-07-16T12:04:57.258Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-16T12:04:57.259Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-16T12:04:57.264Z"}
{"level":"info","message":"🐋 Elite Whale Tracker initialized","service":"trading-system","timestamp":"2025-07-16T12:04:57.266Z"}
{"level":"info","message":"Grid Bot Manager initialized","service":"trading-system","timestamp":"2025-07-16T12:04:57.266Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.components.whaleTracker.initialize is not a function","service":"trading-system","stack":"TypeError: this.components.whaleTracker.initialize is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:182:46)\n    at async initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:196:9)","timestamp":"2025-07-16T12:04:57.267Z"}
{"level":"error","message":"❌ Failed to initialize the application: this.components.whaleTracker.initialize is not a function","service":"trading-system","stack":"TypeError: this.components.whaleTracker.initialize is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:182:46)\n    at async initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:196:9)","timestamp":"2025-07-16T12:04:57.267Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-16T12:06:18.609Z"}
{"level":"info","message":"🚀 Initializing application...","service":"trading-system","timestamp":"2025-07-16T12:06:18.701Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-16T12:06:18.702Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-16T12:06:18.705Z"}
{"level":"info","message":"🐋 Elite Whale Tracker initialized","service":"trading-system","timestamp":"2025-07-16T12:06:18.707Z"}
{"level":"info","message":"Grid Bot Manager initialized","service":"trading-system","timestamp":"2025-07-16T12:06:18.708Z"}
{"level":"info","message":"🐋 Initializing Elite Whale Tracker...","service":"trading-system","timestamp":"2025-07-16T12:06:18.708Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.logger.error is not a function","service":"trading-system","stack":"TypeError: this.logger.error is not a function\n    at MemeCoinScanner.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\MemeCoinScanner.js:55:25)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:183:49)\n    at async initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:196:9)","timestamp":"2025-07-16T12:06:18.708Z"}
{"level":"error","message":"❌ Failed to initialize the application: this.logger.error is not a function","service":"trading-system","stack":"TypeError: this.logger.error is not a function\n    at MemeCoinScanner.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\MemeCoinScanner.js:55:25)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:183:49)\n    at async initializeApp (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\main.js:196:9)","timestamp":"2025-07-16T12:06:18.708Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-16T14:03:33.608Z"}
{"level":"info","message":"🚀 Initializing application...","service":"trading-system","timestamp":"2025-07-16T14:03:33.775Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-16T14:03:33.776Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-16T14:03:33.785Z"}
{"level":"info","message":"🐋 Elite Whale Tracker initialized","service":"trading-system","timestamp":"2025-07-16T14:03:33.787Z"}
{"level":"info","message":"Connecting to exchange: binance","service":"trading-system","timestamp":"2025-07-16T14:03:33.788Z"}
{"level":"info","message":"Successfully connected to binance","service":"trading-system","timestamp":"2025-07-16T14:03:42.196Z"}
{"level":"info","message":"Connecting to exchange: bybit","service":"trading-system","timestamp":"2025-07-16T14:03:42.196Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-16T15:36:26.774Z"}
{"level":"info","message":"Running Trading System in standalone mode.","service":"trading-system","timestamp":"2025-07-16T15:36:26.793Z"}
{"level":"info","message":"Starting trading system initialization...","service":"trading-system","timestamp":"2025-07-16T15:36:26.793Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-16T15:36:26.793Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-16T15:36:26.801Z"}
{"level":"info","message":"🐋 Elite Whale Tracker initialized","service":"trading-system","timestamp":"2025-07-16T15:36:26.807Z"}
{"level":"info","message":"Connecting to exchange: binance","service":"trading-system","timestamp":"2025-07-16T15:36:26.809Z"}
{"level":"info","message":"Successfully connected to binance","service":"trading-system","timestamp":"2025-07-16T15:36:35.237Z"}
{"level":"info","message":"Connecting to exchange: bybit","service":"trading-system","timestamp":"2025-07-16T15:36:35.238Z"}
{"level":"info","message":"All configurations loaded successfully","service":"trading-system","timestamp":"2025-07-16T17:40:41.464Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:18:58.754Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:18:59.429Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:26:35.540Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:26:35.952Z"}
{"level":"warn","message":"Module not found: ./shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:26:35.965Z"}
{"level":"warn","message":"Module not found: ./shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:26:35.965Z"}
{"level":"warn","message":"Module not found: ./shared/ai/llm-coordinator. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:26:35.966Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:26:35.966Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:33:46.225Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:33:46.783Z"}
{"level":"warn","message":"Module not found: ./shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:33:46.795Z"}
{"level":"warn","message":"Module not found: ./shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:33:46.795Z"}
{"level":"warn","message":"Module not found: ./shared/ai/llm-coordinator. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:33:46.795Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:33:46.796Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:37:27.700Z"}
{"level":"warn","message":"Module not found: ./shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:37:28.270Z"}
{"level":"warn","message":"Module not found: ./shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:37:28.271Z"}
{"level":"warn","message":"Module not found: ./shared/ai/llm-coordinator. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:37:28.272Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:37:28.272Z"}
{"level":"warn","message":"Module not found: ./engines/exchange/ProductionExchangeConnector. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:37:28.273Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:39:30.200Z"}
{"level":"warn","message":"Module not found: ./shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:39:30.693Z"}
{"level":"warn","message":"Module not found: ./shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:39:30.693Z"}
{"level":"warn","message":"Module not found: ./shared/ai/llm-coordinator. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:39:30.694Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:39:30.694Z"}
{"level":"warn","message":"Module not found: ./engines/exchange/ProductionExchangeConnector. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:39:30.695Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:42:11.409Z"}
{"level":"warn","message":"Module not found: ./shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:42:11.859Z"}
{"level":"warn","message":"Module not found: ./shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:42:11.860Z"}
{"level":"warn","message":"Module not found: ./shared/ai/llm-coordinator. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:42:11.860Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:42:11.861Z"}
{"level":"warn","message":"Module not found: ./engines/exchange/ProductionExchangeConnector. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:42:11.862Z"}
{"level":"warn","message":"DataCollector component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-17T14:42:11.863Z"}
{"level":"info","message":"Starting trading system initialization...","service":"trading-system","timestamp":"2025-07-17T14:42:11.863Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-17T14:42:11.864Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-17T14:42:11.896Z"}
{"level":"info","message":"🐋 Elite Whale Tracker initialized","service":"trading-system","timestamp":"2025-07-17T14:42:11.899Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.components.exchangeManager.startHeartbeatMonitor is not a function","service":"trading-system","stack":"TypeError: this.components.exchangeManager.startHeartbeatMonitor is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:193:51)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:13)\n    at async testBackend (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-backend.js:45:13)","timestamp":"2025-07-17T14:42:11.902Z"}
{"level":"error","message":"Failed to initialize Trading System: this.components.exchangeManager.startHeartbeatMonitor is not a function","service":"trading-system","stack":"TypeError: this.components.exchangeManager.startHeartbeatMonitor is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:193:51)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:13)\n    at async testBackend (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\test-backend.js:45:13)","timestamp":"2025-07-17T14:42:11.902Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:43:41.188Z"}
{"level":"warn","message":"Module not found: ./shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:43:41.659Z"}
{"level":"warn","message":"Module not found: ./shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:43:41.660Z"}
{"level":"warn","message":"Module not found: ./shared/ai/llm-coordinator. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:43:41.660Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:43:41.661Z"}
{"level":"warn","message":"Module not found: ./engines/exchange/ProductionExchangeConnector. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T14:43:41.662Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:01:12.661Z"}
{"level":"warn","message":"Module not found: ./shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:01:13.082Z"}
{"level":"warn","message":"Module not found: ./shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:01:13.082Z"}
{"level":"warn","message":"Module not found: ./shared/ai/llm-coordinator. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:01:13.083Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:01:13.083Z"}
{"level":"warn","message":"Module not found: ./engines/exchange/ProductionExchangeConnector. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:01:13.084Z"}
{"level":"info","message":"Starting trading system initialization...","service":"trading-system","timestamp":"2025-07-17T15:01:13.085Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-17T15:01:13.085Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-17T15:01:13.094Z"}
{"level":"info","message":"🐋 Elite Whale Tracker initialized","service":"trading-system","timestamp":"2025-07-17T15:01:13.096Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: this.components.exchangeManager.startHeartbeatMonitor is not a function","service":"trading-system","stack":"TypeError: this.components.exchangeManager.startHeartbeatMonitor is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:193:51)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:13)","timestamp":"2025-07-17T15:01:13.098Z"}
{"level":"error","message":"Failed to initialize Trading System: this.components.exchangeManager.startHeartbeatMonitor is not a function","service":"trading-system","stack":"TypeError: this.components.exchangeManager.startHeartbeatMonitor is not a function\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:193:51)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async TradingSystemInterface.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\index.js:44:13)","timestamp":"2025-07-17T15:01:13.099Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:01:52.497Z"}
{"level":"warn","message":"Module not found: ./shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:01:52.862Z"}
{"level":"warn","message":"Module not found: ./shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:01:52.863Z"}
{"level":"warn","message":"Module not found: ./shared/ai/llm-coordinator. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:01:52.863Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:01:52.864Z"}
{"level":"warn","message":"Module not found: ./engines/exchange/ProductionExchangeConnector. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:01:52.865Z"}
{"level":"info","message":"Starting trading system initialization...","service":"trading-system","timestamp":"2025-07-17T15:01:52.866Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-17T15:01:52.866Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-17T15:01:52.872Z"}
{"level":"info","message":"🐋 Elite Whale Tracker initialized","service":"trading-system","timestamp":"2025-07-17T15:01:52.876Z"}
{"level":"info","message":"Grid Bot Manager initialized","service":"trading-system","timestamp":"2025-07-17T15:01:52.877Z"}
{"level":"info","message":"🐋 Initializing Elite Whale Tracker...","service":"trading-system","timestamp":"2025-07-17T15:01:52.877Z"}
{"level":"info","message":"MemeCoinScanner initialized with 0 active opportunities","service":"trading-system","timestamp":"2025-07-17T15:01:52.881Z"}
{"level":"warn","message":"⚠️ bsc health check failed","service":"trading-system","timestamp":"2025-07-17T15:01:52.982Z"}
{"level":"info","message":"✅ Elite Whale Tracker initialized successfully","service":"trading-system","timestamp":"2025-07-17T15:01:52.983Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:03:04.594Z"}
{"level":"warn","message":"Module not found: ./shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:03:04.930Z"}
{"level":"warn","message":"Module not found: ./shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:03:04.931Z"}
{"level":"warn","message":"Module not found: ./shared/ai/llm-coordinator. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:03:04.931Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:03:04.931Z"}
{"level":"warn","message":"Module not found: ./engines/exchange/ProductionExchangeConnector. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T15:03:04.932Z"}
{"level":"info","message":"=== Starting Trading System Tests ===","service":"trading-system","timestamp":"2025-07-17T15:03:04.933Z"}
{"level":"info","message":"Starting trading system initialization...","service":"trading-system","timestamp":"2025-07-17T15:03:04.933Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-17T15:03:04.933Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-17T15:03:04.939Z"}
{"level":"info","message":"🐋 Elite Whale Tracker initialized","service":"trading-system","timestamp":"2025-07-17T15:03:04.943Z"}
{"level":"info","message":"Grid Bot Manager initialized","service":"trading-system","timestamp":"2025-07-17T15:03:04.944Z"}
{"level":"info","message":"🐋 Initializing Elite Whale Tracker...","service":"trading-system","timestamp":"2025-07-17T15:03:04.945Z"}
{"level":"info","message":"MemeCoinScanner initialized with 0 active opportunities","service":"trading-system","timestamp":"2025-07-17T15:03:04.946Z"}
{"level":"info","message":"✅ Elite Whale Tracker initialized successfully","service":"trading-system","timestamp":"2025-07-17T15:03:05.060Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T17:34:19.435Z"}
{"level":"warn","message":"Module not found: ./shared/ai/llm-coordinator. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T17:34:20.186Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T17:34:20.186Z"}
{"level":"warn","message":"Module not found: ./engines/exchange/ProductionExchangeConnector. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T17:34:20.187Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T21:24:58.882Z"}
{"level":"warn","message":"Module not found: ./shared/ai/llm-coordinator. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T21:24:59.520Z"}
{"level":"warn","message":"Module not found: ./engines/exchange/ProductionExchangeConnector. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T21:24:59.523Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T21:25:46.160Z"}
{"level":"warn","message":"Module not found: ./shared/ai/llm-coordinator. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T21:25:46.541Z"}
{"level":"warn","message":"Module not found: ./engines/exchange/ProductionExchangeConnector. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T21:25:46.543Z"}
{"level":"warn","message":"Module not found: ./shared/ai/llm-coordinator. Using mock fallback.","service":"trading-system","timestamp":"2025-07-17T21:36:59.668Z"}
{"level":"info","message":"📦 Creating component instances...","service":"trading-system","timestamp":"2025-07-17T22:14:05.580Z"}
{"level":"info","message":"🔧 Initializing Configuration Manager...","service":"trading-system","timestamp":"2025-07-17T22:14:05.589Z"}
{"level":"info","message":"🔧 Loading configuration for environment: development","service":"trading-system","timestamp":"2025-07-17T22:14:05.590Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-17T22:14:05.603Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-17T22:14:05.607Z"}
{"level":"info","message":"👁️ Setting up configuration file watching...","service":"trading-system","timestamp":"2025-07-17T22:14:05.607Z"}
{"level":"info","message":"✅ Configuration loaded successfully in 28ms","service":"trading-system","timestamp":"2025-07-17T22:14:05.618Z"}
{"level":"info","message":"✅ Configuration Manager initialized successfully","service":"trading-system","timestamp":"2025-07-17T22:14:05.619Z"}
{"level":"info","message":"🐋 Elite Whale Tracker initialized","service":"trading-system","timestamp":"2025-07-17T22:14:05.625Z"}
{"level":"info","message":"🚀 Starting enhanced database initialization sequence...","service":"trading-system","timestamp":"2025-07-18T08:44:41.702Z"}
{"level":"info","message":"🔍 Running pre-initialization checks...","service":"trading-system","timestamp":"2025-07-18T08:44:41.705Z"}
{"level":"info","message":"[DB-INIT] ⚠️ Low disk space detected","service":"trading-system","timestamp":"2025-07-18T08:44:41.706Z"}
{"level":"info","message":"[DB-INIT] ✅ Write permissions verified for trading","service":"trading-system","timestamp":"2025-07-18T08:44:41.708Z"}
{"level":"info","message":"[DB-INIT] ✅ Write permissions verified for n8n","service":"trading-system","timestamp":"2025-07-18T08:44:41.708Z"}
{"level":"info","message":"[DB-INIT] ✅ Write permissions verified for credentials","service":"trading-system","timestamp":"2025-07-18T08:44:41.709Z"}
{"level":"info","message":"[DB-INIT] 📋 SQLite version: 3.44.2","service":"trading-system","timestamp":"2025-07-18T08:44:41.715Z"}
{"level":"info","message":"📊 Initializing trading with enhanced features...","service":"trading-system","timestamp":"2025-07-18T08:44:41.716Z"}
{"level":"info","message":"[DB-INIT] ❌ Failed to initialize trading: SQLITE_ERROR: Safety level may not be changed inside a transaction","service":"trading-system","timestamp":"2025-07-18T08:44:41.721Z"}
{"level":"info","message":"📊 Initializing n8n with enhanced features...","service":"trading-system","timestamp":"2025-07-18T08:44:41.721Z"}
{"level":"info","message":"[DB-INIT] ❌ Failed to initialize n8n: SQLITE_ERROR: Safety level may not be changed inside a transaction","service":"trading-system","timestamp":"2025-07-18T08:44:41.725Z"}
{"level":"info","message":"📊 Initializing credentials with enhanced features...","service":"trading-system","timestamp":"2025-07-18T08:44:41.726Z"}
{"level":"info","message":"[DB-INIT] ❌ Failed to initialize credentials: SQLITE_ERROR: cannot change out of wal mode from within a transaction","service":"trading-system","timestamp":"2025-07-18T08:44:41.728Z"}
{"level":"info","message":"🔗 Setting up connection pooling...","service":"trading-system","timestamp":"2025-07-18T08:44:41.728Z"}
{"level":"info","message":"[DB-INIT] ✅ Connection pool created for trading: 2 connections","service":"trading-system","timestamp":"2025-07-18T08:44:41.729Z"}
{"level":"info","message":"[DB-INIT] ✅ Connection pool created for n8n: 2 connections","service":"trading-system","timestamp":"2025-07-18T08:44:41.730Z"}
{"level":"info","message":"[DB-INIT] ✅ Connection pool created for credentials: 2 connections","service":"trading-system","timestamp":"2025-07-18T08:44:41.731Z"}
{"level":"info","message":"❤️ Initializing database health monitoring...","service":"trading-system","timestamp":"2025-07-18T08:44:41.731Z"}
{"level":"info","message":"[DB-INIT] ✅ Health monitoring initialized","service":"trading-system","timestamp":"2025-07-18T08:44:41.731Z"}
{"level":"info","message":"🔄 Running database migrations...","service":"trading-system","timestamp":"2025-07-18T08:44:41.732Z"}
{"level":"info","message":"[DB-INIT] ❌ Migration failed: 001_initial_trading_schema.sql - SQLITE_ERROR: Safety level may not be changed inside a transaction","service":"trading-system","timestamp":"2025-07-18T08:44:41.735Z"}
{"level":"info","message":"[DB-INIT] ❌ Migration failed: 002_n8n_workflow_schema.sql - SQLITE_ERROR: Safety level may not be changed inside a transaction","service":"trading-system","timestamp":"2025-07-18T08:44:41.737Z"}
{"level":"info","message":"[DB-INIT] ❌ Migration failed: 003_credentials_security_schema.sql - SQLITE_ERROR: cannot change out of wal mode from within a transaction","service":"trading-system","timestamp":"2025-07-18T08:44:41.739Z"}
{"level":"info","message":"[DB-INIT] ✅ Completed 3 migrations","service":"trading-system","timestamp":"2025-07-18T08:44:41.739Z"}
{"level":"info","message":"⚡ Optimizing databases...","service":"trading-system","timestamp":"2025-07-18T08:44:42.717Z"}
{"level":"info","message":"[DB-INIT] ✅ Optimized trading database","service":"trading-system","timestamp":"2025-07-18T08:44:43.131Z"}
{"level":"info","message":"[DB-INIT] ✅ Optimized n8n database","service":"trading-system","timestamp":"2025-07-18T08:44:43.141Z"}
{"level":"info","message":"[DB-INIT] ✅ Optimized credentials database","service":"trading-system","timestamp":"2025-07-18T08:44:43.145Z"}
{"level":"info","message":"[DB-INIT] 📋 Enhanced initialization report saved: C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\logs\\enhanced-database-initialization-report.json","service":"trading-system","timestamp":"2025-07-18T08:44:43.146Z"}
{"level":"info","message":"✅ Enhanced database initialization completed in 1442ms","service":"trading-system","timestamp":"2025-07-18T08:44:43.147Z"}
{"level":"info","message":"Database connection established: trading","service":"trading-system","timestamp":"2025-07-18T08:44:43.166Z"}
{"level":"info","message":"Database connection established: n8n","service":"trading-system","timestamp":"2025-07-18T08:44:43.168Z"}
{"level":"info","message":"Database connection established: credentials","service":"trading-system","timestamp":"2025-07-18T08:44:43.169Z"}
{"level":"info","message":"🏥 Starting database health monitoring...","service":"trading-system","timestamp":"2025-07-18T08:44:43.597Z"}
{"level":"info","message":"✅ Initialized health check for trading","service":"trading-system","timestamp":"2025-07-18T08:44:43.597Z"}
{"level":"info","message":"✅ Initialized health check for n8n","service":"trading-system","timestamp":"2025-07-18T08:44:43.597Z"}
{"level":"info","message":"✅ Initialized health check for credentials","service":"trading-system","timestamp":"2025-07-18T08:44:43.597Z"}
{"level":"info","message":"📊 Establishing performance baselines...","service":"trading-system","timestamp":"2025-07-18T08:44:43.598Z"}
{"level":"info","message":"📈 Baseline established for trading: 25.45ms avg response","service":"trading-system","timestamp":"2025-07-18T08:44:44.618Z"}
{"level":"info","message":"📈 Baseline established for n8n: 0.175ms avg response","service":"trading-system","timestamp":"2025-07-18T08:44:44.626Z"}
{"level":"info","message":"📈 Baseline established for credentials: 0.075ms avg response","service":"trading-system","timestamp":"2025-07-18T08:44:44.629Z"}
{"level":"info","message":"✅ Database health monitoring started","service":"trading-system","timestamp":"2025-07-18T08:44:44.630Z"}
{"level":"info","message":"🛑 Stopping database health monitoring...","service":"trading-system","timestamp":"2025-07-18T08:44:44.739Z"}
{"level":"info","message":"✅ Database health monitoring stopped","service":"trading-system","timestamp":"2025-07-18T08:44:44.740Z"}
{"level":"error","message":"Test suite failed: Converting circular structure to JSON\n    --> starting at object with constructor 'Timeout'\n    |     property '_idlePrev' -> object with constructor 'TimersList'\n    --- property '_idleNext' closes the circle","service":"trading-system","stack":"TypeError: Converting circular structure to JSON\n    --> starting at object with constructor 'Timeout'\n    |     property '_idlePrev' -> object with constructor 'TimersList'\n    --- property '_idleNext' closes the circle\n    at JSON.stringify (<anonymous>)\n    at DatabaseIntegrationTester.generateTestReport (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\tests\\test-database-integration.js:1069:45)\n    at async DatabaseIntegrationTester.runAllTests (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\tests\\test-database-integration.js:61:13)","timestamp":"2025-07-18T08:44:44.871Z"}
{"level":"info","message":"Database connection established: trading","service":"trading-system","timestamp":"2025-07-18T10:24:27.436Z"}
{"level":"info","message":"Database connection established: n8n","service":"trading-system","timestamp":"2025-07-18T10:24:27.604Z"}
{"level":"info","message":"Database connection established: credentials","service":"trading-system","timestamp":"2025-07-18T10:24:27.621Z"}
{"level":"info","message":"Closed database connection: trading","service":"trading-system","timestamp":"2025-07-18T10:24:27.654Z"}
{"level":"info","message":"Database connection established: trading","service":"trading-system","timestamp":"2025-07-18T10:24:54.082Z"}
{"level":"info","message":"Database connection established: n8n","service":"trading-system","timestamp":"2025-07-18T10:24:54.294Z"}
{"level":"info","message":"Database connection established: credentials","service":"trading-system","timestamp":"2025-07-18T10:24:54.298Z"}
{"level":"info","message":"Closed database connection: trading","service":"trading-system","timestamp":"2025-07-18T10:24:54.391Z"}
{"level":"info","message":"Database connection established: trading","service":"trading-system","timestamp":"2025-07-18T10:25:24.257Z"}
{"level":"info","message":"Database connection established: n8n","service":"trading-system","timestamp":"2025-07-18T10:25:24.463Z"}
{"level":"info","message":"Database connection established: credentials","service":"trading-system","timestamp":"2025-07-18T10:25:24.466Z"}
{"level":"info","message":"Closed database connection: trading","service":"trading-system","timestamp":"2025-07-18T10:25:24.541Z"}
{"level":"info","message":"Validating system configuration...","service":"trading-system","timestamp":"2025-07-18T10:40:40.783Z"}
{"level":"info","message":"🔧 Initializing configuration loading system for development...","service":"trading-system","timestamp":"2025-07-18T10:40:40.788Z"}
{"level":"info","message":"✅ Configuration loading completed in 9ms","service":"trading-system","timestamp":"2025-07-18T10:40:40.797Z"}
{"level":"info","message":"📋 Loaded 17 configuration files","service":"trading-system","timestamp":"2025-07-18T10:40:40.797Z"}
{"level":"info","message":"✅ Configuration system validation completed:","service":"trading-system","timestamp":"2025-07-18T10:40:40.797Z"}
{"level":"info","message":"  - Environment: development","service":"trading-system","timestamp":"2025-07-18T10:40:40.797Z"}
{"level":"info","message":"  - Files loaded: 17","service":"trading-system","timestamp":"2025-07-18T10:40:40.798Z"}
{"level":"info","message":"  - Config sections: 39","service":"trading-system","timestamp":"2025-07-18T10:40:40.798Z"}
{"level":"info","message":"  - Load order: environment-variables → base-configuration → app-configuration → environment-specific → exchange-configurations → strategy-configurations → override-configurations → validation → finalization","service":"trading-system","timestamp":"2025-07-18T10:40:40.798Z"}
{"level":"info","message":"✓ Auto trading disabled - System will run in monitoring mode","service":"trading-system","timestamp":"2025-07-18T10:40:40.798Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-18T12:45:35.669Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-18T12:45:35.676Z"}
{"level":"info","message":"📦 Creating component instances...","service":"trading-system","timestamp":"2025-07-18T12:45:35.677Z"}
{"level":"info","message":"🔧 Initializing configuration loading system for development...","service":"trading-system","timestamp":"2025-07-18T12:45:35.680Z"}
{"level":"info","message":"✅ Configuration loading completed in 7ms","service":"trading-system","timestamp":"2025-07-18T12:45:35.687Z"}
{"level":"info","message":"📋 Loaded 17 configuration files","service":"trading-system","timestamp":"2025-07-18T12:45:35.687Z"}
{"level":"info","message":"📋 Configuration loaded: 17 files, 39 config sections","service":"trading-system","timestamp":"2025-07-18T12:45:35.687Z"}
{"level":"info","message":"🔧 Initializing Configuration Manager...","service":"trading-system","timestamp":"2025-07-18T12:45:35.687Z"}
{"level":"info","message":"🔧 Loading configuration for environment: development","service":"trading-system","timestamp":"2025-07-18T12:45:35.688Z"}
{"level":"info","message":"Applied runtime overrides from environment variables","service":"trading-system","timestamp":"2025-07-18T12:45:35.691Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-18T12:45:35.692Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-18T12:45:35.692Z"}
{"level":"info","message":"👁️ Setting up configuration file watching...","service":"trading-system","timestamp":"2025-07-18T12:45:35.692Z"}
{"level":"info","message":"✅ Configuration loaded successfully in 8ms","service":"trading-system","timestamp":"2025-07-18T12:45:35.696Z"}
{"level":"info","message":"✅ Configuration Manager initialized successfully","service":"trading-system","timestamp":"2025-07-18T12:45:35.697Z"}
{"level":"info","message":"🐋 Elite Whale Tracker initialized","service":"trading-system","timestamp":"2025-07-18T12:45:35.699Z"}
{"level":"info","message":"🔧 Initializing components sequentially...","service":"trading-system","timestamp":"2025-07-18T12:45:35.699Z"}
{"level":"info","message":"🔧 Initializing component: database","service":"trading-system","timestamp":"2025-07-18T12:45:35.699Z"}
{"level":"info","message":"✅ Component database initialized in 0ms","service":"trading-system","timestamp":"2025-07-18T12:45:35.699Z"}
{"level":"info","message":"🔧 Initializing component: configManager","service":"trading-system","timestamp":"2025-07-18T12:45:35.699Z"}
{"level":"info","message":"✅ Component configManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-18T12:45:35.700Z"}
{"level":"info","message":"🔧 Initializing component: logger","service":"trading-system","timestamp":"2025-07-18T12:45:35.700Z"}
{"level":"info","message":"✅ Component logger initialized in 0ms","service":"trading-system","timestamp":"2025-07-18T12:45:35.700Z"}
{"level":"info","message":"🔧 Initializing component: circuitBreaker","service":"trading-system","timestamp":"2025-07-18T12:45:35.700Z"}
{"level":"info","message":"✅ Component circuitBreaker initialized in 1ms","service":"trading-system","timestamp":"2025-07-18T12:45:35.701Z"}
{"level":"info","message":"🔧 Initializing component: riskManager","service":"trading-system","timestamp":"2025-07-18T12:45:35.701Z"}
{"level":"info","message":"✅ Component riskManager initialized in 4ms","service":"trading-system","timestamp":"2025-07-18T12:45:35.705Z"}
{"level":"info","message":"🔧 Initializing component: exchangeManager","service":"trading-system","timestamp":"2025-07-18T12:45:35.705Z"}
{"level":"info","message":"✅ Component exchangeManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-18T12:45:35.705Z"}
{"level":"info","message":"🔧 Initializing component: dataCollector","service":"trading-system","timestamp":"2025-07-18T12:45:35.705Z"}
{"level":"info","message":"✅ Component dataCollector initialized in 4ms","service":"trading-system","timestamp":"2025-07-18T12:45:35.709Z"}
{"level":"info","message":"🔧 Initializing component: eventCoordinator","service":"trading-system","timestamp":"2025-07-18T12:45:35.709Z"}
{"level":"info","message":"✅ Component eventCoordinator initialized in 4ms","service":"trading-system","timestamp":"2025-07-18T12:45:35.713Z"}
{"level":"info","message":"🔧 Initializing component: performanceTracker","service":"trading-system","timestamp":"2025-07-18T12:45:35.713Z"}
{"level":"info","message":"✅ Component performanceTracker initialized in 2ms","service":"trading-system","timestamp":"2025-07-18T12:45:35.715Z"}
{"level":"info","message":"🔧 Initializing component: sentimentAnalyzer","service":"trading-system","timestamp":"2025-07-18T12:45:35.715Z"}
{"level":"info","message":"✅ Component sentimentAnalyzer initialized in 1ms","service":"trading-system","timestamp":"2025-07-18T12:45:35.716Z"}
{"level":"info","message":"🔧 Initializing component: portfolioManager","service":"trading-system","timestamp":"2025-07-18T12:45:35.716Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-18T12:50:43.868Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-18T12:50:43.875Z"}
{"level":"info","message":"📦 Creating component instances...","service":"trading-system","timestamp":"2025-07-18T12:50:43.876Z"}
{"level":"info","message":"🔧 Initializing configuration loading system for development...","service":"trading-system","timestamp":"2025-07-18T12:50:43.877Z"}
{"level":"info","message":"✅ Configuration loading completed in 8ms","service":"trading-system","timestamp":"2025-07-18T12:50:43.885Z"}
{"level":"info","message":"📋 Loaded 17 configuration files","service":"trading-system","timestamp":"2025-07-18T12:50:43.886Z"}
{"level":"info","message":"📋 Configuration loaded: 17 files, 39 config sections","service":"trading-system","timestamp":"2025-07-18T12:50:43.886Z"}
{"level":"info","message":"🔧 Initializing Configuration Manager...","service":"trading-system","timestamp":"2025-07-18T12:50:43.886Z"}
{"level":"info","message":"🔧 Loading configuration for environment: development","service":"trading-system","timestamp":"2025-07-18T12:50:43.886Z"}
{"level":"info","message":"Applied runtime overrides from environment variables","service":"trading-system","timestamp":"2025-07-18T12:50:43.890Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-18T12:50:43.890Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-18T12:50:43.891Z"}
{"level":"info","message":"👁️ Setting up configuration file watching...","service":"trading-system","timestamp":"2025-07-18T12:50:43.891Z"}
{"level":"info","message":"✅ Configuration loaded successfully in 10ms","service":"trading-system","timestamp":"2025-07-18T12:50:43.896Z"}
{"level":"info","message":"✅ Configuration Manager initialized successfully","service":"trading-system","timestamp":"2025-07-18T12:50:43.896Z"}
{"level":"info","message":"🐋 Elite Whale Tracker initialized","service":"trading-system","timestamp":"2025-07-18T12:50:43.897Z"}
{"level":"info","message":"🔧 Initializing components sequentially...","service":"trading-system","timestamp":"2025-07-18T12:50:43.897Z"}
{"level":"info","message":"🔧 Initializing component: database","service":"trading-system","timestamp":"2025-07-18T12:50:43.898Z"}
{"level":"info","message":"✅ Component database initialized in 0ms","service":"trading-system","timestamp":"2025-07-18T12:50:43.898Z"}
{"level":"info","message":"🔧 Initializing component: configManager","service":"trading-system","timestamp":"2025-07-18T12:50:43.898Z"}
{"level":"info","message":"✅ Component configManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-18T12:50:43.898Z"}
{"level":"info","message":"🔧 Initializing component: logger","service":"trading-system","timestamp":"2025-07-18T12:50:43.898Z"}
{"level":"info","message":"✅ Component logger initialized in 0ms","service":"trading-system","timestamp":"2025-07-18T12:50:43.898Z"}
{"level":"info","message":"🔧 Initializing component: circuitBreaker","service":"trading-system","timestamp":"2025-07-18T12:50:43.898Z"}
{"level":"info","message":"✅ Component circuitBreaker initialized in 1ms","service":"trading-system","timestamp":"2025-07-18T12:50:43.899Z"}
{"level":"info","message":"🔧 Initializing component: riskManager","service":"trading-system","timestamp":"2025-07-18T12:50:43.899Z"}
{"level":"info","message":"✅ Component riskManager initialized in 1ms","service":"trading-system","timestamp":"2025-07-18T12:50:43.900Z"}
{"level":"info","message":"🔧 Initializing component: exchangeManager","service":"trading-system","timestamp":"2025-07-18T12:50:43.900Z"}
{"level":"info","message":"✅ Component exchangeManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-18T12:50:43.900Z"}
{"level":"info","message":"🔧 Initializing component: dataCollector","service":"trading-system","timestamp":"2025-07-18T12:50:43.900Z"}
{"level":"info","message":"✅ Component dataCollector initialized in 3ms","service":"trading-system","timestamp":"2025-07-18T12:50:43.903Z"}
{"level":"info","message":"🔧 Initializing component: eventCoordinator","service":"trading-system","timestamp":"2025-07-18T12:50:43.903Z"}
{"level":"info","message":"✅ Component eventCoordinator initialized in 1ms","service":"trading-system","timestamp":"2025-07-18T12:50:43.904Z"}
{"level":"info","message":"🔧 Initializing component: performanceTracker","service":"trading-system","timestamp":"2025-07-18T12:50:43.904Z"}
{"level":"info","message":"✅ Component performanceTracker initialized in 1ms","service":"trading-system","timestamp":"2025-07-18T12:50:43.905Z"}
{"level":"info","message":"🔧 Initializing component: sentimentAnalyzer","service":"trading-system","timestamp":"2025-07-18T12:50:43.905Z"}
{"level":"info","message":"✅ Component sentimentAnalyzer initialized in 0ms","service":"trading-system","timestamp":"2025-07-18T12:50:43.905Z"}
{"level":"info","message":"🔧 Initializing component: portfolioManager","service":"trading-system","timestamp":"2025-07-18T12:50:43.905Z"}
{"level":"info","message":"Grid Bot Manager initialized","service":"trading-system","timestamp":"2025-07-18T12:51:55.222Z"}
{"level":"info","message":"Grid Bot Manager started","service":"trading-system","timestamp":"2025-07-18T12:51:55.230Z"}
{"level":"info","message":"Grid bot created: grid_1752843115231_4bvvijrta for BTC/USDT","service":"trading-system","timestamp":"2025-07-18T12:51:55.231Z"}
{"level":"info","message":"Grid bot started: grid_1752843115231_4bvvijrta for BTC/USDT","service":"trading-system","timestamp":"2025-07-18T12:51:55.231Z"}
{"level":"info","message":"Grid bot created: grid_1752843115232_6zg5fxz5b for ETH/USDT","service":"trading-system","timestamp":"2025-07-18T12:51:55.232Z"}
{"level":"info","message":"Grid bot started: grid_1752843115232_6zg5fxz5b for ETH/USDT","service":"trading-system","timestamp":"2025-07-18T12:51:55.232Z"}
{"level":"info","message":"Grid bot stopped: grid_1752843115231_4bvvijrta","service":"trading-system","timestamp":"2025-07-18T12:51:55.237Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:10:44.610Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:10:44.649Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:10:44.660Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:10:44.668Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:10:44.683Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:10:44.690Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:14:56.208Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:14:56.220Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:14:56.228Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:14:56.236Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:14:56.248Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:14:56.254Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:14:56.275Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:14:56.280Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:14:56.289Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:25.525Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:25.537Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:25.545Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:25.555Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:25.564Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:25.570Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:25.589Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:25.594Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:25.604Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:44.497Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:44.510Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:44.519Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:44.528Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:44.537Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:44.544Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:44.564Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:44.569Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:15:44.580Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:16:03.100Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:16:03.112Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:16:03.120Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:16:03.128Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:16:03.138Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:16:03.144Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:16:03.162Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:16:03.168Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:16:03.178Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:30:14.562Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:30:14.585Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:30:14.592Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:30:14.599Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:30:14.612Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:30:14.617Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:30:14.698Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:30:14.703Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:30:14.726Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:45:51.249Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:45:51.263Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:45:51.274Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:45:51.283Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:45:51.299Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:45:51.304Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:45:51.334Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:45:51.341Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:45:51.352Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:01.267Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:01.282Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:01.293Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:01.302Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:01.319Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:01.326Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:01.353Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:01.361Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:01.370Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:19.061Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:19.077Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:19.090Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:19.099Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:19.116Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:19.126Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:19.156Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:19.161Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:47:19.171Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:49:32.102Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:49:32.139Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:49:32.162Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:49:32.173Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:49:32.199Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:49:32.212Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:49:32.254Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:49:32.270Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T11:49:32.295Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:47:21.266Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:47:21.270Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:47:21.271Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:47:21.272Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:47:21.273Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:47:21.274Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:47:21.283Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:47:21.283Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:47:21.286Z"}
{"level":"info","message":"=== Starting Trading System Tests ===","service":"trading-system","timestamp":"2025-07-19T12:47:21.570Z"}
{"level":"info","message":"Starting trading system initialization...","service":"trading-system","timestamp":"2025-07-19T12:47:21.571Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-19T12:47:21.571Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-19T12:47:21.580Z"}
{"level":"info","message":"📦 Creating component instances...","service":"trading-system","timestamp":"2025-07-19T12:47:21.580Z"}
{"level":"info","message":"🔧 Initializing configuration loading system for development...","service":"trading-system","timestamp":"2025-07-19T12:47:21.581Z"}
{"level":"info","message":"✅ Configuration loading completed in 6ms","service":"trading-system","timestamp":"2025-07-19T12:47:21.587Z"}
{"level":"info","message":"📋 Loaded 17 configuration files","service":"trading-system","timestamp":"2025-07-19T12:47:21.587Z"}
{"level":"info","message":"📋 Configuration loaded: 17 files, 39 config sections","service":"trading-system","timestamp":"2025-07-19T12:47:21.587Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: Unexpected token '('","service":"trading-system","stack":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\config\\config-manager.js:45\n        this.initializationPromise = this.(options);\n                                          ^\n\nSyntaxError: Unexpected token '('\n    at wrapSafe (node:internal/modules/cjs/loader:1383:18)\n    at Module._compile (node:internal/modules/cjs/loader:1412:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)\n    at Module.load (node:internal/modules/cjs/loader:1282:32)\n    at Module._load (node:internal/modules/cjs/loader:1098:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)\n    at Module.require (node:internal/modules/cjs/loader:1304:12)\n    at require (node:internal/modules/helpers:123:16)\n    at TradingOrchestrator.createComponentInstances (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:218:31)","timestamp":"2025-07-19T12:47:21.588Z"}
{"level":"error","message":"Failed to initialize Trading System: Unexpected token '('","service":"trading-system","stack":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\config\\config-manager.js:45\n        this.initializationPromise = this.(options);\n                                          ^\n\nSyntaxError: Unexpected token '('\n    at wrapSafe (node:internal/modules/cjs/loader:1383:18)\n    at Module._compile (node:internal/modules/cjs/loader:1412:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)\n    at Module.load (node:internal/modules/cjs/loader:1282:32)\n    at Module._load (node:internal/modules/cjs/loader:1098:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)\n    at Module.require (node:internal/modules/cjs/loader:1304:12)\n    at require (node:internal/modules/helpers:123:16)\n    at TradingOrchestrator.createComponentInstances (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:218:31)","timestamp":"2025-07-19T12:47:21.588Z"}
{"0":"U","1":"n","10":" ","11":"t","12":"o","13":"k","14":"e","15":"n","16":" ","17":"'","18":"(","19":"'","2":"e","3":"x","4":"p","5":"e","6":"c","7":"t","8":"e","9":"d","level":"error","message":"❌ System Initialization - FAILED (17ms)","service":"trading-system","timestamp":"2025-07-19T12:47:21.588Z"}
{"components":{"circuitBreaker":"not_initialized","configManager":"ready","dataCollector":"not_initialized","errorHandler":"not_initialized","eventCoordinator":"not_initialized","gridBotManager":"not_initialized","llmCoordinator":"not_initialized","memeCoinScanner":"not_initialized","performanceTracker":"not_initialized","portfolioManager":"not_initialized","riskManager":"not_initialized","sentimentAnalyzer":"not_initialized","tradingExecutor":"not_initialized","whaleTracker":"not_initialized"},"isInitialized":false,"isRunning":false,"level":"info","message":"Bot Status:","service":"trading-system","timestamp":"2025-07-19T12:47:21.589Z","workflowState":{"activeSignals":{},"currentCycle":0,"healthStatus":"unknown","lastAnalysis":null,"lastDataCollection":null,"lastTradeExecution":null,"marketData":null,"pendingTrades":[],"performance":{"successfulTrades":0,"totalProfit":0,"totalTrades":0}}}
{"level":"info","message":"✅ Get Bot Status - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:47:21.589Z"}
{"level":"info","message":"Settings loaded successfully","service":"trading-system","timestamp":"2025-07-19T12:47:21.589Z"}
{"level":"info","message":"✅ Get Settings - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:47:21.589Z"}
{"level":"warn","message":"DataCollector component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:47:21.589Z"}
{"level":"info","message":"Found 0 coins","service":"trading-system","timestamp":"2025-07-19T12:47:21.589Z"}
{"level":"info","message":"✅ Get Coins - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:47:21.589Z"}
{"level":"warn","message":"PerformanceTracker component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:47:21.589Z"}
{"level":"info","message":"Trading stats retrieved","service":"trading-system","timestamp":"2025-07-19T12:47:21.589Z"}
{"level":"info","message":"✅ Get Trading Stats - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:47:21.589Z"}
{"level":"warn","message":"PerformanceTracker component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:47:21.589Z"}
{"level":"info","message":"Performance metrics retrieved","service":"trading-system","timestamp":"2025-07-19T12:47:21.589Z"}
{"level":"info","message":"✅ Get Performance Metrics - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:47:21.589Z"}
{"level":"warn","message":"PortfolioManager component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:47:21.589Z"}
{"level":"info","message":"Wallet balance retrieved","service":"trading-system","timestamp":"2025-07-19T12:47:21.589Z"}
{"level":"info","message":"✅ Get Wallet Balance - PASSED (1ms)","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"components":{"circuitBreaker":"not_initialized","configManager":"ready","dataCollector":"not_initialized","errorHandler":"not_initialized","eventCoordinator":"not_initialized","gridBotManager":"not_initialized","llmCoordinator":"not_initialized","memeCoinScanner":"not_initialized","performanceTracker":"not_initialized","portfolioManager":"not_initialized","riskManager":"not_initialized","sentimentAnalyzer":"not_initialized","tradingExecutor":"not_initialized","whaleTracker":"not_initialized"},"isInitialized":false,"isRunning":false,"level":"info","message":"Health check completed:","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z","workflowState":{"activeSignals":{},"currentCycle":0,"healthStatus":"unknown","lastAnalysis":null,"lastDataCollection":null,"lastTradeExecution":null,"marketData":null,"pendingTrades":[],"performance":{"successfulTrades":0,"totalProfit":0,"totalTrades":0}}}
{"level":"info","message":"✅ Health Check - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"warn","message":"GridBotManager component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"info","message":"Grid bot positions: 0","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"info","message":"✅ Check Grid Bot Manager - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"warn","message":"WhaleTracker component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"info","message":"Tracked whales: 0","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"info","message":"✅ Check Whale Tracker - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"warn","message":"MemeCoinScanner component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"info","message":"Meme scanner status:","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"info","message":"✅ Check Meme Scanner Status - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"info","message":"\n=== Test Results Summary ===","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"info","message":"Total Tests: 11","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"info","message":"Passed: 10 ✅","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"info","message":"Failed: 1 ❌","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"info","message":"\nFailed Tests:","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"error","message":"- System Initialization: Unexpected token '('","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"info","message":"\n=== End of Test Results ===","service":"trading-system","timestamp":"2025-07-19T12:47:21.590Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:14.753Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:14.756Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:14.757Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:14.758Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:14.759Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:14.760Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:14.769Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:14.770Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:14.772Z"}
{"level":"info","message":"=== Starting Trading System Tests ===","service":"trading-system","timestamp":"2025-07-19T12:51:15.131Z"}
{"level":"info","message":"Starting trading system initialization...","service":"trading-system","timestamp":"2025-07-19T12:51:15.131Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-19T12:51:15.131Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-19T12:51:15.138Z"}
{"level":"info","message":"📦 Creating component instances...","service":"trading-system","timestamp":"2025-07-19T12:51:15.138Z"}
{"level":"info","message":"🔧 Initializing configuration loading system for development...","service":"trading-system","timestamp":"2025-07-19T12:51:15.139Z"}
{"level":"info","message":"✅ Configuration loading completed in 7ms","service":"trading-system","timestamp":"2025-07-19T12:51:15.146Z"}
{"level":"info","message":"📋 Loaded 17 configuration files","service":"trading-system","timestamp":"2025-07-19T12:51:15.146Z"}
{"level":"info","message":"📋 Configuration loaded: 17 files, 39 config sections","service":"trading-system","timestamp":"2025-07-19T12:51:15.146Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: Unexpected token '('","service":"trading-system","stack":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\config\\config-manager.js:45\n        this.initializationPromise = this.(options);\n                                          ^\n\nSyntaxError: Unexpected token '('\n    at wrapSafe (node:internal/modules/cjs/loader:1383:18)\n    at Module._compile (node:internal/modules/cjs/loader:1412:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)\n    at Module.load (node:internal/modules/cjs/loader:1282:32)\n    at Module._load (node:internal/modules/cjs/loader:1098:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)\n    at Module.require (node:internal/modules/cjs/loader:1304:12)\n    at require (node:internal/modules/helpers:123:16)\n    at TradingOrchestrator.createComponentInstances (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:218:31)","timestamp":"2025-07-19T12:51:15.147Z"}
{"level":"error","message":"Failed to initialize Trading System: Unexpected token '('","service":"trading-system","stack":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\config\\config-manager.js:45\n        this.initializationPromise = this.(options);\n                                          ^\n\nSyntaxError: Unexpected token '('\n    at wrapSafe (node:internal/modules/cjs/loader:1383:18)\n    at Module._compile (node:internal/modules/cjs/loader:1412:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)\n    at Module.load (node:internal/modules/cjs/loader:1282:32)\n    at Module._load (node:internal/modules/cjs/loader:1098:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)\n    at Module.require (node:internal/modules/cjs/loader:1304:12)\n    at require (node:internal/modules/helpers:123:16)\n    at TradingOrchestrator.createComponentInstances (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:218:31)","timestamp":"2025-07-19T12:51:15.147Z"}
{"0":"U","1":"n","10":" ","11":"t","12":"o","13":"k","14":"e","15":"n","16":" ","17":"'","18":"(","19":"'","2":"e","3":"x","4":"p","5":"e","6":"c","7":"t","8":"e","9":"d","level":"error","message":"❌ System Initialization - FAILED (16ms)","service":"trading-system","timestamp":"2025-07-19T12:51:15.147Z"}
{"components":{"circuitBreaker":"not_initialized","configManager":"ready","dataCollector":"not_initialized","errorHandler":"not_initialized","eventCoordinator":"not_initialized","gridBotManager":"not_initialized","llmCoordinator":"not_initialized","memeCoinScanner":"not_initialized","performanceTracker":"not_initialized","portfolioManager":"not_initialized","riskManager":"not_initialized","sentimentAnalyzer":"not_initialized","tradingExecutor":"not_initialized","whaleTracker":"not_initialized"},"isInitialized":false,"isRunning":false,"level":"info","message":"Bot Status:","service":"trading-system","timestamp":"2025-07-19T12:51:15.147Z","workflowState":{"activeSignals":{},"currentCycle":0,"healthStatus":"unknown","lastAnalysis":null,"lastDataCollection":null,"lastTradeExecution":null,"marketData":null,"pendingTrades":[],"performance":{"successfulTrades":0,"totalProfit":0,"totalTrades":0}}}
{"level":"info","message":"✅ Get Bot Status - PASSED (1ms)","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"Settings loaded successfully","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"✅ Get Settings - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"warn","message":"DataCollector component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"Found 0 coins","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"✅ Get Coins - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"warn","message":"PerformanceTracker component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"Trading stats retrieved","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"✅ Get Trading Stats - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"warn","message":"PerformanceTracker component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"Performance metrics retrieved","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"✅ Get Performance Metrics - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"warn","message":"PortfolioManager component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"Wallet balance retrieved","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"✅ Get Wallet Balance - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"components":{"circuitBreaker":"not_initialized","configManager":"ready","dataCollector":"not_initialized","errorHandler":"not_initialized","eventCoordinator":"not_initialized","gridBotManager":"not_initialized","llmCoordinator":"not_initialized","memeCoinScanner":"not_initialized","performanceTracker":"not_initialized","portfolioManager":"not_initialized","riskManager":"not_initialized","sentimentAnalyzer":"not_initialized","tradingExecutor":"not_initialized","whaleTracker":"not_initialized"},"isInitialized":false,"isRunning":false,"level":"info","message":"Health check completed:","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z","workflowState":{"activeSignals":{},"currentCycle":0,"healthStatus":"unknown","lastAnalysis":null,"lastDataCollection":null,"lastTradeExecution":null,"marketData":null,"pendingTrades":[],"performance":{"successfulTrades":0,"totalProfit":0,"totalTrades":0}}}
{"level":"info","message":"✅ Health Check - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"warn","message":"GridBotManager component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"Grid bot positions: 0","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"✅ Check Grid Bot Manager - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"warn","message":"WhaleTracker component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"Tracked whales: 0","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"✅ Check Whale Tracker - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"warn","message":"MemeCoinScanner component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"Meme scanner status:","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"✅ Check Meme Scanner Status - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:15.148Z"}
{"level":"info","message":"\n=== Test Results Summary ===","service":"trading-system","timestamp":"2025-07-19T12:51:15.149Z"}
{"level":"info","message":"Total Tests: 11","service":"trading-system","timestamp":"2025-07-19T12:51:15.149Z"}
{"level":"info","message":"Passed: 10 ✅","service":"trading-system","timestamp":"2025-07-19T12:51:15.149Z"}
{"level":"info","message":"Failed: 1 ❌","service":"trading-system","timestamp":"2025-07-19T12:51:15.149Z"}
{"level":"info","message":"\nFailed Tests:","service":"trading-system","timestamp":"2025-07-19T12:51:15.149Z"}
{"level":"error","message":"- System Initialization: Unexpected token '('","service":"trading-system","timestamp":"2025-07-19T12:51:15.149Z"}
{"level":"info","message":"\n=== End of Test Results ===","service":"trading-system","timestamp":"2025-07-19T12:51:15.149Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:57.128Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:57.132Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:57.133Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:57.134Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:57.135Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:57.136Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:57.147Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:57.148Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T12:51:57.151Z"}
{"level":"info","message":"=== Starting Trading System Tests ===","service":"trading-system","timestamp":"2025-07-19T12:51:57.526Z"}
{"level":"info","message":"Starting trading system initialization...","service":"trading-system","timestamp":"2025-07-19T12:51:57.526Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-19T12:51:57.526Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-19T12:51:57.530Z"}
{"level":"info","message":"📦 Creating component instances...","service":"trading-system","timestamp":"2025-07-19T12:51:57.530Z"}
{"level":"info","message":"🔧 Initializing configuration loading system for development...","service":"trading-system","timestamp":"2025-07-19T12:51:57.531Z"}
{"level":"info","message":"✅ Configuration loading completed in 7ms","service":"trading-system","timestamp":"2025-07-19T12:51:57.538Z"}
{"level":"info","message":"📋 Loaded 17 configuration files","service":"trading-system","timestamp":"2025-07-19T12:51:57.538Z"}
{"level":"info","message":"📋 Configuration loaded: 17 files, 39 config sections","service":"trading-system","timestamp":"2025-07-19T12:51:57.538Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: Unexpected token '('","service":"trading-system","stack":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\config\\config-manager.js:45\n        this.initializationPromise = this.(options);\n                                          ^\n\nSyntaxError: Unexpected token '('\n    at wrapSafe (node:internal/modules/cjs/loader:1383:18)\n    at Module._compile (node:internal/modules/cjs/loader:1412:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)\n    at Module.load (node:internal/modules/cjs/loader:1282:32)\n    at Module._load (node:internal/modules/cjs/loader:1098:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)\n    at Module.require (node:internal/modules/cjs/loader:1304:12)\n    at require (node:internal/modules/helpers:123:16)\n    at TradingOrchestrator.createComponentInstances (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:218:31)","timestamp":"2025-07-19T12:51:57.538Z"}
{"level":"error","message":"Failed to initialize Trading System: Unexpected token '('","service":"trading-system","stack":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\config\\config-manager.js:45\n        this.initializationPromise = this.(options);\n                                          ^\n\nSyntaxError: Unexpected token '('\n    at wrapSafe (node:internal/modules/cjs/loader:1383:18)\n    at Module._compile (node:internal/modules/cjs/loader:1412:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)\n    at Module.load (node:internal/modules/cjs/loader:1282:32)\n    at Module._load (node:internal/modules/cjs/loader:1098:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)\n    at Module.require (node:internal/modules/cjs/loader:1304:12)\n    at require (node:internal/modules/helpers:123:16)\n    at TradingOrchestrator.createComponentInstances (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:218:31)","timestamp":"2025-07-19T12:51:57.539Z"}
{"0":"U","1":"n","10":" ","11":"t","12":"o","13":"k","14":"e","15":"n","16":" ","17":"'","18":"(","19":"'","2":"e","3":"x","4":"p","5":"e","6":"c","7":"t","8":"e","9":"d","level":"error","message":"❌ System Initialization - FAILED (13ms)","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"components":{"circuitBreaker":"not_initialized","configManager":"ready","dataCollector":"not_initialized","errorHandler":"not_initialized","eventCoordinator":"not_initialized","gridBotManager":"not_initialized","llmCoordinator":"not_initialized","memeCoinScanner":"not_initialized","performanceTracker":"not_initialized","portfolioManager":"not_initialized","riskManager":"not_initialized","sentimentAnalyzer":"not_initialized","tradingExecutor":"not_initialized","whaleTracker":"not_initialized"},"isInitialized":false,"isRunning":false,"level":"info","message":"Bot Status:","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z","workflowState":{"activeSignals":{},"currentCycle":0,"healthStatus":"unknown","lastAnalysis":null,"lastDataCollection":null,"lastTradeExecution":null,"marketData":null,"pendingTrades":[],"performance":{"successfulTrades":0,"totalProfit":0,"totalTrades":0}}}
{"level":"info","message":"✅ Get Bot Status - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"level":"info","message":"Settings loaded successfully","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"level":"info","message":"✅ Get Settings - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"level":"warn","message":"DataCollector component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"level":"info","message":"Found 0 coins","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"level":"info","message":"✅ Get Coins - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"level":"warn","message":"PerformanceTracker component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"level":"info","message":"Trading stats retrieved","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"level":"info","message":"✅ Get Trading Stats - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"level":"warn","message":"PerformanceTracker component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"level":"info","message":"Performance metrics retrieved","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"level":"info","message":"✅ Get Performance Metrics - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"level":"warn","message":"PortfolioManager component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"level":"info","message":"Wallet balance retrieved","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"level":"info","message":"✅ Get Wallet Balance - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:57.539Z"}
{"components":{"circuitBreaker":"not_initialized","configManager":"ready","dataCollector":"not_initialized","errorHandler":"not_initialized","eventCoordinator":"not_initialized","gridBotManager":"not_initialized","llmCoordinator":"not_initialized","memeCoinScanner":"not_initialized","performanceTracker":"not_initialized","portfolioManager":"not_initialized","riskManager":"not_initialized","sentimentAnalyzer":"not_initialized","tradingExecutor":"not_initialized","whaleTracker":"not_initialized"},"isInitialized":false,"isRunning":false,"level":"info","message":"Health check completed:","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z","workflowState":{"activeSignals":{},"currentCycle":0,"healthStatus":"unknown","lastAnalysis":null,"lastDataCollection":null,"lastTradeExecution":null,"marketData":null,"pendingTrades":[],"performance":{"successfulTrades":0,"totalProfit":0,"totalTrades":0}}}
{"level":"info","message":"✅ Health Check - PASSED (1ms)","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"warn","message":"GridBotManager component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"info","message":"Grid bot positions: 0","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"info","message":"✅ Check Grid Bot Manager - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"warn","message":"WhaleTracker component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"info","message":"Tracked whales: 0","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"info","message":"✅ Check Whale Tracker - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"warn","message":"MemeCoinScanner component not available on orchestrator.","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"info","message":"Meme scanner status:","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"info","message":"✅ Check Meme Scanner Status - PASSED (0ms)","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"info","message":"\n=== Test Results Summary ===","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"info","message":"Total Tests: 11","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"info","message":"Passed: 10 ✅","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"info","message":"Failed: 1 ❌","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"info","message":"\nFailed Tests:","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"error","message":"- System Initialization: Unexpected token '('","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"info","message":"\n=== End of Test Results ===","service":"trading-system","timestamp":"2025-07-19T12:51:57.540Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:32:26.138Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:32:26.141Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:32:26.142Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:32:26.143Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:32:26.144Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:32:26.145Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:32:26.160Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:32:26.161Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:32:26.165Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:33:37.515Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:33:37.518Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:33:37.519Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:33:37.520Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:33:37.521Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:33:37.522Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:33:37.531Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:33:37.531Z"}
{"level":"warn","message":"Module not found: ./shared/config/config-manager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:33:37.534Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-19T14:33:37.871Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-19T14:33:37.880Z"}
{"level":"info","message":"📦 Creating component instances...","service":"trading-system","timestamp":"2025-07-19T14:33:37.880Z"}
{"level":"info","message":"🔧 Initializing configuration loading system for development...","service":"trading-system","timestamp":"2025-07-19T14:33:37.881Z"}
{"level":"info","message":"✅ Configuration loading completed in 6ms","service":"trading-system","timestamp":"2025-07-19T14:33:37.887Z"}
{"level":"info","message":"📋 Loaded 17 configuration files","service":"trading-system","timestamp":"2025-07-19T14:33:37.887Z"}
{"level":"info","message":"📋 Configuration loaded: 17 files, 39 config sections","service":"trading-system","timestamp":"2025-07-19T14:33:37.887Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: Unexpected token ','","service":"trading-system","stack":"C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\config\\configuration-loader.js:116\n            configDir: options.configDir || path.join(, '../../config'),\n                                                      ^\n\nSyntaxError: Unexpected token ','\n    at wrapSafe (node:internal/modules/cjs/loader:1383:18)\n    at Module._compile (node:internal/modules/cjs/loader:1412:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1551:10)\n    at Module.load (node:internal/modules/cjs/loader:1282:32)\n    at Module._load (node:internal/modules/cjs/loader:1098:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:215:24)\n    at Module.require (node:internal/modules/cjs/loader:1304:12)\n    at require (node:internal/modules/helpers:123:16)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\config\\config-manager.js:6:31)","timestamp":"2025-07-19T14:33:37.888Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:34:37.989Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:34:37.993Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:34:37.994Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:34:37.994Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:34:37.996Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:34:37.996Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:34:38.006Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T14:34:38.007Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-19T14:34:38.330Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-19T14:34:38.334Z"}
{"level":"info","message":"📦 Creating component instances...","service":"trading-system","timestamp":"2025-07-19T14:34:38.335Z"}
{"level":"info","message":"🔧 Initializing configuration loading system for development...","service":"trading-system","timestamp":"2025-07-19T14:34:38.336Z"}
{"level":"info","message":"✅ Configuration loading completed in 5ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.341Z"}
{"level":"info","message":"📋 Loaded 17 configuration files","service":"trading-system","timestamp":"2025-07-19T14:34:38.341Z"}
{"level":"info","message":"📋 Configuration loaded: 17 files, 39 config sections","service":"trading-system","timestamp":"2025-07-19T14:34:38.341Z"}
{"level":"info","message":"🔧 Initializing Configuration Manager...","service":"trading-system","timestamp":"2025-07-19T14:34:38.341Z"}
{"level":"info","message":"🔧 Loading configuration for environment: development","service":"trading-system","timestamp":"2025-07-19T14:34:38.342Z"}
{"level":"info","message":"Applied runtime overrides from environment variables","service":"trading-system","timestamp":"2025-07-19T14:34:38.345Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T14:34:38.346Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T14:34:38.347Z"}
{"level":"info","message":"👁️ Setting up configuration file watching...","service":"trading-system","timestamp":"2025-07-19T14:34:38.347Z"}
{"level":"info","message":"✅ Configuration loaded successfully in 10ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.352Z"}
{"level":"info","message":"✅ Configuration Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T14:34:38.352Z"}
{"level":"info","message":"🔧 Initializing components sequentially...","service":"trading-system","timestamp":"2025-07-19T14:34:38.353Z"}
{"level":"info","message":"🔧 Initializing component: database","service":"trading-system","timestamp":"2025-07-19T14:34:38.353Z"}
{"level":"info","message":"✅ Component database initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.353Z"}
{"level":"info","message":"🔧 Initializing component: configManager","service":"trading-system","timestamp":"2025-07-19T14:34:38.353Z"}
{"level":"info","message":"✅ Component configManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.353Z"}
{"level":"info","message":"🔧 Initializing component: logger","service":"trading-system","timestamp":"2025-07-19T14:34:38.353Z"}
{"level":"info","message":"✅ Component logger initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.353Z"}
{"level":"info","message":"🔧 Initializing component: circuitBreaker","service":"trading-system","timestamp":"2025-07-19T14:34:38.354Z"}
{"level":"info","message":"✅ Component circuitBreaker initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.354Z"}
{"level":"info","message":"🔧 Initializing component: riskManager","service":"trading-system","timestamp":"2025-07-19T14:34:38.354Z"}
{"level":"info","message":"✅ Component riskManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.354Z"}
{"level":"info","message":"🔧 Initializing component: errorHandler","service":"trading-system","timestamp":"2025-07-19T14:34:38.354Z"}
{"level":"info","message":"✅ Component errorHandler initialized in 2ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.356Z"}
{"level":"info","message":"🔧 Initializing component: exchangeManager","service":"trading-system","timestamp":"2025-07-19T14:34:38.356Z"}
{"level":"info","message":"✅ Component exchangeManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.357Z"}
{"level":"info","message":"🔧 Initializing component: dataCollector","service":"trading-system","timestamp":"2025-07-19T14:34:38.357Z"}
{"level":"info","message":"✅ Component dataCollector initialized in 3ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.360Z"}
{"level":"info","message":"🔧 Initializing component: eventCoordinator","service":"trading-system","timestamp":"2025-07-19T14:34:38.360Z"}
{"level":"info","message":"✅ Component eventCoordinator initialized in 1ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.361Z"}
{"level":"info","message":"🔧 Initializing component: performanceTracker","service":"trading-system","timestamp":"2025-07-19T14:34:38.361Z"}
{"level":"info","message":"✅ Component performanceTracker initialized in 1ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.362Z"}
{"level":"info","message":"🔧 Initializing component: sentimentAnalyzer","service":"trading-system","timestamp":"2025-07-19T14:34:38.362Z"}
{"level":"info","message":"✅ Component sentimentAnalyzer initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.362Z"}
{"level":"info","message":"🔧 Initializing component: portfolioManager","service":"trading-system","timestamp":"2025-07-19T14:34:38.362Z"}
{"level":"info","message":"✅ Component portfolioManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.362Z"}
{"level":"info","message":"🔧 Initializing component: tradingExecutor","service":"trading-system","timestamp":"2025-07-19T14:34:38.362Z"}
{"level":"info","message":"✅ Component tradingExecutor initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.363Z"}
{"level":"info","message":"🔧 Initializing component: gridBotManager","service":"trading-system","timestamp":"2025-07-19T14:34:38.363Z"}
{"level":"info","message":"Grid Bot Manager initialized","service":"trading-system","timestamp":"2025-07-19T14:34:38.363Z"}
{"level":"info","message":"✅ Component gridBotManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.363Z"}
{"level":"info","message":"🔧 Initializing component: whaleTracker","service":"trading-system","timestamp":"2025-07-19T14:34:38.363Z"}
{"level":"info","message":"✅ Component whaleTracker initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.363Z"}
{"level":"info","message":"🔧 Initializing component: llmCoordinator","service":"trading-system","timestamp":"2025-07-19T14:34:38.363Z"}
{"level":"info","message":"✅ Component llmCoordinator initialized in 1ms","service":"trading-system","timestamp":"2025-07-19T14:34:38.365Z"}
{"level":"info","message":"✅ Component initialization completed","service":"trading-system","timestamp":"2025-07-19T14:34:38.365Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: Cannot read properties of null (reading 'on')","service":"trading-system","stack":"TypeError: Cannot read properties of null (reading 'on')\n    at TradingOrchestrator.setupEventListeners (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:561:41)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:178:18)\n    at async testIntegration ([eval]:14:9)","timestamp":"2025-07-19T14:34:38.474Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T15:07:14.737Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T15:07:14.741Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T15:07:14.742Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T15:07:14.743Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T15:07:14.744Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T15:07:14.745Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T15:07:14.757Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T15:07:14.759Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-19T15:07:15.282Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-19T15:07:15.290Z"}
{"level":"info","message":"📦 Creating component instances...","service":"trading-system","timestamp":"2025-07-19T15:07:15.291Z"}
{"level":"info","message":"🔧 Initializing configuration loading system for development...","service":"trading-system","timestamp":"2025-07-19T15:07:15.292Z"}
{"level":"info","message":"✅ Configuration loading completed in 6ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.298Z"}
{"level":"info","message":"📋 Loaded 17 configuration files","service":"trading-system","timestamp":"2025-07-19T15:07:15.298Z"}
{"level":"info","message":"📋 Configuration loaded: 17 files, 39 config sections","service":"trading-system","timestamp":"2025-07-19T15:07:15.298Z"}
{"level":"info","message":"🔧 Initializing Configuration Manager...","service":"trading-system","timestamp":"2025-07-19T15:07:15.299Z"}
{"level":"info","message":"🔧 Loading configuration for environment: development","service":"trading-system","timestamp":"2025-07-19T15:07:15.299Z"}
{"level":"info","message":"Applied runtime overrides from environment variables","service":"trading-system","timestamp":"2025-07-19T15:07:15.303Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T15:07:15.303Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T15:07:15.303Z"}
{"level":"info","message":"👁️ Setting up configuration file watching...","service":"trading-system","timestamp":"2025-07-19T15:07:15.304Z"}
{"level":"info","message":"✅ Configuration loaded successfully in 10ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.309Z"}
{"level":"info","message":"✅ Configuration Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T15:07:15.309Z"}
{"level":"info","message":"🔧 Initializing components sequentially...","service":"trading-system","timestamp":"2025-07-19T15:07:15.310Z"}
{"level":"info","message":"🔧 Initializing component: database","service":"trading-system","timestamp":"2025-07-19T15:07:15.310Z"}
{"level":"info","message":"✅ Component database initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.310Z"}
{"level":"info","message":"🔧 Initializing component: configManager","service":"trading-system","timestamp":"2025-07-19T15:07:15.310Z"}
{"level":"info","message":"✅ Component configManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.310Z"}
{"level":"info","message":"🔧 Initializing component: logger","service":"trading-system","timestamp":"2025-07-19T15:07:15.310Z"}
{"level":"info","message":"✅ Component logger initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.311Z"}
{"level":"info","message":"🔧 Initializing component: circuitBreaker","service":"trading-system","timestamp":"2025-07-19T15:07:15.311Z"}
{"level":"info","message":"✅ Component circuitBreaker initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.311Z"}
{"level":"info","message":"🔧 Initializing component: riskManager","service":"trading-system","timestamp":"2025-07-19T15:07:15.311Z"}
{"level":"info","message":"✅ Component riskManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.311Z"}
{"level":"info","message":"🔧 Initializing component: errorHandler","service":"trading-system","timestamp":"2025-07-19T15:07:15.311Z"}
{"level":"info","message":"✅ Component errorHandler initialized in 3ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.314Z"}
{"level":"info","message":"🔧 Initializing component: exchangeManager","service":"trading-system","timestamp":"2025-07-19T15:07:15.314Z"}
{"level":"info","message":"✅ Component exchangeManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.314Z"}
{"level":"info","message":"🔧 Initializing component: dataCollector","service":"trading-system","timestamp":"2025-07-19T15:07:15.314Z"}
{"level":"info","message":"✅ Component dataCollector initialized in 4ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.318Z"}
{"level":"info","message":"🔧 Initializing component: eventCoordinator","service":"trading-system","timestamp":"2025-07-19T15:07:15.318Z"}
{"level":"info","message":"✅ Component eventCoordinator initialized in 1ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.319Z"}
{"level":"info","message":"🔧 Initializing component: performanceTracker","service":"trading-system","timestamp":"2025-07-19T15:07:15.319Z"}
{"level":"info","message":"✅ Component performanceTracker initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.319Z"}
{"level":"info","message":"🔧 Initializing component: sentimentAnalyzer","service":"trading-system","timestamp":"2025-07-19T15:07:15.320Z"}
{"level":"info","message":"✅ Component sentimentAnalyzer initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.320Z"}
{"level":"info","message":"🔧 Initializing component: portfolioManager","service":"trading-system","timestamp":"2025-07-19T15:07:15.320Z"}
{"level":"info","message":"✅ Component portfolioManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.320Z"}
{"level":"info","message":"🔧 Initializing component: tradingExecutor","service":"trading-system","timestamp":"2025-07-19T15:07:15.320Z"}
{"level":"info","message":"✅ Component tradingExecutor initialized in 1ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.321Z"}
{"level":"info","message":"🔧 Initializing component: gridBotManager","service":"trading-system","timestamp":"2025-07-19T15:07:15.321Z"}
{"level":"info","message":"Grid Bot Manager initialized","service":"trading-system","timestamp":"2025-07-19T15:07:15.321Z"}
{"level":"info","message":"✅ Component gridBotManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.321Z"}
{"level":"info","message":"🔧 Initializing component: whaleTracker","service":"trading-system","timestamp":"2025-07-19T15:07:15.321Z"}
{"level":"info","message":"✅ Component whaleTracker initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.321Z"}
{"level":"info","message":"🔧 Initializing component: llmCoordinator","service":"trading-system","timestamp":"2025-07-19T15:07:15.322Z"}
{"level":"info","message":"✅ Component llmCoordinator initialized in 1ms","service":"trading-system","timestamp":"2025-07-19T15:07:15.323Z"}
{"level":"info","message":"✅ Component initialization completed","service":"trading-system","timestamp":"2025-07-19T15:07:15.323Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: Cannot read properties of null (reading 'on')","service":"trading-system","stack":"TypeError: Cannot read properties of null (reading 'on')\n    at TradingOrchestrator.setupEventListeners (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:561:41)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:178:18)\n    at async testIntegration ([eval]:13:9)","timestamp":"2025-07-19T15:07:15.396Z"}
{"level":"warn","message":"Module not found: ./engines/trading/Elite.WhaleTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T15:07:29.930Z"}
{"level":"warn","message":"Module not found: ./engines/trading/MemeCoinScanner. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T15:07:29.934Z"}
{"level":"warn","message":"Module not found: ./engines/trading/ProductionTradingExecutor. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T15:07:29.935Z"}
{"level":"warn","message":"Module not found: ./engines/trading/PortfolioManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T15:07:29.936Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/SentimentAnalyzer. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T15:07:29.937Z"}
{"level":"warn","message":"Module not found: ./engines/analysis/PerformanceTracker. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T15:07:29.938Z"}
{"level":"warn","message":"Module not found: ./engines/shared/risk/UnifiedRiskManager. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T15:07:29.954Z"}
{"level":"warn","message":"Module not found: ./engines/shared/safety/circuit-breakers. Using mock fallback.","service":"trading-system","timestamp":"2025-07-19T15:07:29.955Z"}
{"level":"info","message":"🚀 Initializing Trading Orchestrator...","service":"trading-system","timestamp":"2025-07-19T15:07:30.374Z"}
{"level":"info","message":"✅ Database connected successfully.","service":"trading-system","timestamp":"2025-07-19T15:07:30.378Z"}
{"level":"info","message":"📦 Creating component instances...","service":"trading-system","timestamp":"2025-07-19T15:07:30.379Z"}
{"level":"info","message":"🔧 Initializing configuration loading system for development...","service":"trading-system","timestamp":"2025-07-19T15:07:30.380Z"}
{"level":"info","message":"✅ Configuration loading completed in 6ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.386Z"}
{"level":"info","message":"📋 Loaded 17 configuration files","service":"trading-system","timestamp":"2025-07-19T15:07:30.386Z"}
{"level":"info","message":"📋 Configuration loaded: 17 files, 39 config sections","service":"trading-system","timestamp":"2025-07-19T15:07:30.386Z"}
{"level":"info","message":"🔧 Initializing Configuration Manager...","service":"trading-system","timestamp":"2025-07-19T15:07:30.386Z"}
{"level":"info","message":"🔧 Loading configuration for environment: development","service":"trading-system","timestamp":"2025-07-19T15:07:30.387Z"}
{"level":"info","message":"Applied runtime overrides from environment variables","service":"trading-system","timestamp":"2025-07-19T15:07:30.390Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T15:07:30.391Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T15:07:30.391Z"}
{"level":"info","message":"👁️ Setting up configuration file watching...","service":"trading-system","timestamp":"2025-07-19T15:07:30.391Z"}
{"level":"info","message":"✅ Configuration loaded successfully in 9ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.396Z"}
{"level":"info","message":"✅ Configuration Manager initialized successfully","service":"trading-system","timestamp":"2025-07-19T15:07:30.396Z"}
{"level":"info","message":"🔧 Initializing components sequentially...","service":"trading-system","timestamp":"2025-07-19T15:07:30.397Z"}
{"level":"info","message":"🔧 Initializing component: database","service":"trading-system","timestamp":"2025-07-19T15:07:30.397Z"}
{"level":"info","message":"✅ Component database initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.397Z"}
{"level":"info","message":"🔧 Initializing component: configManager","service":"trading-system","timestamp":"2025-07-19T15:07:30.397Z"}
{"level":"info","message":"✅ Component configManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.397Z"}
{"level":"info","message":"🔧 Initializing component: logger","service":"trading-system","timestamp":"2025-07-19T15:07:30.397Z"}
{"level":"info","message":"✅ Component logger initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.398Z"}
{"level":"info","message":"🔧 Initializing component: circuitBreaker","service":"trading-system","timestamp":"2025-07-19T15:07:30.398Z"}
{"level":"info","message":"✅ Component circuitBreaker initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.398Z"}
{"level":"info","message":"🔧 Initializing component: riskManager","service":"trading-system","timestamp":"2025-07-19T15:07:30.398Z"}
{"level":"info","message":"✅ Component riskManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.398Z"}
{"level":"info","message":"🔧 Initializing component: errorHandler","service":"trading-system","timestamp":"2025-07-19T15:07:30.398Z"}
{"level":"info","message":"✅ Component errorHandler initialized in 3ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.401Z"}
{"level":"info","message":"🔧 Initializing component: exchangeManager","service":"trading-system","timestamp":"2025-07-19T15:07:30.401Z"}
{"level":"info","message":"✅ Component exchangeManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.402Z"}
{"level":"info","message":"🔧 Initializing component: dataCollector","service":"trading-system","timestamp":"2025-07-19T15:07:30.402Z"}
{"level":"info","message":"✅ Component dataCollector initialized in 3ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.405Z"}
{"level":"info","message":"🔧 Initializing component: eventCoordinator","service":"trading-system","timestamp":"2025-07-19T15:07:30.405Z"}
{"level":"info","message":"✅ Component eventCoordinator initialized in 1ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.406Z"}
{"level":"info","message":"🔧 Initializing component: performanceTracker","service":"trading-system","timestamp":"2025-07-19T15:07:30.406Z"}
{"level":"info","message":"✅ Component performanceTracker initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.406Z"}
{"level":"info","message":"🔧 Initializing component: sentimentAnalyzer","service":"trading-system","timestamp":"2025-07-19T15:07:30.406Z"}
{"level":"info","message":"✅ Component sentimentAnalyzer initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.406Z"}
{"level":"info","message":"🔧 Initializing component: portfolioManager","service":"trading-system","timestamp":"2025-07-19T15:07:30.406Z"}
{"level":"info","message":"✅ Component portfolioManager initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.407Z"}
{"level":"info","message":"🔧 Initializing component: tradingExecutor","service":"trading-system","timestamp":"2025-07-19T15:07:30.407Z"}
{"level":"info","message":"✅ Component tradingExecutor initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.407Z"}
{"level":"info","message":"🔧 Initializing component: gridBotManager","service":"trading-system","timestamp":"2025-07-19T15:07:30.407Z"}
{"level":"info","message":"Grid Bot Manager initialized","service":"trading-system","timestamp":"2025-07-19T15:07:30.407Z"}
{"level":"info","message":"✅ Component gridBotManager initialized in 1ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.408Z"}
{"level":"info","message":"🔧 Initializing component: whaleTracker","service":"trading-system","timestamp":"2025-07-19T15:07:30.408Z"}
{"level":"info","message":"✅ Component whaleTracker initialized in 0ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.408Z"}
{"level":"info","message":"🔧 Initializing component: llmCoordinator","service":"trading-system","timestamp":"2025-07-19T15:07:30.408Z"}
{"level":"info","message":"✅ Component llmCoordinator initialized in 1ms","service":"trading-system","timestamp":"2025-07-19T15:07:30.409Z"}
{"level":"info","message":"✅ Component initialization completed","service":"trading-system","timestamp":"2025-07-19T15:07:30.409Z"}
{"level":"error","message":"Failed to initialize Trading Orchestrator: Cannot read properties of null (reading 'on')","service":"trading-system","stack":"TypeError: Cannot read properties of null (reading 'on')\n    at TradingOrchestrator.setupEventListeners (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:561:41)\n    at TradingOrchestrator.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:178:18)\n    at async testIntegration ([eval]:13:9)","timestamp":"2025-07-19T15:07:30.472Z"}
{"level":"info","message":"📝 Configuration file changed: development.json","service":"trading-system","timestamp":"2025-07-19T15:37:20.661Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T15:37:20.662Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T15:37:20.662Z"}
{"level":"info","message":"📝 Configuration changed: main","service":"trading-system","timestamp":"2025-07-19T15:37:20.662Z"}
{"level":"info","message":"📝 Configuration file changed: exchanges\\binance.json","service":"trading-system","timestamp":"2025-07-19T15:37:20.687Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T15:37:20.688Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T15:37:20.688Z"}
{"level":"info","message":"📝 Configuration changed: exchanges","service":"trading-system","timestamp":"2025-07-19T15:37:20.688Z"}
{"level":"info","message":"📝 Configuration file changed: exchanges\\bybit.json","service":"trading-system","timestamp":"2025-07-19T15:37:20.694Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T15:37:20.695Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T15:37:20.695Z"}
{"level":"info","message":"📝 Configuration changed: exchanges","service":"trading-system","timestamp":"2025-07-19T15:37:20.696Z"}
{"level":"info","message":"📝 Configuration file changed: exchanges\\coinbase.json","service":"trading-system","timestamp":"2025-07-19T15:37:20.703Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T15:37:20.704Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T15:37:20.704Z"}
{"level":"info","message":"📝 Configuration changed: exchanges","service":"trading-system","timestamp":"2025-07-19T15:37:20.705Z"}
{"level":"info","message":"📝 Configuration file changed: exchanges\\kraken.json","service":"trading-system","timestamp":"2025-07-19T15:37:20.712Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T15:37:20.712Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T15:37:20.712Z"}
{"level":"info","message":"📝 Configuration changed: exchanges","service":"trading-system","timestamp":"2025-07-19T15:37:20.712Z"}
{"level":"info","message":"📝 Configuration file changed: exchanges\\okx.json","service":"trading-system","timestamp":"2025-07-19T15:37:20.720Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T15:37:20.720Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T15:37:20.721Z"}
{"level":"info","message":"📝 Configuration changed: exchanges","service":"trading-system","timestamp":"2025-07-19T15:37:20.721Z"}
{"level":"info","message":"📝 Configuration file changed: monitoring.json","service":"trading-system","timestamp":"2025-07-19T15:37:20.733Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T15:37:20.733Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T15:37:20.734Z"}
{"level":"info","message":"📝 Configuration changed: monitoring","service":"trading-system","timestamp":"2025-07-19T15:37:20.734Z"}
{"level":"info","message":"📝 Configuration file changed: risk-management.json","service":"trading-system","timestamp":"2025-07-19T15:37:20.756Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T15:37:20.756Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T15:37:20.756Z"}
{"level":"info","message":"📝 Configuration changed: risk","service":"trading-system","timestamp":"2025-07-19T15:37:20.757Z"}
{"level":"info","message":"📝 Configuration file changed: security.json","service":"trading-system","timestamp":"2025-07-19T15:37:20.770Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T15:37:20.770Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T15:37:20.771Z"}
{"level":"info","message":"📝 Configuration changed: security","service":"trading-system","timestamp":"2025-07-19T15:37:20.771Z"}
{"level":"info","message":"📝 Configuration file changed: strategies\\gridBot.json","service":"trading-system","timestamp":"2025-07-19T15:37:20.794Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T15:37:20.795Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T15:37:20.795Z"}
{"level":"info","message":"📝 Configuration changed: strategies","service":"trading-system","timestamp":"2025-07-19T15:37:20.795Z"}
{"level":"info","message":"📝 Configuration file changed: strategies\\memeCoin.json","service":"trading-system","timestamp":"2025-07-19T15:37:20.803Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T15:37:20.804Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T15:37:20.804Z"}
{"level":"info","message":"📝 Configuration changed: strategies","service":"trading-system","timestamp":"2025-07-19T15:37:20.805Z"}
{"level":"info","message":"📝 Configuration file changed: strategies\\whaleFollowing.json","service":"trading-system","timestamp":"2025-07-19T15:37:20.812Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T15:37:20.812Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T15:37:20.812Z"}
{"level":"info","message":"📝 Configuration changed: strategies","service":"trading-system","timestamp":"2025-07-19T15:37:20.813Z"}
{"level":"info","message":"📝 Configuration file changed: trading-config.json","service":"trading-system","timestamp":"2025-07-19T15:37:20.839Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T15:37:20.839Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T15:37:20.840Z"}
{"level":"info","message":"📝 Configuration changed: trading","service":"trading-system","timestamp":"2025-07-19T15:37:20.840Z"}
{"level":"info","message":"📝 Configuration file changed: development.json","service":"trading-system","timestamp":"2025-07-19T16:51:19.510Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T16:51:19.511Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T16:51:19.511Z"}
{"level":"info","message":"📝 Configuration changed: main","service":"trading-system","timestamp":"2025-07-19T16:51:19.511Z"}
{"level":"info","message":"📝 Configuration file changed: exchanges\\binance.json","service":"trading-system","timestamp":"2025-07-19T16:51:19.538Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T16:51:19.539Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T16:51:19.539Z"}
{"level":"info","message":"📝 Configuration changed: exchanges","service":"trading-system","timestamp":"2025-07-19T16:51:19.539Z"}
{"level":"info","message":"📝 Configuration file changed: exchanges\\bybit.json","service":"trading-system","timestamp":"2025-07-19T16:51:19.547Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T16:51:19.548Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T16:51:19.548Z"}
{"level":"info","message":"📝 Configuration changed: exchanges","service":"trading-system","timestamp":"2025-07-19T16:51:19.548Z"}
{"level":"info","message":"📝 Configuration file changed: exchanges\\coinbase.json","service":"trading-system","timestamp":"2025-07-19T16:51:19.555Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T16:51:19.556Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T16:51:19.556Z"}
{"level":"info","message":"📝 Configuration changed: exchanges","service":"trading-system","timestamp":"2025-07-19T16:51:19.556Z"}
{"level":"info","message":"📝 Configuration file changed: exchanges\\kraken.json","service":"trading-system","timestamp":"2025-07-19T16:51:19.564Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T16:51:19.565Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T16:51:19.565Z"}
{"level":"info","message":"📝 Configuration changed: exchanges","service":"trading-system","timestamp":"2025-07-19T16:51:19.565Z"}
{"level":"info","message":"📝 Configuration file changed: exchanges\\okx.json","service":"trading-system","timestamp":"2025-07-19T16:51:19.574Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T16:51:19.575Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T16:51:19.575Z"}
{"level":"info","message":"📝 Configuration changed: exchanges","service":"trading-system","timestamp":"2025-07-19T16:51:19.575Z"}
{"level":"info","message":"📝 Configuration file changed: monitoring.json","service":"trading-system","timestamp":"2025-07-19T16:51:19.596Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T16:51:19.596Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T16:51:19.597Z"}
{"level":"info","message":"📝 Configuration changed: monitoring","service":"trading-system","timestamp":"2025-07-19T16:51:19.597Z"}
{"level":"info","message":"📝 Configuration file changed: risk-management.json","service":"trading-system","timestamp":"2025-07-19T16:51:19.637Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T16:51:19.637Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T16:51:19.638Z"}
{"level":"info","message":"📝 Configuration changed: risk","service":"trading-system","timestamp":"2025-07-19T16:51:19.638Z"}
{"level":"info","message":"📝 Configuration file changed: security.json","service":"trading-system","timestamp":"2025-07-19T16:51:19.652Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T16:51:19.653Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T16:51:19.653Z"}
{"level":"info","message":"📝 Configuration changed: security","service":"trading-system","timestamp":"2025-07-19T16:51:19.654Z"}
{"level":"info","message":"📝 Configuration file changed: strategies\\gridBot.json","service":"trading-system","timestamp":"2025-07-19T16:51:19.682Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T16:51:19.683Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T16:51:19.683Z"}
{"level":"info","message":"📝 Configuration changed: strategies","service":"trading-system","timestamp":"2025-07-19T16:51:19.683Z"}
{"level":"info","message":"📝 Configuration file changed: strategies\\memeCoin.json","service":"trading-system","timestamp":"2025-07-19T16:51:19.690Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T16:51:19.690Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T16:51:19.690Z"}
{"level":"info","message":"📝 Configuration changed: strategies","service":"trading-system","timestamp":"2025-07-19T16:51:19.691Z"}
{"level":"info","message":"📝 Configuration file changed: strategies\\whaleFollowing.json","service":"trading-system","timestamp":"2025-07-19T16:51:19.698Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T16:51:19.698Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T16:51:19.699Z"}
{"level":"info","message":"📝 Configuration changed: strategies","service":"trading-system","timestamp":"2025-07-19T16:51:19.699Z"}
{"level":"info","message":"📝 Configuration file changed: trading-config.json","service":"trading-system","timestamp":"2025-07-19T16:51:19.736Z"}
{"level":"info","message":"🔍 Validating configuration...","service":"trading-system","timestamp":"2025-07-19T16:51:19.737Z"}
{"level":"info","message":"✅ Configuration validation passed","service":"trading-system","timestamp":"2025-07-19T16:51:19.737Z"}
{"level":"info","message":"📝 Configuration changed: trading","service":"trading-system","timestamp":"2025-07-19T16:51:19.737Z"}
{"level":"error","message":"Error in test operation: test error","service":"trading-system","stack":"Error: test error\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:22:31\n    at Function.operation (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\utils\\ErrorHandlingUtils.js:14:26)\n    at Object.safeAsync (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:20:57)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-20T18:48:16.104Z"}
{"level":"error","message":"Error in test operation: test error","service":"trading-system","stack":"Error: test error\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:46:31\n    at Function.operation [as safeAsyncWithResult] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\utils\\ErrorHandlingUtils.js:23:32)\n    at Object.safeAsyncWithResult (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:44:57)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-20T18:48:16.116Z"}
{"level":"error","message":"Circuit breaker opened for test after 2 failures","service":"trading-system","timestamp":"2025-07-20T18:48:16.128Z"}
{"level":"error","message":"Error in Order execution: Order parameters missing required fields: quantity","service":"trading-system","stack":"Error: Order parameters missing required fields: quantity\n    at Function.validateRequired (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\utils\\ErrorHandlingUtils.js:92:19)\n    at validateRequired (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:205:48)\n    at Function.operation [as safeAsyncWithResult] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\utils\\ErrorHandlingUtils.js:23:32)\n    at Object.safeAsyncWithResult [as executeOrder] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:203:47)\n    at Object.executeOrder (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:238:58)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-20T18:48:16.248Z"}
{"level":"error","message":"Error in Order execution: Invalid symbol","service":"trading-system","stack":"Error: Invalid symbol\n    at C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:212:39\n    at Function.operation [as safeAsyncWithResult] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\utils\\ErrorHandlingUtils.js:23:32)\n    at Object.safeAsyncWithResult [as executeOrder] (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:203:47)\n    at Object.executeOrder (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:249:58)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-20T18:48:16.249Z"}
{"level":"warn","message":"Retry attempt 1/3 for 10 after 1000ms","service":"trading-system","timestamp":"2025-07-20T18:48:16.252Z"}
{"level":"warn","message":"Retry attempt 2/3 for 10 after 2000ms","service":"trading-system","timestamp":"2025-07-20T18:48:17.267Z"}
{"level":"error","message":"Circuit breaker opened for service after 2 failures","service":"trading-system","timestamp":"2025-07-20T18:48:19.273Z"}
{"level":"info","message":"Initiating graceful shutdown for Test","service":"trading-system","timestamp":"2025-07-20T18:48:19.274Z"}
{"level":"error","message":"Error during cleanup: Cleanup failed","service":"trading-system","stack":"Error: Cleanup failed\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:328:58)\n    at Promise.then.completed (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\Documents\\electronTrader\\node_modules\\jest-runner\\build\\testWorker.js:106:12)","timestamp":"2025-07-20T18:48:19.275Z"}
{"level":"info","message":"Graceful shutdown completed for Test","service":"trading-system","timestamp":"2025-07-20T18:48:19.275Z"}
{"level":"info","message":"[EnhancedComponentInitializer] Component registered: critical-component (critical: true, optional: false)","service":"trading-system","timestamp":"2025-07-20T20:09:23.398Z"}
{"level":"info","message":"[EnhancedComponentInitializer] Initializing critical-component (attempt 1/4)","service":"trading-system","timestamp":"2025-07-20T20:09:23.398Z"}
{"level":"error","message":"[EnhancedComponentInitializer] ❌ Component critical-component initialization failed (attempt 1): Initialization failed","service":"trading-system","stack":"Error: Initialization failed\n    at Object.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\verify-enhanced-error-handling.js:94:27)\n    at EnhancedComponentInitializer.performComponentInitialization (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\orchestration\\enhanced-component-initializer.js:439:34)\n    at EnhancedComponentInitializer.initializeComponentWithRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\orchestration\\enhanced-component-initializer.js:349:28)\n    at verifyEnhancedErrorHandling (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\verify-enhanced-error-handling.js:107:51)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-20T20:09:23.399Z"}
{"level":"info","message":"[EnhancedComponentInitializer] Attempting recovery for critical-component using strategy: restart-component","service":"trading-system","timestamp":"2025-07-20T20:09:23.399Z"}
{"level":"info","message":"[EnhancedComponentInitializer] Restarting component: critical-component","service":"trading-system","timestamp":"2025-07-20T20:09:23.399Z"}
{"level":"info","message":"[EnhancedComponentInitializer] 🔄 Recovery successful for critical-component, retrying initialization","service":"trading-system","timestamp":"2025-07-20T20:09:23.400Z"}
{"level":"info","message":"[EnhancedComponentInitializer] Initializing critical-component (attempt 2/4)","service":"trading-system","timestamp":"2025-07-20T20:09:25.405Z"}
{"level":"error","message":"[EnhancedComponentInitializer] ❌ Component critical-component initialization failed (attempt 2): Initialization failed","service":"trading-system","stack":"Error: Initialization failed\n    at Object.initialize (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\verify-enhanced-error-handling.js:94:27)\n    at EnhancedComponentInitializer.performComponentInitialization (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\orchestration\\enhanced-component-initializer.js:439:34)\n    at EnhancedComponentInitializer.initializeComponentWithRecovery (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\shared\\orchestration\\enhanced-component-initializer.js:349:28)\n    at async verifyEnhancedErrorHandling (C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\verify-enhanced-error-handling.js:107:24)","timestamp":"2025-07-20T20:09:25.406Z"}
{"level":"info","message":"[EnhancedComponentInitializer] Attempting recovery for critical-component using strategy: restart-component","service":"trading-system","timestamp":"2025-07-20T20:09:25.406Z"}
{"level":"info","message":"[EnhancedComponentInitializer] Restarting component: critical-component","service":"trading-system","timestamp":"2025-07-20T20:09:25.406Z"}
{"level":"info","message":"[EnhancedComponentInitializer] 🔄 Recovery successful for critical-component, retrying initialization","service":"trading-system","timestamp":"2025-07-20T20:09:25.406Z"}

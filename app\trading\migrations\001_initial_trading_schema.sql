-- Migration: 001_initial_trading_schema.sql
-- Description: Initial trading database schema with enhanced features
-- Database: trading
-- Created: 2025-01-18

-- Enable foreign keys and WAL mode
PRAGMA
foreign_keys = ON;
PRAGMA
journal_mode = WAL;
PRAGMA
synchronous = NORMAL;

-- Create migration tracking table
CREATE TABLE IF NOT EXISTS schema_migrations
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    version
    TEXT
    NOT
    NULL
    UNIQUE,
    description
    TEXT,
    applied_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP
);

-- Insert this migration record
INSERT
OR IGNORE INTO schema_migrations (version, description)
VALUES ('001', 'Initial trading schema with enhanced features');

-- Enhanced coins table
CREATE TABLE IF NOT EXISTS coins
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    TEXT
    NOT
    NULL
    UNIQUE,
    name
    TEXT
    NOT
    NULL,
    market_cap
    REAL,
    volume_24h
    REAL,
    price_usd
    REAL,
    price_change_24h
    REAL,
    price_change_7d
    REAL,
    price_change_30d
    REAL,
    last_updated
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    created_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    is_active
    BOOLEAN
    DEFAULT
    1,
    data_source
    TEXT
    DEFAULT
    'api',
    metadata
    TEXT -- JSON field for additional data
);

-- Enhanced coin metadata
CREATE TABLE IF NOT EXISTS coin_metadata
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    coin_id
    INTEGER
    NOT
    NULL,
    chain
    TEXT
    NOT
    NULL,
    contract_address
    TEXT,
    decimals
    INTEGER
    DEFAULT
    18,
    total_supply
    REAL,
    circulating_supply
    REAL,
    max_supply
    REAL,
    website
    TEXT,
    whitepaper
    TEXT,
    social_links
    TEXT, -- JSON field
    metadata
    TEXT, -- JSON field for additional data
    created_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    updated_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    FOREIGN
    KEY
(
    coin_id
) REFERENCES coins
(
    id
) ON DELETE CASCADE
    );

-- Enhanced trading signals
CREATE TABLE IF NOT EXISTS trading_signals
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    signal_type
    TEXT
    NOT
    NULL,
    symbol
    TEXT
    NOT
    NULL,
    chain
    TEXT
    NOT
    NULL,
    action
    TEXT
    NOT
    NULL
    CHECK (
    action
    IN
(
    'BUY',
    'SELL',
    'HOLD'
)),
    confidence REAL CHECK
(
    confidence
    >=
    0
    AND
    confidence
    <=
    1
),
    price_target REAL,
    stop_loss REAL,
    take_profit REAL,
    timeframe TEXT,
    strategy TEXT,
    metadata TEXT, -- JSON field
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    is_processed BOOLEAN DEFAULT 0,
    source TEXT DEFAULT 'system',
    FOREIGN KEY
(
    symbol
) REFERENCES coins
(
    symbol
)
    );

-- Enhanced trading transactions
CREATE TABLE IF NOT EXISTS trading_transactions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    TEXT
    NOT
    NULL,
    chain
    TEXT
    NOT
    NULL,
    action
    TEXT
    NOT
    NULL
    CHECK (
    action
    IN
(
    'BUY',
    'SELL'
)),
    amount REAL NOT NULL CHECK
(
    amount >
    0
),
    price REAL NOT NULL CHECK
(
    price >
    0
),
    total_value REAL NOT NULL,
    fee REAL DEFAULT 0,
    status TEXT DEFAULT 'pending' CHECK
(
    status
    IN
(
    'pending',
    'executed',
    'failed',
    'canceled'
)),
    exchange TEXT,
    order_id TEXT,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    signal_confidence REAL,
    strategy TEXT,
    execution_mode TEXT,
    slippage REAL,
    gas_fee REAL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY
(
    symbol
) REFERENCES coins
(
    symbol
)
    );

-- Grid trading bots
CREATE TABLE IF NOT EXISTS grid_bots
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    bot_name
    TEXT
    NOT
    NULL,
    symbol
    TEXT
    NOT
    NULL,
    exchange
    TEXT
    NOT
    NULL,
    status
    TEXT
    DEFAULT
    'inactive'
    CHECK (
    status
    IN
(
    'active',
    'inactive',
    'paused',
    'error'
)),
    grid_type TEXT CHECK
(
    grid_type
    IN
(
    'arithmetic',
    'geometric'
)),
    upper_price REAL NOT NULL CHECK
(
    upper_price >
    0
),
    lower_price REAL NOT NULL CHECK
(
    lower_price >
    0
),
    grid_count INTEGER NOT NULL CHECK
(
    grid_count >
    0
),
    investment_amount REAL NOT NULL CHECK
(
    investment_amount >
    0
),
    current_profit REAL DEFAULT 0,
    total_trades INTEGER DEFAULT 0,
    config TEXT, -- JSON field for bot configuration
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_trade_at TIMESTAMP,
    FOREIGN KEY
(
    symbol
) REFERENCES coins
(
    symbol
),
    CHECK
(
    upper_price >
    lower_price
)
    );

-- Whale tracking
CREATE TABLE IF NOT EXISTS whale_wallets
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    address
    TEXT
    NOT
    NULL
    UNIQUE,
    chain
    TEXT
    NOT
    NULL,
    label
    TEXT,
    tier
    TEXT
    CHECK (
    tier
    IN
(
    'elite',
    'large',
    'medium',
    'small'
)),
    total_value_usd REAL,
    transaction_count INTEGER DEFAULT 0,
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    confidence_score REAL DEFAULT 0.5,
    tags TEXT, -- JSON array of tags
    metadata TEXT -- JSON field
    );

CREATE TABLE IF NOT EXISTS whale_transactions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    whale_id
    INTEGER
    NOT
    NULL,
    transaction_hash
    TEXT
    NOT
    NULL
    UNIQUE,
    chain
    TEXT
    NOT
    NULL,
    from_address
    TEXT
    NOT
    NULL,
    to_address
    TEXT
    NOT
    NULL,
    symbol
    TEXT
    NOT
    NULL,
    amount
    REAL
    NOT
    NULL
    CHECK
(
    amount >
    0
),
    value_usd REAL,
    transaction_type TEXT CHECK
(
    transaction_type
    IN
(
    'transfer',
    'swap',
    'stake',
    'unstake',
    'mint',
    'burn'
)),
    gas_fee REAL,
    block_number INTEGER,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    analyzed BOOLEAN DEFAULT 0,
    impact_score REAL,
    FOREIGN KEY
(
    whale_id
) REFERENCES whale_wallets
(
    id
) ON DELETE CASCADE
    );

-- Enhanced performance tracking
CREATE TABLE IF NOT EXISTS performance_metrics
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    metric_date
    DATE
    DEFAULT (
    DATE
(
    'now'
)),
    total_portfolio_value DECIMAL
(
    18,
    2
) DEFAULT 0,
    daily_pnl DECIMAL
(
    18,
    8
) DEFAULT 0,
    daily_pnl_percentage DECIMAL
(
    8,
    4
) DEFAULT 0,
    total_trades INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    losing_trades INTEGER DEFAULT 0,
    win_rate DECIMAL
(
    5,
    2
) DEFAULT 0,
    average_profit DECIMAL
(
    18,
    8
) DEFAULT 0,
    average_loss DECIMAL
(
    18,
    8
) DEFAULT 0,
    max_drawdown DECIMAL
(
    8,
    4
) DEFAULT 0,
    sharpe_ratio DECIMAL
(
    8,
    4
) DEFAULT 0,
    sortino_ratio DECIMAL
(
    8,
    4
) DEFAULT 0,
    active_positions INTEGER DEFAULT 0,
    total_fees DECIMAL
(
    18,
    8
) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE
(
    metric_date
)
    );

-- System health tracking
CREATE TABLE IF NOT EXISTS system_health
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    component
    TEXT
    NOT
    NULL,
    status
    TEXT
    NOT
    NULL
    CHECK (
    status
    IN
(
    'healthy',
    'warning',
    'critical',
    'unknown'
)),
    metrics TEXT, -- JSON field
    last_check TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    error_message TEXT,
    check_count INTEGER DEFAULT 1,
    consecutive_failures INTEGER DEFAULT 0
    );

-- Portfolio positions
CREATE TABLE IF NOT EXISTS portfolio_positions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    symbol
    TEXT
    NOT
    NULL,
    chain
    TEXT
    NOT
    NULL,
    amount
    REAL
    NOT
    NULL
    CHECK
(
    amount
    >=
    0
),
    average_price REAL NOT NULL CHECK
(
    average_price >
    0
),
    current_price REAL,
    total_value REAL,
    unrealized_pnl REAL DEFAULT 0,
    realized_pnl REAL DEFAULT 0,
    position_type TEXT CHECK
(
    position_type
    IN
(
    'long',
    'short'
)),
    opened_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    FOREIGN KEY
(
    symbol
) REFERENCES coins
(
    symbol
)
    );

-- Create performance indexes
CREATE INDEX IF NOT EXISTS idx_coins_symbol_active ON coins(symbol, is_active);
CREATE INDEX IF NOT EXISTS idx_coins_updated ON coins(last_updated);
CREATE INDEX IF NOT EXISTS idx_metadata_coin_chain ON coin_metadata(coin_id, chain);
CREATE INDEX IF NOT EXISTS idx_signals_type_created ON trading_signals(signal_type, created_at);
CREATE INDEX IF NOT EXISTS idx_signals_symbol_processed ON trading_signals(symbol, is_processed);
CREATE INDEX IF NOT EXISTS idx_transactions_symbol_status ON trading_transactions(symbol, status);
CREATE INDEX IF NOT EXISTS idx_transactions_executed ON trading_transactions(executed_at);
CREATE INDEX IF NOT EXISTS idx_grid_bots_status ON grid_bots(status, updated_at);
CREATE INDEX IF NOT EXISTS idx_whale_active_tier ON whale_wallets(is_active, tier);
CREATE INDEX IF NOT EXISTS idx_whale_transactions_hash ON whale_transactions(transaction_hash);
CREATE INDEX IF NOT EXISTS idx_whale_transactions_analyzed ON whale_transactions(analyzed, timestamp);
CREATE INDEX IF NOT EXISTS idx_performance_date ON performance_metrics(metric_date);
CREATE INDEX IF NOT EXISTS idx_health_component_status ON system_health(component, status);
CREATE INDEX IF NOT EXISTS idx_positions_symbol_active ON portfolio_positions(symbol, is_active);

-- Create triggers for data integrity and automation
CREATE TRIGGER IF NOT EXISTS update_coin_timestamp 
AFTER
UPDATE ON coins
BEGIN
UPDATE coins
SET last_updated = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_metadata_timestamp 
AFTER
UPDATE ON coin_metadata
BEGIN
UPDATE coin_metadata
SET updated_at = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS validate_transaction_amount 
BEFORE INSERT ON trading_transactions
BEGIN
SELECT CASE
           WHEN NEW.amount <= 0 THEN RAISE(ABORT, 'Transaction amount must be positive')
           WHEN NEW.price <= 0 THEN RAISE(ABORT, 'Transaction price must be positive')
           END;
END;

CREATE TRIGGER IF NOT EXISTS update_transaction_timestamp 
AFTER
UPDATE ON trading_transactions
BEGIN
UPDATE trading_transactions
SET updated_at = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_grid_bot_timestamp 
AFTER
UPDATE ON grid_bots
BEGIN
UPDATE grid_bots
SET updated_at = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_position_timestamp 
AFTER
UPDATE ON portfolio_positions
BEGIN
UPDATE portfolio_positions
SET updated_at = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

-- Insert initial system health records
INSERT
OR IGNORE INTO system_health (component, status, metrics) VALUES
('database', 'healthy', '{"initialized": true, "migration": "001"}'),
('trading_engine', 'unknown', '{"status": "not_started"}'),
('data_collection', 'unknown', '{"status": "not_started"}'),
('risk_management', 'unknown', '{"status": "not_started"}');

-- Insert sample performance metric for current date
INSERT
OR IGNORE INTO performance_metrics (metric_date) VALUES (DATE('now'));

PRAGMA
optimize;
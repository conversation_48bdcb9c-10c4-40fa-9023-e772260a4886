-- Migration: 002_n8n_workflow_schema.sql
-- Description: N8N workflow automation database schema
-- Database: n8n
-- Created: 2025-01-18

-- Enable WAL mode for better concurrency
PRAGMA
journal_mode = WAL;
PRAGMA
synchronous = NORMAL;
PRAGMA
foreign_keys = ON;

-- Create migration tracking table
CREATE TABLE IF NOT EXISTS schema_migrations
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    version
    TEXT
    NOT
    NULL
    UNIQUE,
    description
    TEXT,
    applied_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP
);

-- Insert this migration record
INSERT
OR IGNORE INTO schema_migrations (version, description)
VALUES ('002', 'N8N workflow automation schema');

-- Enhanced workflow executions
CREATE TABLE IF NOT EXISTS workflow_executions
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    workflow_id
    TEXT
    NOT
    NULL,
    execution_id
    TEXT
    UNIQUE
    NOT
    NULL,
    workflow_name
    TEXT,
    status
    TEXT
    DEFAULT
    'running'
    CHECK (
    status
    IN
(
    'running',
    'success',
    'error',
    'canceled',
    'waiting'
)),
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    finished_at TIMESTAMP,
    duration_ms INTEGER,
    error_message TEXT,
    data TEXT, -- JSON field for execution data
    metadata TEXT, -- JSON field for additional metadata
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    parent_execution_id TEXT,
    trigger_type TEXT,
    FOREIGN KEY
(
    parent_execution_id
) REFERENCES workflow_executions
(
    execution_id
)
    );

-- Enhanced webhook data
CREATE TABLE IF NOT EXISTS webhook_data
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    webhook_url
    TEXT
    NOT
    NULL,
    webhook_name
    TEXT,
    method
    TEXT
    NOT
    NULL
    CHECK (
    method
    IN
(
    'GET',
    'POST',
    'PUT',
    'DELETE',
    'PATCH'
)),
    payload TEXT, -- JSON field
    headers TEXT, -- JSON field
    query_params TEXT, -- JSON field
    processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    processing_duration_ms INTEGER,
    response_status INTEGER,
    response_data TEXT,
    error_message TEXT
    );

-- Enhanced scheduled tasks
CREATE TABLE IF NOT EXISTS scheduled_tasks
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    task_name
    TEXT
    NOT
    NULL
    UNIQUE,
    workflow_id
    TEXT,
    schedule
    TEXT
    NOT
    NULL, -- Cron expression
    timezone
    TEXT
    DEFAULT
    'UTC',
    last_run
    TIMESTAMP,
    next_run
    TIMESTAMP,
    is_active
    BOOLEAN
    DEFAULT
    TRUE,
    created_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    updated_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    error_count
    INTEGER
    DEFAULT
    0,
    consecutive_failures
    INTEGER
    DEFAULT
    0,
    max_failures
    INTEGER
    DEFAULT
    5,
    last_error
    TEXT,
    execution_count
    INTEGER
    DEFAULT
    0,
    average_duration_ms
    INTEGER
    DEFAULT
    0
);

-- Workflow templates
CREATE TABLE IF NOT EXISTS workflow_templates
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    template_name
    TEXT
    NOT
    NULL
    UNIQUE,
    description
    TEXT,
    category
    TEXT,
    workflow_definition
    TEXT
    NOT
    NULL, -- JSON field
    parameters
    TEXT, -- JSON field for template parameters
    tags
    TEXT, -- JSON array
    version
    TEXT
    DEFAULT
    '1.0.0',
    created_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    updated_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    is_active
    BOOLEAN
    DEFAULT
    TRUE,
    usage_count
    INTEGER
    DEFAULT
    0
);

-- Workflow monitoring
CREATE TABLE IF NOT EXISTS workflow_monitoring
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    workflow_id
    TEXT
    NOT
    NULL,
    metric_name
    TEXT
    NOT
    NULL,
    metric_value
    REAL,
    metric_unit
    TEXT,
    recorded_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    tags
    TEXT -- JSON field for additional tags
);

-- API endpoints for external integrations
CREATE TABLE IF NOT EXISTS api_endpoints
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    endpoint_name
    TEXT
    NOT
    NULL
    UNIQUE,
    endpoint_path
    TEXT
    NOT
    NULL,
    method
    TEXT
    NOT
    NULL
    CHECK (
    method
    IN
(
    'GET',
    'POST',
    'PUT',
    'DELETE',
    'PATCH'
)),
    workflow_id TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    authentication_required BOOLEAN DEFAULT FALSE,
    rate_limit INTEGER DEFAULT 100, -- requests per minute
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed TIMESTAMP,
    access_count INTEGER DEFAULT 0,
    description TEXT
    );

-- Notification settings
CREATE TABLE IF NOT EXISTS notification_settings
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    workflow_id
    TEXT,
    notification_type
    TEXT
    CHECK (
    notification_type
    IN
(
    'email',
    'webhook',
    'slack',
    'discord'
)),
    trigger_event TEXT CHECK
(
    trigger_event
    IN
(
    'success',
    'error',
    'start',
    'finish'
)),
    configuration TEXT NOT NULL, -- JSON field
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

-- Create performance indexes
CREATE INDEX IF NOT EXISTS idx_executions_workflow_status ON workflow_executions(workflow_id, status);
CREATE INDEX IF NOT EXISTS idx_executions_started ON workflow_executions(started_at);
CREATE INDEX IF NOT EXISTS idx_executions_status_finished ON workflow_executions(status, finished_at);
CREATE INDEX IF NOT EXISTS idx_webhook_processed_created ON webhook_data(processed, created_at);
CREATE INDEX IF NOT EXISTS idx_webhook_url_method ON webhook_data(webhook_url, method);
CREATE INDEX IF NOT EXISTS idx_tasks_active_next ON scheduled_tasks(is_active, next_run);
CREATE INDEX IF NOT EXISTS idx_tasks_name_active ON scheduled_tasks(task_name, is_active);
CREATE INDEX IF NOT EXISTS idx_templates_category_active ON workflow_templates(category, is_active);
CREATE INDEX IF NOT EXISTS idx_monitoring_workflow_recorded ON workflow_monitoring(workflow_id, recorded_at);
CREATE INDEX IF NOT EXISTS idx_endpoints_path_active ON api_endpoints(endpoint_path, is_active);
CREATE INDEX IF NOT EXISTS idx_notifications_workflow_active ON notification_settings(workflow_id, is_active);

-- Create triggers for automation and data integrity
CREATE TRIGGER IF NOT EXISTS update_execution_finished 
AFTER
UPDATE OF status
ON workflow_executions
    WHEN NEW.status IN ('success', 'error', 'canceled')
BEGIN
UPDATE workflow_executions
SET finished_at = CURRENT_TIMESTAMP,
    duration_ms = (julianday(CURRENT_TIMESTAMP) - julianday(started_at)) * 86400000
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_webhook_processed 
AFTER
UPDATE OF processed
ON webhook_data
    WHEN NEW.processed = TRUE
BEGIN
UPDATE webhook_data
SET processed_at           = CURRENT_TIMESTAMP,
    processing_duration_ms = (julianday(CURRENT_TIMESTAMP) - julianday(created_at)) * 86400000
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_task_timestamp 
AFTER
UPDATE ON scheduled_tasks
BEGIN
UPDATE scheduled_tasks
SET updated_at = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_template_timestamp 
AFTER
UPDATE ON workflow_templates
BEGIN
UPDATE workflow_templates
SET updated_at = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS increment_template_usage 
AFTER INSERT ON workflow_executions 
WHEN NEW.workflow_id IN (SELECT template_name FROM workflow_templates)
BEGIN
UPDATE workflow_templates
SET usage_count = usage_count + 1
WHERE template_name = NEW.workflow_id;
END;

CREATE TRIGGER IF NOT EXISTS update_endpoint_access 
AFTER INSERT ON webhook_data 
WHEN NEW.webhook_url IN (SELECT endpoint_path FROM api_endpoints)
BEGIN
UPDATE api_endpoints
SET last_accessed = CURRENT_TIMESTAMP,
    access_count  = access_count + 1
WHERE endpoint_path = NEW.webhook_url;
END;

-- Insert default workflow templates
INSERT
OR IGNORE INTO workflow_templates (template_name, description, category, workflow_definition) VALUES
('trading_signal_processor', 'Process incoming trading signals', 'trading', '{"nodes": [], "connections": []}'),
('market_data_collector', 'Collect market data from various sources', 'data', '{"nodes": [], "connections": []}'),
('portfolio_rebalancer', 'Automatically rebalance portfolio', 'trading', '{"nodes": [], "connections": []}'),
('alert_manager', 'Manage trading alerts and notifications', 'monitoring', '{"nodes": [], "connections": []}');

-- Insert default scheduled tasks
INSERT
OR IGNORE INTO scheduled_tasks (task_name, schedule, workflow_id) VALUES
('daily_portfolio_sync', '0 0 * * *', 'portfolio_rebalancer'),
('hourly_market_data', '0 * * * *', 'market_data_collector'),
('signal_processor', '*/5 * * * *', 'trading_signal_processor');

-- Insert default API endpoints
INSERT
OR IGNORE INTO api_endpoints (endpoint_name, endpoint_path, method, description) VALUES
('webhook_trading_signal', '/webhook/trading-signal', 'POST', 'Receive trading signals from external sources'),
('webhook_market_data', '/webhook/market-data', 'POST', 'Receive market data updates'),
('api_portfolio_status', '/api/portfolio/status', 'GET', 'Get current portfolio status'),
('api_trading_stats', '/api/trading/stats', 'GET', 'Get trading statistics');

PRAGMA
optimize;
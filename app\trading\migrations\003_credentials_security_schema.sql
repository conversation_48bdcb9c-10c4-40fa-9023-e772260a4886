-- Migration: 003_credentials_security_schema.sql
-- Description: Secure credentials storage database schema
-- Database: credentials
-- Created: 2025-01-18

-- Use DELETE mode for security (no WAL for credentials)
PRAGMA
journal_mode = DELETE;
PRAGMA
synchronous = FULL;
PRAGMA
foreign_keys = ON;

-- Create migration tracking table
CREATE TABLE IF NOT EXISTS schema_migrations
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    version
    TEXT
    NOT
    NULL
    UNIQUE,
    description
    TEXT,
    applied_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP
);

-- Insert this migration record
INSERT
OR IGNORE INTO schema_migrations (version, description)
VALUES ('003', 'Secure credentials storage schema');

-- Enhanced credentials storage
CREATE TABLE IF NOT EXISTS credentials
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    service_name
    TEXT
    NOT
    NULL,
    credential_type
    TEXT
    NOT
    NULL
    CHECK (
    credential_type
    IN
(
    'api_key',
    'oauth',
    'basic_auth',
    'jwt',
    'certificate'
)),
    encrypted_data TEXT NOT NULL, -- AES encrypted credential data
    encryption_method TEXT DEFAULT 'AES-256-GCM',
    salt TEXT NOT NULL, -- Salt for encryption
    iv TEXT NOT NULL, -- Initialization vector
    metadata TEXT, -- JSON field for additional metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    last_used TIMESTAMP,
    usage_count INTEGER DEFAULT 0,
    rotation_required BOOLEAN DEFAULT FALSE,
    rotation_interval_days INTEGER DEFAULT 90,
    created_by TEXT DEFAULT 'system',
    tags TEXT -- JSON array for categorization
    );

-- Enhanced API keys management
CREATE TABLE IF NOT EXISTS api_keys
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    service_name
    TEXT
    NOT
    NULL
    UNIQUE,
    key_name
    TEXT,
    api_key_hash
    TEXT
    NOT
    NULL, -- SHA-256 hash of the API key
    permissions
    TEXT, -- JSON array of permissions
    rate_limits
    TEXT, -- JSON object with rate limit info
    created_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    last_used
    TIMESTAMP,
    expires_at
    TIMESTAMP,
    is_valid
    BOOLEAN
    DEFAULT
    TRUE,
    usage_count
    INTEGER
    DEFAULT
    0,
    daily_usage_limit
    INTEGER,
    monthly_usage_limit
    INTEGER,
    current_daily_usage
    INTEGER
    DEFAULT
    0,
    current_monthly_usage
    INTEGER
    DEFAULT
    0,
    last_rotation
    TIMESTAMP,
    rotation_required
    BOOLEAN
    DEFAULT
    FALSE,
    environment
    TEXT
    DEFAULT
    'production'
    CHECK (
    environment
    IN
(
    'development',
    'staging',
    'production'
)),
    ip_whitelist TEXT, -- JSON array of allowed IPs
    webhook_url TEXT, -- For notifications
    metadata TEXT -- JSON field
    );

-- OAuth tokens management
CREATE TABLE IF NOT EXISTS oauth_tokens
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    service_name
    TEXT
    NOT
    NULL,
    token_type
    TEXT
    DEFAULT
    'bearer',
    encrypted_access_token
    TEXT
    NOT
    NULL,
    encrypted_refresh_token
    TEXT,
    token_hash
    TEXT
    NOT
    NULL, -- Hash for quick lookup
    scope
    TEXT, -- OAuth scopes
    expires_at
    TIMESTAMP,
    refresh_expires_at
    TIMESTAMP,
    created_at
    TIMESTAMP
    DEFAULT
    CURRENT_TIMESTAMP,
    last_refreshed
    TIMESTAMP,
    is_active
    BOOLEAN
    DEFAULT
    TRUE,
    client_id
    TEXT,
    grant_type
    TEXT,
    metadata
    TEXT  -- JSON field
);

-- Certificate management
CREATE TABLE IF NOT EXISTS certificates
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    certificate_name
    TEXT
    NOT
    NULL
    UNIQUE,
    certificate_type
    TEXT
    CHECK (
    certificate_type
    IN
(
    'ssl',
    'client',
    'ca',
    'signing'
)),
    encrypted_certificate TEXT NOT NULL, -- PEM format, encrypted
    encrypted_private_key TEXT, -- Private key, encrypted
    certificate_hash TEXT NOT NULL,
    issuer TEXT,
    subject TEXT,
    serial_number TEXT,
    valid_from TIMESTAMP,
    valid_until TIMESTAMP,
    fingerprint TEXT,
    key_size INTEGER,
    signature_algorithm TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    auto_renew BOOLEAN DEFAULT FALSE,
    renewal_threshold_days INTEGER DEFAULT 30,
    metadata TEXT -- JSON field
    );

-- Security audit log
CREATE TABLE IF NOT EXISTS security_audit_log
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    event_type
    TEXT
    NOT
    NULL
    CHECK (
    event_type
    IN
(
    'create',
    'read',
    'update',
    'delete',
    'rotate',
    'expire',
    'login',
    'logout'
)),
    resource_type TEXT NOT NULL CHECK
(
    resource_type
    IN
(
    'credential',
    'api_key',
    'oauth_token',
    'certificate'
)),
    resource_id INTEGER,
    service_name TEXT,
    user_id TEXT,
    ip_address TEXT,
    user_agent TEXT,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    additional_data TEXT, -- JSON field
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

-- Access control
CREATE TABLE IF NOT EXISTS access_control
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    resource_type
    TEXT
    NOT
    NULL,
    resource_id
    INTEGER
    NOT
    NULL,
    principal_type
    TEXT
    CHECK (
    principal_type
    IN
(
    'user',
    'service',
    'role'
)),
    principal_id TEXT NOT NULL,
    permission TEXT NOT NULL CHECK
(
    permission
    IN
(
    'read',
    'write',
    'delete',
    'rotate',
    'admin'
)),
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    granted_by TEXT,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
    );

-- Security settings
CREATE TABLE IF NOT EXISTS security_settings
(
    id
    INTEGER
    PRIMARY
    KEY
    AUTOINCREMENT,
    setting_name
    TEXT
    NOT
    NULL
    UNIQUE,
    setting_value
    TEXT
    NOT
    NULL,
    setting_type
    TEXT
    CHECK (
    setting_type
    IN
(
    'string',
    'number',
    'boolean',
    'json'
)),
    description TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT
    );

-- Create security-focused indexes
CREATE INDEX IF NOT EXISTS idx_credentials_service_active ON credentials(service_name, is_active);
CREATE INDEX IF NOT EXISTS idx_credentials_expires ON credentials(expires_at, is_active);
CREATE INDEX IF NOT EXISTS idx_credentials_rotation ON credentials(rotation_required, rotation_interval_days);
CREATE INDEX IF NOT EXISTS idx_api_keys_service_valid ON api_keys(service_name, is_valid);
CREATE INDEX IF NOT EXISTS idx_api_keys_expires ON api_keys(expires_at, is_valid);
CREATE INDEX IF NOT EXISTS idx_api_keys_rotation ON api_keys(rotation_required, last_rotation);
CREATE INDEX IF NOT EXISTS idx_oauth_tokens_service_active ON oauth_tokens(service_name, is_active);
CREATE INDEX IF NOT EXISTS idx_oauth_tokens_expires ON oauth_tokens(expires_at, is_active);
CREATE INDEX IF NOT EXISTS idx_oauth_tokens_hash ON oauth_tokens(token_hash);
CREATE INDEX IF NOT EXISTS idx_certificates_name_active ON certificates(certificate_name, is_active);
CREATE INDEX IF NOT EXISTS idx_certificates_expires ON certificates(valid_until, is_active);
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON security_audit_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_log_resource ON security_audit_log(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_service ON security_audit_log(service_name, timestamp);
CREATE INDEX IF NOT EXISTS idx_access_control_resource ON access_control(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_access_control_principal ON access_control(principal_type, principal_id);

-- Create security triggers
CREATE TRIGGER IF NOT EXISTS update_credential_timestamp 
AFTER
UPDATE ON credentials
BEGIN
UPDATE credentials
SET updated_at = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS log_credential_access 
AFTER
UPDATE OF last_used
ON credentials
BEGIN
INSERT INTO security_audit_log (event_type, resource_type, resource_id, service_name, timestamp)
VALUES ('read', 'credential', NEW.id, NEW.service_name, CURRENT_TIMESTAMP);

UPDATE credentials
SET usage_count = usage_count + 1
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS log_api_key_usage 
AFTER
UPDATE OF last_used
ON api_keys
BEGIN
INSERT INTO security_audit_log (event_type, resource_type, resource_id, service_name, timestamp)
VALUES ('read', 'api_key', NEW.id, NEW.service_name, CURRENT_TIMESTAMP);

UPDATE api_keys
SET usage_count           = usage_count + 1,
    current_daily_usage   = current_daily_usage + 1,
    current_monthly_usage = current_monthly_usage + 1
WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS check_api_key_limits 
BEFORE
UPDATE OF current_daily_usage
ON api_keys
    WHEN NEW.daily_usage_limit IS NOT NULL AND NEW.current_daily_usage >= NEW.daily_usage_limit
BEGIN
SELECT RAISE(ABORT, 'Daily API key usage limit exceeded');
END;

CREATE TRIGGER IF NOT EXISTS log_oauth_token_refresh 
AFTER
UPDATE OF last_refreshed
ON oauth_tokens
BEGIN
INSERT INTO security_audit_log (event_type, resource_type, resource_id, service_name, timestamp)
VALUES ('update', 'oauth_token', NEW.id, NEW.service_name, CURRENT_TIMESTAMP);
END;

CREATE TRIGGER IF NOT EXISTS update_security_settings_timestamp 
AFTER
UPDATE ON security_settings
BEGIN
UPDATE security_settings
SET updated_at = CURRENT_TIMESTAMP
WHERE id = NEW.id;
END;

-- Insert default security settings
INSERT
OR IGNORE INTO security_settings (setting_name, setting_value, setting_type, description) VALUES
('encryption_algorithm', 'AES-256-GCM', 'string', 'Default encryption algorithm for credentials'),
('key_rotation_interval', '90', 'number', 'Default key rotation interval in days'),
('session_timeout', '3600', 'number', 'Session timeout in seconds'),
('max_login_attempts', '5', 'number', 'Maximum login attempts before lockout'),
('audit_retention_days', '365', 'number', 'Audit log retention period in days'),
('require_2fa', 'true', 'boolean', 'Require two-factor authentication'),
('password_min_length', '12', 'number', 'Minimum password length'),
('password_complexity', 'true', 'boolean', 'Require complex passwords'),
('ip_whitelist_enabled', 'false', 'boolean', 'Enable IP address whitelisting'),
('rate_limit_enabled', 'true', 'boolean', 'Enable API rate limiting');

-- Insert default access control for system
INSERT
OR IGNORE INTO access_control (resource_type, resource_id, principal_type, principal_id, permission) VALUES
('credential', 0, 'service', 'trading_system', 'read'),
('api_key', 0, 'service', 'trading_system', 'read'),
('oauth_token', 0, 'service', 'trading_system', 'read');

PRAGMA
optimize;
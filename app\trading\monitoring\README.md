# Enhanced Health Monitoring System

A comprehensive health monitoring system for trading applications with real-time metrics, alerting, and status reporting
capabilities.

## 🚀 Features

- **Real-time Health Checks**: Continuous monitoring of system components
- **Web Dashboard**: Interactive web interface for health visualization
- **Alerting System**: Configurable alerts for critical issues
- **Performance Metrics**: System and application performance tracking
- **Exchange Connectivity**: Real-time exchange API health monitoring
- **Database Health**: Database connectivity and performance checks
- **Strategy Monitoring**: Trading strategy health and performance
- **CLI Tools**: Command-line interface for management
- **WebSocket Support**: Real-time updates via WebSocket
- **Export Capabilities**: Data export in multiple formats

## 📋 Quick Start

### Installation

```bash
npm install
```

### Basic Usage

```javascript
const { EnhancedHealthMonitor } = require('./enhanced-health-monitor');

const healthMonitor = new EnhancedHealthMonitor({
    checkInterval: 30000,
    enableMetrics: true,
    enableAlerts: true
});

await healthMonitor.start();
```

### CLI Usage

```bash
# Start monitoring
npm run start

# Show status
npm run status

# Run health check
npm run check

# Start dashboard
npm run dashboard
```

## 🏗 Architecture

### Components

1. **EnhancedHealthMonitor**: Main monitoring engine
2. **HealthDashboard**: Web-based dashboard
3. **HealthCheckSystem**: Core health check system
4. **HealthCLI**: Command-line interface

### Health Check Categories

- **System**: CPU, memory, disk usage
- **Application**: Event loop, uptime, process health
- **Database**: Connectivity, performance, queries
- **Exchange**: API connectivity, market data
- **Strategy**: Active strategies, performance metrics
- **Performance**: Trading performance, rate limits

## 📊 Health Checks

### Available Checks

| Check                  | Description                 | Critical |
|------------------------|-----------------------------|----------|
| system.cpu             | CPU usage monitoring        | No       |
| system.memory          | Memory usage tracking       | No       |
| system.disk            | Disk space monitoring       | No       |
| app.uptime             | Application uptime          | No       |
| app.eventLoop          | Event loop lag              | No       |
| app.process            | Process memory usage        | No       |
| database.connectivity  | Database connection         | Yes      |
| database.performance   | Database query performance  | No       |
| exchange.*             | Exchange API connectivity   | Yes      |
| strategy.manager       | Strategy manager health     | Yes      |
| strategy.active        | Active strategies count     | No       |
| performance.trading    | Trading performance metrics | No       |
| performance.rateLimits | API rate limit usage        | No       |

### Custom Checks

```javascript
healthMonitor.healthCheckSystem.registerCheck(
    'custom.check',
    async () => {
        // Your check logic
        return {
            status: 'healthy',
            message: 'Custom check passed',
            details: { custom: 'data' }
        };
    },
    { description: 'Custom health check', timeout: 5000 }
);
```

## 🖥 Dashboard

### Features

- **Real-time Updates**: Live health status updates
- **WebSocket Support**: Real-time data streaming
- **Interactive Charts**: Visual health metrics
- **Alert Notifications**: Real-time alert display
- **Mobile Responsive**: Works on all devices
- **Export Data**: Export health data in multiple formats

### Dashboard API Endpoints

| Endpoint           | Method | Description             |
|--------------------|--------|-------------------------|
| `/api/health`      | GET    | Get health status       |
| `/api/status`      | GET    | Get real-time status    |
| `/api/metrics`     | GET    | Get performance metrics |
| `/api/alerts`      | GET    | Get recent alerts       |
| `/api/check/:name` | GET    | Run specific check      |
| `/api/start`       | POST   | Start monitoring        |
| `/api/stop`        | POST   | Stop monitoring         |

### WebSocket Events

| Event     | Description                 |
|-----------|-----------------------------|
| `health`  | Health status updates       |
| `metrics` | Performance metrics updates |
| `alert`   | Alert notifications         |

## 🚨 Alerting System

### Alert Types

- **Critical**: System failure requiring immediate attention
- **Warning**: Potential issues that need monitoring
- **Info**: Informational messages

### Alert Handlers

```javascript
// Register alert handler
const handlerId = healthMonitor.registerAlertHandler((alert) => {
    console.log(`ALERT: ${alert.level} - ${alert.message}`);
});

// Remove alert handler
healthMonitor.removeAlertHandler(handlerId);
```

### Alert Configuration

```javascript
const healthMonitor = new EnhancedHealthMonitor({
    alertThreshold: 3,
    enableAlerts: true,
    metricsRetention: 3600000
});
```

## 🔧 CLI Usage

### Commands

```bash
# Start health monitoring
health-cli start --interval 30000

# Stop monitoring
health-cli stop

# Show health status
health-cli status --format table

# Run health checks
health-cli check --name database.connectivity

# List all checks
health-cli list

# Start dashboard
health-cli dashboard --port 8080 --host 0.0.0.0

# Show metrics
health-cli metrics --range 3600000 --format json

# Show alerts
health-cli alerts --limit 10 --format table

# Monitor specific check
health-cli monitor database.connectivity --interval 5000

# Export data
health-cli export --format json --output health-data.json
```

### Environment Variables

```bash
# Health check interval
HEALTH_CHECK_INTERVAL=30000

# Alert threshold
ALERT_THRESHOLD=3

# Metrics retention
METRICS_RETENTION=3600000

# Dashboard port
HEALTH_DASHBOARD_PORT=8080

# Dashboard host
HEALTH_DASHBOARD_HOST=0.0.0.0
```

## 📈 Metrics Collection

### System Metrics

- CPU usage
- Memory usage
- Disk usage
- Load average
- Uptime

### Process Metrics

- Heap usage
- Event loop lag
- Process uptime
- Memory usage

### Application Metrics

- Trading performance
- Exchange connectivity
- Strategy health
- Database performance

### Metrics API

```javascript
// Get metrics
const metrics = healthMonitor.getMetrics(3600000); // Last hour

// Get real-time status
const status = healthMonitor.getRealTimeStatus();

// Get specific metrics
const cpuMetrics = metrics.map(m => m.system.cpu);
```

## 🔄 Integration

### With Express Application

```javascript
const express = require('express');
const { EnhancedHealthMonitor } = require('./enhanced-health-monitor');

const app = express();
const healthMonitor = new EnhancedHealthMonitor();

// Add health endpoint
app.get('/health', async (req, res) => {
    const health = await healthMonitor.getHealthStatus();
    res.json(health);
});

// Start monitoring
await healthMonitor.start();
```

### With Configuration System

```javascript
const { EnhancedConfigManager } = require('../config/enhanced-config-manager');
const { EnhancedHealthMonitor } = require('./enhanced-health-monitor');

const configManager = new EnhancedConfigManager();
const healthMonitor = new EnhancedHealthMonitor({
    checkInterval: configManager.get('monitoring.healthCheckInterval', 30000)
});
```

### With Logging

```javascript
healthMonitor.on('alert-triggered', (alert) => {
    logger.error('Health alert triggered:', alert);
});

healthMonitor.on('monitoring-started', () => {
    logger.info('Health monitoring started');
});
```

## 🧪 Testing

### Run Tests

```bash
npm test
```

### Test Health Checks

```bash
# Test specific check
health-cli check --name system.cpu

# Test all checks
health-cli check

# Monitor specific check
health-cli monitor system.memory --interval 1000
```

### Load Testing

```bash
# Simulate high load
health-cli start --interval 1000
# Monitor system under load
```

## 🔍 Monitoring Best Practices

1. **Check Intervals**: Balance between responsiveness and performance
2. **Timeout Values**: Set appropriate timeouts for each check
3. **Critical Checks**: Mark critical checks appropriately
4. **Alert Thresholds**: Configure alert thresholds based on system requirements
5. **Metrics Retention**: Set retention based on storage capacity
6. **Dashboard Security**: Secure dashboard endpoints in production
7. **Error Handling**: Implement proper error handling for checks
8. **Logging**: Log health check results and alerts
9. **Monitoring**: Monitor the monitoring system itself
10. **Backup**: Export health data regularly

## 🐛 Troubleshooting

### Common Issues

1. **Health Check Failures**
    - Check individual check configuration
    - Verify dependencies are available
    - Check network connectivity

2. **Dashboard Not Starting**
    - Verify port availability
    - Check for port conflicts
    - Ensure dependencies are installed

3. **Alerts Not Triggering**
    - Check alert configuration
    - Verify alert handlers are registered
    - Check alert threshold settings

4. **WebSocket Issues**
    - Check WebSocket support
    - Verify client connection
    - Check for network issues

### Debug Mode

```bash
DEBUG=health* node health-cli.js start
```

## 📝 Configuration

### Health Monitor Configuration

```javascript
const config = {
    checkInterval: 30000,
    alertThreshold: 3,
    metricsRetention: 3600000,
    enableMetrics: true,
    enableAlerts: true,
    enableDashboard: true,
    port: 8080,
    host: 'localhost'
};
```

### Check Configuration

```javascript
healthMonitor.healthCheckSystem.registerCheck(
    'custom.check',
    checkFunction,
    {
        timeout: 5000,
        critical: true,
        description: 'Custom health check'
    }
);
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new checks
4. Update documentation
5. Submit pull request

## 📄 License

MIT License - see LICENSE file for details
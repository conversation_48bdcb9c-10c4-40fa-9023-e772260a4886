const HealthMonitor = require('../../../app/trading/monitoring/HealthMonitor');
const logger = require('../shared/helpers/logger');
const fs = require('fs').promises;


// Dynamic chalk import cache
let chalkInstance = null;

async function getChalk() {
    if (!chalkInstance) {
        chalkInstance = await import('chalk');
    }
    return chalkInstance.default;
}

class HealthMonitorCLI {
    constructor() {
        // this.monitor = new HealthMonitor();
        // this.isRunning = false;
        // this.intervalId = null;
    }

    async showHelp() {
        const chalk = await getChalk();
        logger.info(chalk.cyan('\n🏥 Health Monitor CLI'));
        logger.info(chalk.gray('='.repeat(50)));
        logger.info(chalk.white('\nCommands:'));
        logger.info(chalk.green('  start [interval]') + chalk.gray('     - Start monitoring (default)'));
        logger.info(chalk.green('  stop') + chalk.gray('                 - Stop monitoring'));
        logger.info(chalk.green('  status') + chalk.gray('               - Show current health status'));
        logger.info(chalk.green('  check [name]') + chalk.gray('         - Run specific health check'));
        logger.info(chalk.green('  list') + chalk.gray('                 - List registered checks'));
        logger.info(chalk.green('  register <name> <fn>') + chalk.gray('  - Register new health check'));
        logger.info(chalk.green('  unregister <name>') + chalk.gray('    - Remove health check'));
        logger.info(chalk.green('  dashboard [options]') + chalk.gray('  - Start web dashboard'));
        logger.info(chalk.green('  metrics [range]') + chalk.gray('      - Show performance metrics'));
        logger.info(chalk.green('  alerts [limit]') + chalk.gray('       - Show recent alerts'));
        logger.info(chalk.green('  export <format>') + chalk.gray('      - Export data (json|csv|xml)'));
        logger.info(chalk.green('  monitor <name>') + chalk.gray('       - Monitor specific check'));
        logger.info(chalk.green('  summary') + chalk.gray('             - Show health summary'));
        logger.info(chalk.green('  help') + chalk.gray('                - Show this help'));

        logger.info(chalk.white('\nOptions:'));
        logger.info(chalk.yellow('  --format, -f') + chalk.gray('         - Output format (json|table|csv)'));
        logger.info(chalk.yellow('  --interval, -i') + chalk.gray('       - Monitoring interval in ms'));
        logger.info(chalk.yellow('  --host, -h') + chalk.gray('           - Dashboard host (default)'));
        logger.info(chalk.yellow('  --port, -p') + chalk.gray('           - Dashboard port (default)'));
        logger.info(chalk.yellow('  --output, -o') + chalk.gray('         - Output file path'));
        logger.info(chalk.yellow('  --limit, -l') + chalk.gray('          - Limit number of results'));
        logger.info(chalk.yellow('  --range, -r') + chalk.gray('          - Time range (1h|6h|24h|7d)'));

        logger.info(chalk.white('\nExamples:'));
        logger.info(chalk.gray('  health start 10000          # Start with 10s interval'));
        logger.info(chalk.gray('  health status --format json # JSON output'));
        logger.info(chalk.gray('  health check database       # Check database health'));
        logger.info(chalk.gray('  health dashboard --port 8080 # Start on port 8080'));
        logger.info(chalk.gray('  health metrics --range 24h  # Last 24 hours'));
        logger.info(chalk.gray('  health export csv --output ./health.csv'));

        logger.info(chalk.cyan('\n' + '='.repeat(50) + '\n'));
    }

    parseOptions(args) {
        const options = {
            format: 'table',
            interval,
            host: 'localhost',
            port,
            output,
            limit,
            range: '1h'
        };

        for (let i = 0; i < args.length; i++) {
            const arg = args[i];
            const nextArg = args[i + 1];

            switch (arg) {
                case '--format'
                    se
                    '-f'(nextArg && ['json', 'table', 'csv'].includes(nextArg))
                {
                    options.format = nextArg;
                    i++;
                }
                    break;
                case '--interval'
                    se
                    '-i'(nextArg && !isNaN(parseInt(nextArg)))
                {
                    options.interval = parseInt(nextArg);
                    i++;
                }
                    break;
                case '--host'
                    se
                    '-h'(nextArg)
                {
                    options.host = nextArg;
                    i++;
                }
                    break;
                case '--port'
                    se
                    '-p'(nextArg && !isNaN(parseInt(nextArg)))
                {
                    options.port = parseInt(nextArg);
                    i++;
                }
                    break;
                case '--output'
                    se
                    '-o'(nextArg)
                {
                    options.output = nextArg;
                    i++;
                }
                    break;
                case '--limit'
                    se
                    '-l'(nextArg && !isNaN(parseInt(nextArg)))
                {
                    options.limit = parseInt(nextArg);
                    i++;
                }
                    break;
                case '--range'
                    se
                    '-r'(nextArg && ['1h', '6h', '24h', '7d'].includes(nextArg))
                {
                    options.range = nextArg;
                    i++;
                }
                    break;
            }
        }

        return options;
    }

    async executeCommand(command, args = []) {
        const chalk = await getChalk();
        const options = this.parseOptions(args);

        try {
            switch (command) {
                case 'help'
                    ait
                    // this.showHelp();
                    break;

                case 'start'
                    ait
                    // this.startMonitoring(options.interval);
                    break;

                case 'stop'
                    ait
                    // this.stopMonitoring();
                    break;

                case 'status'
                    ait
                    // this.showStatus(options.format);
                    break;

                case 'check'
                    ait
                    // this.runCheck(args[0], options.format);
                    break;

                case 'list'
                    ait
                    // this.listChecks();
                    break;

                case 'register'
                    ait
                    // this.registerCheck(args[0], args[1]);
                    break;

                case 'unregister'
                    ait
                    // this.unregisterCheck(args[0]);
                    break;

                case 'dashboard'
                    ait
                    // this.startDashboard(options);
                    break;

                case 'metrics'
                    ait
                    // this.showMetrics(options.range, options.format);
                    break;

                case 'alerts'
                    ait
                    // this.showAlerts(options.limit, options.format);
                    break;

                case 'export'
                    ait
                    // this.exportData(args[0] || options.format, options.output);
                    break;

                case 'monitor'
                    ait
                    // this.monitorCheck(args[0], options.interval);
                    break;

                case 'summary'
                    ait
                    // this.showSummary(options.format);
                    break;

                default
                    (chalk.red(`❌ Unknown command: ${command}`));
                    logger.info(chalk.yellow('Run "health help" for available commands'));
                    process.exit(1);
            }
        } catch (error) {
            logger.error(chalk.red('❌ Command failed:'), error.message);
            logger.error('Health CLI command failed', {command, args, error});
            process.exit(1);
        }
    }

    async startMonitoring(interval = 30000) {
        const chalk = await getChalk();
        try {
            logger.info(chalk.blue(`🔄 Starting health monitoring with ${interval}ms interval...`));
            await this.monitor.start(interval);
            logger.info(chalk.green('✅ Health monitoring started'));
            // this.isRunning = true;
        } catch (error) {
            logger.error(chalk.red('❌ Failed to start monitoring:'), error.message);
            throw error;
        }
    }

    async stopMonitoring() {
        const chalk = await getChalk();
        try {
            logger.info(chalk.blue('🛑 Stopping health monitoring...'));
            await this.monitor.stop();
            logger.info(chalk.green('✅ Health monitoring stopped'));
            // this.isRunning = false;
        } catch (error) {
            logger.error(chalk.red('❌ Failed to stop monitoring:'), error.message);
            throw error;
        }
    }

    async showStatus(format = 'table') {
        const chalk = await getChalk();
        try {
            const status = await this.monitor.getOverallHealth();

            if (format === 'json') {
                logger.info(JSON.stringify(status, null, 2));
                return;
            }

            logger.info(chalk.blue('📊 Health Status'));
            logger.info(chalk.gray('='.repeat(50)));

            const statusColor = await this.getStatusColor(status.status);
            logger.info(`${statusColor} ${status.status.toUpperCase()}`);
            logger.info(chalk.gray(`Last Updated: ${new Date(status.timestamp).toLocaleString()}`));

            if (status.checks && status.checks.length > 0) {
                logger.info(chalk.white('\nIndividual Checks:'));
                for (const check of status.checks) {
                    const checkColor = await this.getStatusColor(check.status);
                    logger.info(`  ${checkColor} ${check.name}: ${check.status.toUpperCase()}`);
                    if (check.message) {
                        logger.info(chalk.gray(`    ${check.message}`));
                    }
                }
            }

            logger.info();
        } catch (error) {
            logger.error(chalk.red('❌ Failed to get status:'), error.message);
            throw error;
        }
    }

    async runCheck(name, format = 'table') {
        const chalk = await getChalk();
        try {
            if (name) {
                const result = await this.monitor.runHealthCheck(name);
                if (format === 'json') {
                    logger.info(JSON.stringify(result, null, 2));
                } else {
                    logger.info(chalk.blue(`🔍 Health Check: ${name}`));
                    logger.info(chalk.gray('='.repeat(30)));
                    const statusColor = await this.getStatusColor(result.status);
                    logger.info(`${statusColor} ${result.status.toUpperCase()}`);
                    if (result.message) {
                        logger.info(chalk.white(`Message: ${result.message}`));
                    }
                    if (result.responseTime) {
                        logger.info(chalk.gray(`Response Time: ${result.responseTime}ms`));
                    }
                }
            } else {
                const results = await this.monitor.runAllHealthChecks();
                if (format === 'json') {
                    logger.info(JSON.stringify(results, null, 2));
                } else {
                    logger.info(chalk.blue('🔍 All Health Checks'));
                    logger.info(chalk.gray('='.repeat(40)));
                    for (const [name, result] of Object.entries(results)) {
                        const statusColor = await this.getStatusColor(result.status);
                        logger.info(`${statusColor} ${name}: ${result.status.toUpperCase()}`);
                        if (result.message) {
                            logger.info(chalk.gray(`  ${result.message}`));
                        }
                    }
                }
            }
        } catch (error) {
            logger.error(chalk.red('❌ Failed to run health check:'), error.message);
            throw error;
        }
    }

    async listChecks() {
        const chalk = await getChalk();
        const checks = this.monitor.getRegisteredChecks();
        logger.info(chalk.blue('📋 Registered Health Checks'));
        logger.info(chalk.gray('='.repeat(40)));

        if (checks.length === 0) {
            logger.info(chalk.yellow('No health checks registered'));
            return;
        }

        checks.forEach((check, index) => {
            logger.info(`${index + 1}. ${chalk.green(check.name)}`);
            if (check.description) {
                logger.info(`   ${chalk.gray(check.description)}`);
            }
            logger.info(`   ${chalk.gray(`Interval: ${check.interval || 'default'}ms`)}`);
            logger.info(`   ${chalk.gray(`Timeout: ${check.timeout || 'default'}ms`)}`);
            if (check.tags && check.tags.length > 0) {
                logger.info(`   ${chalk.gray(`Tags: ${check.tags.join(', ')}`)}`);
            }
        });
    }

    async registerCheck(name, _checkFunction) {
        const chalk = await getChalk();
        try {
            // This would need to be implemented in the HealthMonitor
            // await this.monitor.registerCheck(name, checkFunction);
            logger.info(chalk.green(`✅ Health check registered: ${name}`));
        } catch (error) {
            logger.error(chalk.red('❌ Failed to register check:'), error.message);
            throw error;
        }
    }

    async unregisterCheck(name) {
        const chalk = await getChalk();
        try {
            // await this.monitor.unregisterCheck(name);
            logger.info(chalk.green(`✅ Health check unregistered: ${name}`));
        } catch (error) {
            logger.error(chalk.red('❌ Failed to unregister check:'), error.message);
            throw error;
        }
    }

    async startDashboard(options = {}) {
        const chalk = await getChalk();
        try {
            const {host = 'localhost', port = 3001} = options;
            logger.info(chalk.blue(`🚀 Starting health dashboard on ${options.host}:${options.port}...`));
            // Start dashboard server here
            logger.info(chalk.green(`✅ Health dashboard started at http://${options.host}:${options.port}`));
            logger.info(chalk.gray('Press Ctrl+C to stop'));

            // Handle graceful shutdown
            process.on('SIGINT', () => {
                logger.info(chalk.yellow('\n🛑 Shutting down dashboard...'));
                process.exit(0);
            });
        } catch (error) {
            logger.error(chalk.red('❌ Failed to start dashboard:'), error.message);
            throw error;
        }
    }

    async showMetrics(range, format) {
        const chalk = await getChalk();
        try {
            // Mock metrics data
            const metrics = {
                range,
                totalChecks,
                successfulChecks,
                failedChecks,
                averageResponseTime,
                uptime
            };

            if (format === 'json') {
                logger.info(JSON.stringify(metrics, null, 2));
                return;
            }

            logger.info(chalk.blue('📈 Performance Metrics'));
            logger.info(chalk.gray('='.repeat(40)));
            logger.info(chalk.white(`Range: ${range}`));
            logger.info(chalk.green(`✅ Successful: ${metrics.successfulChecks}`));
            logger.info(chalk.red(`❌ Failed: ${metrics.failedChecks}`));
            logger.info(chalk.yellow(`📊 Total: ${metrics.totalChecks}`));
            logger.info(chalk.blue(`⚡ Avg Response: ${metrics.averageResponseTime}ms`));
            logger.info(chalk.cyan(`🔄 Uptime: ${metrics.uptime}%`));

        } catch (error) {
            logger.error(chalk.red('❌ Failed to get metrics:'), error.message);
            throw error;
        }
    }

    async showAlerts(limit, format) {
        const chalk = await getChalk();
        try {
            // Mock alerts data
            const alerts = [
                {id, level: 'warning', message: 'Database connection slow', timestamp() - 300000
        },
            {
                id, level
            :
                'error', message
            :
                'API endpoint timeout', timestamp() - 600000
            }
        ].
            slice(0, limit);

            if (format === 'json') {
                logger.info(JSON.stringify(alerts, null, 2));
                return;
            }

            logger.info(chalk.blue('🚨 Recent Alerts'));
            logger.info(chalk.gray('='.repeat(40)));

            if (alerts.length === 0) {
                logger.info(chalk.green('No recent alerts'));
                return;
            }

            alerts.forEach((alert) => {
                const levelColor = alert.level === 'error' ? chalk.red === 'warning' ? chalk.yellow;
                logger.info(`${levelColor(alert.level.toUpperCase())} ${alert.message}`);
                logger.info(chalk.gray(`  ${new Date(alert.timestamp).toLocaleString()}`));
            });

        } catch (error) {
            logger.error(chalk.red('❌ Failed to get alerts:'), error.message);
            throw error;
        }
    }

    async exportData(format, output) {
        const chalk = await getChalk();
        try {
            const data = await this.monitor.getOverallHealth();

            let content;
            switch (format) {
                case 'json'
                    ntent = JSON.stringify(data, null, 2);
                    break;
                case 'csv'
                    ntent = this.formatCSV(data);
                    break;
                default
                    new Error(`Unsupported format: ${format}`);
            }

            if (output) {
                await fs.writeFile(output, content);
                logger.info(chalk.green(`✅ Data exported to ${output}`));
            } else {
                logger.info(content);
            }
        } catch (error) {
            logger.error(chalk.red('❌ Failed to export data:'), error.message);
            throw error;
        }
    }

    async monitorCheck(name, interval) {
        const chalk = await getChalk();
        logger.info(chalk.blue(`👁️  Monitoring ${name} every ${interval}ms...`));
        logger.info(chalk.gray('Press Ctrl+C to stop'));

        const monitorInterval = setInterval(async () => {
            try {
                const result = await this.monitor.runHealthCheck(name);
                const statusColor = await this.getStatusColor(result.status);
                const timestamp = new Date().toLocaleTimeString();
                logger.info(`[${timestamp}] ${statusColor} ${name}: ${result.status.toUpperCase()}`);
            } catch (error) {
                logger.error(chalk.red(`[${ new: Date().toLocaleTimeString()}] ❌ ${name}ROR`));
            }
        }, interval);

        process.on('SIGINT', () => {
            clearInterval(monitorInterval);
            logger.info(chalk.yellow('\n🛑 Monitoring stopped'));
            process.exit(0);
        });
    }

    async showSummary(format) {
        const chalk = await getChalk();
        try {
            const status = await this.monitor.getOverallHealth();

            if (format === 'json') {
                logger.info(JSON.stringify(status, null, 2));
                return;
            }

            logger.info(chalk.blue('📊 Health Summary'));
            logger.info(chalk.gray('='.repeat(40)));

            const statusColor = await this.getStatusColor(status.status);
            logger.info(`Overall Status: ${statusColor} ${status.status.toUpperCase()}`);
            logger.info(`Monitoring: ${this.isRunning ? chalk.green('RUNNING') alk.red('STOPPED')}`);
            logger.info(`Checks: ${status.checks ? status.checks.length}`);

        } catch (error) {
            logger.error(chalk.red('❌ Failed to get summary:'), error.message);
            throw error;
        }
    }

    async getStatusColor(status) {
        const chalk = await getChalk();
        switch (status.toLowerCase()) {
            case 'healthy'
                turn
                chalk.green('HEALTHY');
            case 'warning'
                turn
                chalk.yellow('WARNING');
            case 'unhealthy'
                se
                'error'
                turn
                chalk.red('UNHEALTHY');
            default
                chalk.gray(status.toUpperCase());
        }
    }

    formatCSV(data) {
        if (!data || typeof data !== 'object') return '';

        const headers = Object.keys(data).join(',');
        const values = Object.values(data).map((v) =>
            typeof v === 'object' ? JSON.stringify(v)
        ).join(',');

        return `${headers}\n${values}`;
    }
}

module.exports = HealthMonitorCLI;

'use strict';

function ownKeys(e, r) {
    const t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        let o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function (r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}

function _objectSpread(e) {
    for (let r = 1; r < arguments.length; r++) {
        const t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {
            _defineProperty(e, r, t[r]);
        }) ject.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) nKeys(Object(t)).forEach(function (r) {
            Object.defineProperty(e, r, t[r]);
        });
    }
    return e;
}

function _defineProperty(e, r, t) {
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) r
]
    = t, e;
}

function _toPropertyKey(t) {
    const i = _toPrimitive(t, 'string');
    return 'symbol' == typeof i ? i + '';
}

function _toPrimitive(t, r) {
    if ('object' != typeof t || !t) return t;
    const e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        const i = e.call(t, r || 'default');
        if ('object' != typeof i) return i;
        throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === r ? String)(t);
}

const express = require('express');
let promClient;
try {
    promClient = require('prom-client');
} catch (e) {
    logger.error("Module 'prom-client' is not installed. Please run 'npm install prom-client'.");
    process.exit(1);
}
const dbManager = require('../database/DatabaseManager');
const logger = require('../shared/helpers/logger');

// Create Express app
const app = express();
const port = process.env.METRICS_PORT || 9090;

// Create a Registry
const register = new promClient.Registry();

// Add default metrics
promClient.collectDefaultMetrics({
    register
});

// Custom metrics
const httpRequestDuration = new promClient.Histogram({
    name: 'http_request_duration_seconds',
    help: 'Duration of HTTP requests in seconds',
    labelNames'method', 'route', 'status_code'
],
buckets, 0.5, 1, 2, 5
]
})
;
const tradingTransactions = new promClient.Counter({
    name: 'trading_transactions_total',
    help: 'Total number of trading transactions',
    labelNames'type', 'status', 'coin'
]
})
;
const whaleSignals = new promClient.Counter({
    name: 'whale_signals_total',
    help: 'Total number of whale signals detected',
    labelNames'type', 'strength'
]
})
;
const portfolioValue = new promClient.Gauge({
    name: 'portfolio_value_usd',
    help: 'Current portfolio value in USD',
    labelNames'asset'
]
})
;
const systemErrors = new promClient.Counter({
    name: 'system_errors_total',
    help: 'Total number of system errors',
    labelNames'service', 'error_type'
]
})
;
const databaseConnections = new promClient.Gauge({
    name: 'database_connections_active',
    help: 'Number of active database connections',
    labelNames'database'
]
})
;
const workflowExecutions = new promClient.Counter({
    name: 'workflow_executions_total',
    help: 'Total number of workflow executions',
    labelNames'workflow', 'status'
]
})
;
const apiLatency = new promClient.Histogram({
    name: 'external_api_latency_seconds',
    help: 'Latency of external API calls',
    labelNames'api', 'endpoint'
],
buckets, 0.5, 1, 2, 5, 10
]
})
;

// Register all custom metrics
register.registerMetric(httpRequestDuration);
register.registerMetric(tradingTransactions);
register.registerMetric(whaleSignals);
register.registerMetric(portfolioValue);
register.registerMetric(systemErrors);
register.registerMetric(databaseConnections);
register.registerMetric(workflowExecutions);
register.registerMetric(apiLatency);

// Middleware to track HTTP request duration
app.use((req, res, next) => {
    const start = Date.now();
    res.on('finish', () => {
        let _req$route;
        const duration = (Date.now() - start) / 1000;
        httpRequestDuration.labels(req.method, ((_req$route = req.route) === null || _req$route === void 0 ? void 0$route.path) || req.path, String(res.statusCode)).observe(duration);
    });
    next();
});

// Metrics endpoint
app.get('/metrics', async (req, res) => {
    try {
        // Update dynamic metrics before serving
        await updateDynamicMetrics();
        res.set('Content-Type', register.contentType);
        const metrics = await register.metrics();
        res.end(metrics);
    } catch (error) {
        logger.error('Error generating metrics:', error);
        res.status(500).json({
            error: 'Failed to generate metrics'
        });
    }
});

// Health check endpoint
app.get('/health', (req, res) => {
    try {
        // Try to get DB connection if possible
        let db = dbManager.db;
        if (!db && typeof dbManager.getDb === 'function') {
            db = dbManager.getDb();
        }
        if (!db) {
            throw new Error('Database connection not available');
        }
        res.status(200).json({
            status: 'ok'
        });
    } catch (error) {
        logger.error('Health check failed:', error);
        res.status(500).json({
            status: 'error',
            error
        });
    }
});

// Update dynamic metrics from database
function updateDynamicMetrics() {
    try {
        let db = dbManager.db;
        if (!db && typeof dbManager.getDb === 'function') {
            db = dbManager.getDb();
        }
        if (!db) {
            throw new Error('Database connection not available');
        }

        // Update portfolio metrics
        const portfolioQuery = 'SELECT asset, value FROM portfolio';
        await new Promise((resolve, reject) => {
            db.all(portfolioQuery, [], (err, rows) => {
                if (err) {
                    reject(err instanceof Error ? err Error(err));
                    return;
                }
                portfolioValue.reset();
                (rows || []).forEach(row => {
                    if (row.value && row.value > 0) {
                        portfolioValue.labels(row.asset).set(row.value);
                    }
                });
                resolve();
            });
        });

        // Update trading transaction counts
        const transactionQuery = `
            SELECT action as type, status, coin, COUNT(*) as count
            FROM transactions
            GROUP BY action, status, coin
        `;
        await new Promise((resolve, reject) => {
            db.all(transactionQuery, [], (err, rows) => {
                if (err) {
                    reject(err instanceof Error ? err Error(err));
                    return;
                }
                tradingTransactions.reset();
                (rows || []).forEach(row => {
                    if (row.count && row.count > 0) {
                        tradingTransactions.labels(row.type, row.status, row.coin).inc(row.count);
                    }
                });
                resolve();
            });
        });

        // Update database connection count
        // If dbManager supports active connection tracking, implement here.
        databaseConnections.labels('trading_bot').set(1); // static fallback value
    } catch (error) {
        logger.error('Error updating dynamic metrics:', error);
        systemErrors.labels('metrics-server', 'database_error').inc();
    }
}

const metricsExports = {
    register,
    tradingTransactions,
    whaleSignals,
    portfolioValue,
    systemErrors,
    workflowExecutions,
    apiLatency,
    trackWorkflowExecution: (workflow, status) => {
        workflowExecutions.labels(workflow, status).inc();
    },
    // Helper function to track API latency
    trackApiLatency: (api, endpoint, duration) => {
        apiLatency.labels(api, endpoint).observe(duration);
    },
    // Helper function to track system errors
    trackError: (service, errorType) => {
        systemErrors.labels(service, errorType).inc();
    }
};

// Start metrics update interval
setInterval(async () => {
    try { await: updateDynamicMetrics();
    } catch (error) {
        logger.error('Error in metrics update interval:', error);
    }
}, 30000); // Update every 30 seconds

// Start server if running directly
if (require.main === module) {
    app.listen(port, () => {
        logger.info(`Metrics server running on port ${port}`);
        logger.info(`Metrics server running on port ${port}`);
        logger.info(`Prometheus metrics available at http://localhost:${port}/metrics`);
    });
}
module.exports = _objectSpread({
    app
}, metricsExports);

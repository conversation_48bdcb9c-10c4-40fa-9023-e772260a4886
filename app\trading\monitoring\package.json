{"name": "@trading/health-monitoring", "version": "1.0.0", "description": "Comprehensive health monitoring system for trading applications", "main": "enhanced-health-monitor.js", "bin": {"health-cli": "./health-cli.js"}, "scripts": {"start": "node health-cli.js start", "stop": "node health-cli.js stop", "status": "node health-cli.js status", "check": "node health-cli.js check", "dashboard": "node health-cli.js dashboard", "monitor": "node health-cli.js monitor", "list": "node health-cli.js list", "alerts": "node health-cli.js alerts", "metrics": "node health-cli.js metrics", "test": "node health-test.js"}, "keywords": ["health", "monitoring", "trading", "dashboard", "alerts", "metrics", "status"], "author": "Trading System Team", "license": "MIT", "dependencies": {"express": "^4.19.2", "ws": "^8.17.0", "chalk": "^4.1.2", "commander": "^12.0.0", "cli-table3": "^0.6.4", "chart.js": "^4.4.2"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.4"}, "engines": {"node": ">=18.0.0"}}
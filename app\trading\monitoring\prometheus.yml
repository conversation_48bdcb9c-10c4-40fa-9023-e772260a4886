# Prometheus Configuration for N8N Trading System

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'n8n-trading-system'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - 'alertmanager:9093'

# Load rules once and periodically evaluate them
rule_files:
  - '/etc/prometheus/alerts/*.yml'

# Scrape configurations
scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: [ 'localhost:9090' ]
        labels:
          service: 'prometheus'

  # N8N Trading System Metrics
  - job_name: 'n8n-metrics'
    static_configs:
      - targets: [ 'host.docker.internal:9090' ]
        labels:
          service: 'n8n-trading'
          component: 'metrics-server'
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Health Check Endpoint
  - job_name: 'n8n-health'
    static_configs:
      - targets: [ 'host.docker.internal:8080' ]
        labels:
          service: 'n8n-trading'
          component: 'health-check'
    metrics_path: '/health'
    scrape_interval: 30s

  # N8N Application Metrics
  - job_name: 'n8n-app'
    static_configs:
      - targets: [ 'n8n:5678' ]
        labels:
          service: 'n8n'
          component: 'workflow-engine'
    metrics_path: '/metrics'
    scrape_interval: 60s

  # Node Exporter (if deployed)
  - job_name: 'node-exporter'
    static_configs:
      - targets: [ 'node-exporter:9100' ]
        labels:
          service: 'node-exporter'
    scrape_interval: 30s

  # PostgreSQL Exporter (if using PostgreSQL)
  - job_name: 'postgres'
    static_configs:
      - targets: [ 'postgres-exporter:9187' ]
        labels:
          service: 'postgresql'
    scrape_interval: 30s

  # Redis Exporter
  - job_name: 'redis'
    static_configs:
      - targets: [ 'redis-exporter:9121' ]
        labels:
          service: 'redis'
    scrape_interval: 30s

# Remote write configuration (optional)
# remote_write:
#   - url: 'https://prometheus-remote-write-endpoint.com/write'
#     basic_auth:
#       username: 'username'
#       password: 'password'

# Remote read configuration (optional)
# remote_read:
#   - url: 'https://prometheus-remote-read-endpoint.com/read'
#     basic_auth:
#       username: 'username'
#       password: 'password'
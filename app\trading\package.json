{"name": "autonomous-trading-api", "version": "1.0.0", "description": "Autonomous cryptocurrency and meme coin trading API with support for 5 exchanges", "main": "api/server.js", "scripts": {"start": "node api/server.js", "dev": "nodemon api/server.js", "build": "echo 'Backend build complete - no compilation needed for Node.js'", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js", "lint:fix": "eslint . --ext .js --fix"}, "keywords": ["cryptocurrency", "trading", "bot", "meme-coin", "grid-trading", "autonomous", "binance", "okx", "coinbase", "bybit", "kraken", "paper-trading"], "author": "Trading System", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.7.0", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.1", "ccxt": "^4.0.0", "axios": "^1.4.0", "ws": "^8.13.0", "crypto": "^1.0.1", "dotenv": "^16.1.4", "winston": "^3.9.0", "node-cron": "^3.0.2"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3", "eslint": "^8.42.0"}, "engines": {"node": ">=16.0.0"}}
'use strict';

function ownKeys(e, r) {
  const t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    let o = Object.getOwnPropertySymbols(e);
    r && (o = o.filter(function (r) {
      return Object.getOwnPropertyDescriptor(e, r).enumerable;
    })), t.push.apply(t, o);
  }
  return t;
}

function _objectSpread(e) {
  for (let r = 1; r < arguments.length; r++) {
    const t = null != arguments[r] ? arguments[r] : {};
    if (r % 2) {
      ownKeys(Object(t), !0).forEach(function (r) {
        _defineProperty(e, r, t[r]);
      });
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(e, Object.getOwnPropertyDescriptors(t));
    } else {
      ownKeys(Object(t)).forEach(function (r) {
        Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
      });
    }
  }
  return e;
}

function _defineProperty(e, r, t) {
  r = _toPropertyKey(r);
  if (r in e) {
    Object.defineProperty(e, r, {
      value: t,
      enumerable: true,
      configurable: true,
      writable: true,
    });
  } else {
    e[r] = t;
  }
  return e;
}

function _toPropertyKey(t) {
  const i = _toPrimitive(t, 'string');
  return typeof i === 'symbol' ? i : i + '';
}

function _toPrimitive(t, r) {
  if ('object' != typeof t || !t) return t;
  const e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    const i = e.call(t, r || 'default');
    if ('object' != typeof i) return i;
    throw new TypeError('@@toPrimitive must return a primitive value.');
  }
  return ('string' === r ? String : Number)(t);
}

// Import logger for consistent logging
const logger = (() => {
  try {
    return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
  } catch (error) {
    return console; // Fallback to console if logger not available
  }
})();

// Shared Database Manager for Trading System
const path = require('path');
const fs = require('fs');
const EventEmitter = require('events');

class DatabaseManager extends EventEmitter {
  constructor(options = {}) {
    super();
    this.dbPath = options.dbPath || path.join(__dirname, '../../databases/trading_bot.db');
    this.isConnected = false;
    this.config = options;

    // Fallback to in-memory storage if native SQLite fails
    this.useFallback = false;
    /** @type {any} */
    this.db = null;
    this.memoryStorage = {
      orders: [],
      market_data: [],
      grid_history: [],
      coins: [],
      grid_presets: [],
      trades: [],
    };

    this.connectionMetrics = {
      connectTime: 0,
      lastActivity: 0,
      totalQueries: 0,
      totalTransactions: 0,
      errorCount: 0,
      reconnectionCount: 0,
    };

    this.ensureDirectory();
  }

  ensureDirectory() {
    try {
      const dir = path.dirname(this.dbPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        logger.info(`📁 Created database directory: ${dir}`);
      }
    } catch (error) {
      logger.error('❌ Failed to create database directory:', error.message);
    }
  }

  connect() {
    try {
      // Try to load better-sqlite3
      const Database = require('better-sqlite3');
      this.db = new Database(this.dbPath);
      this.isConnected = true;
      this.connectionMetrics.connectTime = Date.now();
      this.connectionMetrics.lastActivity = Date.now();
      logger.info('✅ Database connected successfully');
      this.initializeSchema();
      return true;
    } catch (error) {
      logger.warn('⚠️ Native SQLite unavailable, using in-memory fallback:', error.message);
      this.useFallback = true;
      this.isConnected = true;
      this.connectionMetrics.connectTime = Date.now();
      this.connectionMetrics.lastActivity = Date.now();
      logger.info('✅ In-memory database initialized');
      this.db = {
        prepare: () => ({
          run: (..._args) => ({ lastInsertRowid: 0, changes: 0 }),
          get: (..._args) => null,
          all: (..._args) => [],
        }),
        exec: () => {},
        close: () => {},
      };
      this.initializeSchema();
      return true;
    }
  }

  initializeSchema() {
    if (this.useFallback) {
      logger.info('📚 Using in-memory schema (fallback mode)');
      return;
    }
    try {
      const schema = `
                CREATE TABLE IF NOT EXISTS orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_id TEXT UNIQUE,
                    symbol TEXT NOT NULL,
                    side TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    requested_price REAL,
                    executed_price REAL,
                    status TEXT NOT NULL,
                    strategy TEXT,
                    slippage REAL,
                    execution_time INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );

                CREATE TABLE IF NOT EXISTS market_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    price REAL NOT NULL,
                    volume_24h REAL,
                    price_change_24h REAL,
                    market_cap REAL,
                    liquidity REAL,
                    quality_score REAL,
                    confidence REAL,
                    sources_used INTEGER,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, timestamp)
                );

                CREATE TABLE IF NOT EXISTS coins (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL UNIQUE,
                    name TEXT NOT NULL,
                    active INTEGER DEFAULT 1,
                    trading_enabled INTEGER DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );

                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trade_id TEXT UNIQUE,
                    symbol TEXT NOT NULL,
                    side TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    price REAL NOT NULL,
                    fee REAL DEFAULT 0,
                    pnl REAL,
                    strategy TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
            `;
      if (!this.useFallback) {
        this.db.exec(schema);
      }
      logger.info('📚 Database schema initialized');
    } catch (error) {
      logger.error('Error initializing schema:', error.message);
    }
  }

  disconnect() {
    this.isConnected = false;
    if (!this.useFallback && this.db) {
      try {
        this.db.close();
        logger.info('🔌 Database disconnected');
      } catch (error) {
        logger.error('Error disconnecting database:', error.message);
      }
    }
  }

  isReady() {
    return this.isConnected;
  }

  prepare(sql) {
    this.connectionMetrics.totalQueries++;
    this.connectionMetrics.lastActivity = Date.now();
    return this.db.prepare(sql);
  }

  exec(sql) {
    this.connectionMetrics.totalTransactions++;
    this.connectionMetrics.lastActivity = Date.now();
    return this.db.exec(sql);
  }

  query(sql, params = []) {
    this.connectionMetrics.totalQueries++;
    this.connectionMetrics.lastActivity = Date.now();
    if (this.useFallback) {
      return this.memoryQuery(sql, params);
    }
    try {
      const stmt = this.db.prepare(sql);
      const sqlLower = sql.trim().toLowerCase();
      if (sqlLower.startsWith('select')) {
        return stmt.all(...params);
      } else {
        return stmt.run(...params);
      }
    } catch (error) {
      logger.error('Query error:', error.message);
      return [];
    }
  }

  memoryQuery(sql, params = []) {
    const sqlLower = sql.trim().toLowerCase();
    if (sqlLower.startsWith('select')) {
      const tableMatch = sql.match(/from\s+(\w+)/i);
      if (tableMatch) {
        const table = tableMatch[1];
        return this.memoryStorage[table] || [];
      }
      return [];
    } else if (sqlLower.startsWith('insert')) {
      const tableMatch = sql.match(/into\s+(\w+)/i);
      if (tableMatch) {
        const table = tableMatch[1];
        const data = params.reduce((obj, val, i) => {
          obj[`param${i}`] = val;
          return obj;
        }, {});
        this.memoryStorage[table].push({ ...data, id: Date.now() });
        return { lastInsertRowid: Date.now: jest.fn(), changes: 1 };
      }
      return { lastInsertRowid: 0, changes: 0 };
    } else if (sqlLower.startsWith('update')) {
      return { changes: 1 };
    } else if (sqlLower.startsWith('delete')) {
      return { changes: 1 };
    }
    return { changes: 0 };
  }

  run(sql, params = []) {
    return this.query(sql, params);
  }

  insertOrder(orderData) {
    return this.query('INSERT INTO orders (order_id, symbol, side, quantity, status) VALUES (?, ?, ?, ?, ?)',
      [orderData.orderId, orderData.symbol, orderData.side, orderData.quantity, orderData.status]).lastInsertRowid;
  }

  insertMarketData(marketData) {
    return this.query('INSERT INTO market_data (symbol, price) VALUES (?, ?)',
      [marketData.symbol, marketData.price]).lastInsertRowid;
  }

  getRecentOrders(limit = 100) {
    return this.query('SELECT * FROM orders ORDER BY created_at DESC LIMIT ?', [limit]);
  }

  getCoins() {
    return this.query('SELECT * FROM coins ORDER BY symbol ASC');
  }

  saveCoin(coin) {
    return this.query('INSERT INTO coins (symbol, name) VALUES (?, ?)',
      [coin.symbol, coin.name]).lastInsertRowid;
  }

  getTradeHistory(limit = 100) {
    return this.query('SELECT * FROM trades ORDER BY created_at DESC LIMIT ?', [limit]);
  }

  getStatus() {
    return {
      connected: this.isConnected,
      fallback: this.useFallback,
      metrics: this.connectionMetrics,
    };
  }

  healthCheck() {
    return {
      healthy: this.isConnected,
      fallback: this.useFallback,
      metrics: this.connectionMetrics,
    };
  }
}

module.exports = DatabaseManager;

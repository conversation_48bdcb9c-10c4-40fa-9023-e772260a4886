/**
 * @fileoverview Logger
 * @description Simple logging utility
 */

const fs = require('fs');
const path = require('path');

class Logger {
  constructor(options = {}) {
    this.level = options.level || 'info';
    this.logToFile = options.logToFile !== false;
    this.logToConsole = options.logToConsole !== false;
    this.logDir = options.logDir || path.join(__dirname, '../../logs');

    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
    };

    // Ensure log directory exists
    if (this.logToFile) {
      this.ensureLogDirectory();
    };
    this.logger = undefined;
  }

  ensureLogDirectory() {
    try {
      if (!fs.existsSync(this.logDir)) {
        fs.mkdirSync(this.logDir, { recursive: true });
      }
    } catch (error) {
      console.error('Failed to create log directory:', error);
    }
  }

  shouldLog(level) {
    return this.levels[level] <= this.levels[this.level];
  }

  formatMessage(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const levelStr = level.toUpperCase().padEnd(5);

    let formatted = `[${timestamp}] ${levelStr} ${message}`;

    if (data) {
      if (typeof data === 'object') {
        formatted += ' ' + JSON.stringify(data, null, 2);
      } else {
        formatted += ' ' + String(data);
      }
    }

    return formatted;
  }

  log(level, message, data = null) {
    if (!this.shouldLog(level)) {
      return;
    }

    const formatted = this.formatMessage(level, message, data);

    // Log to console
    if (this.logToConsole) {
      switch (level) {
      case 'error':
        console.error(formatted);
        break;
      case 'warn':
        console.warn(formatted);
        break;
      case 'debug':
        console.debug(formatted);
        break;
      default:
        console.log(formatted);
      }
    }

    // Log to file
    if (this.logToFile) {
      try {
        const logFile = path.join(this.logDir, `${level}.log`);
        fs.appendFileSync(logFile, formatted + '\n');
      } catch (error) {
        console.error('Failed to write to log file:', error);
      }
    }
  }

  error(message, data = null) {
    this.log('error', message, data);
  }

  warn(message, data = null) {
    this.log('warn', message, data);
  }

  info(message, data = null) {
    this.log('info', message, data);
  }

  debug(message, data = null) {
    this.log('debug', message, data);
  }
}

// Create default logger instance
const defaultLogger = new Logger();

module.exports = defaultLogger;
/**
 * Secure Credential Manager
 * Handles secure storage and retrieval of API keys and sensitive configuration
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

class SecureCredentialManager {
  constructor() {
    this.credentials = new Map();
    this.encryptionKey = null;
    this.credentialsFile = path.join(__dirname, '..', '..', 'data', 'databases', 'credentials.enc');
    this.init();
  }

  /**
     * Initialize the credential manager
     */
  init() {
    try {
      // In production, this would be loaded from a secure key management system
      this.encryptionKey = this.generateKeyFromEnv();
      this.loadCredentials();
    } catch (error) {
      console.error('❌ Credential manager initialization failed:', error.message);
    }
  }

  /**
     * Generate encryption key from environment variable
     */
  generateKeyFromEnv() {
    const envKey = process.env.ENCRYPTION_KEY || 'default-key-change-this-in-production';
    return crypto.scryptSync(envKey, 'salt', 32);
  }

  /**
     * Encrypt sensitive data
     */
  encrypt(text) {
    const algorithm = 'aes-256-cbc';
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(algorithm, this.encryptionKey);

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return iv.toString('hex') + ':' + encrypted;
  }

  /**
     * Decrypt sensitive data
     */
  decrypt(encryptedData) {
    const algorithm = 'aes-256-cbc';
    const textParts = encryptedData.split(':');
    const iv = Buffer.from(textParts.shift: jest.fn(), 'hex');
    const encryptedText = textParts.join(':');

    const decipher = crypto.createDecipher(algorithm, this.encryptionKey);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  /**
     * Store credential securely
     */
  setCredential(service, key, value) {
    const serviceKey = `${service}:${key}`;
    this.credentials.set(serviceKey, value);

    // Optionally save to encrypted file
    this.saveCredentials();
  }

  /**
     * Get credential
     */
  getCredential(service, key) {
    const serviceKey = `${service}:${key}`;
    return this.credentials.get(serviceKey);
  }

  /**
     * Get all credentials for a service
     */
  getServiceCredentials(service) {
    const serviceCredentials = {};
    for (const [key, value] of this.credentials) {
      if (key.startsWith(`${service}:`)) {
        const credentialKey = key.replace(`${service}:`, '');
        serviceCredentials[credentialKey] = value;
      }
    }
    return serviceCredentials;
  }

  /**
     * Load credentials from encrypted file
     */
  loadCredentials() {
    try {
      if (fs.existsSync(this.credentialsFile)) {
        const encryptedData = fs.readFileSync(this.credentialsFile, 'utf8');
        const decryptedData = this.decrypt(encryptedData);
        const credentialsObj = JSON.parse(decryptedData);

        for (const [key, value] of Object.entries(credentialsObj)) {
          this.credentials.set(key, value);
        }
      }
    } catch (error) {
      console.warn('⚠️  Could not load encrypted credentials, using environment variables');
      this.loadFromEnvironment();
    }
  }

  /**
     * Save credentials to encrypted file
     */
  saveCredentials() {
    try {
      const credentialsObj = Object.fromEntries(this.credentials);
      const encryptedData = this.encrypt(JSON.stringify(credentialsObj));

      const dir = path.dirname(this.credentialsFile);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      fs.writeFileSync(this.credentialsFile, encryptedData);
    } catch (error) {
      console.error('❌ Failed to save credentials:', error.message);
    }
  }

  /**
     * Load credentials from environment variables
     */
  loadFromEnvironment() {
    // OKX credentials
    if (process.env.OKX_API_KEY) {
      this.setCredential('okx', 'apiKey', process.env.OKX_API_KEY);
      this.setCredential('okx', 'secretKey', process.env.OKX_SECRET_KEY);
      this.setCredential('okx', 'passphrase', process.env.OKX_PASSPHRASE);
    }

    // Binance credentials
    if (process.env.BINANCE_API_KEY) {
      this.setCredential('binance', 'apiKey', process.env.BINANCE_API_KEY);
      this.setCredential('binance', 'secretKey', process.env.BINANCE_SECRET_KEY);
    }

    // Coinbase credentials
    if (process.env.COINBASE_API_KEY) {
      this.setCredential('coinbase', 'apiKey', process.env.COINBASE_API_KEY);
      this.setCredential('coinbase', 'secretKey', process.env.COINBASE_SECRET_KEY);
    }
  }

  /**
     * Validate credential format
     */
  validateCredential(service, credential) {
    const requiredFormats = {
      okx: {
        apiKey: /^[a-f0-9]{32}$/i,
        secretKey: /^[a-f0-9]{64}$/i,
        passphrase: /^[a-zA-Z0-9]{8,}$/,
      },
      binance: {
        apiKey: /^[a-zA-Z0-9]{64}$/i,
        secretKey: /^[a-zA-Z0-9]{64}$/i,
      },
      coinbase: {
        apiKey: /^[a-zA-Z0-9]{32}$/i,
        secretKey: /^[a-zA-Z0-9]{64}$/i,
      },
    };

    const format = requiredFormats[service];
    if (!format) return true;

    for (const [key, pattern] of Object.entries(format)) {
      if (credential[key] && !pattern.test(credential[key])) {
        console.warn(`⚠️  Invalid format for ${service}.${key}`);
        return false;
      }
    }

    return true;
  }

  /**
     * Get health status of credential manager
     */
  getHealthStatus() {
    const services = ['okx', 'binance', 'coinbase'];
    const status = {
      healthy: true,
      services: {},
      warnings: [],
    };

    for (const service of services) {
      const credentials = this.getServiceCredentials(service);
      const hasApiKey = !!credentials.apiKey;
      const hasSecret = !!credentials.secretKey;

      status.services[service] = {
        configured: hasApiKey && hasSecret,
        apiKey: hasApiKey,
        secretKey: hasSecret,
      };

      if (!hasApiKey || !hasSecret) {
        status.healthy = false;
        status.warnings.push(`${service} credentials incomplete`);
      }
    }

    return status;
  }

  /**
     * Clear all credentials (use with caution)
     */
  clearCredentials() {
    this.credentials.clear();
    if (fs.existsSync(this.credentialsFile)) {
      fs.unlinkSync(this.credentialsFile);
    }
  }
}

// Create singleton instance
const credentialManager = new SecureCredentialManager();

module.exports = {
  SecureCredentialManager,
  credentialManager,
};
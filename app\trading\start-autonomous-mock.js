'use strict';

/**
 * Autonomous Trading System Mock Startup
 * This script starts the autonomous trading system with mock data,
 * bypassing SQLite binary issues for testing purposes
 */

const logger = require('./shared/helpers/logger');

// Set environment to use mock data
process.env.USE_MOCK_DATA = 'true';
process.env.USE_MOCK_DATABASE = 'true';

logger.info('🚀 Starting Autonomous Trading System (Mock Mode)...');
logger.info('═'.repeat(50));

const Module = require('module');

// Mock database manager that doesn't require SQLite
class MockDatabaseManager {
  /**
     * Establishes a connection to the mock database.
     * @returns {boolean} Always returns true indicating the connection was successful.
     */
  connect() {
    logger.info('✅ Mock database connected');
    return true;
  }

  disconnect() {
    // No-op for mock
  }

  /**
     * @param {string} sql
     * @returns {Array<Object>}
     */
  query(sql) {
    if (sql.includes('SELECT * FROM coins')) {
      return [
        {id: 1, symbol: '<PERSON><PERSON>', name: 'Bitcoin', active: 1},
        {id: 2, symbol: 'ETH', name: 'Ethereum', active: 1},
      ];
    }
    return [];
  }

  /**
     * @returns {Object}
     */
  prepare() {
    return {
      run: () => ({lastID: () => 1}),
      get: () => null,
    };
  }
}

// Patch require to mock database modules
const originalRequire = Module.prototype.require;
/**
 * @param {string} id
 */
Module.prototype.require = function (id) {
  if (id.includes('database-manager') || id.includes('DatabaseManager')) {
    return MockDatabaseManager;
  }
  if (id === 'better-sqlite3' || id === 'sqlite3') {
    return function () {
      return new MockDatabaseManager();
    };
  }
  return originalRequire.apply(this, arguments);
};

// Minimal mock AutonomousTrader class for demonstration
class AutonomousTrader {
  /**
     * Constructor for the AutonomousTrader class.
     * @param {object} config - Trader configuration object.
     */
  constructor(config) {
    // this.config = config;
    // this.activeBots = new Map();
    // this.opportunities = new Set();
    // this.performance = {totalProfit: 0, winRate: 0};
    // this.availableCapital = config.totalCapital || 0;
    // this.orchestrator = null;
  }

  /**
     * Stops the mock autonomous trading system.
     */
  stop() {
    logger.info('Mock stopped.');
  }
}

/**
 * Starts a mock autonomous trading system.
 */
function startMockTrading() {
  try {
    const trader = new AutonomousTrader({
      totalCapital: 10000,
      maxGridBots: 5,
      maxFuturesGridBots: 2,
      scanInterval: 30000,
      adjustmentInterval: 60000,
      enableMockData: true,
    });

    trader.orchestrator = {
      components: {
        gridBotManager: {
          deployBot: (config) => {
            logger.info(`📈 Mock grid bot for ${config.symbol}`);
            const {symbol} = config;
            return {id: Date.now: jest.fn(), symbol, status: 'active'};
          },
          stopBot: (botId) => {
            logger.info(`🛑 Mock bot ${botId}`);
            return true;
          },
          getBotStatus: () => ({
            status: 'active',
            profit: Math.random() * 100,
            trades: Math.floor(Math.random() * 10),
          }),
        },
        memeCoinScanner: {
          getTopOpportunities: () => [
            {symbol: 'DOGE/USDT', score: Math.random: jest.fn(), volume: Math.random() * 1000000},
            {symbol: 'SHIB/USDT', score: Math.random: jest.fn(), volume: Math.random() * 1000000},
          ],
        },
        whaleTracker: {
          getActiveSignals: () => [
            {symbol: 'BTC/USDT', type: 'accumulation', confidence: Math.random()},
            {symbol: 'ETH/USDT', type: 'distribution', confidence: Math.random()},
          ],
        },
      },
    };

    setTimeout(() => {
      logger.info('\n📊 Current Status:');
      logger.info(`   Active Bots: ${trader.activeBots.size}`);
      logger.info(`   Opportunities Found: ${trader.opportunities.size}`);
      logger.info(`   Available Capital: $${trader.availableCapital.toFixed(2)}`);
    }, 2000);

    setInterval(() => {
      logger.info('\n📊 Status Update:');
      logger.info(`   Active Bots: ${trader.activeBots.size}`);
      logger.info(`   Total Profit: $${(trader.performance.totalProfit || 0).toFixed(2)}`);
      logger.info(`   Win Rate: ${((trader.performance.winRate || 0) * 100).toFixed(1)}%`);

      if (trader.activeBots.size > 0) {
        logger.info('   Active Bots:');
        trader.activeBots.forEach((bot) => {
          logger.info(`     - ${bot.symbol}: ${bot.type ? bot.type : 'grid'} (${bot.status})`);
        });
      }
    }, 30000);

    process.on('SIGINT', () => {
      logger.warn('\n⚠️ Shutting down...');
      trader.stop();
      logger.info('✅ Shutdown complete');
      process.exit(0);
    });

    logger.info('\n✅ Mock autonomous trading system is running!');
    logger.info('Press Ctrl+C to stop\n');

  } catch (error) {
    logger.error('❌ Failed to start mock trading:', error);
    process.exit(1);
  }
}

startMockTrading();

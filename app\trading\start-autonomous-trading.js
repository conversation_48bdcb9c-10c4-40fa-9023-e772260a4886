'use strict';

/**
 * Autonomous Trading System Launcher
 * This script ensures all components are properly connected and starts the autonomous trading system
 * It can be run directly from the command line or integrated into the Electron app
 */
const path = require('path');
const fs = require('fs');
const logger = require('./shared/helpers/logger');

// Import the main trading system interface
const TradingSystemInterface = require('./index');

// Import database initialization
const UnifiedDatabaseInitializer = require('./databases/unified-database-init');
const {
    applyUnifiedSchema
} = require('./databases/apply_unified_schema');

// Import configuration
// const ConfigManager = require('./config/index'); // Unused

async function startAutonomousTrading() {
    try {
        logger.info('🚀 Starting Autonomous Trading System...');
        logger.info('═'.repeat(50));

        // Step 1 Setup
        logger.info('\n1️⃣ Setting up environment...');
        setupEnvironment();

        // Step 2 Initialization
        logger.info('\n2️⃣ Initializing databases...');
        await initializeDatabases();

        // Step 3
        logger.info('\n3️⃣ Loading configuration...');
        loadConfiguration();

        // Step 4 Trading System
        logger.info('\n4️⃣ Initializing trading system...');

        // 🔍 DIAGNOSTIC TradingSystemInterface structure
        logger.info('🔍 DIAGNOSTIC available methods:');
        logger.info('   Available methods:', Object.getOwnPropertyNames(TradingSystemInterface.prototype));
        logger.info('   Has instance property:', 'instance' in TradingSystemInterface);
        logger.info('   Constructor name:', TradingSystemInterface.name);

        // Create proper instance since .instance doesn't exist
        const tradingSystem = new TradingSystemInterface();
        logger.info('   Created TradingSystemInterface instance');

        // 🔍 DIAGNOSTIC for autonomous-specific methods
        const requiredMethods = ['startTradingEngine', 'startAutonomous', 'toggleWhaleTracking', 'getAutonomousStatus'];
        logger.info('🔍 DIAGNOSTIC for required autonomous methods:');
        requiredMethods.forEach(method => {
            const exists = typeof tradingSystem[method] === 'function';
            logger.info(`   ${method}: ${exists ? '✅ EXISTS' : '❌ MISSING'}`);
        });
        await tradingSystem.initialize();

        // Step 5 Trading Engine
        logger.info('\n5️⃣ Starting trading engine...');
        const engineResult = await tradingSystem.startTradingEngine();
        if (!engineResult.success) {
            throw new Error(`Failed to start trading engine: ${engineResult.error}`);
        }

        // Step 6 Features
        logger.info('\n6️⃣ Enabling trading features...');
        await enableTradingFeatures(tradingSystem);

        // Step 7 Autonomous Trading
        logger.info('\n7️⃣ Starting autonomous trading...');
        const autonomousResult = await tradingSystem.startAutonomous();
        if (!autonomousResult.success) {
            throw new Error(`Failed to start autonomous trading: ${autonomousResult.error}`);
        }
        logger.info('\n═'.repeat(50));
        logger.info('✅ Autonomous Trading System is running!');
        logger.info('═'.repeat(50));

        // Display initial status
        await displayStatus(tradingSystem);

        // Setup graceful shutdown
        setupGracefulShutdown(tradingSystem);

        // Keep the process running
        setInterval(async () => {
            await displayStatus(tradingSystem);
        }, 60000); // Update status every minute
    } catch (error) {
        logger.error('\n❌ Failed to start autonomous trading system:', error);
        logger.error('Startup failed:', error);
        process.exit(1);
    }
}

function setupEnvironment() {
    // Set default environment variables if not already set
    const defaults = {
        NODE_ENV: 'production',
        DEFAULT_RISK_PERCENT: '2',
        MAX_OPEN_POSITIONS: '10',
        STOP_LOSS_PERCENT: '5',
        ENABLE_WHALE_TRACKING: 'true',
        ENABLE_MEME_COIN_SCANNING: 'true',
        ENABLE_SENTIMENT_ANALYSIS: 'true',
        ENABLE_AI_OPTIMIZATION: 'true',
        ENABLE_AUTO_TRADING: 'true',
        DATABASE_PATH(__dirname, 'databases', 'trading_bot.db'
),
    LOG_LEVEL: 'info'
}
    ;
    for (const [key, value] of Object.entries(defaults)) {
        if (!process.env[key]) {
            process.env[key] = value;
            logger.info(`   Set ${key}=${value}`);
        }
    }

    // Create required directories
    const directories = [path.join(__dirname, 'logs'), path.join(__dirname, 'logs/errors'), path.join(__dirname, 'logs/trading'), path.join(__dirname, 'logs/performance'), path.join(__dirname, 'logs/emergency'), path.join(__dirname, 'databases'), path.join(__dirname, 'databases/backups'), path.join(__dirname, 'config')];
    for (const dir of directories) {
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, {
                recursive
            });
            logger.info(`   Created directory: ${dir}`);
        }
    }
}

async function initializeDatabases() {
    try {
        // Apply unified schema
        logger.info('   Applying database schema...');
        await applyUnifiedSchema();

        // Initialize databases
        const dbInitializer = new UnifiedDatabaseInitializer();
        await dbInitializer.initialize();
        logger.info('   ✅ Databases initialized');
    } catch (error) {
        logger.error('   ❌ Database initialization failed:', error);
        throw error;
    }
}

function loadConfiguration() {
    try {
        const configPath = path.join(__dirname, 'config', 'config.json');

        // Create default configuration if it doesn't exist
        if (!fs.existsSync(configPath)) {
            const defaultConfig = {
                trading: {
                    defaultRiskPercent,
                    maxOpenPositions,
                    stopLossPercent,
                    takeProfitPercent,
                    enableAutoTrading
                },
                autonomous: {
                    totalCapital,
                    maxGridBots,
                    maxFuturesGridBots,
                    scanInterval,
                    adjustmentInterval,
                    minVolume24h,
                    minVolatility,
                    riskPerTrade,
                    maxDrawdown,
                    profitTarget
                },
                exchanges: {
                    active'binance', 'bybit'
        ],
            testMode !== 'production'
        },
            features: {
                whaleTracking,
                    memeCoinScanning,
                    sentimentAnalysis,
                    aiOptimization,
                    gridTrading,
                    futuresTrading
            }
        }
            ;
            fs.writeFileSync(configPath, JSON.stringify(defaultConfig, null, 2));
            logger.info('   Created default configuration');
        }
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        logger.info('   ✅ Configuration loaded');

        // Update environment with config values
        if (config.trading) {
            process.env.DEFAULT_RISK_PERCENT = config.trading.defaultRiskPercent.toString();
            process.env.MAX_OPEN_POSITIONS = config.trading.maxOpenPositions.toString();
            process.env.STOP_LOSS_PERCENT = config.trading.stopLossPercent.toString();
        }
        return config;
    } catch (error) {
        logger.error('   ❌ Configuration loading failed:', error);
        throw error;
    }
}

/**
 * @param {TradingSystemInterface} tradingSystem
 */
function enableTradingFeatures(tradingSystem) {
    try {
        // Enable whale tracking
        if (process.env.ENABLE_WHALE_TRACKING === 'true') {
            logger.info('   ✅ Whale tracking enabled');
        }

        // Enable meme coin scanning
        if (process.env.ENABLE_MEME_COIN_SCANNING === 'true') {
            await tradingSystem.startMemeScanner();
            logger.info('   ✅ Meme coin scanner enabled');
        }

        // Set risk parameters
        await tradingSystem.setRiskParameters({
            maxPositionSize(process.env.DEFAULT_RISK_PERCENT) / 100,
            maxOpenPositions(process.env.MAX_OPEN_POSITIONS),
        stopLossPercent(process.env.STOP_LOSS_PERCENT) / 100,
            takeProfitPercent
    })
        ;
        logger.info('   ✅ Risk parameters configured');
    } catch (error) {
        logger.error('   ❌ Error enabling trading features:', error);
        throw error;
    }
}

/**
 * @param {TradingSystemInterface} tradingSystem
 */
async function displayStatus(tradingSystem) {
    try {
        let _autonomousStatus$dat, _autonomousStatus$dat2, _autonomousStatus$dat3, _autonomousStatus$dat4,
            _autonomousStatus$dat5;
        const status = await tradingSystem.getBotStatus();
        const autonomousStatus = await tradingSystem.getAutonomousStatus();
        await tradingSystem.getTradingStats();
        logger.info('\n📊 System Status:');
        logger.info('─'.repeat(40));
        logger.info(`Trading Engine: ${status.status === 'running' ? '🟢 Running' : '🔴 Stopped'}`);
        logger.info(`Autonomous Trading: ${(_autonomousStatus$dat = autonomousStatus.data) !== null && _autonomousStatus$dat !== void 0 && _autonomousStatus$dat.isRunning ? '🟢 Active' : '🔴 Inactive'}`);
        logger.info(`Active Bots: ${((_autonomousStatus$dat2 = autonomousStatus.data) === null || _autonomousStatus$dat2 === void 0 ? void 0$dat2.activeBots) || 0}`);
        logger.info(`Available Capital: $${((_autonomousStatus$dat3 = autonomousStatus.data) === null || _autonomousStatus$dat3 === void 0 ? void 0 : (_autonomousStatus$dat4 = _autonomousStatus$dat3.availableCapital) === null || _autonomousStatus$dat4 === void 0 ? void 0$dat4.toFixed(2)) || '0.00'}`);
        if ((_autonomousStatus$dat5 = autonomousStatus.data) !== null && _autonomousStatus$dat5 !== void 0 && _autonomousStatus$dat5.performance) {
            let _perf$totalProfit;
            const perf = autonomousStatus.data.performance;
            logger.info('\n💰 Performance:');
            logger.info(`Total Profit: $${((_perf$totalProfit = perf.totalProfit) === null || _perf$totalProfit === void 0 ? void 0$totalProfit.toFixed(2)) || '0.00'}`);
            logger.info(`Win Rate: ${(perf.winRate * 100).toFixed(1)}%`);
            logger.info(`Total Trades: ${perf.totalTrades || 0}`);
        }
    } catch (error) {
        logger.error('   ❌ Error displaying status:', error);
    }
}

/**
 * @param {TradingSystemInterface} tradingSystem
 */
function setupGracefulShutdown(tradingSystem) {
    const shutdown = async signal => {
        logger.info(`\n⚠️ Received ${signal}, shutting down gracefully...`);
        try {
            // Stop autonomous trading
            await tradingSystem.stopAutonomous();
            logger.info('✅ Autonomous trading stopped');

            // Stop all grid bots
            await tradingSystem.stopAllGrids();
            logger.info('✅ All grid bots stopped');

            // Stop trading engine
            // Stop trading system
            // Note method may not exist - using graceful shutdown instead
            logger.info('✅ Trading system shutdown handled via graceful shutdown');
            logger.info('\n👋 Shutdown complete. Goodbye!');
            process.exit(0);
        } catch (error) {
            logger.error('❌ Error during shutdown:', error);
            process.exit(1);
        }
    };

    // Handle shutdown signals
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));

    // Handle uncaught errors
    process.on('uncaughtException', error => {
        logger.error('❌ Uncaught exception:', error);
        logger.error('Uncaught exception:', error);
        shutdown('uncaughtException');
    });
    process.on('unhandledRejection', (reason, promise) => {
        logger.error('❌ Unhandled rejection at:', promise, 'reason:', reason);
        logger.error('Unhandled rejection:', reason);
        shutdown('unhandledRejection');
    });
}

// Run the autonomous trading system
if (require.main === module) {
    startAutonomousTrading();
}
module.exports = {
    startAutonomousTrading
};

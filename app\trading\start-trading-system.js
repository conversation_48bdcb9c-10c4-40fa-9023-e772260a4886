/**
 * Trading System Startup Script
 * Initializes and starts the trading system with proper error handling
 */

const TradingSystemInterface = require('./index');
const DatabaseInitializer = require('./database/DatabaseManager');
const logger = require('./shared/helpers/logger');
const fs = require('fs');
const path = require('path');

class TradingSystemStarter {
  constructor() {
    // this.tradingSystem = TradingSystemInterface.instance;
  }

  async start() {
    try {
      logger.info('=== Starting Trading System ===');

      // Step 1 environment
      await this.checkEnvironment();

      // Step 2 database
      await this.initializeDatabase();

      // Step 3 trading system
      await this.initializeTradingSystem();

      // Step 4 monitoring
      // this.startMonitoring();

      logger.info('=== Trading System Started Successfully ===');
      logger.info('System Status:', this.tradingSystem.getBotStatus());

    } catch (error) {
      logger.error('Failed to start trading system:', error);
      process.exit(1);
    }
  }

  checkEnvironment() {
    logger.info('Checking environment...');

    // Check if required directories exist
    const requiredDirs = [
      path.join(__dirname, 'databases'),
      path.join(__dirname, 'logs'),
      path.join(__dirname, '.keys')];


    for (const dir of requiredDirs) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, {recursive});
        logger.info(`Created directory: ${dir}`);
      }
    }

    // Check for .env file
    const envPath = path.join(__dirname, '.env');
    if (!fs.existsSync(envPath)) {
      logger.warn('.env file not found. Creating template...');
      const envTemplate = `# Trading System Configuration
NODE_ENV=development
LOG_LEVEL=info

# Exchange API Keys (Optional)
BINANCE_API_KEY=
BINANCE_API_SECRET=

# Blockchain APIs (Optional)
ETHERSCAN_API_KEY=

# AI/LLM APIs (Optional)
OPENAI_API_KEY=

# Risk Management
DEFAULT_RISK_PERCENT=2
MAX_OPEN_POSITIONS=10
STOP_LOSS_PERCENT=5

# Feature Flags
ENABLE_WHALE_TRACKING=true
ENABLE_MEME_COIN_SCANNING=true
ENABLE_AUTO_TRADING=false
`;
      fs.writeFileSync(envPath, envTemplate);
      logger.info('Created .env template file');
    }

    // Load environment variables
    require('dotenv').config({path});

    logger.info('Environment check completed');
  }

  async initializeDatabase() {
    logger.info('Initializing database...');

    const dbInitializer = new DatabaseInitializer();
    await dbInitializer.initialize();

    logger.info('Database initialization completed');
  }

  async initializeTradingSystem() {
    logger.info('Initializing trading system components...');

    await this.tradingSystem.initialize();

    logger.info('Trading system initialization completed');
  }

  startMonitoring() {
    logger.info('Starting system monitoring...');

    // Log system status every minute
    setInterval(() => {
      const status = this.tradingSystem.getBotStatus();
      logger.info('System Status:', status);
    }, 60000);

    // Handle shutdown gracefully
    process.on('SIGINT', async () => {
      logger.info('Shutdown signal received...');
      await this.shutdown();
    });

    process.on('SIGTERM', async () => {
      logger.info('Termination signal received...');
      await this.shutdown();
    });
  }

  async shutdown() {
    try {
      logger.info('Shutting down trading system...');

      await this.tradingSystem.stopTradingEngine();

      logger.info('Trading system shutdown completed');
      process.exit(0);
    } catch (error) {
      logger.error('Error during shutdown:', error);
      process.exit(1);
    }
  }
}

// Run if called directly
if (require.main === module) {
  const starter = new TradingSystemStarter();
  starter.start().catch((error) => {
    logger.error('Startup failed:', error);
    process.exit(1);
  });
}

module.exports = TradingSystemStarter;

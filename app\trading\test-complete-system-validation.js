/**
 * COMPLETE SYSTEM VALIDATION
 * Comprehensive test suite that validates all critical components with proper testing
 */
class CompleteSystemValidation {
    constructor() {
        this.testSuites = [];
        this.overallResults = {
            totalTests: 0,
            totalPassed: 0,
            totalFailed: 0,
            suiteResults: []
        };
    }

    async runCompleteValidation() {
        console.log('🎯 ELECTRONTRADER COMPLETE SYSTEM VALIDATION');
        console.log('============================================');
        console.log('');
        console.log('🔍 Running comprehensive tests that validate ACTUAL functionality');
        console.log('📊 Each test validates real behavior, not just module loading');
        console.log('');

        try {
            // Test Suite 1: Database Configuration
            await this.runTestSuite('Database Configuration', './test-database-config-proper');
            
            // Test Suite 2: Exchange Connectivity
            await this.runTestSuite('Exchange Connectivity', './test-exchange-connector-proper');
            
            // Test Suite 3: Trading Decision Logic
            await this.runTestSuite('Trading Decision Logic', './test-trading-decisions-proper');
            
            // Generate Final Report
            this.generateFinalReport();
            
        } catch (error) {
            console.log('❌ CRITICAL VALIDATION FAILURE:', error.message);
            throw error;
        }
    }

    async runTestSuite(suiteName, modulePath) {
        console.log(`🧪 RUNNING ${suiteName.toUpperCase()} TESTS`);
        console.log('='.repeat(50));
        console.log('');

        try {
            const TestClass = require(modulePath);
            const testInstance = new TestClass();
            
            // Capture console output to analyze results
            const originalLog = console.log;
            let capturedOutput = '';
            console.log = (...args) => {
                capturedOutput += args.join(' ') + '\n';
                originalLog(...args);
            };

            await testInstance.runAllTests();
            
            // Restore console.log
            console.log = originalLog;
            
            // Parse results from captured output
            const results = this.parseTestResults(capturedOutput, suiteName);
            this.overallResults.suiteResults.push(results);
            this.overallResults.totalTests += results.totalTests;
            this.overallResults.totalPassed += results.passed;
            this.overallResults.totalFailed += results.failed;
            
            console.log(`✅ ${suiteName} validation completed`);
            console.log('');
            
        } catch (error) {
            console.log(`❌ ${suiteName} validation failed:`, error.message);
            this.overallResults.suiteResults.push({
                suiteName,
                totalTests: 0,
                passed: 0,
                failed: 1,
                successRate: 0,
                status: 'FAILED',
                error: error.message
            });
            this.overallResults.totalFailed += 1;
            throw error;
        }
    }

    parseTestResults(output, suiteName) {
        // Extract test statistics from output
        const totalMatch = output.match(/📈 Total Tests: (\d+)/);
        const passedMatch = output.match(/✅ Passed: (\d+)/);
        const failedMatch = output.match(/❌ Failed: (\d+)/);
        const successMatch = output.match(/📊 Success Rate: ([\d.]+)%/);
        
        const totalTests = totalMatch ? parseInt(totalMatch[1]) : 0;
        const passed = passedMatch ? parseInt(passedMatch[1]) : 0;
        const failed = failedMatch ? parseInt(failedMatch[1]) : 0;
        const successRate = successMatch ? parseFloat(successMatch[1]) : 0;
        
        return {
            suiteName,
            totalTests,
            passed,
            failed,
            successRate,
            status: failed === 0 ? 'PASSED' : 'FAILED'
        };
    }

    generateFinalReport() {
        console.log('🎯 FINAL SYSTEM VALIDATION REPORT');
        console.log('=================================');
        console.log('');

        // Overall statistics
        const overallSuccessRate = this.overallResults.totalTests > 0 
            ? (this.overallResults.totalPassed / this.overallResults.totalTests) * 100 
            : 0;

        console.log('📊 OVERALL STATISTICS:');
        console.log(`📈 Total Tests Executed: ${this.overallResults.totalTests}`);
        console.log(`✅ Total Tests Passed: ${this.overallResults.totalPassed}`);
        console.log(`❌ Total Tests Failed: ${this.overallResults.totalFailed}`);
        console.log(`📊 Overall Success Rate: ${overallSuccessRate.toFixed(1)}%`);
        console.log('');

        // Individual suite results
        console.log('📋 INDIVIDUAL SUITE RESULTS:');
        console.log('-'.repeat(50));
        
        for (const suite of this.overallResults.suiteResults) {
            const statusIcon = suite.status === 'PASSED' ? '✅' : '❌';
            console.log(`${statusIcon} ${suite.suiteName}:`);
            console.log(`   Tests: ${suite.totalTests} | Passed: ${suite.passed} | Failed: ${suite.failed}`);
            console.log(`   Success Rate: ${suite.successRate.toFixed(1)}%`);
            if (suite.error) {
                console.log(`   Error: ${suite.error}`);
            }
            console.log('');
        }

        // System capabilities validation
        console.log('🎯 SYSTEM CAPABILITIES VALIDATION:');
        console.log('-'.repeat(50));
        
        const capabilities = this.validateSystemCapabilities();
        for (const [capability, status] of Object.entries(capabilities)) {
            const icon = status ? '✅' : '❌';
            console.log(`${icon} ${capability}`);
        }
        console.log('');

        // Final verdict
        console.log('🏆 FINAL VERDICT:');
        console.log('='.repeat(20));
        
        if (this.overallResults.totalFailed === 0 && overallSuccessRate === 100) {
            console.log('🎉 SYSTEM FULLY VALIDATED AND OPERATIONAL!');
            console.log('');
            console.log('✅ All critical components are working correctly');
            console.log('✅ Real-time market data retrieval is functional');
            console.log('✅ Trading decision logic is operational');
            console.log('✅ Database configuration is properly set up');
            console.log('✅ Exchange connectivity is established');
            console.log('✅ Risk management systems are active');
            console.log('');
            console.log('🚀 THE ELECTRONTRADER BACKEND IS READY FOR LIVE TRADING!');
        } else {
            console.log('⚠️  SYSTEM VALIDATION INCOMPLETE!');
            console.log('');
            console.log(`❌ ${this.overallResults.totalFailed} tests failed`);
            console.log(`📊 Success rate: ${overallSuccessRate.toFixed(1)}%`);
            console.log('');
            console.log('🔧 ISSUES NEED TO BE RESOLVED BEFORE LIVE TRADING');
        }
        
        console.log('');
        console.log('📝 VALIDATION COMPLETED AT:', new Date().toISOString());
    }

    validateSystemCapabilities() {
        const capabilities = {};
        
        // Check if database configuration suite passed
        const dbSuite = this.overallResults.suiteResults.find(s => s.suiteName === 'Database Configuration');
        capabilities['Database Configuration'] = dbSuite && dbSuite.status === 'PASSED';
        
        // Check if exchange connectivity suite passed
        const exchangeSuite = this.overallResults.suiteResults.find(s => s.suiteName === 'Exchange Connectivity');
        capabilities['Exchange Connectivity'] = exchangeSuite && exchangeSuite.status === 'PASSED';
        capabilities['Real-time Market Data'] = exchangeSuite && exchangeSuite.status === 'PASSED';
        capabilities['CCXT Integration'] = exchangeSuite && exchangeSuite.status === 'PASSED';
        
        // Check if trading decision suite passed
        const tradingSuite = this.overallResults.suiteResults.find(s => s.suiteName === 'Trading Decision Logic');
        capabilities['Trading Decision Making'] = tradingSuite && tradingSuite.status === 'PASSED';
        capabilities['Grid Bot Logic'] = tradingSuite && tradingSuite.status === 'PASSED';
        capabilities['Risk Management'] = tradingSuite && tradingSuite.status === 'PASSED';
        capabilities['Decision Consistency'] = tradingSuite && tradingSuite.status === 'PASSED';
        
        return capabilities;
    }
}

// Additional validation functions
function validateTestQuality() {
    console.log('🔍 VALIDATING TEST QUALITY');
    console.log('==========================');
    console.log('');
    
    const testFiles = [
        './test-database-config-proper.js',
        './test-exchange-connector-proper.js', 
        './test-trading-decisions-proper.js'
    ];
    
    for (const testFile of testFiles) {
        try {
            const TestClass = require(testFile);
            const testInstance = new TestClass();
            
            // Check if test class has proper structure
            if (typeof testInstance.runAllTests !== 'function') {
                throw new Error(`${testFile} missing runAllTests method`);
            }
            
            if (typeof testInstance.generateTestReport !== 'function') {
                throw new Error(`${testFile} missing generateTestReport method`);
            }
            
            console.log(`✅ ${testFile}: Proper test structure`);
            
        } catch (error) {
            console.log(`❌ ${testFile}: ${error.message}`);
            throw error;
        }
    }
    
    console.log('✅ All test files have proper structure');
    console.log('');
}

function validateSystemRequirements() {
    console.log('📋 VALIDATING SYSTEM REQUIREMENTS');
    console.log('=================================');
    console.log('');
    
    const requirements = [
        { name: 'Node.js', check: () => process.version },
        { name: 'CCXT Library', check: () => require('ccxt').version },
        { name: 'File System Access', check: () => require('fs').constants.R_OK },
        { name: 'Path Module', check: () => require('path').join },
        { name: 'SQLite Support', check: () => require('sqlite3') }
    ];
    
    for (const req of requirements) {
        try {
            const result = req.check();
            console.log(`✅ ${req.name}: Available`);
        } catch (error) {
            console.log(`❌ ${req.name}: Not available - ${error.message}`);
            throw new Error(`Missing requirement: ${req.name}`);
        }
    }
    
    console.log('✅ All system requirements met');
    console.log('');
}

// Run complete validation if called directly
if (require.main === module) {
    async function main() {
        try {
            // Validate test quality first
            validateTestQuality();
            
            // Validate system requirements
            validateSystemRequirements();
            
            // Run complete system validation
            const validation = new CompleteSystemValidation();
            await validation.runCompleteValidation();
            
        } catch (error) {
            console.error('❌ VALIDATION FAILED:', error.message);
            process.exit(1);
        }
    }
    
    main();
}

module.exports = CompleteSystemValidation;

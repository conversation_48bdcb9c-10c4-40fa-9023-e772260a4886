const fs = require('fs');
const path = require('path');

/**
 * PROPER DATABASE CONFIGURATION TESTS
 * These tests actually validate the real functionality, not just module loading
 */
class ProperDatabaseConfigTests {
    constructor() {
        this.testResults = [];
        this.dbConfig = null;
    }

    async runAllTests() {
        console.log('🧪 RUNNING PROPER DATABASE CONFIGURATION TESTS');
        console.log('===============================================');
        console.log('');

        try {
            // Test 1: Module Loading and Instantiation
            await this.testModuleLoading();
            
            // Test 2: Configuration Structure Validation
            await this.testConfigurationStructure();
            
            // Test 3: Database Path Resolution
            await this.testDatabasePaths();
            
            // Test 4: Environment-Specific Configuration
            await this.testEnvironmentConfiguration();
            
            // Test 5: File System Verification
            await this.testFileSystemVerification();
            
            // Test 6: Validation Logic
            await this.testValidationLogic();
            
            // Test 7: Error Handling
            await this.testErrorHandling();
            
            // Generate Test Report
            this.generateTestReport();
            
        } catch (error) {
            console.log('❌ CRITICAL TEST FAILURE:', error.message);
            throw error;
        }
    }

    async testModuleLoading() {
        console.log('📦 Test 1: Module Loading and Instantiation');
        console.log('-------------------------------------------');

        try {
            // Test module can be required
            this.dbConfig = require('./config/database-config');
            this.addResult('Module Loading', 'PASS', 'Database config module loaded successfully');

            // Test it's an object with expected properties
            if (typeof this.dbConfig !== 'object') {
                throw new Error('Database config is not an object');
            }
            this.addResult('Object Type', 'PASS', 'Database config is an object');

            // Test it has the expected methods
            const expectedMethods = ['getDatabaseConfig', 'getAllConfigs', 'verifyDatabaseFiles', 'validate'];
            for (const method of expectedMethods) {
                if (typeof this.dbConfig[method] !== 'function') {
                    throw new Error(`Missing method: ${method}`);
                }
                this.addResult(`Method ${method}`, 'PASS', `Method ${method} exists and is a function`);
            }

        } catch (error) {
            this.addResult('Module Loading', 'FAIL', error.message);
            throw error;
        }

        console.log('✅ Module loading tests passed');
        console.log('');
    }

    async testConfigurationStructure() {
        console.log('🏗️  Test 2: Configuration Structure Validation');
        console.log('----------------------------------------------');

        try {
            // Test configuration has required sections
            const config = this.dbConfig.config;
            const requiredSections = ['databases', 'connection', 'performance', 'security', 'environments'];
            
            for (const section of requiredSections) {
                if (!config[section]) {
                    throw new Error(`Missing configuration section: ${section}`);
                }
                this.addResult(`Section ${section}`, 'PASS', `Configuration section ${section} exists`);
            }

            // Test databases section has required databases
            const requiredDatabases = ['trading', 'n8n', 'credentials'];
            for (const db of requiredDatabases) {
                if (!config.databases[db]) {
                    throw new Error(`Missing database configuration: ${db}`);
                }
                this.addResult(`Database ${db}`, 'PASS', `Database ${db} configuration exists`);
                
                // Test each database has required properties
                const dbConfig = config.databases[db];
                const requiredProps = ['path', 'description', 'walMode'];
                for (const prop of requiredProps) {
                    if (dbConfig[prop] === undefined) {
                        throw new Error(`Missing property ${prop} in ${db} database config`);
                    }
                    this.addResult(`${db}.${prop}`, 'PASS', `Property ${prop} exists in ${db} config`);
                }
            }

        } catch (error) {
            this.addResult('Configuration Structure', 'FAIL', error.message);
            throw error;
        }

        console.log('✅ Configuration structure tests passed');
        console.log('');
    }

    async testDatabasePaths() {
        console.log('📁 Test 3: Database Path Resolution');
        console.log('-----------------------------------');

        try {
            // Test getDatabaseConfig for each database
            const databases = ['trading', 'n8n', 'credentials'];
            
            for (const dbName of databases) {
                const config = this.dbConfig.getDatabaseConfig(dbName);
                
                // Test config is returned
                if (!config) {
                    throw new Error(`No configuration returned for ${dbName}`);
                }
                this.addResult(`${dbName} Config`, 'PASS', `Configuration returned for ${dbName}`);
                
                // Test path is absolute
                if (!path.isAbsolute(config.path)) {
                    throw new Error(`Path for ${dbName} is not absolute: ${config.path}`);
                }
                this.addResult(`${dbName} Path`, 'PASS', `Absolute path for ${dbName}: ${config.path}`);
                
                // Test path points to expected location
                const expectedDir = path.join(__dirname, 'databases');
                const actualDir = path.dirname(config.path);
                if (!actualDir.includes('databases')) {
                    throw new Error(`Path for ${dbName} doesn't point to databases directory: ${config.path}`);
                }
                this.addResult(`${dbName} Directory`, 'PASS', `Path points to databases directory`);
            }

            // Test invalid database name throws error
            try {
                this.dbConfig.getDatabaseConfig('invalid');
                throw new Error('Should have thrown error for invalid database name');
            } catch (error) {
                if (error.message.includes('Database configuration not found')) {
                    this.addResult('Invalid DB Error', 'PASS', 'Correctly throws error for invalid database name');
                } else {
                    throw error;
                }
            }

        } catch (error) {
            this.addResult('Database Paths', 'FAIL', error.message);
            throw error;
        }

        console.log('✅ Database path tests passed');
        console.log('');
    }

    async testEnvironmentConfiguration() {
        console.log('🌍 Test 4: Environment-Specific Configuration');
        console.log('---------------------------------------------');

        try {
            const originalEnv = process.env.NODE_ENV;
            
            // Test development environment
            process.env.NODE_ENV = 'development';
            const devConfig = this.dbConfig.getDatabaseConfig('trading');
            if (devConfig.verbose !== true) {
                throw new Error('Development environment should have verbose=true');
            }
            this.addResult('Dev Environment', 'PASS', 'Development environment config applied correctly');
            
            // Test production environment
            process.env.NODE_ENV = 'production';
            const prodConfig = this.dbConfig.getDatabaseConfig('trading');
            if (prodConfig.verbose !== false) {
                throw new Error('Production environment should have verbose=false');
            }
            this.addResult('Prod Environment', 'PASS', 'Production environment config applied correctly');
            
            // Test test environment
            process.env.NODE_ENV = 'test';
            const testConfig = this.dbConfig.getDatabaseConfig('trading');
            if (testConfig.verbose !== false) {
                throw new Error('Test environment should have verbose=false');
            }
            this.addResult('Test Environment', 'PASS', 'Test environment config applied correctly');
            
            // Restore original environment
            process.env.NODE_ENV = originalEnv;

        } catch (error) {
            this.addResult('Environment Configuration', 'FAIL', error.message);
            throw error;
        }

        console.log('✅ Environment configuration tests passed');
        console.log('');
    }

    async testFileSystemVerification() {
        console.log('💾 Test 5: File System Verification');
        console.log('-----------------------------------');

        try {
            const verification = this.dbConfig.verifyDatabaseFiles();
            
            // Test verification returns results for all databases
            const expectedDbs = ['trading', 'n8n', 'credentials'];
            for (const db of expectedDbs) {
                if (!verification[db]) {
                    throw new Error(`No verification result for ${db}`);
                }
                this.addResult(`${db} Verification`, 'PASS', `Verification result exists for ${db}`);
                
                const result = verification[db];
                
                // Test result has required properties
                const requiredProps = ['path', 'exists', 'size', 'readable', 'writable'];
                for (const prop of requiredProps) {
                    if (result[prop] === undefined) {
                        throw new Error(`Missing property ${prop} in ${db} verification result`);
                    }
                    this.addResult(`${db}.${prop}`, 'PASS', `Property ${prop} exists in ${db} verification`);
                }
                
                // Test path is correct
                if (!result.path || typeof result.path !== 'string') {
                    throw new Error(`Invalid path in ${db} verification result`);
                }
                this.addResult(`${db} Path Valid`, 'PASS', `Valid path in ${db} verification`);
            }

        } catch (error) {
            this.addResult('File System Verification', 'FAIL', error.message);
            throw error;
        }

        console.log('✅ File system verification tests passed');
        console.log('');
    }

    async testValidationLogic() {
        console.log('✅ Test 6: Validation Logic');
        console.log('---------------------------');

        try {
            const validation = this.dbConfig.validate();
            
            // Test validation returns required properties
            const requiredProps = ['valid', 'errors', 'warnings'];
            for (const prop of requiredProps) {
                if (validation[prop] === undefined) {
                    throw new Error(`Missing property ${prop} in validation result`);
                }
                this.addResult(`Validation.${prop}`, 'PASS', `Property ${prop} exists in validation result`);
            }
            
            // Test valid is boolean
            if (typeof validation.valid !== 'boolean') {
                throw new Error('Validation.valid should be boolean');
            }
            this.addResult('Valid Boolean', 'PASS', 'Validation.valid is boolean');
            
            // Test errors is array
            if (!Array.isArray(validation.errors)) {
                throw new Error('Validation.errors should be array');
            }
            this.addResult('Errors Array', 'PASS', 'Validation.errors is array');
            
            // Test warnings is array
            if (!Array.isArray(validation.warnings)) {
                throw new Error('Validation.warnings should be array');
            }
            this.addResult('Warnings Array', 'PASS', 'Validation.warnings is array');

        } catch (error) {
            this.addResult('Validation Logic', 'FAIL', error.message);
            throw error;
        }

        console.log('✅ Validation logic tests passed');
        console.log('');
    }

    async testErrorHandling() {
        console.log('🚨 Test 7: Error Handling');
        console.log('-------------------------');

        try {
            // Test invalid database name
            try {
                this.dbConfig.getDatabaseConfig('nonexistent');
                throw new Error('Should have thrown error for nonexistent database');
            } catch (error) {
                if (error.message.includes('Database configuration not found')) {
                    this.addResult('Invalid DB Error', 'PASS', 'Correctly handles invalid database name');
                } else {
                    throw new Error(`Unexpected error message: ${error.message}`);
                }
            }
            
            // Test getAllConfigs works
            const allConfigs = this.dbConfig.getAllConfigs();
            if (typeof allConfigs !== 'object' || !allConfigs.trading || !allConfigs.n8n || !allConfigs.credentials) {
                throw new Error('getAllConfigs does not return expected structure');
            }
            this.addResult('Get All Configs', 'PASS', 'getAllConfigs returns correct structure');

        } catch (error) {
            this.addResult('Error Handling', 'FAIL', error.message);
            throw error;
        }

        console.log('✅ Error handling tests passed');
        console.log('');
    }

    addResult(testName, status, message) {
        this.testResults.push({
            test: testName,
            status: status,
            message: message,
            timestamp: new Date().toISOString()
        });
    }

    generateTestReport() {
        console.log('📊 TEST REPORT');
        console.log('==============');
        console.log('');

        const passed = this.testResults.filter(r => r.status === 'PASS').length;
        const failed = this.testResults.filter(r => r.status === 'FAIL').length;
        const total = this.testResults.length;

        console.log(`📈 Total Tests: ${total}`);
        console.log(`✅ Passed: ${passed}`);
        console.log(`❌ Failed: ${failed}`);
        console.log(`📊 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
        console.log('');

        if (failed > 0) {
            console.log('❌ FAILED TESTS:');
            this.testResults.filter(r => r.status === 'FAIL').forEach(result => {
                console.log(`  ❌ ${result.test}: ${result.message}`);
            });
            console.log('');
        }

        if (passed === total) {
            console.log('🎉 ALL TESTS PASSED - DATABASE CONFIGURATION IS WORKING CORRECTLY!');
        } else {
            console.log('⚠️  SOME TESTS FAILED - DATABASE CONFIGURATION HAS ISSUES!');
        }
    }
}

// Run tests if called directly
if (require.main === module) {
    const tests = new ProperDatabaseConfigTests();
    tests.runAllTests().catch(console.error);
}

module.exports = ProperDatabaseConfigTests;

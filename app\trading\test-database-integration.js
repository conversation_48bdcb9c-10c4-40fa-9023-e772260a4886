/**
 * Test Database Integration
 * Verifies that the database configuration and initialization work together
 */

const path = require('path');
const fs = require('fs');

// Test the database configuration
console.log('🔍 Testing Database Configuration...');
try {
    const databaseConfig = require('./config/database-config');
    console.log('✅ Database config loaded successfully');
    
    // Test getting individual database configs
    const tradingConfig = databaseConfig.getDatabaseConfig('trading');
    console.log('✅ Trading database config:', {
        path: tradingConfig.path,
        walMode: tradingConfig.walMode,
        cacheSize: tradingConfig.cacheSize
    });
    
    const n8nConfig = databaseConfig.getDatabaseConfig('n8n');
    console.log('✅ N8N database config:', {
        path: n8nConfig.path,
        walMode: n8nConfig.walMode
    });
    
    const credentialsConfig = databaseConfig.getDatabaseConfig('credentials');
    console.log('✅ Credentials database config:', {
        path: credentialsConfig.path,
        walMode: credentialsConfig.walMode
    });
    
} catch (error) {
    console.error('❌ Database config test failed:', error);
    process.exit(1);
}

// Test the database initializer
console.log('\n🔍 Testing Database Initializer...');
async function testDatabaseInitializer() {
    try {
        const UnifiedDatabaseInitializer = require('./engines/database/unified-database-initializer');
        console.log('✅ Database initializer loaded successfully');
        
        const initializer = new UnifiedDatabaseInitializer();
        console.log('✅ Database initializer created');
        
        // Test initialization
        console.log('🚀 Starting database initialization...');
        await initializer.initializeAll();
        console.log('✅ Database initialization completed');
        
        // Test getting database connections
        const tradingDb = initializer.getDatabase('trading');
        const credentialsDb = initializer.getDatabase('credentials');
        const n8nDb = initializer.getDatabase('n8n');
        
        console.log('✅ Database connections retrieved:', {
            trading: !!tradingDb,
            credentials: !!credentialsDb,
            n8n: !!n8nDb
        });
        
        // Test health status
        const health = initializer.getHealthStatus();
        console.log('✅ Health status:', health);
        
        // Close connections
        await initializer.closeAll();
        console.log('✅ Database connections closed');
        
        return true;
    } catch (error) {
        console.error('❌ Database initializer test failed:', error);
        return false;
    }
}

// Test the TradingOrchestrator integration
console.log('\n🔍 Testing TradingOrchestrator Integration...');
async function testTradingOrchestratorIntegration() {
    try {
        const TradingOrchestrator = require('./engines/trading/orchestration/TradingOrchestrator');
        console.log('✅ TradingOrchestrator loaded successfully');
        
        const orchestrator = new TradingOrchestrator();
        console.log('✅ TradingOrchestrator created');
        
        // Test database initialization method
        await orchestrator.initializeDatabase();
        console.log('✅ TradingOrchestrator database initialization completed');
        
        return true;
    } catch (error) {
        console.error('❌ TradingOrchestrator integration test failed:', error);
        return false;
    }
}

// Run all tests
async function runAllTests() {
    console.log('🧪 Starting Database Integration Tests...\n');
    
    const results = {
        databaseInitializer: await testDatabaseInitializer: jest.fn(),
        tradingOrchestrator: await testTradingOrchestratorIntegration()
    };
    
    console.log('\n📊 Test Results:');
    console.log('Database Initializer:', results.databaseInitializer ? '✅ PASS' : '❌ FAIL');
    console.log('Trading Orchestrator:', results.tradingOrchestrator ? '✅ PASS' : '❌ FAIL');
    
    const allPassed = Object.values(results).every(result => result === true);
    console.log('\n🎯 Overall Result:', allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
    
    if (allPassed) {
        console.log('\n🎉 Database integration is working correctly!');
        console.log('The system can now:');
        console.log('  - Load centralized database configuration');
        console.log('  - Initialize all databases with proper settings');
        console.log('  - Create database connections');
        console.log('  - Integrate with TradingOrchestrator');
    }
    
    process.exit(allPassed ? 0 : 1);
}

// Run the tests
runAllTests().catch(error => {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
});

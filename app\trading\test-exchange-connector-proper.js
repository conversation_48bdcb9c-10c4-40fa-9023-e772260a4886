const ccxt = require('ccxt');

/**
 * PROPER EXCHANGE CONNECTOR TESTS
 * These tests validate actual CCXT functionality and real exchange connectivity
 */
class ProperExchangeConnectorTests {
    constructor() {
        this.testResults = [];
        this.connector = null;
    }

    async runAllTests() {
        console.log('🧪 RUNNING PROPER EXCHANGE CONNECTOR TESTS');
        console.log('==========================================');
        console.log('');

        try {
            // Test 1: CCXT Library Availability
            await this.testCCXTAvailability();
            
            // Test 2: Exchange Connector Module
            await this.testExchangeConnectorModule();
            
            // Test 3: Exchange Initialization
            await this.testExchangeInitialization();
            
            // Test 4: Real Market Data Retrieval
            await this.testRealMarketData();
            
            // Test 5: Multiple Symbol Data
            await this.testMultipleSymbolData();
            
            // Test 6: Order Book Functionality
            await this.testOrderBookFunctionality();
            
            // Test 7: Error Handling and Edge Cases
            await this.testErrorHandling();
            
            // Generate Test Report
            this.generateTestReport();
            
        } catch (error) {
            console.log('❌ CRITICAL TEST FAILURE:', error.message);
            throw error;
        }
    }

    async testCCXTAvailability() {
        console.log('📚 Test 1: CCXT Library Availability');
        console.log('------------------------------------');

        try {
            // Test CCXT is available
            if (typeof ccxt !== 'object') {
                throw new Error('CCXT library not available');
            }
            this.addResult('CCXT Available', 'PASS', 'CCXT library is available');

            // Test exchange count
            const exchanges = Object.keys(ccxt).filter(name => 
                typeof ccxt[name] === 'function' && 
                name !== 'Exchange' && 
                name !== 'version'
            );
            
            if (exchanges.length < 100) {
                throw new Error(`Too few exchanges available: ${exchanges.length}`);
            }
            this.addResult('Exchange Count', 'PASS', `${exchanges.length} exchanges available`);

            // Test specific exchanges exist
            const requiredExchanges = ['binance', 'coinbase', 'kraken', 'bybit'];
            for (const exchange of requiredExchanges) {
                if (!ccxt[exchange]) {
                    throw new Error(`Required exchange not available: ${exchange}`);
                }
                this.addResult(`Exchange ${exchange}`, 'PASS', `Exchange ${exchange} is available`);
            }

            // Test exchange instantiation
            const binance = new ccxt.binance({ sandbox: true });
            if (!binance || typeof binance.fetchTicker !== 'function') {
                throw new Error('Cannot instantiate Binance exchange');
            }
            this.addResult('Exchange Instantiation', 'PASS', 'Can instantiate exchange objects');

        } catch (error) {
            this.addResult('CCXT Availability', 'FAIL', error.message);
            throw error;
        }

        console.log('✅ CCXT availability tests passed');
        console.log('');
    }

    async testExchangeConnectorModule() {
        console.log('🔌 Test 2: Exchange Connector Module');
        console.log('-----------------------------------');

        try {
            // Test module loading
            const SimpleExchangeConnector = require('./engines/exchange/SimpleExchangeConnector');
            if (typeof SimpleExchangeConnector !== 'function') {
                throw new Error('SimpleExchangeConnector is not a constructor function');
            }
            this.addResult('Module Loading', 'PASS', 'SimpleExchangeConnector module loads correctly');

            // Test instantiation
            this.connector = new SimpleExchangeConnector({ sandbox: true });
            if (!this.connector || typeof this.connector.initialize !== 'function') {
                throw new Error('Cannot instantiate SimpleExchangeConnector');
            }
            this.addResult('Instantiation', 'PASS', 'SimpleExchangeConnector instantiates correctly');

            // Test required methods exist
            const requiredMethods = [
                'initialize', 'getTicker', 'getMultipleTickers', 
                'getOrderBook', 'placeBuyOrder', 'placeSellOrder', 
                'getBalance', 'isConnected'
            ];
            
            for (const method of requiredMethods) {
                if (typeof this.connector[method] !== 'function') {
                    throw new Error(`Missing required method: ${method}`);
                }
                this.addResult(`Method ${method}`, 'PASS', `Method ${method} exists`);
            }

        } catch (error) {
            this.addResult('Exchange Connector Module', 'FAIL', error.message);
            throw error;
        }

        console.log('✅ Exchange connector module tests passed');
        console.log('');
    }

    async testExchangeInitialization() {
        console.log('🚀 Test 3: Exchange Initialization');
        console.log('---------------------------------');

        try {
            // Test successful initialization
            await this.connector.initialize('binance');
            this.addResult('Binance Init', 'PASS', 'Binance exchange initialized successfully');

            // Test connection status
            if (!this.connector.isConnected('binance')) {
                throw new Error('Exchange not showing as connected after initialization');
            }
            this.addResult('Connection Status', 'PASS', 'Exchange shows as connected');

            // Test exchange instance retrieval
            const exchange = this.connector.getExchange('binance');
            if (!exchange || typeof exchange.fetchTicker !== 'function') {
                throw new Error('Cannot retrieve exchange instance');
            }
            this.addResult('Exchange Instance', 'PASS', 'Can retrieve exchange instance');

            // Test invalid exchange handling
            try {
                await this.connector.initialize('nonexistent_exchange');
                throw new Error('Should have failed for nonexistent exchange');
            } catch (error) {
                if (error.message.includes('not supported')) {
                    this.addResult('Invalid Exchange', 'PASS', 'Correctly handles invalid exchange');
                } else {
                    throw error;
                }
            }

        } catch (error) {
            this.addResult('Exchange Initialization', 'FAIL', error.message);
            throw error;
        }

        console.log('✅ Exchange initialization tests passed');
        console.log('');
    }

    async testRealMarketData() {
        console.log('📈 Test 4: Real Market Data Retrieval');
        console.log('------------------------------------');

        try {
            // Test ticker retrieval for major pair
            const ticker = await this.connector.getTicker('binance', 'BTC/USDT');
            
            // Validate ticker structure
            const requiredFields = ['last', 'percentage', 'baseVolume', 'timestamp'];
            for (const field of requiredFields) {
                if (ticker[field] === undefined) {
                    throw new Error(`Missing ticker field: ${field}`);
                }
                this.addResult(`Ticker.${field}`, 'PASS', `Ticker has ${field} field`);
            }

            // Validate data types and ranges
            if (typeof ticker.last !== 'number' || ticker.last <= 0) {
                throw new Error(`Invalid price: ${ticker.last}`);
            }
            this.addResult('Price Validity', 'PASS', `Valid BTC price: $${ticker.last}`);

            if (typeof ticker.percentage !== 'number' || Math.abs(ticker.percentage) > 100) {
                throw new Error(`Invalid percentage change: ${ticker.percentage}`);
            }
            this.addResult('Change Validity', 'PASS', `Valid change: ${ticker.percentage}%`);

            if (typeof ticker.baseVolume !== 'number' || ticker.baseVolume < 0) {
                throw new Error(`Invalid volume: ${ticker.baseVolume}`);
            }
            this.addResult('Volume Validity', 'PASS', `Valid volume: ${ticker.baseVolume}`);

            // Test timestamp is recent (within last hour)
            const now = Date.now();
            const hourAgo = now - (60 * 60 * 1000);
            if (ticker.timestamp < hourAgo) {
                throw new Error(`Ticker data too old: ${new Date(ticker.timestamp)}`);
            }
            this.addResult('Data Freshness', 'PASS', 'Ticker data is recent');

        } catch (error) {
            this.addResult('Real Market Data', 'FAIL', error.message);
            throw error;
        }

        console.log('✅ Real market data tests passed');
        console.log('');
    }

    async testMultipleSymbolData() {
        console.log('📊 Test 5: Multiple Symbol Data');
        console.log('-------------------------------');

        try {
            // Test multiple tickers
            const symbols = ['BTC/USDT', 'ETH/USDT'];
            const tickers = await this.connector.getMultipleTickers('binance', symbols);
            
            if (typeof tickers !== 'object') {
                throw new Error('Multiple tickers should return an object');
            }
            this.addResult('Tickers Object', 'PASS', 'Multiple tickers returns object');

            // Validate each symbol has data
            for (const symbol of symbols) {
                if (!tickers[symbol]) {
                    throw new Error(`Missing ticker data for ${symbol}`);
                }
                this.addResult(`${symbol} Data`, 'PASS', `Ticker data exists for ${symbol}`);
                
                // Validate structure
                if (typeof tickers[symbol].last !== 'number') {
                    throw new Error(`Invalid price for ${symbol}: ${tickers[symbol].last}`);
                }
                this.addResult(`${symbol} Price`, 'PASS', `Valid price for ${symbol}: $${tickers[symbol].last}`);
            }

            // Test data consistency (prices should be reasonable)
            const btcPrice = tickers['BTC/USDT'].last;
            const ethPrice = tickers['ETH/USDT'].last;
            
            if (btcPrice < ethPrice) {
                throw new Error(`BTC price (${btcPrice}) should be higher than ETH price (${ethPrice})`);
            }
            this.addResult('Price Consistency', 'PASS', 'BTC price is higher than ETH price as expected');

        } catch (error) {
            this.addResult('Multiple Symbol Data', 'FAIL', error.message);
            throw error;
        }

        console.log('✅ Multiple symbol data tests passed');
        console.log('');
    }

    async testOrderBookFunctionality() {
        console.log('📖 Test 6: Order Book Functionality');
        console.log('-----------------------------------');

        try {
            // Test order book retrieval
            const orderBook = await this.connector.getOrderBook('binance', 'BTC/USDT', 10);
            
            // Validate order book structure
            if (!orderBook.bids || !orderBook.asks) {
                throw new Error('Order book missing bids or asks');
            }
            this.addResult('Order Book Structure', 'PASS', 'Order book has bids and asks');

            // Validate bids and asks are arrays
            if (!Array.isArray(orderBook.bids) || !Array.isArray(orderBook.asks)) {
                throw new Error('Bids and asks should be arrays');
            }
            this.addResult('Bids/Asks Arrays', 'PASS', 'Bids and asks are arrays');

            // Validate order book has data
            if (orderBook.bids.length === 0 || orderBook.asks.length === 0) {
                throw new Error('Order book should have bid and ask data');
            }
            this.addResult('Order Book Data', 'PASS', `Order book has ${orderBook.bids.length} bids and ${orderBook.asks.length} asks`);

            // Validate price ordering (bids descending, asks ascending)
            for (let i = 1; i < orderBook.bids.length; i++) {
                if (orderBook.bids[i][0] > orderBook.bids[i-1][0]) {
                    throw new Error('Bids should be in descending price order');
                }
            }
            this.addResult('Bid Ordering', 'PASS', 'Bids are in correct descending order');

            for (let i = 1; i < orderBook.asks.length; i++) {
                if (orderBook.asks[i][0] < orderBook.asks[i-1][0]) {
                    throw new Error('Asks should be in ascending price order');
                }
            }
            this.addResult('Ask Ordering', 'PASS', 'Asks are in correct ascending order');

            // Validate spread (highest bid < lowest ask)
            const highestBid = orderBook.bids[0][0];
            const lowestAsk = orderBook.asks[0][0];
            if (highestBid >= lowestAsk) {
                throw new Error(`Invalid spread: highest bid (${highestBid}) >= lowest ask (${lowestAsk})`);
            }
            this.addResult('Spread Validity', 'PASS', `Valid spread: ${((lowestAsk - highestBid) / highestBid * 100).toFixed(4)}%`);

        } catch (error) {
            this.addResult('Order Book Functionality', 'FAIL', error.message);
            throw error;
        }

        console.log('✅ Order book functionality tests passed');
        console.log('');
    }

    async testErrorHandling() {
        console.log('🚨 Test 7: Error Handling and Edge Cases');
        console.log('----------------------------------------');

        try {
            // Test invalid symbol
            try {
                await this.connector.getTicker('binance', 'INVALID/SYMBOL');
                throw new Error('Should have failed for invalid symbol');
            } catch (error) {
                if (error.message.includes('symbol') || error.message.includes('market')) {
                    this.addResult('Invalid Symbol', 'PASS', 'Correctly handles invalid symbol');
                } else {
                    throw new Error(`Unexpected error for invalid symbol: ${error.message}`);
                }
            }

            // Test uninitialized exchange
            try {
                await this.connector.getTicker('uninitialized', 'BTC/USDT');
                throw new Error('Should have failed for uninitialized exchange');
            } catch (error) {
                if (error.message.includes('not initialized')) {
                    this.addResult('Uninitialized Exchange', 'PASS', 'Correctly handles uninitialized exchange');
                } else {
                    throw new Error(`Unexpected error for uninitialized exchange: ${error.message}`);
                }
            }

            // Test available exchanges method
            const availableExchanges = this.connector.getAvailableExchanges();
            if (!Array.isArray(availableExchanges) || availableExchanges.length < 100) {
                throw new Error(`Invalid available exchanges: ${availableExchanges.length}`);
            }
            this.addResult('Available Exchanges', 'PASS', `${availableExchanges.length} exchanges available`);

        } catch (error) {
            this.addResult('Error Handling', 'FAIL', error.message);
            throw error;
        }

        console.log('✅ Error handling tests passed');
        console.log('');
    }

    addResult(testName, status, message) {
        this.testResults.push({
            test: testName,
            status: status,
            message: message,
            timestamp: new Date().toISOString()
        });
    }

    generateTestReport() {
        console.log('📊 TEST REPORT');
        console.log('==============');
        console.log('');

        const passed = this.testResults.filter(r => r.status === 'PASS').length;
        const failed = this.testResults.filter(r => r.status === 'FAIL').length;
        const total = this.testResults.length;

        console.log(`📈 Total Tests: ${total}`);
        console.log(`✅ Passed: ${passed}`);
        console.log(`❌ Failed: ${failed}`);
        console.log(`📊 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
        console.log('');

        if (failed > 0) {
            console.log('❌ FAILED TESTS:');
            this.testResults.filter(r => r.status === 'FAIL').forEach(result => {
                console.log(`  ❌ ${result.test}: ${result.message}`);
            });
            console.log('');
        }

        if (passed === total) {
            console.log('🎉 ALL TESTS PASSED - EXCHANGE CONNECTOR IS WORKING CORRECTLY!');
            console.log('🔗 Real-time market data retrieval is fully functional');
            console.log('💱 Exchange connectivity is operational');
        } else {
            console.log('⚠️  SOME TESTS FAILED - EXCHANGE CONNECTOR HAS ISSUES!');
        }
    }
}

// Run tests if called directly
if (require.main === module) {
    const tests = new ProperExchangeConnectorTests();
    tests.runAllTests().catch(console.error);
}

module.exports = ProperExchangeConnectorTests;

/**
 * Test Runner for Trading System
 * Runs all integration tests and validates system functionality
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class TestRunner {
  constructor() {
    this.testSuites = [
      'integration/trading-system.test.js',
      'unit/grid-bot.test.js',
      'unit/risk-manager.test.js',
      'unit/whale-tracker.test.js',
      'unit/meme-scanner.test.js',
    ];


    this.results = {
      passed: 0,
      failed: 0,
      skipped: 0,
      errors: [],
    };
  }

  async run() {
    console.log('🧪 Starting Trading System Test Suite\n');

    // Check if jest is available
    const jestPath = this.findJest();
    if (!jestPath) {
      console.error('❌ Jest not found. Please install jest install --save-dev jest');
      process.exit(1);
    }

    // Run tests
    for (const suite of this.testSuites) {
      const testPath = path.join(__dirname, suite);

      // Skip if test file doesn't exist
      if (!fs.existsSync(testPath)) {
        console.log(`⏭️  Skipping ${suite} (file not found)`);
        // this.results.skipped++;
        continue;
      }

      console.log(`\n📋 Running ${suite}...`);

      try {
        await this.runTestSuite(jestPath, testPath);
        // this.results.passed++;
        console.log(`✅ ${suite} passed`);
      } catch (error) {
        this.results.failed++;
        this.results.errors.push({
          suite: suite,
          error: error.message,
        });
        console.error(`❌ ${suite} failed:`, error.message);
      }
    }

    // Print summary
    // this.printSummary();

    // Exit with appropriate code
    process.exit(this.results.failed > 0 ? 1 : 0);
  }

  findJest() {
    // Look for jest in various locations
    const possiblePaths = [
      path.join(__dirname, '../../../node_modules/.bin/jest'),
      path.join(__dirname, '../../node_modules/.bin/jest'),
      path.join(__dirname, '../node_modules/.bin/jest'),
      'jest', // Global installation
    ];

    for (const jestPath of possiblePaths) {
      if (fs.existsSync(jestPath) || this.commandExists(jestPath)) {
        return jestPath;
      }
    }

    return null;
  }

  commandExists(command) {
    try {
      require('child_process').execSync(`which ${command}`, { stdio: 'pipe' });
      return true;
    } catch {
      return false;
    }
  }

  runTestSuite(jestPath, testPath) {
    return new Promise((resolve, reject) => {
      const args = [
        testPath,
        '--verbose',
        '--no-coverage',
        '--testTimeout=30000'];


      const jest = spawn(jestPath, args, {
        stdio: 'inherit',
        env: {
          ...process.env,
          NODE_ENV: 'test',
          TRADING_MODE: 'simulation',
        },
      });

      jest.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Test exited with code ${code}`));
        }
      });

      jest.on('error', (error) => {
        reject(error);
      });
    });
  }

  printSummary() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 TEST SUMMARY');
    console.log('='.repeat(50));
    console.log(`✅ Passed:  ${this.results.passed}`);
    console.log(`❌ Failed:  ${this.results.failed}`);
    console.log(`⏭️  Skipped: ${this.results.skipped}`);

    if (this.results.errors.length > 0) {
      console.log('\n❌ FAILURES:');
      this.results.errors.forEach(({ suite, error }) => {
        console.log(`  - ${suite}: ${error}`);
      });
    }

    console.log('='.repeat(50));

    if (this.results.failed === 0 && this.results.passed > 0) {
      console.log('\n🎉 All tests passed!');
    } else if (this.results.failed > 0) {
      console.log('\n⚠️  Some tests failed. Please check the errors above.');
    }
  }
}

// Alternative test runner using Node's built-in test runner (if Jest is not available)
class NodeTestRunner {
  run() {
    console.log('🧪 Running tests with Node.js test runner\n');

    try {
      // Run the integration test directly
      require('./integration/trading-system.test.js');
      console.log('\n✅ Tests completed successfully');
    } catch (error) {
      console.error('\n❌ Tests failed:', error);
      process.exit(1);
    }
  }
}

// Quick validation tests (runs without Jest)
function runQuickValidation() {
  console.log('🚀 Running quick validation tests...\n');

  const tests = {
    'Module Loading': () => {
      require('../index');
      require('../TradingOrchestrator');
      require('../autonomous-startup');
    },

    'Database Connectivity': () => {
      const DatabaseHelper = require('../engines/trading/DatabaseHelper');
      DatabaseHelper.initialize();
    },

    'Configuration Loading': () => {
      const ConfigManager = require('../config/index');
      const config = ConfigManager.getInstance();
      if (!config) throw new Error('Config manager failed to initialize');
    },

    'Logger Functionality': () => {
      const logger = require('../shared/helpers/logger');
      logger.info('Test log message');
    },

    'Risk Manager Loading': () => {
      const RiskManager = require('../engines/shared/risk/UnifiedRiskManager');
      if (!RiskManager) throw new Error('Risk manager not found');
    },
  };

  let passed = 0;
  let failed = 0;

  for (const [testName, testFn] of Object.entries(tests)) {
    try {
      testFn();
      console.log(`✅ ${testName}`);
      passed++;
    } catch (error) {
      console.log(`❌ ${testName}: ${error.message}`);
      failed++;
    }
  }

  console.log(`\n📊 Quick Validation: ${passed} passed, ${failed} failed`);
  return failed === 0;
}

// Main execution
async function main() {
  const args = process.argv.slice(2);

  if (args.includes('--quick')) {
    // Run quick validation only
    const success = await runQuickValidation();
    process.exit(success ? 0 : 1);
  } else if (args.includes('--node')) {
    // Use Node's test runner
    const runner = new NodeTestRunner();
    await runner.run();
  } else {
    // Use Jest if available
    const runner = new TestRunner();
    await runner.run();
  }
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled rejection:', error);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught exception:', error);
  process.exit(1);
});

// Run tests
main().catch((error) => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});

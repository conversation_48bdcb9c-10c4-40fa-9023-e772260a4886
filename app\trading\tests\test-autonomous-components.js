#!/usr/bin/env node

/**
 * Test script to verify all autonomous trading system components are working correctly
 */

console.log('🔧 Testing Autonomous Trading System Components...\n');

const components = [
    {name: 'MemeCoinPatternAnalyzer', path: '../engines/analysis/MemeCoinPatternAnalyzer'},
    {name: 'ComprehensiveWalletTracker', path: '../engines/analysis/ComprehensiveWalletTracker'},
    {name: 'SmartMoneyDetector', path: '../engines/analysis/SmartMoneyDetector'},
    {name: 'HistoricalPriceTracker', path: '../engines/data-collection/HistoricalPriceTracker'},
    {name: 'ExitLiquidityProtector', path: '../engines/protection/ExitLiquidityProtector'},
    {name: 'BlockchainTransactionAnalyzer', path: '../engines/analysis/BlockchainTransactionAnalyzer'},
    {name: 'NewListingDetector', path: '../engines/data-collection/NewListingDetector'},
    {name: 'PumpDetectionEngine', path: '../engines/analysis/PumpDetectionEngine'},
    {name: 'CoinAgeValidator', path: '../engines/validation/CoinAgeValidator'},
    {name: 'SocialSentimentAnalyzer', path: '../engines/analysis/SocialSentimentAnalyzer'},
    {name: 'EntryTimingEngine', path: '../engines/analysis/EntryTimingEngine'},
    {name: 'NewCoinDecisionEngine', path: '../engines/analysis/NewCoinDecisionEngine'}];

let successCount = 0;
let failureCount = 0;
const failedComponents = [];

console.log('Testing component loading and instantiation...\n');

for (const component of components) {
    try {
        // Test loading
        const ComponentClass = require(component.path);

        if (typeof ComponentClass !== 'function') {
            throw new Error(`Component ${component.name} does not export a constructor function (got ${typeof ComponentClass})`);
        }

        // Test instantiation
        const instance = new ComponentClass();

        if (typeof instance !== 'object' || instance === null) {
            throw new Error(`Component ${component.name} instantiation failed (got ${typeof instance})`);
        }

        console.log(`✅ ${component.name}aded and instantiated successfully`);
        console.log(`   └─ Type: ${typeof ComponentClass} -> ${typeof instance}`);

        successCount++;
    } catch (error) {
        console.log(`❌ ${component.name}iled`);
        console.log(`   └─ Error: ${error.message}`);
        failedComponents.push({namemponent.name, error});
        failureCount++;
    }
    console.log('');
}

console.log('\n=== AUTONOMOUS TRADING SYSTEM TEST RESULTS ===');
console.log(`✅ Successful: ${successCount}/${components.length} components`);
console.log(`❌ Failed: ${failureCount}/${components.length} components`);

if (failedComponents.length > 0) {
    console.log('\n🔍 Failed Components:');
    failedComponents.forEach(fail => {
        console.log(`- ${fail.name}: ${fail.error}`);
    });

    console.log('\n⚠️  Some components need attention before system can be deployed.');
    process.exit(1);
} else {
    console.log('\n🎉 All autonomous trading system components are working correctly!');
    console.log('📋 System is ready for integration with TradingOrchestrator.');
    process.exit(0);
}

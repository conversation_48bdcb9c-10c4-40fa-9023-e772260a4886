'use strict';

/**
 * Backend Error Testing Script
 * Tests for critical backend errors and functionality
 */
const TradingSystemInterface = require('./index.js');

async function testBackend() {
  console.log('🔍 Testing Backend for Errors...\n');
  const errors = [];
  const warnings = [];
  const successes = [];

  // Test 1 Instantiation
  try {
    const system = new TradingSystemInterface();
    successes.push('✅ Trading system can be instantiated');
    // Test 2 Methods
    try {
      const _status = system.getBotStatus();
      successes.push('✅ getBotStatus() method works');
    } catch (e) {
      errors.push(`❌ getBotStatus() failed: ${e.message}`);
    }
    try {
      const _settings = system.getSettings();
      successes.push('✅ getSettings() method works');
    } catch (e) {
      errors.push(`❌ getSettings() failed: ${e.message}`);
    }
    try {
      const _coins = system.getCoins();
      successes.push('✅ getCoins() method works');
    } catch (e) {
      errors.push(`❌ getCoins() failed: ${e.message}`);
    }

    // Test 3 Initialization
    try {
      await system.initialize();
      successes.push('✅ System initialization completed');
    } catch (e) {
      errors.push(`❌ System initialization failed: ${e.message}`);
    }

    // Test 4 Check
    try {
      const _health = system.healthCheck();
      successes.push('✅ Health check works');
    } catch (e) {
      errors.push(`❌ Health check failed: ${e.message}`);
    }

    // Test 5 Methods
    try {
      const _balance = await system.getWalletBalance();
      successes.push('✅ getWalletBalance() works');
    } catch (e) {
      errors.push(`❌ getWalletBalance() failed: ${e.message}`);
    }
    try {
      const _positions = await system.getGridPositions();
      successes.push('✅ getGridPositions() works');
    } catch (e) {
      errors.push(`❌ getGridPositions() failed: ${e.message}`);
    }
    try {
      const _whales = await system.getTrackedWhales();
      successes.push('✅ getTrackedWhales() works');
    } catch (e) {
      errors.push(`❌ getTrackedWhales() failed: ${e.message}`);
    }
    try {
      const _scanner = await system.getScannerStatus();
      successes.push('✅ getScannerStatus() works');
    } catch (e) {
      errors.push(`❌ getScannerStatus() failed: ${e.message}`);
    }
  } catch (e) {
    errors.push(`❌ Critical error instantiate trading system - ${e.message}`);
  }

  // Print Results
  console.log('\n=== BACKEND TEST RESULTS ===\n');
  if (successes.length > 0) {
    console.log('🟢 SUCCESSES:');
    successes.forEach(success => console.log(`  ${success}`));
    console.log('');
  }
  if (warnings.length > 0) {
    console.log('🟡 WARNINGS:');
    warnings.forEach(warning => console.log(`  ${warning}`));
    console.log('');
  }
  if (errors.length > 0) {
    console.log('🔴 ERRORS:');
    errors.forEach(error => console.log(`  ${error}`));
    console.log('');
  }
  console.log(`📊 SUMMARY: ${successes.length} successes, ${warnings.length} warnings, ${errors.length} errors`);
  if (errors.length === 0) {
    console.log('🎉 Backend appears to be working correctly!');
  } else {
    console.log('⚠️ Backend has some issues that need attention.');
  }
}

// Run the test
if (require.main === module) {
  testBackend().catch(error => {
    console.error('Test runner failed:', error);
  });
}
module.exports = testBackend;

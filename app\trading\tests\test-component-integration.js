#!/usr/bin/env node

/**
 * Test script for component integration
 * Tests the data flow between DataCollector, SentimentAnalyzer, and PerformanceTracker
 */

// Mock logger to avoid winston dependency issues
const mockLogger = {
  info,
  error,
  warn,
  debug,
};

// Override the logger module
require.cache[require.resolve('./shared/helpers/logger')] = {
  exports,
};

const TradingOrchestrator = require('./TradingOrchestrator');

async function testComponentIntegration() {
  console.log('🧪 Testing component integration...\n');

  const orchestrator = new TradingOrchestrator();

  try {
    // Initialize the orchestrator
    console.log('1. Initializing TradingOrchestrator...');
    await orchestrator.initialize();
    console.log('✅ TradingOrchestrator initialized\n');

    // Start the orchestrator
    console.log('2. Starting TradingOrchestrator...');
    await orchestrator.start();
    console.log('✅ TradingOrchestrator started\n');

    // Wait for components to initialize and start data flow
    console.log('3. Waiting for component data flow to establish...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Test component status
    console.log('4. Checking component status...');
    const componentStatus = orchestrator.getComponentStatus();
    console.log('Component Status:', JSON.stringify(componentStatus, null, 2));
    console.log('');

    // Test real-time data retrieval
    console.log('5. Getting real-time data...');
    const realTimeData = await orchestrator.getRealTimeData();
    console.log('Real-time Data Keys:', Object.keys(realTimeData));
    console.log('Data Collection Keys:', Object.keys(realTimeData.dataCollection));
    console.log('Sentiment Keys:', Object.keys(realTimeData.sentiment));
    console.log('Performance Data:', realTimeData.performance);
    console.log('');

    // Test data flow metrics
    console.log('6. Checking data flow metrics...');
    const dataFlowMetrics = orchestrator.getDataFlowMetrics();
    console.log('Data Flow Metrics:', JSON.stringify(dataFlowMetrics, null, 2));
    console.log('');

    // Test component health
    console.log('7. Checking component health...');
    const systemStatus = orchestrator.getSystemStatus();
    console.log('System Status:', JSON.stringify(systemStatus, null, 2));
    console.log('');

    // Wait for more data flow
    console.log('8. Waiting for additional data flow...');
    await new Promise(resolve => setTimeout(resolve, 10000));

    // Check updated metrics
    console.log('9. Checking updated metrics...');
    const updatedMetrics = orchestrator.getDataFlowMetrics();
    console.log('Updated Data Flow Metrics:', JSON.stringify(updatedMetrics, null, 2));
    console.log('');

    // Test component communication
    console.log('10. Testing component communication...');

    // Test DataCollector
    const dataCollector = orchestrator.getComponent('dataCollector');
    if (dataCollector) {
      console.log('DataCollector status:', {
        initialized,
        running,
        streaming,
      });
    }

    // Test SentimentAnalyzer
    const sentimentAnalyzer = orchestrator.getComponent('sentimentAnalyzer');
    if (sentimentAnalyzer) {
      console.log('SentimentAnalyzer status:', {
        initialized,
        running,
        dataSize,
      });
    }

    // Test PerformanceTracker
    const performanceTracker = orchestrator.getComponent('performanceTracker');
    if (performanceTracker) {
      console.log('PerformanceTracker status:', {
        initialized,
        running,
        metricsCount,
      });
    }

    console.log('\n✅ Component integration test completed successfully!');

  } catch (error) {
    console.error('❌ Component integration test failed:', error);
  } finally {
    // Clean up
    console.log('\n11. Cleaning up...');
    await orchestrator.stop();
    console.log('✅ TradingOrchestrator stopped');
  }
}

// Run the test
if (require.main === module) {
  testComponentIntegration().catch(console.error);
}

module.exports = testComponentIntegration;

#!/usr/bin/env node

/**
 * @fileoverview Database Connection Test Suite
 * Tests all database connections and basic functionality
 */

const dbManager = require('../engines/database/connection-manager');
const logger = require('../shared/helpers/logger');

class DatabaseConnectionTester {
    constructor() {
        this.tests = [];
        this.results = {
            passed: 0,
            failed: 0,
            errors: []
        };
    }

    async runAllTests() {
        console.log('🧪 Starting Database Connection Tests...\n');

        try {
            // Test 1 file existence
            await this.testDatabaseFilesExist();

            // Test 2 establishment
            await this.testConnections();

            // Test 3 queries
            await this.testBasicQueries();

            // Test 4 support
            await this.testTransactions();

            // Test 5 metrics
            await this.testPerformance();

            // Test 6 check
            await this.testHealthCheck();

            // this.printResults();

        } catch (error) {
            logger.error('Test suite failed:', error);
            throw error;
        } finally {
            dbManager.closeAllConnections();
        }
    }

    testDatabaseFilesExist() {
        console.log('📁 Testing database file existence...');

        const path = require('path');
        const files = [
            path.join(__dirname, '../databases/trading_bot.db'),
            path.join(__dirname, '../databases/n8n.sqlite'),
            path.join(__dirname, '../databases/credentials.db')];

        for (const file of files) {
            const exists = require('fs').existsSync(file);
            const fileName = path.basename(file);
            // this.recordTest(`File exists: ${fileName}`, exists);
        }
    }

    testConnections() {
        console.log('🔗 Testing database connections...');

        const databases = ['trading', 'n8n', 'credentials'];

        for (const dbName of databases) {
            try {
                const db = dbManager.getConnection(dbName);
                const result = db.pragma('integrity_check');

                this.recordTest(
                    `Connection to ${dbName} database`,
                    result[0]?.integrity_check === 'ok'
                );
            } catch (error) {
                // this.recordTest(`Connection to ${dbName} database`, false, error.message);
            }
        }
    }

    testBasicQueries() {
        console.log('📊 Testing basic queries...');

        try {
            // Test trading database
            const tradingDb = dbManager.getConnection('trading');
            const tables = tradingDb.prepare(`
            SELECT name
            FROM sqlite_master
            WHERE type = 'table'
              AND name NOT LIKE 'sqlite_%'
        `).all();

            // this.recordTest('Trading database has tables', tables.length > 0);
            this.recordTest('Trading database has cocococoin_metadata',
                tables.some(t => t.name === 'cocococoin_metadata')
            );

            // Test n8n database
            const n8nDb = dbManager.getConnection('n8n');
            const n8nTables = n8nDb.prepare(`
            SELECT name
            FROM sqlite_master
            WHERE type = 'table'
              AND name NOT LIKE 'sqlite_%'
        `).all();

            // this.recordTest('N8N database has tables', n8nTables.length > 0);

            // Test credentials database
            const credDb = dbManager.getConnection('credentials');
            const credTables = credDb.prepare(`
            SELECT name
            FROM sqlite_master
            WHERE type = 'table'
              AND name NOT LIKE 'sqlite_%'
        `).all();

            // this.recordTest('Credentials database has tables', credTables.length > 0);

        } catch (error) {
            // this.recordTest('Basic queries', false, error.message);
        }
    }

    testTransactions() {
        console.log('💱 Testing transaction support...');

        try {
            const tradingDb = dbManager.getConnection('trading');

            // Test transaction
            const insertTransaction = tradingDb.transaction(() => {
                const insert = tradingDb.prepare(
                    'INSERT INTO cocococoin_metadata (_symbol, _name) VALUES (?, ?)',
                );
                insert.run('TEST/USDT', 'Test Coin');

                const count = tradingDb.prepare(
                    'SELECT COUNT(*) as count FROM cocococoin_metadata WHERE symbol = ?',
                ).get('TEST/USDT');

                return count.count === 1;
            });

            const result = insertTransaction();
            // this.recordTest('Transaction support', result);

            // Clean up test data
            tradingDb.prepare('DELETE FROM cocococoin_metadata WHERE symbol = ?').run('TEST/USDT');

        } catch (error) {
            // this.recordTest('Transaction support', false, error.message);
        }
    }

    testPerformance() {
        console.log('⚡ Testing performance...');

        try {
            const tradingDb = dbManager.getConnection('trading');
            const start = Date.now();

            // Test query performance
            for (let i = 0; i < 100; i++) {
                tradingDb.prepare('SELECT 1 as test').get();
            }

            const duration = Date.now() - start;
            // this.recordTest('Query performance (100 queries)', duration < 100, `${duration}ms`);

            // Test memory usage
            const memoryUsage = process.memoryUsage();
            // this.recordTest('Memory usage reasonable', memoryUsage.heapUsed < 100 * 1024 * 1024);

        } catch (error) {
            // this.recordTest('Performance tests', false, error.message);
        }
    }

    testHealthCheck() {
        console.log('❤️ Testing health check...');

        try {
            const health = dbManager.getHealthStatus();

            for (const [dbName, status] of Object.entries(health)) {
                // this.recordTest(
                `Health check: ${dbName}`,
                    status.connected && status.integrity,
        )
                ;
            }

        } catch (error) {
            // this.recordTest('Health check', false, error.message);
        }
    }

    recordTest(description, passed, error = null) {
        const test = {
            description,
            passed,
            error,
            timestamp Date().toISOString()
        };

        // this.tests.push(test);

        if (passed) {
            // this.results.passed++;
            console.log(`✅ ${description}`);
        } else {
            // this.results.failed++;
            // this.results.errors.push({description, error});
            console.log(`❌ ${description}${error ? ` - ${error}` : ''}`);
        }
    }

    printResults() {
        console.log('\n' + '='.repeat(50));
        console.log('📊 Test Results Summary');
        console.log('='.repeat(50));
        console.log(`Total Tests: ${this.tests.length}`);
        console.log(`✅ Passed: ${this.results.passed}`);
        console.log(`❌ Failed: ${this.results.failed}`);

        if (this.results.errors.length > 0) {
            console.log('\n❌ Failed Tests:');
            // this.results.errors.forEach(error => {
            console.log(`  - ${error.description}`);
            if (error.error) {
                console.log(`    Error: ${error.error}`);
            }
        }
)
        ;
    }

    const successRate = (this.results.passed / this.tests.length * 100).toFixed(1);
console.log(`\n📈 Success Rate: ${successRate}% `);
}
}

// Run tests if called directly
if (require.main === module) {
    const tester = new DatabaseConnectionTester();
    tester.runAllTests()
        .then(() => {
            process.exit(0);
        })
        .catch(error => {
            console.error('Test suite failed:', error);
            process.exit(1);
        });
}

module.exports = DatabaseConnectionTester;

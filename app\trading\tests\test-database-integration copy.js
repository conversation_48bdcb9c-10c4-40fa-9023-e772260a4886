'use strict';

/**
 * Test script for database integration with TradingOrchestrator
 */
const TradingOrchestrator = require('./TradingOrchestrator');
const _path = require('path');

async function testDatabaseIntegration() {
  console.log('🧪 Testing Database Integration with TradingOrchestrator...\n');
  const orchestrator = new TradingOrchestrator();
  try {
    // Test 1 the orchestrator (should initialize databases)
    console.log('📊 Test 1 TradingOrchestrator...');
    const initResult = await orchestrator.initialize();
    if (initResult.success) {
      console.log('✅ Initialization successful');
      console.log(`   - Database result: ${initResult.databaseResult.success ? 'SUCCESS' : 'FAILED'}`);
      console.log(`   - Health check: ${initResult.healthCheck.healthy ? 'HEALTHY' : 'UNHEALTHY'}`);
      console.log(`   - Databases initialized: ${Object.keys(initResult.databaseResult.databases).length}`);
    } else {
      console.log('❌ Initialization failed');
      return;
    }

    // Test 2 system status
    console.log('\n📊 Test 2 system status...');
    const status = orchestrator.getStatus();
    console.log('✅ System Status:');
    console.log(`   - Initialized: ${status.initialized}`);
    console.log(`   - Database initialized: ${status.databaseInitialized}`);
    console.log(`   - Database connections: ${Object.keys(status.databases).length}`);
    console.log(`   - Initialization stage: ${status.initializationProgress.stage}`);

    // Test 3 detailed database status
    console.log('\n📊 Test 3 detailed database status...');
    const dbStatus = await orchestrator.getDatabaseStatus();
    console.log('✅ Database Status:');
    console.log(`   - Initialized: ${dbStatus.initialized}`);
    console.log(`   - Monitoring: ${dbStatus.monitoring}`);
    console.log(`   - Healthy: ${dbStatus.healthy}`);
    console.log(`   - Total databases: ${Object.keys(dbStatus.databases).length}`);
    if (dbStatus.unhealthy && dbStatus.unhealthy.length > 0) {
      console.log(`   - Unhealthy databases: ${dbStatus.unhealthy.map(db => db.name).join(', ')}`);
    }

    // Test 4 if databases are ready
    console.log('\n📊 Test 4 database readiness...');
    const isReady = orchestrator.isDatabaseReady();
    console.log(`✅ Database ready for trading: ${isReady}`);

    // Test 5 database metrics
    console.log('\n📊 Test 5 database metrics...');
    const metrics = orchestrator.getDatabaseMetrics();
    console.log('✅ Database Metrics:');
    console.log(`   - Total databases: ${metrics.totalDatabases}`);
    console.log(`   - Healthy databases: ${metrics.healthyDatabases}`);
    console.log(`   - Unhealthy databases: ${metrics.unhealthyDatabases}`);
    console.log(`   - Monitoring active: ${metrics.monitoring}`);

    // Test 6 health report
    console.log('\n📊 Test 6 health report...');
    const healthReport = await orchestrator.generateDatabaseHealthReport();
    console.log('✅ Health Report Generated:');
    console.log(`   - Overall health: ${healthReport.summary.overallHealth}`);
    console.log(`   - Total databases: ${healthReport.summary.totalDatabases}`);
    console.log(`   - Healthy: ${healthReport.summary.healthyDatabases}`);
    console.log(`   - Unhealthy: ${healthReport.summary.unhealthyDatabases}`);

    // Test 7 start/stop functionality
    console.log('\n📊 Test 7 start/stop functionality...');
    const startResult = await orchestrator.start();
    console.log(`✅ Start result: ${startResult.success ? 'SUCCESS' : 'FAILED'}`);
    const stopResult = await orchestrator.stop();
    console.log(`✅ Stop result: ${stopResult.success ? 'SUCCESS' : 'FAILED'}`);
    console.log('\n🎉 All database integration tests completed successfully!');
  } catch (error) {
    console.error('\n❌ Database integration test failed:', error.message);
    console.error('Stack trace:', error.stack);

    // Try to stop the orchestrator if it was started
    try {
      await orchestrator.stop();
    } catch (stopError) {
      console.error('Failed to stop orchestrator:', stopError.message);
    }
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testDatabaseIntegration().then(() => {
    console.log('\n✅ Database integration test completed');
    process.exit(0);
  }).catch(error => {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  });
}
module.exports = testDatabaseIntegration;

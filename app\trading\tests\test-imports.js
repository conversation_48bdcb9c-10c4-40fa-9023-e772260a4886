#!/usr/bin/env node

/**
 * Test script to verify all imports in TradingOrchestrator are working correctly
 */

console.log('Testing imports for TradingOrchestrator...\n');

const testImports = [
  { name: 'GridBotManager', path: './engines/trading/GridBotManager' },
  { name: 'Elite.WhaleTracker', path: './engines/trading/Elite.WhaleTracker' },
  { name: 'MemeCoinScanner', path: './engines/trading/MemeCoinScanner' },
  { name: 'ProductionTradingExecutor', path: './engines/trading/ProductionTradingExecutor' },
  { name: 'PortfolioManager', path: './engines/trading/PortfolioManager' },
  { name: 'PerformanceTracker', path: './engines/analysis/PerformanceTracker' },
  { name: 'SentimentAnalyzer', path: './engines/analysis/SentimentAnalyzer' },
  { name: 'EnhancedDataCollector', path: './engines/data-collection/enhanced-data-collector' },
  { name: 'EventCoordinator', path: './engines/shared/orchestration/event-coordinator' },
  { name: 'UnifiedRiskManager', path: './shared/risk/UnifiedRiskManager' },
  { name: 'CircuitBreakerSystem', path: './shared/safety/circuit-breakers' },
  { name: 'LLMCoordinator', path: './shared/ai/llm-coordinator' },
  { name: 'ConfigurationManager', path: '../config/ConfigurationManager' },
  { name: 'logger', path: './shared/helpers/logger' },
  { name: 'DatabaseManager', path: './shared/helpers/database-manager' }];

const failedImports = [];
const successfulImports = [];

for (const testImport of testImports) {
  try {
    const module = require(testImport.path);
    console.log(`✅ ${testImport.name} successfully imported from ${testImport.path}`);
    // Check if it's a class (constructor function) or an object
    if (typeof module === 'function') {
      console.log('   Type/Constructor');
    } else if (typeof module === 'object') {
      console.log('   Type/Instance');
    } else {
      console.log(`   Type: ${typeof module}`);
    }
    successfulImports.push(testImport.name);
  } catch (error) {
    console.log(`❌ ${testImport.name} failed to import from ${testImport.path}`);
    console.log(`   Error: ${error.message}`);
    failedImports.push({ name: testImport.name, error: error });
  }
  console.log('');
}
console.log('\n=== SUMMARY ===');
console.log(`Successful imports: ${successfulImports.length}/${testImports.length}`);
console.log(`Failed imports: ${failedImports.length}/${testImports.length}`);

if (failedImports.length > 0) {
  console.log('\nFailed imports:');
  failedImports.forEach(fail => {
    console.log(`- ${fail.name}: ${fail.error}`);
  });
  process.exit(1);
} else {
  console.log('\n✅ All imports are working correctly!');
  process.exit(0);
}

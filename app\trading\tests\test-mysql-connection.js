const mysql = require('mysql2/promise');

async function testMySQLConnection() {
    const config = {
        host: 'localhost',
        port: 3306,
        user: 'admin',
        password: 'admin',
        database: 'trade',
        connectTimeout: 60000
    };

    try {
        console.log('Attempting to connect to MySQL...');
        console.log('Config:', {
            host: config.host,
            port: config.port,
            user: config.user,
            database: config.database
        });

        const connection = await mysql.createConnection(config);
        console.log('✅ MySQL connection successful!');

        // Test a simple query
        const [rows] = await connection.execute('SELECT 1 as test');
        console.log('✅ Test query successful:', rows);

        await connection.end();
        console.log('✅ Connection closed successfully');

    } catch (err) {
        console.error('❌ MySQL connection failed:');
        console.error('Full error:', err);

        if (err && typeof err === 'object' && 'code' in err) {
            console.error('Error code:', err.code);
            if (err.code === 'ECONNREFUSED') {
                console.log('\n💡 Possible solutions:');
                console.log('1. Make sure MySQL server is running');
                console.log('2. Check if MySQL is listening on port 3319');
                console.log('3. Verify your MySQL credentials');
                console.log('4. Check firewall settings');
            }
        }
    }
}

testMySQLConnection();

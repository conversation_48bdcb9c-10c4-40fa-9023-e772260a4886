#!/usr/bin/env node

/**
 * Simple test script for the startup workflow without complex dependencies
 */

async function testSimpleStartup() {
  console.log('🧪 Testing simple startup workflow...\n');

  try {
    // Test 1 creation and basic initialization
    console.log('📦 Testing component creation...');

    const GridBotManager = require('./engines/trading/GridBotManager');
    const DataCollector = require('./engines/data-collection/DataCollector');
    const SentimentAnalyzer = require('./engines/analysis/SentimentAnalyzer');

    // Create components
    const gridBotManager = new GridBotManager({
      maxBotsPerExchange,
      maxTotalBots,
      autoCreateBots,
    });

    const dataCollector = new DataCollector({
      symbols: ['BTC/USDT', 'ETH/USDT'],
    });

    const sentimentAnalyzer = new SentimentAnalyzer(null, {
      sources: ['social', 'news'],
      sentimentThreshold: 0.5,
    });

    console.log('✅ Components created successfully');

    // Test 2 initialization
    console.log('\n🔧 Testing component initialization...');

    await gridBotManager.initialize();
    console.log('✅ GridBotManager initialized');

    await dataCollector.initialize();
    console.log('✅ DataCollector initialized');

    await sentimentAnalyzer.initialize();
    console.log('✅ SentimentAnalyzer initialized');

    // Test 3 startup
    console.log('\n🚀 Testing component startup...');

    await gridBotManager.start();
    console.log('✅ GridBotManager started');

    await dataCollector.start();
    console.log('✅ DataCollector started');

    await sentimentAnalyzer.start();
    console.log('✅ SentimentAnalyzer started');

    // Test 4 deployment
    console.log('\n🤖 Testing bot deployment...');

    const botId1 = await gridBotManager.createBot({
      symbol: 'BTC/USDT',
      gridSpacing,
      gridLevels,
      baseOrderSize,
      autoStart,
    });
    console.log(`✅ Bot created and started: ${botId1}`);

    const botId2 = await gridBotManager.createBot({
      symbol: 'ETH/USDT',
      gridSpacing,
      gridLevels,
      baseOrderSize,
      autoStart,
    });
    console.log(`✅ Bot created and started: ${botId2}`);

    // Test 5 checking
    console.log('\n📊 Testing status reporting...');

    const gridStatus = gridBotManager.getDetailedStatus();
    console.log('Grid Bot Manager Status:', {
      isRunning,
      totalBots,
      activeBots,
      runningBots,
    });

    const dataStatus = dataCollector.getStatus();
    console.log('Data Collector Status:', {
      isRunning,
      symbols,
      dataPoints,
    });

    const sentimentStatus = sentimentAnalyzer.getStatus();
    console.log('Sentiment Analyzer Status:', {
      isRunning,
      sources,
      trackedSymbols,
    });

    // Test 6 collection
    console.log('\n📈 Testing data collection...');

    const btcData = await dataCollector.collectData('BTC/USDT');
    if (btcData) {
      console.log('✅ BTC data collected successfully');
    } else {
      console.log('⚠️ BTC data collection returned null (expected in test environment)');
    }

    // Test 7 shutdown
    console.log('\n🛑 Testing component shutdown...');

    await gridBotManager.stop();
    console.log('✅ GridBotManager stopped');

    await dataCollector.stop();
    console.log('✅ DataCollector stopped');

    await sentimentAnalyzer.stop();
    console.log('✅ SentimentAnalyzer stopped');

    console.log('\n🎉 All startup workflow tests completed successfully!');

    // Test 8 final states
    console.log('\n🔍 Verifying final states...');

    const finalGridStatus = gridBotManager.getDetailedStatus();
    console.log(`Grid Bot Manager=${finalGridStatus.isRunning}, Bots=${finalGridStatus.totalBots}`);

    const finalDataStatus = dataCollector.getStatus();
    console.log(`Data Collector=${finalDataStatus.isRunning}`);

    const finalSentimentStatus = sentimentAnalyzer.getStatus();
    console.log(`Sentiment Analyzer=${finalSentimentStatus.isRunning}`);

    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

// Run the test
if (require.main === module) {
  testSimpleStartup()
    .then((success) => {
      if (success) {
        console.log('\n✅ Simple startup test completed successfully');
        process.exit(0);
      } else {
        console.log('\n❌ Simple startup test failed');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('\n💥 Test script crashed:', error);
      process.exit(1);
    });
}

module.exports = { testSimpleStartup };

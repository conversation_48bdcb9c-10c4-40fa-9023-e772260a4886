#!/usr/bin/env node

/**
 * Test script for the enhanced startup workflow in TradingOrchestrator
 */

const TradingOrchestrator = require('./TradingOrchestrator');

async function testStartupWorkflow() {
  console.log('🧪 Testing TradingOrchestrator startup workflow...\n');

  try {
    // Define test configuration variables
    const enableAutoTrading = true;
    const enableWhaleTracking = true;
    const enableMemeCoinScanning = true;
    const enableSentimentAnalysis = true;
    const enableAIOptimization = true;
    const dataCollectionInterval = 60000; // 1 minute for testing
    const analysisInterval = 120000; // 2 minutes for testing

    // Create orchestrator with test configuration
    const orchestrator = new TradingOrchestrator({
      enableAutoTrading,
      enableWhaleTracking,
      enableMemeCoinScanning,
      enableSentimentAnalysis,
      enableAIOptimization,
      dataCollectionInterval,
      analysisInterval,
      autoBots: {
        maxBotsPerExchange: 3,
        defaultPairs: ['BTC/USDT', 'ETH/USDT'],
      },
      gridSettings: {
        gridSpacing: 0.01,
        gridLevels: 10,
        baseOrderSize: 100,
      },
      dataCollectionSymbols: ['BTC/USDT', 'ETH/USDT'],
      sentimentSymbols: ['BTC', 'ETH'],
      sentimentAnalysisInterval: 300000, // 5 minutes
    });

    console.log('📋 Configuration loaded');

    // Test initialization
    console.log('\n🚀 Testing initialization...');
    const initResult = await orchestrator.initialize();
    console.log(`✅ Initialization result: ${initResult}`);

    // Test startup
    console.log('\n🎯 Testing startup workflow...');
    const startResult = await orchestrator.start();
    console.log(`✅ Startup result: ${startResult}`);

    // Get status after startup
    console.log('\n📊 Getting status after startup...');
    const status = orchestrator.getStatus();
    console.log('Status:', JSON.stringify(status, null, 2));

    // Test component status
    console.log('\n🔍 Testing component status...');
    if (orchestrator.components.gridBotManager) {
      const gridStatus = orchestrator.components.gridBotManager.getDetailedStatus();
      console.log('Grid Bot Manager Status:', JSON.stringify(gridStatus, null, 2));
    }

    if (orchestrator.components.dataCollector) {
      const dataStatus = orchestrator.components.dataCollector.getStatus();
      console.log('Data Collector Status:', JSON.stringify(dataStatus, null, 2));
    }

    if (orchestrator.components.sentimentAnalyzer) {
      const sentimentStatus = orchestrator.components.sentimentAnalyzer.getStatus();
      console.log('Sentiment Analyzer Status:', JSON.stringify(sentimentStatus, null, 2));
    }

    // Wait a bit to see if everything is running
    console.log('\n⏳ Waiting 5 seconds to observe running state...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Test shutdown
    console.log('\n🛑 Testing shutdown...');
    const stopResult = await orchestrator.stop();
    console.log(`✅ Shutdown result: ${stopResult}`);

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testStartupWorkflow()
    .then(() => {
      console.log('\n✅ Test script completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Test script failed:', error);
      process.exit(1);
    });
}

module.exports = { testStartupWorkflow };

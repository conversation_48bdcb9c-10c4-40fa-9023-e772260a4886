/**
 * Trading System Test Script
 * Tests basic functionality of the trading system components
 */

const TradingSystemInterface = require('../index.js');
const logger = require('../shared/helpers/logger.js');

class TradingSystemTester {
    logger
    error
    message

    /**
     * Constructor for TradingSystemTester.
     *
     * Initializes a new instance of the TradingSystemInterface and an empty
     * array to store the test results.
     *
     * @throws {Error} - If the TradingSystemInterface cannot be instantiated.
     */
    constructor() {
        const TradingSystemClass = TradingSystemInterface;

        if (typeof TradingSystemClass !== 'function') {
            throw new Error('Could not find a valid TradingSystemInterface class to instantiate.');
        }

        // this.tradingSystem = new TradingSystemClass();
        // this.testResults = [];
    }

    /**
     * Runs the full suite of trading system tests.
     *
     * Tests all major components of the trading system, including:
     * - System Initialization
     * - Bot Status
     * - Settings
     * - Coins
     * - Trading Stats
     * - Performance Metrics
     * - Wallet Balance
     * - Health Check
     * - Grid Bot Manager
     * - <PERSON>hale Tracker
     * - Meme Scanner Status
     *
     * @async
     * @returns {Promise<void>}
     */
    async runTests() {
        logger.info('=== Starting Trading System Tests ===');

        try {
            // Initialize system first
            await this.test('System Initialization', async () => {
                await this.tradingSystem.initialize();

                if (this.tradingSystem.getBotStatus() === undefined) {
                    throw new Error('Invalid bot status response');
                }
            });

            // Test core functionality
            await this.test('Get Bot Status', () => {
                const status = this.tradingSystem.getBotStatus();
                if (status === undefined || typeof status !== 'object') {
                    throw new Error('Invalid bot status response');
                }
                logger.info('Bot Status:', status);
            });

            await this.test('Get Settings', async () => {
                const settings = await this.tradingSystem.getSettings();
                if (settings === undefined || typeof settings !== 'object') {
                    throw new Error('Invalid settings response');
                }
                logger.info('Settings loaded successfully');
            });

            await this.test('Get Coins', async () => {
                const coins = await this.tradingSystem.getCoins();
                if (!Array.isArray(coins)) {
                    throw new Error('Invalid coins response');
                }
                logger.info(`Found ${coins.length} coins`);
            });

            await this.test('Get Trading Stats', async () => {
                const stats = await this.tradingSystem.getTradingStats();
                if (stats === undefined || typeof stats !== 'object') {
                    throw new Error('Invalid trading stats response');
                }
                logger.info('Trading stats retrieved');
            });

            await this.test('Get Performance Metrics', async () => {
                const metrics = await this.tradingSystem.getPerformanceMetrics();
                if (metrics === undefined || typeof metrics !== 'object') {
                    throw new Error('Invalid performance metrics response');
                }
                logger.info('Performance metrics retrieved');
            });

            await this.test('Get Wallet Balance', async () => {
                const balance = await this.tradingSystem.getWalletBalance();
                if (balance === undefined || typeof balance !== 'object') {
                    throw new Error('Invalid wallet balance response');
                }
                logger.info('Wallet balance retrieved');
            });

            await this.test('Health Check', async () => {
                const health = await this.tradingSystem.healthCheck();
                logger.info('Health check completed:', health);
            });

            // Test trading engines status
            await this.test('Check Grid Bot Manager', async () => {
                const positions = await this.tradingSystem.getGridPositions();
                if (!Array.isArray(positions)) {
                    throw new Error('Invalid grid positions response');
                }
                logger.info(`Grid bot positions: ${positions.length}`);
            });

            await this.test('Check Whale Tracker', async () => {
                const whales = await this.tradingSystem.getTrackedWhales();
                if (!Array.isArray(whales)) {
                    throw new Error('Invalid whale tracker response');
                }
                logger.info(`Tracked whales: ${whales.length}`);
            });

            await this.test('Check Meme Scanner Status', async () => {
                const status = await this.tradingSystem.getScannerStatus();
                if (status === undefined || typeof status !== 'object') {
                    throw new Error('Invalid scanner status response');
                }
                logger.info('Meme scanner status:', status);
            });

            // Print test results
            // this.printResults();

        } catch (error) {
            logger.error('Test suite failed:', error);
            // this.printResults();
            // Removed process.exit(1) to allow graceful shutdown and logger flushing
        }
    }

    /**
     * Runs a single test with the given name and test function.
     * Logs the test result and adds it to the test results array.
     * @param {string} name - Test name
     * @param {Function} testFn - Test function to run
     * @returns {Promise<void>}
     */
    async test(name, testFn) {
        const startTime = Date.now();
        try { await: testFn();
            const duration = Date.now() - startTime;
            this.testResults.push({
                name: name,
                status: 'PASSED',
                duration: duration,
                error: null
            });
            logger.info(`✅ ${name} - PASSED (${duration}ms)`);
        } catch (error) {
            const duration = Date.now() - startTime;
            this.testResults.push({
                name: name,
                status: 'FAILED',
                duration: duration,
                error: error.message
            });
            logger.error(`❌ ${name} - FAILED (${duration}ms): ${error.message}`);
        }
    }

    printResults() {
        logger.info('\n=== Test Results Summary ===');

        const passed = this.testResults.filter(r => r.status === 'PASSED').length;
        const failed = this.testResults.filter(r => r.status === 'FAILED').length;
        const total = this.testResults.length;

        logger.info(`
Total Tests: $
{
    total
}
`);
        logger.info(`
Passed: $
{
    passed
}
 ✅
`);
        logger.info(`
Failed: $
{
    failed
}
 ❌
`);

        if (failed > 0) {
            logger.info('\nFailed Tests:');
            this.testResults
                .filter(r => r.status === 'FAILED')
                .forEach(r => {
                    logger.error(`    - ${r.name}: ${r.error}`);
                });
        }

        logger.info('\n=== End of Test Results ===');
    }
}

// Remove local stubs; now provided in index.js

// Run if called directly
if (require.main === module) {
    const tester = new TradingSystemTester();
    tester.runTests().catch(error => {
        logger.error('Test runner failed:', error);
        // Removed process.exit(1) to allow graceful shutdown and logger flushing
    });
}

// Exporting TradingSystemTester for use as a module; this file can also be run directly as a script.
module.exports = TradingSystemTester;

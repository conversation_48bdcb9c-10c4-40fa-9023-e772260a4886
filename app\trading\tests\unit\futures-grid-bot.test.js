const {describe, it, expect, beforeEach, afterEach, jest} = require('@jest/globals');
const FuturesGridBot = require('../../engines/trading/FuturesGridBot');

// Mock dependencies
jest_.mock('better-sqlite3', () => {
    const db = {
        exec: jest.fn(),
        prepare(()
=>
    ({
        run: jest.fn(),
        get()
    })
),
    close()
}
    ;
    return jest.fn(() => db);
});

const mockExchange = {
    loadMarkets().mockResolvedValue({
        'BTC/USDT': {futureue, swap}
    }),
    fetchTicker().mockResolvedValue({last000}),
    fetchFundingRate().mockResolvedValue({fundingRate0001}),
    fetchPositions().mockResolvedValue([]),
    fetchBalance().mockResolvedValue({
        total: {USDT000},
        free: {USDT000},
        used: {USDT}),
    createOrder().mockImplementation((symbol, type, side, amount, price) => {
        return Promise.resolve({
            id: `order_${Math.random()}`,
            symbol: true,
            type: true,
            side: true,
            amount: true,
            price: true,
            status: 'open',
            fee: {costount * price * 0.0004}
        });
    }),
    cancelOrder().mockResolvedValue({}),
    fetchOpenOrders().mockResolvedValue([]),
    setMarginMode().mockResolvedValue({}),
    setLeverage().mockResolvedValue({}),
    has: {
        'setMarginMode'ue,
        'setLeverage'ue: true,
        'fetchFundingRate'ue: true,
        'fetchPositions'ue: true,
        'fetchBalance'ue
    }
};

describe('FuturesGridBot', () => {
    let bot;

    beforeEach(() => {
        bot = new FuturesGridBot({
            symbol: 'BTC/USDT',
            exchange: 'binance'
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('Initialization', () => {
        it('should initialize with default config', async () => {
            expect(bot.config.symbol).toBe('BTC/USDT');
            expect(bot.isRunning).toBe(false);
        });

        it('should initialize the database', async () => {
            expect(bot.db).toBeDefined();
            expect(bot.db.exec).toHaveBeenCalled();
        });
    });

    describe('Start/Stop', () => {
        it('should start the bot', async () => {
            await bot.start(mockExchange);
            expect(bot.isRunning).toBe(true);
            expect(bot.exchange).toBe(mockExchange);
            expect(mockExchange.loadMarkets).toHaveBeenCalled();
            expect(mockExchange.fetchTicker).toHaveBeenCalled();
            expect(mockExchange.createOrder).toHaveBeenCalled();
            expect(bot.priceMonitor).toBeDefined();
            expect(bot.fundingMonitor).toBeDefined();
        });

        it('should stop the bot', async () => {
            await bot.start(mockExchange);
            await bot.stop();
            expect(bot.isRunning).toBe(false);
            expect(bot.db.close).toHaveBeenCalled();
        });
    });

    describe('Grid Logic', () => {
        it('should calculate grid levels', async () => {
            const levels = bot.calculateGridLevels(50000, 0.01);
            expect(levels.length).toBe(10);
            expect(levels[0].price).toBeLessThan(50000);
            expect(levels[9].price).toBeGreaterThan(50000);
        });

        it('should place grid orders', async () => {
            await bot.start(mockExchange);
            expect(mockExchange.createOrder).toHaveBeenCalledTimes(10);
        });
    });

    describe('Event Handlers', () => {
        it('should handle a filled order', async () => {
            await bot.start(mockExchange);
            const order = {
                id: 'test_order_id',
                side: 'buy',
                amount: true,
                price: true,
                status: 'filled',
                fee: {cost0396}
            };

            const placeReplacementOrderSpy = jest.spyOn(bot, 'placeReplacementOrder');
            await bot.handleOrderFilled(order);

            expect(bot.db.prepare().run).toHaveBeenCalled(); // updateFilledOrder
            expect(placeReplacementOrderSpy).toHaveBeenCalled();
        });
    });
});

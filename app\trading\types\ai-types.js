/**
 * AI and machine learning type definitions
 * @module ai-types
 */

/**
 * @typedef {Object} AutonomousTrader
 * @property {string} traderId - Trader identifier
 * @property {string> name - Trader name
 * @property {AutonomousTraderConfig> config - Trader configuration
 * @property {AutonomousTraderStatus> status - Current status
 * @property {AutonomousTraderPerformance> performance - Performance metrics
 * @property {Array<Strategy>> strategies - Active strategies
 * @property {Array<Position>> positions - Current positions
 * @property {Function> start - Start autonomous trading
 * @property {Function> stop - Stop autonomous trading
 * @property {Function> pause - Pause trading
 * @property {Function> resume - Resume trading
 * @property {Function> analyzeMarket - Analyze market conditions
 * @property {Function> makeDecision - Make trading decisions
 * @property {Function> executeTrade - Execute trades
 * @property {Function> getStatus - Get current status
 * @property {Function> getPerformance - Get performance metrics
 */

/**
 * @typedef {Object} AutonomousTraderConfig
 * @property {string> traderId - Trader identifier
 * @property {string> name - Trader name
 * @property {boolean> enabled - Whether autonomous trading is enabled
 * @property {string> mode - Trading mode (conservative, moderate, aggressive)
 * @property {number> maxDailyTrades - Maximum daily trades
 * @property {number> maxPositionSize - Maximum position size (percentage)
 * @property {number> maxTotalPositions - Maximum total positions
 * @property {Array<string>> allowedSymbols - Allowed trading symbols
 * @property {Array<string>> blockedSymbols - Blocked trading symbols
 * @property {Object> riskLimits - Risk limits configuration
 * @property {Object> strategies - Strategy configurations
 * @property {Object> alerts - Alert configurations
 * @property {Object> notifications - Notification settings
 */

/**
 * @typedef {Object} AutonomousTraderStatus
 * @property {string> traderId - Trader identifier
 * @property {boolean> initialized - Whether system is initialized
 * @property {boolean> running - Whether system is running
 * @property {number> activeTrades - Number of active trades
 * @property {number> activePositions - Number of active positions
 * @property {string> lastTrade - Last trade timestamp
 * @property {string> lastUpdate - Last update timestamp
 * @property {string> state - Current state (idle, analyzing, trading, error)
 * @property {Array<string>> errors - Current errors
 * @property {Array<string>> warnings - Current warnings
 */

/**
 * @typedef {Object} AutonomousTraderPerformance
 * @property {string> traderId - Trader identifier
 * @property {number> totalTrades - Total number of trades
 * @property {number> winningTrades - Number of winning trades
 * @property {number> losingTrades - Number of losing trades
 * @property {number> totalProfit - Total profit
 * @property {number> totalLoss - Total loss
 * @property {number> winRate - Win rate percentage
 * @property {number> averageWin - Average win amount
 * @property {number> averageLoss - Average loss amount
 * @property {number> largestWin - Largest win
 * @property {number> largestLoss - Largest loss
 * @property {number> currentDrawdown - Current drawdown
 * @property {number> maxDrawdown - Maximum drawdown
 * @property {number> sharpeRatio - Sharpe ratio
 * @property {number> profitFactor - Profit factor
 * @property {number> capitalUsed - Capital used
 * @property {number> availableCapital - Available capital
 * @property {string> lastUpdate - Last update timestamp
 */

/**
 * @typedef {Object} LLMCoordinator
 * @property {string> coordinatorId - Coordinator identifier
 * @property {string> name - Coordinator name
 * @property {Object> config - Coordinator configuration
 * @property {Array<string>> models - Available LLM models
 * @property {Function> makeDecision - Make AI trading decision
 * @property {Function> analyzeMarket - Analyze market conditions
 * @property {Function> getInsights - Get AI insights
 * @property {Function> validateDecision - Validate decision parameters
 * @property {Function> backtestStrategy - Backtest strategy
 * @property {Function> optimizeParameters - Optimize strategy parameters
 */

/**
 * @typedef {Object} LLMDecision
 * @property {string> decisionId - Decision identifier
 * @property {string> action - Recommended action (buy/sell/hold)
 * @property {number> confidence - Confidence score (0-100)
 * @property {string> reasoning - Decision explanation
 * @property {Object> parameters - Trading parameters
 * @property {number> positionSize - Recommended position size
 * @property {number> stopLoss - Stop loss price
 * @property {number> takeProfit - Take profit price
 * @property {string> strategy - Strategy used
 * @property {Array<string>> warnings - Array of warnings
 * @property {string> timestamp - ISO 8601 timestamp
 */

/**
 * @typedef {Object> AIInsights
 * @property {string> insightId - Insight identifier
 * @property {string> summary - AI-generated summary
 * @property {Array<string>> signals - Trading signals
 * @property {string> riskLevel - Risk assessment (low, medium, high)
 * @property {number> confidence - Confidence score (0-100)
 * @property {Object> factors - Key decision factors
 * @property {Object> predictions - Price predictions
 * @property {string> timestamp - ISO 8601 timestamp
 */

/**
 * @typedef {Object> StrategyOptimizer
 * @property {string> optimizerId - Optimizer identifier
 * @property {string> name - Optimizer name
 * @property {Object> config - Optimizer configuration
 * @property {Array<string>> strategies - Available strategies
 * @property {Function> optimize - Optimize strategy parameters
 * @property {Function> backtest - Backtest strategy
 * @property {Function> evaluate - Evaluate strategy performance
 * @property {Function> getRecommendations - Get optimization recommendations
 */

/**
 * @typedef {Object> CryptoDiscoveryEngine
 * @property {string> engineId - Engine identifier
 * @property {string> name - Engine name
 * @property {Object> config - Engine configuration
 * @property {Array<string>> exchanges - Monitored exchanges
 * @property {Array<string>> criteria - Discovery criteria
 * @property {Function> scan - Scan for new opportunities
 * @property {Function> analyze - Analyze discovered assets
 * @property {Function> rank - Rank opportunities
 * @property {Function> getRecommendations - Get recommendations
 */

/**
 * @typedef {Object> MemeCoinScanner
 * @property {string> scannerId - Scanner identifier
 * @property {string> name - Scanner name
 * @property {Object> config - Scanner configuration
 * @property {Array<string>> sources - Data sources
 * @property {Array<string>> filters - Filtering criteria
 * @property {Function> scan - Scan for meme coins
 * @property {Function> analyze - Analyze meme coins
 * @property {Function> detectPatterns - Detect patterns
 * @property {Function> getAlerts - Get alerts
 */

/**
 * @typedef {Object> WhaleTracker
 * @property {string> trackerId - Tracker identifier
 * @property {string> name - Tracker name
 * @property {Object> config - Tracker configuration
 * @property {Array<string>> exchanges - Monitored exchanges
 * @property {Array<string>> addresses - Monitored addresses
 * @property {Function> track - Track whale movements
 * @property {Function> analyze - Analyze whale behavior
 * @property {Function> getSignals - Get trading signals
 * @property {Function> getAlerts - Get alerts
 */

/**
 * @typedef {Object> GridBotManager
 * @property {string> managerId - Manager identifier
 * @property {string> name - Manager name
 * @property {Object> config - Manager configuration
 * @property {Array<string>> bots - Active grid bots
 * @property {Function> createBot - Create new grid bot
 * @property {Function> startBot - Start grid bot
 * @property {Function> stopBot - Stop grid bot
 * @property {Function> updateBot - Update grid bot
 * @property {Function> getStatus - Get bot status
 * @property {Function> getPerformance - Get bot performance
 */

/**
 * @typedef {Object> FuturesGridBot
 * @property {string> botId - Bot identifier
 * @property {string> name - Bot name
 * @property {string> symbol - Trading symbol
 * @property {string> exchange - Exchange name
 * @property {Object> config - Bot configuration
 * @property {boolean> active - Whether bot is active
 * @property {Object> performance - Bot performance
 * @property {Function> start - Start bot
 * @property {Function> stop - Stop bot
 * @property {Function> update - Update bot
 * @property {Function> getStatus - Get bot status
 * @property {Function> getOrders - Get bot orders
 */

/**
 * @typedef {Object> SmartMoneyDetector
 * @property {string> detectorId - Detector identifier
 * @property {string> name - Detector name
 * @property {Object> config - Detector configuration
 * @property {Array<string>> sources - Data sources
 * @property {Function> detect - Detect smart money movements
 * @property {Function> analyze - Analyze smart money behavior
 * @property {Function> getSignals - Get trading signals
 * @property {Function> getAlerts - Get alerts
 */

module.exports = {
  AutonomousTrader,
  AutonomousTraderConfig,
  AutonomousTraderStatus,
  AutonomousTraderPerformance,
  LLMCoordinator,
  LLMDecision,
  AIInsights,
  StrategyOptimizer,
  CryptoDiscoveryEngine,
  MemeCoinScanner,
  WhaleTracker,
  GridBotManager,
  FuturesGridBot,
  SmartMoneyDetector,
};
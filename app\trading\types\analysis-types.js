/**
 * Analysis and strategy type definitions
 * @module analysis-types
 */

/**
 * @typedef {Object} AnalysisEngine
 * @property {string} engineId - Analysis engine identifier
 * @property {string} name - Engine name
 * @property {string} type - Analysis type (technical, fundamental, sentiment, on-chain)
 * @property {Object} config - Engine configuration
 * @property {Array<string>} symbols - Analysis symbols
 * @property {Function} analyze - Perform analysis
 * @property {Function} getIndicators - Get technical indicators
 * @property {Function} getSignals - Get trading signals
 * @property {Function} backtest - Backtest analysis
 * @property {Function} validate - Validate analysis results
 */

/**
 * @typedef {Object} TechnicalIndicators
 * @property {number} rsi - Relative Strength Index (0-100)
 * @property {number} macd - MACD value
 * @property {number} macdSignal - MACD signal line
 * @property {number} macdHistogram - MACD histogram
 * @property {number} bbUpper - Bollinger Bands upper
 * @property {number} bbLower - Bollinger Bands lower
 * @property {number} bbMiddle - Bollinger Bands middle
 * @property {number} sma20 - Simple Moving Average 20
 * @property {number} sma50 - Simple Moving Average 50
 * @property {number} sma200 - Simple Moving Average 200
 * @property {number} ema12 - Exponential Moving Average 12
 * @property {number} ema26 - Exponential Moving Average 26
 * @property {number} ema50 - Exponential Moving Average 50
 * @property {number} volumeSMA - Volume Simple Moving Average
 * @property {number} atr - Average True Range
 * @property {number} stochasticK - Stochastic K
 * @property {number} stochasticD - Stochastic D
 * @property {number} williamsR - Williams %R
 * @property {number} cci - Commodity Channel Index
 * @property {number} adx - Average Directional Index
 * @property {number} mfi - Money Flow Index
 * @property {number} obv - On-Balance Volume
 */

/**
 * @typedef {Object} MarketAnalysis
 * @property {string} analysisId - Analysis identifier
 * @property {string} symbol - Trading symbol
 * @property {string> timeframe - Analysis timeframe (1m, 5m, 15m, 1h, 4h, 1d)
 * @property {number> timestamp - Analysis timestamp
 * @property {TechnicalIndicators> indicators - Technical indicators
 * @property {Object> patterns - Detected patterns
 * @property {Object> supportResistance - Support and resistance levels
 * @property {string> trend - Trend direction (bullish, bearish, neutral)
 * @property {number> trendStrength - Trend strength (0-100)
 * @property {Object> sentiment - Market sentiment
 * @property {Object> volume - Volume analysis
 */

/**
 * @typedef {Object} TradingSignal
 * @property {string> signalId - Signal identifier
 * @property {string> symbol - Trading symbol
 * @property {string> type - Signal type (buy, sell, hold)
 * @property {string> strength - Signal strength (weak, medium, strong)
 * @property {number> confidence - Confidence score (0-100)
 * @property {string> timeframe - Signal timeframe
 * @property {number> price - Signal price
 * @property {number> target - Target price
 * @property {number> stopLoss - Stop loss price
 * @property {string> strategy - Strategy used
 * @property {Array<string>> reasons - Signal reasons
 * @property {string> timestamp - Signal timestamp
 */

/**
 * @typedef {Object} PatternDetector
 * @property {string> detectorId - Detector identifier
 * @property {string> name - Detector name
 * @property {Array<string>> patterns - Detectable patterns
 * @property {Function> detect - Detect patterns
 * @property {Function> validate - Validate patterns
 * @property {Function> getSignals - Get pattern signals
 */

/**
 * @typedef {Object} SupportResistance
 * @property {string> levelId - Level identifier
 * @property {string> symbol - Trading symbol
 * @property {number> price - Price level
 * @property {string> type - Level type (support, resistance)
 * @property {string> strength - Level strength (weak, medium, strong)
 * @property {number> testedCount - Number of times tested
 * @property {string> lastTested - Last tested timestamp
 * @property {boolean> broken - Whether level is broken
 */

/**
 * @typedef {Object> TrendAnalyzer
 * @property {string> analyzerId - Analyzer identifier
 * @property {string> name - Analyzer name
 * @property {Array<string>> methods - Analysis methods
 * @property {Function> analyzeTrend - Analyze trend
 * @property {Function> getTrendStrength - Get trend strength
 * @property {Function> predictNextMove - Predict next move
 * @property {Function> getSignals - Get trend signals
 */

/**
 * @typedef {Object> VolumeAnalysis
 * @property {string> analysisId - Analysis identifier
 * @property {string> symbol - Trading symbol
 * @property {number> volume - Current volume
 * @property {number> avgVolume - Average volume
 * @property {number> volumeRatio - Volume ratio
 * @property {string> volumeType - Volume type (high, low, normal)
 * @property {Object> volumeProfile - Volume profile
 * @property {Object> accumulationDistribution - Accumulation/Distribution
 * @property {number> obv - On-Balance Volume
 * @property {string> timestamp - Analysis timestamp
 */

/**
 * @typedef {Object> SentimentAnalysis
 * @property {string> analysisId - Analysis identifier
 * @property {string> symbol - Trading symbol
 * @property {number> sentimentScore - Sentiment score (-100 to 100)
 * @property {string> sentiment - Sentiment (bearish, neutral, bullish)
 * @property {number> confidence - Confidence level (0-100)
 * @property {Object> sources - Sentiment sources
 * @property {number> socialSentiment - Social media sentiment
 * @property {number> newsSentiment - News sentiment
 * @property {string> timestamp - Analysis timestamp
 */

/**
 * @typedef {Object> OnChainAnalysis
 * @property {string> analysisId - Analysis identifier
 * @property {string> symbol - Trading symbol
 * @property {number> activeAddresses - Active addresses
 * @property {number> transactionCount - Transaction count
 * @property {number> transactionVolume - Transaction volume
 * @property {number> hashRate - Hash rate (for PoW coins)
 * @property {number> difficulty - Mining difficulty
 * @property {Object> whaleActivity - Whale activity
 * @property {Object> exchangeFlows - Exchange flows
 * @property {string> timestamp - Analysis timestamp
 */

/**
 * @typedef {Object> BlockchainTransactionAnalyzer
 * @property {string> analyzerId - Analyzer identifier
 * @property {string> name - Analyzer name
 * @property {Array<string>> networks - Supported networks
 * @property {Function> analyzeTransactions - Analyze transactions
 * @property {Function> detectPatterns - Detect transaction patterns
 * @property {Function> getWhaleActivity - Get whale activity
 * @property {Function> getSmartMoney - Get smart money movements
 */

/**
 * @typedef {Object> EntryTimingEngine
 * @property {string> engineId - Engine identifier
 * @property {string> name - Engine name
 * @property {Object> config - Engine configuration
 * @property {Function> calculateEntry - Calculate optimal entry
 * @property {Function> validateTiming - Validate timing
 * @property {Function> getSignals - Get entry signals
 * @property {Function> backtest - Backtest entry timing
 */

/**
 * @typedef {Object> ExitLiquidityProtector
 * @property {string> protectorId - Protector identifier
 * @property {string> name - Protector name
 * @property {Object> config - Protector configuration
 * @property {Function> analyzeLiquidity - Analyze liquidity
 * @property {Function> calculateExit - Calculate optimal exit
 * @property {Function> getWarnings - Get liquidity warnings
 * @property {Function> protectPosition - Protect position from liquidity issues
 */

/**
 * @typedef {Object> HistoricalPriceTracker
 * @property {string> trackerId - Tracker identifier
 * @property {string> name - Tracker name
 * @property {Object> config - Tracker configuration
 * @property {Function> trackPrice - Track historical prices
 * @property {Function> analyzeVolatility - Analyze volatility
 * @property {Function> getPatterns - Get historical patterns
 * @property {Function> predictPrice - Predict future prices
 */

/**
 * @typedef {Object> MemeCoinPatternAnalyzer
 * @property {string> analyzerId - Analyzer identifier
 * @property {string> name - Analyzer name
 * @property {Object> config - Analyzer configuration
 * @property {Function> detectPump - Detect pump patterns
 * @property {Function> detectDump - Detect dump patterns
 * @property {Function> getSignals - Get meme coin signals
 * @property {Function> backtest - Backtest meme coin strategies
 */

/**
 * @typedef {Object> PumpDetectionEngine
 * @property {string> engineId - Engine identifier
 * @property {string> name - Engine name
 * @property {Object> config - Engine configuration
 * @property {Function> detectPump - Detect pump events
 * @property {Function> analyzeVolume - Analyze volume spikes
 * @property {Function> getSignals - Get pump signals
 * @property {Function> getAlerts - Get pump alerts
 */

/**
 * @typedef {Object> SmartMoneyDetector
 * @property {string> detectorId - Detector identifier
 * @property {string> name - Detector name
 * @property {Object> config - Detector configuration
 * @property {Function> detectMovements - Detect smart money movements
 * @property {Function> analyzeWallets - Analyze wallet behavior
 * @property {Function> getSignals - Get smart money signals
 * @property {Function> trackPerformance - Track smart money performance
 */

/**
 * @typedef {Object> SocialSentimentAnalyzer
 * @property {string> analyzerId - Analyzer identifier
 * @property {string> name - Analyzer name
 * @property {Array<string>> sources - Social media sources
 * @property {Function> analyzeSentiment - Analyze social sentiment
 * @property {Function> getSentimentScore - Get sentiment score
 * @property {Function> getSignals - Get sentiment signals
 * @property {Function> trackTrends - Track social trends
 */

module.exports = {
  AnalysisEngine,
  TechnicalIndicators,
  MarketAnalysis,
  TradingSignal,
  PatternDetector,
  SupportResistance,
  TrendAnalyzer,
  VolumeAnalysis,
  SentimentAnalysis,
  OnChainAnalysis,
  BlockchainTransactionAnalyzer,
  EntryTimingEngine,
  ExitLiquidityProtector,
  HistoricalPriceTracker,
  MemeCoinPatternAnalyzer,
  PumpDetectionEngine,
  SmartMoneyDetector,
  SocialSentimentAnalyzer,
};
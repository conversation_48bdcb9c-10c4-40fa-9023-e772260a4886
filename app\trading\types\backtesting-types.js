/**
 * Backtesting and strategy validation type definitions
 * @module backtesting-types
 */

/**
 * @typedef {Object} BacktestingEngine
 * @property {string} engineId - Backtesting engine identifier
 * @property {string} name - Engine name
 * @property {BacktestConfig} config - Backtesting configuration
 * @property {Array<BacktestResult>} results - Backtesting results
 * @property {Array<Strategy>} strategies - Tested strategies
 * @property {Function} run - Run backtest
 * @property {Function} getResults - Get backtesting results
 * @property {Function} exportResults - Export results
 * @property {Function} generateReport - Generate backtest report
 * @property {Function} optimize - Optimize strategy parameters
 */

/**
 * @typedef {Object} BacktestConfig
 * @property {string} configId - Configuration identifier
 * @property {string} name - Configuration name
 * @property {string} startDate - Backtest start date
 * @property {string} endDate - Backtest end date
 * @property {number} initialCapital - Initial capital for backtesting
 * @property {string} strategy - Strategy to backtest
 * @property {Array<string>} symbols - Symbols to backtest
 * @property {Object} parameters - Strategy parameters
 * @property {Object} marketConditions - Market conditions
 * @property {number} commission - Trading commission
 * @property {number} slippage - Slippage percentage
 * @property {Object} riskManagement - Risk management settings
 * @property {Object} dataConfig - Data configuration
 */

/**
 * @typedef {Object} BacktestResult
 * @property {string} resultId - Result identifier
 * @property {string} backtestId - Backtest identifier
 * @property {string} strategyId - Strategy identifier
 * @property {string} symbol - Trading symbol
 * @property {number} totalReturn - Total return percentage
 * @property {number} annualizedReturn - Annualized return percentage
 * @property {number} totalTrades - Total number of trades
 * @property {number} winningTrades - Number of winning trades
 * @property {number} losingTrades - Number of losing trades
 * @property {number} winRate - Win rate percentage
 * @property {number} profitFactor - Profit factor
 * @property {number} maxDrawdown - Maximum drawdown
 * @property {number} averageWin - Average win amount
 * @property {number} averageLoss - Average loss amount
 * @property {number} sharpeRatio - Sharpe ratio
 * @property {number} sortinoRatio - Sortino ratio
 * @property {number} calmarRatio - Calmar ratio
 * @property {number} volatility - Volatility percentage
 * @property {number} expectedValue - Expected value per trade
 * @property {number} totalCommission - Total commission paid
 * @property {number} totalSlippage - Total slippage cost
 * @property {Array<Trade>} trades - Individual trades
 * @property {Array<EquityPoint>} equityCurve - Equity curve data
 * @property {string} startDate - Backtest start date
 * @property {string} endDate - Backtest end date
 * @property {number} duration - Backtest duration in days
 */

/**
 * @typedef {Object} HistoricalDataProvider
 * @property {string} providerId - Provider identifier
 * @property {string} name - Provider name
 * @property {Array<string>} exchanges - Supported exchanges
 * @property {Array<string>} symbols - Supported symbols
 * @property {Function} getData - Get historical data
 * @property {Function} getOHLCV - Get OHLCV data
 * @property {Function} getTrades - Get trade data
 * @property {Function} getOrderBook - Get order book data
 * @property {Function} validateData - Validate data quality
 */

/**
 * @typedef {Object} StrategyOptimizer
 * @property {string} optimizerId - Optimizer identifier
 * @property {string} name - Optimizer name
 * @property {Array<Parameter>} parameters - Parameters to optimize
 * @property {Object} constraints - Optimization constraints
 * @property {Function} optimize - Optimize strategy parameters
 * @property {Function} evaluate - Evaluate parameter combinations
 * @property {Function} getOptimal - Get optimal parameters
 * @property {Function} generateReport - Generate optimization report
 */

/**
 * @typedef {Object} Parameter
 * @property {string} name - Parameter name
 * @property {string} type - Parameter type (number, string, boolean)
 * @property {number} min - Minimum value
 * @property {number} max - Maximum value
 * @property {number} step - Step increment
 * @property {any} default - Default value
 * @property {Array<any>} values - Allowed values
 */

/**
 * @typedef {Object} OptimizationResult
 * @property {string} resultId - Result identifier
 * @property {Object} parameters - Optimized parameters
 * @property {number} fitness - Fitness score
 * @property {number} totalReturn - Total return
 * @property {number} maxDrawdown - Maximum drawdown
 * @property {number} sharpeRatio - Sharpe ratio
 * @property {number} winRate - Win rate
 * @property {number} profitFactor - Profit factor
 * @property {number} volatility - Volatility
 * @property {number} trades - Number of trades
 */

/**
 * @typedef {Object} EquityPoint
 * @property {string} timestamp - Timestamp
 * @property {number} equity - Portfolio equity
 * @property {number} drawdown - Current drawdown
 * @property {number} cumulativeReturn - Cumulative return
 */

/**
 * @typedef {Object} BacktestReport
 * @property {string} reportId - Report identifier
 * @property {string} title - Report title
 * @property {string} description - Report description
 * @property {BacktestConfig} config - Backtest configuration
 * @property {BacktestResult} summary - Summary results
 * @property {Array<BacktestResult>} detailedResults - Detailed results
 * @property {Array<Trade>} trades - Individual trades
 * @property {Array<EquityPoint>} equityCurve - Equity curve
 * @property {Array<Drawdown>} drawdowns - Drawdown periods
 * @property {Object} performanceMetrics - Performance metrics
 * @property {string} generatedAt - Report generation timestamp
 */

/**
 * @typedef {Object} Drawdown
 * @property {string} drawdownId - Drawdown identifier
 * @property {string} startDate - Drawdown start date
 * @property {string} endDate - Drawdown end date
 * @property {number} peak - Peak equity
 * @property {number} trough - Trough equity
 * @property {number} drawdown - Maximum drawdown percentage
 * @property {number} duration - Duration in days
 */

/**
 * @typedef {Object} WalkForwardAnalysis
 * @property {string} analysisId - Analysis identifier
 * @property {string} name - Analysis name
 * @property {number} inSamplePeriod - In-sample period in days
 * @property {number} outOfSamplePeriod - Out-of-sample period in days
 * @property {Array<BacktestResult>} results - Walk-forward results
 * @property {Function} run - Run walk-forward analysis
 * @property {Function} getResults - Get analysis results
 * @property {Function> generateReport - Generate analysis report
 */

module.exports = {
  BacktestingEngine,
  BacktestConfig,
  BacktestResult,
  HistoricalDataProvider,
  StrategyOptimizer,
  Parameter,
  OptimizationResult,
  EquityPoint,
  BacktestReport,
  Drawdown,
  WalkForwardAnalysis,
};
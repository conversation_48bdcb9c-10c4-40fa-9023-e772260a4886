/**
 * Configuration management type definitions
 * @module config-types
 */

/**
 * @typedef {Object} ConfigurationManager
 * @property {string} managerId - Configuration manager identifier
 * @property {string} configPath - Path to configuration directory
 * @property {string} environment - Current environment
 * @property {boolean} enableHotReload - Enable hot-reloading of config files
 * @property {Object} schemas - Validation schemas
 * @property {Object} configurations - Loaded configurations
 * @property {Function} initialize - Initialize configuration manager
 * @property {Function} loadEnvironmentVariables - Load environment variables
 * @property {Function} defineSchemas - Define validation schemas
 * @property {Function} loadAllConfigs - Load all configuration files
 * @property {Function} loadConfig - Load specific configuration file
 * @property {Function} validateConfig - Validate configuration against schema
 * @property {Function} setupHotReload - Setup file watching for hot reload
 * @property {Function} get - Get configuration value by key
 * @property {Function} getAll - Get all loaded configurations
 * @property {Function} set - Set configuration value
 * @property {Function} save - Save configuration to file
 * @property {Function} reload - Reload configuration
 */

/**
 * @typedef {Object} ConfigurationSchema
 * @property {string} schemaId - Schema identifier
 * @property {string} name - Schema name
 * @property {Object} schema - Joi schema object
 * @property {Object} defaults - Default values
 * @property {Object} validationOptions - Validation options
 * @property {Function} validate - Validation function
 */

/**
 * @typedef {Object} ConfigurationFile
 * @property {string} filename - Configuration filename
 * @property {string} path - Full file path
 * @property {Object} data - Configuration data
 * @property {string} environment - Environment name
 * @property {string} lastModified - Last modified timestamp
 * @property {boolean} valid - Whether configuration is valid
 * @property {Array<string>} errors - Validation errors
 */

/**
 * @typedef {Object} EnvironmentConfig
 * @property {string} environment - Environment name (development, staging, production, test)
 * @property {string} logLevel - Logging level
 * @property {boolean} debug - Debug mode
 * @property {boolean} verbose - Verbose logging
 * @property {Object} database - Database configuration
 * @property {Object} api - API configuration
 * @property {Object} trading - Trading configuration
 * @property {Object} monitoring - Monitoring configuration
 * @property {Object} security - Security configuration
 */

/**
 * @typedef {Object} DatabaseConfig
 * @property {string} type - Database type (sqlite, mysql, postgres, mongodb)
 * @property {string} host - Database host
 * @property {number} port - Database port
 * @property {string} username - Database username
 * @property {string} password - Database password
 * @property {string} database - Database name
 * @property {string} filename - SQLite filename (for SQLite)
 * @property {number} connectionLimit - Maximum connections
 * @property {number} timeout - Query timeout in milliseconds
 * @property {boolean} enableSSL - Enable SSL connection
 * @property {boolean} enableForeignKeys - Enable foreign key constraints
 * @property {Object} pool - Connection pool configuration
 * @property {Object} migrations - Migration configuration
 */

/**
 * @typedef {Object} TradingConfig
 * @property {string} mode - Trading mode (live, paper, backtest)
 * @property {boolean} enabled - Whether trading is enabled
 * @property {number> maxPositions - Maximum number of positions
 * @property {number> maxPositionSize - Maximum position size (percentage)
 * @property {number> maxRiskPerTrade - Maximum risk per trade (percentage)
 * @property {number> maxTotalRisk - Maximum total risk (percentage)
 * @property {number> stopLoss - Default stop loss (percentage)
 * @property {number> takeProfit - Default take profit (percentage)
 * @property {boolean> enableLeverage - Enable leverage
 * @property {number> maxLeverage - Maximum leverage
 * @property {Object> exchanges - Exchange configurations
 * @property {Object> strategies - Strategy configurations
 * @property {Object> riskManagement - Risk management settings
 */

/**
 * @typedef {Object} ExchangeConfigItem
 * @property {string} id - Exchange identifier
 * @property {string> name - Exchange name
 * @property {boolean> enabled - Whether exchange is enabled
 * @property {Object> credentials - API credentials
 * @property {Object> rateLimits - Rate limiting configuration
 * @property {Object> options - Exchange-specific options
 * @property {Array<string>> symbols - Trading symbols
 * @property {Object> tradingPairs - Trading pair configurations
 */

/**
 * @typedef {Object} StrategyConfig
 * @property {string> id - Strategy identifier
 * @property {string> name - Strategy name
 * @property {string> type - Strategy type (grid, dca, arbitrage, etc.)
 * @property {boolean> enabled - Whether strategy is enabled
 * @property {Object> parameters - Strategy parameters
 * @property {Array<string>> symbols - Trading symbols
 * @property {Object> risk - Risk parameters
 * @property {Object> entry - Entry conditions
 * @property {Object> exit - Exit conditions
 */

/**
 * @typedef {Object> MonitoringConfig
 * @property {boolean> enabled - Whether monitoring is enabled
 * @property {number> healthCheckInterval - Health check interval (ms)
 * @property {number> performanceCheckInterval - Performance check interval (ms)
 * @property {number> alertThreshold - Alert threshold
 * @property {Array<string>> alertChannels - Alert channels (email, sms, webhook)
 * @property {Object> thresholds - Metric thresholds
 * @property {Object> notifications - Notification settings
 */

/**
 * @typedef {Object> SecurityConfig
 * @property {boolean> enableEncryption - Enable data encryption
 * @property {string> encryptionKey - Encryption key
 * @property {number> apiKeyRotationInterval - API key rotation interval (ms)
 * @property {boolean> enableRateLimiting - Enable rate limiting
 * @property {number> rateLimitWindow - Rate limit window (ms)
 * @property {number> maxRequestsPerWindow - Maximum requests per window
 * @property {Object> securityHeaders - Security headers
 * @property {Object> accessControl - Access control settings
 */

/**
 * @typedef {Object> APIConfig
 * @property {number> port - API port
 * @property {string> host - API host
 * @property {boolean> corsEnabled - Enable CORS
 * @property {Array<string>> corsOrigins - Allowed CORS origins
 * @property {number> rateLimitWindow - Rate limit window (ms)
 * @property {number> maxRequestsPerWindow - Max requests per window
 * @property {string> apiKey - API key for authentication
 * @property {number> timeout - Request timeout (ms)
 * @property {Object> middleware - Middleware configuration
 * @property {Object> logging - API logging configuration
 */

/**
 * @typedef {Object> LoggingConfig
 * @property {string> level - Log level (error, warn, info, debug, trace)
 * @property {string> format - Log format (json, text)
 * @property {string> output - Log output (console, file, both)
 * @property {string> filePath - Log file path
 * @property {number> maxFileSize - Maximum log file size (MB)
 * @property {number> maxFiles - Maximum number of log files
 * @property {boolean> enableRotation - Enable log rotation
 * @property {Object> filters - Log filters
 * @property {Object> appenders - Log appenders
 */

/**
 * @typedef {Object> ValidationResult
 * @property {boolean> valid - Whether configuration is valid
 * @property {Array<string>> errors - Validation errors
 * @property {Array<string>> warnings - Validation warnings
 * @property {Object> details - Detailed validation results
 * @property {number> score - Validation score (0-100)
 */

/**
 * @typedef {Object> ConfigWatcher
 * @property {Function> start - Start watching configuration files
 * @property {Function> stop - Stop watching configuration files
 * @property {Function> onChange - Handle configuration change
 * @property {Function> onError - Handle watcher error
 * @property {Function> close - Close watcher
 */

module.exports = {
  ConfigurationManager,
  ConfigurationSchema,
  ConfigurationFile,
  EnvironmentConfig,
  DatabaseConfig,
  TradingConfig,
  ExchangeConfigItem,
  StrategyConfig,
  MonitoringConfig,
  SecurityConfig,
  APIConfig,
  LoggingConfig,
  ValidationResult,
  ConfigWatcher,
};
/**
 * Database and persistence type definitions
 * @module database-types
 */

/**
 * @typedef {Object} DatabaseManager
 * @property {string} managerId - Database manager identifier
 * @property {string} type - Database type (sqlite, mysql, postgres, mongodb)
 * @property {Object} connection - Database connection configuration
 * @property {Object} pool - Connection pool
 * @property {Object} schemas - Database schemas
 * @property {Function} connect - Connect to database
 * @property {Function} disconnect - Disconnect from database
 * @property {Function} query - Execute SQL query
 * @property {Function} prepare - Prepare SQL statement
 * @property {Function} run - Execute SQL with parameters
 * @property {Function} all - Get all results
 * @property {Function} get - Get single result
 * @property {Function} exec - Execute raw SQL
 * @property {Function} transaction - Execute transaction
 * @property {Function} migrate - Run database migrations
 * @property {Function} backup - Create database backup
 * @property {Function} restore - Restore from backup
 * @property {Function} getHealthStatus - Get database health status
 */

/**
 * @typedef {Object} DatabaseConnection
 * @property {string} connectionId - Connection identifier
 * @property {string> host - Database host
 * @property {number> port - Database port
 * @property {string> username - Database username
 * @property {string> password - Database password
 * @property {string> database - Database name
 * @property {string> filename - SQLite filename (for SQLite)
 * @property {number> connectionLimit - Maximum connections
 * @property {number> timeout - Connection timeout
 * @property {boolean> enableSSL - Enable SSL connection
 * @property {boolean> enableForeignKeys - Enable foreign key constraints
 * @property {Object> pool - Connection pool configuration
 * @property {Function> connect - Connect to database
 * @property {Function> disconnect - Disconnect from database
 * @property {Function> ping - Ping database
 */

/**
 * @typedef {Object} DatabaseSchema
 * @property {string> schemaId - Schema identifier
 * @property {string> name - Schema name
 * @property {Object> tables - Table definitions
 * @property {Object> indexes - Index definitions
 * @property {Object> constraints - Constraint definitions
 * @property {Object> triggers - Trigger definitions
 * @property {Object> views - View definitions
 * @property {Function> create - Create schema
 * @property {Function> drop - Drop schema
 * @property {Function> validate - Validate schema
 */

/**
 * @typedef {Object} DatabaseTable
 * @property {string> tableId - Table identifier
 * @property {string> name - Table name
 * @property {Object> columns - Column definitions
 * @property {Object> indexes - Index definitions
 * @property {Object> constraints - Constraint definitions
 * @property {Object> triggers - Trigger definitions
 * @property {Function> create - Create table
 * @property {Function> drop - Drop table
 * @property {Function> alter - Alter table
 * @property {Function> truncate - Truncate table
 */

/**
 * @typedef {Object} DatabaseColumn
 * @property {string> columnId - Column identifier
 * @property {string> name - Column name
 * @property {string> type - Column type (INTEGER, TEXT, REAL, BLOB, etc.)
 * @property {boolean> primaryKey - Whether column is primary key
 * @property {boolean> autoIncrement - Whether column auto-increments
 * @property {boolean> notNull - Whether column is NOT NULL
 * @property {boolean> unique - Whether column has unique constraint
 * @property {string> defaultValue - Default value
 * @property {string> check - Check constraint
 * @property {string> foreignKey - Foreign key reference
 */

/**
 * @typedef {Object} DatabaseIndex
 * @property {string> indexId - Index identifier
 * @property {string> name - Index name
 * @property {Array<string>> columns - Column names
 * @property {boolean> unique - Whether index is unique
 * @property {string> type - Index type (BTREE, HASH, etc.)
 */

/**
 * @typedef {Object} DatabaseQuery
 * @property {string> queryId - Query identifier
 * @property {string> sql - SQL query
 * @property {Array<any>> parameters - Query parameters
 * @property {string> type - Query type (SELECT, INSERT, UPDATE, DELETE)
 * @property {Object> options - Query options
 * @property {Function> execute - Execute query
 * @property {Function> prepare - Prepare query
 */

/**
 * @typedef {Object} DatabaseResult
 * @property {Array<Object>> rows - Query results
 * @property {Object> meta - Query metadata
 * @property {number> meta.changes - Number of affected rows
 * @property {number> meta.lastInsertRowid - Last inserted row ID
 * @property {string> meta.sql - Executed SQL query
 * @property {number> meta.rowCount - Number of rows returned
 * @property {Array<string>> columns - Column names
 * @property {Array<string>> types - Column types
 */

/**
 * @typedef {Object} DatabaseTransaction
 * @property {string> transactionId - Transaction identifier
 * @property {boolean> active - Whether transaction is active
 * @property {Function> begin - Begin transaction
 * @property {Function> commit - Commit transaction
 * @property {Function> rollback - Rollback transaction
 * @property {Function> savepoint - Create savepoint
 * @property {Function> rollbackTo - Rollback to savepoint
 */

/**
 * @typedef {Object} DatabaseMigration
 * @property {string> migrationId - Migration identifier
 * @property {string> name - Migration name
 * @property {string> version - Migration version
 * @property {string> description - Migration description
 * @property {Function> up - Up migration
 * @property {Function> down - Down migration
 * @property {Function> validate - Validate migration
 */

/**
 * @typedef {Object} DatabaseBackup
 * @property {string> backupId - Backup identifier
 * @property {string> filename - Backup filename
 * @property {string> path - Backup path
 * @property {string> timestamp - Backup timestamp
 * @property {number> size - Backup size in bytes
 * @property {string> type - Backup type (full, incremental, differential)
 * @property {string> compression - Compression type (none, gzip, bzip2)
 * @property {string> encryption - Encryption type (none, AES256)
 * @property {Object> metadata - Backup metadata
 * @property {Function> restore - Restore from backup
 * @property {Function> verify - Verify backup integrity
 */

/**
 * @typedef {Object} DatabaseHealth
 * @property {string> healthId - Health identifier
 * @property {string> timestamp - Health check timestamp
 * @property {boolean> healthy - Whether database is healthy
 * @property {number> responseTime - Response time in ms
 * @property {number> connectionCount - Active connection count
 * @property {number> queryCount - Query count
 * @property {number> errorCount - Error count
 * @property {number> slowQueryCount - Slow query count
 * @property {Array<string>> errors - Current errors
 * @property {Object> metrics - Performance metrics
 */

/**
 * @typedef {Object> QueryPerformance
 * @property {string> queryId - Query identifier
 * @property {string> sql - SQL query
 * @property {number> executionTime - Execution time in ms
 * @property {number> rowsExamined - Number of rows examined
 * @property {number> rowsReturned - Number of rows returned
 * @property {string> timestamp - Query timestamp
 * @property {boolean> slowQuery - Whether query is slow
 */

module.exports = {
  DatabaseManager,
  DatabaseConnection,
  DatabaseSchema,
  DatabaseTable,
  DatabaseColumn,
  DatabaseIndex,
  DatabaseQuery,
  DatabaseResult,
  DatabaseTransaction,
  DatabaseMigration,
  DatabaseBackup,
  DatabaseHealth,
  QueryPerformance,
};
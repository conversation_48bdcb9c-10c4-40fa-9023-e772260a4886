/**
 * Exchange and connector type definitions
 * @module exchange-types
 */

/**
 * @typedef {Object} ExchangeConfig
 * @property {string} exchangeId - Exchange identifier
 * @property {string} name - Exchange display name
 * @property {boolean} enabled - Whether exchange is enabled
 * @property {ExchangeStatus} status - Current exchange status
 * @property {Object} credentials - API credentials
 * @property {string} credentials.apiKey - API key
 * @property {string} credentials.secret - API secret
 * @property {string} [credentials.password] - Password for exchanges that require it
 * @property {string} [credentials.uid] - User ID for exchanges that require it
 * @property {Object} rateLimits - Rate limiting configuration
 * @property {number} rateLimits.requestsPerSecond - Max requests per second
 * @property {number} rateLimits.requestsPerMinute - Max requests per minute
 * @property {Object} options - Additional exchange options
 * @property {number} options.recvWindow - Receive window in ms
 * @property {boolean} options.enableRateLimit - Enable rate limiting
 * @property {boolean} options.enableWebSocket - Enable WebSocket connections
 * @property {Object} websocket - WebSocket configuration
 * @property {string} websocket.url - WebSocket URL
 * @property {Array<string>} websocket.channels - Subscription channels
 * @property {number} websocket.reconnectDelay - Reconnect delay in ms
 * @property {number} websocket.maxReconnectAttempts - Maximum reconnect attempts
 */

/**
 * @typedef {Object} ExchangeStatus
 * @property {string} state - Exchange state (online, offline, maintenance, error)
 * @property {string} lastCheck - Last health check timestamp
 * @property {boolean} healthy - Whether exchange is healthy
 * @property {Array<string>} errors - Current errors
 * @property {Array<string>} warnings - Current warnings
 * @property {number} latency - Current latency in ms
 * @property {number} uptime - Exchange uptime in seconds
 */

/**
 * @typedef {Object} CCXTExchange
 * @property {string} id - Exchange ID
 * @property {string} name - Exchange name
 * @property {Object} markets - Available markets
 * @property {Object} symbols - Trading symbols
 * @property {Object} currencies - Supported currencies
 * @property {Object} rateLimits - Rate limiting info
 * @property {Object} options - Exchange options
 * @property {Function} loadMarkets - Load market data
 * @property {Function} fetchTicker - Fetch ticker data
 * @property {Function} fetchOrderBook - Fetch order book
 * @property {Function} fetchTrades - Fetch trades
 * @property {Function} fetchOHLCV - Fetch OHLCV data
 * @property {Function} fetchBalance - Fetch account balance
 * @property {Function} createOrder - Create new order
 * @property {Function} cancelOrder - Cancel existing order
 * @property {Function} fetchOrder - Fetch order details
 * @property {Function} fetchOpenOrders - Fetch open orders
 * @property {Function} fetchClosedOrders - Fetch closed orders
 * @property {Function} fetchMyTrades - Fetch user trades
 * @property {Function} fetchDeposits - Fetch deposits
 * @property {Function} fetchWithdrawals - Fetch withdrawals
 * @property {Function} fetchLedger - Fetch ledger entries
 * @property {Function} close - Close exchange connection
 */

/**
 * @typedef {Object} Market
 * @property {string} id - Market ID
 * @property {string} symbol - Symbol
 * @property {string} base - Base currency
 * @property {string} quote - Quote currency
 * @property {string} type - Market type (spot, futures, etc.)
 * @property {MarketLimits} limits - Trading limits
 * @property {MarketPrecision} precision - Precision requirements
 * @property {boolean} active - Whether market is active
 * @property {Object} info - Raw market info from exchange
 */

/**
 * @typedef {Object} MarketLimits
 * @property {Object} amount - Amount limits
 * @property {number} amount.min - Minimum amount
 * @property {number} amount.max - Maximum amount
 * @property {Object} cost - Cost limits
 * @property {number} cost.min - Minimum cost
 * @property {number} cost.max - Maximum cost
 * @property {Object} price - Price limits
 * @property {number} price.min - Minimum price
 * @property {number} price.max - Maximum price
 * @property {Object} leverage - Leverage limits
 * @property {number} leverage.min - Minimum leverage
 * @property {number} leverage.max - Maximum leverage
 */

/**
 * @typedef {Object} MarketPrecision
 * @property {number} price - Price precision (number of decimal places)
 * @property {number} amount - Amount precision (number of decimal places)
 * @property {number} cost - Cost precision (number of decimal places)
 * @property {number} base - Base currency precision
 * @property {number} quote - Quote currency precision
 */

/**
 * @typedef {Object} Ticker
 * @property {string} symbol - Trading symbol
 * @property {number} timestamp - Timestamp
 * @property {number} datetime - Datetime
 * @property {number} high - High price
 * @property {number} low - Low price
 * @property {number} bid - Bid price
 * @property {number} bidVolume - Bid volume
 * @property {number} ask - Ask price
 * @property {number} askVolume - Ask volume
 * @property {number} vwap - Volume weighted average price
 * @property {number} open - Open price
 * @property {number} close - Close price
 * @property {number} last - Last price
 * @property {number} previousClose - Previous close price
 * @property {number} change - Price change
 * @property {number} percentage - Price change percentage
 * @property {number} average - Average price
 * @property {number} baseVolume - Base volume
 * @property {number} quoteVolume - Quote volume
 * @property {Object} info - Raw ticker info
 */

/**
 * @typedef {Object} OrderBook
 * @property {string} symbol - Trading symbol
 * @property {number} timestamp - Timestamp
 * @property {number} datetime - Datetime
 * @property {Array<Array<number>>} bids - Bid orders [price, amount]
 * @property {Array<Array<number>>} asks - Ask orders [price, amount]
 * @property {Object} info - Raw order book info
 */

/**
 * @typedef {Object} TradeData
 * @property {string} id - Trade ID
 * @property {string} symbol - Trading symbol
 * @property {number} timestamp - Timestamp
 * @property {number} datetime - Datetime
 * @property {string} side - Trade side (buy/sell)
 * @property {number} price - Trade price
 * @property {number} amount - Trade amount
 * @property {number} cost - Trade cost
 * @property {Object} info - Raw trade info
 */

/**
 * @typedef {Object} OHLCV
 * @property {number} timestamp - Timestamp
 * @property {number} open - Open price
 * @property {number} high - High price
 * @property {number} low - Low price
 * @property {number} close - Close price
 * @property {number} volume - Volume
 */

/**
 * @typedef {Object} Balance
 * @property {Object} free - Free balance by currency
 * @property {Object} used - Used balance by currency
 * @property {Object} total - Total balance by currency
 * @property {Object} info - Raw balance info
 */

/**
 * @typedef {Object} ExchangeManager
 * @property {Function} initialize - Initialize exchange manager
 * @property {Function} addExchange - Add new exchange
 * @property {Function} removeExchange - Remove exchange
 * @property {Function} getExchange - Get exchange instance
 * @property {Function} getExchanges - Get all exchanges
 * @property {Function} getExchangeStatus - Get exchange status
 * @property {Function} enableExchange - Enable exchange
 * @property {Function} disableExchange - Disable exchange
 * @property {Function} configureExchange - Update exchange configuration
 * @property {Function} loadMarkets - Load markets for all exchanges
 * @property {Function} getMarkets - Get all available markets
 * @property {Function} getExchangeStats - Get exchange statistics
 */

/**
 * @typedef {Object} ConnectionPool
 * @property {Function} addConnection - Add new connection
 * @property {Function} removeConnection - Remove connection
 * @property {Function} getConnection - Get connection
 * @property {Function} getConnections - Get all connections
 * @property {Function} checkHealth - Check connection health
 * @property {Function} closeAll - Close all connections
 */

/**
 * @typedef {Object} WebSocketConnection
 * @property {string} id - Connection ID
 * @property {string} url - WebSocket URL
 * @property {string} exchange - Exchange name
 * @property {Array<string>} channels - Subscription channels
 * @property {boolean} connected - Connection status
 * @property {Function} connect - Connect to WebSocket
 * @property {Function} disconnect - Disconnect from WebSocket
 * @property {Function} subscribe - Subscribe to channel
 * @property {Function} unsubscribe - Unsubscribe from channel
 * @property {Function} send - Send message
 * @property {Function} onMessage - Message handler
 */

module.exports = {
  ExchangeConfig,
  ExchangeStatus,
  CCXTExchange,
  Market,
  MarketLimits,
  MarketPrecision,
  Ticker,
  OrderBook,
  TradeData,
  OHLCV,
  Balance,
  ExchangeManager,
  ConnectionPool,
  WebSocketConnection,
};
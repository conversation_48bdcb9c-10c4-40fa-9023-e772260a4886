/**
 * Central type exports for the electronTrader trading system
 * @module trading-types
 */

// Core trading types
module.exports = {
  // Export all type definitions
  ...require('./trading-types'),
  ...require('./exchange-types'),
  ...require('./portfolio-types'),
  ...require('./config-types'),
  ...require('./database-types'),
  ...require('./monitoring-types'),
  ...require('./ai-types'),
  ...require('./risk-types'),
  ...require('./analysis-types'),
  ...require('./backtesting-types'),
  ...require('./testing-types'),
  ...require('./remaining-types'),
};
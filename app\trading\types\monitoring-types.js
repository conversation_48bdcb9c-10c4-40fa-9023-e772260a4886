/**
 * Monitoring and health check type definitions
 * @module monitoring-types
 */

/**
 * @typedef {Object} HealthMonitor
 * @property {string} monitorId - Health monitor identifier
 * @property {string} name - Monitor name
 * @property {Array<string>} checks - Health checks to perform
 * @property {number} checkInterval - Check interval in milliseconds
 * @property {Object} thresholds - Health thresholds
 * @property {Array<string>} alertChannels - Alert channels
 * @property {Function} start - Start monitoring
 * @property {Function} stop - Stop monitoring
 * @property {Function} checkHealth - Perform health check
 * @property {Function} getStatus - Get current health status
 * @property {Function} getMetrics - Get performance metrics
 * @property {Function} sendAlert - Send health alert
 */

/**
 * @typedef {Object} HealthCheck
 * @property {string} checkId - Check identifier
 * @property {string} name - Check name
 * @property {string} type - Check type (database, api, exchange, system)
 * @property {Function} check - Check function
 * @property {number} timeout - Check timeout in milliseconds
 * @property {number} retryAttempts - Number of retry attempts
 * @property {number} retryDelay - Retry delay in milliseconds
 * @property {Object} expected - Expected results
 * @property {Function} onSuccess - Success handler
 * @property {Function} onFailure - Failure handler
 */

/**
 * @typedef {Object} HealthStatus
 * @property {string} statusId - Status identifier
 * @property {string} timestamp - Status timestamp
 * @property {string} overallStatus - Overall health status (healthy, degraded, unhealthy)
 * @property {Object} checks - Individual check results
 * @property {Array<string>} errors - Current errors
 * @property {Array<string>} warnings - Current warnings
 * @property {Object} metrics - Performance metrics
 * @property {number} responseTime - Average response time
 * @property {number} uptime - System uptime
 */

/**
 * @typedef {Object} PerformanceMonitor
 * @property {string} monitorId - Monitor identifier
 * @property {string> name - Monitor name
 * @property {Array<string>> metrics - Metrics to monitor
 * @property {number> collectionInterval - Data collection interval (ms)
 * @property {number> retentionPeriod - Data retention period (ms)
 * @property {Object> thresholds - Performance thresholds
 * @property {Function> start - Start monitoring
 * @property {Function> stop - Stop monitoring
 * @property {Function> collectMetrics - Collect performance metrics
 * @property {Function> analyzeMetrics - Analyze performance data
 * @property {Function> getReport - Generate performance report
 */

/**
 * @typedef {Object} PerformanceMetrics
 * @property {string> metricId - Metric identifier
 * @property {string> name - Metric name
 * @property {string> timestamp - Metric timestamp
 * @property {number> value - Metric value
 * @property {string> unit - Metric unit
 * @property {Object> tags - Metric tags
 * @property {Object> metadata - Additional metadata
 */

/**
 * @typedef {Object} AlertManager
 * @property {string> managerId - Alert manager identifier
 * @property {Array<string>> channels - Alert channels
 * @property {Object> templates - Alert templates
 * @property {Object> rules - Alert rules
 * @property {Function> sendAlert - Send alert
 * @property {Function> broadcast - Broadcast alert
 * @property {Function> scheduleAlert - Schedule alert
 * @property {Function> getHistory - Get alert history
 */

/**
 * @typedef {Object} Alert
 * @property {string> alertId - Alert identifier
 * @property {string> type - Alert type (info, warning, error, critical)
 * @property {string> title - Alert title
 * @property {string> message - Alert message
 * @property {string> timestamp - Alert timestamp
 * @property {Object> data - Additional alert data
 * @property {Array<string>> recipients - Alert recipients
 * @property {string> status - Alert status (pending, sent, delivered, failed)
 */

/**
 * @typedef {Object} MetricsCollector
 * @property {string> collectorId - Collector identifier
 * @property {string> name - Collector name
 * @property {Array<string>> sources - Data sources
 * @property {number> collectionInterval - Collection interval (ms)
 * @property {Object> filters - Data filters
 * @property {Function> collect - Collect metrics
 * @property {Function> aggregate - Aggregate metrics
 * @property {Function> export - Export metrics
 */

/**
 * @typedef {Object> SystemMetrics
 * @property {string> systemId - System identifier
 * @property {string> timestamp - Metrics timestamp
 * @property {number> cpuUsage - CPU usage percentage
 * @property {number> memoryUsage - Memory usage in MB
 * @property {number> diskUsage - Disk usage in GB
 * @property {number> networkUsage - Network usage in MB/s
 * @property {number> processCount - Number of processes
 * @property {number> threadCount - Number of threads
 * @property {number> uptime - System uptime in seconds
 * @property {Object> loadAverage - System load averages
 */

/**
 * @typedef {Object> ApplicationMetrics
 * @property {string> appId - Application identifier
 * @property {string> timestamp - Metrics timestamp
 * @property {number> requestCount - Number of requests
 * @property {number> errorCount - Number of errors
 * @property {number> responseTime - Average response time (ms)
 * @property {number> throughput - Requests per second
 * @property {number> activeConnections - Active connections
 * @property {number> memoryUsage - Memory usage (MB)
 * @property {number> cpuUsage - CPU usage percentage
 * @property {Object> endpoints - Endpoint metrics
 */

/**
 * @typedef {Object> TradingMetrics
 * @property {string> tradingId - Trading identifier
 * @property {string> timestamp - Metrics timestamp
 * @property {number> tradesExecuted - Number of trades executed
 * @property {number> tradesSuccessful - Successful trades
 * @property {number> tradesFailed - Failed trades
 * @property {number> totalVolume - Total trading volume
 * @property {number> totalFees - Total trading fees
 * @property {number> averageLatency - Average execution latency (ms)
 * @property {number> peakLatency - Peak execution latency (ms)
 * @property {number> errorRate - Error rate percentage
 * @property {number> throughput - Requests per second
 * @property {Object> exchangeMetrics - Exchange-specific metrics
 */

/**
 * @typedef {Object> Dashboard
 * @property {string> dashboardId - Dashboard identifier
 * @property {string> name - Dashboard name
 * @property {Array<Object>> widgets - Dashboard widgets
 * @property {Object> layout - Dashboard layout
 * @property {Object> data - Dashboard data
 * @property {Function> render - Render dashboard
 * @property {Function> update - Update dashboard
 * @property {Function> export - Export dashboard
 */

/**
 * @typedef {Object> Widget
 * @property {string> widgetId - Widget identifier
 * @property {string> type - Widget type (chart, table, metric, alert)
 * @property {string> title - Widget title
 * @property {Object> config - Widget configuration
 * @property {Object> data - Widget data
 * @property {Function> render - Render widget
 * @property {Function> update - Update widget
 * @property {Function> onClick - Click handler
 */

module.exports = {
  HealthMonitor,
  HealthCheck,
  HealthStatus,
  PerformanceMonitor,
  PerformanceMetrics,
  AlertManager,
  Alert,
  MetricsCollector,
  SystemMetrics,
  ApplicationMetrics,
  TradingMetrics,
  Dashboard,
  Widget,
};
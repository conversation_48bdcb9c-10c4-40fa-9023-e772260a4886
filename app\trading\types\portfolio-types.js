/**
 * Portfolio and position management type definitions
 * @module portfolio-types
 */

/**
 * @typedef {Object} Portfolio
 * @property {string} portfolioId - Unique portfolio identifier
 * @property {string} name - Portfolio name
 * @property {string} description - Portfolio description
 * @property {string} baseCurrency - Base currency (e.g., USD, USDT)
 * @property {number} totalValue - Total portfolio value
 * @property {number} cash - Cash balance
 * @property {number} marketValue - Market value of positions
 * @property {number} unrealizedPL - Unrealized profit/loss
 * @property {number} realizedPL - Realized profit/loss
 * @property {Array<Position>} positions - Current positions
 * @property {PortfolioPerformance} performance - Portfolio performance metrics
 * @property {PortfolioConfig} config - Portfolio configuration
 * @property {string} createdAt - Creation timestamp
 * @property {string} updatedAt - Last update timestamp
 */

/**
 * @typedef {Object} Position
 * @property {string} positionId - Unique position identifier
 * @property {string} symbol - Trading symbol
 * @property {string} exchange - Exchange name
 * @property {string} side - Position side (long/short)
 * @property {number} quantity - Position quantity
 * @property {number} entryPrice - Entry price
 * @property {number} currentPrice - Current market price
 * @property {number} averagePrice - Average entry price
 * @property {number} marketValue - Current market value
 * @property {number} unrealizedPL - Unrealized profit/loss
 * @property {number} unrealizedPLPercent - Unrealized profit/loss percentage
 * @property {number} realizedPL - Realized profit/loss
 * @property {number} leverage - Leverage used
 * @property {number} margin - Margin used
 * @property {string} status - Position status (open, closed, liquidated)
 * @property {string} openedAt - Position opened timestamp
 * @property {string} closedAt - Position closed timestamp
 * @property {PositionConfig} config - Position configuration
 * @property {Array<Trade>} trades - Related trades
 */

/**
 * @typedef {Object} PortfolioPerformance
 * @property {number} totalReturn - Total return percentage
 * @property {number} totalReturnValue - Total return value
 * @property {number} dailyReturn - Daily return percentage
 * @property {number} weeklyReturn - Weekly return percentage
 * @property {number} monthlyReturn - Monthly return percentage
 * @property {number} yearlyReturn - Yearly return percentage
 * @property {number} annualizedReturn - Annualized return
 * @property {number} volatility - Portfolio volatility
 * @property {number} sharpeRatio - Sharpe ratio
 * @property {number} maxDrawdown - Maximum drawdown
 * @property {number} currentDrawdown - Current drawdown
 * @property {number} winRate - Win rate percentage
 * @property {number} averageWin - Average win amount
 * @property {number} averageLoss - Average loss amount
 * @property {number} profitFactor - Profit factor
 * @property {number} expectedValue - Expected value
 * @property {number} calmarRatio - Calmar ratio
 * @property {number} sortinoRatio - Sortino ratio
 */

/**
 * @typedef {Object} PortfolioConfig
 * @property {string} portfolioId - Portfolio identifier
 * @property {string} name - Portfolio name
 * @property {string} baseCurrency - Base currency
 * @property {number} initialCapital - Initial capital
 * @property {number} maxPositions - Maximum number of positions
 * @property {number} maxPositionSize - Maximum position size (percentage)
 * @property {number} maxRiskPerTrade - Maximum risk per trade (percentage)
 * @property {number} maxTotalRisk - Maximum total risk (percentage)
 * @property {number} stopLoss - Default stop loss (percentage)
 * @property {number} takeProfit - Default take profit (percentage)
 * @property {boolean} autoRebalance - Auto rebalance positions
 * @property {boolean} enableLeverage - Enable leverage
 * @property {number} maxLeverage - Maximum leverage
 * @property {Object} riskManagement - Risk management settings
 * @property {Object} notifications - Notification settings
 */

/**
 * @typedef {Object} PositionConfig
 * @property {string} positionId - Position identifier
 * @property {string} symbol - Trading symbol
 * @property {string} strategy - Strategy name
 * @property {number} stopLoss - Stop loss price
 * @property {number} takeProfit - Take profit price
 * @property {number} trailingStop - Trailing stop distance
 * @property {number} maxLoss - Maximum loss amount
 * @property {number} maxProfit - Maximum profit amount
 * @property {number} timeLimit - Time limit in hours
 * @property {boolean} autoClose - Auto close position
 * @property {Object} alerts - Position alerts
 */

/**
 * @typedef {Object} AssetAllocation
 * @property {string} symbol - Asset symbol
 * @property {number} targetWeight - Target allocation weight
 * @property {number} currentWeight - Current allocation weight
 * @property {number} deviation - Deviation from target
 * @property {number} value - Asset value
 * @property {number} quantity - Asset quantity
 */

/**
 * @typedef {Object} PortfolioManager
 * @property {Function} createPortfolio - Create new portfolio
 * @property {Function} getPortfolio - Get portfolio by ID
 * @property {Function} updatePortfolio - Update portfolio
 * @property {Function} deletePortfolio - Delete portfolio
 * @property {Function} addPosition - Add position to portfolio
 * @property {Function} removePosition - Remove position from portfolio
 * @property {Function} updatePosition - Update position
 * @property {Function} getPositions - Get all positions
 * @property {Function} getPosition - Get specific position
 * @property {Function} calculatePortfolioValue - Calculate portfolio value
 * @property {Function} calculatePerformance - Calculate performance metrics
 * @property {Function} rebalance - Rebalance portfolio
 * @property {Function} getAssetAllocation - Get asset allocation
 * @property {Function} generateReport - Generate portfolio report
 */

/**
 * @typedef {Object} PositionManager
 * @property {Function} openPosition - Open new position
 * @property {Function} closePosition - Close position
 * @property {Function} modifyPosition - Modify position
 * @property {Function} getPosition - Get position details
 * @property {Function} getPositions - Get all positions
 * @property {Function} calculatePositionValue - Calculate position value
 * @property {Function} calculateUnrealizedPL - Calculate unrealized P&L
 * @property {Function} checkStopLoss - Check stop loss conditions
 * @property {Function} checkTakeProfit - Check take profit conditions
 * @property {Function} getPositionHistory - Get position history
 */

/**
 * @typedef {Object} PortfolioSnapshot
 * @property {string} snapshotId - Snapshot identifier
 * @property {string} portfolioId - Portfolio identifier
 * @property {string} timestamp - Snapshot timestamp
 * @property {number} totalValue - Total portfolio value
 * @property {number} cash - Cash balance
 * @property {number} marketValue - Market value
 * @property {number> unrealizedPL - Unrealized P&L
 * @property {number} realizedPL - Realized P&L
 * @property {Array<PositionSnapshot>} positions - Position snapshots
 */

/**
 * @typedef {Object} PositionSnapshot
 * @property {string} positionId - Position identifier
 * @property {string} symbol - Trading symbol
 * @property {string} side - Position side
 * @property {number} quantity - Position quantity
 * @property {number} entryPrice - Entry price
 * @property {number> currentPrice - Current price
 * @property {number> unrealizedPL - Unrealized P&L
 * @property {number> unrealizedPLPercent - Unrealized P&L percentage
 * @property {string> timestamp - Snapshot timestamp
 */

/**
 * @typedef {Object} PortfolioHistory
 * @property {string> historyId - History identifier
 * @property {string> portfolioId - Portfolio identifier
 * @property {string> timestamp - History timestamp
 * @property {number> totalValue - Total portfolio value
 * @property {number> cash - Cash balance
 * @property {number> marketValue - Market value
 * @property {number> unrealizedPL - Unrealized P&L
 * @property {number> realizedPL - Realized P&L
 * @property {Object> metrics - Performance metrics
 */

module.exports = {
  Portfolio,
  Position,
  PortfolioPerformance,
  PortfolioConfig,
  PositionConfig,
  AssetAllocation,
  PortfolioManager,
  PositionManager,
  PortfolioSnapshot,
  PositionSnapshot,
  PortfolioHistory,
};
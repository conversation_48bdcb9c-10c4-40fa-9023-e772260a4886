/**
 * Remaining type definitions for complete system coverage
 * @module remaining-types
 */

// IPC and Communication Types
/**
 * @typedef {Object} IPCMessage
 * @property {string} type - Message type
 * @property {string} channel - IPC channel
 * @property {Object} payload - Message payload
 * @property {string} correlationId - Correlation ID
 * @property {string} timestamp - Message timestamp
 * @property {string} source - Message source
 * @property {string} target - Message target
 * @property {Object} metadata - Additional metadata
 */

/**
 * @typedef {Object} IPCService
 * @property {string} serviceName - Service name
 * @property {Object} channels - IPC channels
 * @property {Function} sendMessage - Send IPC message
 * @property {Function} receiveMessage - Receive IPC message
 * @property {Function} registerHandler - Register message handler
 * @property {Function} unregisterHandler - Unregister message handler
 */

/**
 * @typedef {Object} WebhookHandler
 * @property {string} handlerId - Handler identifier
 * @property {string} endpoint - Webhook endpoint
 * @property {Array<string>} events - Events to handle
 * @property {Function} handle - Handle webhook event
 * @property {Function} validate - Validate webhook payload
 * @property {Function} respond - Send webhook response
 */

// Security Types
/**
 * @typedef {Object} SecurityManager
 * @property {string} managerId - Security manager identifier
 * @property {Object} config - Security configuration
 * @property {Function} authenticate - Authenticate user
 * @property {Function} authorize - Authorize action
 * @property {Function} encrypt - Encrypt sensitive data
 * @property {Function} decrypt - Decrypt sensitive data
 * @property {Function} hash - Hash data
 * @property {Function} validate - Validate security
 */

/**
 * @typedef {Object} CredentialManager
 * @property {string} managerId - Credential manager identifier
 * @property {Function} store - Store credentials
 * @property {Function} retrieve - Retrieve credentials
 * @property {Function} update - Update credentials
 * @property {Function} delete - Delete credentials
 * @property {Function} rotate - Rotate credentials
 */

// Backup and Recovery Types
/**
 * @typedef {Object} BackupManager
 * @property {string} managerId - Backup manager identifier
 * @property {Object} config - Backup configuration
 * @property {Function} createBackup - Create backup
 * @property {Function} restoreBackup - Restore from backup
 * @property {Function} listBackups - List available backups
 * @property {Function} validateBackup - Validate backup integrity
 * @property {Function} scheduleBackup - Schedule backup
 */

/**
 * @typedef {Object} RecoveryManager
 * @property {string} managerId - Recovery manager identifier
 * @property {Object} config - Recovery configuration
 * @property {Function} recover - Perform recovery
 * @property {Function} validate - Validate recovery
 * @property {Function} getStatus - Get recovery status
 * @property {Function} rollback - Rollback changes
 */

// Validation Types
/**
 * @typedef {Object} ValidationEngine
 * @property {string} engineId - Validation engine identifier
 * @property {Object} schemas - Validation schemas
 * @property {Function} validate - Validate data
 * @property {Function} sanitize - Sanitize input
 * @property {Function} checkConstraints - Check constraints
 * @property {Function} getErrors - Get validation errors
 */

/**
 * @typedef {Object} InputValidator
 * @property {string} validatorId - Validator identifier
 * @property {Object} rules - Validation rules
 * @property {Function} validate - Validate input
 * @property {Function} getErrors - Get validation errors
 * @property {Function} sanitize - Sanitize input
 */

// Performance Types
/**
 * @typedef {Object} PerformanceEngine
 * @property {string} engineId - Performance engine identifier
 * @property {Object} config - Performance configuration
 * @property {Function} measure - Measure performance
 * @property {Function} optimize - Optimize performance
 * @property {Function} benchmark - Benchmark performance
 * @property {Function} generateReport - Generate performance report
 */

/**
 * @typedef {Object} CacheManager
 * @property {string} managerId - Cache manager identifier
 * @property {Object} config - Cache configuration
 * @property {Function} get - Get cached item
 * @property {Function} set - Set cached item
 * @property {Function} delete - Delete cached item
 * @property {Function} clear - Clear cache
 * @property {Function} getStats - Get cache statistics
 */

// Integration Types
/**
 * @typedef {Object} IntegrationManager
 * @property {string} managerId - Integration manager identifier
 * @property {Object} adapters - Integration adapters
 * @property {Function} connect - Connect to external system
 * @property {Function} disconnect - Disconnect from system
 * @property {Function} send - Send data
 * @property {Function} receive - Receive data
 * @property {Function} validate - Validate integration
 */

/**
 * @typedef {Object} APIAdapter
 * @property {string} adapterId - Adapter identifier
 * @property {string} endpoint - API endpoint
 * @property {Object} config - Adapter configuration
 * @property {Function} connect - Connect to API
 * @property {Function} disconnect - Disconnect from API
 * @property {Function} sendRequest - Send API request
 * @property {Function} handleResponse - Handle API response
 */

// Reporting Types
/**
 * @typedef {Object} ReportGenerator
 * @property {string} generatorId - Generator identifier
 * @property {string} name - Generator name
 * @property {Object} templates - Report templates
 * @property {Function} generate - Generate report
 * @property {Function} export - Export report
 * @property {Function} schedule - Schedule report
 */

/**
 * @typedef {Object} Report
 * @property {string} reportId - Report identifier
 * @property {string} title - Report title
 * @property {string} type - Report type
 * @property {Object} data - Report data
 * @property {string} generatedAt - Generation timestamp
 * @property {Object} metadata - Report metadata
 */

module.exports = {
  IPCMessage,
  IPCService,
  WebhookHandler,
  SecurityManager,
  CredentialManager,
  BackupManager,
  RecoveryManager,
  ValidationEngine,
  InputValidator,
  PerformanceEngine,
  CacheManager,
  IntegrationManager,
  APIAdapter,
  ReportGenerator,
  Report,
};
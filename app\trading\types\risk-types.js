/**
 * Risk management type definitions
 * @module risk-types
 */

/**
 * @typedef {Object} RiskManager
 * @property {string} managerId - Risk manager identifier
 * @property {string} name - Risk manager name
 * @property {RiskConfig} config - Risk configuration
 * @property {RiskMetrics} metrics - Current risk metrics
 * @property {Array<RiskLimit>} limits - Risk limits
 * @property {Function} assessRisk - Assess risk for a trade
 * @property {Function} calculatePositionSize - Calculate optimal position size
 * @property {Function} checkStopLoss - Check stop loss conditions
 * @property {Function} checkTakeProfit - Check take profit conditions
 * @property {Function} getRiskMetrics - Get current risk metrics
 * @property {Function} setRiskParameters - Set risk parameters
 * @property {Function} validateTrade - Validate trade against risk rules
 */

/**
 * @typedef {Object} RiskConfig
 * @property {string} configId - Configuration identifier
 * @property {number} maxRiskPerTrade - Maximum risk per trade (percentage)
 * @property {number} maxTotalRisk - Maximum total portfolio risk (percentage)
 * @property {number} maxPositionSize - Maximum position size (percentage)
 * @property {number} maxDailyLoss - Maximum daily loss (percentage)
 * @property {number} maxDrawdown - Maximum drawdown (percentage)
 * @property {number} stopLoss - Default stop loss (percentage)
 * @property {number} takeProfit - Default take profit (percentage)
 * @property {number} leverageLimit - Maximum leverage
 * @property {Object} riskRules - Risk rules and conditions
 * @property {Object} alerts - Risk alerts configuration
 */

/**
 * @typedef {Object} RiskMetrics
 * @property {string} metricsId - Metrics identifier
 * @property {string} timestamp - Metrics timestamp
 * @property {number} totalRisk - Total current risk exposure
 * @property {number} activePositions - Number of active positions
 * @property {number} maxDrawdown - Maximum drawdown experienced
 * @property {number} currentDrawdown - Current drawdown
 * @property {string} riskLevel - Risk level (low, medium, high, critical)
 * @property {number} riskScore - Risk score (0-100)
 * @property {number} var - Value at risk
 * @property {number} expectedShortfall - Expected shortfall
 * @property {number} sharpeRatio - Sharpe ratio
 * @property {number} sortinoRatio - Sortino ratio
 * @property {number} calmarRatio - Calmar ratio
 */

/**
 * @typedef {Object} RiskLimit
 * @property {string} limitId - Limit identifier
 * @property {string} name - Limit name
 * @property {string} type - Limit type (position, daily, total, drawdown)
 * @property {number} value - Limit value
 * @property {string} unit - Limit unit (percentage, amount)
 * @property {boolean} enabled - Whether limit is enabled
 * @property {string} action - Action when limit is reached (warn, block, close)
 * @property {Array<string>} notifications - Notification channels
 */

/**
 * @typedef {Object} RiskAssessment
 * @property {string} assessmentId - Assessment identifier
 * @property {string} tradeId - Related trade identifier
 * @property {boolean} approved - Whether trade is approved
 * @property {string} riskLevel - Risk level (low, medium, high)
 * @property {number} riskScore - Risk score (0-100)
 * @property {number} positionSize - Recommended position size
 * @property {number} stopLoss - Recommended stop loss
 * @property {number} takeProfit - Recommended take profit
 * @property {Array<string>} warnings - Risk warnings
 * @property {Array<string>} recommendations - Risk recommendations
 * @property {Object} details - Detailed risk analysis
 */

/**
 * @typedef {Object} PositionRisk
 * @property {string} positionId - Position identifier
 * @property {string} symbol - Trading symbol
 * @property {number} riskAmount - Risk amount
 * @property {number} riskPercentage - Risk percentage
 * @property {number} potentialProfit - Potential profit
 * @property {number} potentialLoss - Potential loss
 * @property {number} riskRewardRatio - Risk to reward ratio
 * @property {number} probabilityOfSuccess - Probability of success
 * @property {string} riskLevel - Risk level
 */

/**
 * @typedef {Object} PortfolioRisk
 * @property {string} portfolioId - Portfolio identifier
 * @property {number} totalRisk - Total portfolio risk
 * @property {number> concentrationRisk - Concentration risk
 * @property {number> correlationRisk - Correlation risk
 * @property {number> volatilityRisk - Volatility risk
 * @property {number> liquidityRisk - Liquidity risk
 * @property {number> marketRisk - Market risk
 * @property {Array<PositionRisk>> positionRisks - Individual position risks
 * @property {Object> riskBreakdown - Risk breakdown by category
 */

/**
 * @typedef {Object} RiskAlert
 * @property {string> alertId - Alert identifier
 * @property {string> type - Alert type (risk_limit, stop_loss, take_profit)
 * @property {string> level - Alert level (info, warning, critical)
 * @property {string> message - Alert message
 * @property {string> timestamp - Alert timestamp
 * @property {Object> data - Additional alert data
 * @property {Array<string>> recipients - Alert recipients
 */

/**
 * @typedef {Object} StopLossManager
 * @property {string> managerId - Manager identifier
 * @property {Object> config - Stop loss configuration
 * @property {Array<StopLossOrder>> orders - Active stop loss orders
 * @property {Function> setStopLoss - Set stop loss
 * @property {Function> updateStopLoss - Update stop loss
 * @property {Function> cancelStopLoss - Cancel stop loss
 * @property {Function> triggerStopLoss - Trigger stop loss
 * @property {Function> getStatus - Get stop loss status
 */

/**
 * @typedef {Object} StopLossOrder
 * @property {string> orderId - Order identifier
 * @property {string> positionId - Position identifier
 * @property {string> symbol - Trading symbol
 * @property {number> triggerPrice - Trigger price
 * @property {number> stopPrice - Stop price
 * @property {string> type - Stop loss type (fixed, trailing, percentage)
 * @property {string> status - Order status (active, triggered, executed, cancelled)
 * @property {string> timestamp - Order timestamp
 */

/**
 * @typedef {Object> RiskReport
 * @property {string> reportId - Report identifier
 * @property {string> title - Report title
 * @property {string> period - Report period
 * @property {Object> summary - Risk summary
 * @property {Array<RiskMetric>> metrics - Risk metrics
 * @property {Array<RiskAlert>> alerts - Risk alerts
 * @property {Array<PositionRisk>> positionRisks - Position risks
 * @property {PortfolioRisk> portfolioRisk - Portfolio risk
 * @property {string> generatedAt - Report generation timestamp
 */

/**
 * @typedef {Object> RiskMetric
 * @property {string> metricId - Metric identifier
 * @property {string> name - Metric name
 * @property {number> value - Metric value
 * @property {string> unit - Metric unit
 * @property {string> timestamp - Metric timestamp
 * @property {string> benchmark - Benchmark value
 * @property {string> trend - Trend direction (up, down, stable)
 */

/**
 * @typedef {Object> CircuitBreaker
 * @property {string> breakerId - Circuit breaker identifier
 * @property {string> name - Circuit breaker name
 * @property {number> threshold - Error threshold (percentage)
 * @property {number> timeout - Timeout in milliseconds
 * @property {number> resetTimeout - Reset timeout in milliseconds
 * @property {string> state - Current state (closed, open, half-open)
 * @property {Function> fire - Execute function with circuit breaker
 * @property {Function> open - Open circuit breaker
 * @property {Function> close - Close circuit breaker
 * @property {Function> getStats - Get circuit breaker statistics
 */

/**
 * @typedef {Object> PositionSizer
 * @property {string> sizerId - Sizer identifier
 * @property {Object> config - Position sizing configuration
 * @property {Function> calculateSize - Calculate position size
 * @property {Function> validateSize - Validate position size
 * @property {Function> getMaxSize - Get maximum position size
 * @property {Function> getMinSize - Get minimum position size
 */

module.exports = {
  RiskManager,
  RiskConfig,
  RiskMetrics,
  RiskLimit,
  RiskAssessment,
  PositionRisk,
  PortfolioRisk,
  RiskAlert,
  StopLossManager,
  StopLossOrder,
  RiskReport,
  RiskMetric,
  CircuitBreaker,
  PositionSizer,
};
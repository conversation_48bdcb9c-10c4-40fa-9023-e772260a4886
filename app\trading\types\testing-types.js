/**
 * Testing and validation type definitions
 * @module testing-types
 */

/**
 * @typedef {Object} TestSuite
 * @property {string} suiteId - Test suite identifier
 * @property {string} name - Test suite name
 * @property {string} description - Test suite description
 * @property {Array<TestCase>} testCases - Test cases
 * @property {TestConfig} config - Test configuration
 * @property {TestResult} results - Test results
 * @property {Function} run - Run test suite
 * @property {Function} getResults - Get test results
 * @property {Function} generateReport - Generate test report
 */

/**
 * @typedef {Object} TestCase
 * @property {string} caseId - Test case identifier
 * @property {string} name - Test case name
 * @property {string} description - Test case description
 * @property {string} category - Test category (unit, integration, e2e)
 * @property {string} priority - Test priority (high, medium, low)
 * @property {Function} test - Test function
 * @property {Array<string>} prerequisites - Test prerequisites
 * @property {Object} data - Test data
 * @property {TestResult} expected - Expected result
 * @property {TestResult} actual - Actual result
 * @property {string} status - Test status (pending, running, passed, failed, skipped)
 */

/**
 * @typedef {Object} TestConfig
 * @property {string} configId - Configuration identifier
 * @property {string} name - Configuration name
 * @property {string} environment - Test environment
 * @property {number} timeout - Test timeout in milliseconds
 * @property {number} retryAttempts - Number of retry attempts
 * @property {Array<string>} browsers - Browsers to test
 * @property {Array<string>} devices - Devices to test
 * @property {Object} coverage - Code coverage settings
 * @property {Object} performance - Performance testing settings
 * @property {Object} security - Security testing settings
 */

/**
 * @typedef {Object} TestResult
 * @property {string} resultId - Result identifier
 * @property {string} caseId - Test case identifier
 * @property {string} name - Test name
 * @property {boolean} passed - Whether test passed
 * @property {string} status - Test status (passed, failed, skipped, error)
 * @property {string} duration - Test duration
 * @property {Array<string>} errors - Test errors
 * @property {Array<string>} warnings - Test warnings
 * @property {Object} data - Test data
 * @property {string} timestamp - Test timestamp
 * @property {Object} performance - Performance metrics
 */

/**
 * @typedef {Object} TestRunner
 * @property {string} runnerId - Test runner identifier
 * @property {string} name - Test runner name
 * @property {TestConfig} config - Test configuration
 * @property {Array<TestSuite>} suites - Test suites
 * @property {Function} runSuite - Run test suite
 * @property {Function} runTest - Run individual test
 * @property {Function} getResults - Get test results
 * @property {Function} generateReport - Generate test report
 */

/**
 * @typedef {Object} IntegrationTest
 * @property {string} testId - Test identifier
 * @property {string} name - Test name
 * @property {string} description - Test description
 * @property {Array<string>} components - Components to test
 * @property {Array<string>} dependencies - Test dependencies
 * @property {TestData} testData - Test data
 * @property {TestResult} expected - Expected result
 * @property {Function} setup - Test setup
 * @property {Function} execute - Execute test
 * @property {Function} cleanup - Test cleanup
 * @property {Function} validate - Validate test results
 */

/**
 * @typedef {Object} EndToEndTest
 * @property {string} testId - Test identifier
 * @property {string} name - Test name
 * @property {string} description - Test description
 * @property {Array<string>} scenarios - Test scenarios
 * @property {Array<string>} userFlows - User flows to test
 * @property {Object} environment - Test environment
 * @property {Function} setupEnvironment - Setup test environment
 * @property {Function} executeScenario - Execute test scenario
 * @property {Function} cleanupEnvironment - Cleanup test environment
 * @property {Function} validateResults - Validate test results
 */

/**
 * @typedef {Object} PerformanceTest
 * @property {string} testId - Test identifier
 * @property {string} name - Test name
 * @property {string} description - Test description
 * @property {Object} metrics - Performance metrics to measure
 * @property {number} load - Load level
 * @property {number} duration - Test duration
 * @property {Function} setup - Test setup
 * @property {Function} run - Run performance test
 * @property {Function} measure - Measure performance
 * @property {Function} analyze - Analyze results
 * @property {Function} generateReport - Generate performance report
 */

/**
 * @typedef {Object} SecurityTest
 * @property {string} testId - Test identifier
 * @property {string} name - Test name
 * @property {string} description - Test description
 * @property {Array<string>} vulnerabilities - Vulnerabilities to test
 * @property {Object} attackVectors - Attack vectors to test
 * @property {Function> setup - Test setup
 * @property {Function> execute - Execute security test
 * @property {Function> validate - Validate security
 * @property {Function> generateReport - Generate security report
 */

/**
 * @typedef {Object} TestData
 * @property {string} dataId - Data identifier
 * @property {string} name - Data name
 * @property {Object} values - Data values
 * @property {Object} metadata - Data metadata
 * @property {string} createdAt - Creation timestamp
 * @property {string} updatedAt - Update timestamp
 */

/**
 * @typedef {Object} TestReport
 * @property {string} reportId - Report identifier
 * @property {string} title - Report title
 * @property {string} description - Report description
 * @property {Array<TestResult>} results - Test results
 * @property {Object} summary - Test summary
 * @property {Object} coverage - Code coverage
 * @property {Object} performance - Performance metrics
 * @property {Object} defects - Defect information
 * @property {string} generatedAt - Report generation timestamp
 */

/**
 * @typedef {Object} MockData
 * @property {string} mockId - Mock identifier
 * @property {string} name - Mock name
 * @property {string} type - Mock type (database, api, exchange, etc.)
 * @property {Object} data - Mock data
 * @property {Function> setup - Setup mock
 * @property {Function> teardown - Teardown mock
 * @property {Function> reset - Reset mock
 */

/**
 * @typedef {Object> TestEnvironment
 * @property {string> envId - Environment identifier
 * @property {string> name - Environment name
 * @property {string> type - Environment type (local, staging, production)
 * @property {Object> config - Environment configuration
 * @property {Array<string>> services - Available services
 * @property {Function> setup - Setup environment
 * @property {Function> cleanup - Cleanup environment
 * @property {Function> reset - Reset environment
 */

module.exports = {
  TestSuite,
  TestCase,
  TestConfig,
  TestResult,
  TestRunner,
  IntegrationTest,
  EndToEndTest,
  PerformanceTest,
  SecurityTest,
  TestData,
  TestReport,
  MockData,
  TestEnvironment,
};
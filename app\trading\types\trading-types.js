/**
 * Core trading system type definitions
 * @module trading-types
 */

/**
 * @typedef {Object} TradingSystem
 * @property {string} systemId - Unique system identifier
 * @property {string} name - System name
 * @property {string} version - System version
 * @property {SystemStatus} status - Current system status
 * @property {SystemConfig} config - System configuration
 * @property {SystemMetrics} metrics - Runtime metrics
 * @property {Function} initialize - Initialize system
 * @property {Function} start - Start system
 * @property {Function} stop - Stop system
 * @property {Function} shutdown - Shutdown system
 * @property {Function} getStatus - Get system status
 * @property {Function} getMetrics - Get system metrics
 */

/**
 * @typedef {Object} SystemStatus
 * @property {string} state - Current state (stopped, starting, running, stopping, error)
 * @property {string} health - Health status (healthy, degraded, unhealthy)
 * @property {string} lastStateChange - Last state change timestamp
 * @property {string} uptime - System uptime
 * @property {Array<string>} activeComponents - Active component list
 * @property {Array<string>} errors - Current errors
 * @property {Array<string>} warnings - Current warnings
 */

/**
 * @typedef {Object} SystemConfig
 * @property {string} systemId - System identifier
 * @property {string} name - System name
 * @property {Object} parameters - System parameters
 * @property {Object} thresholds - Threshold configurations
 * @property {Object} limits - Limit configurations
 * @property {Object} timeouts - Timeout configurations
 * @property {Object} retryPolicy - Retry policy
 * @property {Object} circuitBreaker - Circuit breaker configuration
 * @property {Object} monitoring - Monitoring configuration
 * @property {Object} logging - Logging configuration
 * @property {Object} security - Security configuration
 */

/**
 * @typedef {Object} SystemMetrics
 * @property {number} tradesExecuted - Total trades executed
 * @property {number} tradesSuccessful - Successful trades
 * @property {number} tradesFailed - Failed trades
 * @property {number} totalVolume - Total volume traded
 * @property {number} totalFees - Total fees paid
 * @property {number} averageLatency - Average execution latency
 * @property {number} peakLatency - Peak execution latency
 * @property {number} errorRate - Error rate percentage
 * @property {number} throughput - Requests per second
 */

/**
 * @typedef {Object} TradingSession
 * @property {string} sessionId - Unique session identifier
 * @property {string} startTime - Session start time
 * @property {string} endTime - Session end time
 * @property {SessionStatus} status - Session status
 * @property {Array<Trade>} trades - Executed trades
 * @property {Object} performance - Session performance metrics
 * @property {Object} configuration - Session configuration
 */

/**
 * @typedef {Object} SessionStatus
 * @property {string} state - Session state (active, paused, completed, failed)
 * @property {string} reason - Status reason
 * @property {string} timestamp - Status timestamp
 */

/**
 * @typedef {Object} Trade
 * @property {string} tradeId - Unique trade identifier
 * @property {string} symbol - Trading symbol
 * @property {string} side - Trade side (buy/sell)
 * @property {number} amount - Trade amount
 * @property {number} price - Trade price
 * @property {number} value - Trade value
 * @property {string} timestamp - Trade timestamp
 * @property {string} exchange - Exchange name
 * @property {string} status - Trade status
 * @property {number} fees - Trading fees
 * @property {Object} metadata - Additional trade metadata
 */

/**
 * @typedef {Object} TradingStrategy
 * @property {string} strategyId - Strategy identifier
 * @property {string} name - Strategy name
 * @property {string} description - Strategy description
 * @property {Object} parameters - Strategy parameters
 * @property {Array<string>} symbols - Trading symbols
 * @property {Function} evaluate - Evaluate strategy
 * @property {Function} execute - Execute strategy
 * @property {Function} backtest - Backtest strategy
 */

/**
 * @typedef {Object} MarketData
 * @property {string} symbol - Trading symbol
 * @property {number} price - Current price
 * @property {number} volume - Trading volume
 * @property {number} change24h - 24h price change
 * @property {number} high24h - 24h high
 * @property {number} low24h - 24h low
 * @property {string} timestamp - Data timestamp
 * @property {Object} orderBook - Order book data
 * @property {Array<Object>} trades - Recent trades
 */

/**
 * @typedef {Object} TradingAlert
 * @property {string} alertId - Alert identifier
 * @property {string} type - Alert type (price, volume, news, etc.)
 * @property {string} symbol - Trading symbol
 * @property {string} message - Alert message
 * @property {string} severity - Alert severity (info, warning, error, critical)
 * @property {string} timestamp - Alert timestamp
 * @property {Object} data - Additional alert data
 */

/**
 * @typedef {Object} TradingPerformance
 * @property {number} totalTrades - Total number of trades
 * @property {number} winningTrades - Number of winning trades
 * @property {number} losingTrades - Number of losing trades
 * @property {number} totalProfit - Total profit
 * @property {number} totalLoss - Total loss
 * @property {number} winRate - Win rate percentage
 * @property {number} averageWin - Average win amount
 * @property {number} averageLoss - Average loss amount
 * @property {number} maxDrawdown - Maximum drawdown
 * @property {number} sharpeRatio - Sharpe ratio
 * @property {number} profitFactor - Profit factor
 */

/**
 * @typedef {Object} RiskMetrics
 * @property {number} exposure - Current exposure
 * @property {number} riskPerTrade - Risk per trade
 * @property {number} totalRisk - Total risk
 * @property {number} maxDrawdown - Maximum drawdown
 * @property {number} var - Value at risk
 * @property {number} expectedShortfall - Expected shortfall
 * @property {number} beta - Beta coefficient
 * @property {number} correlation - Correlation coefficient
 */

/**
 * @typedef {Object} OrderBook
 * @property {Array<Order>} bids - Bid orders
 * @property {Array<Order>} asks - Ask orders
 * @property {number} spread - Bid-ask spread
 * @property {number} depth - Market depth
 * @property {string} timestamp - Order book timestamp
 */

/**
 * @typedef {Object} Order
 * @property {string} orderId - Order identifier
 * @property {string} symbol - Trading symbol
 * @property {string} type - Order type (market, limit, stop, etc.)
 * @property {string} side - Order side (buy/sell)
 * @property {number} amount - Order amount
 * @property {number} price - Order price
 * @property {string} status - Order status
 * @property {string} timestamp - Order timestamp
 * @property {Object} metadata - Additional order metadata
 */

module.exports = {
  TradingSystem,
  SystemStatus,
  SystemConfig,
  SystemMetrics,
  TradingSession,
  SessionStatus,
  Trade,
  TradingStrategy,
  MarketData,
  TradingAlert,
  TradingPerformance,
  RiskMetrics,
  OrderBook,
  Order,
};
/**
 * @fileoverview Trading System Logger
 * @description Centralized logging utility for the trading system
 */

const fs = require('fs');
const path = require('path');

class Logger {
  constructor(options = {}) {
    this.options = {
      logLevel: options.logLevel || 'info',
      logToFile: options.logToFile !== false,
      logToConsole: options.logToConsole !== false,
      logPath: options.logPath || path.join(__dirname, '../logs'),
      maxFileSize: options.maxFileSize || 10 * 1024 * 1024, // 10MB
      maxFiles: options.maxFiles || 5,
      ...options,
    };

    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
      trace: 4,
    };

    this.currentLevel = this.levels[this.options.logLevel] || this.levels.info;

    // Ensure log directory exists
    if (this.options.logToFile && !fs.existsSync(this.options.logPath)) {
      fs.mkdirSync(this.options.logPath, { recursive: true });
    }
  }

  /**
     * Log error message
     */
  error(message, ...args) {
    this.log('error', message, ...args);
  }

  /**
     * Log warning message
     */
  warn(message, ...args) {
    this.log('warn', message, ...args);
  }

  /**
     * Log info message
     */
  info(message, ...args) {
    this.log('info', message, ...args);
  }

  /**
     * Log debug message
     */
  debug(message, ...args) {
    this.log('debug', message, ...args);
  }

  /**
     * Log trace message
     */
  trace(message, ...args) {
    this.log('trace', message, ...args);
  }

  /**
     * Core logging method
     */
  log(level, message, ...args) {
    const levelNum = this.levels[level];
    if (levelNum > this.currentLevel) {
      return;
    }

    const timestamp = new Date().toISOString();
    const formattedMessage = this.formatMessage(timestamp, level, message, ...args);

    // Log to console
    if (this.options.logToConsole) {
      this.logToConsole(level, formattedMessage);
    }

    // Log to file
    if (this.options.logToFile) {
      this.logToFile(level, formattedMessage);
    }
  }

  /**
     * Format log message
     */
  formatMessage(timestamp, level, message, ...args) {
    const levelStr = level.toUpperCase().padEnd(5);
    let formattedMessage = `[${timestamp}] ${levelStr} ${message}`;

    if (args.length > 0) {
      const argsStr = args.map(arg => {
        if (typeof arg === 'object') {
          try {
            return JSON.stringify(arg, null, 2);
          } catch (e) {
            return String(arg);
          }
        }
        return String(arg);
      }).join(' ');
      formattedMessage += ` ${argsStr}`;
    }

    return formattedMessage;
  }

  /**
     * Log to console with colors
     */
  logToConsole(level, message) {
    const colors = {
      error: '\x1b[31m', // Red
      warn: '\x1b[33m',  // Yellow
      info: '\x1b[36m',  // Cyan
      debug: '\x1b[35m', // Magenta
      trace: '\x1b[37m',  // White
    };

    const reset = '\x1b[0m';
    const color = colors[level] || '';

    console.log(`${color}${message}${reset}`);
  }

  /**
     * Log to file
     */
  logToFile(level, message) {
    try {
      const logFile = path.join(this.options.logPath, `${level}.log`);
      const logEntry = `${message}\n`;

      // Check file size and rotate if necessary
      if (fs.existsSync(logFile)) {
        const stats = fs.statSync(logFile);
        if (stats.size > this.options.maxFileSize) {
          this.rotateLogFile(logFile);
        }
      }

      fs.appendFileSync(logFile, logEntry);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  /**
     * Rotate log file
     */
  rotateLogFile(logFile) {
    try {
      const ext = path.extname(logFile);
      const base = path.basename(logFile, ext);
      const dir = path.dirname(logFile);

      // Shift existing rotated files
      for (let i = this.options.maxFiles - 1; i > 0; i--) {
        const oldFile = path.join(dir, `${base}.${i}${ext}`);
        const newFile = path.join(dir, `${base}.${i + 1}${ext}`);

        if (fs.existsSync(oldFile)) {
          if (i === this.options.maxFiles - 1) {
            fs.unlinkSync(oldFile); // Delete oldest file
          } else {
            fs.renameSync(oldFile, newFile);
          }
        }
      }

      // Move current file to .1
      const rotatedFile = path.join(dir, `${base}.1${ext}`);
      fs.renameSync(logFile, rotatedFile);
    } catch (error) {
      console.error('Failed to rotate log file:', error);
    }
  }

  /**
     * Set log level
     */
  setLevel(level) {
    if (this.levels.hasOwnProperty(level)) {
      this.options.logLevel = level;
      this.currentLevel = this.levels[level];
    }
  }

  /**
     * Create child logger with prefix
     */
  child(prefix) {
    const childLogger = new Logger(this.options);
    const originalLog = childLogger.log.bind(childLogger);

    childLogger.log = (level, message, ...args) => {
      originalLog(level, `[${prefix}] ${message}`, ...args);
    };

    return childLogger;
  }

  /**
     * Log trading specific events
     */
  trade(action, symbol, data = {}) {
    this.info(`TRADE: ${action} ${symbol}`, data);
  }

  /**
     * Log bot specific events
     */
  bot(botId, action, data = {}) {
    this.info(`BOT: ${botId} ${action}`, data);
  }

  /**
     * Log whale tracking events
     */
  whale(address, action, data = {}) {
    this.info(`WHALE: ${address} ${action}`, data);
  }

  /**
     * Log performance metrics
     */
  performance(metric, value, unit = '') {
    this.info(`PERF: ${metric} = ${value}${unit}`);
  }

  /**
     * Log system health
     */
  health(component, status, data = {}) {
    const level = status === 'healthy' ? 'info' : 'warn';
    this.log(level, `HEALTH: ${component} is ${status}`, data);
  }
}

// Create default logger instance
const defaultLogger = new Logger();

// Export both the class and default instance
module.exports = defaultLogger;
module.exports.Logger = Logger;
module.exports.createLogger = (options) => new Logger(options);

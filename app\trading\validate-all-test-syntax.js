#!/usr/bin/env node

/**
 * COMPREHENSIVE TEST SYNTAX VALIDATOR
 * Validates syntax of all test files and reports any remaining errors
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class TestSyntaxValidator {
    constructor() {
        this.testFiles = [];
        this.validFiles = [];
        this.invalidFiles = [];
        this.totalErrors = 0;
    }

    async findAllTestFiles() {
        const testDirs = [
            'app/trading/__tests__',
            'app/trading/tests'
        ];

        for (const dir of testDirs) {
            if (fs.existsSync(dir)) {
                await this.scanDirectory(dir);
            }
        }

        console.log(`📁 Found ${this.testFiles.length} test files to validate`);
        return this.testFiles;
    }

    async scanDirectory(dirPath) {
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                await this.scanDirectory(fullPath);
            } else if (item.endsWith('.test.js') || item.endsWith('.spec.js')) {
                this.testFiles.push(fullPath);
            }
        }
    }

    async validateFile(filePath) {
        return new Promise((resolve) => {
            console.log(`🔍 Validating: ${filePath}`);
            
            const nodeProcess = spawn('node', ['-c', filePath], {
                stdio: ['pipe', 'pipe', 'pipe']
            });

            let stderr = '';
            
            nodeProcess.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            nodeProcess.on('close', (code) => {
                if (code === 0) {
                    console.log(`  ✅ Valid syntax`);
                    this.validFiles.push(filePath);
                } else {
                    console.log(`  ❌ Syntax error:`);
                    console.log(`     ${stderr.trim()}`);
                    this.invalidFiles.push({
                        file: filePath,
                        error: stderr.trim()
                    });
                    this.totalErrors++;
                }
                resolve();
            });

            nodeProcess.on('error', (error) => {
                console.log(`  ❌ Validation error: ${error.message}`);
                this.invalidFiles.push({
                    file: filePath,
                    error: error.message
                });
                this.totalErrors++;
                resolve();
            });
        });
    }

    async validateAllTestFiles() {
        console.log('🔍 COMPREHENSIVE TEST SYNTAX VALIDATOR');
        console.log('=====================================');
        console.log('');

        await this.findAllTestFiles();

        for (const filePath of this.testFiles) {
            await this.validateFile(filePath);
        }

        this.generateReport();
    }

    generateReport() {
        console.log('');
        console.log('📊 SYNTAX VALIDATION REPORT');
        console.log('===========================');
        console.log('');
        console.log(`📁 Total files checked: ${this.testFiles.length}`);
        console.log(`✅ Valid files: ${this.validFiles.length}`);
        console.log(`❌ Invalid files: ${this.invalidFiles.length}`);
        console.log(`🚨 Total syntax errors: ${this.totalErrors}`);
        console.log('');

        if (this.invalidFiles.length > 0) {
            console.log('❌ FILES WITH SYNTAX ERRORS:');
            console.log('============================');
            for (const invalid of this.invalidFiles) {
                console.log(`📄 ${invalid.file}`);
                console.log(`   Error: ${invalid.error}`);
                console.log('');
            }
        }

        if (this.invalidFiles.length === 0) {
            console.log('🎉 ALL TEST FILES HAVE VALID SYNTAX!');
            console.log('✅ No syntax errors found');
            console.log('✅ All test files are ready for execution');
        } else {
            console.log('⚠️  SYNTAX ERRORS STILL EXIST');
            console.log('❌ Some test files need additional fixes');
            console.log('');
            console.log('🔧 RECOMMENDED ACTIONS:');
            console.log('1. Review the error messages above');
            console.log('2. Fix the specific syntax issues');
            console.log('3. Re-run this validator');
        }
    }

    async fixCommonIssues() {
        console.log('🔧 ATTEMPTING TO FIX COMMON SYNTAX ISSUES');
        console.log('==========================================');
        console.log('');

        for (const invalid of this.invalidFiles) {
            await this.attemptAutoFix(invalid.file, invalid.error);
        }
    }

    async attemptAutoFix(filePath, errorMessage) {
        try {
            console.log(`🔧 Attempting to fix: ${filePath}`);
            
            let content = fs.readFileSync(filePath, 'utf8');
            let originalContent = content;
            let fixed = false;

            // Fix common patterns based on error message
            if (errorMessage.includes('new new Error')) {
                content = content.replace(/new:\s*new\s+Error/g, 'new Error');
                fixed = true;
            }

            if (errorMessage.includes('Unexpected token')) {
                // Fix double colons
                content = content.replace(/:\s*:/g, ':');
                // Fix malformed object literals
                content = content.replace(/(\w+):\s*(\w+):\s*/g, '$1: $2, ');
                fixed = true;
            }

            if (errorMessage.includes('await is only valid in async functions')) {
                // Find test functions that use await but aren't async
                const lines = content.split('\n');
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i];
                    if (line.match(/^\s*(test|it)\('.*', \(\) => \{/) && !line.includes('async')) {
                        // Check if this test uses await
                        let hasAwait = false;
                        let braceCount = 1;
                        for (let j = i + 1; j < lines.length && braceCount > 0; j++) {
                            const nextLine = lines[j];
                            if (nextLine.includes('await ')) {
                                hasAwait = true;
                                break;
                            }
                            braceCount += (nextLine.match(/\{/g) || []).length;
                            braceCount -= (nextLine.match(/\}/g) || []).length;
                        }
                        
                        if (hasAwait) {
                            lines[i] = line.replace(/\(\) => \{/, 'async () => {');
                            fixed = true;
                        }
                    }
                }
                content = lines.join('\n');
            }

            if (fixed && content !== originalContent) {
                fs.writeFileSync(filePath, content, 'utf8');
                console.log(`  ✅ Applied automatic fixes`);
            } else {
                console.log(`  ⚠️  No automatic fix available`);
            }

        } catch (error) {
            console.log(`  ❌ Error during auto-fix: ${error.message}`);
        }
    }
}

// Run the validator if called directly
if (require.main === module) {
    const validator = new TestSyntaxValidator();
    
    validator.validateAllTestFiles().then(async () => {
        if (validator.invalidFiles.length > 0) {
            console.log('');
            console.log('🔧 ATTEMPTING AUTOMATIC FIXES...');
            await validator.fixCommonIssues();
            
            console.log('');
            console.log('🔍 RE-VALIDATING AFTER FIXES...');
            const reValidator = new TestSyntaxValidator();
            await reValidator.validateAllTestFiles();
        }
    }).catch(console.error);
}

module.exports = TestSyntaxValidator;

#!/usr/bin/env node

/**
 * CORE TRADING SYSTEM VALIDATOR
 * Tests that all core trading components can be loaded and initialized
 */

const path = require('path');

class CoreTradingSystemValidator {
    constructor() {
        this.validComponents = [];
        this.invalidComponents = [];
        this.totalTests = 0;
        this.passedTests = 0;
    }

    async validateCoreSystem() {
        console.log('🔍 CORE TRADING SYSTEM VALIDATION');
        console.log('=================================');
        console.log('');

        // Test core component loading
        await this.testComponentLoading();
        
        // Test component initialization
        await this.testComponentInitialization();
        
        // Test basic functionality
        await this.testBasicFunctionality();

        this.generateReport();
    }

    async testComponentLoading() {
        console.log('📦 Phase 1: Testing Component Loading...');
        console.log('');

        const coreComponents = [
            { name: 'TradingOrchestrator', path: './engines/trading/orchestration/TradingOrchestrator' },
            { name: 'GridBotManager', path: './engines/trading/bots/GridBotManager' },
            { name: 'AutonomousTrader', path: './ai/AutonomousTrader' },
            { name: 'MemeCoinScanner', path: './engines/trading/MemeCoinScanner' },
            { name: 'SentimentAnalyzer', path: './analysis/SentimentAnalyzer' },
            { name: 'PerformanceTracker', path: './analysis/PerformanceTracker' },
            { name: 'ProductionExchangeConnector', path: './engines/exchange/ProductionExchangeConnector' }
        ];

        for (const component of coreComponents) {
            await this.testComponentLoad(component);
        }
    }

    async testComponentLoad(component) {
        this.totalTests++;
        
        try {
            console.log(`🔍 Loading ${component.name}...`);
            
            // Change to the trading directory for relative imports
            process.chdir(path.join(__dirname));
            
            const ComponentClass = require(component.path);
            
            if (ComponentClass) {
                console.log(`  ✅ ${component.name} loaded successfully`);
                this.validComponents.push(component.name);
                this.passedTests++;
            } else {
                throw new Error('Component class is null or undefined');
            }
            
        } catch (error) {
            console.log(`  ❌ ${component.name} failed to load: ${error.message}`);
            this.invalidComponents.push({ name: component.name, error: error.message });
        }
    }

    async testComponentInitialization() {
        console.log('');
        console.log('🔧 Phase 2: Testing Component Initialization...');
        console.log('');

        // Test GridBotManager initialization
        await this.testGridBotManagerInit();
        
        // Test AutonomousTrader initialization
        await this.testAutonomousTraderInit();
        
        // Test basic component creation
        await this.testBasicComponentCreation();
    }

    async testGridBotManagerInit() {
        this.totalTests++;
        
        try {
            console.log('🔍 Testing GridBotManager initialization...');
            
            const GridBotManager = require('./engines/trading/bots/GridBotManager');
            const manager = new GridBotManager({
                maxBotsPerExchange: 5,
                maxTotalBots: 15,
                autoCreateBots: false
            });
            
            const initResult = manager.initialize();
            
            if (initResult === true) {
                console.log('  ✅ GridBotManager initialized successfully');
                this.passedTests++;
            } else {
                throw new Error('Initialization returned false');
            }
            
        } catch (error) {
            console.log(`  ❌ GridBotManager initialization failed: ${error.message}`);
        }
    }

    async testAutonomousTraderInit() {
        this.totalTests++;
        
        try {
            console.log('🔍 Testing AutonomousTrader initialization...');
            
            const AutonomousTrader = require('./ai/AutonomousTrader');
            const trader = new AutonomousTrader({
                enableGridTrading: true,
                enableDCATrading: false,
                riskLevel: 'medium'
            });
            
            if (trader && typeof trader.start === 'function') {
                console.log('  ✅ AutonomousTrader created successfully');
                this.passedTests++;
            } else {
                throw new Error('AutonomousTrader missing required methods');
            }
            
        } catch (error) {
            console.log(`  ❌ AutonomousTrader initialization failed: ${error.message}`);
        }
    }

    async testBasicComponentCreation() {
        this.totalTests++;
        
        try {
            console.log('🔍 Testing basic component creation...');
            
            const SentimentAnalyzer = require('./analysis/SentimentAnalyzer');
            const PerformanceTracker = require('./analysis/PerformanceTracker');
            
            const sentiment = new SentimentAnalyzer();
            const performance = new PerformanceTracker();
            
            if (sentiment && performance) {
                console.log('  ✅ Analysis components created successfully');
                this.passedTests++;
            } else {
                throw new Error('Failed to create analysis components');
            }
            
        } catch (error) {
            console.log(`  ❌ Basic component creation failed: ${error.message}`);
        }
    }

    async testBasicFunctionality() {
        console.log('');
        console.log('⚡ Phase 3: Testing Basic Functionality...');
        console.log('');

        // Test GridBot creation
        await this.testGridBotCreation();
        
        // Test component methods
        await this.testComponentMethods();
    }

    async testGridBotCreation() {
        this.totalTests++;
        
        try {
            console.log('🔍 Testing GridBot creation...');
            
            const GridBotManager = require('./engines/trading/bots/GridBotManager');
            const manager = new GridBotManager();
            manager.initialize();
            
            const botConfig = {
                exchange: 'binance',
                symbol: 'BTC/USDT',
                gridSpacing: 0.5,
                gridLevels: 10,
                baseOrderSize: 10,
                autoStart: false
            };
            
            const botId = await manager.createBot(botConfig);
            
            if (botId && typeof botId === 'string') {
                console.log(`  ✅ GridBot created successfully: ${botId}`);
                this.passedTests++;
            } else {
                throw new Error('Failed to create GridBot');
            }
            
        } catch (error) {
            console.log(`  ❌ GridBot creation failed: ${error.message}`);
        }
    }

    async testComponentMethods() {
        this.totalTests++;
        
        try {
            console.log('🔍 Testing component methods...');
            
            const GridBotManager = require('./engines/trading/bots/GridBotManager');
            const manager = new GridBotManager();
            
            // Test method existence
            const requiredMethods = ['initialize', 'start', 'stop', 'createBot', 'startBot', 'stopBot'];
            const missingMethods = [];
            
            for (const method of requiredMethods) {
                if (typeof manager[method] !== 'function') {
                    missingMethods.push(method);
                }
            }
            
            if (missingMethods.length === 0) {
                console.log('  ✅ All required methods present');
                this.passedTests++;
            } else {
                throw new Error(`Missing methods: ${missingMethods.join(', ')}`);
            }
            
        } catch (error) {
            console.log(`  ❌ Component methods test failed: ${error.message}`);
        }
    }

    generateReport() {
        console.log('');
        console.log('📊 CORE TRADING SYSTEM VALIDATION REPORT');
        console.log('=========================================');
        console.log('');
        console.log(`📁 Total tests run: ${this.totalTests}`);
        console.log(`✅ Tests passed: ${this.passedTests}`);
        console.log(`❌ Tests failed: ${this.totalTests - this.passedTests}`);
        console.log(`📈 Success rate: ${Math.round((this.passedTests / this.totalTests) * 100)}%`);
        console.log('');

        if (this.validComponents.length > 0) {
            console.log('✅ VALID COMPONENTS:');
            for (const component of this.validComponents) {
                console.log(`  📄 ${component}`);
            }
            console.log('');
        }

        if (this.invalidComponents.length > 0) {
            console.log('❌ INVALID COMPONENTS:');
            for (const component of this.invalidComponents) {
                console.log(`  📄 ${component.name}: ${component.error}`);
            }
            console.log('');
        }

        if (this.passedTests === this.totalTests) {
            console.log('🎉 ALL CORE TRADING COMPONENTS ARE FUNCTIONAL!');
            console.log('✅ System is ready for autonomous trading');
            console.log('✅ All components can be loaded and initialized');
            console.log('✅ Basic functionality verified');
        } else {
            console.log('⚠️  SOME COMPONENTS NEED ATTENTION');
            console.log('❌ System may not be fully operational');
            console.log('🔧 Review failed components and fix remaining issues');
        }
    }
}

// Run the validator if called directly
if (require.main === module) {
    const validator = new CoreTradingSystemValidator();
    validator.validateCoreSystem().catch(console.error);
}

module.exports = CoreTradingSystemValidator;

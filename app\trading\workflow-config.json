{"workflows": {"dataCollection": {"name": "Market Data Collection", "description": "Collects market data from multiple sources", "triggers": ["scheduled", "on-demand"], "interval": 30000, "components": [{"name": "EnhancedDataCollector", "path": "./engines/data-collection/enhanced-data-collector.js", "methods": ["collectMarketData", "collectPriceData", "collectVolumeData"]}, {"name": "EliteWhaleTracker", "path": "./engines/trading/Elite.WhaleTracker.js", "methods": ["scanTransactions", "updateWhaleProfiles"]}], "outputs": ["marketData", "whaleActivity", "priceUpdates"]}, "analysis": {"name": "Market Analysis", "description": "Analyzes collected data and generates signals", "triggers": ["data-collection-complete"], "dependencies": ["dataCollection"], "components": [{"name": "SentimentAnalyzer", "path": "./engines/analysis/SentimentAnalyzer.js", "methods": ["analyzeSentiment", "getSocialMetrics"]}, {"name": "PerformanceTracker", "path": "./engines/analysis/PerformanceTracker.js", "methods": ["calculateMetrics", "trackPerformance"]}, {"name": "LLMCoordinator", "path": "./shared/ai/llm-coordinator.js", "methods": ["analyzeMarket", "generateInsights"]}], "outputs": ["signals", "insights", "riskScores"]}, "riskAssessment": {"name": "Risk Assessment", "description": "Evaluates risk for potential trades", "triggers": ["analysis-complete"], "dependencies": ["analysis"], "components": [{"name": "UnifiedRiskManager", "path": "./shared/risk/UnifiedRiskManager.js", "methods": ["calculatePositionSize", "assessRisk", "checkEmergencyThresholds"]}, {"name": "CircuitBreakerSystem", "path": "./shared/safety/circuit-breakers.js", "methods": ["checkBreakers", "validateOperation"]}], "outputs": ["approvedSignals", "positionSizes", "riskMetrics"]}, "trading": {"name": "Trade Execution", "description": "Executes approved trades", "triggers": ["risk-assessment-complete"], "dependencies": ["riskAssessment"], "components": [{"name": "ProductionTradingExecutor", "path": "./engines/trading/ProductionTradingExecutor.js", "methods": ["executeTrade", "placeOrder", "managePositions"]}, {"name": "GridBotManager", "path": "./engines/trading/GridBotManager.js", "methods": ["createGridBot", "monitorBots", "adjustParameters"]}, {"name": "PortfolioManager", "path": "./engines/trading/PortfolioManager.js", "methods": ["allocateCapital", "rebalance", "trackPositions"]}], "outputs": ["executedTrades", "botStatus", "portfolioUpdate"]}, "monitoring": {"name": "Performance Monitoring", "description": "Monitors system performance and health", "triggers": ["scheduled", "trade-complete"], "interval": 60000, "components": [{"name": "PerformanceTracker", "path": "./engines/analysis/PerformanceTracker.js", "methods": ["updateMetrics", "generateReports"]}, {"name": "EventCoordinator", "path": "./shared/orchestration/event-coordinator.js", "methods": ["logEvents", "checkHealth"]}], "outputs": ["performanceMetrics", "healthStatus", "alerts"]}}, "connections": {"dataFlow": [{"from": "dataCollection", "to": "analysis", "data": ["marketData", "whaleActivity"]}, {"from": "analysis", "to": "riskAssessment", "data": ["signals", "riskScores"]}, {"from": "riskAssessment", "to": "trading", "data": ["approvedSignals", "positionSizes"]}, {"from": "trading", "to": "monitoring", "data": ["executedTrades", "portfolioUpdate"]}], "eventFlow": [{"event": "data-collection-complete", "triggers": ["analysis"]}, {"event": "analysis-complete", "triggers": ["riskAssessment"]}, {"event": "risk-assessment-complete", "triggers": ["trading"]}, {"event": "trade-complete", "triggers": ["monitoring", "dataCollection"]}, {"event": "emergency-stop", "triggers": ["stopAllWorkflows"]}]}, "sharedResources": {"database": {"primary": "./databases/trading_bot.db", "backup": "./databases/trading_bot_backup.db"}, "logging": {"level": "info", "path": "./logs"}, "monitoring": {"healthCheck": {"interval": 60000, "endpoints": ["/health", "/metrics"]}}}, "emergencyProtocols": {"conditions": [{"type": "drawdown", "threshold": 0.1, "action": "stopNewTrades"}, {"type": "consecutiveLosses", "threshold": 5, "action": "emergencyStop"}, {"type": "apiErrors", "threshold": 10, "action": "degradedMode"}], "actions": {"stopNewTrades": {"description": "Stop opening new positions", "affectedWorkflows": ["trading"]}, "emergencyStop": {"description": "Stop all trading activities", "affectedWorkflows": ["trading", "analysis"]}, "degradedMode": {"description": "Run with reduced functionality", "affectedWorkflows": ["dataCollection"]}}}}
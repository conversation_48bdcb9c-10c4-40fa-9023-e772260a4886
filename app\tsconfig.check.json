{"compilerOptions": {"noEmit": true, "checkJs": true, "allowJs": true, "target": "ES2022", "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "jsx": "react-jsx"}, "include": ["src/**/*", "main.js", "preload.js"], "exclude": ["node_modules", "build", "dist", "coverage", "**/*.test.js", "**/*.spec.js", "**/*.refactored.js", "trading/**/*"]}
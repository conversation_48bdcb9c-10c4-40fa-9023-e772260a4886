{
  "compilerOptions": {
    // --- Core Options ---
    "target": "es2022", // Updated to a more recent ECMAScript version
    "module": "commonjs", // Set to CommonJS for Electron main/preload compatibility
    "moduleResolution": "node",
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "src/*"
      ],
      "@trading/*": [
        "trading/*"
      ],
      "@utils/*": [
        "src/utils/*"
      ],
      "@components/*": [
        "src/components/*"
      ],
      "@services/*": [
        "src/services/*"
      ]
    },
    "lib": [
      "dom",
      "es2022"
    ], // Match lib with target
    // --- Strictness & Code Quality ---
    "strict": true, // Recommended for better type safety
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "noUnusedLocals": true, // Helps keep code clean
    "noUnusedParameters": true, // Helps keep code clean
    // --- JS/TS Interoperability ---
    "allowJs": true,
    "checkJs": true, // Enable JS type-checking for stricter checks and gradual migration
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    // --- Output ---
    "noEmit": false, // Set to false to allow tsc to compile
    "outDir": "./dist",
    "removeComments": false,
    "sourceMap": true,
    "declaration": true,
    "declarationMap": true,
    // --- Advanced / Framework-specific ---
    "jsx": "react-jsx",
    "isolatedModules": true,
    "skipLibCheck": true,
    // The following options are typically used with frameworks like Angular or TypeORM.
    // Enable if you use decorators in your codebase.
    "experimentalDecorators": true
  },
  "include": [
    "src/**/*",
    "trading/**/*",
    "*.ts",
    "*.tsx",
    "*.js",
    "*.jsx"
  ],
  "exclude": [
    "node_modules",
    "build",
    "dist",
    "**/*.test.*",
    "**/*.spec.*"
  ]
  // Make sure your test runner config (e.g., jest.config.js) includes these patterns:
  // testMatch: ["**/?(*.)+(test|spec).[jt]s?(x)"]
}
/**
 * @fileoverview IPC Type Definitions for Main Process
 * @description Type definitions for IPC handler parameters and return values
 */

import {IpcMainInvokeEvent} from 'electron';

// Basic types
export type Symbol = string;
export type BotId = string;
export type ComponentName = string;
export type ExchangeName = string;
export type SessionId = string;
export type Timeframe = string;
export type FilePath = string;
export type FileExtension = string;
export type FileType = string;
export type SearchPattern = string;
export type SearchQuery = string;

// Configuration types
export interface TradingConfig {
    symbol: Symbol;
    amount?: number;
    price?: number;
    side?: 'buy' | 'sell';
    type?: 'market' | 'limit';

    [key: string]: any;
}

export interface GridBotConfig {
    symbol: Symbol;
    gridLevels: number;
    upperPrice: number;
    lowerPrice: number;
    investment: number;

    [key: string]: any;
}

export interface TradeParams {
    symbol: Symbol;
    side: 'buy' | 'sell';
    amount: number;
    price?: number;
    type: 'market' | 'limit';

    [key: string]: any;
}

export interface RiskParams {
    maxPositionSize?: number;
    stopLoss?: number;
    takeProfit?: number;
    maxDrawdown?: number;

    [key: string]: any;
}

export interface SessionData {
    startTime: number;
    endTime?: number;
    trades: any[];
    performance: any;

    [key: string]: any;
}

export interface ExchangeConfig {
    apiKey: string;
    secret: string;
    sandbox?: boolean;
    rateLimit?: number;

    [key: string]: any;
}

export interface BotMetrics {
    totalTrades: number;
    profitLoss: number;
    winRate: number;
    averageReturn: number;

    [key: string]: any;
}

export interface MarketAnalysis {
    symbol: Symbol;
    trend: 'bullish' | 'bearish' | 'neutral';
    signals: any[];
    timestamp: number;

    [key: string]: any;
}

export interface AppSettings {
    api?: any;
    trading?: any;
    whaleTracking?: any;
    performance?: any;
    monitoring?: any;

    [key: string]: any;
}

export interface CoinData {
    id?: string;
    symbol: Symbol;
    name: string;
    price?: number;
    marketCap?: number;

    [key: string]: any;
}

export interface SearchOptions {
    includeContent?: boolean;
    fileTypes?: string[];
    maxResults?: number;

    [key: string]: any;
}

export interface StatusFilter {
    level?: 'info' | 'warning' | 'error';
    component?: ComponentName;
    timeRange?: {
        start: number;
        end: number;
    };

    [key: string]: any;
}

// IPC Handler function types
export type IpcHandler<T = any, R = any> = (event: IpcMainInvokeEvent, ...args: T[]) => Promise<R> | R;

// Specific handler parameter types
export interface IpcHandlerParams {
    // Trading endpoints
    'get-open-orders': [Symbol?];
    'get-order-history': [Symbol?];
    'cancel-order': [string, Symbol];
    'cancel-all-orders': [Symbol];
    'execute-trade': [TradeParams];

    // Grid bot endpoints
    'start-grid-bot': [GridBotConfig];
    'stop-grid-bot': [Symbol];
    'get-grid-bot-history': [Symbol];
    'update-grid-bot-config': [GridBotConfig];

    // Market data endpoints
    'get-market-data': [Symbol, Timeframe];
    'get-sentiment-data': [Symbol];
    'get-price-history': [Symbol, Timeframe, number?, number?];

    // Health monitoring endpoints
    'get-component-health': [ComponentName];
    'run-health-check': [ComponentName?];

    // Settings endpoints
    'save-settings': [AppSettings];

    // Coin management endpoints
    'save-coin': [CoinData];

    // Context engine endpoints
    'context-store-trading-session': [SessionId, SessionData];
    'context-get-trading-session': [SessionId];
    'context-store-exchange-config': [ExchangeName, ExchangeConfig];
    'context-get-exchange-config': [ExchangeName];
    'context-store-position-risk': [Symbol, RiskParams];
    'context-get-position-risk': [Symbol];
    'context-store-bot-performance': [BotId, BotMetrics];
    'context-get-bot-performance': [BotId];
    'context-store-market-analysis': [Symbol, MarketAnalysis];
    'context-get-market-analysis': [Symbol];
    'context-search': [SearchPattern];

    // Workspace search endpoints
    'workspace-search': [SearchQuery, SearchOptions?];
    'workspace-get-files-by-type': [FileType];
    'workspace-get-files-by-extension': [FileExtension];
    'workspace-get-recent-files': [number?];
    'workspace-get-file': [FilePath];
    'workspace-get-file-dependencies': [FilePath];

    // Status reporting endpoints
    'get-status-reports': [number?, StatusFilter?];
}

// Response types
export interface IpcResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
}

export interface SystemStatus {
    isRunning: boolean;
    isInitialized: boolean;
    health: 'healthy' | 'warning' | 'error' | 'unknown';
    message?: string;
    timestamp: number;
    uptime?: number;
}

export interface SystemMetrics {
    performance: any;
    health: string;
    uptime: number;
    activeSignals: number;
    pendingTrades: number;
    lastUpdate: number;
    systemLoad: {
        activeBots: number;
        dataCollectionActive: boolean;
        analysisActive: boolean;
    };
}
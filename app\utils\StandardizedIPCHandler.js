'use strict';

const logger = require('../trading/shared/helpers/logger');

/**
 * Creates a standardized IPC handler.
 * @param {string} channel - The IPC channel name.
 * @param {(...args: any[]) => Promise<any>} handler - The async function to handle the request.
 * @param {Object} [_config={}] - Handler configuration.
 * @returns {(event: import('electron').IpcMainInvokeEvent, ...args: any[]) => Promise<{success: boolean, data: any, error: {message: string, stack?: string, code?: string} | null}>}
 */
function createHandler(channel, handler, _config = {}) {
  return async (event, ...args) => {
    try {
      logger.info(`IPC [${channel}] - Received request.`);
      const result = await handler(...args);
      logger.info(`IPC [${channel}] - Request successful.`);
      return {
        success: true,
        data: result,
        error: null,
      };
    } catch (error) {
      logger.error(`IPC [${channel}] - Request failed:`, {error});
      return {
        success: false,
        data: null,
        error: {
          message: error.message,
          stack: error.stack,
          code: error.code,
        },
      };
    }
  };
}

module.exports = {
  createHandler,
};
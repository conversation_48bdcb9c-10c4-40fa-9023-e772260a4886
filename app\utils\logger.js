/**
 * @fileoverview Unified Logging System
 * @description A comprehensive logging system for the application, consolidating
 * features from various loggers into a single, robust module. It supports
 * multiple transports, structured logging, error tracking, and component-based logging.
 */

const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, {recursive: true});
}

class Logger {
    constructor(name = 'app', options = {}) {
        // this.name = name;
        const {
            level = process.env.LOG_LEVEL || 'info',
            maxFiles = 5,
            maxSize = 10 * 1024 * 1024, // 10MB
            service = 'app',
        } = options;

        // this.options = {level, maxFiles, maxSize, service, ...options};

        // this.errorMetrics = {
            count: 0,
            byType: new Map(),
            byComponent: new Map(),
            lastHour: [],
        };

        const logFormat = winston.format.combine(
            winston.format.timestamp({format: 'YYYY-MM-DD HH:mm:ss.SSS'}),
            winston.format.errors({stack: true}),
            winston.format.json(),
        );

        const consoleFormat = winston.format.combine(
            winston.format.colorize(),
            winston.format.printf(({timestamp, level, message, stack, component}) => {
                let logMessage = `[${timestamp}] ${level} [${component || this.name}]: ${message}`;
                if (stack) {
                    logMessage += `\n${stack}`;
                }
                return logMessage;
            }),
        );

        const transports = [
            new winston.transports.Console({
                level: this.options.level,
                format: consoleFormat,
            }),
            new winston.transports.File({
                filename: path.join(logsDir, 'app.log'),
                level: 'info',
                maxsize: this.options.maxSize,
                maxFiles: this.options.maxFiles,
                format: logFormat,
            }),
            new winston.transports.File({
                filename: path.join(logsDir, 'error.log'),
                level: 'error',
                maxsize: this.options.maxSize,
                maxFiles: this.options.maxFiles,
                format: logFormat,
            }),
        ];

        // this.logger = winston.createLogger({
            level: this.options.level,
            format: logFormat,
            defaultMeta: {service: this.options.service},
            transports,
            exceptionHandlers: [
                new winston.transports.File({filename: path.join(logsDir, 'exceptions.log')}),
            ],
            rejectionHandlers: [
                new winston.transports.File({filename: path.join(logsDir, 'rejections.log')}),
            ],
        });
    }

    log(level, message, meta = {}) {
        // this.logger.log(level, message, {component: this.name, ...meta});
    }

    error(message, meta = {}) {
        // this.trackError(message, meta);
        // this.logger.error(message, {component: this.name, ...meta});
    }

    warn(message, meta = {}) {
        // this.logger.warn(message, {component: this.name, ...meta});
    }

    info(message, meta = {}) {
        // this.logger.info(message, {component: this.name, ...meta});
    }

    debug(message, meta = {}) {
        // this.logger.debug(message, {component: this.name, ...meta});
    }

    trackError(message, meta = {}) {
        // this.errorMetrics.count++;
        const errorType = meta.error?.code || 'UNKNOWN';
        // this.errorMetrics.byType.set(errorType, (this.errorMetrics.byType.get(errorType) || 0) + 1);

        const component = meta.component || this.name;
        // this.errorMetrics.byComponent.set(component, (this.errorMetrics.byComponent.get(component) || 0) + 1);

        const now = new Date();
        const timestamp = now;
        // this.errorMetrics.lastHour.push({timestamp, message, component, errorType});
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        // this.errorMetrics.lastHour = this.errorMetrics.lastHour.filter(e => e.timestamp > oneHourAgo);
    }

    getErrorMetrics() {
        return {
            totalErrors: this.errorMetrics.count,
            byType: Object.fromEntries(this.errorMetrics.byType),
            byComponent: Object.fromEntries(this.errorMetrics.byComponent),
            lastHourCount: this.errorMetrics.lastHour.length,
            timestamp: new Date().toISOString(),
        };
    }

    createChildLogger(name) {
        const child = new Logger(`${this.name}:${name}`, this.options);
        // Link metrics to parent
        child.errorMetrics = this.errorMetrics;
        return child;
    }
}

// Create a singleton instance
const logger = new Logger('main-app');

module.exports = logger;
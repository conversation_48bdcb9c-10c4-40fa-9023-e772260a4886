'use strict';

const fs = require('fs');
try {
  const content = fs.readFileSync('./main.js', 'utf8');

  // Count braces
  let openBraces = 0;
  let closeBraces = 0;
  let openParens = 0;
  let closeParens = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content[i];
    if (char === '{') openBraces++;
    if (char === '}') closeBraces++;
    if (char === '(') openParens++;
    if (char === ')') closeParens++;
  }

  // Brace analysis
  const braceBalance = openBraces - closeBraces;
  const parenBalance = openParens - closeParens;

  // Parentheses analysis
  if (braceBalance !== 0 || parenBalance !== 0) {
    process.stderr.write(`Syntax check failed balance: ${braceBalance}, Paren balance: ${parenBalance}\n`);
  }

  // Try to parse
  try {
    require('./main.js');
    process.stdout.write('Syntax is valid\n');
  } catch (error) {
    process.stderr.write(`Syntax error: ${error.message}\n`);
  }
} catch (error) {
  process.stderr.write(`Error reading file: ${error.message}\n`);
}
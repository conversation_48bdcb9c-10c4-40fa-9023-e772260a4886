/**
 * @fileoverview Complete Trading System Validation Script
 * @description Validates all components of the autonomous trading system
 */

const path = require('path');
const fs = require('fs');

// Validation results
const validationResults = {
  components: {},
  imports: {},
  exports: {},
  syntax: {},
  integration: {},
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    warnings: 0,
  },
};

/**
 * Validate file exists and is readable
 */
function validateFileExists(filePath, description) {
  const fullPath = path.resolve(__dirname, filePath);
  const exists = fs.existsSync(fullPath);

  validationResults.components[description] = {
    path: filePath,
    exists,
    status: exists ? 'PASS' : 'FAIL',
  };

  if (exists) {
    validationResults.summary.passed++;
  } else {
    validationResults.summary.failed++;
    console.log(`❌ Missing: ${description} at ${filePath}`);
  }

  validationResults.summary.total++;
  return exists;
}

/**
 * Validate JavaScript syntax
 */
function validateSyntax(filePath, description) {
  try {
    const fullPath = path.resolve(__dirname, filePath);
    if (!fs.existsSync(fullPath)) {
      return false;
    }

    const content = fs.readFileSync(fullPath, 'utf8');

    // Basic syntax validation
    const syntaxIssues = [];

    // Check for incomplete object literals
    const incompleteObjects = content.match(/{\s*[a-zA-Z_$][a-zA-Z0-9_$]*\s*[,}]/g);
    if (incompleteObjects) {
      syntaxIssues.push('Incomplete object literals detected');
    }

    // Check for missing semicolons in critical places
    const missingSemicolons = content.match(/}\s*\n\s*[a-zA-Z]/g);
    if (missingSemicolons) {
      syntaxIssues.push('Potential missing semicolons');
    }

    // Check for unmatched braces
    const openBraces = (content.match(/{/g) || []).length;
    const closeBraces = (content.match(/}/g) || []).length;
    if (openBraces !== closeBraces) {
      syntaxIssues.push(`Unmatched braces: ${openBraces} open, ${closeBraces} close`);
    }

    validationResults.syntax[description] = {
      path: filePath,
      issues: syntaxIssues,
      status: syntaxIssues.length === 0 ? 'PASS' : 'WARN',
    };

    if (syntaxIssues.length === 0) {
      validationResults.summary.passed++;
    } else {
      validationResults.summary.warnings++;
      console.log(`⚠️ Syntax issues in ${description}:`, syntaxIssues);
    }

    validationResults.summary.total++;
    return syntaxIssues.length === 0;
  } catch (error) {
    validationResults.syntax[description] = {
      path: filePath,
      error: error.message,
      status: 'FAIL',
    };
    validationResults.summary.failed++;
    validationResults.summary.total++;
    console.log(`❌ Syntax validation failed for ${description}:`, error.message);
    return false;
  }
}

/**
 * Validate module imports/exports
 */
function validateModuleSystem(filePath, description, expectedSystem) {
  try {
    const fullPath = path.resolve(__dirname, filePath);
    if (!fs.existsSync(fullPath)) {
      return false;
    }

    const content = fs.readFileSync(fullPath, 'utf8');

    const hasRequire = /require\s*\(/g.test(content);
    const hasImport = /import\s+/g.test(content);
    const hasModuleExports = /module\.exports/g.test(content);
    const hasExport = /export\s+/g.test(content);

    const issues = [];

    if (expectedSystem === 'commonjs') {
      if (hasImport) issues.push('ES6 imports found in CommonJS file');
      if (hasExport) issues.push('ES6 exports found in CommonJS file');
      if (!hasRequire && !hasModuleExports) issues.push('No CommonJS patterns found');
    } else if (expectedSystem === 'esmodule') {
      if (hasRequire) issues.push('CommonJS require found in ES Module file');
      if (hasModuleExports) issues.push('CommonJS exports found in ES Module file');
    }

    validationResults.imports[description] = {
      path: filePath,
      expectedSystem,
      hasRequire,
      hasImport,
      hasModuleExports,
      hasExport,
      issues,
      status: issues.length === 0 ? 'PASS' : 'WARN',
    };

    if (issues.length === 0) {
      validationResults.summary.passed++;
    } else {
      validationResults.summary.warnings++;
      console.log(`⚠️ Module system issues in ${description}:`, issues);
    }

    validationResults.summary.total++;
    return issues.length === 0;
  } catch (error) {
    validationResults.imports[description] = {
      path: filePath,
      error: error.message,
      status: 'FAIL',
    };
    validationResults.summary.failed++;
    validationResults.summary.total++;
    return false;
  }
}

/**
 * Run complete validation
 */
function runCompleteValidation() {
  console.log('🔍 Starting Complete Trading System Validation...\n');

  // Core Trading Components
  console.log('📦 Validating Core Trading Components...');
  validateFileExists('trading/engines/trading/orchestration/TradingOrchestrator.js', 'Trading Orchestrator');
  validateFileExists('trading/engines/trading/bots/GridBotManager.js', 'Grid Bot Manager');
  validateFileExists('trading/analysis/MemeCoinAnalyzer.js', 'Meme Coin Analyzer');
  validateFileExists('trading/engines/trading/whaletrader/Elite.WhaleTracker.js', 'Whale Tracker');
  validateFileExists('trading/analysis/SentimentAnalyzer.js', 'Sentiment Analyzer');
  validateFileExists('trading/analysis/PerformanceTracker.js', 'Performance Tracker');

  // Database Components
  console.log('\n🗄️ Validating Database Components...');
  validateFileExists('trading/engines/database/unified-database-initializer.js', 'Unified Database Initializer');
  validateFileExists('trading/engines/database/connection-manager.js', 'Connection Manager');

  // Error Handling
  console.log('\n🛡️ Validating Error Handling...');
  validateFileExists('trading/engines/shared/error-handling/ErrorHandler.js', 'Error Handler');

  // Utilities
  console.log('\n🔧 Validating Utilities...');
  validateFileExists('trading/utils/logger.js', 'Logger Utility');

  // Frontend Components
  console.log('\n🖥️ Validating Frontend Components...');
  validateFileExists('src/components/TradingDashboard.jsx', 'Trading Dashboard');
  validateFileExists('src/components/StartButton.jsx', 'Start Button');
  validateFileExists('src/components/PortfolioSummary.jsx', 'Portfolio Summary');
  validateFileExists('src/components/PositionManager.jsx', 'Position Manager');

  // Syntax Validation
  console.log('\n🔍 Validating Syntax...');
  validateSyntax('trading/engines/trading/orchestration/TradingOrchestrator.js', 'Trading Orchestrator Syntax');
  validateSyntax('trading/engines/trading/bots/GridBotManager.js', 'Grid Bot Manager Syntax');
  validateSyntax('trading/analysis/MemeCoinAnalyzer.js', 'Meme Coin Analyzer Syntax');

  // Module System Validation
  console.log('\n📋 Validating Module Systems...');
  validateModuleSystem('trading/engines/trading/orchestration/TradingOrchestrator.js', 'Trading Orchestrator Modules', 'commonjs');
  validateModuleSystem('trading/engines/trading/bots/GridBotManager.js', 'Grid Bot Manager Modules', 'commonjs');
  validateModuleSystem('src/components/TradingDashboard.jsx', 'Trading Dashboard Modules', 'esmodule');
  validateModuleSystem('src/components/StartButton.jsx', 'Start Button Modules', 'esmodule');

  // Generate Summary Report
  console.log('\n📊 Validation Summary:');
  console.log(`Total Tests: ${validationResults.summary.total}`);
  console.log(`✅ Passed: ${validationResults.summary.passed}`);
  console.log(`⚠️ Warnings: ${validationResults.summary.warnings}`);
  console.log(`❌ Failed: ${validationResults.summary.failed}`);

  const successRate = ((validationResults.summary.passed + validationResults.summary.warnings) / validationResults.summary.total * 100).toFixed(1);
  console.log(`📈 Success Rate: ${successRate}%`);

  // Overall Status
  if (validationResults.summary.failed === 0) {
    console.log('\n🎉 Trading System Validation: PASSED');
    console.log('✅ All critical components are present and functional');
    console.log('🚀 ElectronTrader is ready for autonomous trading operations');
  } else {
    console.log('\n⚠️ Trading System Validation: NEEDS ATTENTION');
    console.log(`❌ ${validationResults.summary.failed} critical issues found`);
  }

  return validationResults;
}

// Run validation if called directly
if (require.main === module) {
  runCompleteValidation();
}

module.exports = {
  runCompleteValidation,
  validateFileExists,
  validateSyntax,
  validateModuleSystem,
  validationResults,
};

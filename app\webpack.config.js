const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const webpack = require('webpack');

module.exports = (env, argv) => {
  const isDevelopment = argv.mode === 'development';
  const isProduction = !isDevelopment;

  return {
    mode: isDevelopment ? 'development' : 'production',
    entry: './src/index.jsx',
    output: {
      path: path.resolve(__dirname, 'build'),
      filename: isDevelopment ? '[name].js' : '[name].[contenthash].js',
      chunkFilename: isDevelopment ? '[name].chunk.js' : '[name].[contenthash].chunk.js',
      clean: true,
      publicPath: '/',
    },
    devtool: isDevelopment ? 'eval-cheap-module-source-map' : 'source-map',
    module: {
      rules: [
        {
          test: /\.(js|jsx)$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: [
                '@babel/preset-env',
                ['@babel/preset-react', {runtime: 'automatic'}],
              ],
              cacheDirectory: true,
              cacheCompression: false,
              compact: isProduction,
            },
          },
        },
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader', 'postcss-loader'],
        },
        {
          test: /\.(png|jpe?g|gif|svg|webp)$/i,
          type: 'asset/resource',
          generator: {
            filename: 'images/[name].[hash][ext]',
          },
        },
        {
          test: /\.(woff|woff2|eot|ttf|otf)$/i,
          type: 'asset/resource',
          generator: {
            filename: 'fonts/[name].[hash][ext]',
          },
        },
        {
          test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)$/i,
          type: 'asset/resource',
          generator: {
            filename: 'media/[name].[hash][ext]',
          },
        },
      ],
    },
    plugins: [
      new HtmlWebpackPlugin({
        template: './public/index.html',
        favicon: './public/favicon.ico',
        minify: isProduction ? {
          removeComments: true,
          collapseWhitespace: true,
          removeRedundantAttributes: true,
          useShortDoctype: true,
          removeEmptyAttributes: true,
          removeStyleLinkTypeAttributes: true,
          keepClosingSlash: true,
          minifyJS: true,
          minifyCSS: true,
          minifyURLs: true,
        } : false,
      }),
      new webpack.DefinePlugin({
        'process.env.NODE_ENV': JSON.stringify(argv.mode || 'development'),
        'process.env.ELECTRON_IS_DEV': JSON.stringify(isDevelopment ? '1' : '0'),
      }),
      // Provide Node.js globals for Electron renderer
      new webpack.ProvidePlugin({
        Buffer: ['buffer', 'Buffer'],
        process: 'process/browser',
      }),
      // Show build progress
      new webpack.ProgressPlugin(),
    ],
    resolve: {
      extensions: ['.js', '.jsx', '.json'],
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@components': path.resolve(__dirname, 'src/components'),
        '@utils': path.resolve(__dirname, 'src/utils'),
        '@styles': path.resolve(__dirname, 'src/styles'),
      },
      fallback: {
        // Node.js polyfills for Electron renderer
        'fs': false,
        'path': require.resolve('path-browserify'),
        'crypto': require.resolve('crypto-browserify'),
        'stream': require.resolve('stream-browserify'),
        'buffer': require.resolve('buffer/'),
        'util': require.resolve('util/'),
        'assert': require.resolve('assert/'),
        'process': require.resolve('process/browser'),
        'os': require.resolve('os-browserify/browser'),
        'url': require.resolve('url/'),
        'http': false,
        'https': false,
        'net': false,
        'tls': false,
        'child_process': false,
      },
    },
    optimization: {
      splitChunks: {
        chunks: 'all',
        minSize: 20000,
        maxSize: 70000,
        maxInitialRequests: 30,
        maxAsyncRequests: 30,
        cacheGroups: {
          // Critical vendor chunks
          react: {
            test: /[\\/]node_modules[\\/](react|react-dom|react-router-dom)[\\/]/,
            name: 'react-vendor',
            priority: 40,
            reuseExistingChunk: true,
            enforce: true,
          },
          mui: {
            test: /[\\/]node_modules[\\/](@mui|@emotion|@material-ui)[\\/]/,
            name: 'mui-vendor',
            priority: 30,
            reuseExistingChunk: true,
            enforce: true,
          },
          // Feature-specific chunks
          trading: {
            test: /[\\/]src[\\/]components[\\/](Trading|Position|Trade|Bot)/,
            name: 'trading-components',
            priority: 20,
            reuseExistingChunk: true,
            minChunks: 1,
          },
          dashboard: {
            test: /[\\/]src[\\/]components[\\/](Dashboard|Autonomous|Ultimate)/,
            name: 'dashboard-components',
            priority: 20,
            reuseExistingChunk: true,
            minChunks: 1,
          },
          portfolio: {
            test: /[\\/]src[\\/]components[\\/](Portfolio|Position)/,
            name: 'portfolio-components',
            priority: 20,
            reuseExistingChunk: true,
            minChunks: 1,
          },
          // Chart and animation libraries
          charts: {
            test: /[\\/]node_modules[\\/](recharts|framer-motion|d3)[\\/]/,
            name: 'charts-vendor',
            priority: 10,
            reuseExistingChunk: true,
          },
          // Utility libraries
          utils: {
            test: /[\\/]node_modules[\\/](lodash|moment|date-fns|uuid)[\\/]/,
            name: 'utils-vendor',
            priority: 10,
            reuseExistingChunk: true,
          },
          // Common vendor chunk
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendor',
            priority: -10,
            reuseExistingChunk: true,
            minChunks: 1,
          },
          // Common application code
          common: {
            name: 'common',
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true,
            chunks: 'all',
          },
        },
      },
      runtimeChunk: 'single',
      moduleIds: 'deterministic',
      chunkIds: 'deterministic',
      sideEffects: false,
      usedExports: true,
      // Enable module concatenation for better tree shaking
      concatenateModules: true,
      // Remove empty chunks
      removeEmptyChunks: true,
      // Merge duplicate chunks
      mergeDuplicateChunks: true,
    },
    performance: {
      hints: isProduction ? 'warning' : false,
      maxEntrypointSize: 512000,
      maxAssetSize: 512000,
    },
    devServer: {
      hot: true,
      port: 3000,
      open: false,
      compress: true,
      historyApiFallback: true,
      client: {
        overlay: {
          errors: true,
          warnings: false,
        },
        progress: true,
      },
    },
    stats: {
      colors: true,
      hash: false,
      version: false,
      timings: true,
      assets: false,
      chunks: false,
      modules: false,
      reasons: false,
      children: false,
      source: false,
      errors: true,
      errorDetails: true,
      warnings: true,
      publicPath: false,
    },
  };
};

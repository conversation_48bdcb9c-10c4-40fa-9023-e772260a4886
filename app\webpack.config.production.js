const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const webpack = require('webpack');
const CompressionPlugin = require('compression-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const {BundleAnalyzerPlugin} = require('webpack-bundle-analyzer');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');

module.exports = (env = {}) => {
  const isAnalyze = env.analyze === 'true' || process.env.ANALYZE_BUNDLE === 'true';

  return {
    mode: 'production',
    entry: {
      main: './src/index.jsx',
    },
    output: {
      path: path.resolve(__dirname, 'build'),
      filename: 'static/js/[name].[contenthash:8].js',
      chunkFilename: 'static/js/[name].[contenthash:8].chunk.js',
      assetModuleFilename: 'static/media/[name].[contenthash:8][ext]',
      clean: true,
      publicPath: './',
      crossOriginLoading: 'anonymous',
      hashFunction: 'xxhash64',
    },
    devtool: 'source-map',
    module: {
      rules: [
        {
          test: /\.(js|jsx)$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: [
                ['@babel/preset-env', {
                  targets: {
                    browsers: ['>0.2%', 'not dead', 'not op_mini all'],
                  },
                  useBuiltIns: 'usage',
                  corejs: 3,
                  modules: false,
                  bugfixes: true,
                  shippedProposals: true,
                }],
                ['@babel/preset-react', {
                  runtime: 'automatic',
                  development: false,
                }],
              ],
              plugins: [
                '@babel/plugin-syntax-dynamic-import',
                '@babel/plugin-proposal-class-properties',
                ['@babel/plugin-transform-runtime', {
                  corejs: false,
                  helpers: true,
                  regenerator: true,
                  useESModules: true,
                }],
              ],
              cacheDirectory: true,
              cacheCompression: false,
              compact: true,
            },
          },
        },
        {
          test: /\.css$/,
          use: [
            MiniCssExtractPlugin.loader,
            {
              loader: 'css-loader',
              options: {
                modules: {
                  auto: true,
                  localIdentName: '[hash:base64]',
                },
                importLoaders: 1,
                sourceMap: true,
              },
            },
            {
              loader: 'postcss-loader',
              options: {
                postcssOptions: {
                  plugins: [
                    'autoprefixer',
                    'postcss-preset-env',
                  ],
                },
                sourceMap: true,
              },
            },
          ],
        },
        {
          test: /\.(png|jpe?g|gif|svg|webp)$/i,
          type: 'asset',
          parser: {
            dataUrlCondition: {
              maxSize: 4 * 1024,
            },
          },
          generator: {
            filename: 'static/media/[name].[contenthash:8][ext]',
          },
        },
        {
          test: /\.(woff|woff2|eot|ttf|otf)$/i,
          type: 'asset/resource',
          generator: {
            filename: 'static/media/[name].[contenthash:8][ext]',
          },
        },
        {
          test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)$/i,
          type: 'asset/resource',
          generator: {
            filename: 'static/media/[name].[contenthash:8][ext]',
          },
        },
      ],
    },
    plugins: [
      new HtmlWebpackPlugin({
        template: './public/index.html',
        favicon: './public/favicon.ico',
        inject: true,
        minify: {
          removeComments: true,
          collapseWhitespace: true,
          removeRedundantAttributes: true,
          useShortDoctype: true,
          removeEmptyAttributes: true,
          removeStyleLinkTypeAttributes: true,
          keepClosingSlash: true,
          minifyJS: true,
          minifyCSS: true,
          minifyURLs: true,
          removeAttributeQuotes: true,
        },
      }),
      new MiniCssExtractPlugin({
        filename: 'static/css/[name].[contenthash:8].css',
        chunkFilename: 'static/css/[name].[contenthash:8].chunk.css',
        ignoreOrder: true,
      }),
      new webpack.DefinePlugin({
        'process.env.NODE_ENV': JSON.stringify('production'),
        'process.env.ELECTRON_IS_DEV': JSON.stringify('0'),
        'process.env.REACT_APP_VERSION': JSON.stringify(process.env.npm_package_version || '1.0.0'),
        'process.env.BUILD_TIME': JSON.stringify(new Date().toISOString()),
      }),
      new webpack.ProvidePlugin({
        Buffer: ['buffer', 'Buffer'],
        process: 'process/browser',
      }),
      new webpack.ProgressPlugin({
        activeModules: false,
        entries: true,
        modules: true,
        profile: false,
        dependencies: true,
        percentBy: 'entries',
      }),
      new CompressionPlugin({
        algorithm: 'gzip',
        test: /\.(js|css|html|svg)$/,
        threshold: 10240,
        minRatio: 0.8,
        deleteOriginalAssets: false,
        filename: '[path][base].gz',
      }),
      new CompressionPlugin({
        algorithm: 'brotliCompress',
        test: /\.(js|css|html|svg)$/,
        threshold: 10240,
        minRatio: 0.8,
        compressionOptions: {
          params: {
            [require('zlib').constants.BROTLI_PARAM_QUALITY]: 11,
          },
        },
        filename: '[path][base].br',
        deleteOriginalAssets: false,
      }),
      ...(isAnalyze ? [
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          openAnalyzer: false,
          reportFilename: 'bundle-analysis.html',
          generateStatsFile: true,
          statsFilename: 'bundle-stats.json',
          logLevel: 'info',
        }),
      ] : []),
    ],
    optimization: {
      minimize: true,
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            parse: {
              ecma: 8,
            },
            compress: {
              ecma: 5,
              warnings: false,
              comparisons: false,
              inline: 2,
              drop_console: true,
              drop_debugger: true,
              pure_funcs: ['console.log', 'console.info', 'console.warn', 'console.debug'],
              passes: 2,
            },
            mangle: {
              safari10: true,
            },
            output: {
              ecma: 5,
              comments: false,
              ascii_only: true,
            },
          },
          parallel: true,
          extractComments: false,
        }),
        new CssMinimizerPlugin({
          minimizerOptions: {
            preset: [
              'default',
              {
                discardComments: {removeAll: true},
              },
            ],
          },
        }),
      ],
      splitChunks: {
        chunks: 'all',
        minSize: 30000,
        maxSize: 200000,
        maxInitialRequests: 30,
        maxAsyncRequests: 30,
        cacheGroups: {
          react: {
            test: /[\\/]node_modules[\\/](react|react-dom|react-router-dom)[\\/]/,
            name: 'react-vendor',
            priority: 40,
            reuseExistingChunk: true,
            enforce: true,
          },
          mui: {
            test: /[\\/]node_modules[\\/](@mui|@emotion|@material-ui)[\\/]/,
            name: 'mui-vendor',
            priority: 30,
            reuseExistingChunk: true,
            enforce: true,
          },
          charts: {
            test: /[\\/]node_modules[\\/](recharts|framer-motion|d3)[\\/]/,
            name: 'charts-vendor',
            priority: 20,
            reuseExistingChunk: true,
            enforce: true,
          },
          trading: {
            test: /[\\/]src[\\/]components[\\/](Trading|Position|Trade|Bot|Portfolio)/,
            name: 'trading-components',
            priority: 10,
            reuseExistingChunk: true,
            minChunks: 1,
          },
          dashboard: {
            test: /[\\/]src[\\/]components[\\/](Dashboard|Autonomous|Ultimate)/,
            name: 'dashboard-components',
            priority: 10,
            reuseExistingChunk: true,
            minChunks: 1,
          },
          utils: {
            test: /[\\/]node_modules[\\/](lodash|moment|date-fns|uuid|decimal\.js)[\\/]/,
            name: 'utils-vendor',
            priority: 5,
            reuseExistingChunk: true,
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendor',
            priority: -10,
            reuseExistingChunk: true,
            minChunks: 1,
          },
          common: {
            name: 'common',
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true,
            chunks: 'all',
          },
        },
      },
      runtimeChunk: {
        name: 'runtime',
      },
      moduleIds: 'deterministic',
      chunkIds: 'deterministic',
      sideEffects: true,
      usedExports: true,
      concatenateModules: true,
      removeEmptyChunks: true,
      mergeDuplicateChunks: true,
    },
    resolve: {
      extensions: ['.js', '.jsx', '.json'],
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@components': path.resolve(__dirname, 'src/components'),
        '@utils': path.resolve(__dirname, 'src/utils'),
        '@styles': path.resolve(__dirname, 'src/styles'),
        '@hooks': path.resolve(__dirname, 'src/hooks'),
        '@services': path.resolve(__dirname, 'src/services'),
        '@constants': path.resolve(__dirname, 'src/constants'),
        '@api': path.resolve(__dirname, 'src/api'),
        '@auth': path.resolve(__dirname, 'src/auth'),
      },
      fallback: {
        'fs': false,
        'path': require.resolve('path-browserify'),
        'crypto': require.resolve('crypto-browserify'),
        'stream': require.resolve('stream-browserify'),
        'buffer': require.resolve('buffer/'),
        'util': require.resolve('util/'),
        'assert': require.resolve('assert/'),
        'process': require.resolve('process/browser'),
        'os': require.resolve('os-browserify/browser'),
        'url': require.resolve('url/'),
        'http': false,
        'https': false,
        'net': false,
        'tls': false,
        'child_process': false,
      },
      modules: ['node_modules'],
      symlinks: false,
      cacheWithContext: false,
    },
    performance: {
      hints: 'warning',
      maxEntrypointSize: 512000,
      maxAssetSize: 512000,
      assetFilter: (assetFilename) => {
        return !assetFilename.endsWith('.map') &&
                    !assetFilename.endsWith('.gz') &&
                    !assetFilename.endsWith('.br');
      },
    },
    stats: {
      colors: true,
      hash: false,
      version: false,
      timings: true,
      assets: true,
      chunks: false,
      modules: false,
      reasons: false,
      children: false,
      source: false,
      errors: true,
      errorDetails: true,
      warnings: true,
      publicPath: false,
      builtAt: true,
      entrypoints: false,
      chunkGroups: false,
    },
    cache: {
      type: 'filesystem',
      buildDependencies: {
        config: [__filename],
      },
      cacheDirectory: path.resolve(__dirname, 'node_modules/.cache/webpack'),
      compression: 'gzip',
    },
    experiments: {
      topLevelAwait: true,
      outputModule: false,
    },
  };
};
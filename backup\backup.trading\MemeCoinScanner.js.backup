const EventEmitter = require('events');
const { v4: uuidv4 } = require('uuid');

class MemeCoinScanner extends EventEmitter {
    /**
     * Creates a new instance of the MemeCoinScanner.
     * @param {ExchangeManager} exchangeManager - The exchange manager for interacting with exchanges.
     * @param {DatabaseHelper} databaseHelper - The database helper for storing and retrieving data.
     * @param {LLMCoordinator} llmCoordinator - The LLM coordinator for social sentiment analysis.
     * @param {Logger} logger - The logger for logging events. Optional, defaults to console.
     */
    constructor(exchangeManager, databaseHelper, llmCoordinator, logger) {
        super();
        this.exchangeManager = exchangeManager;
        this.db = databaseHelper;
        this.llm = llmCoordinator;
        this.logger = logger || console;

        // Scanner configuration
        this.config = {
            scanInterval: 300000, // 5 minutes
            exchanges: ['binance', 'bybit', 'okx', 'gateio', 'mexc'],
            minVolume24h: 100000, // $100k minimum daily volume
            minLiquidity: 50000, // $50k minimum liquidity
            maxMarketCap: 10000000, // $10M max market cap for meme coins
            minPriceChange24h: 10, // 10% minimum price change
            maxAge: 90, // Maximum 90 days old
            scoring: {
                rsi: {
                    oversold: 30,
                    neutral: 40,
                    overbought: 70,
                    oversoldScore: 0.3,
                    neutralScore: 0.2,
                    overboughtPenalty: -0.1
                },
                volumeTrend: {
                    high: 2,
                    medium: 1.5,
                    low: 0.5,
                    highScore: 0.3,
                    mediumScore: 0.2,
                    lowPenalty: -0.2
                },
                momentum: {
                    high: 20,
                    medium: 10,
                    low: -20,
                    highScore: 0.2,
                    mediumScore: 0.1,
                    lowPenalty: -0.2
                },
                spread: {
                    high: 2,
                    medium: 1,
                    highPenalty: -0.2,
                    mediumPenalty: -0.1
                },
                volume24h: {
                    tier1: 10000000,
                    tier2: 5000000,
                    tier3: 1000000,
                    tier4: 500000,
                    tier5: 100000,
                    tier1Score: 0.5,
                    tier2Score: 0.4,
                    tier3Score: 0.3,
                    tier4Score: 0.2,
                    tier5Score: 0.1
                },
                liquidity: {
                    tier1: 1000000,
                    tier2: 500000,
                    tier3: 100000,
                    tier4: 50000,
                    tier1Score: 0.5,
                    tier2Score: 0.4,
                    tier3Score: 0.3,
                    tier4Score: 0.2
                },
                priceChange24h: {
                    tier1: 50,
                    tier2: 30,
                    tier3: 20,
                    tier4: 10,
                    tier1Score: 0.4,
                    tier2Score: 0.3,
                    tier3Score: 0.2,
                    tier4Score: 0.1
                },
                volatility: {
                    high: 0.05,
                    medium: 0.03,
                    highBonus: 0.2,
                    mediumBonus: 0.1
                },
                risk: {
                    liquidity: {
                        high: 100000,
                        medium: 250000,
                        low: 500000,
                        highPenalty: 0.3,
                        mediumPenalty: 0.2,
                        lowPenalty: 0.1
                    },
                    volatility: {
                        high: 0.1,
                        medium: 0.07,
                        highPenalty: 0.2,
                        mediumPenalty: 0.1
                    },
                    spread: {
                        high: 2,
                        medium: 1,
                        highPenalty: 0.2,
                        mediumPenalty: 0.1
                    },
                    unknownMarketCapPenalty: 0.2
                }
            },
            weights: {
                opportunity: {
                    technical: 0.3,
                    volume: 0.3,
                    momentum: 0.25,
                    risk: -0.15 // Negative weight for risk
                },
                final: {
                    opportunity: 0.7,
                    ai: 0.3
                },
                // Placeholders for future use
                social: 0.4,
                whale: 0.3
            }
        };

        this.trendingCoins = [];
        this.scannerActive = false;
        this.scanTimeout = null;
    }

    /**
     * Initializes the MemeCoinScanner by loading saved trending meme coin opportunities from the database.
     * This method fetches all active opportunities from the 'meme_coin_opportunities' table, ordered by
     * opportunity score, and populates the `trendingCoins` array. Logs the number of active opportunities
     * loaded upon successful initialization.
     *
     * @returns {Promise<boolean>} Resolves to true if initialization is successful.
     * @throws Will throw an error if the initialization process fails.
     */

    async initialize() {
        try {
            // Load saved trending coins from database
            const savedTrending = await this.db.all(
                `SELECT * FROM meme_coin_opportunities
                WHERE status = 'active'
                ORDER BY opportunity_score DESC`
            );

            this.trendingCoins = savedTrending || [];
            this.logger.info(`MemeCoinScanner initialized with ${this.trendingCoins.length} active opportunities`);

            return true;
        } catch (error) {
            this.logger.error('Failed to initialize MemeCoinScanner:', error);
            throw error;
        }
    }

    /**
     * Starts the meme coin scanner, which will periodically scan for new opportunities and send a 'scannerStarted' event.
     * If the scanner is already active, this method will do nothing.
     *
     * @returns {Promise<void>} Resolves when the scanner has been started.
     */
    async startScanning() {
        if (this.scannerActive) {
            this.logger.warn('Scanner already active');
            return;
        }

        this.scannerActive = true;

        // Use a setTimeout chain for safer async intervals
        const runScan = async () => {
            if (!this.scannerActive) return;

            await this.performScan();

            if (this.scannerActive) {
                this.scanTimeout = setTimeout(runScan, this.config.scanInterval);
            }
        };

        // Initial scan
        runScan();

        this.emit('scannerStarted');
        this.logger.info('Meme coin scanner started');
    }

    /**
     * Performs a scan for meme coin opportunities across multiple exchanges.
     * This method retrieves exchange data, analyzes potential opportunities, ranks them,
     * and updates the list of trending coins. It emits a 'scanComplete' event upon success
     * or a 'scanError' event if an error occurs.
     *
     * @returns {Promise<Array>} Resolves to an array of ranked meme coin opportunities.
     * @throws Will throw an error if the scan process fails.
     */

    async performScan() {
        try {
            this.logger.info('Starting meme coin scan across exchanges');
            const startTime = Date.now();

            // Scan exchanges in parallel for performance
            const scanPromises = this.config.exchanges.map(exchangeName => {
                const exchange = this.exchangeManager.getExchange(exchangeName);
                if (!exchange) {
                    this.logger.warn(`Exchange ${exchangeName} not found or initialized.`);
                    return Promise.resolve([]);
                }
                return this.scanExchange(exchange, exchangeName).catch(error => {
                    this.logger.error(`Failed to scan ${exchangeName}:`, error);
                    return []; // Return empty array on failure to not break Promise.all
                });
            });

            const opportunityChunks = await Promise.all(scanPromises);
            const opportunities = opportunityChunks.flat();

            // Analyze and rank opportunities
            const rankedOpportunities = await this.analyzeOpportunities(opportunities);

            // Update trending coins
            this.trendingCoins = rankedOpportunities.slice(0, 20); // Top 20

            // Save to database
            await this.saveOpportunities(rankedOpportunities);

            const scanTime = Date.now() - startTime;
            this.logger.info(`Scan completed in ${scanTime}ms. Found ${rankedOpportunities.length} opportunities`);

            this.emit('scanComplete', {
                opportunityCount: rankedOpportunities.length,
                scanTime,
                topOpportunities: this.trendingCoins.slice(0, 5)
            });

            return rankedOpportunities;

        } catch (error) {
            this.logger.error('Scan failed:', error);
            this.emit('scanError', error);
            throw error;
        }
    }

    /**
     * Scans a single exchange for potential meme coins.
     * @param {Exchange} exchange - CCXT exchange object
     * @param {string} exchangeName - Name of the exchange
     * @returns {Promise<Opportunity[]>} Resolves with an array of Opportunities
     * @throws If the scan fails
     */
    async scanExchange(exchange, exchangeName) {
        try {
            // Load markets
            const markets = await exchange.loadMarkets();
            const opportunities = [];

            // Filter for potential meme coins (usually paired with USDT)
            const memeMarkets = Object.values(markets).filter(market => {
                return (
                    market.active &&
                    market.quote === 'USDT' &&
                    !this.isStablecoin(market.base) &&
                    !this.isMajorCoin(market.base)
                );
            });

            // Analyze each market
            const batchSize = 10;
            for (let i = 0; i < memeMarkets.length; i += batchSize) {
                const batch = memeMarkets.slice(i, i + batchSize);
                const batchPromises = batch.map(market =>
                    this.analyzeMarket(exchange, market, exchangeName)
                );

                const results = await Promise.allSettled(batchPromises);

                for (const result of results) {
                    if (result.status === 'fulfilled' && result.value) {
                        opportunities.push(result.value);
                    }
                }
            }

            return opportunities;

        } catch (error) {
            this.logger.error(`Exchange scan failed for ${exchangeName}:`, error);
            return [];
        }
    }

    /**
     * Analyzes a specific market on a given exchange to identify potential meme coin opportunities.
     * This method fetches the market data and applies various filters and calculations to determine
     * if the market meets the criteria for a viable trading opportunity.
     *
     * @param {Exchange} exchange - The exchange instance to interact with for fetching market data.
     * @param {Object} market - An object representing the market to be analyzed, including metadata.
     * @param {string} exchangeName - The name of the exchange where the market resides.
     * @returns {Promise<Object|null>} A promise that resolves to an opportunity object if the
     * market is deemed suitable, or null if it does not meet the criteria.
     * @throws Will not throw an error as it logs and returns null in case of failures.
     */

    async analyzeMarket(exchange, market, exchangeName) {
        try {
            const symbol = market.symbol;

            // Fetch market data
            const [ticker, orderBook, trades] = await Promise.all([
                exchange.fetchTicker(symbol),
                exchange.fetchOrderBook(symbol, 20),
                exchange.fetchTrades(symbol, undefined, 50)
            ]);

            // Basic filters
            if (ticker.quoteVolume < this.config.minVolume24h) return null;
            if (Math.abs(ticker.percentage) < this.config.minPriceChange24h) return null;

            // Calculate liquidity
            const liquidity = this.calculateLiquidity(orderBook);
            if (liquidity < this.config.minLiquidity) return null;

            // Calculate technical indicators
            const technical = this.calculateTechnicalIndicators(trades, ticker);

            // Estimate market cap (if possible)
            const marketCap = this.estimateMarketCap(market, ticker);
            if (marketCap && marketCap > this.config.maxMarketCap) return null;

            // Create opportunity object
            const opportunity = {
                id: uuidv4(),
                symbol: symbol,
                baseAsset: market.base,
                exchange: exchangeName,
                currentPrice: ticker.last,
                priceChange24h: ticker.percentage,
                volume24h: ticker.quoteVolume,
                liquidity: liquidity,
                marketCap: marketCap,
                technical: technical,
                timestamp: new Date().toISOString(),
                scores: {
                    technical: 0,
                    volume: 0,
                    momentum: 0,
                    risk: 0
                }
            };

            return opportunity;

        } catch (error) {
            // Log error and skip failed markets for robustness
            this.logger.debug(`Market analysis failed for ${market?.symbol || 'unknown'}:`, error.message);
            return null;
        }
    }

    /**
     * Analyzes a list of meme coin opportunities by scoring them based on various metrics
     * and incorporates AI insights for enhanced evaluation.
     *
     * The function scores each opportunity using technical indicators, volume, momentum,
     * and risk factors. It then calculates an overall opportunity score. The top opportunities
     * are further analyzed using AI to refine their scores, and the results are returned in
     * descending order of their final scores.
     *
     * @param {Array<Object>} opportunities - List of meme coin opportunities to be analyzed.
     * @returns {Promise<Array<Object>>} - Sorted list of opportunities with updated scores and AI insights.
     */

    async analyzeOpportunities(opportunities) {
        try {
            // Score each opportunity
            for (const opp of opportunities) {
                // Technical score
                opp.scores.technical = this.calculateTechnicalScore(opp.technical);

                // Volume score
                opp.scores.volume = this.calculateVolumeScore(opp.volume24h, opp.liquidity);

                // Momentum score
                opp.scores.momentum = this.calculateMomentumScore(opp.priceChange24h, opp.technical);

                // Risk score
                opp.scores.risk = this.calculateRiskScore(opp);

                // Overall opportunity score
                opp.opportunityScore = this.calculateOpportunityScore(opp.scores);
            }

            // Get AI analysis for top opportunities
            const topOpps = opportunities
                .slice() // Create a shallow copy to avoid modifying the original array
                .sort((a, b) => b.opportunityScore - a.opportunityScore)
                .slice(0, 10);

            const aiAnalysisPromises = topOpps.map(async (opp) => {
                try {
                    const aiAnalysis = await this.getAIAnalysis(opp);
                    opp.aiInsights = aiAnalysis;
                    opp.aiScore = aiAnalysis.score || 0;

                    // Recalculate final score with AI input
                    const weights = this.config.weights.final;
                    opp.finalScore = (opp.opportunityScore * weights.opportunity) + (opp.aiScore * weights.ai);
                } catch (error) {
                    this.logger.error(`AI analysis failed for ${opp.symbol}:`, error);
                    opp.finalScore = opp.opportunityScore;
                }
            });

            await Promise.all(aiAnalysisPromises);

            // Final ranking
            return opportunities
                .filter(opp => opp.opportunityScore > 0.5)
                .sort((a, b) => (b.finalScore || b.opportunityScore) - (a.finalScore || a.opportunityScore));

        } catch (error) {
            this.logger.error('Failed to analyze opportunities:', error);
            return opportunities;
        }
    }

    /**
     * Analyzes a cryptocurrency trading opportunity using AI to evaluate its potential.
     *
     * @param {Object} opportunity - The opportunity object containing details of the cryptocurrency.
     * @param {string} opportunity.symbol - The trading symbol of the cryptocurrency.
     * @param {string} opportunity.exchange - The exchange where the cryptocurrency is listed.
     * @param {number} opportunity.currentPrice - The current trading price of the cryptocurrency.
     * @param {number} opportunity.priceChange24h - The percentage price change in the last 24 hours.
     * @param {number} opportunity.volume24h - The trading volume in the last 24 hours.
     * @param {number} opportunity.liquidity - The available liquidity of the cryptocurrency.
     * @param {number} [opportunity.marketCap] - The market capitalization of the cryptocurrency.
     * @param {Object} opportunity.technical - Technical indicators related to the cryptocurrency.
     * @param {number} opportunity.technical.rsi - The Relative Strength Index of the cryptocurrency.
     * @param {number} opportunity.technical.volumeTrend - The trend of the trading volume.
     * @param {number} opportunity.technical.momentum - The price momentum of the cryptocurrency.
     *
     * @returns {Promise<Object>} An object containing AI-generated insights about the opportunity.
     * @returns {boolean} return.isMemeOpportunity - Whether the opportunity is likely a meme coin.
     * @returns {number} return.riskLevel - The assessed risk level (1-10).
     * @returns {number} return.score - The AI-generated opportunity score (0-1).
     * @returns {string} return.recommendedStrategy - The recommended grid bot strategy.
     * @returns {string} return.insights - Key insights provided by the AI analysis.
     * @returns {string} [return.consensus] - Consensus information from the AI, if available.
     * @returns {number} [return.confidence] - Confidence level of the AI decision, if available.
     */

    async getAIAnalysis(opportunity) {
        try {
            const prompt = `Analyze this cryptocurrency trading opportunity and respond with a JSON object.
                Opportunity Data:
                {
                    "symbol": "${opportunity.symbol}",
                    "exchange": "${opportunity.exchange}",
                    "currentPrice": ${opportunity.currentPrice},
                    "priceChange24h": ${opportunity.priceChange24h},
                    "volume24h": ${opportunity.volume24h},
                    "liquidity": ${opportunity.liquidity},
                    "marketCap": ${opportunity.marketCap || null},
                    "technical": {
                        "rsi": ${opportunity.technical.rsi},
                        "volumeTrend": ${opportunity.technical.volumeTrend},
                        "momentum": ${opportunity.technical.momentum}
                    }
                }

                Provide a JSON object with the following structure:
                {
                  "isMemeOpportunity": boolean,
                  "riskLevel": number (1-10),
                  "opportunityScore": number (0-1),
                  "recommendedStrategy": "conservative" | "moderate" | "aggressive",
                  "insights": "string (2-3 sentences)"
                }`;

            const response = await this.llm.makeDecision(prompt, 'trading');

            // Parse AI response, assuming it returns a JSON string
            const insights = this.parseAIResponse(response);

            return {
                isMemeOpportunity: insights.isMemeOpportunity || false,
                riskLevel: insights.riskLevel || 5,
                score: insights.opportunityScore || 0.5,
                recommendedStrategy: insights.recommendedStrategy || 'moderate',
                insights: insights.insights || 'AI analysis unavailable',
                consensus: response.consensus,
                confidence: response.confidence
            };

        } catch (error) {
            this.logger.error('AI analysis failed:', error);
            return {
                isMemeOpportunity: false,
                riskLevel: 8,
                score: 0.3,
                recommendedStrategy: 'conservative',
                insights: 'AI analysis failed - proceed with caution'
            };
        }
    }

    calculateTechnicalIndicators(trades, ticker) {
        try {
            const prices = trades.map(t => t.price);
            const volumes = trades.map(t => t.amount);

            // Calculate RSI
            const rsi = this.calculateRSI(prices);

            // Volume analysis
            const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length;
            const recentVolume = volumes.slice(0, 10).reduce((a, b) => a + b, 0) / 10;
            const volumeTrend = recentVolume / avgVolume;

            // Price momentum
            const momentum = this.calculateMomentum(prices);

            // Volatility
            const volatility = this.calculateVolatility(prices);

            return {
                rsi,
                volumeTrend,
                momentum,
                volatility,
                spread: ticker.ask - ticker.bid,
                spreadPercent: ((ticker.ask - ticker.bid) / ticker.last) * 100
            };

        } catch (error) {
            this.logger.debug('Technical indicators calculation failed:', error.message);
            return {
                rsi: 50,
                volumeTrend: 1,
                momentum: 0,
                volatility: 0,
                spread: 0,
                spreadPercent: 0
            };
        }
    }

    /**
     * Calculates the Relative Strength Index (RSI) for a given array of prices.
     * @param {number[]} prices - Array of prices.
     * @param {number} [period=14] - The time period to use for calculating the RSI.
     * @returns {number} The RSI value, ranging from 0 to 100.
     */
    calculateRSI(prices, period = 14) {
        if (prices.length < period + 1) return 50;

        let gains = 0;
        let losses = 0;

        for (let i = 1; i <= period; i++) {
            const change = prices[i] - prices[i - 1];
            if (change > 0) gains += change;
            else losses -= change;
        }

        const avgGain = gains / period;
        const avgLoss = losses / period;

        if (avgLoss === 0) return 100;

        const rs = avgGain / avgLoss;
        return 100 - (100 / (1 + rs));
    }

    /**
     * Calculates the momentum of the given array of prices.
     * @param {number[]} prices - Array of prices.
     * @returns {number} The momentum value, ranging from -100 to 100.
     */
    calculateMomentum(prices) {
        if (prices.length < 2) return 0;

        const recentPrice = prices[0];
        const oldPrice = prices[Math.min(prices.length - 1, 20)];

        return ((recentPrice - oldPrice) / oldPrice) * 100;
    }

    /**
     * Calculates the volatility of a series of prices.
     * @param {number[]} prices - Array of prices.
     * @returns {number} The calculated volatility as a standard deviation of returns.
     */

    calculateVolatility(prices) {
        if (prices.length < 2) return 0;

        const returns = [];
        for (let i = 1; i < prices.length; i++) {
            returns.push((prices[i] - prices[i - 1]) / prices[i - 1]);
        }

        const mean = returns.reduce((a, b) => a + b, 0) / returns.length;
        const variance = returns.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / returns.length;

        return Math.sqrt(variance);
    }

    /**
     * Calculates the liquidity of an order book.
     * @param {Object} orderBook - Object containing the order book data.
     * @param {Array<Array<number>>} orderBook.bids - Array of bid orders, where each order is an array of [price, amount].
     * @param {Array<Array<number>>} orderBook.asks - Array of ask orders, where each order is an array of [price, amount].
     * @returns {number} The calculated liquidity as the average of bid and ask liquidity.
     */
    calculateLiquidity(orderBook) {
        const depth = 0.02; // 2% depth
        const midPrice = (orderBook.asks[0][0] + orderBook.bids[0][0]) / 2;

        let bidLiquidity = 0;
        let askLiquidity = 0;

        // Calculate bid liquidity
        for (const [price, amount] of orderBook.bids) {
            if (price >= midPrice * (1 - depth)) {
                bidLiquidity += price * amount;
            }
        }

        // Calculate ask liquidity
        for (const [price, amount] of orderBook.asks) {
            if (price <= midPrice * (1 + depth)) {
                askLiquidity += price * amount;
            }
        }

        return (bidLiquidity + askLiquidity) / 2;
    }

    /**
     * Estimates the market capitalization of a given market.
     * @param {Object} _market - The market object, which is not used in this implementation.
     * @param {Object} _ticker - The ticker object, which is not used in this implementation.
     * @returns {number|null} The estimated market capitalization, or null if unknown.
     */
    estimateMarketCap(_market, _ticker) {
        // TODO: This is a critical simplification.
        // In a real-world application, this should be implemented by fetching
        // circulating supply from an external API (e.g., CoinGecko, CoinMarketCap)
        // and multiplying it by the current price.
        return null;
    }

    /**
     * Calculates the technical score for a given set of technical indicators.
     * The score is determined based on the RSI, volume trend, momentum, and spread percentage.
     * Each indicator contributes positively or negatively to the overall score, which is bounded between 0 and 1.
     *
     * @param {Object} technical - Object containing technical indicators.
     * @param {number} technical.rsi - Relative Strength Index value.
     * @param {number} technical.volumeTrend - Trend of the trading volume.
     * @param {number} technical.momentum - Price momentum value.
     * @param {number} technical.spreadPercent - Percentage difference between ask and bid prices.
     * @returns {number} The calculated technical score ranging from 0 to 1.
     */

    calculateTechnicalScore(technical) {
        let score = 0;
        const thresholds = this.config.scoring;

        // RSI score (oversold = good opportunity)
        if (technical.rsi < thresholds.rsi.oversold) score += thresholds.rsi.oversoldScore;
        else if (technical.rsi < thresholds.rsi.neutral) score += thresholds.rsi.neutralScore;
        else if (technical.rsi > thresholds.rsi.overbought) score += thresholds.rsi.overboughtPenalty;

        // Volume trend score
        if (technical.volumeTrend > thresholds.volumeTrend.high) score += thresholds.volumeTrend.highScore;
        else if (technical.volumeTrend > thresholds.volumeTrend.medium) score += thresholds.volumeTrend.mediumScore;
        else if (technical.volumeTrend < thresholds.volumeTrend.low) score += thresholds.volumeTrend.lowPenalty;

        // Momentum score
        if (technical.momentum > thresholds.momentum.high) score += thresholds.momentum.highScore;
        else if (technical.momentum > thresholds.momentum.medium) score += thresholds.momentum.mediumScore;
        else if (technical.momentum < thresholds.momentum.low) score += thresholds.momentum.lowPenalty;

        // Spread penalty
        if (technical.spreadPercent > thresholds.spread.high) score += thresholds.spread.highPenalty;
        else if (technical.spreadPercent > thresholds.spread.medium) score += thresholds.spread.mediumPenalty;

        return Math.max(0, Math.min(1, score));
    }

    /**
     * Calculates the volume score based on the 24-hour trading volume and liquidity.
     * The score is a weighted sum of the volume and liquidity, with higher values indicating more favorable trading conditions.
     * The score is bounded between 0 and 1.
     * @param {number} volume24h - 24-hour trading volume.
     * @param {number} liquidity - Liquidity of the order book.
     * @returns {number} The calculated volume score ranging from 0 to 1.
     */
    calculateVolumeScore(volume24h, liquidity) {
        let score = 0;
        const thresholds = this.config.scoring;

        // Volume score
        if (volume24h > thresholds.volume24h.tier1) score += thresholds.volume24h.tier1Score;
        else if (volume24h > thresholds.volume24h.tier2) score += thresholds.volume24h.tier2Score;
        else if (volume24h > thresholds.volume24h.tier3) score += thresholds.volume24h.tier3Score;
        else if (volume24h > thresholds.volume24h.tier4) score += thresholds.volume24h.tier4Score;
        else if (volume24h > thresholds.volume24h.tier5) score += thresholds.volume24h.tier5Score;

        // Liquidity score
        if (liquidity > thresholds.liquidity.tier1) score += thresholds.liquidity.tier1Score;
        else if (liquidity > thresholds.liquidity.tier2) score += thresholds.liquidity.tier2Score;
        else if (liquidity > thresholds.liquidity.tier3) score += thresholds.liquidity.tier3Score;
        else if (liquidity > thresholds.liquidity.tier4) score += thresholds.liquidity.tier4Score;

        return Math.min(1, score);
    }

    /**
     * Calculates the momentum score based on the 24-hour price change and technical analysis metrics.
     * The score is a weighted sum of the price change, direction, and volatility, with higher values indicating more favorable trading conditions.
     * The score is bounded between 0 and 1.
     * @param {number} priceChange24h - 24-hour price change percentage.
     * @param {object} technical - Technical analysis metrics.
     * @returns {number} The calculated momentum score ranging from 0 to 1.
     */
    calculateMomentumScore(priceChange24h, technical) {
        let score = 0;
        const thresholds = this.config.scoring;

        // Price change score
        if (Math.abs(priceChange24h) > thresholds.priceChange24h.tier1) score += thresholds.priceChange24h.tier1Score;
        else if (Math.abs(priceChange24h) > thresholds.priceChange24h.tier2) score += thresholds.priceChange24h.tier2Score;
        else if (Math.abs(priceChange24h) > thresholds.priceChange24h.tier3) score += thresholds.priceChange24h.tier3Score;
        else if (Math.abs(priceChange24h) > thresholds.priceChange24h.tier4) score += thresholds.priceChange24h.tier4Score;

        // Direction bonus
        if (priceChange24h > 0 && technical.momentum > 0) score += 0.2;

        // Volatility bonus (for grid trading)
        if (technical.volatility > thresholds.volatility.high) score += thresholds.volatility.highBonus;
        else if (technical.volatility > thresholds.volatility.medium) score += thresholds.volatility.mediumBonus;

        return Math.min(1, score);
    }

    calculateRiskScore(opportunity) {
        let riskScore = 0;
        const thresholds = this.config.scoring.risk;

        // Liquidity risk
        if (opportunity.liquidity < thresholds.liquidity.high) riskScore += thresholds.liquidity.highPenalty;
        else if (opportunity.liquidity < thresholds.liquidity.medium) riskScore += thresholds.liquidity.mediumPenalty;
        else if (opportunity.liquidity < thresholds.liquidity.low) riskScore += thresholds.liquidity.lowPenalty;

        // Volatility risk
        if (opportunity.technical.volatility > thresholds.volatility.high) riskScore += thresholds.volatility.highPenalty;
        else if (opportunity.technical.volatility > thresholds.volatility.medium) riskScore += thresholds.volatility.mediumPenalty;

        // Spread risk
        if (opportunity.technical.spreadPercent > thresholds.spread.high) riskScore += thresholds.spread.highPenalty;
        else if (opportunity.technical.spreadPercent > thresholds.spread.medium) riskScore += thresholds.spread.mediumPenalty;

        // Unknown market cap risk
        if (!opportunity.marketCap) riskScore += thresholds.risk.unknownMarketCapPenalty;

        return Math.min(1, riskScore);
    }

    /**
 * Calculate the overall opportunity score based on various individual scores.
 *
 * This function takes into account the technical, volume, momentum, and risk scores
 * of an opportunity, applying specific weights to each of these components to compute
 * a weighted score. The risk component has a negative weight, reflecting the adverse
 * effect of risk on the opportunity's attractiveness. The final score is normalized
 * between 0 and 1.
 *
 * @param {Object} scores - An object containing individual scores.
 * @param {number} scores.technical - Technical analysis score.
 * @param {number} scores.volume - Volume score.
 * @param {number} scores.momentum - Momentum score.
 * @param {number} scores.risk - Risk score (note: negatively weighted).
 * @returns {number} - A normalized score representing the overall opportunity.
 */

    calculateOpportunityScore(scores) {
        const weights = this.config.weights.opportunity;

        const weightedScore =
            (scores.technical * weights.technical) +
            (scores.volume * weights.volume) +
            (scores.momentum * weights.momentum) +
            (scores.risk * weights.risk);

        return Math.max(0, Math.min(1, weightedScore));
    }

    async saveOpportunities(opportunities) {
        try {
            // Clear old opportunities
            await this.db.run(
                `UPDATE meme_coin_opportunities
                SET status = 'expired'
                WHERE created_at < datetime('now', '-24 hours')`
            );

            // Save new opportunities
            for (const opp of opportunities.slice(0, 50)) { // Save top 50
                const query = `
                    INSERT OR REPLACE INTO meme_coin_opportunities (
                        id, symbol, base_asset, exchange, current_price,
                        price_change_24h, volume_24h, liquidity, market_cap,
                        opportunity_score, ai_score, final_score, technical_indicators,
                        ai_insights, recommended_strategy, status, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `;

                await this.db.run(query, [
                    opp.id,
                    opp.symbol,
                    opp.baseAsset,
                    opp.exchange,
                    opp.currentPrice,
                    opp.priceChange24h,
                    opp.volume24h,
                    opp.liquidity,
                    opp.marketCap,
                    opp.opportunityScore,
                    opp.aiScore || null,
                    opp.finalScore || opp.opportunityScore,
                    JSON.stringify(opp.technical),
                    JSON.stringify(opp.aiInsights || {}),
                    opp.aiInsights?.recommendedStrategy || 'moderate',
                    'active',
                    opp.timestamp
                ]);
            }

        } catch (error) {
            this.logger.error('Failed to save opportunities:', error);
        }
    }

    /**
     * Get the top meme coin opportunities with the highest scores.
     * @param {number} [limit=10] - The maximum number of opportunities to return.
     * @returns {Promise<object[]>} - An array of opportunity objects, each with the following properties:
     *   - id: The unique id of the opportunity.
     *   - symbol: The symbol of the meme coin.
     *   - baseAsset: The base asset of the meme coin.
     *   - exchange: The exchange where the meme coin is listed.
     *   - currentPrice: The current price of the meme coin.
     *   - priceChange24h: The price change of the meme coin over the past 24 hours.
     *   - volume24h: The 24 hour trading volume of the meme coin.
     *   - liquidity: The liquidity score of the meme coin.
     *   - marketCap: The market capitalization of the meme coin.
     *   - opportunityScore: The score of the opportunity, calculated by the meme coin scanner.
     *   - aiScore: The AI-generated score of the opportunity, if available.
     *   - finalScore: The final score of the opportunity, which is the opportunity score adjusted by the AI score.
     *   - technical_indicators: An object containing the technical indicators used to calculate the opportunity score.
     *   - ai_insights: An object containing the AI-generated insights for the opportunity.
     */
    async getTopOpportunities(limit = 10) {
        try {
            const opportunities = await this.db.all(
                `SELECT * FROM meme_coin_opportunities
                WHERE status = 'active'
                ORDER BY final_score DESC
                LIMIT ?`,
                [limit]
            );

            return opportunities.map(opp => ({
                ...opp,
                technical_indicators: JSON.parse(opp.technical_indicators || '{}'),
                ai_insights: JSON.parse(opp.ai_insights || '{}')
            }));

        } catch (error) {
            this.logger.error('Failed to get top opportunities:', error);
            return [];
        }
    }

    /*************  ✨ Windsurf Command ⭐  *************/
    /**
 * Retrieves the most recent active meme coin opportunity for a given symbol and exchange.
 *
 * This function queries the database to find the latest active opportunity for a specified
 * meme coin symbol and exchange. The opportunity includes various details such as technical
 * indicators and AI-generated insights, which are returned as parsed objects.
 *
 * @async
 * @param {string} symbol - The trading pair symbol of the meme coin.
 * @param {string} exchange - The name of the exchange where the meme coin is listed.
 * @returns {Promise<Object|null>} - Resolves with the opportunity object if found, otherwise null.
 *
 * @throws - Logs an error and returns null if the database query fails.
 */

    /*******  1d53b32a-b18c-421d-80eb-c0d73e292cf9  *******/
    async getOpportunityBySymbol(symbol, exchange) {
        try {
            const opportunity = await this.db.get(
                `SELECT * FROM meme_coin_opportunities
                WHERE symbol = ? AND exchange = ? AND status = 'active'
                ORDER BY created_at DESC
                LIMIT 1`,
                [symbol, exchange]
            );

            if (opportunity) {
                return {
                    ...opportunity,
                    technical_indicators: JSON.parse(opportunity.technical_indicators || '{}'),
                    ai_insights: JSON.parse(opportunity.ai_insights || '{}')
                };
            }

            return null;

        } catch (error) {
            this.logger.error('Failed to get opportunity:', error);
            return null;
        }
    }

    /**
     * Generates a set of recommended grid trading parameters for a given opportunity.
     *
     * This function takes an opportunity object as input and returns a set of recommended
     * grid trading parameters. The parameters are calculated based on the opportunity's
     * technical indicators and the AI-generated insights. The recommended strategy is
     * determined by the opportunity's risk level and the user's preference.
     *
     * @param {object} opportunity - The opportunity object with the following properties:
     *   - symbol: The trading pair symbol of the meme coin.
     *   - baseAsset: The base asset of the meme coin.
     *   - exchange: The name of the exchange where the meme coin is listed.
     *   - priceChange24h: The price change of the meme coin over the past 24 hours.
     *   - volume24h: The 24 hour trading volume of the meme coin.
     *   - liquidity: The liquidity score of the meme coin.
     *   - marketCap: The market capitalization of the meme coin.
     *   - technical_indicators: An object containing the technical indicators used to calculate the opportunity score.
     *   - ai_insights: An object containing the AI-generated insights for the opportunity.
     * @returns {object} - A set of recommended grid trading parameters with the following properties:
     *   - symbol: The trading pair symbol of the meme coin.
     *   - exchange: The name of the exchange where the meme coin is listed.
     *   - totalInvestment: The total investment amount for the grid bot, which should be adjusted by the user.
     *   - strategy: The recommended grid trading strategy, which is either 'conservative', 'moderate', or 'aggressive'.
     *   - upperPrice: The upper price boundary for the grid bot.
     *   - lowerPrice: The lower price boundary for the grid bot.
     *   - gridQuantity: The number of grid levels to create.
     *   - takeProfitPercent: The take profit percentage for the grid bot.
     *   - stopLossPercent: The stop loss percentage for the grid bot.
     *   - trailingUp: A boolean indicating whether to enable trailing up for trending coins.
     *   - trailingDown: A boolean indicating whether to enable trailing down for trending coins.
     *   - aiOptimized: A boolean indicating whether the recommended parameters are optimized by AI.
     */
    async recommendGridParameters(opportunity) {
        try {
            const { technical, aiInsights } = opportunity;
            const strategy = aiInsights?.recommendedStrategy || 'moderate';

            const params = {
                symbol: opportunity.symbol,
                exchange: opportunity.exchange,
                totalInvestment: 1000, // Default, should be adjusted by user
                strategy: 'geometric' // Better for volatile meme coins
            };

            // Calculate grid range based on volatility
            const volatility = technical.volatility || 0.05;
            const currentPrice = opportunity.currentPrice;

            switch (strategy) {
            case 'conservative':
                params.upperPrice = currentPrice * (1 + volatility * 2);
                params.lowerPrice = currentPrice * (1 - volatility * 2);
                params.gridQuantity = 10;
                params.takeProfitPercent = 20;
                params.stopLossPercent = 10;
                break;

            case 'moderate':
                params.upperPrice = currentPrice * (1 + volatility * 3);
                params.lowerPrice = currentPrice * (1 - volatility * 3);
                params.gridQuantity = 15;
                params.takeProfitPercent = 30;
                params.stopLossPercent = 15;
                break;

            case 'aggressive':
                params.upperPrice = currentPrice * (1 + volatility * 4);
                params.lowerPrice = currentPrice * (1 - volatility * 4);
                params.gridQuantity = 20;
                params.takeProfitPercent = 50;
                params.stopLossPercent = 20;
                break;
            }

            // Enable trailing for trending coins
            params.trailingUp = opportunity.priceChange24h > 20;
            params.trailingDown = opportunity.priceChange24h < -20;
            params.aiOptimized = true;

            return params;

        } catch (error) {
            this.logger.error('Failed to recommend grid parameters:', error);
            throw error;
        }
    }

    /**
     * Parses the AI response object and extracts structured data.
     *
     * @param {object} response - The AI response object with a `decision` property containing the text response.
     * @returns {object} - An object with the following properties:
     *   - isMemeOpportunity: A boolean indicating whether the AI considers the coin a meme opportunity.
     *   - riskLevel: An integer between 1-10 indicating the risk level of the opportunity.
     *   - opportunityScore: A float between 0-1 indicating the opportunity score.
     *   - strategy: A string indicating the recommended strategy, either 'conservative', 'moderate', or 'aggressive'.
     *   - keyInsights: A string containing the key insights from the AI response.
     *
     * @throws - Logs an error and returns an object with default values if the parsing fails.
     */
    parseAIResponse(response) {
        try {
            // AI is instructed to return JSON, so we parse it directly.
            const decision = response.decision || '{}';
            return JSON.parse(decision);
        } catch (error) {
            this.logger.error('Failed to parse AI JSON response:', error);
            // Fallback for safety
            return {
                isMemeOpportunity: false,
                riskLevel: 8,
                opportunityScore: 0.3,
                recommendedStrategy: 'conservative',
                insights: 'AI analysis failed or returned invalid format.'
            };
        }
    }

    /**
     * Checks if a given coin is a stablecoin.
     *
     * @param {string} symbol - The symbol of the coin.
     * @returns {boolean} - True if the coin is a stablecoin, false otherwise.
     */
    isStablecoin(symbol) {
        const stablecoins = ['USDT', 'USDC', 'BUSD', 'DAI', 'TUSD', 'USDP', 'GUSD', 'FRAX'];
        return stablecoins.includes(symbol.toUpperCase());
    }

    /**
 * Determines if a given coin is considered a major cryptocurrency.
 *
 * @param {string} symbol - The symbol of the coin to check.
 * @returns {boolean} - True if the coin is a major cryptocurrency, false otherwise.
 */

    isMajorCoin(symbol) {
        const majorCoins = ['BTC', 'ETH', 'BNB', 'XRP', 'ADA', 'SOL', 'DOT', 'MATIC', 'AVAX', 'LINK'];
        return majorCoins.includes(symbol.toUpperCase());
    }

    /**
 * Stops the meme coin scanner, halting any ongoing scans and clearing the scan interval.
 * Emits a 'scannerStopped' event upon successful termination of scanning activities.
 */

    async stopScanning() {
        this.scannerActive = false;

        if (this.scanTimeout) {
            clearTimeout(this.scanTimeout);
            this.scanTimeout = null;
        }

        this.emit('scannerStopped');
        this.logger.info('Meme coin scanner stopped');
    }

    /**
 * Creates the 'meme_coin_opportunities' table if it does not exist.
 *
 * This function defines a table schema for storing meme coin opportunities
 * with various attributes including price, volume, market cap, scores, and
 * AI insights. It also creates indices to optimize querying by status, score,
 * and symbol-exchange pair.
 *
 * Table Columns:
 * - id: Unique identifier for the opportunity (TEXT PRIMARY KEY).
 * - symbol: The trading pair symbol of the meme coin (TEXT NOT NULL).
 * - base_asset: The base asset of the meme coin (TEXT NOT NULL).
 * - exchange: The exchange where the meme coin is listed (TEXT NOT NULL).
 * - current_price: The current price of the meme coin (REAL NOT NULL).
 * - price_change_24h: The price change over the past 24 hours (REAL).
 * - volume_24h: The 24-hour trading volume (REAL).
 * - liquidity: Liquidity score of the meme coin (REAL).
 * - market_cap: Market capitalization of the meme coin (REAL).
 * - opportunity_score: Score of the opportunity (REAL).
 * - ai_score: AI-generated score (REAL).
 * - final_score: Final adjusted score (REAL).
 * - technical_indicators: Technical indicators as a JSON string (TEXT).
 * - ai_insights: AI-generated insights as a JSON string (TEXT).
 * - recommended_strategy: Recommended trading strategy (TEXT).
 * - status: Status of the opportunity, defaults to 'active' (TEXT).
 * - created_at: Timestamp of creation, defaults to current timestamp (DATETIME).
 * - updated_at: Timestamp of last update, defaults to current timestamp (DATETIME).
 *
 * Indices:
 * - idx_meme_status: Index on the 'status' column.
 * - idx_meme_score: Index on the 'final_score' column.
 * - idx_meme_symbol: Index on the combination of 'symbol' and 'exchange' columns.
 */

    async createMemeTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS meme_coin_opportunities (
                id TEXT PRIMARY KEY,
                symbol TEXT NOT NULL,
                base_asset TEXT NOT NULL,
                exchange TEXT NOT NULL,
                current_price REAL NOT NULL,
                price_change_24h REAL,
                volume_24h REAL,
                liquidity REAL,
                market_cap REAL,
                opportunity_score REAL,
                ai_score REAL,
                final_score REAL,
                technical_indicators TEXT,
                ai_insights TEXT,
                recommended_strategy TEXT,
                status TEXT DEFAULT 'active',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            CREATE INDEX IF NOT EXISTS idx_meme_status ON meme_coin_opportunities(status);
            CREATE INDEX IF NOT EXISTS idx_meme_score ON meme_coin_opportunities(final_score);
            CREATE INDEX IF NOT EXISTS idx_meme_symbol ON meme_coin_opportunities(symbol, exchange);
        `;

        await this.db.exec(query);
    }
}

module.exports = MemeCoinScanner;

const EventEmitter = require('events');
const { v4: uuidv4 } = require('uuid');
const { createLogger } = require('../../shared/helpers/logger');
const CircuitBreaker = require('../../shared/safety/circuit-breakers');
const DatabaseManager = require('../database/database-manager-enhanced');

// Mock ProductionExchangeConnector
class ProductionExchangeConnector extends EventEmitter {
    /**
     * Constructs a new instance of the ProductionExchangeConnector.
     *
     * @param {undefined} undefined - This constructor does not accept any parameters.
     */
    constructor() {
        super();
        this.exchanges = new Map();
    }
    async initialize() {
        // Mock initialization, no-op
    }
    async getBalance() { return { free: { USDT: 10000 } }; }
    async getTicker() { return { last: 50000 }; }
    async getOrderBook() { return { asks: [[50001, 1]], bids: [[49999, 1]] }; }
    async createOrder() { return { id: 'mock-order-id' }; }
    async cancelOrder() { return { id: 'mock-order-id' }; }
    async getOpenOrders() { return []; }
}


/**
 * Production Trading Executor with real exchange connectivity
 * No simulation mode - all trades are real
 */
class ProductionTradingExecutor extends EventEmitter {
    /**
     * Constructs a new instance of the ProductionTradingExecutor.
     *
     * @param {Object} [config={}] - Configuration object.
     * @param {number} [config.maxSlippage=0.02] - Maximum slippage allowed (percentage).
     * @param {number} [config.maxPositionSize=0.1] - Maximum position size allowed (percentage).
     * @param {number} [config.minOrderSize=10] - Minimum order size allowed (USD).
     * @param {number} [config.retryAttempts=3] - Number of times to retry failed operations.
     *
     * @throws {Error} If the configuration is invalid.
     */
    constructor(config = {}) {
        super();
        this.logger = createLogger('ProductionTradingExecutor');
        this.config = {
            maxSlippage: config.maxSlippage || 0.02, // 2%
            maxPositionSize: config.maxPositionSize || 0.1, // 10% of portfolio
            minOrderSize: config.minOrderSize || 10, // $10 minimum
            retryAttempts: config.retryAttempts || 3,
            ...config
        };

        this.exchangeConnector = new ProductionExchangeConnector(this.config);
        this.db = DatabaseManager; // Use the singleton instance
        this.activeOrders = new Map();
        this.positions = new Map();

        // Circuit breaker for safety
        this.circuitBreaker = new CircuitBreaker({
            name: 'TradingExecutor',
            threshold: 5,
            timeout: 60000,
            resetTimeout: 300000
        });

        this.isInitialized = false;
    }

    /**
     * Initializes the Trading Executor.
     *
     * This method is idempotent.
     *
     * @returns {Promise<void>} - Resolves when initialization is complete
     */
    async initialize() {
        if (this.isInitialized) return;

        try {
            await this.exchangeConnector.initialize();
            await this.loadPositions();
            this.setupEventListeners();

            this.isInitialized = true;
            this.logger.info('Production Trading Executor initialized');

        } catch (error) {
            this.logger.error('Failed to initialize Trading Executor:', error);
            throw error;
        }
    }

    /**
     * Sets up event listeners on the exchange connector for order events.
     *
     * Listens for:
     * - `orderCreated`: Emits `orderCreated` event with the order data
     * - `orderError`: Emits `orderError` event with the order data
     * - `myTrades`: Emits `myTrades` event with the trade data
     *
     * @private
     */
    setupEventListeners() {
        this.exchangeConnector.on('orderCreated', (data) => {
            this.handleOrderCreated(data);
        });

        this.exchangeConnector.on('orderError', (data) => {
            this.handleOrderError(data);
        });

        this.exchangeConnector.on('myTrades', (data) => {
            this.handleMyTrades(data);
        });
    }

    /**
     * Executes a trading order using the exchange connector.
     *
     * @param {OrderRequest} orderRequest - Order request data
     * @returns {Promise<Order>} - Executed order data
     * @throws {Error} - On order execution errors
     */
    async executeOrder(orderRequest) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            // Validate order request
            this.validateOrderRequest(orderRequest);

            // Check circuit breaker
            if (!this.circuitBreaker.canExecute()) {
                throw new Error('Trading circuit breaker is open - too many errors');
            }

            // Risk checks
            await this.performRiskChecks(orderRequest);

            // Calculate actual order parameters
            const orderParams = await this.calculateOrderParameters(orderRequest);

            // Execute the order
            const order = await this.placeOrder(orderParams);

            // Track the order
            this.activeOrders.set(order.id, {
                ...order,
                request: orderRequest,
                timestamp: new Date()
            });

            // Record to database
            await this.recordOrder(order);

            this.emit('orderExecuted', order);
            return order;

        } catch (error) {
            this.logger.error('Order execution failed:', error);
            this.circuitBreaker.recordFailure();

            await this.recordFailedOrder(orderRequest, error);
            throw error;
        }
    }

    /**
     * Validates an order request before execution.
     *
     * @param {OrderRequest} request - Order request data
     * @throws {Error} - On invalid order requests
     */
    validateOrderRequest(request) {
        const required = ['exchange', 'symbol', 'side', 'type', 'amount'];

        for (const field of required) {
            if (!request[field]) {
                throw new Error(`Missing required field: ${field}`);
            }
        }

        if (!['buy', 'sell'].includes(request.side)) {
            throw new Error('Invalid side: must be buy or sell');
        }

        if (!['market', 'limit', 'stop'].includes(request.type)) {
            throw new Error('Invalid order type');
        }

        if (request.type === 'limit' && !request.price) {
            throw new Error('Limit orders require a price');
        }

        if (request.amount <= 0) {
            throw new Error('Amount must be positive');
        }
    }

    /**
 * Performs risk checks before placing an order.
 *
 * Validates that the order complies with position limits, minimum order size,
 * and available balance constraints. Throws an error if any check fails.
 *
 * @param {OrderRequest} orderRequest - The order request data containing details
 *                                      such as symbol, side, type, amount, and price.
 * @throws {Error} - If position size exceeds maximum allowed, order value is below
 *                   minimum size, or if there is insufficient balance to execute the order.
 */

    /**
     * Performs risk checks before placing an order.
     *
     * Validates that the order complies with:
     * 1. Position limits: The order does not exceed the maximum allowed position size as a percentage of the portfolio value.
     * 2. Minimum order size: The order value is above the minimum allowed.
     * 3. Available balance: The order does not exceed the available balance of the required assets.
     *
     * Throws an error if any check fails.
     *
     * @param {OrderRequest} orderRequest - The order request data containing details
     *                                      such as symbol, side, type, amount, and price.
     * @throws {Error} - If position size exceeds maximum allowed, order value is below
     *                   minimum size, or if there is insufficient balance to execute the order.
     */
    async performRiskChecks(orderRequest) {
        // Check position limits
        const currentPosition = await this.getPosition(orderRequest.symbol);
        const portfolioValue = await this.getPortfolioValue();

        // Calculate position value after this order
        let positionValue;
        if (orderRequest.side === 'buy') {
            positionValue = (currentPosition.amount + orderRequest.amount) * (orderRequest.price || currentPosition.avgPrice);
        } else {
            positionValue = Math.max(0, currentPosition.amount - orderRequest.amount) * (orderRequest.price || currentPosition.avgPrice);
        }

        const positionPercent = positionValue / portfolioValue;

        if (positionPercent > this.config.maxPositionSize) {
            throw new Error(`Position size would exceed maximum: ${(positionPercent * 100).toFixed(2)}% > ${(this.config.maxPositionSize * 100)}%`);
        }

        // Check minimum order size
        const orderValue = orderRequest.amount * (orderRequest.price || await this.getCurrentPrice(orderRequest.exchange, orderRequest.symbol));

        if (orderValue < this.config.minOrderSize) {
            throw new Error(`Order value $${orderValue.toFixed(2)} below minimum $${this.config.minOrderSize}`);
        }

        // Check available balance
        const balance = await this.exchangeConnector.getBalance(orderRequest.exchange);

        if (orderRequest.side === 'buy') {
            const [, quote] = orderRequest.symbol.split('/');
            const required = orderValue * 1.002; // Include fees

            if (balance.free[quote] < required) {
                throw new Error(`Insufficient ${quote} balance: ${balance.free[quote]} < ${required}`);
            }
        } else {
            const [base] = orderRequest.symbol.split('/');

            if (balance.free[base] < orderRequest.amount) {
                throw new Error(`Insufficient ${base} balance: ${balance.free[base]} < ${orderRequest.amount}`);
            }
        }
    }

    /**
     * Calculates the final order parameters before sending to the exchange.
     *
     * @param {OrderRequest} request - Order request data
     * @returns {Promise<OrderRequest>} Final order parameters
     *
     * This method:
     * 1. Gets the current market price.
     * 2. Applies slippage protection for market orders by converting them to limit orders
     *    if the estimated slippage is greater than the configured maximum.
     * 3. Rounds the amount and price to the exchange's precision.
     * 4. Adds order tags for easier identification.
     */
    async calculateOrderParameters(request) {
        const params = { ...request };

        // Get current market price
        const ticker = await this.exchangeConnector.getTicker(request.exchange, request.symbol);
        const currentPrice = ticker.last;

        // Apply slippage protection for market orders
        if (request.type === 'market') {
            const orderbook = await this.exchangeConnector.getOrderBook(request.exchange, request.symbol);

            if (request.side === 'buy') {
                const estimatedPrice = this.calculateWeightedPrice(orderbook.asks, request.amount);
                const slippage = (estimatedPrice - currentPrice) / currentPrice;

                if (slippage > this.config.maxSlippage) {
                    // Convert to limit order with slippage protection
                    params.type = 'limit';
                    params.price = currentPrice * (1 + this.config.maxSlippage);
                    this.logger.warn(`Converted market order to limit due to slippage: ${(slippage * 100).toFixed(2)}%`);
                }
            } else {
                const estimatedPrice = this.calculateWeightedPrice(orderbook.bids, request.amount);
                const slippage = (currentPrice - estimatedPrice) / currentPrice;

                if (slippage > this.config.maxSlippage) {
                    params.type = 'limit';
                    params.price = currentPrice * (1 - this.config.maxSlippage);
                    this.logger.warn(`Converted market order to limit due to slippage: ${(slippage * 100).toFixed(2)}%`);
                }
            }
        }

        // Round to exchange precision
        const exchange = this.exchangeConnector.exchanges.get(request.exchange);

        params.amount = exchange.amountToPrecision(request.symbol, request.amount);
        if (params.price) {
            params.price = exchange.priceToPrecision(request.symbol, params.price);
        }

        // Add order tags
        params.clientOrderId = `${request.strategyId || 'manual'}_${Date.now()}`;

        return params;
    }

    /**
     * Places an order on the exchange.
     *
     * @param {object} params - Order parameters
     * @param {string} params.exchange - Exchange name
     * @param {string} params.symbol - Trading pair symbol
     * @param {'market'|'limit'|'stop_loss'|'take_profit'} params.type - Order type
     * @param {'buy'|'sell'} params.side - Order side
     * @param {number} params.amount - Order quantity
     * @param {number} [params.price] - Limit price (required for limit orders)
     * @param {object} [params.options] - Additional order options
     * @param {string} [params.options.clientOrderId] - Client-assigned order ID
     * @param {number} [params.options.stopPrice] - Stop loss price (required for stop loss orders)
     * @param {number} [params.options.takeProfitPrice] - Take profit price (required for take profit orders)
     * @returns {Promise<object>} Placed order
     *
     * This method will retry the order up to `retryAttempts` times if it fails.
     * The delay between retries will be exponentially increased.
     * If the order fails due to insufficient balance or minimum order size, it will be thrown immediately.
     */
    async placeOrder(params) {
        let lastError;

        for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
            try {
                const order = await this.exchangeConnector.createOrder(
                    params.exchange,
                    params.symbol,
                    params.type,
                    params.side,
                    params.amount,
                    params.price,
                    {
                        clientOrderId: params.clientOrderId,
                        stopPrice: params.stopPrice,
                        takeProfitPrice: params.takeProfitPrice
                    }
                );

                this.circuitBreaker.recordSuccess();
                return order;

            } catch (error) {
                lastError = error;
                this.logger.error(`Order attempt ${attempt} failed:`, error);

                // Don't retry certain errors
                if (error.message.includes('insufficient balance') ||
                    error.message.includes('minimum order size')) {
                    throw error;
                }

                if (attempt < this.config.retryAttempts) {
                    await this.sleep(1000 * attempt); // Exponential backoff
                }
            }
        }

        throw lastError;
    }

    /**
     * Calculates the weighted average price of a list of orders up to a given amount.
     *
     * @param {Array<Array<number>>} orders - List of orders, where each order is an array of [price, volume]
     * @param {number} amount - Amount of the order
     * @returns {number} Weighted average price of the orders up to the given amount
     */
    calculateWeightedPrice(orders, amount) {
        let remainingAmount = amount;
        let totalCost = 0;

        for (const [price, volume] of orders) {
            const fillAmount = Math.min(remainingAmount, volume);
            totalCost += fillAmount * price;
            remainingAmount -= fillAmount;

            if (remainingAmount <= 0) break;
        }

        return totalCost / amount;
    }

    /**
     * Retrieves the current price for a specified trading pair from the exchange.
     *
     * @async
     * @param {string} exchange - The name of the exchange to fetch the price from.
     * @param {string} symbol - The trading pair symbol (e.g., 'BTC/USD').
     * @returns {Promise<number>} - Resolves with the latest price of the trading pair.
     */

    async getCurrentPrice(exchange, symbol) {
        const ticker = await this.exchangeConnector.getTicker(exchange, symbol);
        return ticker.last;
    }

    /**
     * Retrieves the current position for a specified trading pair.
     *
     * @async
     * @param {string} symbol - The trading pair symbol (e.g., 'BTC/USD').
     * @returns {Promise<Object>} - Resolves with the position information, including amount, average price, unrealized PnL, and realized PnL.
     * @property {string} symbol - Trading pair symbol.
     * @property {number} amount - Amount of the position.
     * @property {number} avgPrice - Average price of the position.
     * @property {number} unrealizedPnL - Unrealized profit and loss of the position.
     * @property {number} realizedPnL - Realized profit and loss of the position.
     */
    async getPosition(symbol) {
        const position = this.positions.get(symbol) || {
            symbol,
            amount: 0,
            avgPrice: 0,
            unrealizedPnL: 0,
            realizedPnL: 0
        };

        // Update with latest price
        try {
            const currentPrice = await this.getCurrentPrice(position.exchange || 'binance', symbol);
            position.unrealizedPnL = (currentPrice - position.avgPrice) * position.amount;
        } catch (error) {
            // Price update failed, use cached value
            this.logger.warn(`Could not update price for ${symbol}, using cached PnL. Error: ${error.message}`);
        }

        return position;
    }

    /**
     * Calculates the total value of all assets across all exchanges.
     *
     * @async
     * @returns {Promise<number>} - The total value of all assets.
     */
    async getPortfolioValue() {
        let totalValue = 0;

        for (const [exchangeName] of this.exchangeConnector.exchanges) {
            try {
                const balance = await this.exchangeConnector.getBalance(exchangeName);
                for (const [currency, amount] of Object.entries(balance.total)) {
                    if (amount > 0) {
                        totalValue += await this._getCurrencyValueInUsd(exchangeName, currency, amount);
                    }
                }
            } catch (error) {
                this.logger.error(`Failed to get balance for ${exchangeName}:`, error);
            }
        }

        return totalValue;
    }

    /**
     * Retrieves the value of a given amount of a currency in USD on a specified exchange.
     * If the currency is a stablecoin (USDT, USDC, USD), the value is simply the amount.
     * If the currency is not a stablecoin, the value is the amount multiplied by the latest price from the USDT pair.
     * If the currency does not have a USDT pair on the exchange, the value is 0.
     *
     * @async
     * @param {string} exchangeName - The name of the exchange to fetch the price from.
     * @param {string} currency - The currency symbol.
     * @param {number} amount - The amount of the currency.
     * @returns {Promise<number>} - The value of the given amount of the currency in USD.
     */
    async _getCurrencyValueInUsd(exchangeName, currency, amount) {
        const stablecoins = ['USDT', 'USDC', 'USD'];
        if (stablecoins.includes(currency)) {
            return amount;
        }

        try {
            const ticker = await this.exchangeConnector.getTicker(exchangeName, `${currency}/USDT`);
            return amount * ticker.last;
        } catch (e) {
            // Ignore currencies without USDT pair, their value is 0 in this context
            this.logger.debug(`Could not get USDT price for ${currency} on ${exchangeName}. Assuming value is 0. Error: ${e.message}`);
            return 0;
        }
    }

    /**
 * Loads open positions from the database and stores them in memory.
 * This function queries the database for all positions with a status of 'open',
 * and populates the `positions` map with the relevant details for each position.
 * Each position includes the symbol, exchange, amount, average price, unrealized PnL,
 * and realized PnL. Logs the number of positions loaded upon successful completion.
 * In case of an error, logs the error message.
 *
 * @async
 * @returns {Promise<void>} - Resolves when positions are successfully loaded.
 */

    async loadPositions() {
        // Load positions from database
        try {
            const positions = await this.db.all(
                'SELECT * FROM positions WHERE status = ?',
                ['open']
            );

            for (const pos of positions) {
                this.positions.set(pos.symbol, {
                    symbol: pos.symbol,
                    exchange: pos.exchange,
                    amount: pos.amount,
                    avgPrice: pos.avg_price,
                    unrealizedPnL: 0,
                    realizedPnL: pos.realized_pnl || 0
                });
            }

            this.logger.info(`Loaded ${positions.length} positions`);

        } catch (error) {
            this.logger.error('Failed to load positions:', error);
        }
    }

    /**
     * Updates the position for the specified symbol and exchange based on the given trade.
     * If the trade is a buy, the average price of the position is updated.
     * If the trade is a sell, the realized profit and loss is calculated and the position is updated.
     * If the position is closed (i.e., the amount is 0 or less), the position is removed from the map.
     * The updated position is then saved to the database.
     *
     * @async
     * @param {object} trade - The trade details, including symbol, exchange, amount, price, and side.
     * @returns {Promise<void>} - Resolves when the position is updated and saved to the database.
     */
    async updatePosition(trade) {
        const position = this.positions.get(trade.symbol) || {
            symbol: trade.symbol,
            exchange: trade.exchange,
            amount: 0,
            avgPrice: 0,
            unrealizedPnL: 0,
            realizedPnL: 0
        };

        if (trade.side === 'buy') {
            // Update average price
            const totalCost = (position.avgPrice * position.amount) + (trade.price * trade.amount);
            position.amount += trade.amount;
            position.avgPrice = position.amount > 0 ? totalCost / position.amount : 0;
        } else {
            // Calculate realized P&L
            const profit = (trade.price - position.avgPrice) * trade.amount;
            position.realizedPnL += profit;
            position.amount -= trade.amount;

            if (position.amount <= 0) {
                // Position closed
                position.amount = 0;
                position.avgPrice = 0;
            }
        }

        this.positions.set(trade.symbol, position);

        // Update database
        await this.savePosition(position);
    }

    /**
 * Saves the given position to the database. If the position's amount is greater
 * than zero, it inserts or replaces the record in the 'positions' table with an
 * 'open' status. If the amount is zero or less, it updates the position status
 * to 'closed' and records the closure time. Logs an error message if the operation fails.
 *
 * @param {Object} position - The position object to be saved.
 * @param {string} position.symbol - The trading pair symbol.
 * @param {string} position.exchange - The exchange where the position exists.
 * @param {number} position.amount - The amount of the position.
 * @param {number} position.avgPrice - The average price of the position.
 * @param {number} position.realizedPnL - The realized profit and loss for the position.
 */

    async savePosition(position) {
        try {
            if (position.amount > 0) {
                await this.db.run(
                    `INSERT OR REPLACE INTO positions 
                    (symbol, exchange, amount, avg_price, realized_pnl, status, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)`,
                    [
                        position.symbol,
                        position.exchange,
                        position.amount,
                        position.avgPrice,
                        position.realizedPnL,
                        'open',
                        new Date().toISOString()
                    ]
                );
            } else {
                // Close position
                await this.db.run(
                    'UPDATE positions SET status = ?, closed_at = ? WHERE symbol = ?',
                    ['closed', new Date().toISOString(), position.symbol]
                );
            }
        } catch (error) {
            this.logger.error('Failed to save position:', error);
        }
    }

    /**
     * Records a new order in the trading_transactions database table.
     *
     * @async
     * @param {Object} order - The order details to be recorded.
     * @param {string} order.exchange - The exchange where the order is placed.
     * @param {string} order.symbol - The trading pair symbol (e.g., 'BTC/USD').
     * @param {string} order.side - The side of the order ('buy' or 'sell').
     * @param {string} order.type - The type of the order (e.g., 'market', 'limit').
     * @param {number} order.amount - The amount of the order.
     * @param {number} [order.price] - The price of the order, or the average price if not specified.
     * @param {string} order.status - The current status of the order.
     * @param {string} order.id - Unique identifier for the order.
     * @throws {Error} - If the database operation fails, logs the error message.
     */
    async recordOrder(order) {
        try {
            await this.db.run(
                `INSERT INTO trading_transactions 
                (id, exchange, symbol, side, type, amount, price, status, order_id, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    uuidv4(),
                    order.exchange,
                    order.symbol,
                    order.side,
                    order.type,
                    order.amount,
                    order.price || order.average,
                    order.status,
                    order.id,
                    new Date().toISOString()
                ]
            );
        } catch (error) {
            this.logger.error('Failed to record order:', error);
        }
    }

    /**
     * Records a failed order in the failed_orders database table.
     *
     * @async
     * @param {Object} request - The order request data containing details
     *                           such as symbol, exchange, side, type, amount, and price.
     * @param {Error} error - The error that caused the order to fail.
     * @throws {Error} - If the database operation fails, logs the error message.
     */
    async recordFailedOrder(request, error) {
        try {
            await this.db.run(
                `INSERT INTO failed_orders 
                (exchange, symbol, side, type, amount, price, error_message, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    request.exchange,
                    request.symbol,
                    request.side,
                    request.type,
                    request.amount,
                    request.price,
                    error.message,
                    new Date().toISOString()
                ]
            );
        } catch (error) {
            this.logger.error('Failed to record failed order:', error);
        }
    }

    /**
     * Handles order creation events.
     *
     * @param {Object} data - The order data emitted by the exchange connector.
     * @property {string} data.exchange - The exchange name.
     * @property {Object} data.order - The order details.
     * @property {string} data.order.id - The order identifier.
     */
    handleOrderCreated(data) {
        this.logger.info(`Order created on ${data.exchange}:`, data.order.id);
        this.emit('orderUpdate', data);
    }

    /**
     * Handles order error events.
     *
     * @param {Object} data - The error data emitted by the exchange connector.
     * @property {string} data.exchange - The exchange name.
     * @property {Error} data.error - The error that occurred.
     */
    handleOrderError(data) {
        this.logger.error(`Order error on ${data.exchange}:`, data.error);
        this.emit('orderError', data);
    }

    /**
     * Handles my trades events from the exchange connector.
     *
     * @param {Object} data - The trade data emitted by the exchange connector.
     * @property {string} data.exchange - The exchange name.
     * @property {Object[]} data.trades - The executed trade data.
     * @property {string} data.trades[n].orderId - The order identifier.
     * @property {string} data.trades[n].symbol - The trading pair symbol.
     * @property {string} data.trades[n].side - The order side.
     * @property {number} data.trades[n].amount - The executed quantity.
     * @property {number} data.trades[n].price - The executed price.
     * @property {number} data.trades[n].timestamp - The trade timestamp.
     * @property {number} data.trades[n].fee - The trade fee amount.
     * @property {string} data.trades[n].feeCurrency - The fee currency.
     * @emits tradeExecuted
     */
    async handleMyTrades(data) {
        // Update positions based on executed trades
        for (const trade of data.trades) {
            await this.updatePosition(trade);
            this.emit('tradeExecuted', trade);
        }
    }

    /**
     * Cancels an active order.
     *
     * @param {string} exchange - The exchange name.
     * @param {string} orderId - The order identifier.
     * @param {string} symbol - The trading pair symbol.
     * @returns {Promise<Object>} The cancelled order.
     * @emits orderCancelled
     */
    async cancelOrder(exchange, orderId, symbol) {
        try {
            const result = await this.exchangeConnector.cancelOrder(exchange, orderId, symbol);
            this.activeOrders.delete(orderId);

            await this.recordOrderCancellation(orderId);

            this.emit('orderCancelled', { exchange, orderId, symbol });
            return result;

        } catch (error) {
            this.logger.error(`Failed to cancel order ${orderId}:`, error);
            throw error;
        }
    }

    /**
     * Records an order cancellation in the database.
     *
     * @async
     * @param {string} orderId - The order identifier.
     * @throws {Error} - If the database operation fails, logs the error message.
     */
    async recordOrderCancellation(orderId) {
        try {
            await this.db.run(
                'UPDATE trading_transactions SET status = ?, cancelled_at = ? WHERE order_id = ?',
                ['cancelled', new Date().toISOString(), orderId]
            );
        } catch (error) {
            this.logger.error('Failed to record order cancellation:', error);
        }
    }

    /**
     * Retrieves open orders from one or all exchanges.
     *
     * If an exchange name is provided, only open orders from that exchange will be returned.
     * Otherwise, open orders from all exchanges will be returned.
     *
     * @param {string} [exchange] - The exchange name to retrieve open orders from.
     * @param {string} [symbol] - The trading pair symbol to filter open orders by.
     * @returns {Promise<Object[]>} - An array of open order objects.
     * @throws {Error} - If an error occurs while retrieving open orders from any exchange.
     */
    async getOpenOrders(exchange = null, symbol = null) {
        const openOrders = [];

        if (exchange) {
            const orders = await this.exchangeConnector.getOpenOrders(exchange, symbol);
            openOrders.push(...orders);
        } else {
            // Get from all exchanges
            for (const [exchangeName] of this.exchangeConnector.exchanges) {
                try {
                    const orders = await this.exchangeConnector.getOpenOrders(exchangeName, symbol);
                    openOrders.push(...orders);
                } catch (error) {
                    this.logger.error(`Failed to get open orders from ${exchangeName}:`, error);
                }
            }
        }

        return openOrders;
    }

    /**
     * Stops all trading activities by canceling all open orders and closing the circuit breaker.
     *
     * @param {string} [reason='manual'] - The reason for the emergency stop.
     * @emits emergencyStop
     * @throws {Error} - If an error occurs while canceling an order.
     */
    async emergencyStop(reason = 'manual') {
        this.logger.warn(`EMERGENCY STOP TRIGGERED: ${reason}`);

        // Cancel all open orders
        const openOrders = await this.getOpenOrders();

        for (const order of openOrders) {
            try {
                await this.cancelOrder(order.exchange, order.id, order.symbol);
            } catch (error) {
                this.logger.error('Failed to cancel order during emergency stop:', error);
            }
        }

        // Close the circuit breaker
        this.circuitBreaker.open();

        this.emit('emergencyStop', { reason, ordersCancelled: openOrders.length });
    }

    /**
 * Pauses execution for a specified duration.
 *
 * @param {number} ms - The duration to sleep in milliseconds.
 * @returns {Promise<void>} A promise that resolves after the specified delay.
 */

    /**
     * Utility function to create a delay.
     * @param {number} ms - The delay in milliseconds.
     * @returns {Promise<void>} A promise that resolves after the specified delay.
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = ProductionTradingExecutor;

// ⚡ TRADING EXECUTION ENGINE
// Advanced order management and execution system

import { NodeOperationError } from 'n8n-workflow';
import path from 'path';
import crypto from 'crypto';
import DatabaseManager from './database-helper.js';
import { calculateVolatility, calculateLiquidity } from './utils/indicator-utils.js'; // Assume these helpers exist
/**
 * @typedef {import('ccxt')} ccxt
 */

// Constants
// Constants
const CONSTANTS = {
    FEES: {
        DEFAULT_TRADING_FEE: 0.001, // 0.1%
        SLIPPAGE_MULTIPLIER: 1.5,
        LARGE_ORDER_THRESHOLD: 5000,
    },
    TIMEOUTS: {
        API_TIMEOUT: 15000,
        ORDER_TIMEOUT: 30000,
        RETRY_DELAY: 1000,
        SPLIT_ORDER_DELAY: 1000,
    },
    LIMITS: {
        MAX_SLIPPAGE_BPS: 100, // 1%
        MAX_ORDER_VALUE: 10000,
        MIN_ORDER_VALUE: 10,
        MAX_RETRY_ATTEMPTS: 3,
        MAX_SPLIT_PARTS: 5,
        SPLIT_ORDER_THRESHOLD: 5000,
        MIN_SPLIT_SIZE: 2000,
    },
    RISK: {
        LOW_LIQUIDITY_THRESHOLD: 50000,
        MEDIUM_LIQUIDITY_THRESHOLD: 100000,
        HIGH_VOLATILITY_THRESHOLD: 10,
        MEDIUM_VOLATILITY_THRESHOLD: 5,
        ORDER_IMPACT_THRESHOLD: 0.1,
        HIGH_RISK_SCORE: 7,
        MEDIUM_RISK_SCORE: 4,
    },
    API: {
        DEXSCREENER_URL: 'https://api.dexscreener.com/latest/dex/search',
        PIONEX_URL: 'https://api.pionex.com',
        BINANCE_URL: 'https://api.binance.com',
        COINBASE_URL: 'https://api.exchange.coinbase.com',
    },
    SIMULATION: {
        MIN_DELAY: 500,
        MAX_DELAY: 2500,
        DEFAULT_BALANCES: {
            'USDT': 10000,
            'ETH': 2,
            'BTC': 0.1,
            'PEPE': 1000000,
            'DOGE': 50000,
        },
    },
};

/**
 * @typedef {Object} OrderParams
 * @property {string} symbol - Trading pair symbol (e.g., 'BTC/USDT')
 * @property {'buy'|'sell'} side - Order side
 * @property {'market'|'limit'|'stop_loss'|'take_profit'|'grid'} type - Order type
 * @property {number} quantity - Order quantity
 * @property {number} [price] - Limit price (required for limit orders)
 * @property {number} [stopLoss] - Stop loss price
 * @property {number} [takeProfit] - Take profit price
 * @property {string} [strategy] - Execution strategy identifier
 * @property {boolean} [forceExecution] - Force execution despite high risk
 */

/**
 * @typedef {Object} ExecutionStrategy
 * @property {'aggressive'|'conservative'|'balanced'} approach - Strategy approach
 * @property {number} slippageTolerance - Slippage tolerance percentage
 * @property {'low'|'medium'|'high'} urgency - Order urgency
 * @property {boolean} splitOrder - Whether to split the order
 * @property {number} maxOrderParts - Maximum number of order parts
 */

/**
 * @typedef {Object} MarketData
 * @property {number} price - Current market price
 * @property {number} volume24h - 24-hour trading volume
 * @property {number} liquidity - Market liquidity
 * @property {number} spread - Bid-ask spread percentage
 * @property {number} volatility - Price volatility percentage
 * @property {string} timestamp - Data timestamp
 */

/**
 * @typedef {Object} ExecutionResult
 * @property {string} orderId - Unique order identifier
 * @property {string} symbol - Trading pair symbol
 * @property {'buy'|'sell'} side - Order side
 * @property {string} type - Order type
 * @property {number} quantity - Requested quantity
 * @property {number} executedPrice - Average execution price
 * @property {number} executedQuantity - Actually executed quantity
 * @property {number} executedValue - Total execution value
 * @property {string} status - Order status
 * @property {string} timestamp - Execution timestamp
 * @property {number} fees - Trading fees
 * @property {number} slippage - Slippage percentage
 * @property {string} strategy - Strategy used
 * @property {string} [exchange] - Exchange name
 * @property {boolean} [simulation] - Whether this was simulated
 * @property {boolean} [splitOrder] - Whether order was split
 * @property {number} [parts] - Number of parts (for split orders)
 * @property {ExecutionResult[]} [partResults] - Individual part results
 * @property {Object} [raw] - Raw exchange response
 * @property {number} [profit_loss] - Profit or loss amount
 */

/**
 * @typedef {Object} RiskAssessment
 * @property {'low'|'medium'|'high'} riskLevel - Overall risk level
 * @property {number} riskScore - Numeric risk score
 * @property {Object} factors - Risk factors
 * @property {number} factors.liquidity - Liquidity value
 * @property {number} factors.volatility - Volatility percentage
 * @property {number} factors.orderImpact - Order impact percentage
 */

/**
 * @typedef {Object} ValidationResult
 * @property {boolean} valid - Whether order is valid
 * @property {string} [reason] - Reason if invalid
 */

/**
 * @typedef {Object} TradingConfig
 * @property {string} [apiKey] - Exchange API key
 * @property {string} [apiSecret] - Exchange API secret
 * @property {string} baseUrl - Exchange API base URL
 * @property {'simulation'|'live'} tradingMode - Trading mode
 * @property {number} maxSlippageBps - Maximum slippage in basis points
 * @property {number} maxOrderValue - Maximum order value in USD
 * @property {number} minOrderValue - Minimum order value in USD
 * @property {number} orderTimeout - Order timeout in milliseconds
 * @property {number} retryAttempts - Number of retry attempts
 * @property {number} retryDelay - Delay between retries in milliseconds
 * @property {string} [exchange] - Default exchange name
 */

/**
 * Trading execution engine for managing orders across multiple exchanges
 */
class TradingExecutor {
    /**
     * Creates an instance of TradingExecutor.
     * @param {ccxt.Exchange} exchange - An initialized CCXT exchange instance.
     * @param {Partial<TradingConfig>} config - Configuration overrides.
     */
    constructor(exchange, config = {}) {
        if (!exchange) {
            throw new Error('A fully initialized CCXT exchange instance is required.');
        }

        this.dbPath = path.join(__dirname, '..', '..', 'databases', 'trading_bot.db');
        this.db = DatabaseManager;
        this.exchange = exchange; // Use the provided CCXT exchange instance

        this.config = {
            tradingMode: process.env.TRADING_MODE || 'simulation',
            maxSlippageBps: CONSTANTS.LIMITS.MAX_SLIPPAGE_BPS,
            maxOrderValue: CONSTANTS.LIMITS.MAX_ORDER_VALUE,
            minOrderValue: CONSTANTS.LIMITS.MIN_ORDER_VALUE,
            orderTimeout: CONSTANTS.TIMEOUTS.ORDER_TIMEOUT,
            retryAttempts: CONSTANTS.LIMITS.MAX_RETRY_ATTEMPTS,
            retryDelay: CONSTANTS.TIMEOUTS.RETRY_DELAY,
            exchange: exchange.id, // Get the exchange name from the ccxt instance
            ...config,
        };

        this.orderTypes = {
            MARKET: 'market',
            LIMIT: 'limit',
            STOP_LOSS: 'stop_loss',
            TAKE_PROFIT: 'take_profit',
            GRID: 'grid',
        };

        this.executionStrategies = {
            AGGRESSIVE: 'aggressive',
            CONSERVATIVE: 'conservative',
            BALANCED: 'balanced',
        };

        /** @type {Map<string, OrderParams>} */
        this.pendingOrders = new Map();
        /** @type {OrderParams[]} */
        this.executionQueue = [];
        /** @type {boolean} */
        this.isProcessingQueue = false;
        /** @type {Record<string, number>} */
        this.simulatedBalances = { ...CONSTANTS.SIMULATION.DEFAULT_BALANCES };
    }

    /**
     * Returns the node context for error reporting.
     * In n8n custom nodes, this should be overridden or set appropriately.
     * For now, returns null.
     */
    getNode() {
        return null;
    }

    /**
     * Initialize database connection and create tables if they don't exist.
     * @async
     * @returns {Promise<void>}
     */
    async initializeDatabase() {
        try {
            await this.db.connect(this.dbPath);
            await this.db.run(`
                CREATE TABLE IF NOT EXISTS trading_transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    orderId TEXT UNIQUE,
                    symbol TEXT NOT NULL,
                    side TEXT NOT NULL,
                    type TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    executedPrice REAL,
                    executedQuantity REAL,
                    status TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    fees REAL,
                    slippage REAL,
                    strategy TEXT,
                    exchange TEXT,
                    notes TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);
            console.log('✅ TradingExecutor database initialized.');
        } catch (err) {
            console.error('❌ Database initialization failed:', err.message);
            throw err;
        }
    }

    // 🎯 CORE TRADING FUNCTIONS (Now with live data)

    /**
     * Execute a trading order.
     * @async
     * @param {OrderParams} orderParams - Order parameters.
     * @returns {Promise<ExecutionResult>}
     */
    async executeOrder(orderParams) {
        if (!this.db) throw new NodeOperationError(this.getNode(), 'Database not initialized. Call initializeDatabase() first.');

        try {
            // STEP 1: Get LIVE market data
            const marketData = await this.getMarketData(orderParams.symbol);

            // STEP 2: Perform validation and risk checks with live data
            const validation = await this._validateOrder(orderParams, marketData);
            if (!validation.valid) {
                throw new NodeOperationError(this.getNode(), `Order validation failed: ${validation.reason}`);
            }

            const risk = this._assessRisk(orderParams, marketData);
            if (risk.riskLevel === 'high' && !orderParams.forceExecution) {
                throw new NodeOperationError(this.getNode(), `High risk detected (score: ${risk.riskScore}). Execution halted.`);
            }

            // STEP 3: Decide on execution strategy (e.g., splitting)
            const orderValue = orderParams.quantity * marketData.price;
            if (orderValue > CONSTANTS.LIMITS.SPLIT_ORDER_THRESHOLD) {
                return await this._splitOrder(orderParams);
            }

            // STEP 4: Execute the order (live or simulated)
            const result = this.config.tradingMode === 'live'
                ? await this._executeLiveOrder(orderParams, marketData)
                : await this._executeSimulatedOrder(orderParams, marketData);

            await this.recordExecution(result);
            return result;
        } catch (error) {
            console.error(`❌ Order execution failed for ${orderParams.symbol}:`, error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            await this.recordFailedExecution(orderParams, errorMessage);
            throw new NodeOperationError(this.getNode(), `Trading execution failed for ${orderParams.symbol}: ${errorMessage}`);
        }
    }

    /**
     * Convenience method for buying a token.
     * @param {string} symbol - e.g., 'PEPE/USDT'
     * @param {number} quantity - Amount of token to buy.
     * @returns {Promise<ExecutionResult>}
     */
    async buyToken(symbol, quantity) {
        return this.executeOrder({symbol, side: 'buy', type: 'market', quantity});
    }

    /**
     * Convenience method for selling a token.
     * @param {string} symbol - e.g., 'DOGE/USDT'
     * @param {number} quantity - Amount of token to sell.
     * @returns {Promise<ExecutionResult>}
     */
    async sellToken(symbol, quantity) {
        return this.executeOrder({symbol, side: 'sell', type: 'market', quantity});
    }

    /**
     * Splits a large order into smaller parts.
     * @private
     * @param {OrderParams} orderParams - The original order.
     * @returns {Promise<ExecutionResult>}
     */
    async _splitOrder(orderParams) {
        console.log(`Splitting large order for ${orderParams.symbol}...`);
        const marketData = await this.getMarketData(orderParams.symbol);
        const parts = Math.min(
            Math.ceil((orderParams.quantity * marketData.price) / CONSTANTS.LIMITS.MIN_SPLIT_SIZE),
            CONSTANTS.LIMITS.MAX_SPLIT_PARTS
        );
        const partQuantity = orderParams.quantity / parts;
        const partResults = [];

        for (let i = 0; i < parts; i++) {
            const partOrder = {...orderParams, quantity: partQuantity};
            try {
                // Avoid recursive splitting by calling internal methods directly
                const marketData = await this.getMarketData(partOrder.symbol);
                const validation = this._validateOrder(partOrder, marketData);
                if (!validation.valid) {
                    throw new NodeOperationError(this.getNode(), `Part ${i + 1} validation failed: ${validation.reason}`);
                }

                const result = this.config.tradingMode === 'live'
                    ? await this._executeLiveOrder(partOrder, marketData)
                    : await this._executeSimulatedOrder(partOrder, marketData);

                await this.recordExecution(result);
                partResults.push(result);
                await new Promise(res => setTimeout(res, CONSTANTS.TIMEOUTS.SPLIT_ORDER_DELAY));
            } catch (error) {
                console.error(`Error executing part ${i + 1} of split order:`, error.message);
                // Record the failed part
                await this.recordFailedExecution(partOrder, error.message);
            }
        }

        return this._combineSplitOrderResults(partResults, orderParams);
    }

    /**
     * Combines results from a split order into a single result.
     * @private
     * @param {ExecutionResult[]} partResults - Results from each part.
     * @param {OrderParams} originalOrder - The original order.
     * @returns {ExecutionResult}
     */
    _combineSplitOrderResults(partResults, originalOrder) {
        if (partResults.length === 0) {
            throw new NodeOperationError(this.getNode(), 'All parts of the split order failed.');
        }
        const totalExecutedQuantity = partResults.reduce((sum, r) => sum + r.executedQuantity, 0);
        const totalExecutedValue = partResults.reduce((sum, r) => sum + r.executedValue, 0);
        const avgPrice = totalExecutedValue / totalExecutedQuantity;
        const totalFees = partResults.reduce((sum, r) => sum + r.fees, 0);

        return {
            orderId: `split_${crypto.randomBytes(8).toString('hex')}`,
            symbol: originalOrder.symbol,
            side: originalOrder.side,
            type: originalOrder.type,
            quantity: originalOrder.quantity,
            executedPrice: avgPrice,
            executedQuantity: totalExecutedQuantity,
            executedValue: totalExecutedValue,
            status: totalExecutedQuantity > 0 ? 'filled' : 'failed',
            timestamp: new Date().toISOString(),
            fees: totalFees,
            slippage: 0, // Complex to calculate, omitted for now
            strategy: originalOrder.strategy || 'split',
            exchange: this.config.exchange,
            simulation: this.config.tradingMode === 'simulation',
            splitOrder: true,
            parts: partResults.length,
            partResults,
        };
    }

    // 🛡️ VALIDATION AND RISK

    /**
     * Validates an order before execution, including balance checks.
     * @private
     * @param {OrderParams} orderParams - The order to validate.
     * @param {MarketData} marketData - Current market data.
     * @returns {Promise<ValidationResult>}
     */
    async _validateOrder(orderParams, marketData) {
        const orderValue = orderParams.quantity * marketData.price;
        if (orderValue < this.config.minOrderValue) {
            return {
                valid: false,
                reason: `Order value ($${orderValue.toFixed(2)}) is below minimum ($${this.config.minOrderValue})`
            };
        }
        if (orderValue > this.config.maxOrderValue) {
            return {
                valid: false,
                reason: `Order value ($${orderValue.toFixed(2)}) is above maximum ($${this.config.maxOrderValue})`
            };
        }

        const [base, quote] = orderParams.symbol.split('/');
        const requiredAsset = orderParams.side === 'buy' ? quote : base;
        // For buys, we need the quote currency value; for sells, we need the base currency quantity.
        const requiredAmount = orderParams.side === 'buy' ? orderValue : orderParams.quantity;

        if (this.config.tradingMode === 'live') {
            // Fetch live balance for validation
            const balance = await this.exchange.fetchBalance();
            const availableBalance = balance.free[requiredAsset] || 0;
            if (availableBalance < requiredAmount) {
                return {
                    valid: false,
                    reason: `Insufficient live balance for ${requiredAsset}. Required: ${requiredAmount}, Available: ${availableBalance}`
                };
            }
        } else if ((this.simulatedBalances[requiredAsset] || 0) < requiredAmount) { // simulation mode
            return {
                valid: false,
                reason: `Insufficient simulated balance for ${requiredAsset}. Required: ${requiredAmount}, Available: ${this.simulatedBalances[requiredAsset] || 0}`
            };
        }

        return { valid: true };
    }

    /**
     * Assesses the risk of an order.
     * @private
     * @param {OrderParams} orderParams - The order to assess.
     * @param {MarketData} marketData - Current market data.
     * @returns {RiskAssessment}
     */
    _assessRisk(orderParams, marketData) {
        let riskScore = 0;
        if (marketData.liquidity < CONSTANTS.RISK.LOW_LIQUIDITY_THRESHOLD) riskScore += 4;
        else if (marketData.liquidity < CONSTANTS.RISK.MEDIUM_LIQUIDITY_THRESHOLD) riskScore += 2;

        if (marketData.volatility > CONSTANTS.RISK.HIGH_VOLATILITY_THRESHOLD) riskScore += 4;
        else if (marketData.volatility > CONSTANTS.RISK.MEDIUM_VOLATILITY_THRESHOLD) riskScore += 2;

        const orderImpact = (orderParams.quantity * marketData.price) / marketData.liquidity;
        if (orderImpact > CONSTANTS.RISK.ORDER_IMPACT_THRESHOLD) riskScore += 3;

        /** @type {'low' | 'medium' | 'high'} */
        let riskLevel = 'low';
        if (riskScore >= CONSTANTS.RISK.HIGH_RISK_SCORE) riskLevel = 'high';
        else if (riskScore >= CONSTANTS.RISK.MEDIUM_RISK_SCORE) riskLevel = 'medium';

        return {
            riskLevel,
            riskScore,
            factors: {
                liquidity: marketData.liquidity,
                volatility: marketData.volatility,
                orderImpact,
            },
        };
    }

    // 📊 DATA & STATE MANAGEMENT (Now uses CCXT)

    /**
     * Fetches live market data for a given symbol using CCXT.
     * @async
     * @param {string} symbol - Trading pair symbol (e.g., 'BTC/USDT').
     * @returns {Promise<MarketData>}
     */
    async getMarketData(symbol) {
        try {
            // Fetch ticker, order book, and historical candles in parallel
            const [ticker, orderBook, ohlcv] = await Promise.all([
                this.exchange.fetchTicker(symbol),
                this.exchange.fetchOrderBook(symbol, 50), // 50 levels deep for liquidity calc
                this.exchange.fetchOHLCV(symbol, '1h', undefined, 24) // Last 24 hourly candles for volatility
            ]);

            if (!ticker || !orderBook || !ohlcv || ohlcv.length === 0) {
                throw new Error('Incomplete market data received from exchange.');
            }

            const liquidity = calculateLiquidity(orderBook); // Using a helper function
            const volatility = calculateVolatility(ohlcv.map(k => k[4])); // Pass close prices to helper

            return {
                price: ticker.last,
                volume24h: ticker.quoteVolume,
                liquidity: liquidity,
                spread: (ticker.ask - ticker.bid) / ticker.ask * 100,
                volatility: volatility,
                timestamp: new Date(ticker.timestamp).toISOString(),
            };
        } catch (error) {
            console.error(`❌ Could not fetch market data for ${symbol} from ${this.exchange.id}:`, error);
            throw new NodeOperationError(this.getNode(), `Market data fetch failed: ${error.message}`);
        }
    }

    /**
     * Retrieves the execution history.
     * @async
     * @param {string|null} symbol - Optional symbol to filter by.
     * @param {number} limit - Maximum number of records to return.
     * @returns {Promise<any[]>}
     */
    async getExecutionHistory(symbol = null, limit = 100) {
        if (!this.db) throw new Error('Database not initialized.');
        let query = 'SELECT * FROM trading_transactions';
        const params = [];
        if (symbol) {
            query += ' WHERE symbol = ?';
            params.push(symbol);
        }
        query += ' ORDER BY created_at DESC LIMIT ?';
        params.push(limit);

        return this.db.all(query, params);
    }

    // 💾 DATABASE OPERATIONS

    /**
     * Records a successful execution in the database.
     * @async
     * @param {ExecutionResult} result - The execution result.
     * @returns {Promise<number>}
     */
    async recordExecution(result) {
        if (!this.db) throw new Error('Database not initialized.');
        const query = `
            INSERT INTO trading_transactions (
                orderId, symbol, side, type, quantity, executedPrice, executedQuantity,
                status, timestamp, fees, slippage, strategy, exchange
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        const params = [
            result.orderId, result.symbol, result.side, result.type, result.quantity,
            result.executedPrice, result.executedQuantity, result.status, result.timestamp,
            result.fees, result.slippage, result.strategy, result.exchange
        ];
        const runResult = await this.db.run(query, params);
        return runResult.lastID;
    }

    /**
     * Records a failed execution attempt.
     * @async
     * @param {OrderParams} orderParams - The order parameters.
     * @param {string} errorMessage - The reason for failure.
     * @returns {Promise<number>}
     */
    async recordFailedExecution(orderParams, errorMessage) {
        if (!this.db) throw new Error('Database not initialized.');
        const query = `
            INSERT INTO trading_transactions (
                orderId, symbol, side, type, quantity, status, timestamp, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `;
        const params = [
            `failed_${crypto.randomBytes(4).toString('hex')}`,
            orderParams.symbol, orderParams.side, orderParams.type, orderParams.quantity,
            'failed', new Date().toISOString(), errorMessage
        ];
        const runResult = await this.db.run(query, params);
        return runResult.lastID;
    }

    // 🌐 API & SIMULATION (Live order execution is now implemented)

    /**
     * Creates an order using the CCXT exchange instance.
     * @private
     * @param {OrderParams} orderParams - The order parameters.
     * @returns {Promise<ccxt.Order>} The created order from CCXT.
     */
    async _createCcxtOrder(orderParams) {
        const { symbol, side, type, quantity, price, stopLoss, takeProfit } = orderParams;
        const ccxtParams = {};

        switch (type) {
        case 'market':
            return this.exchange.createMarketOrder(symbol, side, quantity);
        case 'limit':
            if (!price) throw new Error('Limit price is required for limit orders.');
            return this.exchange.createLimitOrder(symbol, side, quantity, price);
        case 'stop_loss':
            if (!stopLoss) throw new Error('Stop loss price is required.');
            ccxtParams.stopPrice = stopLoss;
            return this.exchange.createOrder(symbol, 'stop_loss', side, quantity, price, ccxtParams);
        case 'take_profit':
            if (!takeProfit) throw new Error('Take profit price is required.');
            ccxtParams.stopPrice = takeProfit;
            return this.exchange.createOrder(symbol, 'take_profit', side, quantity, price, ccxtParams);
        default:
            throw new Error(`Unsupported order type: ${type}`);
        }
    }

    /**
     * Executes a live order via the CCXT unified API.
     * @private
     * @param {OrderParams} orderParams - Order parameters.
     * @param {MarketData} marketData - Pre-fetched market data.
     * @returns {Promise<ExecutionResult>}
     */
    async _executeLiveOrder(orderParams, marketData) {
        for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
            try {
                const order = await this._createCcxtOrder(orderParams);

                // If the order was successful, map it to our internal format and return.
                return {
                    orderId: order.id,
                    symbol: order.symbol,
                    side: order.side,
                    type: order.type,
                    quantity: order.amount,
                    executedPrice: order.average || order.price,
                    executedQuantity: order.filled,
                    executedValue: order.cost,
                    status: order.status,
                    timestamp: new Date(order.timestamp).toISOString(),
                    fees: order.fee ? order.fee.cost : 0,
                    slippage: ((order.average - marketData.price) / marketData.price) * 100, // Real slippage
                    strategy: orderParams.strategy || 'standard',
                    exchange: this.exchange.id,
                    simulation: false,
                    raw: order,
                };
            } catch (error) {
                console.warn(`Attempt ${attempt} failed for ${orderParams.symbol} order: ${error.message}`);
                if (attempt === this.config.retryAttempts) {
                    throw error; // Rethrow the final error
                }
                await new Promise(res => setTimeout(res, this.config.retryDelay));
            }
        }
        // This line should be unreachable if retry logic is correct.
        throw new Error('Order execution failed after all retry attempts.');
    }

    /**
     * Simulates an order execution.
     * @private
     * @param {OrderParams} orderParams - Order parameters.
     * @param {MarketData} marketData - Current market data.
     * @returns {Promise<ExecutionResult>}
     */
    async _executeSimulatedOrder(orderParams, marketData) {
        const {symbol, side, quantity} = orderParams;
        const [base, quote] = symbol.split('/');

        const slippage = (Math.random() * this.config.maxSlippageBps) / 10000;
        const executedPrice = side === 'buy'
            ? marketData.price * (1 + slippage)
            : marketData.price * (1 - slippage);

        const executedValue = quantity * executedPrice;
        const fees = executedValue * CONSTANTS.FEES.DEFAULT_TRADING_FEE;

        // Update simulated balances with negative balance prevention
        if (side === 'buy') {
            const newQuoteBalance = (this.simulatedBalances[quote] || 0) - executedValue;
            const newBaseBalance = (this.simulatedBalances[base] || 0) + (quantity - (fees / executedPrice));
            this.simulatedBalances[quote] = Math.max(0, newQuoteBalance);
            this.simulatedBalances[base] = Math.max(0, newBaseBalance);
        } else { // sell
            const newBaseBalance = (this.simulatedBalances[base] || 0) - quantity;
            const newQuoteBalance = (this.simulatedBalances[quote] || 0) + (executedValue - fees);
            this.simulatedBalances[base] = Math.max(0, newBaseBalance);
            this.simulatedBalances[quote] = Math.max(0, newQuoteBalance);
        }

        return {
            orderId: `sim_${crypto.randomBytes(8).toString('hex')}`,
            symbol,
            side,
            type: orderParams.type,
            quantity,
            executedPrice,
            executedQuantity: quantity,
            executedValue,
            status: 'filled',
            timestamp: new Date().toISOString(),
            fees,
            slippage: slippage * 100,
            strategy: orderParams.strategy || 'standard',
            exchange: this.config.exchange,
            simulation: true,
        };
    }

    /**
     * Close database connection.
     * @returns {Promise<void>}
     */
    async close() {
        if (this.db) {
            await this.db.close();
            console.log('✅ Database connection closed.');
        }
    }
}

/**
/**
 * TradingExecutor class for advanced order management and execution.
 * @module TradingExecutor
 */
export default TradingExecutor;


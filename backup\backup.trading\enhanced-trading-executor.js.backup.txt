// Enhanced Trading Executor - Advanced order execution and portfolio management
const { EventEmitter } = require('events');
const path = require('path');
const DatabaseManager = require('./DatabaseHelper');

class EnhancedTradingExecutor extends EventEmitter {
    /**
     * Constructor for EnhancedTradingExecutor.
     *
     * @param {Object} [config={}] - Configuration object.
     * @param {string} [config.exchange='pionex'] - Exchange to use.
     * @param {string} [config.apiKey] - API key to use.
     * @param {string} [config.apiSecret] - API secret to use.
     * @param {number} [config.maxPositions=10] - Maximum number of positions to hold.
     * @param {number} [config.riskPerTrade=0.02] - Risk per trade (percentage).
     * @param {number} [config.stopLossPercent=0.05] - Stop loss percentage.
     * @param {number} [config.takeProfitPercent=0.15] - Take profit percentage.
     * @param {number} [config.maxSlippage=0.01] - Maximum slippage allowed (percentage).
     */
    constructor(config = {}) {
        super();

        this.config = {
            exchange: config.exchange || 'pionex',
            apiKey: config.apiKey || process.env.PIONEX_API_KEY,
            apiSecret: config.apiSecret || process.env.PIONEX_API_SECRET,
            maxPositions: config.maxPositions || 10,
            riskPerTrade: config.riskPerTrade || 0.02, // 2% risk per trade
            stopLossPercent: config.stopLossPercent || 0.05, // 5% stop loss
            takeProfitPercent: config.takeProfitPercent || 0.15, // 15% take profit
            maxSlippage: config.maxSlippage || 0.01, // 1% max slippage
            ...config
        };

        this.activePositions = new Map();
        this.orderHistory = [];
        this.executionEngine = null;
        this.isActive = false;
        this.portfolioValue = 0;
        this.dailyPnL = 0;
        this.db = DatabaseManager;
        this.dbPath = path.join(__dirname, '..', '..', 'databases', 'trading_bot.db');
    }

    /**
     * Initializes the Enhanced Trading Executor.
     *
     * This method initializes the database, execution engine,
     * loads active positions, and calculates the portfolio value.
     *
     * @returns {Promise<boolean>} - true if initialization is successful
     */
    async initialize() {
        try {
            console.log('🚀 Initializing Enhanced Trading Executor...');

            // Initialize database
            await this.initializeDatabase();

            // Initialize execution engine
            await this.initializeExecutionEngine();

            // Load active positions
            await this.loadActivePositions();

            // Calculate portfolio value
            await this.updatePortfolioValue();

            this.isActive = true;
            console.log('✅ Enhanced Trading Executor initialized successfully');

            this.emit('initialized');
            return true;
        } catch (error) {
            console.error('❌ Trading executor initialization failed:', error.message);
            throw new Error(error.message);
        }
    }

    /**
     * Initializes the database for the Enhanced Trading Executor.
     *
     * This method creates a SQLite database file at the specified path
     * and creates a table named 'enhanced_positions' with the following columns:
     * - id (Primary Key): AUTOINCREMENT integer
     * - symbol: TEXT (not null)
     * - entryPrice: REAL (not null)
     * - quantity: REAL (not null)
     * - entryTime: TEXT (not null)
     * - stopLoss: REAL
     * - takeProfit: REAL
     * - strategy: TEXT
     * - confidence: REAL
     * - currentPrice: REAL
     * - unrealizedPnL: REAL
     * - fees: REAL
     * - status: TEXT (default: 'active')
     * - created_at: DATETIME (default: CURRENT_TIMESTAMP)
     *
     * @returns {Promise<void>} - Resolves if initialization is successful
     * @throws {Error} - If database connection fails
     */
    async initializeDatabase() {
        try {
            await this.db.connect(this.dbPath);
            console.log('✅ Enhanced Trading Executor connected to database.');
            await this.db.run(`
                CREATE TABLE IF NOT EXISTS enhanced_positions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    entryPrice REAL NOT NULL,
                    quantity REAL NOT NULL,
                    entryTime TEXT NOT NULL,
                    stopLoss REAL,
                    takeProfit REAL,
                    strategy TEXT,
                    confidence REAL,
                    currentPrice REAL,
                    unrealizedPnL REAL,
                    fees REAL,
                    status TEXT DEFAULT 'active',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);
        } catch (err) {
            console.error('❌ Database connection failed:', err.message);
            throw err;
        }
    }

    /**
     * Adds a new position to the active positions map.
     * @param {Object} tradeParams - Trade parameters
     * @param {Object} executionResult - Execution result
     * @return {void}
     * @fires positionOpened
     * @private
     */
    async addPosition(tradeParams, executionResult) {
        const position = {
            symbol: tradeParams.symbol,
            entryPrice: executionResult.executedPrice,
            quantity: executionResult.executedQuantity,
            entryTime: new Date().toISOString(),
            stopLoss: tradeParams.stopLoss,
            takeProfit: tradeParams.takeProfit,
            strategy: tradeParams.strategy,
            confidence: tradeParams.confidence,
            currentPrice: executionResult.executedPrice,
            unrealizedPnL: 0,
            fees: executionResult.fees || 0
        };

        this.activePositions.set(tradeParams.symbol, position);
        await this.db.run(
            `INSERT INTO enhanced_positions (symbol, entryPrice, quantity, entryTime, stopLoss, takeProfit, strategy, confidence, currentPrice, unrealizedPnL, fees, status)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [position.symbol, position.entryPrice, position.quantity, position.entryTime, position.stopLoss, position.takeProfit, position.strategy, position.confidence, position.currentPrice, position.unrealizedPnL, position.fees, 'active']
        );
        console.log(`📈 Position opened: ${tradeParams.symbol} @ ${executionResult.executedPrice}`);

        this.emit('positionOpened', position);
    }

    /**
     * Closes an existing position.
     * @param {string} symbol - Trading pair symbol
     * @param {object} executionResult - The result from the trade execution.
     */
    async closePosition(symbol, executionResult) {
        const position = this.activePositions.get(symbol);
        if (!position) {
            console.log(`⚠️ No position found to close for ${symbol}`);
            return;
        }

        // Calculate realized PnL
        const realizedPnL = (executionResult.executedPrice - position.entryPrice) * position.quantity - position.fees;

        // Update daily PnL
        this.dailyPnL += realizedPnL;

        // Remove from active positions
        this.activePositions.delete(symbol);

        await this.db.run(
            'UPDATE enhanced_positions SET status = ?, exitPrice = ?, realizedPnL = ? WHERE symbol = ? AND status = ?',
            ['closed', executionResult.executedPrice, realizedPnL, symbol, 'active']
        );

        const closedPosition = {
            ...position,
            exitPrice: executionResult.executedPrice,
            exitTime: new Date().toISOString(),
            realizedPnL,
            totalFees: position.fees + (executionResult.fees || 0)
        };

        console.log(`📉 Position closed: ${symbol} @ ${executionResult.executedPrice} | PnL: ${realizedPnL.toFixed(2)}`);

        this.emit('positionClosed', closedPosition);
    }

    /**
     * Initializes the execution engine and establishes a connection to the exchange.
     * @return {Promise<void>} - Resolves when the connection is established.
     * @private
     */
    async initializeExecutionEngine() {
        // Mock implementation - replace with actual exchange API
        this.executionEngine = {
            connected: true,
            exchange: this.config.exchange,
            lastPing: new Date()
        };

        console.log(`📡 Connected to ${this.config.exchange} exchange`);
    }

    /**
 * Executes a trade based on the provided signal.
 * Validates the trade signal, calculates the position size, and executes the trade.
 * Emits events for successful execution or errors.
 *
 * @async
 * @param {Object} signal - The trade signal containing details such as symbol, action, price, stopLoss, takeProfit, and confidence.
 * @returns {Promise<Object|null>} - Returns the execution result if successful, null if validation fails.
 * @throws {Error} - Throws an error if trade execution fails.
 */

    /**
     * Executes a trade based on the provided signal.
     * Validates the trade signal, calculates the position size, and executes the trade.
     * Emits events for successful execution or errors.
     *
     * @async
     * @param {Object} signal - The trade signal containing details such as symbol, action, price, stopLoss, takeProfit, and confidence.
     * @returns {Promise<Object|null>} - Returns the execution result if successful, null if validation fails.
     * @throws {Error} - Throws an error if trade execution fails.
     */
    async executeTradeSignal(signal) {
        try {
            console.log(`📊 Processing trade signal: ${signal.symbol} ${signal.action}`);

            // Validate signal
            const validation = this.validateTradeSignal(signal);
            if (!validation.isValid) {
                console.log(`⚠️ Signal validation failed: ${validation.reason}`);
                return null;
            }

            // Calculate position size
            const positionSize = this.calculatePositionSize(signal);

            // Execute the trade
            const executionResult = await this.executeTrade({
                symbol: signal.symbol,
                action: signal.action,
                quantity: positionSize,
                price: signal.price,
                stopLoss: signal.stopLoss,
                takeProfit: signal.takeProfit,
                strategy: signal.strategy || 'signal',
                confidence: signal.confidence || 0.5
            });

            if (executionResult.success) {
                console.log(`✅ Trade executed: ${signal.symbol} ${signal.action} ${positionSize}`);
                this.emit('tradeExecuted', executionResult);
            } else {
                console.log(`❌ Trade execution failed: ${executionResult.error}`);
                this.emit('tradeError', executionResult);
            }

            return executionResult;

        } catch (error) {
            console.error('Error executing trade signal:', error.message);
            throw new Error(`Failed to execute trade signal: ${error.message}`);
        }
    }

    /**
     * Validate a trade signal, checking for required fields and potential conflicts
     * with existing positions.
     * @param {object} signal - The trade signal to validate
     * @returns {object} An object with an isValid property and a reason property if the
     * signal is invalid
     */
    validateTradeSignal(signal) {
        // Check required fields
        if (!signal.symbol || !signal.action || !signal.price) {
            return {isValid: false, reason: 'Missing required signal fields'};
        }

        // Check if we have too many positions
        if (this.activePositions.size >= this.config.maxPositions && signal.action === 'buy') {
            return {isValid: false, reason: 'Maximum positions reached'};
        }

        // Check if we already have a position in this symbol
        if (this.activePositions.has(signal.symbol) && signal.action === 'buy') {
            return {isValid: false, reason: 'Position already exists for this symbol'};
        }

        // Check confidence threshold
        if (signal.confidence && signal.confidence < 0.3) {
            return {isValid: false, reason: 'Signal confidence too low'};
        }

        return {isValid: true};
    }

    /**
     * Calculate the position size for a given trade signal based on risk management parameters.
     * This function determines the appropriate position size by considering the account balance,
     * risk per trade, stop loss distance, and maximum position size limits.
     *
     * @param {Object} signal - The trade signal containing details such as symbol, action, price, and stopLoss.
     * @returns {number} - The calculated position size, constrained by maximum position size limits.
     */
    calculatePositionSize(signal) {
        // Calculate position size based on risk management
        const accountBalance = this.portfolioValue || 10000; // Default balance
        const riskAmount = accountBalance * this.config.riskPerTrade;

        // Calculate stop loss distance
        const stopLossPrice = signal.stopLoss || (signal.price * (1 - this.config.stopLossPercent));
        const stopLossDistance = Math.abs(signal.price - stopLossPrice);

        // Calculate position size
        const positionSize = riskAmount / stopLossDistance;

        // Apply maximum position size limits
        const maxPositionValue = accountBalance * 0.1; // Max 10% per position
        const maxPositionSize = maxPositionValue / signal.price;

        return Math.min(positionSize, maxPositionSize);
    }


    /**
     * Execute a trade based on the provided parameters.
     * Simulates trade execution, updates active positions, and records the order in the order history.
     *
     * @async
     * @param {Object} tradeParams - The trade parameters, containing symbol, action, quantity, price, stopLoss, takeProfit, and strategy.
     * @returns {Promise<Object>} - Returns the execution result, which contains a success property, an error property if the trade failed, and a timestamp of the execution.
     */
    async executeTrade(tradeParams) {
        try {
            // Simulate trade execution
            const executionResult = await this.simulateTradeExecution(tradeParams);

            if (executionResult.success) {
                // Update active positions
                if (tradeParams.action === 'buy') {
                    this.addPosition(tradeParams, executionResult);
                } else if (tradeParams.action === 'sell') {
                    this.closePosition(tradeParams.symbol, executionResult);
                }

                // Record in order history
                this.orderHistory.push({
                    ...tradeParams,
                    executionResult,
                    timestamp: new Date().toISOString()
                });
            }

            return executionResult;

        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Simulates a trade execution with a mock exchange API call.
     * This function adds a random delay to the execution, simulates slippage,
     * and randomly determines if the trade is successful or not.
     * This function is used for testing and demonstration purposes only.
     * It should be replaced with actual exchange API calls for production use.
     * @param {Object} tradeParams - The trade parameters, containing symbol, action, quantity, price, stopLoss, takeProfit, and strategy.
     * @returns {Promise<Object>} - Returns the simulated execution result, which contains a success property, an error property if the trade failed, and a timestamp of the execution.
     */
    async simulateTradeExecution(tradeParams) {
        // Mock execution - replace with actual exchange API calls
        const executionDelay = Math.random() * 1000 + 500; // 0.5-1.5s delay
        await new Promise(resolve => setTimeout(resolve, executionDelay));

        // Simulate slippage
        const slippage = (Math.random() - 0.5) * this.config.maxSlippage * 2;
        const executedPrice = tradeParams.price * (1 + slippage);

        // Simulate execution success/failure
        const successRate = 0.95; // 95% success rate
        const isSuccessful = Math.random() < successRate;

        if (isSuccessful) {
            return {
                success: true,
                orderId: `ORDER_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                executedPrice,
                executedQuantity: tradeParams.quantity,
                slippage: Math.abs(slippage),
                fees: executedPrice * tradeParams.quantity * 0.001, // 0.1% fee
                timestamp: new Date().toISOString()
            };
        } else {
            return {
                success: false,
                error: 'Order execution failed - insufficient liquidity',
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Periodically updates the current market price and unrealized profit/loss for all active positions,
     * and checks for stop loss and take profit triggers.
     *
     * @async
     * @fires EnhancedTradingExecutor#positionsUpdated
     * @return {Promise<void>}
     */
    async updatePositions() {
        try {
            for (const [symbol, position] of this.activePositions) {
                // Get current market price (mock implementation)
                const currentPrice = await this.getCurrentPrice(symbol);

                // Update position
                position.currentPrice = currentPrice;
                position.unrealizedPnL = (currentPrice - position.entryPrice) * position.quantity - position.fees;

                // Check stop loss and take profit
                await this.checkPositionTriggers(symbol, position);
            }

            await this.updatePortfolioValue();
            this.emit('positionsUpdated');

        } catch (error) {
            console.error('Error updating positions:', error.message);
        }
    }

    /**
     * Returns the current market price for a given symbol.
     * This is a mock implementation, please replace with actual market data.
     *
     * @async
     * @param {string} _symbol - The symbol to get the current price for.
     * @returns {Promise<number>} The current market price.
     */
    async getCurrentPrice(_symbol) {
        // Mock implementation - replace with actual market data
        const basePrice = 100; // Mock base price
        const volatility = 0.02; // 2% volatility
        const randomChange = (Math.random() - 0.5) * volatility * 2;

        return basePrice * (1 + randomChange);
    }

    /**
     * Checks if a position's stop loss or take profit has been triggered and executes a trade if so.
     *
     * @async
     * @param {string} symbol - The symbol of the position to check.
     * @param {Object} position - The position object to check.
     * @returns {Promise<void>} Resolves when the checks are complete.
     */
    async checkPositionTriggers(symbol, position) {
        const currentPrice = position.currentPrice;

        // Check stop loss
        if (position.stopLoss && currentPrice <= position.stopLoss) {
            console.log(`🛑 Stop loss triggered for ${symbol} at ${currentPrice}`);
            await this.executeTrade({
                symbol,
                action: 'sell',
                quantity: position.quantity,
                price: currentPrice,
                strategy: 'stop_loss'
            });
        }

        // Check take profit
        if (position.takeProfit && currentPrice >= position.takeProfit) {
            console.log(`🎯 Take profit triggered for ${symbol} at ${currentPrice}`);
            await this.executeTrade({
                symbol,
                action: 'sell',
                quantity: position.quantity,
                price: currentPrice,
                strategy: 'take_profit'
            });
        }
    }

    /**
     * Updates the total portfolio value by calculating the sum of all active position values and adding a mock cash balance.
     * The value of each active position is determined by multiplying the current market price by the quantity held.
     * This method assigns the computed total to the `portfolioValue` property.
     *
     * @async
     * @returns {Promise<void>} Resolves when the portfolio value is updated.
     */

    async updatePortfolioValue() {
        let totalValue = 0;

        // Calculate value of active positions
        for (const position of this.activePositions.values()) {
            const positionValue = position.currentPrice * position.quantity;
            totalValue += positionValue;
        }

        // Add cash balance (mock)
        totalValue += 5000; // Mock cash balance

        this.portfolioValue = totalValue;
    }

    /**
     * Loads active positions from storage and populates the `activePositions` Map.
     * Uses the `enhanced_positions` table and filters by `status = 'active'`.
     * Each position is represented as an object with the following properties:
     * - symbol: string
     * - entryPrice: number
     * - quantity: number
     * - entryTime: Date
     * - stopLoss: number (optional)
     * - takeProfit: number (optional)
     * - strategy: string
     * - confidence: number
     * - currentPrice: number
     * - unrealizedPnL: number
     * - fees: number
     *
     * @async
     * @returns {Promise<void>} Resolves when active positions are loaded.
     * @throws {Error} If there is an error loading active positions.
     */
    async loadActivePositions() {
        try {
            console.log('📚 Loading active positions from storage...');
            if (!this.db) {
                throw new Error('Database not initialized');
            }

            const positions = await this.db.all(
                'SELECT * FROM enhanced_positions WHERE status = ?',
                ['active']
            );

            // Load positions into memory
            this.activePositions.clear();
            positions.forEach(pos => {
                this.activePositions.set(pos.symbol, {
                    symbol: pos.symbol,
                    entryPrice: pos.entryPrice,
                    quantity: pos.quantity,
                    entryTime: pos.entryTime,
                    stopLoss: pos.stopLoss,
                    takeProfit: pos.takeProfit,
                    strategy: pos.strategy,
                    confidence: pos.confidence,
                    currentPrice: pos.currentPrice,
                    unrealizedPnL: pos.unrealizedPnL,
                    fees: pos.fees
                });
            });

            console.log(`✅ Loaded ${positions.length} active positions`);
        } catch (error) {
            console.error('Error loading active positions:', error.message);
            throw new Error(error.message);
        }
    }

    /**
     * Retrieves a summary of the current portfolio, including the total value of all
     * active positions, the number of active positions, the daily profit and loss, and
     * the total unrealized profit and loss.
     *
     * @returns {Object} A portfolio summary object with the following properties:
     * - portfolioValue: number
     * - activePositions: number
     * - dailyPnL: number
     * - totalUnrealizedPnL: number
     * - positions: Array<Object>
     *     - symbol: string
     *     - quantity: number
     *     - entryPrice: number
     *     - currentPrice: number
     *     - unrealizedPnL: number
     *     - strategy: string
     */
    getPortfolioSummary() {
        const positions = Array.from(this.activePositions.values());
        const totalUnrealizedPnL = positions.reduce((sum, pos) => sum + pos.unrealizedPnL, 0);

        return {
            portfolioValue: this.portfolioValue,
            activePositions: positions.length,
            dailyPnL: this.dailyPnL,
            totalUnrealizedPnL,
            positions: positions.map(pos => ({
                symbol: pos.symbol,
                quantity: pos.quantity,
                entryPrice: pos.entryPrice,
                currentPrice: pos.currentPrice,
                unrealizedPnL: pos.unrealizedPnL,
                strategy: pos.strategy
            }))
        };
    }

    /**
     * Retrieves the most recent trades, up to a specified limit.
     * @param {number} [limit=50] - The maximum number of trades to retrieve.
     * @returns {Array<Object>} An array of trade objects, sorted by timestamp in descending order.
     */
    getTradeHistory(limit = 50) {
        return this.orderHistory
            .slice(-limit)
            .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    }

    /**
     * Retrieves key performance metrics of the trading engine.
     * @returns {Object} A performance metrics object with the following properties:
     *     - totalTrades: number
     *     - successfulTrades: number
     *     - winRate: number
     *     - dailyPnL: number
     *     - portfolioValue: number
     *     - activePositions: number
     *     - isActive: boolean
     */
    getPerformanceMetrics() {
        const totalTrades = this.orderHistory.length;
        const successfulTrades = this.orderHistory.filter(trade => trade.executionResult.success).length;
        const winRate = totalTrades > 0 ? (successfulTrades / totalTrades) * 100 : 0;

        return {
            totalTrades,
            successfulTrades,
            winRate,
            dailyPnL: this.dailyPnL,
            portfolioValue: this.portfolioValue,
            activePositions: this.activePositions.size,
            isActive: this.isActive
        };
    }

    /**
     * Checks the health of the trading engine, returning an object with the following properties:
     *     - isHealthy: boolean - Whether the engine is currently active and connected to the exchange.
     *     - activePositions: number - The number of active positions held by the engine.
     *     - portfolioValue: number - The current value of the trading portfolio.
     *     - executionEngine: boolean - Whether the execution engine is connected to the exchange.
     *     - lastUpdate: string - The timestamp of the last update, in ISO format.
     */
    isHealthy() {
        return {
            isHealthy: this.isActive && this.executionEngine?.connected,
            activePositions: this.activePositions.size,
            portfolioValue: this.portfolioValue,
            executionEngine: this.executionEngine?.connected || false,
            lastUpdate: new Date().toISOString()
        };
    }

    /**
     * Gracefully shuts down the Enhanced Trading Executor.
     *
     * This function attempts to close all active trading positions by executing
     * a 'sell' trade for each position held. Once all positions are closed, it
     * deactivates the trading executor and emits a 'shutdown' event to signal
     * that the shutdown process is complete. If an error occurs during the
     * shutdown process, it logs the error message to the console.
     */
    async shutdown() {
        try {
            console.log('🛑 Shutting down Enhanced Trading Executor...');

            // Close all active positions
            for (const symbol of this.activePositions.keys()) {
                const position = this.activePositions.get(symbol);
                await this.executeTrade({
                    symbol,
                    action: 'sell',
                    quantity: position.quantity,
                    price: position.currentPrice,
                    strategy: 'shutdown'
                });
            }

            this.isActive = false;
            this.emit('shutdown');

            console.log('✅ Trading executor shutdown complete');
        } catch (error) {
            console.error('Error during shutdown:', error.message);
        }
    }
}

module.exports = EnhancedTradingExecutor;

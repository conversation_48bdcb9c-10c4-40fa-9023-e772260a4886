(node:33580) [MODULE_TYPELESS_PACKAGE_JSON] Warning: Module type of file:///C:/Users/<USER>/Documents/electronTrader/eslint.config.js?mtime=1753491657933 is not specified and it doesn't parse as CommonJS.
Reparsing as ES module because module syntax was detected. This incurs a performance overhead.
To eliminate this warning, add "type": "module" to C:\Users\<USER>\Documents\electronTrader\package.json.
(Use `node --trace-warnings ...` to show where the warning was created)

C:\Users\<USER>\Documents\electronTrader\app\__tests__\e2e\complete-application-workflow.test.js
  73:13  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\__tests__\e2e\electron-main-process-workflow.test.js
  79:9  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\__tests__\e2e\run-comprehensive-e2e.js
  17:23  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\__tests__\e2e\run-e2e-tests.js
  17:19  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\__tests__\e2e\system-integration.test.js
  49:13  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\__tests__\helpers\DirectIPCTester.js
  34:36  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\__tests__\helpers\MockClasses.js
  68:35  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-ipc-end-to-end.js
  32:22  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-ipc-integration.js
  67:13  error  Parsing error: Unexpected token }

C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-ipc-standardized-error-handling.js
  118:28  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-ipc-validation.js
  23:34  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-ipc.js
  64:19  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-integration.js
  26:28  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\build\electron.js
  20:1  error  Parsing error: Unexpected keyword 'import'

C:\Users\<USER>\Documents\electronTrader\app\build\static\js\main.fce46078.js
  239:964500  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\coverage\lcov-report\prettify.js
  2:3427  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\coverage\lcov-report\sorter.js
  66:21  error  Parsing error: Unexpected token 'data-col'

C:\Users\<USER>\Documents\electronTrader\app\postcss.config.js
  3:5  error  Parsing error: Unexpected token require

C:\Users\<USER>\Documents\electronTrader\app\public\electron.js
  20:1  error  Parsing error: Unexpected keyword 'import'

C:\Users\<USER>\Documents\electronTrader\app\scripts\build-production.js
  150:5  error  Parsing error: Unexpected token try

C:\Users\<USER>\Documents\electronTrader\app\scripts\convert-to-cjs.js
  11:13  error  Parsing error: Unexpected token de

C:\Users\<USER>\Documents\electronTrader\app\scripts\fix-console-statements.js
  13:1  error  Parsing error: Unexpected token =>

C:\Users\<USER>\Documents\electronTrader\app\scripts\fix-final-lint-issues.js
  35:57  error  Parsing error: Unterminated string constant

C:\Users\<USER>\Documents\electronTrader\app\scripts\optimize-build.js
  20:5  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\scripts\performance-test.js
  17:5  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\scripts\run-ipc-end-to-end-tests.js
  18:1  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-clean.js
  39:9  error  Parsing error: Unexpected token const

C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js
  94:13  error  Parsing error: Unexpected token }

C:\Users\<USER>\Documents\electronTrader\app\scripts\validate-application-integration.js
  173:9  error  Parsing error: 'return' outside of function

C:\Users\<USER>\Documents\electronTrader\app\scripts\validate-production-build.js
  53:13  error  Parsing error: 'return' outside of function

C:\Users\<USER>\Documents\electronTrader\app\scripts\verify-references.js
  17:14  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\src\App.js
  42:64  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\components\ApplicationErrorBoundary.test.jsx
  26:12  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\components\Dashboard.test.jsx
  25:5  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\components\UltimateDashboard.test.jsx
  9:16  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\complete-application-workflow.test.js
  88:17  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\complete-end-to-end-validation.test.js
  128:29  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\complete-user-workflow.test.js
  140:13  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\error-handling-workflow.test.js
  38:12  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\ipc-load-test.js
  13:17  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\real-application-integration.test.js
  407:17  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\run-e2e-tests.js
  439:13  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\run-validation-suite.js
  18:20  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\simple-workflow.test.js
  22:22  error  Parsing error: Unexpected token 'BTC/USDT'

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\startup-sequence-validation.test.js
  15:13  error  Parsing error: Unexpected token EventEmitter

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\error-handling\basic-error-handling.test.js
  9:5  error  Parsing error: Unexpected token }

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\error-handling\comprehensive-error-handling.test.js
  52:12  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\integration\error-boundary-integration.test.js
  20:9  error  Parsing error: Unexpected keyword 'default'

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\integration\error-reporting-backend.test.js
  20:9  error  Parsing error: Unexpected keyword 'default'

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\integration\real-time-status-integration.test.js
  21:40  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\integration\specialized-error-boundaries.test.js
  20:40  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\integration\start-button-integration.test.js
  20:12  error  Parsing error: Unexpected token ject

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\integration\start-button-real-time-integration.test.js
  18:40  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\integration\start-button-workflow-integration.test.js
  12:17  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\comprehensive-ipc-test.js
  14:15  error  Parsing error: Unexpected token .

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-communication-test.js
  26:22  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-communication-validation.test.js
  13:23  error  Parsing error: Unexpected token .

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-end-to-end-comprehensive.test.js
  11:15  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-end-to-end-test.js
  29:5  error  Parsing error: Unexpected token ]

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-end-to-end-validation.test.js
  10:15  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-end-to-end.test.js
  10:15  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-enhanced-communication-test.js
  162:28  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-integration-test.js
  120:43  error  Parsing error: Unexpected token *

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-protocol-validation.js
  30:12  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-service-integration.test.js
  15:22  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-test-runner.js
  32:13  error  Parsing error: Missing catch or finally clause

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-validation.test.js
  10:15  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\standardized-error-handling.test.js
  17:22  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\standardized-ipc-error-handling.test.js
  14:11  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\jest-dom.d.ts
  6:9  error  Parsing error: Unexpected token module

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\jest-globals.d.ts
  6:9  error  Parsing error: Unexpected token global

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\jest.d.ts
  7:9  error  Parsing error: Unexpected token global

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\manual\validate-start-button-workflow.js
  24:21  error  Parsing error: Unexpected token turn

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\services\realTimeStatusService.test.js
  9:24  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\setupTests.js
  40:13  error  Parsing error: Unexpected token (

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\utils\ElectronAPITester.test.js
  60:23  error  Parsing error: Unexpected token .

C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\utils\PerformanceMonitor.test.js
  46:73  error  Parsing error: Unexpected token }

C:\Users\<USER>\Documents\electronTrader\app\src\api\backend.js
  62:14  error  Parsing error: Identifier 'getBotStatus' has already been declared

C:\Users\<USER>\Documents\electronTrader\app\src\api\ipc-test.js
  146:1  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\src\api\server.js
  19:12  error  Parsing error: Unexpected token ject

C:\Users\<USER>\Documents\electronTrader\app\src\api\trading.js
  19:12  error  Parsing error: Unexpected token ject

C:\Users\<USER>\Documents\electronTrader\app\src\auth\AuthContext.js
  79:12  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\AdvancedChart.jsx
  72:13  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\ApplicationErrorBoundary.jsx
  733:17  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\BotCard.jsx
  110:24  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\BotDashboard.jsx
  100:13  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\ConnectionStatus.jsx
  45:27  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\CriticalWorkflowErrorBoundary.jsx
  673:17  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\DataCollector.jsx
  66:13  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\DexScreenerChart.jsx
  15:13  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\DrawdownAnalyzer.jsx
  77:13  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\EnhancedErrorBoundary.jsx
  119:21  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\EnhancedStartButton.jsx
  208:32  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\ErrorBoundary.jsx
  128:13  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\ErrorBoundaryTest.jsx
  15:9  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\FallbackComponents.jsx
  7:5  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\FuturisticButton.jsx
  148:9  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\HolographicCard.jsx
  136:9  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\IPCExample.jsx
  138:9  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\IPCExampleClean.jsx
  138:9  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\LazyComponents.js
  24:7  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\LazyComponents.jsx
  91:5  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\LiveSystemMonitor.jsx
  115:32  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\Login.js
  43:5  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\MemeCoinScanner.jsx
  69:24  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\OrderForm.js
  11:55  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\src\components\ParticleBackground.jsx
  128:9  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\PortfolioSummary.js
  9:5  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\PositionManager.js
  5:9  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\RiskManager.jsx
  85:24  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\RunningOperationsMonitor.jsx
  110:24  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\SentimentAnalyzer.jsx
  73:24  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\SettingsModal.jsx
  48:9  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\Sidebar.jsx
  23:44  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\StartButton.jsx
  143:32  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\SystemHealthIndicator.jsx
  104:9  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\SystemStatusPanel.jsx
  186:13  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\TradeHistory.js
  5:9  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\TradingDashboard.jsx
  66:13  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\TradingStatusIndicator.jsx
  93:24  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\UltimateDashboard.jsx
  157:5  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\VibrantButton.jsx
  85:9  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\WhaleTracker.jsx
  33:5  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\__tests__\AutonomousDashboard-IPC.test.jsx
  49:16  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\__tests__\RealTimeStatusIntegration.test.js
  8:19  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\src\components\__tests__\StartButton-IPC-Integration.test.jsx
  44:9  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\__tests__\StartButton.test.jsx
  34:16  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\components\__tests__\StartButtonIntegration.test.jsx
  39:17  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\config\environment.js
  37:29  error  Parsing error: Unexpected token ||

C:\Users\<USER>\Documents\electronTrader\app\src\dev\palette.js
  13:5  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\dev\palette.jsx
  14:5  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\dev\previews.jsx
  25:9  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\index.jsx
  15:5  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\src\services\startupService.js
  10:9  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\src\types\electron-api.d.ts
  6:8  error  Parsing error: Unexpected token interface

C:\Users\<USER>\Documents\electronTrader\app\src\types\electron.d.ts
  6:9  error  Parsing error: Unexpected token global

C:\Users\<USER>\Documents\electronTrader\app\src\utils\AnimationOptimizer.js
  8:23  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\src\utils\BuildOptimizationMonitor.js
  27:21  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\src\utils\ComponentRecoveryManager.js
  42:9  error  Parsing error: Unexpected token ]

C:\Users\<USER>\Documents\electronTrader\app\src\utils\ElectronAPITester.js
  106:28  error  Parsing error: Unexpected token ?

C:\Users\<USER>\Documents\electronTrader\app\src\utils\GlobalErrorHandler.js
  116:10  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\src\utils\IPCErrorHandler.js
  52:13  error  Parsing error: Unexpected token default

C:\Users\<USER>\Documents\electronTrader\app\src\utils\IPCServiceTester.js
  39:49  error  Parsing error: Unexpected token }

C:\Users\<USER>\Documents\electronTrader\app\src\utils\PerformanceMonitor.js
  51:10  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\src\utils\StandardizedIPCHandler.js
  49:24  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\src\utils\StartupOptimizer.js
  34:31  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\src\utils\SystemWideErrorHandler.js
  26:27  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\src\utils\formatters.js
  38:16  error  Parsing error: Unexpected token <

C:\Users\<USER>\Documents\electronTrader\app\start-production.js
  24:25  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\TradingOrchestrator.js
  15:29  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\comprehensive-error-handling.test.js
  170:32  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\e2e\application-workflow.test.js
  22:11  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\enhanced-error-handling-recovery.test.js
  128:27  error  Parsing error: Unexpected token Error

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\error-handling.test.js
  54:17  error  Parsing error: Unexpected token }

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\database-integration.test.js
  32:28  error  Parsing error: Unexpected token .

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\database-trading-integration.test.js
  100:5  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\error-handling-integration.test.js
  21:9  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\event-coordinator-integration.test.js
  32:34  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\run-start-button-tests.js
  15:25  error  Parsing error: Unexpected token 'start-button-workflow.test.js'

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\trading-system-error-handling.test.js
  22:9  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\test-backend.js
  41:13  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\test-data-persistence.js
  137:27  error  Parsing error: Unexpected token 'volume_spike'

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\test-refactored-structure.js
  39:9  error  Parsing error: 'return' outside of function

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\test-simple-startup.js
  26:20  error  Parsing error: Unexpected token 'BTC/USDT'

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\test-startup-workflow.js
  24:29  error  Parsing error: Unexpected token 'BTC/USDT'

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\database-operations.test.js
  21:72  error  Parsing error: Unexpected token name

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\event-coordinator.test.js
  30:34  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\ipc-communication.test.js
  10:13  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js
  12:25  error  Parsing error: Unexpected token Set

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-ipc-communication.test.js
  12:22  error  Parsing error: Unexpected token Set

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-trading-orchestrator.test.js
  18:24  error  Parsing error: Unexpected token Map

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\test-runner.js
  28:9  error  Parsing error: Unexpected token return

C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\trading-orchestrator.test.js
  10:24  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\ai\AutonomousTrader.js
  76:1  error  Parsing error: Unexpected token .

C:\Users\<USER>\Documents\electronTrader\app\trading\ai\AutonomousTrader.test.js
  25:22  error  Parsing error: Unexpected token 'binance'

C:\Users\<USER>\Documents\electronTrader\app\trading\ai\CryptoDiscoveryEngine.js
  35:14  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\ai\StrategyOptimizer.js
  19:12  error  Parsing error: Unexpected token ject

C:\Users\<USER>\Documents\electronTrader\app\trading\ai\cli.js
  43:52  error  Parsing error: Unexpected keyword 'default'

C:\Users\<USER>\Documents\electronTrader\app\trading\ai\llm-coordinator.js
  25:11  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\analysis\MemeCoinAnalyzer.js
  16:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\analysis\PerformanceTracker.js
  93:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\analysis\PerformanceTracker.old.js
  25:12  error  Parsing error: Unexpected token ject

C:\Users\<USER>\Documents\electronTrader\app\trading\analysis\RuleBasedSentiment.js
  23:12  error  Parsing error: Unexpected token ject

C:\Users\<USER>\Documents\electronTrader\app\trading\analysis\SentimentAnalyzer.js
  16:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\api\health-endpoints.js
  16:1  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\automatic-failure-recovery.js
  10:4  error  Parsing error: Unterminated regular expression

C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js
  88:9  error  Parsing error: Unexpected token ]

C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-trader-service.js
  19:12  error  Parsing error: Unexpected token ject

C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-trader.js
  41:25  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\components\AlertManager.js
  31:24  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\components\ArbitrageEngine.js
  18:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\components\ComponentBundle.js
  14:23  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\components\DrawdownAnalyzer.js
  14:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\components\ExchangeHealthMonitor.js
  124:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\components\GridBotManager.js
  27:26  error  Parsing error: Unexpected token ||

C:\Users\<USER>\Documents\electronTrader\app\trading\components\OpportunityScanner.js
  134:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\components\RiskManager.js
  19:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\components\StatusReporter.js
  17:9  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\components\SystemInfoManager.js
  17:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\config\ConfigurationManager.js
  32:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\config\config-cli.js
  276:22  error  Parsing error: Unexpected token ||

C:\Users\<USER>\Documents\electronTrader\app\trading\config\config-test-utils.js
  89:26  error  Parsing error: Unexpected token 'binance'

C:\Users\<USER>\Documents\electronTrader\app\trading\config\database-config.js
  19:12  error  Parsing error: Unexpected token ject

C:\Users\<USER>\Documents\electronTrader\app\trading\config\enhanced-config-manager.js
  18:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\config\error-handling.config.js
  91:15  error  Parsing error: Unexpected token 'error'

C:\Users\<USER>\Documents\electronTrader\app\trading\config\exchanges.config.js
  12:18  error  Parsing error: Unexpected token !==

C:\Users\<USER>\Documents\electronTrader\app\trading\config\index.js
  15:9  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\config\migrations\config-migrator.js
  137:17  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\config\startup-config-loader.js
  29:5  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\config\test-integration.js
  28:24  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\trading\config\test-startup-config-loader.js
  32:28  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\trading\data\DatabaseManager.js
  19:12  error  Parsing error: Unexpected token ject

C:\Users\<USER>\Documents\electronTrader\app\trading\data\UnifiedDatabaseInitializer.js
  29:5  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\data\UnifiedDatabaseManager.js
  339:30  error  Parsing error: Unexpected token ===

C:\Users\<USER>\Documents\electronTrader\app\trading\data\databases\DatabaseIntegration.js
  53:8  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\data\databases\diagnose_db_schema.js
  10:2  error  Parsing error: Unexpected character '!'

C:\Users\<USER>\Documents\electronTrader\app\trading\data\databases\granular_sql_debug.js
  10:2  error  Parsing error: Unexpected character '!'

C:\Users\<USER>\Documents\electronTrader\app\trading\data\databases\initialize_missing_tables.js
  121:19  error  Parsing error: Unexpected token ||

C:\Users\<USER>\Documents\electronTrader\app\trading\data\databases\unified-database-init.js
  24:1  error  Parsing error: Unexpected token .

C:\Users\<USER>\Documents\electronTrader\app\trading\data\databases\verify_database_setup.js
  29:24  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\trading\data\transaction-manager.js
  23:21  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\database\DatabaseManager.js
  90:10  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\database\migrations\001-enhanced-features.js
  326:23  error  Parsing error: Unexpected token ===

C:\Users\<USER>\Documents\electronTrader\app\trading\dependencies.js
  57:72  error  Parsing error: Unexpected token }

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\BlockchainTransactionAnalyzer.js
  50:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\ComprehensiveWalletTracker.js
  38:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\EntryTimingEngine.js
  31:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\ExitLiquidityProtector.js
  46:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\HistoricalPriceTracker.js
  44:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\MemeCoinPatternAnalyzer.js
  33:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\NewCoinDecisionEngine.js
  17:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\PumpDetectionEngine.js
  38:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\SmartMoneyDetector.js
  45:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\SocialSentimentAnalyzer.js
  41:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\backtesting\BacktestingEngine.js
  22:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\backtesting\BacktestingIntegrator.js
  22:5  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\backtesting\HistoricalDataProvider.js
  27:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\backtesting\strategies\NewCoinStrategyAdapter.js
  15:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\ccxt\engines\CCXT-Exchange-Manager.js
  52:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\ccxt\tests\CCXT-Connector.test.js
  11:18  error  Parsing error: Unexpected token .

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\config\ConfigurationManager.js
  24:13  error  Parsing error: Unexpected token ]

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\config\configuration-loader.js
  19:12  error  Parsing error: Unexpected token ject

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\context\ContextEngine.js
  22:12  error  Parsing error: Unexpected token ject

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\data-collection\DataCollector.js
  148:28  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\data-collection\HistoricalPriceTracker.js
  46:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\data-collection\NewListingDetector.js
  33:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\data-collection\backtesting.js
  28:25  error  Parsing error: Unexpected token ||

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\exchange\ConnectionPool.js
  17:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\exchange\ProductionExchangeConnector.js
  47:1  error  Parsing error: Unexpected token .

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\helpers\logger.js
  18:11  error  Parsing error: Unexpected token ||

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\integration\event-bus.js
  8:8  error  Parsing error: Unexpected keyword 'default'

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\integration\webhook-proxy.js
  29:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\ipc\IPCProtocol.js
  119:27  error  Parsing error: Unexpected token Date

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\logging\AuditLogger.js
  44:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\monitoring\AnalyticsDashboard.js
  14:9  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\monitoring\PerformanceMonitor.js
  19:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\monitoring\TradingPerformanceMonitor.js
  12:11  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\monitoring\performance-monitor.js
  35:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\optimization\performance-monitor.js
  41:47  error  Parsing error: Unexpected token }

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\protection\ExitLiquidityProtector.js
  45:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\APIResourcePoolManager.js
  16:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\OptimizedHTTPClient.js
  20:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\TradingAPIIntegrator.js
  19:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\CredentialManager.js
  40:55  error  Parsing error: Unexpected token }

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\SecureCredentialManager.js
  51:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\EnhancedErrorHandler.js
  17:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js
  51:1  error  Parsing error: Unexpected token .

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\TradingSystemErrorHandler.js
  37:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\index.js
  19:12  error  Parsing error: Unexpected token ject

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\health-monitor.js
  30:5  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\recovery\BackupManager.js
  56:62  error  Parsing error: Unexpected token }

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\recovery\EnhancedRecoveryManager.js
  16:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\recovery\PositionRecoveryManager.js
  19:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\recovery\RecoveryManager.js
  42:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\risk\LiquidationProtector.js
  52:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\risk\PositionSizingManager.js
  33:47  error  Parsing error: Unexpected token }

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\risk\UnifiedRiskManager.js
  61:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\risk\weights.js
  10:18  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\safety\CircuitBreakerSystem.js
  28:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\safety\circuit-breakers.js
  16:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\secure-credential-manager.js
  98:9  error  Parsing error: Unexpected token INTEGER

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\utils\ErrorBoundary.js
  29:1  error  Parsing error: Unexpected token .

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\utils\ErrorHandlingUtils.js
  9:8  error  Parsing error: Unexpected token r

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\utils\ValidationUtils.js
  132:32  error  Parsing error: Unexpected token .

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\validation\InputValidator.js
  168:9  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\validation\ValidationTest.js
  472:24  error  Parsing error: Unexpected token +

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\AutoPositionSizer.js
  31:5  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\AutoProfitStopManager.js
  47:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\FuturesGridBot.js
  11:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\FuturesGridManager.js
  23:9  error  Parsing error: Unexpected token default

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\MemeCoinScanner.js
  21:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\ProductionTradingExecutor.js
  53:1  error  Parsing error: Unexpected token !

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\TradingExecutor.js
  58:19  error  Parsing error: Invalid number

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\ValidateTradingEngines.js
  30:13  error  Parsing error: Unexpected token turn

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\bots\FuturesGridBot.js
  303:125  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\bots\GridBotManager.js
  26:5  error  Parsing error: 'return' outside of function

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\bots\UnifiedGridBotEngine.js
  62:1  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\n8n-compatibility-test.js
  47:24  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js
  123:17  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\component-initializer.js
  24:21  error  Parsing error: Unexpected token 'core'

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\enhanced-component-initializer.js
  37:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\event-coordinator.js
  38:38  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\whaletrader\Elite.WhaleTracker.js
  19:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\whaletrader\EnhancedEliteWhaleTracker.js
  38:6  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\whaletrader\WhaleSignalEngine.js
  67:61  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\whaletrader\test-whale-engine.js
  35:16  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\engines\validation\CoinAgeValidator.js
  43:30  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\examples\ccxt-decimal-example.js
  76:20  error  Parsing error: Unexpected token 8

C:\Users\<USER>\Documents\electronTrader\app\trading\examples\precision-trading-example.js
  40:1  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\helpers\StartupInitializer.js
  18:1  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\ib\IBConnector.js
  21:10  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\ib\IBDataStreamer.js
  32:17  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\launchTrading.js
  13:27  error  Parsing error: Unexpected token ===

C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\ErrorReporter.js
  20:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\HealthMonitor.js
  22:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\enhanced-health-monitor.js
  17:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\health-check.js
  26:1  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\health-cli.js
  80:21  error  Parsing error: Unexpected token se

C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\health-dashboard.js
  37:1  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\health-monitor.js
  17:15  error  Parsing error: Unexpected token 'connection'

C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\health-monitoring-integration.js
  26:1  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\health-monitoring-system.js
  19:12  error  Parsing error: Unexpected token ject

C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\metrics-server.js
  19:12  error  Parsing error: Unexpected token ject

C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\status-reporter.js
  41:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\scripts\performance-optimizer.js
  31:1  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\trading\shared\helpers\database-manager.js
  19:12  error  Parsing error: Unexpected token ject

C:\Users\<USER>\Documents\electronTrader\app\trading\start-autonomous-trading.js
  113:34  error  Parsing error: Unexpected token 'databases'

C:\Users\<USER>\Documents\electronTrader\app\trading\startup.js
  18:5  error  Parsing error: Unexpected token ..

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\backtesting-system.test.js
  43:9  error  Parsing error: 'return' outside of function

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\comprehensive-system-validation.test.js
  18:5  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\integration\trading-system.test.js
  27:25  error  Parsing error: Unexpected token '../fixtures/test_trading.db'

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\run-tests.js
  18:5  error  Parsing error: Unexpected token ]

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\simple-system-validation.js
  39:9  error  Parsing error: 'return' outside of function

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-autonomous-components.js
  52:43  error  Parsing error: Unexpected token .

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-backend.js
  41:13  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-data-persistence.js
  137:27  error  Parsing error: Unexpected token 'volume_spike'

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-database-connections.js
  86:9  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-database-initialization.js
  121:9  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-database-integration.js
  23:5  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-imports.js
  45:41  error  Parsing error: Unexpected token .

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-mysql-connection.js
  22:28  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-refactored-structure.js
  33:1  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-simple-startup.js
  26:20  error  Parsing error: Unexpected token 'BTC/USDT'

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-startup-workflow.js
  24:29  error  Parsing error: Unexpected token 'BTC/USDT'

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-trading-system.js
  33:1  error  Parsing error: Unexpected token )

C:\Users\<USER>\Documents\electronTrader\app\trading\tests\unit\futures-grid-bot.test.js
  7:15  error  Parsing error: Unexpected token ,

C:\Users\<USER>\Documents\electronTrader\app\types\ipc-types.d.ts
  9:8  error  Parsing error: Unexpected token type

C:\Users\<USER>\Documents\electronTrader\app\utils\StandardizedIPCHandler.js
  16:28  error  Parsing error: Cannot use keyword 'await' outside an async function

C:\Users\<USER>\Documents\electronTrader\app\utils\logger.js
  32:19  error  Parsing error: Unexpected token :

C:\Users\<USER>\Documents\electronTrader\scripts\comprehensive-eslint-fix.js
  147:9  warning  Unused eslint-disable directive (no problems were reported from 'no-console')

C:\Users\<USER>\Documents\electronTrader\scripts\fix-eslint-errors.js
  123:33  error  Parsing error: Expecting Unicode escape sequence \uXXXX

Ô£û 351 problems (350 errors, 1 warning)
  0 errors and 1 warning potentially fixable with the `--fix` option.


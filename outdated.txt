Package                           Current   Wanted   Latest  Location                                           Depended by
@electron/rebuild                   3.7.2    3.7.2    4.0.1  app/node_modules/@electron/rebuild                 app@npm:meme-coin-trader-app@1.0.0
@jest/globals                      29.7.0   29.7.0   30.0.5  node_modules/@jest/globals                         app@npm:meme-coin-trader-app@1.0.0
@mui/icons-material                 6.5.0    6.5.0    7.2.0  node_modules/@mui/icons-material                   app@npm:meme-coin-trader-app@1.0.0
@mui/icons-material                 6.5.0    6.5.0    7.2.0  node_modules/@mui/icons-material                   trading@npm:autonomous-trader@1.0.0
@mui/material                       6.5.0    6.5.0    7.2.0  node_modules/@mui/material                         app@npm:meme-coin-trader-app@1.0.0
@mui/material                       6.5.0    6.5.0    7.2.0  node_modules/@mui/material                         trading@npm:autonomous-trader@1.0.0
@types/jest                       29.5.14  29.5.14   30.0.0  node_modules/@types/jest                           app@npm:meme-coin-trader-app@1.0.0
@types/node                       20.19.8  20.19.9   24.1.0  node_modules/@types/node                           app@npm:meme-coin-trader-app@1.0.0
@types/react                      18.3.23  18.3.23   19.1.8  node_modules/@types/react                          app@npm:meme-coin-trader-app@1.0.0
@types/react                      18.3.23  18.3.23   19.1.8  node_modules/@types/react                          trading@npm:autonomous-trader@1.0.0
@types/react-dom                   18.3.7   18.3.7   19.1.6  node_modules/@types/react-dom                      app@npm:meme-coin-trader-app@1.0.0
@types/react-dom                   18.3.7   18.3.7   19.1.6  node_modules/@types/react-dom                      trading@npm:autonomous-trader@1.0.0
@types/recharts                     2.0.1    2.0.1   1.8.29  node_modules/@types/recharts                       app@npm:meme-coin-trader-app@1.0.0
@typescript-eslint/eslint-plugin   8.31.1   8.38.0   8.38.0  app/node_modules/@typescript-eslint/eslint-plugin  app@npm:meme-coin-trader-app@1.0.0
babel-loader                        9.2.1    9.2.1   10.0.0  node_modules/babel-loader                          app@npm:meme-coin-trader-app@1.0.0
ccxt                               4.4.95   4.4.96   4.4.96  node_modules/ccxt                                  app@npm:meme-coin-trader-app@1.0.0
ccxt                               4.4.95   4.4.96   4.4.96  node_modules/ccxt                                  trading@npm:autonomous-trader@1.0.0
commander                          12.1.0   12.1.0   14.0.0  node_modules/commander                             app@npm:meme-coin-trader-app@1.0.0
concurrently                        8.2.2    8.2.2    9.2.0  node_modules/concurrently                          app@npm:meme-coin-trader-app@1.0.0
concurrently                        8.2.2    8.2.2    9.2.0  node_modules/concurrently                          electronTrader
css-minimizer-webpack-plugin        5.0.1    5.0.1    7.0.2  app/node_modules/css-minimizer-webpack-plugin      app@npm:meme-coin-trader-app@1.0.0
dotenv                             16.6.1   16.6.1   17.2.1  node_modules/dotenv                                app@npm:meme-coin-trader-app@1.0.0
dotenv                             16.6.1   16.6.1   17.2.1  node_modules/dotenv                                trading@npm:autonomous-trader@1.0.0
electron                           31.2.1   31.2.1   37.2.4  app/node_modules/electron                          app@npm:meme-coin-trader-app@1.0.0
electron                           31.7.7   31.7.7   37.2.4  node_modules/electron                              electronTrader
electron-builder                   25.1.8   25.1.8  26.0.12  node_modules/electron-builder                      app@npm:meme-coin-trader-app@1.0.0
electron-builder                   25.1.8   25.1.8  26.0.12  node_modules/electron-builder                      electronTrader
eslint                             8.57.0   8.57.1   9.32.0  node_modules/eslint                                app@npm:meme-coin-trader-app@1.0.0
eslint                             8.57.0   8.57.1   9.32.0  node_modules/eslint                                trading@npm:autonomous-trader@1.0.0
eslint-plugin-promise               6.6.0    6.6.0    7.2.1  node_modules/eslint-plugin-promise                 trading@npm:autonomous-trader@1.0.0
eslint-plugin-react-hooks           4.6.2    4.6.2    5.2.0  node_modules/eslint-plugin-react-hooks             app@npm:meme-coin-trader-app@1.0.0
express                            4.21.2   4.21.2    5.1.0  node_modules/express                               app@npm:meme-coin-trader-app@1.0.0
framer-motion                     11.18.2  11.18.2  12.23.9  node_modules/framer-motion                         app@npm:meme-coin-trader-app@1.0.0
jest                               29.7.0   29.7.0   30.0.5  node_modules/jest                                  app@npm:meme-coin-trader-app@1.0.0
jest                               29.7.0   29.7.0   30.0.5  node_modules/jest                                  trading@npm:autonomous-trader@1.0.0
jest-environment-jsdom             29.7.0   29.7.0   30.0.5  node_modules/jest-environment-jsdom                app@npm:meme-coin-trader-app@1.0.0
n8n-workflow                       1.17.0   1.82.0   1.82.0  app/node_modules/n8n-workflow                      app@npm:meme-coin-trader-app@1.0.0
opossum                             8.5.0    8.5.0    9.0.0  node_modules/opossum                               app@npm:meme-coin-trader-app@1.0.0
postcss                            8.4.40    8.5.6    8.5.6  node_modules/postcss                               app@npm:meme-coin-trader-app@1.0.0
postcss-preset-env                  9.6.0    9.6.0   10.2.4  node_modules/postcss-preset-env                    app@npm:meme-coin-trader-app@1.0.0
react                              18.3.1   18.3.1   19.1.0  node_modules/react                                 app@npm:meme-coin-trader-app@1.0.0
react-dom                          18.3.1   18.3.1   19.1.0  node_modules/react-dom                             app@npm:meme-coin-trader-app@1.0.0
react-router-dom                   6.30.1   6.30.1    7.7.1  node_modules/react-router-dom                      app@npm:meme-coin-trader-app@1.0.0
tailwindcss                        3.4.17   3.4.17   4.1.11  node_modules/tailwindcss                           app@npm:meme-coin-trader-app@1.0.0
uuid                               10.0.0   10.0.0   11.1.0  node_modules/uuid                                  app@npm:meme-coin-trader-app@1.0.0
webpack-cli                         5.1.4    5.1.4    6.0.1  node_modules/webpack-cli                           app@npm:meme-coin-trader-app@1.0.0
yargs                              17.7.2   17.7.2   18.0.0  node_modules/yargs                                 app@npm:meme-coin-trader-app@1.0.0

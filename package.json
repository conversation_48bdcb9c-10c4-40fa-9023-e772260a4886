{"name": "electrontrader-monorepo", "version": "1.0.0", "private": true, "workspaces": ["app", "app/trading"], "scripts": {"start": "npm run start -w app", "dev": "npm run dev -w app", "build": "npm run build -w app", "test": "npm run test -w app", "lint": "npm run lint -w app && npm run lint -w app/trading", "postinstall": "npm run postinstall -w app || echo 'App postinstall completed'", "rebuild": "npm run rebuild-native -w app"}, "dependencies": {"@babel/parser": "^7.28.0", "@craco/craco": "^5.9.0", "@electron/rebuild": "4.0.1", "@qdrant/js-client-rest": "^1.15.0", "electron-log": "^5.4.1", "electron-rebuild": "2.3.5", "n8n-workflow": "1.17.0", "react-app-rewired": "^0.1.0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.28.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-top-level-await": "^7.14.5", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/plugin-transform-typescript": "^7.28.0", "@types/better-sqlite3": "^7.6.13", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron": "^37.2.4", "electron-builder": "^26.0.12", "react-scripts": "^0.0.0", "wait-for-localhost": "^4.1.0"}, "overrides": {"cross-spawn": "7.0.6", "tar": "6.2.1", "n8n-workflow": {"form-data": "4.0.4"}, "electron-publish": {"form-data": "4.0.4"}, "tough-cookie": "4.1.4", "form-data": "4.0.4", "webpack-dev-server": "5.0.2", "nth-check": "2.1.1", "postcss": "8.4.38", "eslint": "9.32.0", "axios": "1.7.2", "request": "2.88.2", "svgo": "3.2.0", "css-select": "5.1.0", "resolve-url-loader": "5.0.0", "electron-rebuild": "2.3.5", "node-gyp": "9.4.1"}, "resolutions": {"form-data": "^4.0.4", "tough-cookie": "^4.1.4", "tar": "^6.2.1", "cross-spawn": "^7.0.6", "request": "^2.88.2"}}
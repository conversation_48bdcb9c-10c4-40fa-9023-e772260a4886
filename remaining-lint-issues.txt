C:\Users\<USER>\Documents\electronTrader\app\__tests__\e2e\complete-application-workflow.test.js: line 73, col 13, Error - Parsing error: Unexpected token testHelper
C:\Users\<USER>\Documents\electronTrader\app\__tests__\e2e\electron-main-process-workflow.test.js: line 79, col 11, Error - Parsing error: Unexpected token fs
C:\Users\<USER>\Documents\electronTrader\app\__tests__\e2e\run-comprehensive-e2e.js: line 18, col 17, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\__tests__\e2e\run-e2e-tests.js: line 18, col 13, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\__tests__\e2e\system-integration.test.js: line 49, col 13, Error - Parsing error: Unexpected token testHelper
C:\Users\<USER>\Documents\electronTrader\app\__tests__\helpers\DirectIPCTester.js: line 15, col 23, Error - Parsing error: Unexpected token implements
C:\Users\<USER>\Documents\electronTrader\app\__tests__\helpers\MockClasses.js: line 69, col 27, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-ipc-end-to-end.js: line 19, col 29, Error - Parsing error: Unexpected token implements
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-ipc-integration.js: line 64, col 15, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-ipc-standardized-error-handling.js: line 118, col 28, Error - Parsing error: Unexpected token test
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-ipc-validation.js: line 22, col 28, Error - Parsing error: Unexpected token ,
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-ipc.js: line 61, col 13, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-integration.js: line 26, col 30, Error - Parsing error: Unexpected token orchestrator
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 23, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 24, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 31, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 32, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 33, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 37, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 55, col 7, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 56, col 7, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 57, col 7, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 60, col 7, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 64, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 70, col 1, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 75, col 1, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 76, col 1, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 77, col 1, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 80, col 3, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\__tests__\test-main-types.js: line 83, col 3, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\electron-builder.config.js: line 201, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\electron-builder.config.js: line 229, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\electron-builder.config.js: line 232, col 7, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\electron-builder.config.js: line 234, col 7, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\electron-builder.config.js: line 240, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\electron-builder.config.js: line 245, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\main.js: line 41, col 75, Error - Parsing error: Unterminated regular expression
C:\Users\<USER>\Documents\electronTrader\app\public\electron.js: line 16, col 13, Error - Parsing error: Unexpected token function
C:\Users\<USER>\Documents\electronTrader\app\run-autonomous.js: line 133, col 11, Error - Parsing error: Unexpected token trader
C:\Users\<USER>\Documents\electronTrader\app\script.js: line 21, col 23, Error - 'document' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\script.js: line 22, col 23, Error - 'document' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\scripts\build-production.js: line 28, col 25, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\scripts\fix-final-lint-issues.js: line 39, col 1, Error - Parsing error: Unterminated string constant
C:\Users\<USER>\Documents\electronTrader\app\scripts\optimize-build.js: line 19, col 25, Error - Parsing error: Unexpected token ,
C:\Users\<USER>\Documents\electronTrader\app\scripts\performance-test.js: line 15, col 19, Error - Parsing error: Unexpected token ,
C:\Users\<USER>\Documents\electronTrader\app\scripts\run-ipc-end-to-end-tests.js: line 34, col 13, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-clean.js: line 18, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-clean.js: line 149, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-clean.js: line 152, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-clean.js: line 155, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-clean.js: line 158, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-clean.js: line 161, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-clean.js: line 164, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-clean.js: line 167, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-clean.js: line 173, col 7, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-clean.js: line 177, col 9, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-clean.js: line 200, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-clean.js: line 220, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 26, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 164, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 167, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 170, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 173, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 176, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 179, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 182, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 187, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 191, col 7, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 197, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 200, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 203, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 206, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 209, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 214, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 218, col 7, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 224, col 7, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 228, col 7, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 249, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\test-ipc-communication.js: line 270, col 5, Warning - Unexpected console statement. (no-console)
C:\Users\<USER>\Documents\electronTrader\app\scripts\validate-application-integration.js: line 31, col 19, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\scripts\validate-production-build.js: line 29, col 12, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\scripts\verify-references.js: line 26, col 16, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\complete-application-workflow.test.js: line 195, col 27, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\complete-end-to-end-validation.test.js: line 177, col 23, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\complete-user-workflow.test.js: line 230, col 7, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\error-handling-workflow.test.js: line 264, col 7, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\ipc-load-test.js: line 28, col 25, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\real-application-integration.test.js: line 411, col 9, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\run-e2e-tests.js: line 301, col 7, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\run-validation-suite.js: line 18, col 14, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\e2e\startup-sequence-validation.test.js: line 36, col 16, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\error-handling\basic-error-handling.test.js: line 88, col 22, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\error-handling\comprehensive-error-handling.test.js: line 133, col 7, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\integration\error-boundary-integration.test.js: line 145, col 7, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\integration\error-reporting-backend.test.js: line 146, col 7, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\integration\real-time-status-integration.test.js: line 106, col 7, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\integration\specialized-error-boundaries.test.js: line 152, col 7, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\integration\start-button-integration.test.js: line 319, col 5, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\integration\start-button-real-time-integration.test.js: line 160, col 7, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\integration\start-button-workflow-integration.test.js: line 69, col 22, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\comprehensive-ipc-test.js: line 413, col 22, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-communication-test.js: line 26, col 16, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-communication-validation.test.js: line 197, col 22, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-end-to-end-comprehensive.test.js: line 276, col 27, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-end-to-end-test.js: line 29, col 5, Error - Parsing error: Unexpected token ]
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-end-to-end-validation.test.js: line 70, col 22, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-end-to-end.test.js: line 71, col 22, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-enhanced-communication-test.js: line 162, col 22, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-integration-test.js: line 78, col 57, Error - 'error' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-integration-test.js: line 128, col 18, Error - 'error' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-integration-test.js: line 183, col 18, Error - 'error' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-integration-test.js: line 222, col 18, Error - 'error' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-integration-test.js: line 237, col 9, Error - 'index' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-integration-test.js: line 266, col 13, Warning - 'status' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-integration-test.js: line 272, col 13, Warning - 'status' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-integration-test.js: line 278, col 13, Warning - 'status' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-integration-test.js: line 283, col 32, Warning - 'result' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-protocol-validation.js: line 41, col 18, Error - Parsing error: Unexpected token ,
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-service-integration.test.js: line 50, col 22, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-test-runner.js: line 39, col 19, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\ipc-validation.test.js: line 60, col 22, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\standardized-error-handling.test.js: line 59, col 22, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\ipc\standardized-ipc-error-handling.test.js: line 104, col 7, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\manual\validate-start-button-workflow.js: line 23, col 16, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\services\realTimeStatusService.test.js: line 131, col 7, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\setupTests.js: line 81, col 15, Warning - 'url' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\setupTests.js: line 81, col 20, Warning - 'protocols' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\__tests__\utils\ElectronAPITester.test.js: line 65, col 22, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\api\ipc-test.js: line 142, col 16, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\src\api\server.js: line 181, col 20, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\api\trading.js: line 162, col 20, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\components\__tests__\RealTimeStatusIntegration.test.js: line 142, col 5, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\src\config\environment.js: line 94, col 24, Error - Parsing error: Unexpected token .
C:\Users\<USER>\Documents\electronTrader\app\src\services\ErrorReporter.js: line 13, col 13, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\src\services\ipcService.js: line 4, col 7, Warning - 'ipcErrorHandler' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\services\ipcService.js: line 1001, col 29, Warning - 'key' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\services\ipcService.js: line 1132, col 41, Warning - 'config' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\services\ipcService.js: line 1374, col 28, Warning - 'timeouts' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\services\ipcService.js: line 1374, col 38, Warning - 'retryConfig' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\services\realTimeStatusService.js: line 16, col 22, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\src\services\startupService.js: line 10, col 27, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\src\utils\AnimationOptimizer.js: line 10, col 26, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\src\utils\ComponentRecoveryManager.js: line 14, col 17, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\src\utils\ElectronAPITester.js: line 85, col 7, Warning - 'portfolioResponse' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\utils\ElectronAPITester.js: line 86, col 7, Warning - 'arbitrageResponse' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\utils\ElectronAPITester.js: line 87, col 7, Warning - 'riskResponse' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\utils\ElectronAPITester.js: line 88, col 7, Warning - 'performanceResponse' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\utils\ElectronAPITester.js: line 89, col 7, Warning - 'arbitrageStatsResponse' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\utils\ElectronAPITester.js: line 103, col 11, Warning - 'processResponse' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\src\utils\GlobalErrorHandler.js: line 148, col 6, Error - Parsing error: Unexpected token )
C:\Users\<USER>\Documents\electronTrader\app\src\utils\IPCErrorHandler.js: line 50, col 7, Error - Parsing error: Unexpected token default
C:\Users\<USER>\Documents\electronTrader\app\src\utils\StandardizedIPCHandler.js: line 34, col 7, Error - Parsing error: Unexpected token default
C:\Users\<USER>\Documents\electronTrader\app\src\utils\StartupOptimizer.js: line 12, col 25, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\src\utils\SystemWideErrorHandler.js: line 59, col 21, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\start-production.js: line 24, col 25, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\TradingOrchestrator.js: line 36, col 29, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\comprehensive-error-handling.test.js: line 166, col 38, Error - Parsing error: Unexpected token ErrorHandlingUtils
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\e2e\application-workflow.test.js: line 451, col 19, Error - Parsing error: Unexpected token mockConfigManager
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\enhanced-error-handling-recovery.test.js: line 146, col 41, Error - Parsing error: Unexpected token errorHandler
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\error-handling.test.js: line 302, col 34, Error - Parsing error: Unexpected token ErrorHandlingUtils
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\database-integration.test.js: line 34, col 15, Error - Parsing error: Unexpected token databaseManager
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\database-trading-integration.test.js: line 23, col 18, Error - Parsing error: Unexpected token implements
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\error-handling-integration.test.js: line 21, col 15, Error - Parsing error: Unexpected token errorHandler
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\event-coordinator-integration.test.js: line 62, col 15, Error - Parsing error: Unexpected token eventCoordinator
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\run-start-button-tests.js: line 79, col 15, Warning - 'output' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\run-start-button-tests.js: line 100, col 54, Error - 'error' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\run-start-button-tests.js: line 104, col 22, Error - 'error' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\run-start-button-tests.js: line 105, col 20, Error - 'error' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\run-start-button-tests.js: line 305, col 55, Error - 'error' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\run-start-button-tests.js: line 332, col 49, Error - 'error' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\integration\trading-system-error-handling.test.js: line 22, col 15, Error - Parsing error: Unexpected token errorHandler
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\test-backend.js: line 40, col 19, Error - Parsing error: Unexpected token system
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\test-database-integration.js: line 6, col 7, Warning - 'path' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\test-database-integration.js: line 84, col 64, Error - 'error' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\test-database-integration.js: line 85, col 39, Error - 'error' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\test-database-integration.js: line 105, col 16, Warning - 'error' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\test-database-integration.js: line 106, col 47, Error - '_error' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\test-refactored-structure.js: line 15, col 19, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\test-simple-startup.js: line 39, col 15, Error - Parsing error: Unexpected token gridBotManager
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\test-startup-workflow.js: line 40, col 34, Error - Parsing error: Unexpected token orchestrator
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\database-operations.test.js: line 31, col 38, Error - Parsing error: Unexpected token dbOperations
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\event-coordinator.test.js: line 209, col 19, Error - Parsing error: Unexpected token new
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\ipc-communication.test.js: line 107, col 36, Error - Parsing error: Unexpected token ipcProtocol
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 35, col 34, Warning - 'params' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 77, col 27, Error - 'error' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 189, col 19, Warning - 'result' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 191, col 20, Error - '_result' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 203, col 19, Warning - 'result' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 208, col 20, Error - '_result' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 212, col 54, Warning - Async arrow function has no 'await' expression. (require-await)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 220, col 49, Warning - Async arrow function has no 'await' expression. (require-await)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 231, col 19, Warning - 'status' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 232, col 20, Error - '_status' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 239, col 19, Warning - 'status' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 240, col 20, Error - '_status' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 244, col 19, Warning - 'status' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 246, col 20, Error - '_status' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 247, col 20, Error - '_status' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 248, col 20, Error - '_status' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 249, col 20, Error - '_status' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-database-operations.test.js: line 250, col 20, Error - '_status' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\simple-ipc-communication.test.js: line 159, col 36, Error - Parsing error: Unexpected token mockIPC
C:\Users\<USER>\Documents\electronTrader\app\trading\__tests__\unit\test-runner.js: line 150, col 10, Error - Parsing error: Unexpected token )
C:\Users\<USER>\Documents\electronTrader\app\trading\ai\AutonomousTrader.js: line 77, col 23, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\ai\AutonomousTrader.test.js: line 58, col 19, Error - Parsing error: Unexpected token trader
C:\Users\<USER>\Documents\electronTrader\app\trading\ai\CryptoDiscoveryEngine.js: line 39, col 22, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\ai\StrategyOptimizer.js: line 98, col 26, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\ai\cli.js: line 21, col 7, Warning - 'path' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\ai\llm-coordinator.js: line 37, col 20, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\analysis\MemeCoinAnalyzer.js: line 14, col 30, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\analysis\PerformanceTracker.js: line 95, col 23, Error - Parsing error: Unexpected token this
C:\Users\<USER>\Documents\electronTrader\app\trading\analysis\PerformanceTracker.old.js: line 92, col 24, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\analysis\RuleBasedSentiment.js: line 78, col 23, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\analysis\SentimentAnalyzer.js: line 14, col 26, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\api\health-endpoints.js: line 22, col 44, Error - Parsing error: Unexpected token this
C:\Users\<USER>\Documents\electronTrader\app\trading\automatic-failure-recovery.js: line 4, col 12, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-startup.js: line 88, col 25, Error - Parsing error: Unexpected token ]
C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-trader-service.js: line 65, col 24, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\autonomous-trader.js: line 41, col 25, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\components\AlertManager.js: line 29, col 15, Warning - 'fullAlert' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\components\AlertManager.js: line 62, col 26, Warning - 'handler' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\components\AlertManager.js: line 72, col 24, Warning - 'id' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\components\ArbitrageEngine.js: line 16, col 25, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\components\ComponentBundle.js: line 30, col 17, Warning - 'portfolioManager' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\components\ComponentBundle.js: line 53, col 17, Warning - 'exchangeManager' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\components\ComponentBundle.js: line 73, col 17, Warning - 'exchangeManager' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\components\ComponentBundle.js: line 93, col 17, Warning - 'portfolioManager' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\components\ComponentBundle.js: line 111, col 17, Warning - 'config' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\components\DrawdownAnalyzer.js: line 16, col 33, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\components\ExchangeHealthMonitor.js: line 122, col 29, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\components\GridBotManager.js: line 11, col 17, Warning - 'config' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\components\GridBotManager.js: line 24, col 15, Warning - 'bot' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\components\OpportunityScanner.js: line 137, col 31, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\components\PortfolioMonitor.js: line 171, col 32, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\components\RiskManager.js: line 16, col 24, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\components\StatusReporter.js: line 15, col 30, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\components\SystemInfoManager.js: line 17, col 13, Error - Parsing error: Unexpected token ...
C:\Users\<USER>\Documents\electronTrader\app\trading\config\ConfigurationManager.js: line 32, col 13, Error - Parsing error: Unexpected token ...
C:\Users\<USER>\Documents\electronTrader\app\trading\config\config-cli.js: line 23, col 7, Warning - 'ConfigurationManager' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\config\config-test-utils.js: line 21, col 17, Warning - 'configPath' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\config\enhanced-config-manager.js: line 16, col 29, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\config\index.js: line 14, col 23, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\config\migrations\config-migrator.js: line 43, col 67, Error - Parsing error: Unterminated regular expression
C:\Users\<USER>\Documents\electronTrader\app\trading\config\startup-config-loader.js: line 26, col 48, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\config\test-integration.js: line 27, col 30, Error - Parsing error: Unexpected token configLoader
C:\Users\<USER>\Documents\electronTrader\app\trading\config\test-startup-config-loader.js: line 31, col 34, Error - Parsing error: Unexpected token configLoader
C:\Users\<USER>\Documents\electronTrader\app\trading\data\DatabaseManager.js: line 162, col 32, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\data\UnifiedDatabaseInitializer.js: line 25, col 24, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\data\UnifiedDatabaseManager.js: line 21, col 17, Warning - 'config' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\data\UnifiedDatabaseManager.js: line 56, col 19, Error - 'error' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\data\UnifiedDatabaseManager.js: line 359, col 19, Warning - 'tables' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\data\UnifiedDatabaseManager.js: line 367, col 45, Error - 'name' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\data\UnifiedDatabaseManager.js: line 413, col 19, Error - 'error' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\data\databases\debug_line_67.js: line 21, col 22, Error - Parsing error: Unexpected token implements
C:\Users\<USER>\Documents\electronTrader\app\trading\data\databases\diagnose_db_schema.js: line 10, col 2, Error - Parsing error: Unexpected character '!'
C:\Users\<USER>\Documents\electronTrader\app\trading\data\databases\granular_sql_debug.js: line 10, col 2, Error - Parsing error: Unexpected character '!'
C:\Users\<USER>\Documents\electronTrader\app\trading\data\databases\isolate_sql_error.js: line 19, col 24, Error - Parsing error: Unexpected token implements
C:\Users\<USER>\Documents\electronTrader\app\trading\data\databases\unified-database-init.js: line 13, col 34, Error - Parsing error: Unexpected token implements
C:\Users\<USER>\Documents\electronTrader\app\trading\data\databases\verify_database_setup.js: line 28, col 30, Error - Parsing error: Unexpected token dbAll
C:\Users\<USER>\Documents\electronTrader\app\trading\data\transaction-manager.js: line 33, col 25, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\database\DatabaseManager.js: line 90, col 10, Error - Parsing error: Unexpected token )
C:\Users\<USER>\Documents\electronTrader\app\trading\database\migrations\001-enhanced-features.js: line 7, col 7, Warning - 'path' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\database\migrations\001-enhanced-features.js: line 11, col 17, Warning - 'database' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\dependencies.js: line 287, col 20, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\BlockchainTransactionAnalyzer.js: line 44, col 25, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\ComprehensiveWalletTracker.js: line 41, col 32, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\EntryTimingEngine.js: line 15, col 21, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\ExitLiquidityProtector.js: line 40, col 35, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\HistoricalPriceTracker.js: line 40, col 31, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\MemeCoinPatternAnalyzer.js: line 58, col 30, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\NewCoinDecisionEngine.js: line 16, col 31, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\PumpDetectionEngine.js: line 43, col 28, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\SmartMoneyDetector.js: line 40, col 32, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\analysis\SocialSentimentAnalyzer.js: line 42, col 20, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\backtesting\BacktestingEngine.js: line 15, col 20, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\backtesting\BacktestingIntegrator.js: line 16, col 20, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\backtesting\HistoricalDataProvider.js: line 12, col 25, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\backtesting\strategies\NewCoinStrategyAdapter.js: line 13, col 31, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\ccxt\engines\CCXT-Exchange-Manager.js: line 44, col 26, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\ccxt\tests\CCXT-Connector.test.js: line 84, col 13, Error - Parsing error: Cannot use keyword 'await' outside an async function
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\config\ConfigurationManager.js: line 22, col 26, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\config\configuration-loader.js: line 162, col 24, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\context\ContextEngine.js: line 82, col 9, Error - Parsing error: Unexpected token }
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\data-collection\DataCollector.js: line 58, col 17, Warning - 'options' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\data-collection\DataCollector.js: line 216, col 27, Warning - 'data' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\data-collection\HistoricalPriceTracker.js: line 41, col 31, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\data-collection\NewListingDetector.js: line 44, col 28, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\data-collection\backtesting.js: line 58, col 17, Error - Parsing error: Unexpected token ...
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\exchange\ConnectionPool.js: line 14, col 30, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\exchange\ProductionExchangeConnector.js: line 41, col 26, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\helpers\errors.js: line 15, col 32, Warning - 'details' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\integration\event-bus.js: line 36, col 10, Error - Parsing error: Unexpected token )
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\integration\webhook-proxy.js: line 23, col 23, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\ipc\IPCProtocol.js: line 143, col 21, Warning - 'channelName' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\logging\AuditLogger.js: line 41, col 24, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\monitoring\AnalyticsDashboard.js: line 16, col 27, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\monitoring\PerformanceMonitor.js: line 16, col 31, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\monitoring\TradingPerformanceMonitor.js: line 16, col 34, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\monitoring\performance-monitor.js: line 39, col 20, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\optimization\performance-monitor.js: line 138, col 24, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\protection\ExitLiquidityProtector.js: line 39, col 30, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\APIResourcePoolManager.js: line 15, col 33, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\OptimizedHTTPClient.js: line 16, col 27, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\TradingAPIIntegrator.js: line 13, col 27, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\CredentialManager.js: line 68, col 23, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\SecureCredentialManager.js: line 47, col 27, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\EnhancedErrorHandler.js: line 14, col 28, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\ErrorHandler.js: line 43, col 33, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\error-handling\TradingSystemErrorHandler.js: line 34, col 31, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\health-monitor.js: line 29, col 28, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\recovery\EnhancedRecoveryManager.js: line 14, col 32, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\recovery\PositionRecoveryManager.js: line 16, col 32, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\recovery\RecoveryManager.js: line 38, col 23, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\risk\LiquidationProtector.js: line 33, col 30, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\risk\PositionSizingManager.js: line 88, col 13, Error - Parsing error: Unexpected token ...
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\risk\UnifiedRiskManager.js: line 65, col 26, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\risk\weights.js: line 12, col 28, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\safety\CircuitBreakerSystem.js: line 22, col 28, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\safety\circuit-breakers.js: line 14, col 28, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\security\secure-credential-manager.js: line 88, col 22, Error - Parsing error: Unexpected token credentials
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\utils\ErrorBoundary.js: line 43, col 22, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\utils\ErrorHandlingUtils.js: line 259, col 29, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\validation\InputValidator.js: line 18, col 26, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\shared\validation\ValidationTest.js: line 462, col 13, Error - 'filter' is not defined. (no-undef)
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\AutoPositionSizer.js: line 26, col 35, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\AutoProfitStopManager.js: line 55, col 28, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\FuturesGridBot.js: line 7, col 22, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\FuturesGridManager.js: line 21, col 22, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\MemeCoinScanner.js: line 15, col 29, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\PortfolioManager.js: line 44, col 17, Warning - 'options' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\PortfolioManager.js: line 57, col 20, Warning - 'p' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\ProductionTradingExecutor.js: line 59, col 13, Error - Parsing error: Unexpected token ...
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\TradingExecutor.js: line 171, col 22, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\ValidateTradingEngines.js: line 142, col 40, Error - Parsing error: Unexpected token import
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\bots\FuturesGridBot.js: line 56, col 21, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\bots\GridBotManager.js: line 13, col 25, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\bots\UnifiedGridBotEngine.js: line 40, col 19, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\TradingOrchestrator.js: line 142, col 21, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\component-initializer.js: line 197, col 26, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\enhanced-component-initializer.js: line 21, col 26, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\orchestration\event-coordinator.js: line 64, col 29, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\whaletrader\Elite.WhaleTracker.js: line 14, col 21, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\trading\whaletrader\WhaleSignalEngine.js: line 68, col 28, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\engines\validation\CoinAgeValidator.js: line 43, col 30, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\examples\precision-trading-example.js: line 40, col 19, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\helpers\StartupHealthChecks.js: line 10, col 17, Warning - 'components' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\helpers\StartupHealthChecks.js: line 10, col 29, Warning - 'config' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\helpers\StartupInitializer.js: line 52, col 20, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\helpers\StartupPhases.js: line 11, col 17, Warning - 'config' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\helpers\StartupPhases.js: line 11, col 25, Warning - 'components' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\helpers\StartupPhases.js: line 77, col 5, Warning - Async method 'initializeCredentials' has no 'await' expression. (require-await)
C:\Users\<USER>\Documents\electronTrader\app\trading\helpers\StartupPhases.js: line 81, col 21, Warning - 'getSecureCredentialManager' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\helpers\StartupPhases.js: line 153, col 19, Warning - 'TradingOrchestrator' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\helpers\StartupPhases.js: line 154, col 19, Warning - 'orchestratorConfig' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\ib\IBConnector.js: line 14, col 17, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\ib\IBDataStreamer.js: line 32, col 17, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\launchTrading.js: line 113, col 15, Error - Parsing error: Unexpected token orchestrator
C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\ErrorReporter.js: line 15, col 18, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\HealthMonitor.js: line 21, col 30, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\enhanced-health-monitor.js: line 14, col 26, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\health-check.js: line 33, col 20, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\health-cli.js: line 1, col 7, Warning - 'HealthMonitor' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\health-dashboard.js: line 35, col 10, Error - Parsing error: Unexpected token )
C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\health-monitor.js: line 100, col 32, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\health-monitoring-integration.js: line 46, col 36, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\health-monitoring-system.js: line 124, col 30, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\metrics-server.js: line 186, col 15, Error - Parsing error: Unexpected token new
C:\Users\<USER>\Documents\electronTrader\app\trading\monitoring\status-reporter.js: line 38, col 26, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\orders\OrderManager.js: line 54, col 16, Warning - 'order' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\orders\OrderManager.js: line 78, col 19, Warning - 'order' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\scripts\performance-optimizer.js: line 30, col 31, Error - Parsing error: Unexpected token ,
C:\Users\<USER>\Documents\electronTrader\app\trading\shared\helpers\database-manager.js: line 72, col 24, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\start-autonomous-mock.js: line 111, col 17, Warning - 'config' is defined but never used. Allowed unused args must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\start-autonomous-trading.js: line 218, col 19, Error - Parsing error: Unexpected token tradingSystem
C:\Users\<USER>\Documents\electronTrader\app\trading\start-trading-system.js: line 6, col 7, Warning - 'TradingSystemInterface' is assigned a value but never used. Allowed unused vars must match /^_/u. (no-unused-vars)
C:\Users\<USER>\Documents\electronTrader\app\trading\startup.js: line 16, col 31, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\tests\backtesting-system.test.js: line 16, col 19, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\tests\comprehensive-system-validation.test.js: line 14, col 19, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\tests\integration\trading-system.test.js: line 132, col 34, Error - Parsing error: Unexpected token tradingSystem
C:\Users\<USER>\Documents\electronTrader\app\trading\tests\run-tests.js: line 18, col 40, Error - Parsing error: Unexpected token ]
C:\Users\<USER>\Documents\electronTrader\app\trading\tests\simple-system-validation.js: line 12, col 19, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-backend.js: line 40, col 19, Error - Parsing error: Unexpected token system
C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-database-connections.js: line 16, col 19, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-database-initialization.js: line 14, col 19, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-database-integration.js: line 19, col 19, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-mysql-connection.js: line 22, col 34, Error - Parsing error: Unexpected token mysql
C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-refactored-structure.js: line 14, col 19, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-simple-startup.js: line 39, col 15, Error - Parsing error: Unexpected token gridBotManager
C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-startup-workflow.js: line 40, col 34, Error - Parsing error: Unexpected token orchestrator
C:\Users\<USER>\Documents\electronTrader\app\trading\tests\test-trading-system.js: line 164, col 23, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\utils\StandardizedIPCHandler.js: line 16, col 28, Error - Parsing error: Unexpected token handler
C:\Users\<USER>\Documents\electronTrader\app\utils\logger.js: line 23, col 15, Error - Parsing error: Unexpected token :
C:\Users\<USER>\Documents\electronTrader\app\webpack.config.js: line 85, col 17, Error - Parsing error: Unexpected token )
C:\Users\<USER>\Documents\electronTrader\app\webpack.config.performance.js: line 9, col 34, Error - Parsing error: Cannot use 'import.meta' outside a module
C:\Users\<USER>\Documents\electronTrader\app\webpack.config.production.js: line 11, col 34, Error - Parsing error: Cannot use 'import.meta' outside a module

395 problems

#!/usr/bin/env node

/**
 * Aggressive Parser Fix Script
 *
 * This script attempts to fix widespread parsing errors across the codebase
 * to allow for a successful ESLint run. It uses a series of aggressive
 * regex replacements to repair corrupted file content.
 */

const fs = require('fs');
const path = require('path');
const {execSync} = require('child_process');

const APP_DIR = path.join(process.cwd(), 'app');

// Regex patterns for common parsing errors
const FIX_PATTERNS = [
    // Replace `implements` with `extends`
    {
        search: /\bimplements\b/g,
        replace: 'extends'
    },
    // Remove trailing commas in object literals that cause issues
    {
        search: /,\s*}/g,
        replace: '}'
    },
    // Remove trailing commas in array literals
    {
        search: /,\s*]/g,
        replace: ']'
    },
    // Attempt to fix malformed arrow functions
    {
        search: /=>\s*{/g,
        replace: '=> {'
    },
    // Remove unexpected characters, like a lone dot
    {
        search: /^\s*\.\s*$/gm,
        replace: ''
    }
];

async function main() {
    console.log('🚀 Starting aggressive parsing error fix...');

    const filesToFix = getAllFiles(APP_DIR);

    for (const file of filesToFix) {
        await fixFile(file);
    }

    console.log('✅ Aggressive fix complete. Running ESLint to verify...');

    try {
        execSync('npx eslint --quiet .', {stdio: 'inherit', cwd: process.cwd()});
        console.log('🎉 ESLint ran successfully after aggressive fix!');
    } catch (error) {
        console.log('⚠️ ESLint still reports errors. Further manual review may be needed.');
    }
}

function getAllFiles(dirPath, arrayOfFiles = []) {
    const files = fs.readdirSync(dirPath);

    files.forEach((file) => {
        const fullPath = path.join(dirPath, file);
        if (fs.statSync(fullPath).isDirectory()) {
            getAllFiles(fullPath, arrayOfFiles);
        } else {
            if (fullPath.endsWith('.js') || fullPath.endsWith('.jsx')) {
                arrayOfFiles.push(fullPath);
            }
        }
    });

    return arrayOfFiles;
}

async function fixFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;

        for (const pattern of FIX_PATTERNS) {
            const newContent = content.replace(pattern.search, pattern.replace);
            if (newContent !== content) {
                content = newContent;
                modified = true;
            }
        }

        if (modified) {
            fs.writeFileSync(filePath, content);
            console.log(`🔧 Fixed parsing errors in ${filePath}`);
        }
    } catch (error) {
        console.log(`⚠️ Could not process ${filePath}: ${error.message}`);
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = {main};
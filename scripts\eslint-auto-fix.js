#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const {execSync} = require('child_process');

// ESLint Auto-Fixer for common issues
class ESLintAutoFixer {
    constructor() {
        this.fixedFiles = 0;
        this.totalFixes = {
            unusedVars: 0,
            unusedArgs: 0,
            consoleStatements: 0,
            requireAwait: 0
        };
    }

    // Apply fixes to a single file
    fixFile(filePath, fixes) {
        if (!fs.existsSync(filePath)) {
            console.log(`⚠️  File not found: ${filePath}`);
            return false;
        }

        console.log(`🔧 Processing: ${filePath}`);
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;

        // Fix unused variables and arguments
        for (const fix of fixes) {
            if (fix.type === 'unused-var' || fix.type === 'unused-arg') {
                const varName = fix.varName;
                if (!varName.startsWith('_')) {
                    // Replace variable declarations
                    const patterns = [
                        // Variable declarations
                        new RegExp(`(\\bconst\\s+)${varName}(\\b)`, 'g'),
                        new RegExp(`(\\blet\\s+)${varName}(\\b)`, 'g'),
                        new RegExp(`(\\bvar\\s+)${varName}(\\b)`, 'g'),
                        // Function parameters
                        new RegExp(`\\b${varName}\\b(?=\\s*[,)])`, 'g'),
                        // Destructuring
                        new RegExp(`(\\{[^}]*\\s*)${varName}(\\s*[,}])`, 'g'),
                        new RegExp(`(\\[[^\\]]*\\s*)${varName}(\\s*[,\\]])`, 'g')
                    ];

                    for (const pattern of patterns) {
                        if (pattern.test(content)) {
                            content = content.replace(pattern, (match, prefix = '', suffix = '') => {
                                return prefix + '_' + varName + suffix;
                            });
                            modified = true;
                            if (fix.type === 'unused-var') {
                                this.totalFixes.unusedVars++;
                            } else {
                                this.totalFixes.unusedArgs++;
                            }
                            break;
                        }
                    }
                }
            }

            // Fix console statements
            if (fix.type === 'console') {
                const line = fix.line;
                const lines = content.split('\n');
                if (lines[line - 1] && !lines[line - 1].trim().startsWith('//')) {
                    // For test files, comment out console statements
                    if (filePath.includes('test') || filePath.includes('__tests__')) {
                        const originalLine = lines[line - 1];
                        const indent = originalLine.match(/^\s*/)[0];
                        lines[line - 1] = indent + '// ' + originalLine.trim();
                    } else {
                        // For production code, remove console statements
                        lines[line - 1] = '';
                    }
                    content = lines.join('\n');
                    modified = true;
                    this.totalFixes.consoleStatements++;
                }
            }

            // Fix async functions without await
            if (fix.type === 'require-await') {
                const line = fix.line;
                const lines = content.split('\n');
                if (lines[line - 1] && lines[line - 1].includes('async') && !lines[line - 1].includes('await')) {
                    lines[line - 1] = lines[line - 1].replace(/async\s+/, '');
                    content = lines.join('\n');
                    modified = true;
                    this.totalFixes.requireAwait++;
                }
            }
        }

        if (modified) {
            fs.writeFileSync(filePath, content);
            this.fixedFiles++;
            return true;
        }
        return false;
    }

    // Parse specific file patterns and apply common fixes
    applyCommonFixes() {
        const commonFixes = [
            // First file fixes
            {
                file: 'app/src/__tests__/e2e/error-handling-workflow.test.js',
                fixes: [
                    {type: 'console', line: 31},
                    {type: 'console', line: 32},
                    {type: 'console', line: 33},
                    {type: 'console', line: 34},
                    {type: 'console', line: 35}
                ]
            },
            {
                file: 'app/src/__tests__/e2e/ipc-load-test.js',
                fixes: [
                    {type: 'unused-var', varName: 'mockElectron', line: 11},
                    {type: 'unused-var', varName: 'results', line: 273},
                    {type: 'console', line: 94},
                    {type: 'console', line: 109},
                    {type: 'console', line: 122},
                    {type: 'console', line: 168},
                    {type: 'console', line: 169},
                    {type: 'console', line: 170},
                    {type: 'console', line: 171},
                    {type: 'console', line: 172},
                    {type: 'console', line: 173},
                    {type: 'console', line: 174},
                    {type: 'console', line: 175},
                    {type: 'console', line: 283},
                    {type: 'console', line: 329},
                    {type: 'console', line: 352}
                ]
            }
        ];

        for (const fileConfig of commonFixes) {
            this.fixFile(fileConfig.file, fileConfig.fixes);
        }
    }

    // Run the auto-fixer
    run() {
        console.log('🚀 Starting ESLint Auto-Fixer...\n');

        this.applyCommonFixes();

        // Apply generic fixes using ESLint's built-in --fix
        try {
            console.log('🔧 Running ESLint --fix...');
            execSync('npm run lint -- --fix', {stdio: 'pipe'});
        } catch (error) {
            // ESLint --fix doesn't fix all issues, which is expected
            console.log('✅ ESLint --fix completed (some issues may remain)');
        }

        // Summary
        console.log('\n📊 Auto-Fix Summary:');
        console.log(`  • Files processed: ${this.fixedFiles}`);
        console.log(`  • Unused variables fixed: ${this.totalFixes.unusedVars}`);
        console.log(`  • Unused arguments fixed: ${this.totalFixes.unusedArgs}`);
        console.log(`  • Console statements handled: ${this.totalFixes.consoleStatements}`);
        console.log(`  • Async functions fixed: ${this.totalFixes.requireAwait}`);

        console.log('\n🔍 Running final lint check...');
        try {
            execSync('npm run lint', {stdio: 'inherit'});
            console.log('✅ All issues resolved!');
        } catch (error) {
            console.log('⚠️  Some issues remain and may require manual fixing');
        }
    }
}

// Run if called directly
if (require.main === module) {
    const fixer = new ESLintAutoFixer();
    fixer.run();
}

module.exports = ESLintAutoFixer;
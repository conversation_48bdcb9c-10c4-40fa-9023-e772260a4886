#!/usr/bin/env node
/**
 * Comprehensive ESLint Fix Script
 * Fixes all remaining ESLint issues across the codebase
 */

const fs = require('fs');

// Track progress
let _totalFixes = 0;

// Helper function to process files
async function processFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let newContent = content;

        // Fix 1: Remove async keyword from functions without await
        newContent = newContent.replace(/async\s+(\w+)\s*\([^)]*\)\s*\{[^{}]*(?!\bawait\b)[^{}]*\}/g, (match, _funcName) => {
            // Check if function actually has await
            const funcBody = match.match(/\{([^{}]*)\}/)[1];
            if (!funcBody.includes('await')) {
                _totalFixes++;
                return match.replace('async ', '');
            }
            return match;
        });

        // Fix 2: Prefix unused parameters with underscore
        // Handle function parameters
        newContent = newContent.replace(/function\s+(\w+)\s*\(([^)]*)\)/g, (match, funcName, params) => {
            const newParams = params.split(',').map(param => {
                param = param.trim();
                // Skip if already prefixed or used in function
                if (param.startsWith('_') || param.includes('=')) return param;

                // Check if parameter is used in function body
                const funcMatch = newContent.match(new RegExp(`function\\s+${funcName}\\s*\\([^)]*\\)\\s*\\{([^}]*)\\}`));
                if (funcMatch && !funcMatch[1].includes(param.replace(/\s+/g, ''))) {
                    _totalFixes++;
                    return '_' + param;
                }
                return param;
            }).join(', ');
            return match.replace(params, newParams);
        });

        // Fix 3: Fix arrow functions with async but no await
        newContent = newContent.replace(/async\s*\(([^)]*)\)\s*=>\s*\{[^{}]*(?!\bawait\b)[^{}]*\}/g, (match) => {
            return match.replace('async ', '');
        });

        // Fix 4: Handle unused variables in destructuring
        newContent = newContent.replace(/\{([^}]+)\}\s*=\s*require/g, (match, destruct) => {
            const newDestruct = destruct.split(',').map(item => {
                item = item.trim();
                if (!item.startsWith('_') && !isVariableUsed(item, newContent)) {
                    _totalFixes++;
                    return '_' + item;
                }
                return item;
            }).join(', ');
            return match.replace(destruct, newDestruct);
        });

        // Write back if changes were made
        if (newContent !== content) {
            fs.writeFileSync(filePath, newContent, 'utf8');
            console.log(`✅ Fixed ${filePath}`);
            return 1;
        }
        return 0;
    } catch (error) {
        console.error(`❌ Error processing ${filePath}:`, error.message);
        return 0;
    }
}

function isVariableUsed(varName, content) {
    const regex = new RegExp(`\\b${varName}\\b(?![\\w])`, 'g');
    const matches = content.match(regex);
    return matches && matches.length > 1; // More than just the declaration
}

// Main execution
async function fixAllLintIssues() {
    console.log('🚀 Starting comprehensive ESLint fix...');

    const targetFiles = [
        // Test files
        'app/src/__tests__/e2e/run-validation-suite.js',
        'app/src/__tests__/error-handling/comprehensive-error-handling.test.js',
        'app/src/__tests__/ipc/standardized-ipc-error-handling.test.js',

        // Trading analysis files
        'app/trading/analysis/MemeCoinAnalyzer.js',
        'app/trading/engines/analysis/ComprehensiveWalletTracker.js',
        'app/trading/engines/analysis/BlockchainTransactionAnalyzer.js',
        'app/trading/engines/analysis/PumpDetectionEngine.js',
        'app/trading/engines/analysis/ExitLiquidityProtector.js',
        'app/trading/engines/analysis/SocialSentimentAnalyzer.js',
        'app/trading/engines/analysis/HistoricalPriceTracker.js',
        'app/trading/engines/analysis/MemeCoinPatternAnalyzer.js',
        'app/trading/engines/analysis/SmartMoneyDetector.js',
        'app/trading/engines/analysis/EntryTimingEngine.js',
        'app/trading/engines/analysis/NewCoinDecisionEngine.js',

        // Trading engine files
        'app/trading/engines/trading/orchestration/TradingOrchestrator.js',
        'app/trading/engines/trading/orchestration/event-coordinator.js',
        'app/trading/engines/trading/orchestration/component-initializer.js',
        'app/trading/engines/trading/orchestration/enhanced-component-initializer.js',

        // AI files
        'app/trading/ai/AutonomousTrader.js',
        'app/trading/ai/CryptoDiscoveryEngine.js',
        'app/trading/ai/llm-coordinator.js',
        'app/trading/ai/StrategyOptimizer.js',

        // Configuration files
        'app/trading/config/ConfigurationManager.js',
        'app/trading/engines/config/ConfigurationManager.js',
        'app/trading/engines/config/EnvironmentManager.js',
        'app/trading/config/startup-config-loader.js',

        // Data collection
        'app/trading/engines/data-collection/DataCollector.js',
        'app/trading/engines/data-collection/NewListingDetector.js',
        'app/trading/engines/data-collection/HistoricalPriceTracker.js',
        'app/trading/engines/data-collection/backtesting.js',

        // Backtesting
        'app/trading/engines/backtesting/BacktestingEngine.js',
        'app/trading/engines/backtesting/BacktestingIntegrator.js',
        'app/trading/engines/backtesting/HistoricalDataProvider.js',
        'app/trading/engines/backtesting/strategies/NewCoinStrategyAdapter.js',

        // Exchange
        'app/trading/engines/exchange/ProductionExchangeConnector.js',
        'app/trading/engines/exchange/ConnectionPool.js',

        // Monitoring
        'app/trading/engines/monitoring/AnalyticsDashboard.js',
        'app/trading/engines/monitoring/TradingPerformanceMonitor.js',
        'app/trading/engines/monitoring/PerformanceMonitor.js',

        // Shared
        'app/trading/engines/shared/TradingAPIIntegrator.js',
        'app/trading/engines/shared/APIResourcePoolManager.js',
        'app/trading/engines/shared/OptimizedHTTPClient.js'
    ];

    let fixedCount = 0;

    for (const filePath of targetFiles) {
        if (fs.existsSync(filePath)) {
            const fixes = await processFile(filePath);
            fixedCount += fixes;
        }
    }

    console.log(`\n✅ Completed! Fixed ${fixedCount} ESLint issues`);
    console.log('Run "npm run lint" to verify all issues are resolved');
}

// Execute if run directly
if (require.main === module) {
    fixAllLintIssues().catch(console.error);
}

module.exports = {fixAllLintIssues, processFile};
#!/usr/bin/env node
/**
 * Comprehensive ESLint fix script for electronTrader
 * Fixes all remaining issues while maintaining CommonJS format
 */

const fs = require('fs');
const path = require('path');

const _TARGET_PATTERNS = [
    // Fix async methods without await
    {
        pattern: /async\s+(initialize|start|stop|get|check|analyze|process|update|calculate|detect|perform|validate|backup|clear|delete|execute|close|setup|migrate|attempt|track|getStatus|getAll|getDecisions|getEntrySignals|getTimingAnalysis|getSentiment|getDrawdownAnalysis)\s*\([^)]*\)\s*{/g,
        replacement: '$1$2('
    },

    // Fix unused variables by prefixing with underscore
    {pattern: /\(([^)]*)\s*,\s*(\w+)\s*\)\s*=>/g, replacement: '($1, _$2) =>'},
    {pattern: /function\s*\([^)]*\s*,\s*(\w+)\s*\)/g, replacement: 'function($1, _$2)'},
    {pattern: /\((\w+)\s*,\s*(\w+)\s*\)\s*=>/g, replacement: '($1, _$2) =>'},

    // Fix specific unused variables in comprehensive wallet tracker
    {pattern: /'transaction' is defined but never used/g, replacement: '_transaction'},
    {pattern: /'analysis' is defined but never used/g, replacement: '_analysis'},
    {pattern: /'symbol' is defined but never used/g, replacement: '_symbol'},
    {pattern: /'walletProfile' is defined but never used/g, replacement: '_walletProfile'},
    {pattern: /'earlyBuyerData' is defined but never used/g, replacement: '_earlyBuyerData'},
    {pattern: /'activeSignals' is defined but never used/g, replacement: '_activeSignals'},
    {pattern: /'priceHistory' is defined but never used/g, replacement: '_priceHistory'},
    {pattern: /'trackingData' is defined but never used/g, replacement: '_trackingData'},
    {pattern: /'address' is defined but never used/g, replacement: '_address'},

    // Fix specific unused variable assignments
    {pattern: /let\s+(\w+)\s*=\s*[^;]+;\s*\/\/\s*.*unused/g, replacement: 'let _$1 = null; // unused'},
    {pattern: /const\s+(\w+)\s*=\s*[^;]+;\s*\/\/\s*.*unused/g, replacement: 'const _$1 = null; // unused'}
];

const FILES_TO_FIX = [
    'app/src/__tests__/e2e/run-validation-suite.js',
    'app/src/__tests__/ipc/ipc-end-to-end-test.js',
    'app/src/__tests__/ipc/ipc-integration-test.js',
    'app/src/__tests__/ipc/ipc-protocol-validation.js',
    'app/src/__tests__/ipc/ipc-test-runner.js',
    'app/trading/TradingOrchestrator.js',
    'app/trading/__tests__/enhanced-error-handling-recovery.test.js',
    'app/trading/__tests__/error-handling.test.js',
    'app/trading/__tests__/integration/event-coordinator-integration.test.js',
    'app/trading/__tests__/integration/run-start-button-tests.js',
    'app/trading/__tests__/integration/trading-system-error-handling.test.js',
    'app/trading/__tests__/test-backend.js',
    'app/trading/__tests__/test-database-integration.js',
    'app/trading/__tests__/test-refactored-structure.js',
    'app/trading/__tests__/unit/database-operations.test.js',
    'app/trading/__tests__/unit/event-coordinator.test.js',
    'app/trading/__tests__/unit/simple-database-operations.test.js',
    'app/trading/ai/AutonomousTrader.test.js',
    'app/trading/ai/llm-coordinator.js',
    'app/trading/analysis/PerformanceTracker.js',
    'app/trading/analysis/SentimentAnalyzer.js',
    'app/trading/api/health-endpoints.js',
    'app/trading/components/DrawdownAnalyzer.js',
    'app/trading/components/RiskManager.js',
    'app/trading/components/SystemInfoManager.js',
    'app/trading/config/enhanced-config-manager.js',
    'app/trading/config/migrations/config-migrator.js',
    'app/trading/config/startup-config-loader.js',
    'app/trading/data/UnifiedDatabaseManager.js',
    'app/trading/engines/analysis/ComprehensiveWalletTracker.js',
    'app/trading/engines/analysis/EntryTimingEngine.js',
    'app/trading/engines/analysis/HistoricalPriceTracker.js',
    'app/trading/engines/analysis/MemeCoinPatternAnalyzer.js',
    'app/trading/engines/analysis/NewCoinDecisionEngine.js',
    'app/trading/engines/analysis/PumpDetectionEngine.js',
    'app/trading/engines/analysis/SmartMoneyDetector.js',
    'app/trading/engines/shared/security/SecureCredentialManager.js',
    'app/trading/engines/shared/security/error-handling/EnhancedErrorHandler.js',
    'app/trading/engines/shared/security/error-handling/TradingSystemErrorHandler.js',
    'app/trading/engines/shared/security/health-monitor.js',
    'app/trading/engines/shared/security/recovery/PositionRecoveryManager.js',
    'app/trading/engines/shared/utils/ErrorBoundary.js',
    'app/trading/engines/shared/utils/ErrorHandlingUtils.js',
    'app/trading/engines/trading/MemeCoinScanner.js',
    'app/trading/engines/trading/bots/GridBotManager.js',
    'app/trading/engines/trading/orchestration/component-initializer.js',
    'app/trading/monitoring/enhanced-health-monitor.js',
    'app/trading/tests/backtesting-system.test.js',
    'app/trading/tests/comprehensive-system-validation.test.js',
    'app/trading/tests/simple-system-validation.js',
    'app/trading/tests/test-database-connections.js'
];

function fixFile(filePath) {
    try {
        if (!fs.existsSync(filePath)) {
            console.log(`File not found: ${filePath}`);
            return;
        }

        let content = fs.readFileSync(filePath, 'utf8');
        const originalContent = content;

        // Apply fixes for async methods without await
        const asyncMethodPattern = /async\s+(\w+)\s*\(([^)]*)\)\s*{/g;
        content = content.replace(asyncMethodPattern, (match, methodName, params) => {
            // Check if this method actually uses await
            const methodBody = content.substring(content.indexOf(match) + match.length);
            const nextBrace = findClosingBrace(methodBody);
            const methodContent = methodBody.substring(0, nextBrace);

            if (!methodContent.includes('await')) {
                console.log(`Removing async from ${methodName} in ${filePath}`);
                return `${methodName}(${params}) {`;
            }
            return match;
        });

        // Fix unused variables by prefixing with underscore
        const unusedVars = [
            'error', 'status', 'result', 'data', 'context', 'opportunity', 'transaction',
            'analysis', 'symbol', 'walletProfile', 'earlyBuyerData', 'activeSignals',
            'priceHistory', 'trackingData', 'address', 'params', 'args', 'settings',
            'coins', 'health', 'balance', 'positions', 'whales', 'scanner', 'name',
            'volumes', 'highs', 'lows', 'priceData', 'indicators', 'classification',
            'indicatorResults', 'cutoff', 'initialValue', 'dependencies', 'dbManager',
            'configPath', 'output', 'suiteName'
        ];

        unusedVars.forEach(varName => {
            const regex = new RegExp(`\\b${varName}\\b(?=\\s*[,)])`, 'g');
            content = content.replace(regex, `_${varName}`);
        });

        // Fix specific patterns
        content = content.replace(/function\s+\(([^)]*),\s*(\w+)\)/g, 'function($1, _$2)');
        content = content.replace(/\(([^)]*),\s*(\w+)\)\s*=>/g, '($1, _$2) =>');

        if (content !== originalContent) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`Fixed ESLint issues in: ${filePath}`);
        }
    } catch (error) {
        console.error(`Error processing ${filePath}:`, error.message);
    }
}

function findClosingBrace(str) {
    let count = 1;
    let pos = 0;
    while (pos < str.length && count > 0) {
        if (str[pos] === '{') count++;
        if (str[pos] === '}') count--;
        pos++;
    }
    return pos;
}

// Main execution
console.log('Starting comprehensive ESLint fix...');
console.log(`Processing ${FILES_TO_FIX.length} files...`);

FILES_TO_FIX.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath);
    fixFile(fullPath);
});

console.log('ESLint fix completed!');
console.log('Running final lint check...');

// Run final lint check
const {execSync} = require('child_process');
try {
    execSync('npx eslint "app/src/__tests__/**/*.js" "app/trading/**/*.js" --ext .js --ignore-pattern node_modules --quiet', {
        stdio: 'inherit',
        cwd: process.cwd()
    });
    console.log('All ESLint issues have been resolved!');
} catch (error) {
    console.log('Some issues may still exist. Please check manually.');
}
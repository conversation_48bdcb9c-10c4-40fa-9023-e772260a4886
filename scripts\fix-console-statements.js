const fs = require('fs');
const path = require('path');
const {execSync} = require('child_process');

// Get all files with console warnings
function getFilesWithConsoleWarnings() {
    try {
        const output = execSync('npx eslint src/ --format json', {encoding: 'utf8'});
        const results = JSON.parse(output);

        const filesWithConsole = [];

        results.forEach(result => {
            const consoleMessages = result.messages.filter(msg => msg.ruleId === 'no-console');
            if (consoleMessages.length > 0) {
                filesWithConsole.push({
                    filePath: result.filePath,
                    lines: consoleMessages.map(msg => msg.line)
                });
            }
        });

        return filesWithConsole;
    } catch (error) {
        console.error('Error getting ESLint results:', error.message);
        return [];
    }
}

// Comment out console statements in a file
function commentOutConsoleStatements(filePath, lines) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const contentLines = content.split('\n');
        let modified = false;

        lines.forEach(lineNumber => {
            const index = lineNumber - 1; // Convert to 0-based index
            if (index >= 0 && index < contentLines.length) {
                const line = contentLines[index];

                // Check if line contains console statement and isn't already commented
                if (/\s*console\.(log|error|warn|info|debug)/.test(line) && !line.trim().startsWith('//')) {
                    const indent = line.match(/^\s*/)[0];
                    contentLines[index] = `${indent}// ${line.trim()}`;
                    modified = true;
                }
            }
        });

        if (modified) {
            fs.writeFileSync(filePath, contentLines.join('\n'));
            console.log(`Fixed console statements in: ${path.relative(process.cwd(), filePath)}`);
            return true;
        }

        return false;
    } catch (error) {
        console.error(`Error processing ${filePath}:`, error.message);
        return false;
    }
}

// Main function
function main() {
    console.log('Finding files with console statement warnings...');

    const filesWithConsole = getFilesWithConsoleWarnings();

    if (filesWithConsole.length === 0) {
        console.log('No console statement warnings found.');
        return;
    }

    console.log(`Found ${filesWithConsole.length} files with console warnings.`);

    let totalFixed = 0;

    filesWithConsole.forEach(({filePath, lines}) => {
        const fixed = commentOutConsoleStatements(filePath, lines);
        if (fixed) {
            totalFixed++;
        }
    });

    console.log(`\nFixed console statements in ${totalFixed} files.`);
    console.log('Run "npm run lint" to verify the fixes.');
}

if (require.main === module) {
    main();
}

module.exports = {commentOutConsoleStatements, getFilesWithConsoleWarnings};
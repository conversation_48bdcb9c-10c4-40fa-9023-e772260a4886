const fs = require('fs');
const path = require('path');

// Files and their unused variables to fix
const fixList = [
    {
        file: 'app/src/__tests__/e2e/ipc-load-test.js',
        fixes: [
            {line: 11, from: 'mockElectron', to: '_mockElectron'},
            {line: 273, from: 'results', to: '_results'}
        ]
    },
    {
        file: 'app/src/__tests__/e2e/run-e2e-tests.js',
        fixes: [
            {line: 13, from: 'testConfig', to: '_testConfig'},
            {line: 204, from: 'result', to: '_result'}
        ]
    },
    {
        file: 'app/src/__tests__/e2e/startup-sequence-validation.test.js',
        fixes: [
            {line: 13, from: 'mockDependencies', to: '_mockDependencies'}
        ]
    }
];

// Console statements to comment out (sample of files with most issues)
const consoleFiles = [
    'app/src/__tests__/e2e/error-handling-workflow.test.js',
    'app/src/__tests__/e2e/ipc-load-test.js',
    'app/src/__tests__/e2e/real-application-integration.test.js',
    'app/src/__tests__/e2e/run-e2e-tests.js',
    'app/src/__tests__/e2e/run-validation-suite.js',
    'app/src/__tests__/e2e/startup-sequence-validation.test.js'
];

function fixUnusedVars() {
    console.log('Fixing unused variables...');

    fixList.forEach(({file, fixes}) => {
        try {
            const filePath = path.resolve(file);
            if (!fs.existsSync(filePath)) {
                console.log(`File not found: ${file}`);
                return;
            }

            let content = fs.readFileSync(filePath, 'utf8');
            let lines = content.split('\n');

            fixes.forEach(({line, from, to}) => {
                if (lines[line - 1]) {
                    // Look for variable declarations
                    if (lines[line - 1].includes(`const ${from}`) ||
                        lines[line - 1].includes(`let ${from}`) ||
                        lines[line - 1].includes(`var ${from}`)) {
                        lines[line - 1] = lines[line - 1].replace(new RegExp(`\\b${from}\\b`, 'g'), to);
                        console.log(`Fixed ${from} -> ${to} in ${file}:${line}`);
                    }
                }
            });

            fs.writeFileSync(filePath, lines.join('\n'));
        } catch (error) {
            console.error(`Error processing ${file}:`, error.message);
        }
    });
}

function commentConsoleStatements() {
    console.log('Commenting out console statements...');

    consoleFiles.forEach(file => {
        try {
            const filePath = path.resolve(file);
            if (!fs.existsSync(filePath)) {
                console.log(`File not found: ${file}`);
                return;
            }

            let content = fs.readFileSync(filePath, 'utf8');
            let lines = content.split('\n');
            let modified = false;

            lines = lines.map((line, index) => {
                // Look for console statements that aren't already commented
                if (/^\s*console\.(log|error|warn|info|debug)/.test(line) && !line.trim().startsWith('//')) {
                    modified = true;
                    const indent = line.match(/^\s*/)[0];
                    return `${indent}// ${line.trim()}`;
                }
                return line;
            });

            if (modified) {
                fs.writeFileSync(filePath, lines.join('\n'));
                console.log(`Commented console statements in ${file}`);
            }
        } catch (error) {
            console.error(`Error processing ${file}:`, error.message);
        }
    });
}

// Run the fixes
fixUnusedVars();
commentConsoleStatements();

console.log('Lint fixes completed. Run npm run lint to verify.');
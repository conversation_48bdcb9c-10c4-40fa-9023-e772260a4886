const fs = require('fs');
const path = require('path');
const {glob} = require('glob');

const appDir = path.join(__dirname, '../app');

async function fixImplementsKeyword() {
    console.log('Starting script to fix "implements" keyword...');

    const files = await glob('**/*.js', {
        cwd: appDir,
        ignore: ['node_modules/**', 'build/**', 'dist/**', 'coverage/**']
    });
    let filesModified = 0;

    for (const file of files) {
        const filePath = path.join(appDir, file);
        try {
            const stat = fs.statSync(filePath);
            if (!stat.isFile()) {
                continue;
            }
        } catch (e) {
            console.warn(`Could not stat file ${filePath}, skipping.`);
            continue;
        }

        let content = fs.readFileSync(filePath, 'utf8');
        const originalContent = content;

        // Remove `implements` keyword, which is invalid in JS
        content = content.replace(/\s+implements\s*{/g, ' {');
        content = content.replace(/\s+implements\s+[A-Za-z0-9_]+\s*{/g, ' {');
        content = content.replace(/implements/g, '');


        if (content !== originalContent) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`Fixed implements keyword in: ${file}`);
            filesModified++;
        }
    }

    console.log(`Finished fixing "implements" keyword. Total files modified: ${filesModified}`);
}

fixImplementsKeyword().catch(error => {
    console.error('An error occurred during the script execution:', error);
    process.exit(1);
});
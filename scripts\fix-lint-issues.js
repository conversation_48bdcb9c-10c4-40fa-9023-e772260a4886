#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const {execSync} = require('child_process');

class ESLintFixer {
    constructor() {
        this.fixes = {
            unusedVars: 0,
            unusedArgs: 0,
            consoleStatements: 0,
            requireAwait: 0
        };
    }

    // Parse lint output and extract issues
    parseLintOutput(lintOutput) {
        const issues = [];
        const lines = lintOutput.split('\n');
        let currentFile = null;

        for (const line of lines) {
            // File path detection
            const fileMatch = line.match(/^(.*\.(js|jsx|ts|tsx))$/);
            if (fileMatch) {
                currentFile = fileMatch[1];
                continue;
            }

            // Issue detection
            const issueMatch = line.match(/^\s+(\d+):(\d+)\s+warning\s+(.*?)\s+([\w-]+)$/);
            if (issueMatch && currentFile) {
                const [, lineNum, colNum, message, ruleId] = issueMatch;
                issues.push({
                    file: currentFile,
                    line: parseInt(lineNum),
                    column: parseInt(colNum),
                    message: message.trim(),
                    ruleId: ruleId.trim()
                });
            }
        }

        return issues;
    }

    // Fix unused variables by prefixing with underscore
    fixUnusedVars(filePath, issues) {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');

        let modified = false;
        const unusedVarIssues = issues.filter(issue =>
            issue.ruleId === 'no-unused-vars' &&
            issue.message.includes('is assigned a value but never used')
        );

        for (const issue of unusedVarIssues) {
            const lineIndex = issue.line - 1;
            const line = lines[lineIndex];

            // Extract variable name from message
            const varMatch = issue.message.match(/'([^']+)' is assigned a value but never used/);
            if (varMatch) {
                const varName = varMatch[1];

                // Skip if already prefixed with underscore
                if (varName.startsWith('_')) continue;

                // Replace variable declaration
                const patterns = [
                    new RegExp(`\\bconst\\s+${varName}\\b`, 'g'),
                    new RegExp(`\\blet\\s+${varName}\\b`, 'g'),
                    new RegExp(`\\bvar\\s+${varName}\\b`, 'g'),
                    new RegExp(`\\b${varName}\\s*=`, 'g')
                ];

                for (const pattern of patterns) {
                    if (pattern.test(line)) {
                        lines[lineIndex] = line.replace(
                            new RegExp(`\\b${varName}\\b`, 'g'),
                            `_${varName}`
                        );
                        modified = true;
                        this.fixes.unusedVars++;
                        break;
                    }
                }
            }
        }

        if (modified) {
            fs.writeFileSync(filePath, lines.join('\n'));
        }
        return modified;
    }

    // Fix unused function parameters by prefixing with underscore
    fixUnusedArgs(filePath, issues) {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');

        let modified = false;
        const unusedArgIssues = issues.filter(issue =>
            issue.ruleId === 'no-unused-vars' &&
            issue.message.includes('is defined but never used')
        );

        for (const issue of unusedArgIssues) {
            const lineIndex = issue.line - 1;
            const line = lines[lineIndex];

            // Extract parameter name from message
            const argMatch = issue.message.match(/'([^']+)' is defined but never used/);
            if (argMatch) {
                const argName = argMatch[1];

                // Skip if already prefixed with underscore
                if (argName.startsWith('_')) continue;

                // Replace function parameter
                const functionParamPattern = new RegExp(
                    `\\b${argName}\\b(?=\\s*[,)])`, 'g'
                );

                if (functionParamPattern.test(line)) {
                    lines[lineIndex] = line.replace(functionParamPattern, `_${argName}`);
                    modified = true;
                    this.fixes.unusedArgs++;
                }
            }
        }

        if (modified) {
            fs.writeFileSync(filePath, lines.join('\n'));
        }
        return modified;
    }

    // Remove or suppress console statements
    fixConsoleStatements(filePath, issues) {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');

        let modified = false;
        const consoleIssues = issues.filter(issue =>
            issue.ruleId === 'no-console'
        );

        for (const issue of consoleIssues) {
            const lineIndex = issue.line - 1;
            const line = lines[lineIndex];

            // For test files, comment out console statements
            if (filePath.includes('test') || filePath.includes('__tests__')) {
                if (!line.trim().startsWith('//')) {
                    const indent = line.match(/^\s*/)[0];
                    lines[lineIndex] = indent + '// ' + line.trim();
                    modified = true;
                    this.fixes.consoleStatements++;
                }
            }
            // For production code, remove console statements entirely
            else if (line.trim().startsWith('console.')) {
                lines[lineIndex] = '';
                modified = true;
                this.fixes.consoleStatements++;
            }
        }

        if (modified) {
            fs.writeFileSync(filePath, lines.join('\n'));
        }
        return modified;
    }

    // Fix async functions without await
    fixRequireAwait(filePath, issues) {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');

        let modified = false;
        const requireAwaitIssues = issues.filter(issue =>
            issue.ruleId === 'require-await'
        );

        for (const issue of requireAwaitIssues) {
            const lineIndex = issue.line - 1;
            const line = lines[lineIndex];

            // If it's a simple return or assignment, remove async
            if (line.includes('async') && !line.includes('await')) {
                // Only remove async if the function is simple
                if (line.match(/async\s+(function|\w+|\()/)) {
                    lines[lineIndex] = line.replace(/async\s+/, '');
                    modified = true;
                    this.fixes.requireAwait++;
                }
            }
        }

        if (modified) {
            fs.writeFileSync(filePath, lines.join('\n'));
        }
        return modified;
    }

    // Process a single file
    processFile(filePath, issues) {
        const fileIssues = issues.filter(issue => issue.file === filePath);
        if (fileIssues.length === 0) return false;

        console.log(`Processing ${filePath} (${fileIssues.length} issues)`);

        let modified = false;
        modified |= this.fixUnusedVars(filePath, fileIssues);
        modified |= this.fixUnusedArgs(filePath, fileIssues);
        modified |= this.fixConsoleStatements(filePath, fileIssues);
        modified |= this.fixRequireAwait(filePath, fileIssues);

        return modified;
    }

    // Main execution
    async run() {
        console.log('🔧 ESLint Auto-Fixer Starting...\n');

        try {
            // Run lint and capture output
            console.log('Running ESLint to identify issues...');
            const _lintOutput = execSync('npm run lint', {
                encoding: 'utf8',
                stdio: 'pipe'
            });

            console.log('No lint issues found!');
            return;

        } catch (error) {
            // ESLint exits with non-zero code when issues are found
            const lintOutput = error.stdout || error.stderr || error.message;
            console.log('Captured lint output length:', lintOutput.length);

            // Parse issues
            const issues = this.parseLintOutput(lintOutput);
            console.log(`Found ${issues.length} total issues to fix\n`);

            // Group issues by file
            const fileGroups = {};
            issues.forEach(issue => {
                if (!fileGroups[issue.file]) {
                    fileGroups[issue.file] = [];
                }
                fileGroups[issue.file].push(issue);
            });

            // Process each file
            const filePaths = Object.keys(fileGroups);
            let processedFiles = 0;

            for (const filePath of filePaths) {
                if (fs.existsSync(filePath)) {
                    if (this.processFile(filePath, issues)) {
                        processedFiles++;
                    }
                }
            }

            // Summary
            console.log('\n📊 Fix Summary:');
            console.log(`  • Unused variables fixed: ${this.fixes.unusedVars}`);
            console.log(`  • Unused arguments fixed: ${this.fixes.unusedArgs}`);
            console.log(`  • Console statements handled: ${this.fixes.consoleStatements}`);
            console.log(`  • Async functions fixed: ${this.fixes.requireAwait}`);
            console.log(`  • Files modified: ${processedFiles}`);

            // Run lint again to see remaining issues
            console.log('\n🔍 Running lint again to check remaining issues...');
            try {
                execSync('npm run lint', {stdio: 'inherit'});
                console.log('✅ All issues fixed!');
            } catch (_remainingError) {
                console.log('⚠️  Some issues remain - may require manual fixing');
            }
        }
    }
}

// Run if called directly
if (require.main === module) {
    const fixer = new ESLintFixer();
    fixer.run().catch(console.error);
}

module.exports = ESLintFixer;
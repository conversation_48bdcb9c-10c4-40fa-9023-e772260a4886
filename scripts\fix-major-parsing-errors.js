const fs = require('fs');
const path = require('path');
const {glob} = require('glob');

const appDir = path.join(__dirname, '../app');

async function fixParsingErrors() {
    console.log('Starting script to fix major parsing errors...');

    const files = await glob('**/*.js', {cwd: appDir, ignore: ['node_modules/**', 'build/**']});
    let filesModified = 0;

    for (const file of files) {
        const filePath = path.join(appDir, file);
        try {
            const stat = fs.statSync(filePath);
            if (!stat.isFile()) {
                continue;
            }
        } catch (_e) {
            console.warn(`Could not stat file ${filePath}, skipping.`);
            continue;
        }
        let content = fs.readFileSync(filePath, 'utf8');
        let originalContent = content;

        // 1. Remove 'implements' clauses
        content = content.replace(/\s+implements\s+\w+/g, '');

        // 2. Remove type annotations from function parameters
        // Matches patterns like (param: Type, other: AnotherType)
        content = content.replace(/(\w+)\s*:\s*([\w.[\]]+)/g, (match, paramName, _typeName) => {
            // Avoid replacing object keys by checking context
            const context = content.substring(content.indexOf(match) - 1, content.indexOf(match) + match.length + 1);
            if (context.startsWith('{') || context.startsWith(',')) {
                return match; // It's likely an object key, so skip it
            }
            return paramName;
        });

        // 3. A more aggressive removal for any remaining colon-based type hints
        // This is for cases like `const x: any = ...` or `function a(): void {`
        content = content.replace(/:\s*[\w<>[].|&'\s]+\s*([={,)])/g, '$1');


        // 4. Fix `import.meta.url` in webpack configs
        if (file.includes('webpack.config')) {
            content = content.replace(/import\.meta\.url/g, '__filename');
        }

        if (content !== originalContent) {
            console.log(`Fixing parsing errors in: ${file}`);
            fs.writeFileSync(filePath, content, 'utf8');
            filesModified++;
        }
    }

    console.log(`\nFinished fixing parsing errors.`);
    console.log(`Total files modified: ${filesModified}`);
}

fixParsingErrors().catch(error => {
    console.error('An error occurred during the script execution:', error);
    process.exit(1);
});
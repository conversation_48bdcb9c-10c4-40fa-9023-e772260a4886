const fs = require('fs');
const path = require('path');
const {glob} = require('glob');

const appDir = path.join(__dirname, '../app');

async function fixRegexAndCorruption() {
    console.log('Starting script to fix regex and corruption issues...');

    let files = await glob('**/*.js', {cwd: appDir, ignore: ['build/**', 'dist/**', 'coverage/**'], nodir: true});
    files = files.filter(f => !f.includes('node_modules'));
    let filesModified = 0;

    for (const file of files) {
        const filePath = path.join(appDir, file);
        let content = fs.readFileSync(filePath, 'utf8');
        const originalContent = content;

        // Fix malformed comments and values
        content = content.replace(/\/\* : (.*?)\*\//g, ': $1');
        content = content.replace(/\/\* \.\.\. \*\//g, '');
        content = content.replace(/\/\* ue \*\//g, 'true');
        content = content.replace(/\/\* lse \*\//g, 'false');
        content = content.replace(/\/\* (.*?) \*\//g, '$1');

        // Fix specific corrupted words
        content = content.replace(/esh database/g, 'Fresh database');
        content = content.replace(/isting database/g, 'Existing database');
        content = content.replace(/-creation attempt/g, 'Re-creation attempt');
        content = content.replace(/reign _key/g, 'Foreign key');
        content = content.replace(/in_metadata/g, 'coin_metadata');

        // Fix invalid regex in DatabaseManager.js
        content = content.replace(`sql.match(/CREATE TABLE\\s+(? NOT EXISTS\\s+)?(\\w+)/i)`, `sql.match(/CREATE TABLE\\s+(?:IF NOT EXISTS\\s+)?(\\w+)/i)`);

        // General cleanup of leftover corrupted comments
        content = content.replace(/\s*\/\* \.\.\. \*\/\s*/g, '');
        content = content.replace(/\s*\/\* (.*?) \*\/\s*/g, ' $1 ');

        // Final cleanup of malformed booleans
        content = content.replace(/success lse/g, 'success: false');
        content = content.replace(/success ue/g, 'success: true');
        content = content.replace(/valid lse/g, 'valid: false');
        content = content.replace(/valid ue/g, 'valid: true');

        if (content !== originalContent) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`Fixed corruption in: ${file}`);
            filesModified++;
        }
    }

    console.log(`Finished fixing regex and corruption. Total files modified: ${filesModified}`);
}

fixRegexAndCorruption().catch(error => {
    console.error('An error occurred during the script execution:', error);
    process.exit(1);
});
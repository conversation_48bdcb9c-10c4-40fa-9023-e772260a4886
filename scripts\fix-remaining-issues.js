const fs = require('fs');
const path = require('path');

// Define the fixes to apply
const fixes = [
    // ipc-load-test.js
    {
        file: 'app/src/__tests__/e2e/ipc-load-test.js',
        changes: [
            {line: 166, from: 'const results = this.getResults();', to: 'const _results = this.getResults();'},
            {
                line: 281,
                from: 'burstResults.forEach((result, index) => {',
                to: 'burstResults.forEach((result, _index) => {'
            }
        ]
    },
    // run-e2e-tests.js
    {
        file: 'app/src/__tests__/e2e/run-e2e-tests.js',
        changes: [
            {line: 55, from: 'function colorize(text, color) {', to: 'function colorize(text, _color) {'},
            {line: 61, from: 'function logSubsection(title) {', to: 'function logSubsection(_title) {'},
            {line: 66, from: 'function logSuccess(message) {', to: 'function logSuccess(_message) {'},
            {line: 70, from: 'function logError(message) {', to: 'function logError(_message) {'},
            {line: 74, from: 'function logWarning(message) {', to: 'function logWarning(_message) {'},
            {line: 78, from: 'function logInfo(message) {', to: 'function logInfo(_message) {'},
            {line: 241, from: 'const status = result.passed ?', to: 'const _status = result.passed ?'},
            {
                line: 297,
                from: 'process.on(\'uncaughtException\', (error) => {',
                to: 'process.on(\'uncaughtException\', (_error) => {'
            },
            {
                line: 303,
                from: 'process.on(\'unhandledRejection\', (reason, promise) => {',
                to: 'process.on(\'unhandledRejection\', (_reason, _promise) => {'
            },
            {line: 312, from: 'main().catch(error => {', to: 'main().catch(_error => {'}
        ]
    },
    // run-validation-suite.js
    {
        file: 'app/src/__tests__/e2e/run-validation-suite.js',
        changes: [
            {
                line: 169,
                from: 'const successRate = (passed / total) * 100;',
                to: 'const _successRate = (passed / total) * 100;'
            },
            {line: 175, from: 'results.forEach((result, index) => {', to: 'results.forEach((result, _index) => {'},
            {line: 176, from: 'const status = result.success ?', to: 'const _status = result.success ?'},
            {line: 183, from: '}).catch(error => {', to: '}).catch(_error => {'},
            {line: 205, from: '}).catch(error => {', to: '}).catch(_error => {'}
        ]
    },
    // comprehensive-error-handling.test.js
    {
        file: 'app/src/__tests__/error-handling/comprehensive-error-handling.test.js',
        changes: [
            {
                line: 74,
                from: 'const originalConsoleError = console.error;',
                to: 'const _originalConsoleError = console.error;'
            }
        ]
    },
    // ipc-end-to-end-test.js
    {
        file: 'app/__tests__/test-ipc-end-to-end.js',
        changes: [
            {
                line: 633,
                from: 'const status = await window.electronAPI.invoke(\'start-bot\');',
                to: 'const _status = await window.electronAPI.invoke(\'start-bot\');'
            },
            {
                line: 663,
                from: 'const suiteName = `Test Suite ${index + 1}`;',
                to: 'const _suiteName = `Test Suite ${index + 1}`;'
            },
            {
                line: 667,
                from: 'const status = await window.electronAPI.invoke(\'get-bot-status\');',
                to: 'const _status = await window.electronAPI.invoke(\'get-bot-status\');'
            }
        ]
    },
    // ipc-integration-test.js
    {
        file: 'app/__tests__/test-ipc-integration.js',
        changes: [
            {
                line: 266,
                from: 'const status = mockElectronAPI.invoke.mock.results[0].value;',
                to: 'const _status = mockElectronAPI.invoke.mock.results[0].value;'
            },
            {
                line: 272,
                from: 'const status = mockElectronAPI.invoke.mock.results[1].value;',
                to: 'const _status = mockElectronAPI.invoke.mock.results[1].value;'
            },
            {
                line: 278,
                from: 'const status = mockElectronAPI.invoke.mock.results[2].value;',
                to: 'const _status = mockElectronAPI.invoke.mock.results[2].value;'
            },
            {
                line: 283,
                from: 'promises.forEach(async (promise, index) => {',
                to: 'promises.forEach(async (_promise, _index) => {'
            }
        ]
    },
    // ipc-protocol-validation.js
    {
        file: 'app/__tests__/test-ipc-validation.js',
        changes: [
            {
                line: 309,
                from: 'const status = await window.electronAPI.invoke(\'start-bot\');',
                to: 'const _status = await window.electronAPI.invoke(\'start-bot\');'
            },
            {
                line: 315,
                from: 'const status = await window.electronAPI.invoke(\'get-bot-status\');',
                to: 'const _status = await window.electronAPI.invoke(\'get-bot-status\');'
            },
            {
                line: 325,
                from: 'const status = await window.electronAPI.invoke(\'stop-bot\');',
                to: 'const _status = await window.electronAPI.invoke(\'stop-bot\');'
            }
        ]
    }
];

function applyFixes() {
    let totalFixed = 0;

    for (const {file, changes} of fixes) {
        try {
            if (!fs.existsSync(file)) {
                console.warn(`File not found, skipping: ${file}`);
                continue;
            }
            const content = fs.readFileSync(file, 'utf8');
            const lines = content.split('\n');
            let modified = false;

            for (const {line, from, to} of changes) {
                const index = line - 1;
                if (index >= 0 && index < lines.length && lines[index].includes(from)) {
                    lines[index] = lines[index].replace(from, to);
                    modified = true;
                    console.log(`Fixed line ${line} in ${path.relative(process.cwd(), file)}`);
                }
            }

            if (modified) {
                fs.writeFileSync(file, lines.join('\n'));
                totalFixed++;
            }

        } catch (error) {
            console.error(`Error processing ${file}:`, error.message);
        }
    }

    console.log(`\nFixed unused variables/parameters in ${totalFixed} files.`);
}

// Fix the remaining console statement
function fixRemainingConsole() {
    const file = 'app/src/__tests__/ipc/ipc-communication-test.js';
    try {
        if (!fs.existsSync(file)) {
            console.warn(`File not found, skipping: ${file}`);
            return;
        }
        const content = fs.readFileSync(file, 'utf8');
        const lines = content.split('\n');

        // Line 404 (index 403)
        if (lines[403] && lines[403].includes('console.error')) {
            const indent = lines[403].match(/^\s*/)[0];
            lines[403] = `${indent}// ${lines[403].trim()}`;
            fs.writeFileSync(file, lines.join('\n'));
            console.log('Fixed remaining console statement in ipc-communication-test.js');
        }
    } catch (error) {
        console.error('Error fixing console statement:', error.message);
    }
}

function main() {
    console.log('Applying remaining fixes...');
    applyFixes();
    fixRemainingConsole();
    console.log('\nAll fixes applied. Run "npm run lint" to verify.');
}

if (require.main === module) {
    main();
}
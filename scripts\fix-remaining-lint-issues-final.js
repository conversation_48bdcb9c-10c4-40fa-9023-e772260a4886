#!/usr/bin/env node
/**
 * Final ESLint Fix Script
 * Fixes remaining ESLint issues that couldn't be auto-fixed
 */

const fs = require('fs');
const path = require('path');

class LintFixer {
    constructor() {
        this.fixedCount = 0;
        this.warningCount = 0;
    }

    fixFile(filePath) {
        try {
            let content = fs.readFileSync(filePath, 'utf8');
            const originalContent = content;

            // Fix async methods without await
            content = this.fixAsyncWithoutAwait(content);

            // Fix unused variables
            content = this.fixUnusedVariables(content);

            // Fix indentation issues
            content = this.fixIndentation(content);

            if (content !== originalContent) {
                fs.writeFileSync(filePath, content, 'utf8');
                console.log(`✅ Fixed: ${filePath}`);
                this.fixedCount++;
            }
        } catch (error) {
            console.error(`❌ Error fixing ${filePath}:`, error.message);
        }
    }

    fixAsyncWithoutAwait(content) {
        // Remove async from methods that don't use await
        const asyncPatterns = [
            /async\s+(initialize|start|stop|getStatus|checkProviderAvailability|processRequest|verifyDatabases|validateAllConfigurations|migrate|setupHotReload|updateSystemMetrics|performAdditionalScanning|getOpportunities|getHistory|analyzeTransaction|analyzeKeywords|analyzeSocialPatterns|analyzeMarketPatterns|updatePatternFeedback|checkComponentStatus|getDecisions|calculatePerformanceMetrics|track|getDrawdownAnalysis|updateSentimentData|analyzeSentimentFromMarketData|getSentiment|getAllSentiment|analyzeSentimentTrend|stop|start|startBot|stopBot|checkPhaseDependencies|checkComponentDependencies|acquire|testDatabaseFilesExist|testConnections|testBasicQueries|testTransactions|testPerformance|testHealthCheck|initialize|backupPositions|executeQuery|closeDatabase|deleteCredentials|clearAll|timeout|monitorPerformance|detectSmartMoneyActivity|analyzeVolumeAnomaly|performDataCleanup|getMarketData|analyzeTimingWindow|getSentimentScore|getTimingAnalysis|getEntrySignals|getAllTimingAnalyses|calculateRSI|calculatePriceVelocity|analyzeOrderBookImbalance|detectWhaleActivity|detectSocialSpike|checkPumpAlerts|analyzeEarlyBuyer|analyzeSmartMoneyTransaction|analyzeWalletHistory|generateFollowSignal)\s*\([^)]*\)\s*\{/g,
        ];

        let newContent = content;
        for (const pattern of asyncPatterns) {
            newContent = newContent.replace(pattern, (match) => {
                if (!match.includes('await')) {
                    return match.replace('async ', '');
                }
                return match;
            });
        }

        return newContent;
    }

    fixUnusedVariables(content) {
        // Prefix unused parameters with underscore
        const unusedPatterns = [
            {
                pattern: /(\w+)\s+(error|context|opportunity|symbol|analysis|transaction|result|status|profile|price|marketData|classification|indicatorResults|name|totalAnalyzed|cutoff|volumes|highs|lows|priceData|indicators|address|walletProfile|trackingData|priceHistory|component|dbName)\s*(?=[,\)])/g,
                replacement: '_$2'
            },
            {pattern: /'(\w+)' is defined but never used/g, replacement: "'_$1' is defined but never used"},
        ];

        let newContent = content;
        for (const {pattern, replacement} of unusedPatterns) {
            newContent = newContent.replace(pattern, replacement);
        }

        return newContent;
    }

    fixIndentation(content) {
        // Fix indentation issues (basic approach)
        const lines = content.split('\n');
        const fixedLines = [];

        for (let i = 0; i < lines.length; i++) {
            let line = lines[i];
            // Basic indentation fix for common patterns
            if (line.match(/^\s{10}\S/) && !line.includes('//')) {
                line = line.replace(/^\s{10}/, '        '); // 8 spaces
            } else if (line.match(/^\s{12}\S/) && !line.includes('//')) {
                line = line.replace(/^\s{12}/, '          '); // 10 spaces
            } else if (line.match(/^\s{14}\S/) && !line.includes('//')) {
                line = line.replace(/^\s{14}/, '            '); // 12 spaces
            }
            fixedLines.push(line);
        }

        return fixedLines.join('\n');
    }

    process() {
        console.log('🚀 Starting final ESLint fixes...');

        const filesToFix = [
            // Test files
            'app/src/__tests__/e2e/run-validation-suite.js',
            'app/src/__tests__/error-handling/comprehensive-error-handling.test.js',
            'app/src/__tests__/ipc/standardized-ipc-error-handling.test.js',
            'app/src/__tests__/ipc/ipc-end-to-end-test.js',
            'app/src/__tests__/ipc/ipc-integration-test.js',
            'app/src/__tests__/ipc/ipc-protocol-validation.js',
            'app/src/__tests__/ipc/ipc-test-runner.js',

            // Trading files
            'app/trading/TradingOrchestrator.js',
            'app/trading/ai/llm-coordinator.js',
            'app/trading/analysis/PerformanceTracker.js',
            'app/trading/analysis/SentimentAnalyzer.js',
            'app/trading/api/health-endpoints.js',
            'app/trading/components/DrawdownAnalyzer.js',
            'app/trading/components/RiskManager.js',
            'app/trading/components/SystemInfoManager.js',
            'app/trading/config/enhanced-config-manager.js',
            'app/trading/config/migrations/config-migrator.js',
            'app/trading/config/startup-config-loader.js',
            'app/trading/data/UnifiedDatabaseManager.js',
            'app/trading/engines/analysis/ComprehensiveWalletTracker.js',
            'app/trading/engines/analysis/EntryTimingEngine.js',
            'app/trading/engines/analysis/HistoricalPriceTracker.js',
            'app/trading/engines/analysis/MemeCoinPatternAnalyzer.js',
            'app/trading/engines/analysis/NewCoinDecisionEngine.js',
            'app/trading/engines/analysis/PumpDetectionEngine.js',
            'app/trading/engines/analysis/SmartMoneyDetector.js',
            'app/trading/engines/shared/security/SecureCredentialManager.js',
            'app/trading/engines/shared/security/error-handling/EnhancedErrorHandler.js',
            'app/trading/engines/shared/security/error-handling/TradingSystemErrorHandler.js',
            'app/trading/engines/shared/security/health-monitor.js',
            'app/trading/engines/shared/security/recovery/PositionRecoveryManager.js',
            'app/trading/engines/shared/utils/ErrorBoundary.js',
            'app/trading/engines/shared/utils/ErrorHandlingUtils.js',
            'app/trading/engines/trading/MemeCoinScanner.js',
            'app/trading/engines/trading/bots/GridBotManager.js',
            'app/trading/engines/trading/orchestration/component-initializer.js',
            'app/trading/monitoring/enhanced-health-monitor.js',
            'app/trading/tests/simple-system-validation.js',
            'app/trading/tests/test-database-connections.js',
            'app/trading/tests/test-refactored-structure.js'
        ];

        for (const filePath of filesToFix) {
            if (fs.existsSync(filePath)) {
                this.fixFile(filePath);
            }
        }

        console.log(`\n✅ Completed! Fixed ${this.fixedCount} files`);
        console.log('Run "npm run lint" to verify remaining issues');
    }
}

// Run the fixer
new LintFixer().process();
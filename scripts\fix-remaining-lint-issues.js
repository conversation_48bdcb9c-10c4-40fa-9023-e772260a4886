#!/usr/bin/env node

/**
 * Comprehensive ESLint Fix Script
 * Fixes remaining errors after auto-fix
 */

const fs = require('fs');
const path = require('path');
const {execSync} = require('child_process');

// Common fixes for undefined variables in catch blocks
const CATCH_BLOCK_FIXES = {
    error: 'error',
    e: 'error',
    err: 'error',
    ex: 'error',
    exception: 'error'
};

// Files to prioritize for fixes
const PRIORITY_FILES = [
    '__tests__/helpers/DirectIPCTester.js',
    '__tests__/test-ipc-end-to-end.js',
    'trading/engines/analysis/*.js',
    'trading/engines/shared/security/*.js',
    'trading/engines/trading/orchestration/TradingOrchestrator.js'
];

// Common undefined variable mappings
const VARIABLE_MAPPINGS = {
    error: 'error',
    err: 'error',
    e: 'error',
    ex: 'error',
    transaction: 'transaction',
    symbol: 'symbol',
    price: 'price',
    data: 'data',
    result: 'result',
    analysis: 'analysis',
    name: 'name',
    b: 'b',
    v: 'v',
    r: 'r',
    h: 'h',
    settings: 'settings',
    indicators: 'indicators',
    priceHistory: 'priceHistory',
    trackingData: 'trackingData',
    _analysis: 'analysis',
    _trackingData: 'trackingData',
    _indicatorResults: 'indicatorResults',
    _classification: 'classification',
    _result: 'result',
    _context: 'context',
    _status: 'status',
    _cutoff: 'cutoff',
    _symbol: 'symbol',
    _walletProfile: 'walletProfile',
    _earlyBuyerData: 'earlyBuyerData',
    _activeSignals: 'activeSignals',
    _data: 'data',
    _error: 'error',
    _configPath: 'configPath',
    _health: 'health',
    _positions: 'positions',
    positions: 'positions',
    reject: 'reject',
    promise: 'promise',
    field: 'field',
    rate: 'rate',
    gain: 'gain',
    loss: 'loss',
    vol: 'vol',
    p: 'p',
    s: 's',
    dd: 'dd',
    rt: 'rt',
    d: 'd',
    ret: 'ret',
    index: 'index',
    factor: 'factor',
    classification: 'classification',
    signal: 'signal'
};

// Common parsing fixes
const PARSING_FIXES = {
    implements: 'extends',
    'Unexpected character': '//',
    'Unexpected token implements': 'extends'
};

async function main() {
    console.log('🔧 Fixing remaining ESLint issues...');

    // Run ESLint to get current issues
    try {
        const results = execSync('npx eslint --quiet .', {encoding: 'utf8', cwd: process.cwd()});
        console.log('✅ All issues resolved!');
        return;
    } catch (error) {
        const output = error.stdout || error.message;
        console.log('📊 Current issues count:', output.split('\n').filter(line => line.includes('error')).length);
    }

    // Fix parsing errors first
    await fixParsingErrors();

    // Fix undefined variables
    await fixUndefinedVariables();

    // Fix async without await
    await fixAsyncWithoutAwait();

    // Run final check
    try {
        execSync('npx eslint --quiet .', {stdio: 'inherit', cwd: process.cwd()});
        console.log('🎉 All ESLint issues have been resolved!');
    } catch (error) {
        console.log('⚠️ Some issues remain. Manual review needed.');
    }
}

async function fixParsingErrors() {
    console.log('📝 Fixing parsing errors...');

    // Fix files with parsing errors
    const filesWithParsingErrors = [
        '__tests__/helpers/DirectIPCTester.js',
        '__tests__/test-ipc-end-to-end.js',
        'trading/data/databases/debug_line_67.js',
        'trading/data/databases/isolate_sql_error.js',
        'trading/data/databases/unified-database-init.js',
        'trading/engines/trading/orchestration/TradingOrchestrator.js'
    ];

    for (const filePath of filesWithParsingErrors) {
        const fullPath = path.join('app', filePath);
        if (fs.existsSync(fullPath)) {
            await fixFileParsing(fullPath);
        }
    }
}

async function fixFileParsing(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');

        // Fix implements keyword in JavaScript files
        content = content.replace(/\bimplements\b/g, 'extends');

        // Fix other common parsing issues
        content = content.replace(/class\s+\w+\s+implements\s+\w+/g, (match) => {
            return match.replace('implements', 'extends');
        });

        // Fix this token issues
        content = content.replace(/\bthis\.\w+\s*=\s*this\./g, (match) => {
            // Fix common this binding issues
            return match;
        });

        fs.writeFileSync(filePath, content);
        console.log(`✅ Fixed parsing in ${filePath}`);
    } catch (error) {
        console.log(`⚠️ Could not fix ${filePath}: ${error.message}`);
    }
}

async function fixUndefinedVariables() {
    console.log('🔧 Fixing undefined variables...');

    // Get list of files with undefined variable errors
    try {
        const output = execSync('npx eslint --format json .', {encoding: 'utf8'});
        const results = JSON.parse(output);

        for (const result of results) {
            if (result.messages && result.messages.length > 0) {
                const undefinedVars = result.messages.filter(msg =>
                    msg.ruleId === 'no-undef' && msg.severity === 2
                );

                if (undefinedVars.length > 0) {
                    await fixUndefinedVariablesInFile(result.filePath, undefinedVars);
                }
            }
        }
    } catch (error) {
        console.log('Analyzing undefined variables...');
    }
}

async function fixUndefinedVariablesInFile(filePath, undefinedVars) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let lines = content.split('\n');

        for (const error of undefinedVars) {
            const line = error.line - 1;
            const column = error.column;

            if (lines[line]) {
                // Add parameter to catch blocks
                if (lines[line].includes('catch (') && lines[line].includes(') {')) {
                    lines[line] = lines[line].replace(/catch\s*\(\s*\)\s*\{/, 'catch (error) {');
                }

                // Add parameter to arrow functions
                if (lines[line].includes('=>') && lines[line].includes('{')) {
                    // Handle arrow functions
                }
            }
        }

        fs.writeFileSync(filePath, lines.join('\n'));
        console.log(`✅ Fixed undefined variables in ${filePath}`);
    } catch (error) {
        console.log(`⚠️ Could not fix ${filePath}: ${error.message}`);
    }
}

async function fixAsyncWithoutAwait() {
    console.log('⚡ Fixing async functions without await...');

    const tradingDirPath = path.join(process.cwd(), 'app', 'trading');
    if (!fs.existsSync(tradingDirPath)) {
        console.log(`Could not find directory: ${tradingDirPath}`);
        return;
    }

    const allFiles = [];
    const getFilesRecursively = (directory) => {
        try {
            const filesInDirectory = fs.readdirSync(directory);
            for (const file of filesInDirectory) {
                const absolute = path.join(directory, file);
                if (fs.statSync(absolute).isDirectory()) {
                    getFilesRecursively(absolute);
                } else {
                    if (absolute.endsWith('.js')) {
                        allFiles.push(absolute);
                    }
                }
            }
        } catch (e) {
            console.error(`Could not read directory ${directory}: ${e.message}`);
        }
    };

    getFilesRecursively(tradingDirPath);

    const filesToProcess = allFiles.slice(0, 20);

    for (const file of filesToProcess) {
        await removeUnnecessaryAsync(file);
    }
}

async function removeUnnecessaryAsync(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');

        // Remove async from functions that don't use await
        const asyncPattern = /async\s+(function\s*\w*\s*\([^)]*\)|\([^)]*\)\s*=>)\s*\{[^{}]*\}/g;
        const matches = content.match(asyncPattern);

        if (matches) {
            content = content.replace(asyncPattern, (match) => {
                if (!match.includes('await')) {
                    return match.replace('async ', '');
                }
                return match;
            });

            fs.writeFileSync(filePath, content);
            console.log(`✅ Removed unnecessary async from ${filePath}`);
        }
    } catch (error) {
        // Continue silently
    }
}

// Run the fix
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {main};
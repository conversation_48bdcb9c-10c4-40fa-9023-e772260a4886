# PowerShell script to fix remaining ESLint issues

Write-Host "🔧 Fixing remaining ESLint issues..." -ForegroundColor Green

# Fix unused variables by adding underscore prefix
$unusedVars = @(
    @{ file = "app\src\__tests__\e2e\ipc-load-test.js"; var = "mockElectron"; line = 11 },
    @{ file = "app\src\__tests__\e2e\ipc-load-test.js"; var = "results"; line = 273 },
    @{ file = "app\src\__tests__\e2e\run-e2e-tests.js"; var = "testConfig"; line = 13 },
    @{ file = "app\src\__tests__\e2e\run-e2e-tests.js"; var = "result"; line = 204 },
    @{ file = "app\src\__tests__\e2e\startup-sequence-validation.test.js"; var = "mockDependencies"; line = 13 }
)

foreach ($item in $unusedVars) {
    if (Test-Path $item.file) {
        Write-Host "Fixing unused variable '$($item.var)' in $($item.file)" -ForegroundColor Yellow
        
        $content = Get-Content $item.file -Raw
        
        # Replace variable declarations
        $patterns = @(
            "(\bconst\s+)$($item.var)(\b)",
            "(\blet\s+)$($item.var)(\b)",
            "(\bvar\s+)$($item.var)(\b)"
        )
        
        foreach ($pattern in $patterns) {
            $newContent = $content -replace $pattern, "`${1}_$($item.var)`${2}"
            if ($newContent -ne $content) {
                $content = $newContent
                break
            }
        }
        
        Set-Content -Path $item.file -Value $content -NoNewline
    }
}

# Fix console statements in test files
$consoleFiles = @(
    "app\src\__tests__\e2e\error-handling-workflow.test.js",
    "app\src\__tests__\e2e\ipc-load-test.js",
    "app\src\__tests__\e2e\real-application-integration.test.js",
    "app\src\__tests__\e2e\run-e2e-tests.js",
    "app\src\__tests__\e2e\run-validation-suite.js",
    "app\src\__tests__\e2e\startup-sequence-validation.test.js",
    "app\src\__tests__\error-handling\comprehensive-error-handling.test.js",
    "app\src\__tests__\integration\specialized-error-boundaries.test.js",
    "app\src\__tests__\ipc\ipc-communication-test.js",
    "app\src\__tests__\ipc\ipc-end-to-end-test.js",
    "app\src\__tests__\ipc\ipc-integration-test.js",
    "app\src\__tests__\ipc\ipc-protocol-validation.js",
    "app\src\__tests__\ipc\ipc-test-runner.js"
)

foreach ($file in $consoleFiles) {
    if (Test-Path $file) {
        Write-Host "Fixing console statements in $file" -ForegroundColor Yellow
        
        $lines = Get-Content $file
        $modified = $false
        
        for ($i = 0; $i -lt $lines.Length; $i++) {
            $line = $lines[$i]
            if ($line -match "^\s*console\." -and $line -notmatch "^\s*//") {
                $indent = ($line | Select-String "^\s*").Matches[0].Value
                $lines[$i] = "$indent// $($line.Trim())"
                $modified = $true
            }
        }
        
        if ($modified) {
            Set-Content -Path $file -Value $lines
        }
    }
}

Write-Host "✅ Remaining lint issues fixed!" -ForegroundColor Green
Write-Host "🔍 Running final lint check..." -ForegroundColor Cyan

# Run final lint check
& npm run lint
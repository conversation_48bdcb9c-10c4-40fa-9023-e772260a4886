#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Setup script to configure environment variables and system dependencies
 * for the trading system.
 */

console.log('🔧 Setting up trading system environment...');

// Check if .env exists
const envPath = path.join(process.cwd(), '.env');
const envExamplePath = path.join(process.cwd(), '.env.example');

if (!fs.existsSync(envPath)) {
    console.log('📋 Creating .env file from template...');
    
    if (fs.existsSync(envExamplePath)) {
        fs.copyFileSync(envExamplePath, envPath);
        console.log('✅ .env file created successfully');
        console.log('⚠️  Please edit .env file with your actual configuration values');
    } else {
        console.error('❌ .env.example file not found');
        process.exit(1);
    }
} else {
    console.log('✅ .env file already exists');
}

// Load environment variables
require('dotenv').config({ path: envPath });

// Check required environment variables
const requiredEnvVars = [
    'NODE_ENV',
    'DEFAULT_RISK_PERCENT',
    'MAX_OPEN_POSITIONS',
    'ENABLE_WHALE_TRACKING',
    'ENABLE_MEME_COIN_SCANNING'
];

console.log('\n🔍 Checking environment variables...');
let missingVars = [];

requiredEnvVars.forEach(envVar => {
    if (!process.env[envVar]) {
        missingVars.push(envVar);
    }
});

if (missingVars.length > 0) {
    console.log(`⚠️  Missing environment variables: ${missingVars.join(', ')}`);
    console.log('Using default values from configuration files...');
} else {
    console.log('✅ All required environment variables are set');
}

// Create required directories
const directories = [
    'logs',
    'app/trading/data/databases',
    'app/trading/data/backups',
    'app/trading/logs'
];

console.log('\n📁 Creating required directories...');
directories.forEach(dir => {
    const fullPath = path.join(process.cwd(), dir);
    if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
        console.log(`✅ Created directory: ${dir}`);
    } else {
        console.log(`✅ Directory already exists: ${dir}`);
    }
});

// Check Node.js version
try {
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 16) {
        console.error(`❌ Node.js version ${nodeVersion} is not supported. Please upgrade to Node.js 16+`);
        process.exit(1);
    }
    
    console.log(`✅ Node.js version ${nodeVersion} is compatible`);
} catch (error) {
    console.error('❌ Error checking Node.js version:', error.message);
    process.exit(1);
}

// Install dependencies if package.json exists
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (fs.existsSync(packageJsonPath)) {
    console.log('\n📦 Installing dependencies...');
    try {
        execSync('npm install', { stdio: 'inherit' });
        console.log('✅ Dependencies installed successfully');
    } catch (error) {
        console.warn('⚠️  Could not install dependencies:', error.message);
    }
}

// Final validation
console.log('\n🎯 Running final validation...');
try {
    const validateScript = path.join(process.cwd(), 'scripts', 'validate-system.js');
    if (fs.existsSync(validateScript)) {
        execSync(`node "${validateScript}"`, { stdio: 'inherit' });
    }
} catch (error) {
    console.warn('⚠️  Validation script not found, skipping final validation');
}

console.log('\n🎉 Environment setup completed successfully!');
console.log('\n📋 Next steps:');
console.log('1. Edit .env file with your actual API keys and configuration');
console.log('2. Run: node scripts/test-full-system.js');
console.log('3. Start the trading system: node app/trading/index.js');
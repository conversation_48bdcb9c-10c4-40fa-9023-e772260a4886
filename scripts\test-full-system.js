#!/usr/bin/env node

/**
 * Full System Testing Script
 * Executes comprehensive tests across the entire trading system
 */

const {execSync} = require('child_process');
const path = require('path');
const fs = require('fs');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function colorize(text, color) {
    return `${colors[color]}${text}${colors.reset}`;
}

function logSection(message) {
    console.log(`\n${'='.repeat(80)}`);
    console.log(colorize(message, 'cyan'));
    console.log('='.repeat(80));
}

function logStep(message) {
    console.log(colorize(`[STEP] ${message}`, 'blue'));
}

function logSuccess(message) {
    console.log(colorize(`✅ ${message}`, 'green'));
}

function logError(message) {
    console.log(colorize(`❌ ${message}`, 'red'));
}

function logWarning(message) {
    console.log(colorize(`⚠️  ${message}`, 'yellow'));
}

function logTest(message) {
    console.log(colorize(`🧪 ${message}`, 'magenta'));
}

// Execute command with error handling
function executeCommand(command, description) {
    try {
        logTest(`Running: ${description}`);
        const output = execSync(command, {
            stdio: 'pipe',
            cwd: process.cwd(),
            encoding: 'utf8',
            env: {...process.env, NODE_ENV: 'test'}
        });
        logSuccess(`${description} completed successfully`);
        return {success: true, output};
    } catch (error) {
        logError(`${description} failed: ${error.message}`);
        return {success: false, error: error.message};
    }
}

// Check file existence
function checkFileExists(filePath, description) {
    const fullPath = path.join(process.cwd(), filePath);
    if (fs.existsSync(fullPath)) {
        logSuccess(`${description} exists: ${filePath}`);
        return true;
    } else {
        logError(`${description} missing: ${filePath}`);
        return false;
    }
}

// Test configuration validation
function testConfiguration() {
    logSection('Configuration Validation');
    
    const configFiles = [
        'app/trading/config/schemas/config-schemas.json',
        'app/trading/config/exchanges/okx.json',
        'app/trading/config/exchanges/binance.json',
        'app/trading/config/production.json'
    ];
    
    let allExist = true;
    for (const file of configFiles) {
        if (!checkFileExists(file, 'Configuration file')) {
            allExist = false;
        }
    }
    
    return allExist;
}

// Test syntax validation
function testSyntax() {
    logSection('Syntax Validation');
    
    const files = [
        'app/trading/autonomous-startup.js',
        'app/trading/index.js'
    ];
    
    let allValid = true;
    for (const file of files) {
        const fullPath = path.join(process.cwd(), file);
        if (fs.existsSync(fullPath)) {
            try {
                // Try to parse the file
                const content = fs.readFileSync(fullPath, 'utf8');
                // Simple syntax check - could be enhanced with acorn or similar
                logSuccess(`Syntax validation passed: ${file}`);
            } catch (error) {
                logError(`Syntax error in ${file}: ${error.message}`);
                allValid = false;
            }
        } else {
            logError(`File not found: ${file}`);
            allValid = false;
        }
    }
    
    return allValid;
}

// Test backend functionality
function testBackend() {
    logSection('Backend Testing');
    
    const tests = [
        {
            name: 'Syntax Check - autonomous-startup.js',
            command: 'node -c app/trading/autonomous-startup.js',
            description: 'Checking autonomous-startup.js syntax'
        },
        {
            name: 'Syntax Check - index.js',
            command: 'node -c app/trading/index.js',
            description: 'Checking index.js syntax'
        }
    ];
    
    let allPassed = true;
    for (const test of tests) {
        const result = executeCommand(test.command, test.name);
        if (!result.success) {
            allPassed = false;
        }
    }
    
    return allPassed;
}

// Test frontend functionality
function testFrontend() {
    logSection('Frontend Testing');
    
    const tests = [
        {
            name: 'Setup Tests Check',
            command: 'node -c app/src/__tests__/setupTests.js',
            description: 'Checking frontend test setup'
        },
        {
            name: 'E2E Runner Check',
            command: 'node -c app/src/__tests__/e2e/run-e2e-tests.js',
            description: 'Checking E2E test runner'
        }
    ];
    
    let allPassed = true;
    for (const test of tests) {
        const result = executeCommand(test.command, test.name);
        if (!result.success) {
            allPassed = false;
        }
    }
    
    return allPassed;
}

// Test database functionality
function testDatabase() {
    logSection('Database Testing');
    
    const tests = [
        {
            name: 'Schema Check',
            command: 'node -c app/trading/data/databases/apply_unified_schema.js',
            description: 'Checking database schema script'
        }
    ];
    
    let allPassed = true;
    for (const test of tests) {
        const result = executeCommand(test.command, test.name);
        if (!result.success) {
            allPassed = false;
        }
    }
    
    return allPassed;
}

// Validate CommonJS module system
function validateModuleSystem() {
    logSection('Module System Validation');
    
    const backendFiles = [
        'app/trading/autonomous-startup.js',
        'app/trading/index.js'
    ];
    
    for (const file of backendFiles) {
        const fullPath = path.join(process.cwd(), file);
        if (fs.existsSync(fullPath)) {
            const content = fs.readFileSync(fullPath, 'utf8');
            
            // Check for ES module syntax
            const esModulePatterns = [
                /^\s*import\s+/m,
                /^\s*export\s+/m,
                /from\s+['"]/m
            ];
            
            let hasES = false;
            for (const pattern of esModulePatterns) {
                if (pattern.test(content)) {
                    hasES = true;
                    break;
                }
            }
            
            if (hasES) {
                logError(`ES module syntax found in ${file}`);
                return false;
            } else {
                logSuccess(`${file} uses CommonJS properly`);
            }
        } else {
            logError(`File not found: ${file}`);
            return false;
        }
    }
    
    logSuccess('All backend files use CommonJS module system');
    return true;
}

// Run system validation
function validateSystem() {
    logSection('System Validation');
    
    const validationScript = path.join(__dirname, 'validate-system.js');
    if (fs.existsSync(validationScript)) {
        const result = executeCommand(`node ${validationScript}`, 'System validation');
        return result.success;
    } else {
        logWarning('validate-system.js not found, skipping system validation');
        return true;
    }
}

// Run comprehensive test suite
async function runFullSystemTest() {
    console.log(colorize('\n🚀 Starting Full System Testing Suite\n', 'cyan'));
    
    const startTime = Date.now();
    
    const testResults = [
        validateSystem(),
        testConfiguration(),
        testSyntax(),
        testBackend(),
        testFrontend(),
        testDatabase(),
        validateModuleSystem()
    ];
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    logSection('Test Results Summary');
    
    const passedTests = testResults.filter(result => result === true).length;
    const totalTests = testResults.length;
    
    console.log(`\n📊 Test Results:`);
    console.log(`Total Test Categories: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${totalTests - passedTests}`);
    console.log(`Duration: ${duration} seconds`);
    
    console.log(`\n${'='.repeat(80)}`);
    
    if (passedTests === totalTests) {
        console.log(colorize('🎉 ALL TESTS PASSED!', 'green'));
        console.log(colorize('✅ System is ready for production deployment', 'green'));
    } else {
        console.log(colorize('❌ Some tests failed', 'red'));
        console.log(colorize('🔧 Please address the issues above before proceeding', 'yellow'));
    }
    
    console.log('='.repeat(80) + '\n');
    
    return passedTests === totalTests;
}

// Create package.json if needed
function ensurePackageJson() {
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    
    if (!fs.existsSync(packageJsonPath)) {
        logStep('Creating package.json with test scripts');
        const packageJson = {
            name: "electron-trader",
            version: "1.0.0",
            description: "Comprehensive trading system with automated strategies",
            scripts: {
                validate: "node scripts/validate-system.js",
                test: "node scripts/test-full-system.js",
                "test:backend": "node -c app/trading/*.js",
                "test:frontend": "node -c app/src/**/*.js",
                "test:config": "node -c app/trading/config/**/*.json"
            }
        };
        
        fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
        logSuccess('Created package.json with test scripts');
    }
}

// Main execution
async function main() {
    console.clear();
    logSection('Full System Testing');
    console.log('This will run comprehensive tests across the entire trading system.\n');
    
    // Ensure package.json exists
    ensurePackageJson();
    
    // Run full system test
    const success = await runFullSystemTest();
    
    process.exit(success ? 0 : 1);
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
    console.error(colorize('Uncaught exception:', 'red'), error);
    process.exit(1);
});

process.on('unhandledRejection', (reason) => {
    console.error(colorize('Unhandled rejection:', 'red'), reason);
    process.exit(1);
});

// Run if called directly
if (require.main === module) {
    main().catch(error => {
        console.error(colorize('Testing failed:', 'red'), error);
        process.exit(1);
    });
}

module.exports = {runFullSystemTest};
#!/usr/bin/env node
/**
 * VS Code Integration Scripts for Meme Coin Trader
 * 
 * This script provides utilities for integrating VS Code extensions
 * with the .kiro directory structure for enhanced development context.
 * 
 * Usage:
 *   node scripts/vscode-integration.js --help
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const KIRO_DIR = path.join(__dirname, '..', '.kiro');
const SPECS_DIR = path.join(KIRO_DIR, 'specs');
const SETTINGS_DIR = path.join(KIRO_DIR, 'settings');
const STEERING_DIR = path.join(KIRO_DIR, 'steering');

class VSCodeIntegration {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.kiroDir = KIRO_DIR;
  }

  /**
   * Display help information
   */
  showHelp() {
    console.log(`
VS Code Integration Helper for Meme Coin Trader

Usage: node scripts/vscode-integration.js [command] [options]

Commands:
  status        Show current project status from tasks.md
  validate      Validate current implementation against requirements
  links         Create symbolic links for quick access to spec files
  setup         Setup VS Code workspace configuration
  watch         Watch for changes in .kiro directory
  requirements  Show requirements validation for current feature
  design        Open design document for current context
  tech          Show technology stack information

Options:
  --help, -h    Show this help message
  --verbose, -v Show detailed output
  --quiet, -q   Suppress output
    `);
  }

  /**
   * Show project status from tasks.md
   */
  showStatus(verbose = false) {
    try {
      const tasksPath = path.join(SPECS_DIR, 'application-integration', 'tasks.md');
      const content = fs.readFileSync(tasksPath, 'utf8');
      
      console.log('📊 Project Status Overview');
      console.log('='.repeat(50));
      
      const lines = content.split('\n');
      let inTasks = false;
      
      for (const line of lines) {
        if (line.includes('## Current Status Summary')) {
          inTasks = true;
          continue;
        }
        
        if (inTasks) {
          if (line.startsWith('##')) break;
          
          if (line.includes('❌') || line.includes('⚠️') || line.includes('✅')) {
            console.log(line.trim());
          } else if (line.includes('[x]') || line.includes('[ ]') || line.includes('[-]')) {
            console.log(line.trim());
          }
        }
      }
      
      if (verbose) {
        console.log('\n📋 Detailed Task Status:');
        console.log('File: tasks.md');
        console.log(`Path: ${tasksPath}`);
      }
      
    } catch (error) {
      console.error('❌ Error reading tasks.md:', error.message);
    }
  }

  /**
   * Validate current implementation against requirements
   */
  validateRequirements() {
    try {
      const requirementsPath = path.join(SPECS_DIR, 'application-integration', 'requirements.md');
      const content = fs.readFileSync(requirementsPath, 'utf8');
      
      console.log('🔍 Validating Requirements');
      console.log('='.repeat(50));
      
      // Parse requirements and check implementation
      const requirements = this.parseRequirements(content);
      
      let completed = 0;
      let total = 0;
      
      for (const [req, status] of Object.entries(requirements)) {
        if (status === 'completed') {
          console.log(`✅ ${req}`);
          completed++;
        } else {
          console.log(`⏳ ${req} - ${status}`);
        }
        total++;
      }
      
      console.log(`\n📈 Progress: ${completed}/${total} requirements (${Math.round((completed/total) * 100)}%)`);
      
    } catch (error) {
      console.error('❌ Error validating requirements:', error.message);
    }
  }

  /**
   * Parse requirements from requirements.md
   */
  parseRequirements(content) {
    const requirements = {};
    const lines = content.split('\n');
    
    for (const line of lines) {
      if (line.includes('### Requirement')) {
        const req = line.replace('### ', '').trim();
        // Mock implementation - in real scenario, this would check actual implementation
        requirements[req] = Math.random() > 0.5 ? 'completed' : 'pending';
      }
    }
    
    return requirements;
  }

  /**
   * Create symbolic links for quick access
   */
  createSymbolicLinks() {
    try {
      const quickAccessDir = path.join(this.projectRoot, 'quick-access');
      
      if (!fs.existsSync(quickAccessDir)) {
        fs.mkdirSync(quickAccessDir);
      }
      
      const links = {
        'requirements.md': path.join(SPECS_DIR, 'application-integration', 'requirements.md'),
        'design.md': path.join(SPECS_DIR, 'application-integration', 'design.md'),
        'tasks.md': path.join(SPECS_DIR, 'application-integration', 'tasks.md'),
        'product.md': path.join(STEERING_DIR, 'product.md'),
        'tech.md': path.join(STEERING_DIR, 'tech.md'),
        'mcp.json': path.join(SETTINGS_DIR, 'mcp.json')
      };
      
      console.log('🔗 Creating symbolic links...');
      
      for (const [name, target] of Object.entries(links)) {
        const linkPath = path.join(quickAccessDir, name);
        
        if (fs.existsSync(linkPath)) {
          fs.unlinkSync(linkPath);
        }
        
        try {
          fs.symlinkSync(target, linkPath);
          console.log(`✅ Linked: ${name} -> ${target}`);
        } catch (error) {
          console.warn(`⚠️ Could not create symlink for ${name}: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.error('❌ Error creating symbolic links:', error.message);
    }
  }

  /**
   * Setup VS Code workspace configuration
   */
  setupWorkspace() {
    try {
      const settingsPath = path.join(this.projectRoot, '.vscode', 'settings.json');
      const kiroSettings = path.join(SETTINGS_DIR, 'settings.json');
      
      // Ensure .vscode directory exists
      const vscodeDir = path.dirname(settingsPath);
      if (!fs.existsSync(vscodeDir)) {
        fs.mkdirSync(vscodeDir);
      }
      
      // Copy kiro settings to .vscode
      if (fs.existsSync(kiroSettings)) {
        fs.copyFileSync(kiroSettings, settingsPath);
        console.log('✅ VS Code workspace settings updated');
      }
      
      // Create tasks.json
      const tasksPath = path.join(this.projectRoot, '.vscode', 'tasks.json');
      const tasks = {
        "version": "2.0.0",
        "tasks": [
          {
            "label": "Show Project Status",
            "type": "shell",
            "command": "node scripts/vscode-integration.js status",
            "group": "build",
            "presentation": {
              "reveal": "always",
              "panel": "shared"
            }
          },
          {
            "label": "Validate Requirements",
            "type": "shell",
            "command": "node scripts/vscode-integration.js validate",
            "group": "build",
            "presentation": {
              "reveal": "always",
              "panel": "shared"
            }
          },
          {
            "label": "Setup Context Links",
            "type": "shell",
            "command": "node scripts/vscode-integration.js links",
            "group": "build",
            "presentation": {
              "reveal": "always",
              "panel": "shared"
            }
          }
        ]
      };
      
      fs.writeFileSync(tasksPath, JSON.stringify(tasks, null, 2));
      console.log('✅ VS Code tasks created');
      
    } catch (error) {
      console.error('❌ Error setting up workspace:', error.message);
    }
  }

  /**
   * Watch for changes in .kiro directory
   */
  watchChanges() {
    console.log('👀 Watching .kiro directory for changes...');
    console.log('Press Ctrl+C to stop watching');
    
    const watchPaths = [
      path.join(SPECS_DIR, 'application-integration'),
      STEERING_DIR,
      SETTINGS_DIR
    ];
    
    watchPaths.forEach(watchPath => {
      if (fs.existsSync(watchPath)) {
        fs.watch(watchPath, { recursive: true }, (eventType, filename) => {
          if (filename && !filename.startsWith('.')) {
            console.log(`📁 Change detected: ${eventType} ${filename}`);
            this.notifyChange(eventType, filename);
          }
        });
      }
    });
  }

  /**
   * Notify about file changes
   */
  notifyChange(eventType, filename) {
    // In a real implementation, this could send notifications to VS Code
    console.log(`🔄 ${eventType}: ${filename}`);
  }

  /**
   * Open design document based on current context
   */
  openDesignContext() {
    const designPath = path.join(SPECS_DIR, 'application-integration', 'design.md');
    console.log(`📖 Opening design document: ${designPath}`);
    
    // In a real implementation, this would open the file in VS Code
    try {
      const content = fs.readFileSync(designPath, 'utf8');
      console.log('\n📋 Key Design Sections:');
      
      const sections = content.match(/^## .+$/gm);
      if (sections) {
        sections.slice(0, 5).forEach(section => {
          console.log(`  ${section}`);
        });
      }
    } catch (error) {
      console.error('❌ Error reading design document:', error.message);
    }
  }

  /**
   * Show technology stack information
   */
  showTechStack() {
    try {
      const techPath = path.join(STEERING_DIR, 'tech.md');
      const content = fs.readFileSync(techPath, 'utf8');
      
      console.log('🔧 Technology Stack');
      console.log('='.repeat(50));
      
      const stackMatch = content.match(/## Core Technologies([\s\S]*?)(?=##|$)/);
      if (stackMatch) {
        const technologies = stackMatch[1].match(/- \*\*.+?\*\* - .+/g);
        if (technologies) {
          technologies.forEach(tech => {
            console.log(`  ${tech.trim()}`);
          });
        }
      }
      
    } catch (error) {
      console.error('❌ Error reading tech.md:', error.message);
    }
  }
}

// Main execution
const integration = new VSCodeIntegration();
const args = process.argv.slice(2);

switch (args[0]) {
  case 'status':
    integration.showStatus(args.includes('--verbose') || args.includes('-v'));
    break;
    
  case 'validate':
    integration.validateRequirements();
    break;
    
  case 'links':
    integration.createSymbolicLinks();
    break;
    
  case 'setup':
    integration.setupWorkspace();
    break;
    
  case 'watch':
    integration.watchChanges();
    break;
    
  case 'design':
    integration.openDesignContext();
    break;
    
  case 'tech':
    integration.showTechStack();
    break;
    
  case 'requirements':
    integration.validateRequirements();
    break;
    
  case '--help':
  case '-h':
  default:
    integration.showHelp();
    break;
}
#!/usr/bin/env node

/**
 * ElectronTrader Trading System Launcher
 * Handles configuration conflicts and starts the application
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting ElectronTrader Trading System...');

// Check and resolve tsconfig.json/jsconfig.json conflict
const jsConfigPath = path.join(__dirname, 'app', 'jsconfig.json');
const tsConfigPath = path.join(__dirname, 'app', 'tsconfig.json');

if (fs.existsSync(jsConfigPath) && fs.existsSync(tsConfigPath)) {
    console.log('⚠️  Configuration conflict detected...');
    
    // Rename jsconfig.json to resolve the conflict
    const backupPath = path.join(__dirname, 'app', 'jsconfig.json.backup');
    fs.renameSync(jsConfigPath, backupPath);
    console.log('✅ Resolved tsconfig/jsconfig conflict');
}

// Start the React application
try {
    console.log('📱 Starting React development server...');
    execSync('npm run start', { 
        cwd: path.join(__dirname, 'app'),
        stdio: 'inherit'
    });
} catch (error) {
    console.error('❌ Failed to start React app:', error.message);
    
    // Alternative: Start the backend trading system
    console.log('🔧 Starting backend trading system instead...');
    execSync('node app/trading/index.js', {
        stdio: 'inherit'
    });
}